import{_ as k}from"./add-roles.vue_vue_type_script_setup_true_lang-FrEUQ7D-.js";import w from"./permission-config-Bzm1MNg5.js";import{g as V,d as W}from"./roles-management-DlteFevS.js";import{d as H,l,P as N,aN as m,n as P,ao as T,aV as A,T as x,r as p,o as E,c as F,e as d,b as n,h as u,f as M,u as U,_ as q}from"./index-B63pSD2p.js";import{c as J,a as Q}from"./calculateTableHeight-BjE6OFD1.js";import"./validate-Dc6ka3px.js";import"./user-management-B1vGxPiG.js";const j={class:"zf-first-box"},G={class:"zf-second-box"},I={class:"upload-btn-box"},L=H({name:"roles-management",__name:"index",setup($){const f=l(null),_=l(null),c=l(null),g=l(null),h=l({}),y=N({column:3,labelWidth:"68px",itemWidth:"240px",rules:{role_name:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>40)return t(new Error("角色名称长度不能超过40！"));t()}}]},fields:[{label:"角色名称",prop:"role_name",type:"input",defaultValue:"",placeholder:"请输入角色名称",clearable:!0}]}),o=l({field:[{prop:"role_name",label:"角色名称",minWidth:"120px"},{prop:"u_user_name",label:"更新人",minWidth:"100px"},{prop:"updated_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"role_desc",label:"描述",minWidth:"220px"},{prop:"",label:"操作",type:"template",minWidth:"180px",showOverflowTooltip:!1,templateGroup:[{title:()=>m("roles-management/auth")?"功能权限":"",clickBtn(e){g.value.openDialog("02",e)}},{title:()=>m("roles-management/edit")?"编辑":"",clickBtn(e){c.value.openDialog("02",e)}},{title:()=>m("roles-management/delete")?"删除":"",clickBtn(e){C(e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let b=l([]),v=null;P(()=>{J(v,f.value,o.value),r()}),T(()=>{Q(v)});const O=()=>{c.value.openDialog("01")},r=()=>{let e=JSON.parse(JSON.stringify(_.value.getAllCardData())),{currentPage:a,pageSize:t}=o.value.pageOptions,s={current_page:a,page_size:t};s=Object.assign(e,s),V(s).then(i=>{i.code&&i.code===200&&(b.value=i.data.data,o.value.pageOptions.total=i.data.total)})},C=e=>{A.confirm("确定删除该角色吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a={role_id:e.role_id};W(a).then(t=>{t.code&&t.code===200?(x.success(t.msg),r()):x.warning(t.msg)})}).catch(()=>{})},B=e=>{o.value.pageOptions.pageSize=e,r()},R=e=>{o.value.pageOptions.currentPage=e,r()};return(e,a)=>{const t=p("form-component"),s=p("el-card"),i=p("el-button"),z=p("Auth"),D=p("table-component");return E(),F("div",j,[d("div",G,[n(s,null,{default:u(()=>[d("div",{ref_key:"formDivRef",ref:f},[n(t,{ref_key:"formRef",ref:_,modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=S=>h.value=S),"form-options":y,"is-query-btn":!0,onQueryDataFn:r},null,8,["modelValue","form-options"])],512)]),_:1}),n(s,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:u(()=>[n(z,{value:"roles-management/add"},{default:u(()=>[d("div",I,[n(i,{type:"primary",onClick:O},{default:u(()=>[M("添加角色")]),_:1})])]),_:1}),n(D,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":U(b),onOnHandleSizeChange:B,onOnHandleCurrentChange:R},null,8,["minHeight","table-options","table-data"])]),_:1})]),n(k,{ref_key:"addRolesRef",ref:c,onQueryData:r},null,512),n(w,{ref_key:"permissionConfigRef",ref:g},null,512)])}}}),ne=q(L,[["__scopeId","data-v-6d3638ef"]]);export{ne as default};
