var S=Object.defineProperty;var m=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var I=(a,e,n)=>e in a?S(a,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):a[e]=n,t=(a,e)=>{for(var n in e||(e={}))w.call(e,n)&&I(a,n,e[n]);if(m)for(var n of m(e))b.call(e,n)&&I(a,n,e[n]);return a};import{aC as k,aQ as s,aR as r}from"./index-B63pSD2p.js";const u=k(),C=a=>s.request("post",r("/v1/q_score_analysis/get_user_tasks"),{data:t({user_id:u.userId},a)}),F=a=>Promise.all([s.request("post",r("/v1/q_score_analysis/calculate_mark_total"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),s.request("post",r("/v1/q_score_analysis/calculate_average_speed"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),s.request("post",r("/v1/g_score_analysis/calculate_max_speed"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),s.request("post",r("/v1/g_score_analysis/calculate_min_speed"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),s.request("post",r("/v1/g_score_analysis/calculate_average_score"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e)]).then(e=>{var n,o,_,c,i,l,d,p,y,q,v,g,h,f,D;return{totalCount:(_=(o=(n=e[0])==null?void 0:n.data)==null?void 0:o.total)!=null?_:"-",reviewedCound:(l=(i=(c=e[0])==null?void 0:c.data)==null?void 0:i.reviewed)!=null?l:"-",averageSpeed:(y=(p=(d=e[1])==null?void 0:d.data)==null?void 0:p.average_speed)!=null?y:"-",maxSpeed:(v=(q=e[2])==null?void 0:q.data[0])!=null?v:"-",minSpeed:(h=(g=e[3])==null?void 0:g.data[0])!=null?h:"-",averageScore:(D=(f=e[4])==null?void 0:f.data[0])!=null?D:"-"}}),P=a=>s.request("post",r("/v1/survey_monitor/person_survey_monitor"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),j=a=>s.request("post",r("/v1/score_analysis/calculate_cumulative_average_score_with_time"),{data:t({},a)},{},!1).then(e=>e),J=a=>s.request("post",r("/v1/score_analysis/record_spy_paper_score"),{data:t({},a)},{},!1).then(e=>({code:200,data:{x_data:["第1次(07-02 14:20)","第2次(07-02 15:20)","第3次(07-03 14:20)","第4次(07-03 15:20)","第5次(07-04 14:20)","第6次(07-04 15:20)"],legend:["当前分数","上次分数","差异数据"],y1_data:[5,4,3,2,5,4],y2_data:[2,5,4,3,2,5],y3_data:[-3,1,1,1,-3,1]}})),M=a=>s.request("post",r("/v1/score_analysis/get_all_review_dates"),{data:t({user_id:u.userId},a)},{},!1).then(e=>e),Q=a=>s.request("post",r("/v1/score_analysis/calculate_workload"),{data:t({},a)},{},!1).then(e=>e),R=a=>s.request("post",r("/v1/q_score_analysis/exception_statistics"),{data:t({},a)},{},!1).then(e=>e),W=a=>s.request("post",r("/v1/p_score_analysis/calculate_average_score_per_question"),{data:t({},a)},{},!1).then(e=>e),Z=a=>s.request("post",r("/v1/p_score_analysis/calculate_score_distribution"),{data:t({},a)},{},!1).then(e=>e),B=a=>s.request("post",r("/v1/p_score_analysis/get_review_statistics1"),{data:t({},a)},{},!1).then(e=>e),T=a=>s.request("post",r("/v1/p_score_analysis/calculate_score_progress"),{data:t({},a)},{},!1).then(e=>e),U=a=>s.request("post",r("/v1/p_score_analysis/get_subject_score"),{data:t({},a)},{},!1).then(e=>e),X=a=>s.request("post",r("/v1/p_score_analysis/get_total_statistics"),{data:t({},a)}).then(e=>e);export{X as a,B as b,T as c,U as d,Z as e,W as f,F as g,P as h,M as i,R as j,Q as k,J as l,j as m,C as q};
