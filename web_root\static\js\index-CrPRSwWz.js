import{_ as P}from"./add-examines.vue_vue_type_script_setup_true_lang-ZvP_K7d7.js";import V from"./import-stu-progress-Cn2I09Rl.js";import k from"./importExamineAnswer-CdqPvE5P.js";import{p as f,g as A,a as D}from"./common-methods-BWkba4Bo.js";import{d as H,l as a,P as I,aN as N,n as E,ao as L,r as u,o as B,c as U,e as b,b as l,h as c,f as Q,u as q,_ as J}from"./index-B63pSD2p.js";import{a as T}from"./examinees-management-aJjscxsO.js";import{c as G,a as M}from"./calculateTableHeight-BjE6OFD1.js";import"./response-management-BIkrM71A.js";import"./base-DyvdloLK.js";import"./test-paper-management-DjV_45YZ.js";const $={class:"zf-first-box"},K={class:"zf-second-box"},X={class:"upload-btn-box"},Y=H({name:"examinees-management",__name:"index",setup(Z){a(null);const g=a(null),p=a(null),d=a(null),S=a(null);a(null);const h=a({}),j=I({column:3,labelWidth:"70px",itemWidth:"250px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>f.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>s.value},{label:"准考证号",prop:"allow_exam_num",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入准考证号"}]}),r=a({field:[{prop:"allow_exam_num",label:"考生密号",minWidth:"120px"},{prop:"exam_point_name",label:"考点名称",minWidth:"120px"},{prop:"exam_room_name",label:"考场名称",minWidth:"120px"},{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"created_time",label:"创建时间",minWidth:"140px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"140px",sortable:!0},{prop:"",label:"操作",type:"template",minWidth:"120px",templateGroup:[{title:()=>N("examinees-management/edit")?"编辑":"",clickBtn(e){d.value.openDialog("02",e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),v=a([]),s=a([]);let x=null;a(""),E(()=>{G(x,g.value,r.value),A().then(()=>{var t;const e=(t=f.value[0])==null?void 0:t.project_id;p.value.setCardData("project_id",e),D(e).then(o=>{var m;s.value=o||[];const n=(m=s.value[0])==null?void 0:m.subject_id;p.value.setCardData("subject_id",n),i()})})}),L(()=>{M(x)});const O=(e,t)=>{e.prop==="project_id"&&(t?(s.value=[],p.value.setCardData("subject_id",""),D(t).then(o=>{s.value=o||[]})):(s.value=[],p.value.setCardData("subject_id","")))},i=()=>{let{currentPage:e,pageSize:t}=r.value.pageOptions,o=JSON.parse(JSON.stringify(p.value.getAllCardData()));o.current_page=e,o.page_size=t,T(o).then(n=>{n.code&&n.code===200&&(v.value=n.data.data,r.value.pageOptions.total=n.data.total)})},C=()=>{d.value.openDialog("01")},w=e=>{r.value.pageOptions.pageSize=e,i()},z=e=>{r.value.pageOptions.currentPage=e,i()},y=a(!1);function F(){s.value=[]}return(e,t)=>{const o=u("form-component"),n=u("el-card"),m=u("el-button"),R=u("Auth"),W=u("table-component");return B(),U("div",$,[b("div",K,[l(n,null,{default:c(()=>[b("div",{ref_key:"formDivRef",ref:g},[l(o,{ref_key:"formRef",ref:p,modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=_=>h.value=_),"is-query-btn":!0,"form-options":j,onOnchangeFn:O,onQueryDataFn:i,onResetFields:F},null,8,["modelValue","form-options"])],512)]),_:1}),l(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:c(()=>[b("div",X,[l(R,{value:"examinees-management/add"},{default:c(()=>[l(m,{style:{"margin-left":"12px"},type:"primary",onClick:C},{default:c(()=>[Q("新增")]),_:1})]),_:1})]),l(W,{minHeight:r.value.styleOptions.minHeight,"table-options":r.value,"table-data":v.value,onOnHandleSizeChange:w,onOnHandleCurrentChange:z},null,8,["minHeight","table-options","table-data"])]),_:1})]),l(P,{ref_key:"addExaminesRef",ref:d,projectList:q(f),onQueryData:i},null,8,["projectList"]),l(V,{ref_key:"importStuProgressRef",ref:S,onQueryData:i},null,512),l(k,{isShowDialog:y.value,"onUpdate:isShowDialog":t[1]||(t[1]=_=>y.value=_),title:"导入作答数据",onUploadSuccess:i},null,8,["isShowDialog"])])}}}),ue=J(Y,[["__scopeId","data-v-554b76e0"]]);export{ue as default};
