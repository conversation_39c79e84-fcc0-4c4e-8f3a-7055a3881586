import{aQ as a,aR as e}from"./index-B63pSD2p.js";const s=t=>a.request("post",e("/v1/op/download_single_material",!0),{data:t},{},!1),_=t=>a.request("post",e("/v1/manual_read/get_distri_task_detail"),{data:t}),n=t=>a.request("post",e("/v1/manual_read/manual_mark"),{data:t}),o=t=>a.request("post",e("/v1/manual_read/get_mark_history"),{data:t},{},!1),u=t=>a.request("post",e("/v1/manual_read/get_mark_history_detail"),{data:t}),i=t=>a.request("post",e("/v1/manual_read/get_pre_next_history"),{data:t}),l=t=>a.request("post",e("/v1/manual_read/get_distri_task_process"),{data:t},{},!1),m=t=>a.request("post",e("/v1/manual_read/manual_arbitration"),{data:t}),d=t=>a.request("post",e("/v1/manual_read/get_arbitration_ratio"),{data:t},{},!1),p=t=>a.request("post",e("/v1/manual_read/manual_quality"),{data:t}),g=t=>a.request("post",e("/v1/manual_read/get_distri_task_expert_process"),{data:t},{},!1),c=t=>a.request("post",e("/v1/manual_read/get_arbitration_process"),{data:t},{},!1),q=t=>a.request("post",e("/v1/manual_read/get_quality_general"),{data:t},{},!1);export{i as a,n as b,m as c,s as d,o as e,l as f,u as g,d as h,g as i,c as j,q as k,_ as l,p as m};
