import{aR as g,b5 as d,dh as m,b6 as f,b7 as p,T as t,aK as c}from"./index-B63pSD2p.js";let a=null;function b(r="get",n,i,o=!0,h="加载中..."){let u=g(n),l=d(),e=new FormData;for(let s in i)e.append(s,i[s]);if(o&&(a=m.service({lock:!0,text:h,background:"rgba(255,255,255,0.7)"})),r==="get")return f({timeout:4*60*60*1e3,method:r,url:u,params:e,headers:{Authorization:p(l.accessToken),"Content-Type":"multipart/form-data"}}).then(s=>s.data).catch(s=>{s.response?s.response.status===401?(t.warning("登录已过期！请重新登录！"),c.push("/login")):s.response.status===422?t.error("参数错误！"):s.response.status===500&&t.error("系统错误！请稍后重试！"):t.error(`${n}请求超时！`)});if(r==="post")return f({method:r,url:u,data:e,headers:{Authorization:p(l.accessToken),"Content-Type":"multipart/form-data"}}).then(s=>(o&&a&&a.close(),s.data)).catch(s=>{o&&a&&a.close(),s.response?s.response.status===401?(t.warning("登录已过期！请重新登录！"),c.push("/login")):s.response.status===422?t.error("参数错误！"):s.response.status===500&&t.error("系统错误！请稍后重试！"):t.error(`${n}请求超时！`)})}function R(r="get",n,i,o=!0,h="加载中..."){let u=g(n),l=d();if(o&&(a=m.service({lock:!0,text:h,background:"rgba(255,255,255,0.7)"})),r==="get")return f({timeout:4*60*60*1e3,method:r,url:u,params:i,headers:{Authorization:p(l.accessToken),"Content-Type":"multipart/form-data"}}).then(e=>e.data).catch(e=>{e.response?e.response.status===401?(t.warning("登录已过期！请重新登录！"),c.push("/login")):e.response.status===422?t.error("参数错误！"):e.response.status===500&&t.error("系统错误！请稍后重试！"):t.error(`${n}请求超时！`)});if(r==="post")return f({method:r,url:u,data:i,headers:{Authorization:p(l.accessToken),"Content-Type":"multipart/form-data"}}).then(e=>(o&&a&&a.close(),e.data)).catch(e=>{o&&a&&a.close(),e.response?e.response.status===401?(t.warning("登录已过期！请重新登录！"),c.push("/login")):e.response.status===422?t.error("参数错误！"):e.response.status===500&&t.error("系统错误！请稍后重试！"):t.error(`${n}请求超时！`)})}export{R as a,b as u};
