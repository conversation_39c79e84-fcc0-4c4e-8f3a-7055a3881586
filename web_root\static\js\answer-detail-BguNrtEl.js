import{R as E,W as O,H as N,Q as R}from"./question-desc-DIxRVJjG.js";import{g as Q}from"./score-result-DfeCiQTg.js";import{d as T,l as k,B as V,n as M,T as P,r as v,o as e,c as t,F,p as H,e as l,f as W,t as a,y as b,g as I,h as w,b as d,u as A,_ as j}from"./index-B63pSD2p.js";import"./handleImages-D-nd439N.js";const z={key:0},G=["onClick"],K={key:0},$={class:"grade-expand-icon"},J={key:0},U={class:"stu-answer-box"},X={key:0},Y={key:1},Z={class:"stu-answer-box"},ee={key:0},te={key:1},se={class:"flex justify-center"},oe={key:0},ae={key:1},ne={key:2},re={class:"grade-desc-box"},le={key:0},de={class:"desc-detail-box"},ie={class:"p-[10px] pb-[0px] bg-[#FAFAFA] rounded-[5px] dark:bg-black"},ce={key:1,class:"h-[69vh]"},_e=T({__name:"answer-detail",props:["checkList","studentInfo"],setup(y){const B=y,S=["一","二","三","四","五","六","七","八","九","十","十一","十二","十三","十四","十五","十六","十七","十八","十九","二十","二十一","二十二","二十三","二十四","二十五","二十六","二十七","二十八","二十九","三十"],p=k([]),c=k([0]),m=k([]),_=k({field:[{prop:"ques_order",label:"试题序号",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"160px"},{prop:"standard_answer",label:"参考答案",minWidth:"120px",type:"slot"},{prop:"stu_answer",label:"考生答案",minWidth:"120px",type:"slot"},{prop:"mark_result",label:"判断结果",type:"slot",minWidth:"160px"},{prop:"ques_score",label:"试题分数",minWidth:"100px"},{prop:"stu_score",label:"考生得分",minWidth:"120px",sortable:!0}],styleOptions:{isShowSort:!1,minHeight:"auto",rowKey:"ques_id"},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}});V(()=>B.checkList,r=>{r.includes(1)?_.value.field.forEach(s=>{s.prop==="standard_answer"&&(s.isHidden=!1)}):_.value.field.forEach(s=>{s.prop==="standard_answer"&&(s.isHidden=!0)}),r.includes(2)?_.value.field.forEach(s=>{s.prop==="stu_answer"&&(s.isHidden=!1)}):_.value.field.forEach(s=>{s.prop==="stu_answer"&&(s.isHidden=!0)})}),M(()=>{setTimeout(()=>{D()},100)});const D=()=>{m.value=[];const{grade_id:r,flag:s,paper_code:q}=B.studentInfo;Q({grade_id:r,grade_type:s==="AI"?1:2}).then(u=>{if(u.code&&u.code===200){p.value=u.data;for(let h=0;h<p.value.length;h++)m.value.push(h)}else P.warning(u.msg)})},L=r=>{c.value.indexOf(r)!==-1?c.value.splice(c.value.indexOf(r),1):c.value.push(r)};return(r,s)=>{const q=v("ArrowDownBold"),x=v("el-icon"),u=v("ArrowRightBold"),h=v("table-component"),C=v("el-empty");return e(),t("div",null,[p.value&&p.value.length>0?(e(),t("div",z,[(e(!0),t(F,null,H(p.value,(o,f)=>(e(),t("div",null,[l("div",{class:"ques-type-tit bg-[#F2F2F2] dark:bg-black eye-box",onClick:n=>L(f)},[l("span",null,[W(a(S[f])+"、"+a(o.type)+"：共"+a(o.data.length)+"题，"+a(o.type_ques_score)+"分。 考生答对"+a(o.correct_count)+"题，错"+a(o.wrong_count)+"题， ",1),o.type_code!=="A"&&o.type_code!=="B"?(e(),t("span",K," 部分正确"+a(o.part_correct_count)+"题， ",1)):b("",!0),W(" 得"+a(o.type_stu_score)+"分。",1)]),l("div",$,[c.value.includes(f)?(e(),I(x,{key:0},{default:w(()=>[d(q)]),_:1})):(e(),I(x,{key:1},{default:w(()=>[d(u)]),_:1}))])],8,G),c.value.includes(f)?(e(),t("div",J,[d(h,{minHeight:_.value.styleOptions.minHeight,"table-options":_.value,"table-data":o.data},{standard_answer:w(n=>{var i,g;return[l("div",U,[o.type_code==="B"?(e(),t("span",X,a(((i=n.row.standard_answer)==null?void 0:i[0])==="1"?"正确":"错误"),1)):(e(),t("span",Y,a((g=n.row.standard_answer)==null?void 0:g.toString()),1))])]}),stu_answer:w(n=>{var i,g;return[l("div",Z,[o.type_code==="B"?(e(),t("span",ee,a(((i=n.row.stu_answer)==null?void 0:i[0])==="1"?"正确":"错误"),1)):(e(),t("span",te,a((g=n.row.stu_answer)==null?void 0:g.toString()),1))])]}),mark_result:w(n=>[l("div",se,[n.row.mark_result===1?(e(),t("i",oe,[d(A(E))])):n.row.mark_result===2?(e(),t("i",ae,[d(A(O))])):n.row.mark_result===3?(e(),t("i",ne,[d(A(N))])):b("",!0)])]),_:2},1032,["minHeight","table-options","table-data"]),l("div",re,[y.checkList.includes(3)&&m.value.includes(f)?(e(),t("div",le,[(e(!0),t(F,null,H(o.data,(n,i)=>(e(),t("div",de,[l("div",ie,[d(R,{"question-info":o.data[i],checkList:y.checkList,studentInfo:y.studentInfo},null,8,["question-info","checkList","studentInfo"])])]))),256))])):b("",!0)])])):b("",!0)]))),256))])):(e(),t("div",ce,[d(C,{description:"暂无数据"})]))])}}}),ge=j(_e,[["__scopeId","data-v-a6f2cb19"]]);export{ge as default};
