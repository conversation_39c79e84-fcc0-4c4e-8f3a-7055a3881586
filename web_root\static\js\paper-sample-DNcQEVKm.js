import{aQ as t,aR as s}from"./index-B63pSD2p.js";const n=e=>t.request("post",s("/v1/project/get_project"),{data:e}),o=e=>t.request("post",s("/v1/project/get_subject"),{data:e}),p=e=>({code:200,data:Array.from({length:10},(_,a)=>{const r=a+1;return{text:r,value:r}})}),l=e=>t.request("post",s("/v1/exam_stu/get_exam_info"),{data:e}),c=e=>t.request("post",s("/v1/ques_manage/all_business_ques_type"),{data:e}),g=e=>t.request("post",s("/v1/prepare/get_stu_answer_list"),{data:e}),i=e=>t.request("post",s("/v1/ques/get_ques"),{data:e}),v=e=>[{text:"等于",value:"0"},{text:"不等于",value:"5"},{text:"大于",value:"2"},{text:"小于",value:"3"},{text:"大于等于",value:"1"},{text:"小于等于",value:"4"},{text:"区间",value:"7"}],q=e=>t.request("post",s("/v1/prepare/get_sample_list"),{data:e}),m=e=>t.request("post",s("/v1/prepare/get_stu_answer_id_list_by_sample_id"),{data:e}),d=e=>t.request("post",s("/v1/prepare/create_sample"),{data:e}),x=e=>t.request("post",s("/v1/prepare/get_stu_answer_list_by_answer_id"),{data:e}),b=e=>t.request("post",s("/v1/prepare/delete_sample"),{data:e}),w=e=>t.request("post",s("/v1/prepare/get_stu_answer_list_count"),{data:e}),j=e=>t.request("post",s("/v1/project/extract_range"),{data:e});export{n as a,o as b,p as c,l as d,j as e,m as f,v as g,d as h,x as i,c as j,i as k,w as l,g as m,b as n,q as o};
