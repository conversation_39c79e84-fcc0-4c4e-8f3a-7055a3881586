var m=Object.defineProperty;var l=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var u=(o,t,e)=>t in o?m(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,d=(o,t)=>{for(var e in t||(t={}))x.call(t,e)&&u(o,e,t[e]);if(l)for(var e of l(t))_.call(t,e)&&u(o,e,t[e]);return o};var f=(o,t,e)=>new Promise((c,s)=>{var p=r=>{try{n(e.next(r))}catch(a){s(a)}},i=r=>{try{n(e.throw(r))}catch(a){s(a)}},n=r=>r.done?c(r.value):Promise.resolve(r.value).then(p,i);n((e=e.apply(o,t)).next())});import{d as y,l as L,B as w,n as g,a2 as v,o as E,c as C,dk as b,di as k,_ as z}from"./index-B63pSD2p.js";import"./index-C9GYnvBh.js";const R=y({__name:"scoreRelateLineChart",props:{options:{type:Object,required:!0},series:{type:Array,required:!0}},setup(o,{expose:t}){const e=o,c=L(null);let s=null;const p=["#5087ec","#20c7a6","#f3af2c"];function i(){s||(s=b(c.value));const r={tooltip:{trigger:"axis",valueFormatter:a=>a+"分",axisPointer:{type:"shadow"}},grid:{containLabel:!0,left:10,right:20,bottom:10,top:40},legend:{show:!0,itemHeight:10,textStyle:{fontSize:10}},xAxis:{type:"category",axisLabel:{show:!0},axisTick:{show:!0},data:[]},yAxis:{type:"value",axisLabel:{formatter:function(a){return a+"分"}}},series:[]};e.series.forEach((a,h)=>{r.series.push(d({type:"line",smooth:!0,color:p[h]},a))}),k.merge(r,e.options),s.setOption(r,!0)}w(()=>[e.options,e.series],i,{deep:!0}),g(()=>f(this,null,function*(){i(),window.addEventListener("resize",n)})),v(()=>{s&&s.dispose(),window.removeEventListener("resize",n)});function n(){s&&s.resize()}return t({renderChart:i,resizeChart:n}),(r,a)=>(E(),C("div",{ref_key:"chartRef",ref:c,class:"chart-container"},null,512))}}),O=z(R,[["__scopeId","data-v-5ce2e308"]]);export{O as S};
