var d=Object.defineProperty;var p=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var f=(e,a,o)=>a in e?d(e,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[a]=o,i=(e,a)=>{for(var o in a||(a={}))h.call(a,o)&&f(e,o,a[o]);if(p)for(var o of p(a))l.call(a,o)&&f(e,o,a[o]);return e};import{a6 as c}from"./index-B63pSD2p.js";import{u as g}from"./pageCache-DQQfxtZI.js";const k=(e,a,o,n,r)=>{if(c().multiTags.find(s=>s.path===e)){const s=g().getPageInfo[e];if(s){a.fields.forEach(t=>{s.hasOwnProperty(t.prop)&&o.setCardData(t.prop,s[t.prop])});for(let t in n.pageOptions)s.hasOwnProperty(t)&&(n.pageOptions[t]=s[t]);for(let t in r)s.hasOwnProperty(t)&&(r[t]=s[t])}}},m=(e,a,o,n={})=>{let r=i(i(i({},e.getAllCardData()),a.pageOptions),n);g().handlePageInfo("equal",o,r)};export{m as a,k as h};
