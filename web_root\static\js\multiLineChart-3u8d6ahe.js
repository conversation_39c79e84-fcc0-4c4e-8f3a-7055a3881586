var l=(o,c,e)=>new Promise((n,t)=>{var i=r=>{try{s(e.next(r))}catch(u){t(u)}},a=r=>{try{s(e.throw(r))}catch(u){t(u)}},s=r=>r.done?n(r.value):Promise.resolve(r.value).then(i,a);s((e=e.apply(o,c)).next())});import"./index-C9GYnvBh.js";import{_ as p,l as d,B as f,n as x,a2 as h,o as m,c as y,dk as _}from"./index-B63pSD2p.js";const v={__name:"multiLineChart",props:{xData:{type:Array,required:!0},series:{type:Array,required:!0},unitText:{type:String}},setup(o,{expose:c}){const e=o,n=d(null);let t=null;function i(){t||(t=_(n.value)),t.setOption({tooltip:{trigger:"axis"},legend:{show:!0,itemHeight:10,textStyle:{fontSize:10}},xAxis:{type:"category",data:e.xData},yAxis:{type:"value",axisLabel:{formatter:"{value}"+e.unitText}},series:e.series||[]})}f(()=>[e.xData,e.series],s=>{i()},{deep:!0}),x(()=>l(this,null,function*(){i(),window.addEventListener("resize",a)})),h(()=>{t&&t.dispose(),window.removeEventListener("resize",a)});function a(){t&&t.resize()}return c({resizeChart:a}),(s,r)=>(m(),y("div",{ref_key:"chartRef",ref:n,class:"chart-container"},null,512))}},L=p(v,[["__scopeId","data-v-dc0a0bd9"]]);export{L as default};
