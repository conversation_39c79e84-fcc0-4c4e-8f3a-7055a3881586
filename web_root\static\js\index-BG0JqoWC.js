import{_ as D}from"./mode-detail.vue_vue_type_script_setup_true_lang-TljlcLEN.js";import{a as V,d as R}from"./marking-mode-CLpbbjcA.js";import{d as W,l as i,i as E,P as H,aN as k,T as d,n as N,ao as M,aV as P,r as c,o as T,c as A,e as _,b as n,h as u,f as F,u as q,_ as J}from"./index-B63pSD2p.js";import{c as U,a as j}from"./calculateTableHeight-BjE6OFD1.js";import"./index-BAbjVBSZ.js";import"./vue-drag-resize-CJIVu41Q.js";import"./set-edges-data-ZHEJf664.js";import"./validate-Dc6ka3px.js";import"./set-nodes-data-CVd4iNNP.js";import"./add-mode.vue_vue_type_script_setup_true_lang-D3jlhelb.js";import"./close-line-DgsTxgUT.js";const G={class:"zf-first-box"},I={class:"zf-second-box"},L={class:"upload-btn-box"},Q=W({__name:"index",setup($){const f=i(null),g=i(null),h=i(null),b=E(),x=i({}),y=H({column:3,labelWidth:"96px",itemWidth:"240px",rules:{process_name:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>100)return t(new Error("阅卷模式名称长度不能超过100！"));t()}}],c_name:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>50)return t(new Error("创建人名称长度不能超过50！"));t()}}]},fields:[{label:"阅卷模式名称",prop:"process_name",type:"input",defaultValue:"",placeholder:"请输入阅卷模式名称",clearable:!0},{label:"创建人",prop:"c_name",type:"input",defaultValue:"",placeholder:"请输入创建人",clearable:!0}]}),o=i({field:[{prop:"process_name",label:"阅卷模式名称",minWidth:"200px"},{prop:"c_name",label:"创建人",minWidth:"120px"},{prop:"lock_state_status",label:"状态",minWidth:"120px"},{prop:"created_time",label:"创建时间",minWidth:"160px",sortable:!0},{prop:"created_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"",label:"操作",type:"template",minWidth:"200px",templateGroup:[{title:()=>"预览",clickBtn(e){h.value.openDialog(e)}},{title:()=>k("marking-mode/edit")?"编辑":"",clickBtn(e){e.lock_state===2?d.warning("该模式已锁定！不可编辑！"):b.push({path:"/base-management/new-mode/index",query:{process_id:e.process_id,process_name:e.process_name,flag:"edit"}})}},{title:()=>k("marking-mode/delete")?"删除":"",clickBtn(e){e.lock_state===2?d.warning("该模式已锁定！不可删除！"):w(e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let m=i([]),v=null;N(()=>{U(v,f.value,o.value),p()}),M(()=>{j(v)});const p=()=>{let e=JSON.parse(JSON.stringify(g.value.getAllCardData())),{currentPage:a,pageSize:t}=o.value.pageOptions,l={current_page:a,page_size:t};l=Object.assign(e,l),V(l).then(r=>{r.code&&r.code===200&&(m.value=r.data.data,m.value.forEach(s=>{s.lock_state===1?s.lock_state_status="未使用":s.lock_state===2&&(s.lock_state_status="已使用")}),o.value.pageOptions.total=r.data.total)})},O=()=>{b.push({path:"/base-management/new-mode/index"})},w=e=>{P.confirm("确定删除该模式吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a={process_id:e.process_id};R(a).then(t=>{t.code&&t.code===200?(d.success(t.msg),p()):d.warning(t.msg)})})},C=e=>{o.value.pageOptions.pageSize=e,p()},B=e=>{o.value.pageOptions.currentPage=e,p()};return(e,a)=>{const t=c("form-component"),l=c("el-card"),r=c("el-button"),s=c("Auth"),z=c("table-component");return T(),A("div",G,[_("div",I,[n(l,null,{default:u(()=>[_("div",{ref_key:"formDivRef",ref:f},[n(t,{ref_key:"formRef",ref:g,modelValue:x.value,"onUpdate:modelValue":a[0]||(a[0]=S=>x.value=S),"form-options":y,"is-query-btn":!0,onQueryDataFn:p},null,8,["modelValue","form-options"])],512)]),_:1}),n(l,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:u(()=>[n(s,{value:"marking-mode/add"},{default:u(()=>[_("div",L,[n(r,{type:"primary",onClick:O},{default:u(()=>[F("创建")]),_:1})])]),_:1}),n(z,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":q(m),onOnHandleSizeChange:C,onOnHandleCurrentChange:B},null,8,["minHeight","table-options","table-data"])]),_:1})]),n(D,{ref_key:"modeDetailRef",ref:h},null,512)])}}}),se=J(Q,[["__scopeId","data-v-8293dc38"]]);export{se as default};
