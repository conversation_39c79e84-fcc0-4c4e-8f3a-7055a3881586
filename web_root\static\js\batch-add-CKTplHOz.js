var fe=Object.defineProperty,ve=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ge=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var A=(o,l,t)=>l in o?fe(o,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[l]=t,Q=(o,l)=>{for(var t in l||(l={}))ge.call(l,t)&&A(o,t,l[t]);if(U)for(var t of U(l))ye.call(l,t)&&A(o,t,l[t]);return o},$=(o,l)=>ve(o,be(l));var I=(o,l,t)=>new Promise((s,S)=>{var C=m=>{try{g(t.next(m))}catch(_){S(_)}},r=m=>{try{g(t.throw(m))}catch(_){S(_)}},g=m=>m.done?s(m.value):Promise.resolve(m.value).then(C,r);g((t=t.apply(o,l)).next())});import G from"./setting-task-URsozwG-.js";import{E as ke,F as he}from"./fullscreen-exit-line-DVwpkItP.js";import{b as Te}from"./question-DElFsEXd.js";import{a as xe,c as qe,b as Fe,u as Se,d as Ce}from"./formal-task-CUOmIYGE.js";import{a as Ve}from"./common-methods-BWkba4Bo.js";import{f as Oe}from"./handleMethod-BIjqYEft.js";import{d as je,l as i,P as De,m as He,ao as we,r as h,o as F,g as V,h as c,e as T,t as J,b,u as E,f as j,c as We,F as Le,q as Re,C as ze,T as v,_ as Ee}from"./index-B63pSD2p.js";import"./marking-mode-CLpbbjcA.js";import"./test-paper-management-DjV_45YZ.js";const Pe={class:"flex-bc"},Be={class:"footer-btn"},Ne=je({__name:"batch-add",props:{entrance:{type:String,default:"formal-task"},projectList:{type:Array,default:[]},taskType:{type:Number,default:1}},emits:["queryData"],setup(o,{expose:l,emit:t}){const s=o,S=t,C=i(!1),r=i("first"),g=i(null),m=i(null),_=i(null),x=i(!1),p=i({distribute_state:1}),K=De({column:3,labelWidth:"70px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",defaultValue:null,placeholder:"请选择所属资格",clearable:!0,optionData:()=>s.projectList},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>W.value},{label:"分配状态",prop:"distribute_state",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择分配状态",optionData:()=>[{label:"全部",value:""},{label:"未分配",value:1},{label:"已分配",value:2}]},{label:"试题描述",prop:"ques_desc",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入试题描述"},{label:"题型",prop:"bs_ques_type_name",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择题型",props:{value:"ques_type_name",label:"ques_type_name"},optionData:()=>D.value}]}),W=i([]),D=i([]),y=i({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"ques_name",label:"试题名称",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"ques_desc",label:"试题描述",minWidth:"120px"},{prop:"name",label:"题型",minWidth:"100px"},{prop:"ques_score",label:"试题总分",minWidth:"90px"},{prop:"distribute_state",label:"分配状态",minWidth:"90px",formatter:a=>Oe(a.distribute_state,[{value:1,label:"未分配"},{value:2,label:"已分配"}])}],styleOptions:{isShowSort:!0,isShowSelection:!0,minHeight:"500px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let P=i([]);const k=i([]),f=i("add"),X=i({}),B=He(()=>{if(f.value==="add"){let a="批量创建";if(s.taskType===1)return a+"正评任务";if(s.taskType===3)return a+"培训任务";if(s.taskType===2)return a+"试评任务"}else return"编辑任务"});let L=null;we(()=>{N(L)});const Y=()=>{Z(L,m.value,y.value)},Z=(a=null,e,u)=>{a=new ResizeObserver(n=>{n.forEach(q=>{e&&g.value&&(u.styleOptions.minHeight=g.value.clientHeight-120-e.clientHeight+"px")})}),e&&g.value&&a.observe(e)},N=a=>{a&&(a.disconnect(),a=null)},ee=(a="add",e)=>{C.value=!0,f.value=a,f.value==="add"?(H(),R()):f.value==="edit"&&(X.value=e,r.value="second",ze(()=>{_.value.setFormDataList([e])}))},O=()=>{C.value=!1,p.value={distribute_state:1},r.value="first",k.value=[],N(L)},ae=a=>{z(a)},te=()=>{x.value=!x.value},le=(a,e)=>{a.prop==="project_id"?(W.value=[],D.value=[],p.value.subject_id&&(p.value.subject_id=null),p.value.bs_ques_type_name&&(p.value.bs_ques_type_name=null),e&&Ve(e).then(u=>{W.value=u||[]}),R()):a.prop==="subject_id"&&(D.value=[],p.value.bs_ques_type_name&&(p.value.bs_ques_type_name=null),e&&R())},R=()=>{const{project_id:a,subject_id:e}=p.value;xe({project_id:a,subject_id:e,is_remove_duplicate:!0}).then(n=>{n.code&&n.code===200?D.value=n.data.data:v.error(n.msg)})},z=a=>{a==="first"?r.value=a:a==="second"&&ue()},H=()=>{const{currentPage:a,pageSize:e}=y.value.pageOptions;let u=$(Q({},p.value),{current_page:a,page_size:e,task_type:s.taskType});u.distribute_state===""&&(u.distribute_state=null),Te(u).then(n=>{n.code&&n.code===200?(n.data.ques_data.forEach(q=>{q.name=q.business_type_name?q.business_type_name:q.ques_type_name}),P.value=n.data.ques_data,y.value.pageOptions.total=n.data.total):v.error(n.msg)})},se=a=>{y.value.pageOptions.currentPage=1,y.value.pageOptions.pageSize=a,H()},ne=a=>{y.value.pageOptions.currentPage=a,H()},oe=a=>{k.value=a},ue=()=>{let a={task_type:s.taskType,create_stage:s.taskType,ques_code_list:k.value.map(e=>e.ques_code)};qe(a).then(e=>{var u;e.code&&e.code===200?(u=e.data)!=null&&u.data&&Object.keys(e.data.data).length?s.taskType===1?(r.value="first",v.warning("不可选择已经分配过任务的试题！")):s.taskType===2&&(r.value="second",_.value.setSelectionList(k.value,e.data.data)):(r.value="second",_.value.setSelectionList(k.value)):v.warning(e.msg)})},ie=()=>I(this,null,function*(){const a=yield _.value.getTaskParams();if(a){let e={task_type:s.taskType,human_task_list:a};f.value==="add"?s.taskType===1?re(e):s.taskType===2&&pe(e):f.value==="edit"&&(s.taskType===1?ce(e):s.taskType)}}),re=a=>{Fe(a).then(e=>{e.code&&e.code===200?(v.success(e.msg),S("queryData"),O()):v.error(e.msg)})},ce=a=>{Se(a.human_task_list[0]).then(e=>{e.code&&e.code===200?(v.success(e.msg),S("queryData"),O()):v.error(e.msg)})},pe=a=>{Ce(a).then(e=>{e.code&&e.code===200?(v.success(e.msg),S("queryData"),O()):v.error(e.msg)})};return l({openDialog:ee}),(a,e)=>{const u=h("iconify-icon-offline"),n=h("el-text"),q=h("form-component"),de=h("table-component"),M=h("el-tab-pane"),me=h("el-tabs"),w=h("el-button"),_e=h("el-dialog");return F(),V(_e,{modelValue:C.value,"onUpdate:modelValue":e[5]||(e[5]=d=>C.value=d),title:B.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",width:"80%","destroy-on-close":"","close-on-click-modal":!1,"before-close":O,fullscreen:x.value,onOpen:Y},{header:c(()=>[T("div",Pe,[T("div",null,J(B.value),1),b(n,{class:"cursor-pointer",style:{margin:"-6px 2px 0 0"},onClick:te},{default:c(()=>[T("i",null,[b(u,{icon:x.value?E(ke):E(he)},null,8,["icon"])])]),_:1})])]),footer:c(()=>[T("div",Be,[r.value==="first"?(F(),V(w,{key:0,type:"primary",disabled:!k.value.length,onClick:e[2]||(e[2]=d=>z("second"))},{default:c(()=>[j("下一步 ")]),_:1},8,["disabled"])):(F(),We(Le,{key:1},[r.value==="first"?(F(),V(w,{key:0,onClick:e[3]||(e[3]=d=>z("first"))},{default:c(()=>[j("上一步")]),_:1})):(F(),V(w,{key:1,onClick:e[4]||(e[4]=d=>O())},{default:c(()=>[j("取消")]),_:1})),b(w,{type:"primary",onClick:ie},{default:c(()=>[j("保存")]),_:1})],64))])]),default:c(()=>[T("div",{ref_key:"allRef",ref:g,style:Re({height:x.value?"86vh":"660px"})},[f.value==="add"?(F(),V(me,{key:0,modelValue:r.value,"onUpdate:modelValue":e[1]||(e[1]=d=>r.value=d),class:"demo-tabs",onTabChange:ae},{default:c(()=>[b(M,{label:"第1步：选择试题",name:"first"},{default:c(()=>[T("div",null,[T("div",{ref_key:"formDivRef",ref:m},[b(q,{ref:"formRef",modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=d=>p.value=d),"form-options":K,"is-query-btn":!0,isShowReset:!1,onOnchangeFn:le,onQueryDataFn:H},null,8,["modelValue","form-options"])],512),T("div",null,[b(n,null,{default:c(()=>{var d;return[j("已选中 "+J((d=k.value)==null?void 0:d.length)+" 条数据",1)]}),_:1})]),b(de,{minHeight:y.value.styleOptions.minHeight,"table-options":y.value,"table-data":E(P),onOnHandleSelectionChange:oe,onOnHandleSizeChange:se,onOnHandleCurrentChange:ne},null,8,["minHeight","table-options","table-data"])])]),_:1}),b(M,{label:"第2步：设置任务信息",name:"second",disabled:!k.value.length&&f.value==="add"},{default:c(()=>[b(G,{ref_key:"settingTaskRef",ref:_,isFullscreen:x.value,addFlag:f.value,taskType:o.taskType},null,8,["isFullscreen","addFlag","taskType"])]),_:1},8,["disabled"])]),_:1},8,["modelValue"])):(F(),V(G,{key:1,ref_key:"settingTaskRef",ref:_,isFullscreen:x.value,addFlag:f.value},null,8,["isFullscreen","addFlag"]))],4)]),_:1},8,["modelValue","title","fullscreen"])}}}),Ye=Ee(Ne,[["__scopeId","data-v-7b7740de"]]);export{Ye as default};
