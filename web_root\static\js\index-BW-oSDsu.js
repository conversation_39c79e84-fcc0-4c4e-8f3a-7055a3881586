import t from"./index-B8lLdivE.js";import r from"./reviewerHomePage-B-tiUNo_.js";import{d as n,aC as c,O as m,c as _,u as p,g as o,o as e,_ as f}from"./index-B63pSD2p.js";const i={class:"homePage"},d=n({name:"welcome",__name:"index",setup(u){const s=c(),{systemUserType:a}=m(s);return(l,g)=>(e(),_("div",i,[p(a)===1?(e(),o(t,{key:0})):(e(),o(r,{key:1}))]))}}),h=f(d,[["__scopeId","data-v-febb3796"]]);export{h as default};
