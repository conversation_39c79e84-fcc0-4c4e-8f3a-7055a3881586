var Te=Object.defineProperty,je=Object.defineProperties;var Ne=Object.getOwnPropertyDescriptors;var ve=Object.getOwnPropertySymbols;var $e=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var ye=(i,V,q)=>V in i?Te(i,V,{enumerable:!0,configurable:!0,writable:!0,value:q}):i[V]=q,z=(i,V)=>{for(var q in V||(V={}))$e.call(V,q)&&ye(i,q,V[q]);if(ve)for(var q of ve(V))Ie.call(V,q)&&ye(i,q,V[q]);return i},oe=(i,V)=>je(i,Ne(V));var _e=(i,V,q)=>new Promise((P,C)=>{var N=M=>{try{$(q.next(M))}catch(I){C(I)}},X=M=>{try{$(q.throw(M))}catch(I){C(I)}},$=M=>M.done?P(M.value):Promise.resolve(M.value).then(N,X);$((q=q.apply(i,V)).next())});import{d as ke,aS as xe,G as Ve,aO as ae,B as Ee,l as x,P as Se,n as Qe,r as E,o as l,c as o,F,p as H,t as Q,y as m,b as _,h as b,g as A,u as fe,q as ge,f as R,e as h,aP as B,ac as Ce,ad as Le,_ as Ue,ao as Pe,w as Ae,z as Be,T as D}from"./index-B63pSD2p.js";import{_ as ee}from"./TinymceEditor.vue_vue_type_style_index_0_lang-Dj-6iP6L.js";import Re from"./op-mark-step-DW83lcNi.js";import{h as be}from"./handleImages-D-nd439N.js";import{g as ie}from"./quesNum-CouueI57.js";import{q as qe,g as Oe}from"./rules-form-CST-rV3v.js";import{a as ze,c as He,d as Ke,e as Ge}from"./question-DElFsEXd.js";import{p as We,a as we}from"./common-methods-BWkba4Bo.js";import"./util-DBFSI-P4.js";import"./scoring-rules-BR2vQ7G3.js";import"./test-paper-management-DjV_45YZ.js";const O=i=>(Ce("data-v-5e34980b"),i=i(),Le(),i),Je=["id"],Xe=O(()=>h("div",{class:"w-[3px] h-[14px] mr-2",style:{background:"var(--el-color-primary)","margin-bottom":"18px"}},null,-1)),Ye={key:0,class:"mr-2",style:{"font-size":"16px","margin-bottom":"18px"}},Ze={key:0,class:"absolute",style:{color:"var(--el-color-danger)","font-size":"12px","line-height":"12px",bottom:"-14px",left:"0"}},De={key:1},et={key:0},tt=O(()=>h("span",{class:"text-bold-box whitespace-nowrap"},"答案：",-1)),lt={key:0,class:"flex"},rt={key:1,class:"ques-editor-input"},ot={key:0,class:"ques-editor-input"},at={key:1,class:"text-inline-box"},st=O(()=>h("span",{class:"text-bold-box whitespace-nowrap"},"答案：",-1)),ut={key:0},dt={key:1},nt={key:2},_t={key:3},it={key:0,class:"text-inline-box"},ct={class:"text-inline-box"},pt=["innerHTML"],mt={key:1,class:"text-inline-box"},ht={class:"text-inline-box"},ft=["innerHTML"],vt={key:4},yt={class:"ques-editor-input"},gt={key:2},bt={key:0},qt=O(()=>h("div",{class:"text-bold-box mb-[2px]"},"解析：",-1)),wt={class:"ques-editor-input"},kt={key:1,class:"flex"},xt=O(()=>h("div",{class:"text-bold-box"},"解析：",-1)),Vt=["innerHTML"],Et={key:1},St={key:3,class:"mt-[10px] mb-[10px]"},Qt=O(()=>h("div",{class:"text-bold-box"},"评分规则：",-1)),Ct=["innerHTML"],Lt={key:4},Ut=O(()=>h("span",{class:"text-bold-box"},"权    重：",-1)),Ft={key:0},Mt={key:5,class:"mt-[10px]"},Tt={class:"text-bold-box"},jt={class:"point-form"},Nt={class:"form-point-box"},$t={key:0,style:{color:"var(--el-color-danger)","font-size":"12px"}},It=O(()=>h("span",{class:"text-bold-box"},"评分标准：",-1)),Pt={key:0},At={key:0},Bt={key:0},Rt={key:6,class:"mt-1"},Ot=O(()=>h("div",{class:"mark-step-text text-bold-box"},"评分步骤：",-1)),zt=ke({name:"question-answer",__name:"index",props:xe({isShowQuesCode:{type:Boolean,default:!0},isEdit:{type:Boolean,default:!1},parentIndex:{type:Number||String,default:""}},{currentQuesTypeCode:{},currentQuesTypeCodeModifiers:{},smallQuesList:{},smallQuesListModifiers:{},deleteQuesIdList:{},deleteQuesIdListModifiers:{}}),emits:["update:currentQuesTypeCode","update:smallQuesList","update:deleteQuesIdList"],setup(i,{expose:V}){let q=Ve().smallQuesNumStyle;const P=ae(i,"currentQuesTypeCode"),C=ae(i,"smallQuesList");Ee(()=>C.value,(r,f)=>{},{deep:!0});const N=()=>{C.value.forEach((r,f)=>{var g;if(r.level==3)r.ques_order_new=(g=r.ques_order_new)!=null?g:`(${f+1})`;else{const U=ie(q);r.ques_order_new=U[f]||f+1}r.ques_type_code=="F"||(r.children=[])})},X=()=>{const r=ie(q);C.value.push({ques_nav:B.uniqueId("nav_"),ques_id:"",small_ques_num:C.value.length+1+"",ques_order_new:r[C.value.length],level:2,ques_type_code:"",standard_answer:[],standard_answer_html:[""],rules:{ques_type_code:[{required:!0,message:"请选择所属题型",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入小题分数",trigger:["blur","change"]}]}}),N()},$=(r,f)=>{Array.isArray(r.children)||(r.children=[]),r.children.push({ques_nav:B.uniqueId("nav_"),ques_id:"",parentIndex:f,ques_order_new:`(${r.children.length+1})`,small_ques_num:"1",level:3,ques_type_code:"",standard_answer:[],standard_answer_html:[""],rules:{ques_type_code:[{required:!0,message:"请选择所属题型",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入小题分数",trigger:["blur","change"]}]}}),N(),v(r)},M=["①","②","③","④","⑤","⑥","⑦","⑧","⑨","⑩","⑪","⑫","⑬","⑭","⑮","⑯","⑰","⑱","⑲","⑳","㉑","㉒","㉓","㉔","㉕","㉖","㉗","㉘","㉙","㉚","㉛","㉜","㉝","㉞","㉟","㊱","㊲","㊳","㊴","㊵","㊶","㊷","㊸","㊹","㊺","㊻","㊼","㊽","㊾","㊿"];x({}),Se({column:3,labelWidth:"100px",itemWidth:"140px",rules:{},fields:[{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!1,clearable:!0,optionData:()=>qe},{label:"小题分数",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入小题分数",clearable:!0}]});const I=x(!1),s=r=>{r.stopPropagation()},K=r=>{let f={point:"",score:1};r.ques_mark_point||(r.ques_mark_point=[]),r.ques_mark_point.push(f),v(r)},u=(r,f)=>{r.ques_mark_point.splice(f,1),v(r)},v=r=>{var U,T;const f=Number(r.ques_score);let g=0;(U=r.ques_mark_point)!=null&&U.length&&r.ques_mark_point.forEach(le=>{g+=le.score}),r.ques_type_code=="E"?g<f?r.pointScoreError=!0:r.pointScoreError=!1:g!=f?r.pointScoreError=!0:r.pointScoreError=!1,r.ques_type_code=="F"&&(T=r.children)!=null&&T.length&&(r.pointScoreError=!1)},te=r=>{var f;if(r.level==2&&r.ques_type_code=="F"&&((f=r.children)!=null&&f.length)){let g=0;r.children.forEach(U=>{U.ques_score&&(g+=U.ques_score)}),r.ques_score!=g?r.totalScoreErrorMsg="该分数为子题所有分数之和":r.totalScoreErrorMsg=""}},ce=r=>{te(r),v(r)},Y=ae(i,"deleteQuesIdList"),se=(r,f)=>_e(this,null,function*(){r.ques_id&&r.ques_code&&Y.value.push({ques_id:r.ques_id,ques_code:r.ques_code}),C.value.splice(f,1),N()});return V({handleComputedNo:N,markPointValidate:v,deleteQuesIdList:Y.value}),Qe(()=>{Oe()}),(r,f)=>{const g=E("el-input"),U=E("el-form-item"),T=E("el-option"),le=E("el-select"),ue=E("el-input-number"),pe=E("el-form"),G=E("Delete"),Z=E("el-icon"),re=E("Plus"),me=E("el-text"),de=E("el-card"),ne=E("el-button"),he=E("el-empty");return l(),o(F,null,[(l(!0),o(F,null,H(C.value,(e,t)=>{var p,n,y,w,d,c,L,k,W,J;return l(),o("div",{class:"readOnly-box",id:`section${e.ques_nav}`},[P.value=="F"?(l(),o("div",{key:0,ref_for:!0,ref:"formDivRef",class:"query-box flex items-center"},[Xe,e.level!=3?(l(),o("span",Ye,Q(e.ques_order_new),1)):m("",!0),_(pe,{inline:!0,rules:e.rules,ref_for:!0,ref:a=>e.formRef=a,model:e,"label-width":"auto",class:"flex-1"},{default:b(()=>[e.level==3?(l(),A(U,{key:0,prop:"ques_order_new",rules:[{required:!0,message:"请输入",trigger:"blur"}]},{default:b(()=>[_(g,{placeholder:"题号",modelValue:e.ques_order_new,"onUpdate:modelValue":a=>e.ques_order_new=a,class:"input-margin",style:{width:"50px"},onKeydown:s},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):m("",!0),_(U,{label:"所属题型",prop:"ques_type_code"},{default:b(()=>[_(le,{style:{width:"140px"},modelValue:e.ques_type_code,"onUpdate:modelValue":a=>e.ques_type_code=a,placeholder:"请选择所属题型",onChange:N},{default:b(()=>[(l(!0),o(F,null,H(fe(qe),a=>(l(),A(T,{label:a.label,value:a.value},null,8,["label","value"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),_(U,{class:"relative",label:"小题分数",prop:"ques_score"},{default:b(()=>[_(ue,{min:.1,modelValue:e.ques_score,"onUpdate:modelValue":a=>e.ques_score=a,class:"input-margin",style:{width:"140px"},onKeydown:s,onChange:a=>ce(e)},null,8,["modelValue","onUpdate:modelValue","onChange"]),e.ques_score&&e.totalScoreErrorMsg?(l(),o("span",Ze,Q(e.totalScoreErrorMsg),1)):m("",!0)]),_:2},1024)]),_:2},1032,["rules","model"]),e.level==2&&t==0?m("",!0):(l(),A(Z,{key:1,style:{"margin-bottom":"18px"},class:"point-icon-box point-del",onClick:a=>se(e,t)},{default:b(()=>[_(G)]),_:2},1032,["onClick"]))],512)):m("",!0),e.ques_type_code!=="F"&&e.ques_type_code!=="G"?(l(),o("div",De,[i.isEdit?(l(),o("div",et,[tt,e.ques_type_code==="D"?(l(),o("div",lt,[((p=e.standard_answer_html)==null?void 0:p.length)>0?(l(!0),o(F,{key:0},H(e.standard_answer_html,(a,S)=>(l(),o("div",{class:"ques-editor-input filling-input-box",style:ge({width:`${1/e.standard_answer_html.length*100-.2}%`})},[_(ee,{modelValue:e.standard_answer_html[S],"onUpdate:modelValue":j=>e.standard_answer_html[S]=j,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])],4))),256)):(l(!0),o(F,{key:1},H(e.standard_answer,(a,S)=>(l(),o("div",{class:"ques-editor-input filling-input-box",style:ge({width:`${1/e.standard_answer.length*100-.2}%`})},[_(ee,{modelValue:e.standard_answer[S],"onUpdate:modelValue":j=>e.standard_answer[S]=j,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])],4))),256))])):e.ques_type_code==="E"?(l(),o("div",rt,[_(ee,{modelValue:e.standard_answer[0],"onUpdate:modelValue":a=>e.standard_answer[0]=a,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])])):(l(),o(F,{key:2},[(n=e.standard_answer_html)!=null&&n.length?(l(),o("div",ot,[_(ee,{modelValue:e.standard_answer_html[0],"onUpdate:modelValue":a=>e.standard_answer_html[0]=a,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])])):m("",!0)],64))])):(l(),o("p",at,[st,e.ques_type_code==="A"?(l(),o("span",ut,Q(e.standard_answer[0]),1)):m("",!0),e.ques_type_code==="B"?(l(),o("span",dt,Q(e.standard_answer[0]==="1"?"正确":"错误"),1)):m("",!0),e.ques_type_code==="C"?(l(),o("span",nt,Q(e.standard_answer.join("")),1)):m("",!0),e.ques_type_code==="D"?(l(),o("span",_t,[((y=e.standard_answer_html)==null?void 0:y.length)>0?(l(),o("span",it,[(l(!0),o(F,null,H(e.standard_answer_html,(a,S)=>(l(),o("span",ct,[R(Q(`(${S+1})`),1),h("span",{innerHTML:a},null,8,pt)]))),256))])):(l(),o("span",mt,[(l(!0),o(F,null,H(e.standard_answer,(a,S)=>(l(),o("span",ht,[R(Q(`(${S+1})`),1),h("span",{innerHTML:a},null,8,ft)]))),256))]))])):m("",!0),e.ques_type_code==="E"?(l(),o("span",vt,[h("div",yt,[_(ee,{ref_for:!0,ref:"editorRef",modelValue:e.standard_answer_html,"onUpdate:modelValue":a=>e.standard_answer_html=a,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])])])):m("",!0)]))])):m("",!0),e.ques_type_code!=="F"&&e.ques_type_code!=="G"&&e.level<=2?(l(),o("div",gt,[i.isEdit?(l(),o("div",bt,[qt,h("div",wt,[_(ee,{ref_for:!0,ref:"editorRef",modelValue:e.standard_parse,"onUpdate:modelValue":a=>e.standard_parse=a,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])])])):(l(),o("div",kt,[xt,e.standard_parse!==null?(l(),o("div",{key:0,innerHTML:fe(be)(e.standard_parse)},null,8,Vt)):(l(),o("div",Et,"无"))]))])):m("",!0),e.level==3||e.level==2||e.level==1&&e.ques_type_code!="A"&&e.ques_type_code!="B"&&e.ques_type_code!="C"?(l(),o("div",St,[Qt,i.isEdit?(l(),A(g,{key:0,type:"textarea",modelValue:e.e_mark_rule,"onUpdate:modelValue":a=>e.e_mark_rule=a,placeholder:"请输入评分规则",autosize:"",onKeydown:s},null,8,["modelValue","onUpdate:modelValue"])):(l(),o(F,{key:1},[e.e_mark_rule?(l(),o("div",{key:0,innerHTML:fe(be)(e.e_mark_rule.replace(/\n/g,"<br/>"))},null,8,Ct)):m("",!0)],64))])):m("",!0),e.ques_type_code==="D"&&((w=e==null?void 0:e.weight)==null?void 0:w.length)>0&&e.level<=2?(l(),o("p",Lt,[Ut,e.weight?(l(),o("span",Ft,Q((d=e.weight[0])==null?void 0:d.join(":"))+"（总权重："+Q((c=e.weight[1])==null?void 0:c.toString())+"）",1)):m("",!0)])):m("",!0),e.level==3||e.level==2&&(((L=e.children)==null?void 0:L.length)==0||!e.children)||e.level==1&&e.ques_type_code!="A"&&e.ques_type_code!="B"&&e.ques_type_code!="C"?(l(),o("div",Mt,[i.isEdit?(l(),o(F,{key:0},[h("div",null,[h("div",Tt,[R(" 评分标准："),_(me,{type:"primary",style:{cursor:"pointer"},onClick:a=>K(e)},{default:b(()=>[_(Z,{class:"point-icon-box-add"},{default:b(()=>[_(re)]),_:1})]),_:2},1032,["onClick"])]),h("div",jt,[(l(!0),o(F,null,H(e.ques_mark_point,(a,S)=>(l(),o("div",Nt,[h("div",null,Q(S+1)+"．",1),_(g,{type:"textarea",modelValue:a.point,"onUpdate:modelValue":j=>a.point=j,class:"input-margin",placeholder:"请输入评分标准",autosize:"",onKeydown:s},null,8,["modelValue","onUpdate:modelValue"]),_(ue,{min:.1,modelValue:a.score,"onUpdate:modelValue":j=>a.score=j,class:"input-margin",style:{width:"190px"},onKeydown:s,onChange:j=>v(e)},null,8,["modelValue","onUpdate:modelValue","onChange"]),R(" 分 "),_(Z,{class:"point-icon-box point-del",onClick:j=>u(e,S)},{default:b(()=>[_(G)]),_:2},1032,["onClick"])]))),256))])]),e.pointScoreError?(l(),o("span",$t,"评分标准总分数需"+Q(e.ques_type_code=="E"?"大于":"")+"等于"+Q(e.level==1?"试题总分":"小题分数"),1)):m("",!0)],64)):(l(),o(F,{key:1},[h("p",null,[It,((k=e.ques_mark_point)==null?void 0:k.length)===0?(l(),o("span",Pt,"无")):m("",!0)]),((W=e.ques_mark_point)==null?void 0:W.length)>0?(l(),o("ul",At,[(l(!0),o(F,null,H(e.ques_mark_point,(a,S)=>(l(),o("li",null,Q(M[S])+" "+Q(a.point)+"("+Q(a.score)+"分) ",1))),256))])):m("",!0)],64))])):m("",!0),h("div",null,[(J=e==null?void 0:e.children)!=null&&J.length?(l(),o("div",Bt,[_(de,{style:{"margin-bottom":"12px",overflow:"inherit"}},{default:b(()=>[_(Fe,{smallQuesList:e.children,"onUpdate:smallQuesList":a=>e.children=a,currentQuesTypeCode:P.value,"onUpdate:currentQuesTypeCode":f[0]||(f[0]=a=>P.value=a),deleteQuesIdList:Y.value,"onUpdate:deleteQuesIdList":f[1]||(f[1]=a=>Y.value=a),isEdit:i.isEdit,isShowQuesCode:!1,parentIndex:t},null,8,["smallQuesList","onUpdate:smallQuesList","currentQuesTypeCode","deleteQuesIdList","isEdit","parentIndex"])]),_:2},1024)])):m("",!0)]),e.ques_type_code==="G"?(l(),o("div",Rt,[Ot,_(Re,{opStepList:e.op_step_list,quesDetail:e,isMark:I.value},null,8,["opStepList","quesDetail","isMark"])])):m("",!0),e.level==2&&e.ques_type_code=="F"?(l(),A(ne,{key:7,text:"",type:"primary",onClick:a=>$(e,t)},{default:b(()=>[_(Z,null,{default:b(()=>[_(re)]),_:1}),R("添加子题")]),_:2},1032,["onClick"])):m("",!0),e.level==2&&P.value=="F"&&t==C.value.length-1?(l(),A(ne,{key:8,type:"primary",class:"mb-4 mt-4",plain:"",style:{width:"100%"},onClick:X},{default:b(()=>[R("创建试题"+Q(t+2),1)]),_:2},1024)):m("",!0)],8,Je)}),256)),C.value.length==0?(l(),A(he,{key:0,description:"暂无数据"})):m("",!0)],64)}}}),Fe=Ue(zt,[["__scopeId","data-v-5e34980b"]]),Me=i=>(Ce("data-v-605b70b3"),i=i(),Le(),i),Ht={class:"ques-box every-task-box dark:!bg-black eye-box"},Kt={class:"flex"},Gt={class:"w-[320px]"},Wt=Me(()=>h("h3",{class:"pb-5",style:{"font-weight":"500","padding-top":"18px"}},"试题信息",-1)),Jt={ref:"formDivRef",class:"query-box"},Xt={key:0,class:"relative",style:{color:"var(--el-color-danger)","font-size":"12px","line-height":"12px",top:"-16px",left:"80px"}},Yt={class:"flex-1 flex",style:{"border-top":"1px solid #dcdfe6","padding-top":"18px"}},Zt={key:0,style:{width:"140px","padding-left":"12px"}},Dt=Me(()=>h("div",{class:"title"},"试题导航",-1)),el={class:"btn"},tl=ke({__name:"addQuestion",props:{tableOptions:{},tableOptionsModifiers:{},tableData:{},tableDataModifiers:{}},emits:xe(["queryData","handleCurrentChange"],["update:tableOptions","update:tableData"]),setup(i,{expose:V,emit:q}){let P=Ve().smallQuesNumStyle;const C=q;ae(i,"tableOptions"),ae(i,"tableData");const N=x(),X=x(),$=x(!1),M=x(!1),I=x([]),s=x([]),K=x([]),u=x({}),v=x(""),te=x("创建");Ee(()=>u.value.business_type_id,e=>{},{deep:!0,immediate:!1});const ce=Se({column:3,labelWidth:"80px",itemWidth:"180px",rules:{project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],business_type_id:[{required:!0,message:"请选择试题类型",trigger:["blur","change"]}],ques_code:[{required:!0,message:"请输入试题编号",trigger:["blur","change"]}],ques_name:[{required:!0,message:"请输入试题名称",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入试题总分",trigger:["blur","change"]}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>We.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择所属科目",optionData:()=>I.value},{label:"试题类型",prop:"business_type_id",type:"select",defaultValue:"",placeholder:"请选择试题类型",multiple:!1,clearable:!0,props:{label:"ques_type_name",value:"business_id"},optionData:()=>K.value},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"试题名称",prop:"ques_name",type:"input",defaultValue:"",placeholder:"请输入试题名称",clearable:!0},{label:"试题总分",prop:"ques_score",type:"inputNumber",min:.1,defaultValue:"",placeholder:"请输入试题总分",clearable:!0}]});x([]);const Y=x(""),se=x({}),f={children:"children",label:"ques_order_new",class:(e,t)=>e.pointScoreError||e.totalScoreErrorMsg?"is-error":null},g=x([]);x({});const U=()=>{const e=K.value.filter(n=>u.value.business_type_id==n.business_id);let t={};e.length&&(t=e[0],v.value=t.ques_type_code);const p=ie(P);s.value=[],t.ques_type_code=="A"?s.value.push({ques_nav:B.uniqueId("nav_"),small_ques_num:"1",ques_order_new:1,level:1,ques_type_code:t.ques_type_code,standard_answer:[],standard_answer_html:[""],ques_score:u.value.ques_score,ques_choices:[{code:"1",options:"A"},{code:"2",options:"B"},{code:"3",options:"C"},{code:"4",options:"D"},{code:"5",options:"E"},{code:"6",options:"F"}]}):t.ques_type_code=="D"?s.value.push({ques_nav:B.uniqueId("nav_"),small_ques_num:"1",ques_order_new:1,level:1,ques_type_code:t.ques_type_code,standard_answer:[],standard_answer_html:[""],ques_score:u.value.ques_score}):t.ques_type_code=="E"?s.value.push({ques_nav:B.uniqueId("nav_"),small_ques_num:"1",ques_order_new:1,level:1,ques_type_code:t.ques_type_code,standard_answer:[],standard_answer_html:[""],ques_score:u.value.ques_score}):t.ques_type_code=="F"?s.value.push({ques_nav:B.uniqueId("nav_"),small_ques_num:"1",ques_order_new:p[0]||1,level:2,ques_type_code:"",standard_answer:[],standard_answer_html:[""],rules:{ques_type_code:[{required:!0,message:"请选择所属题型",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入小题分数",trigger:["blur","change"]}]}}):s.value.push({ques_nav:B.uniqueId("nav_"),small_ques_num:"1",ques_order_new:1,level:1,ques_type_code:t.ques_type_code,standard_answer:[],standard_answer_html:[""],ques_score:u.value.ques_score})},T=x(!1),le=()=>{var e,t,p;if(v.value!=="F")(e=s.value)!=null&&e.length&&(s.value[0].ques_score=u.value.ques_score),(t=X.value)==null||t.markPointValidate(s.value[0]);else if((p=s.value)!=null&&p.length){let n=0;s.value.forEach(y=>{y.ques_score&&(n+=y.ques_score)}),u.value.ques_score!=n?T.value=!0:T.value=!1}},ue=(e,t)=>{if(te.value=t,t=="编辑"){u.value=e.ques_detail,u.value.project_id=e.ques_detail.project_id,u.value.subject_id=e.ques_detail.subject_id,u.value.business_type_id=e.ques_detail.business_ques_type_id,u.value.ques_code=e.ques_detail.ques_code,u.value.ques_score=e.ques_detail.ques_score,u.value.ques_name=e.ques_detail.knowledge_show,we(u.value.project_id).then(n=>{I.value=n||[]}),re(),v.value=e.ques_detail.ques_type_code;const p=ie(P);e.ques_detail.ques_type_code=="F"?(e.ques_detail.children.forEach((n,y)=>{n.ques_nav=B.uniqueId("nav_"),n.level=2,n.ques_order_new=p[s.value.length]||y+1,Array.isArray(n.children)&&n.children.length&&n.children.forEach((w,d)=>{n.ques_nav=B.uniqueId("nav_"),w.level=3,w.ques_order_new=w.small_ques_num,w.rules={ques_type_code:[{required:!0,message:"请选择所属题型",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入小题分数",trigger:["blur","change"]}]}}),n.rules={ques_type_code:[{required:!0,message:"请选择所属题型",trigger:["blur","change"]}],ques_score:[{required:!0,message:"请输入小题分数",trigger:["blur","change"]}]}}),s.value=e.ques_detail.children):s.value=[z({level:1},e.ques_detail)]}$.value=!0},pe=()=>{M.value=!0},G=()=>{$.value=!1,u.value={},s.value=[],g.value=[]},Z=(e,t)=>{e.prop==="project_id"&&(I.value=[],K.value=[],u.value.subject_id&&(u.value.subject_id=null),t&&we(t).then(p=>{I.value=p||[]})),e.prop==="subject_id"&&(K.value=[],u.value.business_type_id&&(u.value.business_type_id=null),re()),e.prop==="business_type_id"&&U(),e.prop==="ques_score"&&le()},re=()=>{let e={project_id:u.value.project_id,subject_id:u.value.subject_id,is_remove_duplicate:!0};ze(e).then(t=>{t.code&&t.code===200?K.value=t.data.data:D.warning(t.msg)})};x({}),x("");const me=(e,t)=>{e.level==3?se.value=t.parent.data:se.value=e;let p=e.ques_nav;setTimeout(()=>{document.body.querySelector("#section"+p).scrollIntoView({behavior:"smooth"})},0)},de=()=>{var n,y,w;let e=[N.value.formValidate()];for(let d=0;d<s.value.length;d++){const c=s.value[d];if(c.formRef&&e.push(c.formRef.validate()),(n=c.children)!=null&&n.length)for(let L=0;L<c.children.length;L++){const k=c.children[L];k.formRef&&e.push(k.formRef.validate())}}let t=!0;if(v.value=="F")s.value.forEach(d=>{var c,L;if((c=d.children)!=null&&c.length)d.children.forEach((k,W)=>{var S;const J=Number(k.ques_score);let a=0;(S=k.ques_mark_point)!=null&&S.length&&k.ques_mark_point.forEach(j=>{a+=j.score}),k.ques_type_code=="E"?a<J?(k.pointScoreError=!0,t=!1):k.pointScoreError=!1:a!=J?(k.pointScoreError=!0,t=!1):k.pointScoreError=!1});else{const k=Number(d.ques_score);let W=0;(L=d.ques_mark_point)!=null&&L.length&&d.ques_mark_point.forEach(J=>{W+=J.score}),d.ques_type_code=="E"?W<k?(d.pointScoreError=!0,t=!1):d.pointScoreError=!1:W!=k?(d.pointScoreError=!0,t=!1):d.pointScoreError=!1}});else{const d=Number(u.value.ques_score);let c=0;s.value[0].ques_type_code!="A"&&s.value[0].ques_type_code!="B"&&s.value[0].ques_type_code!="C"&&((y=s.value[0].ques_mark_point)!=null&&y.length&&s.value[0].ques_mark_point.forEach(L=>{c+=L.score}),s.value[0].ques_type_code=="E"?c<d?(s.value[0].pointScoreError=!0,t=!1):s.value[0].pointScoreError=!1:c!=d?(s.value[0].pointScoreError=!0,t=!1):s.value[0].pointScoreError=!1)}t?e.push(Promise.resolve(!0)):e.push(Promise.reject(!1));let p=!0;if(v.value=="F"&&s.value.forEach(d=>{var c;if(d.level==2&&d.ques_type_code=="F"&&((c=d.children)!=null&&c.length)){let L=0;d.children.forEach(k=>{k.ques_score&&(L+=k.ques_score)}),d.ques_score!=L?(d.totalScoreErrorMsg="该分数为子题所有分数之和",p=!1):d.totalScoreErrorMsg=""}}),p?e.push(Promise.resolve(!0)):e.push(Promise.reject(!1)),v.value=="F"&&(w=s.value)!=null&&w.length){let d=0;s.value.forEach(c=>{c.ques_score&&(d+=c.ques_score)}),u.value.ques_score!=d?T.value=!0:T.value=!1}return T.value?e.push(Promise.reject(!1)):e.push(Promise.resolve(!0)),Promise.all(e)},ne=()=>_e(this,null,function*(){de().then(()=>{let e={};v.value=="F"?(s.value.forEach((t,p)=>{var n;t.ques_order=p+1,t.small_ques_int=p+1+"",(n=t.children)!=null&&n.length&&t.children.forEach((y,w)=>{y.small_ques_num=y.ques_order_new,y.ques_order=w+1,y.small_ques_int=w+1+""})}),e=oe(z({ques_order:1,small_ques_int:"1"},u.value),{ques_type_code:v.value,children:s.value})):e=oe(z(z({ques_order:1,small_ques_int:"1"},s.value[0]),u.value),{ques_type_code:v.value}),He(e).then(t=>{t.code&&t.code===200?(D.success(t.msg),u.value={},C("queryData"),G()):D.warning(t.msg)})}).catch(e=>{})}),he=()=>_e(this,null,function*(){de().then(()=>{if(g.value.length){let t=[];g.value.forEach(p=>{t.push(p.ques_id)}),Ke({ques_id:t,ques_code:g.value[0].ques_code}).then(p=>{p.code==200||D.error(p.msg)})}let e={};v.value=="F"?(s.value.forEach((t,p)=>{var n;t.ques_order=p+1,t.small_ques_int=p+1+"",t.ques_code=u.value.ques_code,(n=t.children)!=null&&n.length&&t.children.forEach((y,w)=>{y.small_ques_num=y.ques_order_new,y.ques_code=u.value.ques_code,y.ques_order=w+1,y.small_ques_int=w+1+""})}),e=oe(z({ques_order:1,small_ques_int:"1"},u.value),{ques_type_code:v.value,children:s.value})):e=oe(z(z({ques_order:1,small_ques_int:"1"},u.value),s.value[0]),{ques_type_code:v.value}),Ge(e).then(t=>{t.code&&t.code===200?(D.success(t.msg),u.value={},C("queryData"),G()):D.warning(t.msg)})}).catch(e=>{})});return Qe(()=>{g.value=[]}),Pe(()=>{}),V({openDrawer:ue}),(e,t)=>{const p=E("form-component"),n=E("el-scrollbar"),y=E("el-tree"),w=E("el-button"),d=E("el-dialog");return l(),o("div",null,[_(d,{modelValue:$.value,"onUpdate:modelValue":t[4]||(t[4]=c=>$.value=c),title:te.value,"before-close":G,fullscreen:"","destroy-on-close":"","close-on-click-modal":!1,onOpen:pe},{footer:b(()=>[h("div",el,[_(w,{onClick:G},{default:b(()=>[R("取消")]),_:1}),te.value=="编辑"?(l(),A(w,{key:0,type:"primary",onClick:he},{default:b(()=>[R("保存")]),_:1})):(l(),A(w,{key:1,type:"primary",onClick:ne},{default:b(()=>[R("保存")]),_:1}))])]),default:b(()=>[h("div",Ht,[h("div",Kt,[h("aside",Gt,[_(n,{always:"",height:"85vh",style:{"border-right":"1px solid #dcdfe6","border-top":"1px solid #dcdfe6"}},{default:b(()=>[Wt,h("div",Jt,[_(p,{ref_key:"formRef",ref:N,modelValue:u.value,"onUpdate:modelValue":t[0]||(t[0]=c=>u.value=c),"form-options":ce,"is-query-btn":!1,onOnchangeFn:Z},null,8,["modelValue","form-options"]),T.value&&u.value.ques_score&&v.value=="F"?(l(),o("span",Xt,"试题总分为所有小题的分数之和")):m("",!0)],512)]),_:1})]),h("section",Yt,[v.value==="F"?(l(),o("div",Zt,[_(n,{always:"","max-height":"85vh",style:{"padding-right":"12px"}},{default:b(()=>[Dt,Ae(_(y,{data:s.value,"node-key":"ques_nav","default-expand-all":"","highlight-current":"",props:f,"current-node-key":Y.value,"expand-on-click-node":!1,onNodeClick:me},null,8,["data","current-node-key"]),[[Be,s.value.length]])]),_:1})])):m("",!0),_(n,{always:"","max-height":"85vh",style:{flex:"1"}},{default:b(()=>[_(Fe,{ref_key:"addQuesAnswerRef",ref:X,smallQuesList:s.value,"onUpdate:smallQuesList":t[1]||(t[1]=c=>s.value=c),currentQuesTypeCode:v.value,"onUpdate:currentQuesTypeCode":t[2]||(t[2]=c=>v.value=c),deleteQuesIdList:g.value,"onUpdate:deleteQuesIdList":t[3]||(t[3]=c=>g.value=c),isEdit:!0,isShowQuesCode:!1},null,8,["smallQuesList","currentQuesTypeCode","deleteQuesIdList"])]),_:1})])])])]),_:1},8,["modelValue","title"])])}}}),ml=Ue(tl,[["__scopeId","data-v-605b70b3"]]);export{ml as default};
