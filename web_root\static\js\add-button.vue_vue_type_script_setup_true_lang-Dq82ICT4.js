import{d as F,l,P as N,r as f,o as O,g as P,h as i,e as _,b as m,f as V,C as B,T as r,ih as T,ii as z}from"./index-B63pSD2p.js";const E={class:"zf-dialog-first-box"},J={class:"zf-dialog-second-box"},R={class:"footer-btn"},W=F({__name:"add-button",emits:["queryData"],setup(S,{expose:k,emit:y}){const g=y,c=l("添加功能点"),u=l(!1),p=l("01"),n=l({}),v=l({}),b=N({column:3,labelWidth:"108px",itemWidth:"300px",rules:{func_point_name:[{required:!0,message:"请输入名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(a,e,o)=>{if(e.length>30)return o(new Error("名称长度不能超过30！"));o()}}],func_point_value:[{required:!0,message:"请输入值",trigger:["blur","change"]}],func_point_rank:[{required:!0,message:"请输入排序",trigger:["blur","change"]}]},fields:[{label:"名称",prop:"func_point_name",type:"input",defaultValue:"",placeholder:"请输入名称",clearable:!0},{label:"值",prop:"func_point_value",type:"input",defaultValue:"",placeholder:"请输入值",clearable:!0},{label:"排序",prop:"func_point_rank",type:"input",defaultValue:"",placeholder:"请输入排序",clearable:!0}]}),t=l(null),x=(a,e)=>{u.value=!0,p.value=a,n.value=e,a==="01"?c.value="添加功能点":a==="02"&&(c.value="编辑功能点",B(()=>{b.fields.map(o=>{e.hasOwnProperty(o.prop)&&t.value.setCardData(o.prop,e[o.prop])}),t.value.setCardData("func_point_rank",e.rank),t.value.setCardData("func_point_name",e.module_name)}))},s=()=>{u.value=!1,t.value.resetFieldsFn()},C=()=>{t.value.formValidate().then(()=>{let a=JSON.parse(JSON.stringify(t.value.getAllCardData()));a.func_point_rank=Number(a.func_point_rank),p.value==="01"?(a.parent_module_flag=n.value.module_flag,D(a)):p.value==="02"&&(a.func_point_id=n.value.module_id,q(a))}).catch(()=>{r.warning("请按要求填写！")})},D=a=>{T(a).then(e=>{e.code&&e.code===200?(r.success(e.msg),g("queryData",n.value.module_flag),s()):r.warning(e.msg)})},q=a=>{z(a).then(e=>{e.code&&e.code===200?(r.success(e.msg),g("queryData",n.value.module_flag),s()):r.warning(e.msg)})};return k({openDialog:x}),(a,e)=>{const o=f("form-component"),h=f("el-button"),w=f("el-dialog");return O(),P(w,{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=d=>u.value=d),title:c.value,width:"500px","align-center":"","close-on-click-modal":!1,"before-close":s,draggable:""},{footer:i(()=>[_("div",R,[m(h,{onClick:s},{default:i(()=>[V("取消")]),_:1}),m(h,{type:"primary",onClick:C},{default:i(()=>[V("确定")]),_:1})])]),default:i(()=>[_("div",E,[_("div",J,[m(o,{ref_key:"formRef",ref:t,modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=d=>v.value=d),"form-options":b,"is-query-btn":!1},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}});export{W as _};
