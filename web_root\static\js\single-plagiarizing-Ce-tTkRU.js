var R=Object.defineProperty;var S=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var D=(l,a,t)=>a in l?R(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,k=(l,a)=>{for(var t in a||(a={}))I.call(a,t)&&D(l,t,a[t]);if(S)for(var t of S(a))N.call(a,t)&&D(l,t,a[t]);return l};import{d as U,l as n,P as B,n as L,ao as E,T as J,r as f,o as h,c as v,e as b,b as d,h as y,u as z,aN as V,y as W,ac as M,ad as T,_ as $}from"./index-B63pSD2p.js";import{g as A}from"./plagiarizing-answer-DCON8hS9.js";import{c as Q,a as G}from"./calculateTableHeight-BjE6OFD1.js";import{p as K,g as X,a as Y}from"./common-methods-BWkba4Bo.js";import Z from"./single-detail-CJkFVjgq.js";import ee from"./single-judge-DgOCcZyG.js";import"./test-paper-management-DjV_45YZ.js";import"./judge-copy-BAX6vJkk.js";const ae=l=>(M("data-v-c26dc507"),l=l(),T(),l),le={class:"zf-first-box"},te={class:"zf-second-box"},oe=ae(()=>b("div",{style:{height:"6px",background:"#e0e2e8"}},null,-1)),ne={class:"task-btn-box"},re=U({name:"single-plagiarizing",__name:"single-plagiarizing",setup(l){const a=n(null),t=n(null),g=n(!1),_=n(!1),c=n({}),j=B({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>K.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>r.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>r.value},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入考试密号",clearable:!0},{label:"任务名称",prop:"username5",type:"input",defaultValue:"",placeholder:"请输入任务名称",clearable:!0},{label:"考生密号",prop:"username2",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"判定状态",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定状态",optionData:()=>r.value},{label:"相似度",prop:"username",type:"selectInput",defaultValue:[0,null],leftFilterable:!0,leftPlaceholder:"请选择",leftWidth:"76px",leftClearable:!1,leftOptionData:()=>[{label:"大于",value:0},{label:"等于",value:1},{label:"小于",value:2}],rightPlaceholder:"请输入",rightWidth:"74px",rightClearable:!0},{label:"判定结果",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定结果",optionData:()=>r.value}]}),r=n([]),i=n({field:[{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_order",label:"题号",minWidth:"90px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"task_name",label:"任务名称",minWidth:"120px"},{prop:"stu_secret_num",label:"考生密号（参考卷）",minWidth:"160px"},{prop:"region",label:"考生密号（对比卷）",minWidth:"160px"},{prop:"work_unit",label:"答案相似度",minWidth:"120px"},{prop:"work_unit1",label:"判定状态",minWidth:"120px"},{prop:"role_name",label:"人工判定结果",minWidth:"120px",formatter:o=>"-"},{prop:"operation",label:"操作",type:"slot",minWidth:"310px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),w=n([]);let x=null;L(()=>{X(),Q(x,a.value,i.value,!1,58),m()}),E(()=>{G(x)});const C=(o,e)=>{o.prop==="project_id"&&(r.value=[],c.value.subject_id&&(c.value.subject_id=null),e&&Y(e).then(s=>{r.value=s||[]}))},m=()=>{let{currentPage:o,pageSize:e}=i.value.pageOptions,s=k({current_page:o,page_size:e},c.value);A(s).then(p=>{p.code&&p.code===200||J.error(p.msg)}),w.value=[{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"系统架构设计师（案例分析）",paper_name:"—",ques_order:"—",ques_code:"19490982231",task_name:"案例分析第一题",stu_secret_num:"2003939999020001",region:"20039399990200011",work_unit:"100%",work_unit1:"已判定"}]},O=o=>{i.value.pageOptions.pageSize=o,m()},F=o=>{i.value.pageOptions.currentPage=o,m()};function P(){r.value=[]}return(o,e)=>{const s=f("form-component"),p=f("el-card"),q=f("table-component");return h(),v("div",le,[b("div",te,[d(p,null,{default:y(()=>[b("div",{ref_key:"formDivRef",ref:a},[d(s,{ref_key:"formRef",ref:t,modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=u=>c.value=u),"form-options":j,"is-query-btn":!0,onOnchangeFn:C,onQueryDataFn:m,onResetFields:P},null,8,["modelValue","form-options"])],512)]),_:1}),oe,d(p,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:y(()=>[d(q,{minHeight:i.value.styleOptions.minHeight,"table-options":i.value,"table-data":w.value,onOnHandleSizeChange:O,onOnHandleCurrentChange:F},{operation:y(u=>[b("div",ne,[z(V)("plagiarizing-answer/single-plagiarizing-detail")?(h(),v("span",{key:0,class:"task-btn",onClick:e[1]||(e[1]=H=>g.value=!0)},"详情")):W("",!0),z(V)("plagiarizing-answer/single-plagiarizing-judge")?(h(),v("span",{key:1,class:"task-btn",onClick:e[2]||(e[2]=H=>_.value=!0)},"判定")):W("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),d(Z,{isShowDialog:g.value,"onUpdate:isShowDialog":e[3]||(e[3]=u=>g.value=u)},null,8,["isShowDialog"]),d(ee,{isShowDialog:_.value,"onUpdate:isShowDialog":e[4]||(e[4]=u=>_.value=u)},null,8,["isShowDialog"])])}}}),_e=$(re,[["__scopeId","data-v-c26dc507"]]);export{_e as default};
