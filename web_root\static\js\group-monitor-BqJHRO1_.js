var B=Object.defineProperty,G=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable;var z=(p,o,t)=>o in p?B(p,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):p[o]=t,D=(p,o)=>{for(var t in o||(o={}))N.call(o,t)&&z(p,t,o[t]);if(S)for(var t of S(o))A.call(o,t)&&z(p,t,o[t]);return p},V=(p,o)=>G(p,E(o));var x=(p,o,t)=>new Promise((k,e)=>{var j=n=>{try{d(t.next(n))}catch(u){e(u)}},m=n=>{try{d(t.throw(n))}catch(u){e(u)}},d=n=>n.done?k(n.value):Promise.resolve(n.value).then(j,m);d((t=t.apply(p,o)).next())});import{g as U,b as I,c as Q}from"./formal-monitor-BXl8VaF0.js";import{c as $,a as J}from"./calculateTableHeight-BjE6OFD1.js";import{p as K,g as X,a as Y}from"./common-methods-BWkba4Bo.js";import{d as Z}from"./downloadRequest-CdE2PBjt.js";import{d as ee,l as _,P as ae,n as te,ao as oe,r as g,o as le,c as ne,b as v,h as y,ae as re,e as C,f as pe,aV as se,T as F,C as ue,_ as ie}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const ce={class:"zf-flex-end"},de=ee({__name:"group-monitor",setup(p){const o=[{value:1,label:"未发起"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}],t=_(null),k=_(null),e=_({}),j=ae({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>K.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>m.value},{label:"任务",prop:"task_name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择任务",optionData:()=>d.value},{label:"执行状态",prop:"round_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择执行状态",optionData:()=>o},{label:"所属小组",prop:"group_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属小组",optionData:()=>n.value}]}),m=_([]),d=_([]),n=_([]),u=_({field:[{prop:"project_name",label:"所属资格",minWidth:"220px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"ques_group_name",label:"题组",minWidth:"140px"},{prop:"group_name",label:"小组",minWidth:"140px"},{prop:"expert_total",label:"参评人数",minWidth:"120px"},{prop:"reviewed_count",label:"已阅量",minWidth:"120px"},{prop:"average_speed1",label:"平均速度（份/时）",minWidth:"100px"},{prop:"average_speed2",label:"平均评分时间（秒/份）",minWidth:"120px"},{prop:"max_speed",label:"最长评分时间（秒）",minWidth:"120px"},{prop:"min_speed",label:"最短评分时间（秒）",minWidth:"120px"},{prop:"time",label:"预估剩余时间（时）",minWidth:"120px"},{prop:"average_score",label:"平均分",minWidth:"120px"},{prop:"max_score",label:"最高分",minWidth:"120px"},{prop:"min_score",label:"最低分",minWidth:"120px"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),W=_([]);let O=null;te(()=>{X(),b(),$(O,t.value,u.value,!0,58),f()}),oe(()=>{J(O)});const q=(a,r)=>x(this,null,function*(){if(a.prop==="project_id"&&(m.value=[],d.value=[],n.value=[],e.value.subject_id&&(e.value.subject_id=null),e.value.task_name&&(e.value.task_name=null),e.value.group_id&&(e.value.group_id=null),r)){const s=yield Y(r);m.value=s||[],b()}a.prop==="subject_id"&&(d.value=[],n.value=[],e.value.task_name&&(e.value.task_name=null),e.value.group_id&&(e.value.group_id=null),r&&(b(),R()))}),b=()=>x(this,null,function*(){var i,c;const r=(c=(i=(yield U({page_size:-1,project_id:e.value.project_id,subject_id:e.value.subject_id,task_type:1})).data)==null?void 0:i.data)!=null?c:[],s=new Map(r.map(l=>[l.task_name,l]));d.value=[...s.values()].map(l=>({label:l.task_name,value:l.task_name}))}),R=()=>x(this,null,function*(){var c;const s=(((c=(yield Q({page_size:-1,project_id:e.value.project_id,subject_id:e.value.subject_id})).data)==null?void 0:c.data)||[]).filter(l=>l.group_level===3),i=new Map(s.map(l=>[l.ques_group_id,l]));n.value=[...i.values()].map(l=>({label:l.name,value:l.ques_group_id}))});function w(){let{currentPage:a,pageSize:r}=u.value.pageOptions;return D({current_page:a,page_size:r,task_type:1,round_state_list:e.value.round_state?[e.value.round_state]:[],round_count:"1"},e.value)}const f=()=>{I(w()).then(a=>{var s,i,c;const r=((s=a.data)==null?void 0:s.data)||[];W.value=r,u.value.pageOptions.total=(c=(i=a.data)==null?void 0:i.total)!=null?c:0})},M=()=>{se.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z("post","/v1/survey_monitor/group_survey_monitor_export",void 0,{},V(D({},w()),{page_size:-1}),"xlsx").then(a=>{a&&a.data&&a.data.type=="application/json"&&F({message:"暂无导出信息！",type:"warning"})}).catch(()=>{F({type:"error",message:"导出失败"})})}).catch(()=>{})},L=a=>{u.value.pageOptions.pageSize=a,f()},T=a=>{u.value.pageOptions.currentPage=a,f()};function H(){m.value=[],d.value=[],n.value=[],ue(()=>{b()})}return(a,r)=>{const s=g("form-component"),i=g("el-card"),c=g("el-button"),l=g("Auth"),P=g("table-component");return le(),ne("div",null,[v(i,null,{default:y(()=>[re(a.$slots,"tabs",{},void 0,!0),C("div",{ref_key:"formDivRef",ref:t},[v(s,{ref_key:"formRef",ref:k,modelValue:e.value,"onUpdate:modelValue":r[0]||(r[0]=h=>e.value=h),"form-options":j,"is-query-btn":!0,onOnchangeFn:q,onQueryDataFn:f,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:3}),v(i,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:y(()=>{var h;return[C("div",ce,[v(l,{value:"formal-monitor/group-export"},{default:y(()=>[v(c,{type:"primary",onClick:M},{default:y(()=>[pe("导出")]),_:1})]),_:1})]),v(P,{minHeight:(h=u.value.styleOptions)==null?void 0:h.minHeight,"table-options":u.value,"table-data":W.value,onOnHandleSizeChange:L,onOnHandleCurrentChange:T},null,8,["minHeight","table-options","table-data"])]}),_:1})])}}}),xe=ie(de,[["__scopeId","data-v-c77754f4"]]);export{xe as default};
