var N=Object.defineProperty;var C=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var L=(a,o,l)=>o in a?N(a,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[o]=l,z=(a,o)=>{for(var l in o||(o={}))E.call(o,l)&&L(a,l,o[l]);if(C)for(var l of C(o))Q.call(o,l)&&L(a,l,o[l]);return a};var _=(a,o,l)=>new Promise((v,u)=>{var b=t=>{try{p(l.next(t))}catch(c){u(c)}},d=t=>{try{p(l.throw(t))}catch(c){u(c)}},p=t=>t.done?v(t.value):Promise.resolve(t.value).then(b,d);p((l=l.apply(a,o)).next())});import{aQ as h,aR as y,d as T,l as n,P as A,aN as G,n as K,ao as j,T as J,r as V,o as X,c as Z,e as W,b as g,h as H,_ as $}from"./index-B63pSD2p.js";import ee from"./detail-BDKwNJxA.js";import{f as te}from"./handleMethod-BIjqYEft.js";import{c as ae,a as le}from"./calculateTableHeight-BjE6OFD1.js";const oe=a=>h.request("post",y("/v1/logs/get_business_log"),{data:a}),ne=a=>h.request("get",y("/v1/logs/get_user_names"),{data:a}),se=a=>h.request("get",y("/v1/logs/get_module_list"),{data:a}),re=a=>h.request("post",y("/v1/logs/get_page_list"),{data:a}),ie={class:"zf-first-box"},pe={class:"zf-second-box"},ue=T({name:"business-log",__name:"index",setup(a){const o=n(null),l=n(null),v=n([{label:"新增",value:1},{label:"删除",value:2},{label:"修改",value:3}]),u=n({}),b=n([]),d=n([]),p=n([]),t=n({}),c=A({labelWidth:"68px",itemWidth:"200px",fields:[{label:"操作人",prop:"op_user_name",type:"select",defaultValue:"",placeholder:"请选择",clearable:!0,props:{value:"op_user_id",label:"op_user_name",key:"op_user_id"},optionData:()=>p.value},{label:"操作时间",prop:"operation_time",type:"daterange",defaultValue:"",placeholder:"请选择",clearable:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",optionData:()=>[]},{label:"操作类型",prop:"op_type",type:"select",defaultValue:"",placeholder:"请选择",clearable:!0,optionData:()=>v.value},{label:"所属模块",prop:"module",type:"select",defaultValue:"",placeholder:"请选择",clearable:!0,props:{value:"module_id",label:"module_name",key:"module_id"},optionData:()=>b.value},{label:"所属页面",prop:"page",type:"select",defaultValue:"",placeholder:"请选择",clearable:!0,props:{value:"sub_module_id",label:"page_name",key:"sub_module_id"},optionData:()=>d.value}]}),r=n({field:[{prop:"ip",label:"用户IP",minWidth:"100px"},{prop:"op_content",label:"操作内容",minWidth:"180px"},{prop:"module_name",label:"所属模块",minWidth:"100px"},{prop:"page_name",label:"所属页面",minWidth:"100px"},{prop:"op_type",label:"操作类型",minWidth:"80px",formatter:e=>te(e.op_type,v.value)},{prop:"op_user_name",label:"操作人",minWidth:"100px"},{prop:"op_time",label:"操作时间",minWidth:"120px"},{prop:"",label:"操作",type:"template",minWidth:"80px",fixed:"right",templateGroup:[{title:()=>G("business-log/detail")?"详情":"",clickBtn(e){Y(e)}}]}],styleOptions:{isShowSort:!0,isShowSelection:!1,rowKey:"group_id"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),w=n([]),k=n([]),x=n(!1),F=n("");let S=null;K(()=>{ae(S,l.value,r.value,!1),R(),P(),f()}),j(()=>{le(S)});const R=()=>_(this,null,function*(){const e=yield se({});b.value=(e==null?void 0:e.data)||[]}),P=()=>_(this,null,function*(){const e=yield ne({});p.value=(e==null?void 0:e.data)||[]}),q=()=>_(this,null,function*(){const e=yield re({module_id:t.value.module});d.value=(e==null?void 0:e.data)||[]}),M=(e,s)=>{e.prop==="module"&&(d.value=[],t.value.page&&(t.value.page=null),s&&q())},f=()=>{var m;let{currentPage:e,pageSize:s}=r.value.pageOptions;(m=t.value.operation_time)!=null&&m.length?(t.value.start_time=t.value.operation_time[0],t.value.end_time=t.value.operation_time[1]):(delete t.value.start_time,delete t.value.end_time);let D=z({current_page:e,page_size:s},t.value);oe(D).then(i=>{i.code&&i.code===200?(w.value=i.data.list,r.value.pageOptions.total=i.data.total):J.error(i.msg)})},I=e=>{r.value.pageOptions.pageSize=e,f()},U=e=>{r.value.pageOptions.currentPage=e,f()},B=e=>{k.value=e},Y=e=>_(this,null,function*(){u.value=e,x.value=!0});return(e,s)=>{const D=V("form-component"),m=V("el-card"),i=V("table-component");return X(),Z("div",ie,[W("div",pe,[g(m,null,{default:H(()=>[W("div",{ref_key:"formDivRef",ref:l},[g(D,{ref_key:"formRef",ref:o,modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=O=>t.value=O),"form-options":c,"is-query-btn":!0,onOnchangeFn:M,onQueryDataFn:f},null,8,["modelValue","form-options"])],512)]),_:1}),g(m,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:H(()=>[g(i,{minHeight:r.value.styleOptions.minHeight,"table-options":r.value,"table-data":w.value,onOnHandleSizeChange:I,onOnHandleCurrentChange:U,onOnHandleSelectionChange:B},null,8,["minHeight","table-options","table-data"])]),_:1})]),g(ee,{drawerVisible:x.value,"onUpdate:drawerVisible":s[1]||(s[1]=O=>x.value=O),logId:F.value,detailData:u.value},null,8,["drawerVisible","logId","detailData"])])}}}),ve=$(ue,[["__scopeId","data-v-d737e9e8"]]);export{ve as default};
