var z=Object.defineProperty;var P=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var L=(o,a,r)=>a in o?z(o,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[a]=r,E=(o,a)=>{for(var r in a||(a={}))I.call(a,r)&&L(o,r,a[r]);if(P)for(var r of P(a))M.call(a,r)&&L(o,r,a[r]);return o};var x=(o,a,r)=>new Promise((v,h)=>{var y=i=>{try{n(r.next(i))}catch(f){h(f)}},c=i=>{try{n(r.throw(i))}catch(f){h(f)}},n=i=>i.done?v(i.value):Promise.resolve(i.value).then(y,c);n((r=r.apply(o,a)).next())});import{g as R,p as W}from"./common-methods-BWkba4Bo.js";import{i as $,g as G}from"./validate-Dc6ka3px.js";import{e as J,f as K,h as Q}from"./user-management-B1vGxPiG.js";import{p as X}from"./index-CMAj5lxj.js";import{d as Y,l as p,P as Z,n as ee,r as F,o as le,g as te,h as D,e as C,b as T,f as k,C as re,T as m,_ as ae}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const oe={class:"zf-dialog-first-box"},se={class:"zf-dialog-second-box"},ie={class:"footer-btn"},ne=Y({__name:"add-expert",props:{rolesList:{type:Array,default:()=>{}},defaultPassword:{type:String,default:""},regionList:{type:Array,default:()=>{}}},emits:["queryData"],setup(o,{expose:a,emit:r}){const v=o,h=r,y=p("创建"),c=p(!1),n=p([]),i={username:[{required:!0,message:"请输入账号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(e,l,t)=>{if(l.length>50)return t(new Error("账号长度不能超过50！"));t()}}],name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]},{trigger:["blur","change"],validator:(e,l,t)=>{if(l.length>50)return t(new Error("姓名长度不能超过50！"));t()}}],role_id:[{required:!0,message:"请选择所属角色",trigger:["blur","change"]}],project_id_list:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id_list:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],id_card:[{trigger:["blur","change"],validator:(e,l,t)=>{if(!l||$(l))t();else return t(new Error("请输入正确的身份证号（18位）！"))}}],phone:[{trigger:["blur","change"],validator:(e,l,t)=>{if(!l||G(l))t();else return t(new Error("请正确的手机号码（11位）！"))}}]},f=[{label:"用户名",prop:"name",type:"input",defaultValue:"",placeholder:"请输入用户名",isHidden:!1,clearable:!0},{label:"身份证号",prop:"id_card",type:"input",defaultValue:"",placeholder:"请输入身份证号",isHidden:!1,clearable:!0},{label:"用户账号",prop:"username",type:"input",defaultValue:"",placeholder:"请输入用户账号",isHidden:!1,clearable:!0},{label:"所属角色",prop:"role_id",type:"select",clearable:!0,defaultValue:[],multiple:!0,collapseTags:!0,placeholder:"请选择所属角色",optionData:()=>[]},{label:"所属资格",prop:"project_id_list",type:"select",clearable:!0,defaultValue:"",filterable:!0,multiple:!0,collapseTags:!0,placeholder:"请选择所属资格",optionData:()=>W.value},{label:"所属科目",prop:"subject_id_list",type:"select",clearable:!0,defaultValue:null,filterable:!0,multiple:!0,collapseTags:!0,props:{value:"subject_id",label:"subject_name"},placeholder:"请选择所属科目",optionData:()=>n.value},{label:"所属轮次",prop:"round_count",type:"select",clearable:!0,defaultValue:null,filterable:!0,multiple:!1,placeholder:"请选择所属轮次",optionData:()=>[{label:"第1轮",value:1},{label:"第2轮",value:2},{label:"第3轮",value:3},{label:"第4轮",value:4},{label:"第5轮",value:5},{label:"第6轮",value:6}]},{label:"行政区域",prop:"region",type:"cascader",defaultValue:"",placeholder:"请选择行政区域",optionData:()=>[],props:{checkStrictly:!0,value:"code",label:"name"},clearable:!0},{label:"所在单位",prop:"work_unit",type:"input",defaultValue:"",placeholder:"请输入所在单位",isHidden:!1,clearable:!0},{label:"手机号",prop:"phone",type:"input",defaultValue:"",placeholder:"请输入手机号",isHidden:!1,clearable:!0}],_=p({}),j=Z({column:3,inline:!1,labelWidth:"108px",itemWidth:"250px",rules:i,fields:f}),u=p(null),q=p({}),d=p("04");ee(()=>{R()});const B=(e,l)=>{c.value=!0,d.value=e,y.value="创建专家",d.value==="02"&&(y.value="编辑",q.value=l,l.project_id_list&&H(l.project_id_list),re(()=>{var s,g,b;j.fields.map(V=>{V.prop==="username"&&l.is_used&&(V.disabled=!0),l.hasOwnProperty(V.prop)&&u.value.setCardData(V.prop,l[V.prop])});const t=[(s=l.province_code)!=null?s:"",(g=l.city_code)!=null?g:"",(b=l.district_code)!=null?b:""].filter(Boolean);u.value.setCardData("region",t),setTimeout(()=>{u.value.clearValidateFn()},100)})),j.fields.map(t=>{t.prop==="project_id_list"?t.isHidden&&(t.isHidden=!1):t.prop==="subject_id_list"&&t.isHidden&&(t.isHidden=!1),t.prop==="role_id"?t.optionData=()=>v.rolesList:t.prop==="defaultPwd"?t.text=()=>`<span style="color: #F56C6C">${v.defaultPassword}</span>`:t.prop==="region"&&(t.optionData=()=>v.regionList)}),setTimeout(()=>{u.value.clearValidateFn()},100)},w=()=>{c.value=!1,j.fields=f,j.fields.map(e=>{e.prop==="username"&&(e.disabled=!1)}),u.value.resetFieldsFn()},A=(e,l)=>{if((d.value==="01"||d.value==="04")&&e.prop==="name"&&l){const t=X(l,{toneType:"none",type:"array"}),s=t[0]+t.slice(1).map(g=>g[0]).join("");u.value.setCardData("username",s)}},N=(e,l)=>x(this,null,function*(){if(e.prop==="project_id_list"&&(n.value=[],l)){yield H();const t=n.value.map(s=>s.subject_id);_.value.subject_id_list=_.value.subject_id_list.filter(s=>t.includes(s))}}),O=()=>{u.value.formValidate().then(()=>{let e=E({},_.value);e.region&&e.region.length>0&&(e.province_code=e.region[0],e.city_code=e.region[1]?e.region[1]:null,e.district_code=e.region[2]?e.region[2]:null,delete e.region),e.system_user_type=2,(!e.project_id_list||e.project_id_list.length===0)&&delete e.project_id_list,(!e.subject_id_list||e.subject_id_list.length===0)&&delete e.subject_id_list,delete e.defaultPwd,d.value==="01"||d.value==="04"?S(e):d.value==="02"&&(e.user_id=q.value.user_id,e.role_id_list=e.role_id,delete e.role_id,delete e.username,U(e))}).catch(()=>{m.warning("请按要求填写！")})},S=e=>{J(e).then(l=>{l.code&&l.code===200?(m.success(l.msg),h("queryData"),w()):m.warning(l.msg)})},U=e=>{K(e).then(l=>{l.code&&l.code===200?(m.success(l.msg),h("queryData"),w()):m.warning(l.msg)})},H=e=>x(this,null,function*(){let l={project_id_list:e||_.value.project_id_list};const t=yield Q(l);t.code&&t.code===200?n.value=t.data.data:m.error(t.msg)});return a({openDialog:B}),(e,l)=>{const t=F("form-component"),s=F("el-button"),g=F("el-dialog");return le(),te(g,{modelValue:c.value,"onUpdate:modelValue":l[1]||(l[1]=b=>c.value=b),title:y.value,width:"840px","close-on-click-modal":!1,"before-close":w,"align-center":"",draggable:""},{footer:D(()=>[C("div",ie,[T(s,{onClick:w},{default:D(()=>[k("取消")]),_:1}),T(s,{type:"primary",onClick:O},{default:D(()=>[k("保存")]),_:1})])]),default:D(()=>[C("div",oe,[C("div",se,[T(t,{ref_key:"formRef",ref:u,modelValue:_.value,"onUpdate:modelValue":l[0]||(l[0]=b=>_.value=b),"form-options":j,"is-query-btn":!1,onOnblurFn:A,onOnchangeFn:N},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}}),be=ae(ne,[["__scopeId","data-v-d38f5098"]]);export{be as default};
