import{d as y,m as b,l as g,aa as c,r as s,o as m,c as d,b as l,h as o,e as n,t as w,F as C,p as k,q as x,N as S,I as V,aF as B}from"./index-B63pSD2p.js";const N=n("p",{class:"mb-2"}," 模拟后台根据不同角色返回对应路由，观察左侧菜单变化（管理员角色可查看系统管理菜单、普通角色不可查看系统管理菜单） ",-1),P={class:"card-header"},H=y({name:"PermissionPage",__name:"index",setup(F){var t;const i=b(()=>({width:"85vw",justifyContent:"start"})),a=g((t=c())==null?void 0:t.username),p=[{value:"admin",label:"管理员角色"},{value:"common",label:"普通角色"}];function _(){c().loginByUsername({username:a.value,password:"admin123"}).then(r=>{r.success&&(S().removeItem("async-routes"),V().clearAllCachePage(),B())})}return(r,u)=>{const v=s("el-option"),f=s("el-select"),h=s("el-card");return m(),d("div",null,[N,l(h,{shadow:"never",style:x(i.value)},{header:o(()=>[n("div",P,[n("span",null,"当前角色："+w(a.value),1)])]),default:o(()=>[l(f,{modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=e=>a.value=e),class:"!w-[160px]",onChange:_},{default:o(()=>[(m(),d(C,null,k(p,e=>l(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["style"])])}}});export{H as default};
