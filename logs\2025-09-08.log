2025-09-08 15:04:52.025 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.040 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.070 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.077 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.122 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.136 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.204 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.212 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.233 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.247 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.303 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.310 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.370 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.378 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.447 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.454 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.507 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.514 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.544 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.551 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.564 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.571 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:04:52.584 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:04:52.590 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 15:05:05.322 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:05:05.532 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:05:05.536 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:05:05.551 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:05:05.555 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:05:05.561 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:05:05.567 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:05:05.639 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:05:10.986 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:05:10.992 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:05:10.996 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:05:10.999 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:05:11.001 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:05:11.004 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:05:11.008 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:05:11.010 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:05:11.288 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:05:11.320 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:07:48.171 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:07:48.380 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:07:48.383 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:07:48.396 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:07:48.401 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:07:48.403 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:07:48.408 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:07:48.474 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:07:48.779 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:07:48.810 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:08:38.447 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:08:38.633 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:08:38.636 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:08:38.649 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:08:38.652 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:08:38.654 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:08:38.657 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:08:38.752 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:08:39.671 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:08:39.696 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:09:38.565 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:09:38.565 | INFO | views.get_verify_result_list:146 - 1获取资格科目核验状态列表
2025-09-08 15:09:38.572 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:09:42.508 | INFO | views.get_verification_results_by_category:84 - 1按核验分类获取核验结果列表
2025-09-08 15:09:42.717 | INFO | views.get_verify_result_list:146 - 1获取资格科目核验状态列表
2025-09-08 15:10:55.028 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:10:55.028 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 15:10:55.029 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:10:55.032 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:10:55.034 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:10:55.037 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:11:03.320 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:12:46.622 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:12:46.624 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:12:46.631 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:10.199 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:10.202 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 15:13:10.204 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:10.202 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:11.213 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:11.215 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:12.673 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:14.945 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:14.948 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:14.950 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:13:15.968 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 15:13:15.968 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:15.969 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:15.974 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:15.977 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:15.980 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:21.453 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:13:21.459 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:13:21.462 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:13:21.466 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:13:21.539 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:13:22.009 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 15:13:22.012 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:22.056 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:22.060 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:22.061 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:22.063 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:34.830 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:36.213 | INFO | stu_grade_views.get_subject_grade_detail:277 - admin 获取评分详情
2025-09-08 15:13:40.048 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:13:40.063 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:13:42.519 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:42.521 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 15:13:42.523 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 15:13:42.524 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:13:42.525 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:13:42.529 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:14:13.700 | INFO | user_module_views.get_user_module:26 - admin123 获取 user_id 为 30394923833607943230586880 的用户的功能权限
2025-09-08 15:14:13.703 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:14:13.705 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:14:13.709 | INFO | user_module_views.get_user_module:43 - admin123 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:14:13.810 | INFO | views.delete_func_point:254 - admin123 获取系统版本号
2025-09-08 15:14:15.325 | INFO | views.get_project:132 - admin123 获取资格列表
2025-09-08 15:14:15.329 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:14:15.330 | INFO | views.get_repeat_task_list:302 - admin123 获取复评任务列表
2025-09-08 15:14:26.305 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:14:26.508 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:14:26.511 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:14:26.551 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:14:26.554 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:14:26.556 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:14:26.560 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:14:26.849 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:14:27.926 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:14:27.946 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:14:35.314 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:14:38.158 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:14:39.621 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:14:39.624 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:14:39.625 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:14:45.302 | INFO | views.sample_stu_list:76 - admin 获取抽样
2025-09-08 15:14:45.306 | ERROR | views.sample_stu_list:130 - 更新任务状态失败，Sample larger than population or is negativeTraceback (most recent call last):
  File "apps\human_repeat_mark\views.py", line 96, in sample_stu_list
  File "random.py", line 434, in sample
ValueError: Sample larger than population or is negative

2025-09-08 15:14:50.895 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:14:50.896 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:14:50.901 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:15:47.455 | INFO | views.is_has_several_role:101 - 检查 huany 该用户是否有多个角色
2025-09-08 15:15:47.647 | WARNING | services.verify_login:101 - huany 登录失败，账号或密码不正确。
2025-09-08 15:15:48.437 | INFO | views.is_has_several_role:101 - 检查 huany 该用户是否有多个角色
2025-09-08 15:15:48.625 | WARNING | services.verify_login:101 - huany 登录失败，账号或密码不正确。
2025-09-08 15:15:49.441 | INFO | views.is_has_several_role:101 - 检查 huany 该用户是否有多个角色
2025-09-08 15:15:49.625 | WARNING | services.verify_login:101 - huany 登录失败，账号或密码不正确。
2025-09-08 15:15:50.394 | INFO | views.is_has_several_role:101 - 检查 huany 该用户是否有多个角色
2025-09-08 15:15:50.577 | WARNING | services.verify_login:101 - huany 登录失败，账号或密码不正确。
2025-09-08 15:15:51.615 | INFO | views.is_has_several_role:101 - 检查 huany 该用户是否有多个角色
2025-09-08 15:15:51.804 | WARNING | services.verify_login:101 - huany 登录失败，账号或密码不正确。
2025-09-08 15:17:04.562 | INFO | views.is_has_several_role:101 - 检查 liw 该用户是否有多个角色
2025-09-08 15:17:04.746 | WARNING | services.verify_login:101 - liw 登录失败，账号或密码不正确。
2025-09-08 15:17:05.544 | INFO | views.is_has_several_role:101 - 检查 liw 该用户是否有多个角色
2025-09-08 15:17:05.733 | WARNING | services.verify_login:101 - liw 登录失败，账号或密码不正确。
2025-09-08 15:17:06.542 | INFO | views.is_has_several_role:101 - 检查 liw 该用户是否有多个角色
2025-09-08 15:17:06.729 | WARNING | services.verify_login:101 - liw 登录失败，账号或密码不正确。
2025-09-08 15:17:07.534 | INFO | views.is_has_several_role:101 - 检查 liw 该用户是否有多个角色
2025-09-08 15:17:07.721 | WARNING | services.verify_login:101 - liw 登录失败，账号或密码不正确。
2025-09-08 15:17:08.790 | INFO | views.is_has_several_role:101 - 检查 liw 该用户是否有多个角色
2025-09-08 15:17:08.976 | WARNING | services.verify_login:101 - liw 登录失败，账号或密码不正确。
2025-09-08 15:17:46.549 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:17:46.739 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:17:46.742 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:17:46.779 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:17:46.784 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:17:46.786 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:17:46.790 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:17:46.919 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:17:47.412 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:17:47.432 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:24:43.233 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:24:43.235 | INFO | ques_type_views.get_ques_type:19 - admin 获取题型信息
2025-09-08 15:24:43.237 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:24:43.240 | INFO | ques_type_views.get_ques_type:44 - 获取题型信息成功
2025-09-08 15:24:43.242 | INFO | views.get_ques_list:41 - admin 获取试题列表
2025-09-08 15:24:43.423 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 15:27:59.521 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:27:59.524 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:27:59.527 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:27:59.531 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:27:59.719 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:28:00.686 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:28:00.686 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:28:00.690 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:28:14.108 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:28:14.122 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:28:17.706 | INFO | views.get_ques_list:41 - admin 获取试题列表
2025-09-08 15:28:17.706 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:28:17.712 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:28:17.713 | INFO | ques_type_views.get_ques_type:19 - admin 获取题型信息
2025-09-08 15:28:17.718 | INFO | ques_type_views.get_ques_type:44 - 获取题型信息成功
2025-09-08 15:28:17.870 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 15:31:39.821 | INFO | views.get_region:764 - admin 获取行政区域信息
2025-09-08 15:31:39.822 | INFO | role_views.get_role:48 - admin 获取角色列表
2025-09-08 15:31:39.825 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 15:31:39.828 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:31:39.829 | INFO | role_views.get_role:102 - 获取角色列表成功
2025-09-08 15:31:39.832 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:31:39.833 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:31:39.836 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:31:39.852 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 15:31:39.853 | INFO | views.get_default_pwd:754 - admin 获取默认密码
2025-09-08 15:31:59.529 | INFO | views.get_ques_detail:29 - admin 获取人工阅卷小组
2025-09-08 15:31:59.539 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:31:59.542 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:32:10.615 | INFO | views.get_ques_detail:29 - admin 获取人工阅卷小组
2025-09-08 15:32:10.616 | INFO | views.filter_human_group_member:174 - admin 过滤阅卷小组可选择的组员
2025-09-08 15:32:10.617 | INFO | views.get_human_group_member:243 - admin 获取人工阅卷小组组员
2025-09-08 15:32:11.637 | INFO | views.filter_human_group_member:174 - admin 过滤阅卷小组可选择的组员
2025-09-08 15:32:34.085 | INFO | views.get_ques_detail:257 - admin 获取试题详情
2025-09-08 15:32:34.223 | INFO | views.get_ques_detail:257 - admin 获取试题详情
2025-09-08 15:32:58.424 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:32:58.425 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:32:58.428 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:33:05.605 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:33:05.608 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:33:05.610 | INFO | mark_rule_views.get_mark_rule:67 - admin 获取评分规则列表
2025-09-08 15:33:05.615 | INFO | mark_rule_views.get_mark_rule:130 - 获取评分规则列表成功
2025-09-08 15:37:30.588 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:37:30.588 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 15:37:30.588 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 15:37:30.592 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:37:32.616 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:37:32.619 | INFO | views.get_task_list:511 - admin 获取任务列表
2025-09-08 15:37:32.624 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:39:13.486 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:39:13.489 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 15:39:13.511 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 15:39:13.513 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 15:39:13.489 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:39:14.499 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:39:16.708 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:39:16.717 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:52:17.902 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:52:17.905 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:53:11.618 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:53:11.618 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 15:53:11.621 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:53:11.622 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 15:53:11.624 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:53:11.645 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 15:53:13.148 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:53:13.157 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:53:20.261 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:53:22.677 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:53:22.684 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:53:22.979 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:53:30.680 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:55:45.068 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:55:45.073 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 15:55:45.075 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:55:45.097 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 15:55:45.075 | INFO | survey_monitor_views.quality_monitor_api:1476 - admin 质检监控
2025-09-08 15:55:47.900 | INFO | views.get_quality_stu_list:147 - admin 获取已质检考生列表
2025-09-08 15:55:47.911 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:55:47.914 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:56:04.992 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:56:04.996 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:56:10.784 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 15:56:10.968 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 15:56:10.971 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 15:56:10.990 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:56:10.993 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:56:10.995 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:56:11.000 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:56:11.110 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:56:11.958 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 15:56:11.978 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 15:56:17.854 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 15:56:17.855 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:56:17.858 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:56:17.859 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 15:56:17.878 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 15:56:17.880 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:56:19.088 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:56:19.091 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:57:56.652 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:57:56.659 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:58:08.333 | INFO | views.get_subject:351 - admin 获取科目列表
2025-09-08 15:58:08.340 | INFO | views.get_subject:415 - 获取科目列表成功
2025-09-08 15:58:14.534 | INFO | views.get_stu_list:137 - admin 获取考生作答列表
2025-09-08 15:58:27.562 | INFO | views.verify_unlock_screen:148 - admin 解锁屏幕
2025-09-08 15:58:27.748 | INFO | views.verify_unlock_screen:155 - admin 解锁屏幕成功
2025-09-08 15:59:07.773 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:59:09.161 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 15:59:09.163 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 15:59:09.166 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 15:59:09.170 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 15:59:09.265 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 15:59:09.655 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:59:11.138 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 15:59:11.139 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 15:59:11.145 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 15:59:15.210 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:01.866 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:01:01.867 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:01:01.870 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:01.892 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:01:01.893 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:01.898 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:03.949 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:01:03.949 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 16:01:03.949 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:04.965 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:04.966 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:04.969 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:07.091 | INFO | views.get_subject:351 - admin 获取科目列表
2025-09-08 16:01:07.095 | INFO | views.get_subject:415 - 获取科目列表成功
2025-09-08 16:01:08.200 | INFO | exam_paper_views.get_paper:168 - admin 获取试卷信息列表
2025-09-08 16:01:08.206 | INFO | exam_paper_views.get_paper:300 - 获取试卷信息列表成功
2025-09-08 16:01:11.374 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:01:12.329 | INFO | stu_grade_views.get_subject_grade_detail:277 - admin 获取评分详情
2025-09-08 16:01:16.167 | INFO | stu_grade_views.get_subject_grade_detail:277 - admin 获取评分详情
2025-09-08 16:01:26.615 | INFO | views.get_stu_list:137 - admin 获取考生作答列表
2025-09-08 16:01:28.908 | INFO | views.create_sample:189 - admin 创建复评
2025-09-08 16:01:29.168 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:38.002 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:38.008 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:38.012 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:40.113 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:45.673 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:45.674 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:45.677 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:48.384 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:48.387 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:48.388 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:01:50.544 | INFO | views.get_default_pwd:754 - admin 获取默认密码
2025-09-08 16:01:50.545 | INFO | views.get_region:764 - admin 获取行政区域信息
2025-09-08 16:01:50.549 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:50.553 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:50.555 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:50.558 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:50.559 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:01:50.582 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:01:50.584 | INFO | role_views.get_role:48 - admin 获取角色列表
2025-09-08 16:01:50.587 | INFO | role_views.get_role:102 - 获取角色列表成功
2025-09-08 16:01:54.268 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:01:54.277 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:01:59.981 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:01:59.985 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:01:59.985 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:00.165 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:02:00.186 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:02:02.428 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:02:02.445 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:02:03.146 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:03.151 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:03.152 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:06.565 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:02:06.583 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:02:18.350 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:18.351 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:18.358 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:24.378 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:24.381 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:24.382 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:25.745 | INFO | views.is_has_several_role:101 - 检查 fpy26 该用户是否有多个角色
2025-09-08 16:02:25.929 | INFO | views.is_has_several_role:119 - fpy26 用户无多个角色，直接登录
2025-09-08 16:02:25.933 | INFO | views.is_has_several_role:122 - fpy26 登录成功
2025-09-08 16:02:28.651 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:28.651 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:28.654 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:30.204 | INFO | views.first_change_password:499 - 修改成功
2025-09-08 16:02:30.440 | INFO | user_module_views.get_user_module:26 - fpy26 获取 user_id 为 125717236122115112960 的用户的功能权限
2025-09-08 16:02:30.447 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:02:30.450 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:02:30.452 | INFO | user_module_views.get_user_module:43 - fpy26 用户无特定功能权限，按照角色 9 功能权限分配
2025-09-08 16:02:30.680 | INFO | views.delete_func_point:254 - fpy26 获取系统版本号
2025-09-08 16:02:30.958 | INFO | views.get_repeat_task_list:302 - fpy26 获取复评任务列表
2025-09-08 16:02:33.761 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:02:48.818 | INFO | views.get_region:764 - admin 获取行政区域信息
2025-09-08 16:02:48.821 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:02:48.824 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:48.828 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:48.843 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:02:48.846 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:02:48.849 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:02:48.850 | INFO | role_views.get_role:48 - admin 获取角色列表
2025-09-08 16:02:48.854 | INFO | role_views.get_role:102 - 获取角色列表成功
2025-09-08 16:02:48.818 | INFO | views.get_default_pwd:754 - admin 获取默认密码
2025-09-08 16:02:53.760 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:02:53.779 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:06.489 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:06.502 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:10.993 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:11.008 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:18.143 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:18.154 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:22.185 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:22.208 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:25.281 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:03:25.283 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:03:25.285 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:03:27.868 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:03:27.869 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:03:27.873 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:03:31.959 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:03:31.960 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:03:31.963 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:03:37.500 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:37.524 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:03:47.331 | INFO | views.get_subject_by_project_list:812 - admin 根据多个资格获取科目列表
2025-09-08 16:03:47.333 | INFO | views.get_subject_by_project_list:827 - 根据多个资格获取科目列表成功
2025-09-08 16:03:50.812 | INFO | views.update_user:567 - admin 编辑用户信息，id 为 125717236122115112960
2025-09-08 16:03:50.826 | INFO | views.update_user:646 - 编辑用户信息成功
2025-09-08 16:03:50.883 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:03:50.906 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:04:00.871 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:04:00.872 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:04:00.875 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:04:03.835 | INFO | views.get_repeat_task_list:302 - fpy26 获取复评任务列表
2025-09-08 16:04:24.407 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:04:28.149 | INFO | views.update_task_state_api:503 - admin 更新复评任务 125757585777029021696 的状态为 
2025-09-08 16:04:28.182 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:04:32.492 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:04:32.495 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:04:32.497 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:04:32.501 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:04:32.682 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:04:36.652 | INFO | views.is_has_several_role:101 - 检查 fpy26 该用户是否有多个角色
2025-09-08 16:04:36.836 | INFO | views.is_has_several_role:119 - fpy26 用户无多个角色，直接登录
2025-09-08 16:04:36.839 | INFO | views.is_has_several_role:122 - fpy26 登录成功
2025-09-08 16:04:37.060 | INFO | user_module_views.get_user_module:26 - fpy26 获取 user_id 为 125717236122115112960 的用户的功能权限
2025-09-08 16:04:37.063 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:04:37.065 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:04:37.068 | INFO | user_module_views.get_user_module:43 - fpy26 用户无特定功能权限，按照角色 9 功能权限分配
2025-09-08 16:04:37.295 | INFO | views.delete_func_point:254 - fpy26 获取系统版本号
2025-09-08 16:04:37.574 | INFO | views.get_repeat_task_list:302 - fpy26 获取复评任务列表
2025-09-08 16:04:41.057 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:04:41.057 | INFO | views.get_subject:351 - fpy26 获取科目列表
2025-09-08 16:04:41.065 | INFO | views.get_subject:415 - 获取科目列表成功
2025-09-08 16:04:41.114 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:04:41.298 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:04:43.481 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:04:43.484 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:04:43.486 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:04:43.490 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:04:43.647 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:04:46.928 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:04:46.931 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:04:46.933 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:04:46.937 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:04:47.104 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:04:48.034 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:04:48.036 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:04:48.040 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:18.424 | INFO | views.get_subject:351 - fpy26 获取科目列表
2025-09-08 16:05:18.428 | INFO | views.get_subject:415 - 获取科目列表成功
2025-09-08 16:05:18.430 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:05:18.470 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:05:18.650 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:05:19.776 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:19.784 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:19.787 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:25.541 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:05:27.360 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:27.360 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:27.364 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:27.783 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:05:29.280 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:05:43.357 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:05:46.977 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:46.977 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:46.980 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:47.866 | INFO | stu_grade_views.get_subject_grade_detail:277 - admin 获取评分详情
2025-09-08 16:05:51.621 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:51.624 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:51.625 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:53.050 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:53.050 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:53.054 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:05:54.976 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:05:55.801 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:05:55.809 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:05:55.812 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:06:01.676 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:06:01.677 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:06:01.680 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:06:02.399 | INFO | views.repeat_stu_socre:638 - fpy26 获取复评考生评分
2025-09-08 16:06:02.690 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:06:02.734 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:06:02.931 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:06:08.461 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:06:21.715 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:06:21.715 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:06:21.718 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:07:02.797 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:07:02.803 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:07:02.805 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:07:02.809 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:07:02.934 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:07:03.396 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:07:03.396 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:07:03.400 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:07:18.633 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:07:18.633 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:07:18.639 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:07:52.757 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:07:52.758 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:07:52.761 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:07:57.129 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:07:57.130 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:07:57.133 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:07.831 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:07.832 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:07.836 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:10.653 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:10.655 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:10.657 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:15.370 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:15.390 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:15.392 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:22.286 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:22.287 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:22.294 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:42.803 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:08:49.155 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:08:49.192 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:08:52.119 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:52.123 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:52.124 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:53.303 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:08:53.347 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:08:53.530 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:08:54.065 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:54.069 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:54.070 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:55.648 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:08:57.181 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:08:57.181 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:08:57.185 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:08:59.065 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:08:59.118 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:03.935 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:05.291 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:09:05.322 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:05.605 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:06.875 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:09:09.018 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:09:09.054 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:16.281 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:18.722 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:19.908 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:20.991 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:22.881 | INFO | views.repeat_stu_socre:638 - fpy26 获取复评考生评分
2025-09-08 16:09:23.141 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:09:23.168 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:09:23.203 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:23.370 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:26.381 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:09:27.665 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:09:27.700 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:33.042 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:09:33.096 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:09:33.281 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:09:37.585 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:37.592 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:37.597 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:37.601 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:37.798 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:38.738 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:38.742 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:38.763 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:39.794 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:39.805 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:39.807 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:39.811 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:40.796 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:40.805 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:40.807 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:40.811 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:40.981 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:41.972 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:41.972 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:41.975 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:44.243 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:44.251 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:44.253 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:44.257 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:44.396 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:45.192 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:45.196 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:45.204 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:46.446 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:46.454 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:46.458 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:46.463 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:46.637 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:47.643 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:47.646 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:47.661 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:51.389 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:51.392 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:51.394 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:51.398 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:51.572 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:52.461 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:52.463 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:52.468 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:54.387 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:54.389 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:54.392 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:54.499 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:54.774 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:55.745 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:55.749 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:55.751 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:09:57.381 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:09:57.391 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:09:57.397 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:09:57.403 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:09:57.602 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:09:58.518 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:09:58.518 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:09:58.523 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:10:01.419 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:10:01.422 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:10:01.424 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:10:01.428 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:10:01.649 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:10:02.209 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:10:02.209 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:02.213 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:10:24.251 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:10:24.255 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:10:24.571 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:26.365 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:28.401 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:32.443 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:10:32.443 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:10:32.447 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:10:32.450 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:10:32.468 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:10:32.472 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:35.639 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:10:48.464 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:10:50.814 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:10:50.866 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:12:07.347 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:12:07.354 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:12:07.358 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:12:07.362 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:12:07.461 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:12:07.901 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:12:11.587 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:12:11.625 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:13:54.826 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:13:54.829 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:13:54.831 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:13:54.835 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:13:55.032 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:13:55.803 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:14:01.343 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:14:01.346 | INFO | views.get_my_human_task_list:33 - admin 获取评阅员的任务列表
2025-09-08 16:14:01.351 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:14:02.774 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:14:02.774 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:14:02.778 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:14:02.793 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:14:03.094 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:14:02.774 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:14:30.086 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:14:30.089 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:14:30.091 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:14:30.094 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:14:30.181 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:14:30.648 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:14:30.648 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:14:30.649 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:14:30.652 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:14:30.667 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:14:30.652 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:15:58.713 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:15:58.723 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:15:58.725 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:15:58.729 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:15:58.852 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:15:59.289 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:15:59.290 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:15:59.293 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:15:59.294 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:15:59.308 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:15:59.313 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:16:01.515 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:01.524 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:11.971 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:16:11.974 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:16:11.976 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:16:11.980 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:16:12.043 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:16:12.420 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:12.420 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:16:12.422 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:16:12.424 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:12.429 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:16:12.447 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:16:13.689 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:13.693 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:38.452 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:16:39.819 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:16:40.390 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:16:40.738 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:16:40.741 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:16:40.743 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:16:40.748 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:16:40.805 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:16:41.199 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:41.199 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:16:41.201 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:16:41.203 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:41.219 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:16:41.708 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:16:41.204 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:16:43.854 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:43.859 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:46.296 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:16:46.299 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:16:46.301 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:16:46.306 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:16:46.441 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:16:46.842 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:46.842 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:16:46.844 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:16:46.846 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:16:46.846 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:16:46.862 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:16:47.976 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:16:47.980 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:17:06.901 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:17:23.090 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:17:23.096 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:17:23.099 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:17:23.103 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:17:23.168 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:17:23.536 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:17:23.537 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:17:23.539 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:17:23.541 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:17:23.542 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:17:23.555 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:17:25.618 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:17:25.626 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:17:47.338 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 16:17:47.360 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 16:17:54.826 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - admin阅卷组长页面： 获取评卷统计信息
2025-09-08 16:17:54.840 | INFO | p_score_analysis_views.get_total_statistics_api:1341 - 阅卷组长页面：获取评卷统计信息完成，共 2 个科目
2025-09-08 16:18:26.837 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:18:26.839 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:18:26.842 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:18:26.846 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:18:26.908 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:18:27.279 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:18:27.279 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:18:27.282 | INFO | views.get_repeat_task_round:47 - admin 获取复评任务轮次
2025-09-08 16:18:27.284 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:18:27.299 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:18:27.284 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:18:31.705 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:18:36.002 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:18:38.287 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:18:38.292 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:18:38.287 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:18:54.363 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:18:58.422 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:26.314 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:19:26.315 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:26.320 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:19:33.618 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:33.628 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:19:33.631 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:19:34.431 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:19:34.436 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:19:35.045 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:19:34.431 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:38.218 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:19:38.220 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:19:38.222 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:19:38.227 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:19:38.307 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:19:38.761 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:19:38.764 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:19:38.761 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:41.613 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:19:41.636 | ERROR | views.export_tech_acceptance_excel:885 - 导出技术验收Excel失败: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757879102089199616.xlsx'Traceback (most recent call last):
  File "apps\human_repeat_mark\views.py", line 872, in export_tech_acceptance_excel
  File "apps\human_repeat_mark\services.py", line 77, in export_technology
  File "openpyxl\workbook\workbook.py", line 386, in save
  File "openpyxl\writer\excel.py", line 291, in save_workbook
  File "zipfile\__init__.py", line 1367, in __init__
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757879102089199616.xlsx'

2025-09-08 16:20:02.158 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:02.159 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:02.165 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:03.321 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:03.321 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:03.324 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:07.147 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:07.148 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:07.150 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:12.588 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:12.588 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:12.591 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:13.872 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:13.876 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:13.872 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:16.389 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:20:16.390 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:16.392 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:20:19.153 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:19.177 | ERROR | views.export_tech_acceptance_excel:885 - 导出技术验收Excel失败: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757889179424391168.xlsx'Traceback (most recent call last):
  File "apps\human_repeat_mark\views.py", line 872, in export_tech_acceptance_excel
  File "apps\human_repeat_mark\services.py", line 77, in export_technology
  File "openpyxl\workbook\workbook.py", line 386, in save
  File "openpyxl\writer\excel.py", line 291, in save_workbook
  File "zipfile\__init__.py", line 1367, in __init__
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757889179424391168.xlsx'

2025-09-08 16:20:25.402 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:25.750 | ERROR | views.export_tech_acceptance_excel:885 - 导出技术验收Excel失败: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757890943851167744.xlsx'Traceback (most recent call last):
  File "apps\human_repeat_mark\views.py", line 872, in export_tech_acceptance_excel
  File "apps\human_repeat_mark\services.py", line 77, in export_technology
  File "openpyxl\workbook\workbook.py", line 386, in save
  File "openpyxl\writer\excel.py", line 291, in save_workbook
  File "zipfile\__init__.py", line 1367, in __init__
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757890943851167744.xlsx'

2025-09-08 16:20:32.724 | INFO | views.get_repeat_task_list:302 - admin 获取复评任务列表
2025-09-08 16:20:33.070 | ERROR | views.export_tech_acceptance_excel:885 - 导出技术验收Excel失败: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757892908798705664.xlsx'Traceback (most recent call last):
  File "apps\human_repeat_mark\views.py", line 872, in export_tech_acceptance_excel
  File "apps\human_repeat_mark\services.py", line 77, in export_technology
  File "openpyxl\workbook\workbook.py", line 386, in save
  File "openpyxl\writer\excel.py", line 291, in save_workbook
  File "zipfile\__init__.py", line 1367, in __init__
FileNotFoundError: [Errno 2] No such file or directory: 'D:\\工信阅卷\\090801\\human_read_paper_250908\\human_read_paper_250908\\server_static/export/125757892908798705664.xlsx'

2025-09-08 16:20:34.946 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:21:34.942 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:22:34.938 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:23:34.945 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:23:41.729 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:23:42.132 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:23:45.669 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:23:46.896 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:23:47.423 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:23:47.870 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:24:08.058 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:24:08.601 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:24:12.469 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:24:12.471 | INFO | views.create_ai_grade_process:211 - admin 获取生成成绩进度
2025-09-08 16:24:12.472 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:24:12.472 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:24:12.469 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:24:13.479 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:24:15.393 | INFO | stu_grade_views.get_subject_grade_list:230 - admin 查询科目成绩
2025-09-08 16:24:19.002 | INFO | stu_grade_views.get_subject_grade_detail:277 - admin 获取评分详情
2025-09-08 16:24:34.949 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:25:34.955 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:25:36.780 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:38.144 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:39.887 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:41.871 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:43.574 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:45.205 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:48.204 | INFO | views.repeat_stu_socre:638 - fpy26 获取复评考生评分
2025-09-08 16:25:48.471 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:25:48.471 | INFO | views.get_repeat_task_stu:524 - fpy26 获取未复评任务考生
2025-09-08 16:25:49.573 | INFO | views.get_ques_detail:267 - fpy26 获取试题详情
2025-09-08 16:25:49.767 | INFO | views.get_step_by_ques_code:684 - fpy26 根据试题编号获取任务评分步长
2025-09-08 16:25:55.627 | INFO | views.get_repeat_round_detail_list:742 - fpy26 根据复评任务id和复评用户id获取复评详情列表
2025-09-08 16:26:34.960 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:27:34.954 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:27:38.540 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:27:38.540 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:27:38.543 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:27:38.540 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:27:49.147 | INFO | views.launch_human_mark_task:394 - admin 发起人工阅卷任务
2025-09-08 16:27:49.151 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:27:49.157 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 16:27:49.193 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:27:52.010 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:27:52.018 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 16:27:52.022 | INFO | services.distribute_human_task_data:367 - 为 125732862623765495808 轮次自动分配作答数据
2025-09-08 16:27:52.022 | INFO | services.distribute_human_task_data:369 - 为 125732862623765495808 轮次自动分配 3 个考生的作答数据成功
2025-09-08 16:28:07.226 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:28:07.227 | INFO | manual_work_flow_views.create_manual_group:53 - admin 获取人工阅卷流程列表
2025-09-08 16:28:07.228 | INFO | views.get_ques_list:41 - admin 获取试题列表
2025-09-08 16:28:07.232 | INFO | manual_work_flow_views.create_manual_group:134 - 获取人工阅卷流程列表成功
2025-09-08 16:28:10.668 | INFO | views.get_subject:351 - admin 获取科目列表
2025-09-08 16:28:10.669 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:28:10.671 | INFO | views.get_subject:415 - 获取科目列表成功
2025-09-08 16:28:15.546 | INFO | views.check_human_task_exist:38 - admin 检查人工阅卷任务是否已经创建
2025-09-08 16:28:15.617 | INFO | views.get_small_human_group:374 - admin 获取人工评阅阅卷小组
2025-09-08 16:28:19.286 | INFO | views.create_mark_task:74 - admin 创建人工阅卷任务
2025-09-08 16:28:19.300 | INFO | views.create_mark_task:131 - 创建人工阅卷任务成功，共创建 1 个任务
2025-09-08 16:28:19.354 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:28:22.643 | INFO | views.launch_human_mark_task:394 - admin 发起人工阅卷任务
2025-09-08 16:28:22.647 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:28:22.654 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 16:28:34.519 | INFO | views.get_ques_detail:29 - admin 获取人工阅卷小组
2025-09-08 16:28:34.525 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:28:34.528 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:28:34.988 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:28:41.963 | INFO | views.get_parent_human_group:61 - admin 获取父级小组编号和名称
2025-09-08 16:28:41.964 | INFO | views.get_human_group_member:243 - admin 获取人工阅卷小组组员
2025-09-08 16:28:41.964 | INFO | views.get_group_union_ques:107 - admin 获取父级小组编号和名称
2025-09-08 16:28:41.972 | INFO | views.filter_human_group_member:174 - admin 过滤阅卷小组可选择的组员
2025-09-08 16:28:46.260 | INFO | views.get_ques_detail:29 - admin 获取人工阅卷小组
2025-09-08 16:28:46.263 | INFO | views.filter_human_group_member:174 - admin 过滤阅卷小组可选择的组员
2025-09-08 16:28:46.274 | INFO | views.get_human_group_member:243 - admin 获取人工阅卷小组组员
2025-09-08 16:28:46.264 | INFO | views.filter_human_group_member:174 - admin 过滤阅卷小组可选择的组员
2025-09-08 16:28:52.024 | INFO | services.distribute_human_task_data:367 - 为 125732862623765495808 轮次自动分配作答数据
2025-09-08 16:28:52.025 | INFO | services.distribute_human_task_data:369 - 为 125732862623765495808 轮次自动分配 2 个考生的作答数据成功
2025-09-08 16:28:55.271 | INFO | views.update_human_group:297 - admin 编辑人工阅卷小组
2025-09-08 16:28:55.358 | INFO | views.get_ques_detail:29 - admin 获取人工阅卷小组
2025-09-08 16:29:04.252 | INFO | views.launch_human_mark_task:394 - admin 发起人工阅卷任务
2025-09-08 16:29:04.256 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:29:04.263 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 16:29:04.278 | ERROR | services.save_round_distri_answer:140 - Traceback (most recent call last):
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
pymysql.err.OperationalError: (1526, 'Table has no partition for value from column_list')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "apps\human_task_manage\services.py", line 136, in save_round_distri_answer
  File "sqlalchemy\orm\session.py", line 4578, in bulk_save_objects
  File "sqlalchemy\orm\session.py", line 4755, in _bulk_save_mappings
  File "sqlalchemy\util\langhelpers.py", line 224, in __exit__
  File "sqlalchemy\orm\session.py", line 4744, in _bulk_save_mappings
  File "sqlalchemy\orm\bulk_persistence.py", line 222, in _bulk_insert
  File "sqlalchemy\orm\persistence.py", line 1048, in _emit_insert_statements
  File "sqlalchemy\engine\base.py", line 1415, in execute
  File "sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
  File "sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
  File "sqlalchemy\engine\base.py", line 1842, in _execute_context
  File "sqlalchemy\engine\base.py", line 1982, in _exec_single_context
  File "sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1526, 'Table has no partition for value from column_list')
[SQL: INSERT INTO t_human_round_distri_answer (distri_id, round_id, stu_secret_num, ques_code, ques_id, answer_id, is_distri, quality_state, is_again_mark, created_time) VALUES (%(distri_id)s, %(round_id)s, %(stu_secret_num)s, %(ques_code)s, %(ques_id)s, %(answer_id)s, %(is_distri)s, %(quality_state)s, %(is_again_mark)s, %(created_time)s)]
[parameters: [{'distri_id': '125758030133271986176', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110078058496', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000040', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266827)}, {'distri_id': '125758030133271986177', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110078058498', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000046', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266829)}, {'distri_id': '125758030133271986178', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110346493952', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000063', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266830)}, {'distri_id': '125758030133271986179', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110346493954', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000064', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266831)}, {'distri_id': '125758030133271986180', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929410', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000001', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266831)}, {'distri_id': '125758030133271986181', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929412', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000015', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266831)}, {'distri_id': '125758030133271986182', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929414', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000019', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266832)}, {'distri_id': '125758030133271986183', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929416', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000098', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266832)}  ... displaying 10 of 11 total bound parameter sets ...  {'distri_id': '125758030133271986185', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929420', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000079', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266833)}, {'distri_id': '125758030133271986186', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929422', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000082', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 4, 266833)}]]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-09-08 16:29:19.928 | INFO | views.launch_human_mark_task:394 - admin 发起人工阅卷任务
2025-09-08 16:29:19.933 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:29:19.940 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 16:29:19.956 | ERROR | services.save_round_distri_answer:140 - Traceback (most recent call last):
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
pymysql.err.OperationalError: (1526, 'Table has no partition for value from column_list')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "apps\human_task_manage\services.py", line 136, in save_round_distri_answer
  File "sqlalchemy\orm\session.py", line 4578, in bulk_save_objects
  File "sqlalchemy\orm\session.py", line 4755, in _bulk_save_mappings
  File "sqlalchemy\util\langhelpers.py", line 224, in __exit__
  File "sqlalchemy\orm\session.py", line 4744, in _bulk_save_mappings
  File "sqlalchemy\orm\bulk_persistence.py", line 222, in _bulk_insert
  File "sqlalchemy\orm\persistence.py", line 1048, in _emit_insert_statements
  File "sqlalchemy\engine\base.py", line 1415, in execute
  File "sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
  File "sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
  File "sqlalchemy\engine\base.py", line 1842, in _execute_context
  File "sqlalchemy\engine\base.py", line 1982, in _exec_single_context
  File "sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1526, 'Table has no partition for value from column_list')
[SQL: INSERT INTO t_human_round_distri_answer (distri_id, round_id, stu_secret_num, ques_code, ques_id, answer_id, is_distri, quality_state, is_again_mark, created_time) VALUES (%(distri_id)s, %(round_id)s, %(stu_secret_num)s, %(ques_code)s, %(ques_id)s, %(answer_id)s, %(is_distri)s, %(quality_state)s, %(is_again_mark)s, %(created_time)s)]
[parameters: [{'distri_id': '125758034341534892032', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110078058496', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000040', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944333)}, {'distri_id': '125758034341534892033', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110078058498', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000046', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944336)}, {'distri_id': '125758034341534892034', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110346493952', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000063', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944336)}, {'distri_id': '125758034341534892035', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110346493954', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000064', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944336)}, {'distri_id': '125758034341534892036', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929410', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000001', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944337)}, {'distri_id': '125758034341534892037', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929412', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000015', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944337)}, {'distri_id': '125758034341534892038', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929414', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000019', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944338)}, {'distri_id': '125758034341534892039', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929416', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000098', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944338)}  ... displaying 10 of 11 total bound parameter sets ...  {'distri_id': '125758034341534892041', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929420', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000079', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944339)}, {'distri_id': '125758034341534892042', 'round_id': '125758018059581784064', 'stu_secret_num': '125715458110614929422', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000082', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 16, 29, 19, 944339)}]]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-09-08 16:29:34.949 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:29:40.703 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:29:40.729 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:29:52.024 | INFO | services.distribute_human_task_data:367 - 为 125732862623765495808 轮次自动分配作答数据
2025-09-08 16:29:52.025 | INFO | services.distribute_human_task_data:369 - 为 125732862623765495808 轮次自动分配 1 个考生的作答数据成功
2025-09-08 16:29:54.827 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:29:54.854 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:30:02.766 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:30:02.795 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:30:34.951 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:31:34.950 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:31:41.881 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:31:41.913 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:32:15.377 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:32:15.399 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:32:32.084 | INFO | views.get_subject_by_project_list:812 - admin 根据多个资格获取科目列表
2025-09-08 16:32:32.086 | INFO | views.get_subject_by_project_list:827 - 根据多个资格获取科目列表成功
2025-09-08 16:32:34.941 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:32:36.893 | INFO | views.register:34 - admin 创建用户
2025-09-08 16:33:25.326 | INFO | views.get_subject_by_project_list:812 - admin 根据多个资格获取科目列表
2025-09-08 16:33:25.328 | INFO | views.get_subject_by_project_list:827 - 根据多个资格获取科目列表成功
2025-09-08 16:33:29.468 | INFO | views.register:34 - admin 创建用户
2025-09-08 16:33:35.130 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:33:53.741 | INFO | views.register:34 - admin 创建用户
2025-09-08 16:33:53.950 | INFO | views.register:90 - xiaob 创建用户成功
2025-09-08 16:33:53.997 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 16:33:54.014 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 16:34:34.942 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:35:34.944 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:36:34.953 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:37:34.960 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:38:34.955 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:39:34.953 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:44:19.393 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:44:19.393 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:44:19.406 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:44:19.410 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:44:19.412 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:44:19.415 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:44:19.419 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:44:19.421 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:44:19.718 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:44:19.749 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:44:26.317 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:44:26.322 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:44:26.326 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:44:26.328 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:44:28.343 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:44:30.417 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:44:30.421 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:44:30.426 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:44:30.431 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:44:30.494 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:44:30.786 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:44:30.789 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:44:30.793 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:44:30.797 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:44:33.028 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 1 的用户的功能权限
2025-09-08 16:44:33.032 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 16:44:33.036 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 16:44:33.041 | INFO | user_module_views.get_user_module:43 - admin 用户无特定功能权限，按照角色 1 功能权限分配
2025-09-08 16:44:33.097 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 16:44:33.384 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 16:44:33.385 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 16:44:33.386 | INFO | views.get_paper:559 - admin 获取所有业务题型
2025-09-08 16:44:33.390 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 16:45:08.361 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 16:46:08.267 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:30:50.576 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 17:30:50.823 | WARNING | services.verify_login:101 - admin 登录失败，账号或密码不正确。
2025-09-08 17:30:57.347 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 17:30:57.553 | WARNING | services.verify_login:101 - admin 登录失败，账号或密码不正确。
2025-09-08 17:31:04.506 | INFO | views.is_has_several_role:101 - 检查 admin123 该用户是否有多个角色
2025-09-08 17:31:32.955 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:31:49.625 | INFO | views.is_has_several_role:101 - 检查 admin 该用户是否有多个角色
2025-09-08 17:31:49.811 | INFO | views.is_has_several_role:119 - admin 用户无多个角色，直接登录
2025-09-08 17:31:49.819 | INFO | views.is_has_several_role:122 - admin 登录成功
2025-09-08 17:31:49.839 | INFO | user_module_views.get_user_module:26 - admin 获取 user_id 为 30394923833607943230586880 的用户的功能权限
2025-09-08 17:31:49.844 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 17:31:49.846 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 17:31:49.851 | INFO | user_module_views.get_user_module:41 - admin 用户有特定的功能权限
2025-09-08 17:31:49.891 | INFO | views.delete_func_point:254 - admin 获取系统版本号
2025-09-08 17:31:50.096 | INFO | views.get_sys_module:27 - admin 获取系统模块列表
2025-09-08 17:31:50.101 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 17:31:50.103 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 17:32:23.711 | INFO | views.get_region:764 - admin 获取行政区域信息
2025-09-08 17:32:23.712 | INFO | role_views.get_role:48 - admin 获取角色列表
2025-09-08 17:32:23.716 | INFO | views.get_default_pwd:754 - admin 获取默认密码
2025-09-08 17:32:23.718 | INFO | role_views.get_role:102 - 获取角色列表成功
2025-09-08 17:32:23.724 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 17:32:23.730 | INFO | views.get_project:132 - admin 获取资格列表
2025-09-08 17:32:23.733 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:32:23.737 | INFO | views.get_user:252 - admin 获取用户信息列表
2025-09-08 17:32:23.738 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:32:23.766 | INFO | views.get_user:351 - 获取用户信息列表成功
2025-09-08 17:32:32.956 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:32:50.890 | INFO | views.is_has_several_role:101 - 检查 张三 该用户是否有多个角色
2025-09-08 17:32:51.094 | INFO | views.is_has_several_role:119 - 张三 用户无多个角色，直接登录
2025-09-08 17:32:51.098 | INFO | views.is_has_several_role:122 - 张三 登录成功
2025-09-08 17:32:51.111 | INFO | user_module_views.get_user_module:26 - 张三 获取 user_id 为 125716423408540188672 的用户的功能权限
2025-09-08 17:32:51.115 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 17:32:51.118 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 17:32:51.122 | INFO | user_module_views.get_user_module:41 - 张三 用户有特定的功能权限
2025-09-08 17:32:51.161 | INFO | views.delete_func_point:254 - 张三 获取系统版本号
2025-09-08 17:32:51.448 | INFO | p_score_analysis_views.get_total_statistics_api:1044 - 张三阅卷组长页面： 获取评卷统计信息
2025-09-08 17:33:17.634 | INFO | views.get_project:132 - 张三 获取资格列表
2025-09-08 17:33:17.636 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:33:17.638 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:33:17.639 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:33:19.279 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:33:19.283 | INFO | views.get_ques_list:41 - 张三 获取试题列表
2025-09-08 17:33:19.308 | INFO | views.get_ques_list:56 - 更新redis里的试题信息
2025-09-08 17:33:19.341 | INFO | services.add_f_business_data:450 - 补充组合题业务试题数据
2025-09-08 17:33:19.279 | INFO | manual_work_flow_views.create_manual_group:53 - 张三 获取人工阅卷流程列表
2025-09-08 17:33:20.294 | INFO | manual_work_flow_views.create_manual_group:134 - 获取人工阅卷流程列表成功
2025-09-08 17:33:24.230 | INFO | views.check_human_task_exist:38 - 张三 检查人工阅卷任务是否已经创建
2025-09-08 17:33:24.283 | INFO | views.get_small_human_group:374 - 张三 获取人工评阅阅卷小组
2025-09-08 17:33:32.953 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:33:33.715 | INFO | views.create_try_mark_task:27 - 张三 创建人工阅卷试评任务
2025-09-08 17:33:33.729 | INFO | views.create_try_mark_task:79 - 创建人工阅卷试评任务成功，共创建 1 个任务
2025-09-08 17:33:33.769 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:33:37.424 | INFO | views.launch_human_mark_task:394 - 张三 发起人工阅卷任务
2025-09-08 17:33:37.429 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:33:37.438 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:33:37.460 | ERROR | services.save_round_distri_answer:140 - Traceback (most recent call last):
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
pymysql.err.OperationalError: (1526, 'Table has no partition for value from column_list')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "apps\human_task_manage\services.py", line 136, in save_round_distri_answer
  File "sqlalchemy\orm\session.py", line 4578, in bulk_save_objects
  File "sqlalchemy\orm\session.py", line 4755, in _bulk_save_mappings
  File "sqlalchemy\util\langhelpers.py", line 224, in __exit__
  File "sqlalchemy\orm\session.py", line 4744, in _bulk_save_mappings
  File "sqlalchemy\orm\bulk_persistence.py", line 222, in _bulk_insert
  File "sqlalchemy\orm\persistence.py", line 1048, in _emit_insert_statements
  File "sqlalchemy\engine\base.py", line 1415, in execute
  File "sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
  File "sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
  File "sqlalchemy\engine\base.py", line 1842, in _execute_context
  File "sqlalchemy\engine\base.py", line 1982, in _exec_single_context
  File "sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1526, 'Table has no partition for value from column_list')
[SQL: INSERT INTO t_human_round_distri_answer (distri_id, round_id, stu_secret_num, ques_code, ques_id, answer_id, is_distri, quality_state, is_again_mark, created_time) VALUES (%(distri_id)s, %(round_id)s, %(stu_secret_num)s, %(ques_code)s, %(ques_id)s, %(answer_id)s, %(is_distri)s, %(quality_state)s, %(is_again_mark)s, %(created_time)s)]
[parameters: [{'distri_id': '125759069832651210752', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607663214604', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000019', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 37, 448877)}, {'distri_id': '125759069832651210753', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607931650051', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000028', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 37, 448881)}, {'distri_id': '125759069832651210754', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691606857908224', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000064', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 37, 448881)}, {'distri_id': '125759069832651210755', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607663214600', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000001', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 37, 448882)}, {'distri_id': '125759069832651210756', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691606589472770', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000046', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 37, 448882)}]]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-09-08 17:33:46.591 | INFO | views.launch_human_mark_task:394 - 张三 发起人工阅卷任务
2025-09-08 17:33:46.598 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:33:46.616 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:33:46.637 | ERROR | services.save_round_distri_answer:140 - Traceback (most recent call last):
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
pymysql.err.OperationalError: (1526, 'Table has no partition for value from column_list')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "apps\human_task_manage\services.py", line 136, in save_round_distri_answer
  File "sqlalchemy\orm\session.py", line 4578, in bulk_save_objects
  File "sqlalchemy\orm\session.py", line 4755, in _bulk_save_mappings
  File "sqlalchemy\util\langhelpers.py", line 224, in __exit__
  File "sqlalchemy\orm\session.py", line 4744, in _bulk_save_mappings
  File "sqlalchemy\orm\bulk_persistence.py", line 222, in _bulk_insert
  File "sqlalchemy\orm\persistence.py", line 1048, in _emit_insert_statements
  File "sqlalchemy\engine\base.py", line 1415, in execute
  File "sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
  File "sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
  File "sqlalchemy\engine\base.py", line 1842, in _execute_context
  File "sqlalchemy\engine\base.py", line 1982, in _exec_single_context
  File "sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1526, 'Table has no partition for value from column_list')
[SQL: INSERT INTO t_human_round_distri_answer (distri_id, round_id, stu_secret_num, ques_code, ques_id, answer_id, is_distri, quality_state, is_again_mark, created_time) VALUES (%(distri_id)s, %(round_id)s, %(stu_secret_num)s, %(ques_code)s, %(ques_id)s, %(answer_id)s, %(is_distri)s, %(quality_state)s, %(is_again_mark)s, %(created_time)s)]
[parameters: [{'distri_id': '125759072295009386496', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607931650049', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000098', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 46, 622041)}, {'distri_id': '125759072295009386497', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691606589472772', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000063', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 46, 622044)}, {'distri_id': '125759072295009386498', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607663214604', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000019', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 46, 622045)}, {'distri_id': '125759072295009386499', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691607663214600', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000001', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 46, 622045)}, {'distri_id': '125759072295009386500', 'round_id': '125759068831387222016', 'stu_secret_num': '125714691606857908224', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000064', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 33, 46, 622046)}]]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-09-08 17:33:53.676 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:33:53.676 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:33:53.678 | INFO | views.get_project:132 - 张三 获取资格列表
2025-09-08 17:33:54.686 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:33:56.827 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:33:56.828 | INFO | manual_work_flow_views.create_manual_group:53 - 张三 获取人工阅卷流程列表
2025-09-08 17:33:56.829 | INFO | views.get_ques_list:41 - 张三 获取试题列表
2025-09-08 17:33:56.834 | INFO | manual_work_flow_views.create_manual_group:134 - 获取人工阅卷流程列表成功
2025-09-08 17:34:00.165 | INFO | views.check_human_task_exist:38 - 张三 检查人工阅卷任务是否已经创建
2025-09-08 17:34:00.216 | INFO | views.get_small_human_group:374 - 张三 获取人工评阅阅卷小组
2025-09-08 17:34:02.213 | INFO | views.create_mark_task:74 - 张三 创建人工阅卷任务
2025-09-08 17:34:02.224 | INFO | views.create_mark_task:131 - 创建人工阅卷任务成功，共创建 1 个任务
2025-09-08 17:34:02.261 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:34:04.855 | INFO | views.launch_human_mark_task:394 - 张三 发起人工阅卷任务
2025-09-08 17:34:04.859 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:34:04.869 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:34:04.886 | ERROR | services.save_round_distri_answer:140 - Traceback (most recent call last):
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
pymysql.err.OperationalError: (1526, 'Table has no partition for value from column_list')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "apps\human_task_manage\services.py", line 136, in save_round_distri_answer
  File "sqlalchemy\orm\session.py", line 4578, in bulk_save_objects
  File "sqlalchemy\orm\session.py", line 4755, in _bulk_save_mappings
  File "sqlalchemy\util\langhelpers.py", line 224, in __exit__
  File "sqlalchemy\orm\session.py", line 4744, in _bulk_save_mappings
  File "sqlalchemy\orm\bulk_persistence.py", line 222, in _bulk_insert
  File "sqlalchemy\orm\persistence.py", line 1048, in _emit_insert_statements
  File "sqlalchemy\engine\base.py", line 1415, in execute
  File "sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
  File "sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
  File "sqlalchemy\engine\base.py", line 1842, in _execute_context
  File "sqlalchemy\engine\base.py", line 1982, in _exec_single_context
  File "sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
  File "sqlalchemy\engine\base.py", line 1932, in _exec_single_context
  File "sqlalchemy\dialects\mysql\mysqldb.py", line 172, in do_executemany
  File "pymysql\cursors.py", line 182, in executemany
  File "pymysql\cursors.py", line 220, in _do_execute_many
  File "pymysql\cursors.py", line 153, in execute
  File "pymysql\cursors.py", line 322, in _query
  File "pymysql\connections.py", line 563, in query
  File "pymysql\connections.py", line 825, in _read_query_result
  File "pymysql\connections.py", line 1199, in read
  File "pymysql\connections.py", line 775, in _read_packet
  File "pymysql\protocol.py", line 219, in raise_for_error
  File "pymysql\err.py", line 150, in raise_mysql_exception
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1526, 'Table has no partition for value from column_list')
[SQL: INSERT INTO t_human_round_distri_answer (distri_id, round_id, stu_secret_num, ques_code, ques_id, answer_id, is_distri, quality_state, is_again_mark, created_time) VALUES (%(distri_id)s, %(round_id)s, %(stu_secret_num)s, %(ques_code)s, %(ques_id)s, %(answer_id)s, %(is_distri)s, %(quality_state)s, %(is_again_mark)s, %(created_time)s)]
[parameters: [{'distri_id': '125759077194224893952', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691606589472768', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000040', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872906)}, {'distri_id': '125759077194224893953', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691606589472770', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000046', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872909)}, {'distri_id': '125759077194224893954', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691606589472772', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000063', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872909)}, {'distri_id': '125759077194224893955', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691606857908224', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000064', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872910)}, {'distri_id': '125759077194224893956', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607663214600', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000001', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872910)}, {'distri_id': '125759077194224893957', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607663214602', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000015', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872911)}, {'distri_id': '125759077194224893958', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607663214604', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000019', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872911)}, {'distri_id': '125759077194224893959', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607931650049', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000098', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872911)}  ... displaying 10 of 11 total bound parameter sets ...  {'distri_id': '125759077194224893961', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607931650053', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000079', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872912)}, {'distri_id': '125759077194224893962', 'round_id': '125759076480723451904', 'stu_secret_num': '125714691607931650055', 'ques_code': '000008000000291FF7E5920010', 'ques_id': '000008000000291FEC2A94000F', 'answer_id': '0210000082', 'is_distri': 0, 'quality_state': 0, 'is_again_mark': 0, 'created_time': datetime.datetime(2025, 9, 8, 17, 34, 4, 872912)}]]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-09-08 17:34:32.959 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:35:32.960 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:36:32.954 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:37:32.953 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:38:32.953 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:39:32.954 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:40:32.958 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:41:32.966 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:42:32.965 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:43:32.958 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:44:32.959 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:45:32.957 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:46:32.966 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:47:32.967 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:48:32.958 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:49:32.969 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:50:38.395 | INFO | user_module_views.get_user_module:26 - 张三 获取 user_id 为 125716423408540188672 的用户的功能权限
2025-09-08 17:50:38.398 | INFO | user_module_views.get_user_module:26 - 张三 获取 user_id 为 125716423408540188672 的用户的功能权限
2025-09-08 17:50:38.403 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 17:50:38.406 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 17:50:38.410 | INFO | user_module_views.get_user_module:41 - 张三 用户有特定的功能权限
2025-09-08 17:50:38.437 | INFO | views.delete_func_point:254 - 张三 获取系统版本号
2025-09-08 17:50:38.403 | INFO | services.get_module_info:140 - 获取系统模块列表成功
2025-09-08 17:50:39.422 | INFO | services.get_func_point_info:163 - 获取系统功能点成功
2025-09-08 17:50:39.434 | INFO | user_module_views.get_user_module:41 - 张三 用户有特定的功能权限
2025-09-08 17:50:39.473 | INFO | views.delete_func_point:254 - 张三 获取系统版本号
2025-09-08 17:50:48.655 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:50:48.661 | INFO | views.get_project:132 - 张三 获取资格列表
2025-09-08 17:50:48.670 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:50:48.675 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:50:50.935 | INFO | views.delete_human_mark_round:239 - 张三 删除人工阅卷任务
2025-09-08 17:50:50.963 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:50:54.185 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:50:54.185 | INFO | views.get_project:132 - 张三 获取资格列表
2025-09-08 17:50:55.193 | INFO | views.get_project:202 - 获取资格列表成功
2025-09-08 17:50:55.194 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:50:57.636 | INFO | views.delete_human_mark_round:239 - 张三 删除人工阅卷任务
2025-09-08 17:50:57.657 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:51:28.591 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:51:28.594 | INFO | views.get_ques_list:41 - 张三 获取试题列表
2025-09-08 17:51:28.636 | INFO | manual_work_flow_views.create_manual_group:53 - 张三 获取人工阅卷流程列表
2025-09-08 17:51:28.641 | INFO | manual_work_flow_views.create_manual_group:134 - 获取人工阅卷流程列表成功
2025-09-08 17:51:29.696 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:51:31.268 | INFO | views.check_human_task_exist:38 - 张三 检查人工阅卷任务是否已经创建
2025-09-08 17:51:31.317 | INFO | views.get_small_human_group:374 - 张三 获取人工评阅阅卷小组
2025-09-08 17:51:39.463 | INFO | views.create_try_mark_task:27 - 张三 创建人工阅卷试评任务
2025-09-08 17:51:39.473 | INFO | views.create_try_mark_task:79 - 创建人工阅卷试评任务成功，共创建 1 个任务
2025-09-08 17:51:39.511 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:51:41.887 | INFO | views.launch_human_mark_task:394 - 张三 发起人工阅卷任务
2025-09-08 17:51:41.893 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:51:41.910 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:51:41.943 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:51:46.615 | INFO | views.get_paper:559 - 张三 获取所有业务题型
2025-09-08 17:51:46.616 | INFO | manual_work_flow_views.create_manual_group:53 - 张三 获取人工阅卷流程列表
2025-09-08 17:51:46.617 | INFO | views.get_ques_list:41 - 张三 获取试题列表
2025-09-08 17:51:46.623 | INFO | manual_work_flow_views.create_manual_group:134 - 获取人工阅卷流程列表成功
2025-09-08 17:51:49.459 | INFO | views.check_human_task_exist:38 - 张三 检查人工阅卷任务是否已经创建
2025-09-08 17:51:49.511 | INFO | views.get_small_human_group:374 - 张三 获取人工评阅阅卷小组
2025-09-08 17:51:51.686 | INFO | views.create_mark_task:74 - 张三 创建人工阅卷任务
2025-09-08 17:51:51.696 | INFO | views.create_mark_task:131 - 创建人工阅卷任务成功，共创建 1 个任务
2025-09-08 17:51:51.729 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:51:54.533 | INFO | views.launch_human_mark_task:394 - 张三 发起人工阅卷任务
2025-09-08 17:51:54.537 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:51:54.545 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:51:54.573 | INFO | views.get_mark_task_list:142 - 张三 获取人工阅卷任务列表
2025-09-08 17:52:29.684 | INFO | services.query_and_insert_group_statistics:1361 - 小组统计数据处理完成
2025-09-08 17:52:34.581 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:52:34.588 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:52:34.599 | INFO | views.get_mark_task_list:142 - admin 获取人工阅卷任务列表
2025-09-08 17:52:34.607 | INFO | services.request_api:24 - 获取获取人工阅卷任务列表成功
2025-09-08 17:52:34.613 | INFO | services.distribute_human_task_data:367 - 为 125759363565197197312 轮次自动分配作答数据
2025-09-08 17:52:34.614 | INFO | services.distribute_human_task_data:369 - 为 125759363565197197312 轮次自动分配 3 个考生的作答数据成功
