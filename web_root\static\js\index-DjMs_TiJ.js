var S=Object.defineProperty;var f=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var _=(n,e,t)=>e in n?S(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,h=(n,e)=>{for(var t in e||(e={}))z.call(e,t)&&_(n,t,e[t]);if(f)for(var t of f(e))C.call(e,t)&&_(n,t,e[t]);return n};import{c as E,a as R}from"./calculateTableHeight-BjE6OFD1.js";import{p as H,g as P,a as q}from"./common-methods-BWkba4Bo.js";import{d as w,i as B,l as i,P as L,n as N,ao as U,r as m,o as A,c as G,e as g,b as c,h as v,_ as I}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const M={class:"zf-first-box"},Q={class:"zf-second-box"},T=w({name:"my-marking-task",__name:"index",setup(n){const e=B(),t=i(null),x=i(null),u=i({}),y=L({labelWidth:"68px",itemWidth:"160px",rules:{username:[{trigger:["blur","change"],validator:(a,l,r)=>{if(l&&l.length>50)return r(new Error("账号长度不能超过50！"));r()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>H.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>o.value},{label:"试卷",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择题型",optionData:()=>o.value},{label:"题型",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择题型",optionData:()=>o.value},{label:"题号",prop:"username1",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"username2",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"仲裁状态",prop:"subject_id1",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择仲裁状态",optionData:()=>[]}]}),o=i([]),p=i({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"name",label:"所属试卷",minWidth:"120px"},{prop:"name",label:"题号",minWidth:"120px"},{prop:"name1",label:"题型",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"task_name",label:"任务名称",minWidth:"120px"},{prop:"name2",label:"仲裁数量",minWidth:"120px"},{prop:"name3",label:"仲裁进度",minWidth:"140px",type:"progress"},{prop:"name4",label:"仲裁阈值",minWidth:"120px"},{prop:"group_level",label:"仲裁状态",minWidth:"120px"},{prop:"",label:"操作",type:"template",minWidth:"160px",fixed:"right",templateGroup:[{title:()=>"处理",clickBtn(a){e.push({path:"/manual-marking/marking-paper/index",query:{type:1}})}},{title:()=>"查看",clickBtn(a){e.push({path:"/manual-marking/marking-paper/index"})}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),j=i([{project_name:"系统分析师",subject_name:"系统分析师（案例分析）",process_name:"双评+仲裁",name:"-",name1:"问答题",ques_code:"0063089EF68827F08D476D0E6E",task_name:"系统分析师（案例分析）0063089EF68827F08D476D0E6E",name2:"23",name3:20,name4:"10%",group_level:"进行中",group_level1:"-",phone_encipher:1523,region:"83"}]);let d=null;N(()=>{P(),E(d,t.value,p.value,!1),s()}),U(()=>{R(d)});const D=(a,l)=>{a.prop==="project_id"&&(o.value=[],u.value.subject_id&&(u.value.subject_id=null),l&&q(l).then(r=>{o.value=r||[]}))},s=()=>{let{currentPage:a,pageSize:l}=p.value.pageOptions;h({current_page:a,page_size:l},u.value)},F=a=>{p.value.pageOptions.pageSize=a,s()},W=a=>{p.value.pageOptions.currentPage=a,s()};function k(){o.value=[]}return(a,l)=>{const r=m("form-component"),b=m("el-card"),O=m("table-component");return A(),G("div",M,[g("div",Q,[c(b,null,{default:v(()=>[g("div",{ref_key:"formDivRef",ref:t},[c(r,{ref_key:"formRef",ref:x,modelValue:u.value,"onUpdate:modelValue":l[0]||(l[0]=V=>u.value=V),"form-options":y,"is-query-btn":!0,onOnchangeFn:D,onQueryDataFn:s,onResetFields:k},null,8,["modelValue","form-options"])],512)]),_:1}),c(b,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:v(()=>[c(O,{minHeight:p.value.styleOptions.minHeight,"table-options":p.value,"table-data":j.value,onOnHandleSizeChange:F,onOnHandleCurrentChange:W},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),$=I(T,[["__scopeId","data-v-e8cecc68"]]);export{$ as default};
