import{aQ as e,aR as s}from"./index-B63pSD2p.js";const a=(t,n=!0)=>e.request("post",s("/v1/human_quality/quality_mark"),{data:t},{},n),r=(t,n=!0)=>e.request("post",s("/v1/human_quality/get_yet_quality_stu_list"),{data:t},{},n),o=t=>e.request("post",s("/v1/exception/get_answer_exception_list"),{data:t}),i=t=>e.request("post",s("/v1/exception/get_no_handle_exception_list"),{data:t}),u=t=>e.request("post",s("/v1/exception/handle_exception_answer"),{data:t});export{i as a,o as b,r as g,u as h,a as q};
