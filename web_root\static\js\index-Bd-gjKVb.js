var ve=Object.defineProperty;var Y=Object.getOwnPropertySymbols;var pe=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable;var K=(n,o,a)=>o in n?ve(n,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[o]=a,P=(n,o)=>{for(var a in o||(o={}))pe.call(o,a)&&K(n,a,o[a]);if(Y)for(var a of Y(o))_e.call(o,a)&&K(n,a,o[a]);return n};import{a as fe}from"./index-aLSsAUpX.js";import me from"./basic-monitor-jamMEtXh.js";import be from"./table-monitor-D2MxPGP1.js";import Q from"./barChart-BwsIMmQR.js";import{P as X}from"./processChart-BH-8wiYU.js";import{d as ge,l as s,n as he,a1 as ee,a2 as ye,P as De,r as C,j as je,o as c,c as $,ae as xe,e as l,b as g,u as ae,aT as te,w as se,g as p,h as oe,C as ke,N as Se,ac as we,ad as Pe,_ as Ce}from"./index-B63pSD2p.js";import"./index-C9GYnvBh.js";const L=n=>(we("data-v-718fded3"),n=n(),Pe(),n),$e={class:"dashboard"},Le={class:"h-full main-monitor"},Be={class:"left"},Ie={class:"l-top dark:!bg-black eye-box echart-card"},Te={class:"echart-title"},ze=L(()=>l("div",null,"各科目评分情况",-1)),Ae={class:"echart-wrap"},Ee={class:"l-bottom dark:!bg-black eye-box"},Me={class:"r-top dark:!bg-black eye-box"},We={class:"r-middle dark:!bg-black eye-box echart-card"},Ne={class:"echart-title"},Oe=L(()=>l("div",null,"评分总进度",-1)),Re={class:"echart-wrap"},Ge={class:"fullscreen-chart-wrapper"},He=L(()=>l("template",null,null,-1)),Ze=ge({__name:"index",setup(n){const o=s({task_type:1,round_count:"1"});he(()=>{S(),ce(B),ee.on("dataStatistics:reflushEcharts",e=>{o.value=e,ue()}),window.addEventListener("resize",S)}),ye(()=>{re(B),ee.off("dataStatistics:reflushEcharts"),window.addEventListener("resize",S)});const a=s([{name:"阅卷资格",value:"-",image:"yyjl"},{name:"参与人员",value:"-",image:"wxl"},{name:"阅卷科目",value:"-",image:"yxl"},{name:"阅卷总量(人题)",value:"-",image:"pjsd"}]),x=s([]),le=s(!1),_={xData:["","","","","",""],yAxis:{minInterval:1,axisLabel:{formatter:function(e){return e+"份"},fontSize:10},valueFormatter:e=>e+"份"},series:[{name:"已阅量",data:[0,0,0,0,0,0],color:"#409eff",barCategoryGap:"0%"},{name:"未阅量",data:[0,0,0,0,0,0],color:"#20c7a6",barCategoryGap:"0%"}]},f=s(_.xData),h=s(_.series),ne=s(!1),r=s(60),y=s(["已阅量: 1000","未阅量: 800"]),ie=s({field:[{prop:"subject_name",label:"科目",minWidth:"160px"},{prop:"count",label:"复评次数",minWidth:"75px"},{prop:"status",label:"状态",minWidth:"60px"}],styleOptions:{minHeight:"100",emptyImageSize:80,isShowSort:!1},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}});s([{subject_name:"软件设计师/案例分析(1)",count:1,status:"进行中"},{subject_name:"软件设计师/案例分析(1)",count:1,status:"进行中"},{subject_name:"软件设计师/案例分析(1)",count:1,status:"进行中"}]);let B=null;const D=s(),I=s(1),k=s();function S(){k.value&&(I.value=k.value.offsetWidth/408)}const ce=(e=null)=>{e=new ResizeObserver(t=>{t.forEach(v=>{D.value&&(ie.value.styleOptions.minHeight=(D.value.offsetHeight-60)/I.value+"px")})}),D.value&&e.observe(D.value)},re=e=>{e&&(e.disconnect(),e=null)};function ue(){f.value=_.xData,h.value=_.series,x.value=[],a.value[0].value="-",a.value[1].value="-",a.value[2].value="-",a.value[3].value="-",r.value=0,y.value=["已阅量: 0","未阅量: 0"],fe(P({},o.value)).then(e=>{var b,j,A,E,M,W,N,O,R,G,H,Z,q,F,V,J,U;const t=(j=(b=e.data)==null?void 0:b.workload_data)!=null?j:{};f.value=(E=(A=t.x_data)==null?void 0:A.map(d=>`${d.project_name}-${d.subject_name}`))!=null?E:[],t.legend&&(h.value=t.legend.map((d,w)=>({name:d,data:t[`y${w+1}_data`].length!==0?t[`y${w+1}_data`]:[],color:w===0?"#409eff":"#20c7a6",barCategoryGap:"0%"}))),((W=(M=e.data)==null?void 0:M.group_data)!=null?W:[]).forEach(d=>{x.value.push(P({title:`${d.project_name}-${d.subject_name}`},d))});const u=(O=(N=e.data)==null?void 0:N.total1)!=null?O:{};a.value[0].value=(R=u.project_count)!=null?R:"-",a.value[1].value=(G=u.expert_total)!=null?G:"-",a.value[2].value=(H=u.subject_count)!=null?H:"-",a.value[3].value=(Z=u.total_count_total)!=null?Z:"-";const m=(F=(q=e.data)==null?void 0:q.total2)!=null?F:{};r.value=(V=m.progress)!=null?V:0,y.value=[`已阅量: ${(J=m.reviewed_count_total)!=null?J:0}`,`未阅量: ${(U=m.unreviewed_count_total)!=null?U:0}`]})}const i=De({visible:!1,type:"",title:""}),T=()=>{i.type="",i.visible=!1};function z(e){e=="everySubjectProcess"?i.title="各科目评分情况":e=="pingfenTotalProcess"&&(i.title="评分总进度"),i.visible=!0,ke(()=>{i.type=e})}function de(e,t){const v=`pId_${e.project_id}_sId_${e.subject_id}`;Se().setItem(v,{subject_id:e.subject_id,subject_name:e.subject_name,project_id:e.project_id,project_name:e.project_name,pageName:"project"}),t({path:"/data-statistics/project-group-dashboard/index",query:{key:v}})}return(e,t)=>{const v=C("el-button"),u=C("el-empty"),m=C("DialogComponent"),b=je("loading");return c(),$("div",$e,[xe(e.$slots,"tab",{},void 0,!0),l("div",Le,[l("div",Be,[l("div",Ie,[l("div",Te,[ze,g(v,{class:"fullscreen-btn",title:"全屏",icon:ae(te),circle:"",text:"",onClick:t[0]||(t[0]=j=>z("everySubjectProcess"))},null,8,["icon"])]),se((c(),$("div",Ae,[f.value.length!==0?(c(),p(Q,{key:0,xData:f.value,barWidth:"10%",yAxis:_.yAxis,series:h.value,showLegend:!0},null,8,["xData","yAxis","series"])):(c(),p(u,{key:1,description:"暂无数据","image-size":80}))])),[[b,le.value]])]),l("div",Ee,[g(be,{tableListData:x.value,onOpenDetailPage:de},null,8,["tableListData"])])]),l("div",{class:"right",ref_key:"rightRef",ref:k},[l("div",Me,[g(me,{basicData:a.value},null,8,["basicData"])]),l("div",We,[l("div",Ne,[Oe,g(v,{class:"fullscreen-btn",title:"全屏",icon:ae(te),circle:"",text:"",onClick:t[1]||(t[1]=j=>z("pingfenTotalProcess"))},null,8,["icon"])]),se((c(),$("div",Re,[r.value!==void 0&&r.value!==null?(c(),p(X,{key:0,hiddenLegend:!1,data:r.value,legendData:y.value},null,8,["data","legendData"])):(c(),p(u,{key:1,description:"暂无数据","image-size":60}))])),[[b,ne.value]])])],512),g(m,{isShowDialog:i.visible,onCloseDialog:T,beforeClose:T,title:i.title,fullscreen:!0,class:"rootDialogClass"},{content:oe(()=>[l("div",Ge,[i.type==="everySubjectProcess"&&f.value.length!==0?(c(),p(Q,{key:0,xData:f.value,barWidth:"10%",yAxis:_.yAxis,series:h.value,showLegend:!0},null,8,["xData","yAxis","series"])):i.type==="pingfenTotalProcess"&&r.value!==void 0&&r.value!==null?(c(),p(X,{key:1,hiddenLegend:!1,data:r.value,fullScreen:!0,legendData:y.value},null,8,["data","legendData"])):(c(),p(u,{key:2,description:"暂无数据"}))])]),footer:oe(()=>[He]),_:1},8,["isShowDialog","title"])])])}}}),Xe=Ce(Ze,[["__scopeId","data-v-718fded3"]]);export{Xe as default};
