import{g as D}from"./examinees-management-aJjscxsO.js";import{g as T}from"./response-management-BIkrM71A.js";import{d as C,l as s,ao as P,r as _,o as v,g as p,h as S,e as g,c as q,t as m,u as f,y as h,aV as z,_ as E}from"./index-B63pSD2p.js";const F={class:"zf-dialog-first-box"},N={class:"zf-dialog-second-box"},U={key:0},A=C({__name:"import-stu-progress",emits:["queryData"],setup(L,{expose:x,emit:I}){const w=I,n=s("导入考生进度"),u=s(!1),a=s(0);let t=s(null),i=s(0);const d=s(!0);let l=s(0);const k=(o,e)=>{u.value=!0,o==="01"?(n.value="导入考生进度",y(e)):o==="02"&&(n.value="导入作答信息进度",i.value=e.record_id_list.length,V(e))},y=o=>{t.value&&clearInterval(t.value),t.value=setInterval(()=>{D(o).then(e=>{e.code&&e.code===200&&(a.value=e.data.data.progress,a.value===100&&setTimeout(()=>{c()},1e3))})},5e3)},V=o=>{t.value&&clearInterval(t.value),t.value=setInterval(()=>{const e={record_id:o.record_id_list[l.value]};T(e).then(r=>{r.code&&r.code===200&&(a.value=r.data.data.progress,a.value>=100&&(l.value+1>=i.value?(setTimeout(()=>{c()},1e3),z.alert("导入成功","提示",{confirmButtonText:"确定",type:"success"})):setTimeout(()=>{d.value=!1,a.value=0,setTimeout(()=>{d.value=!0},20),l.value+=1},1e3)))})},5e3),l.value=0},c=()=>{u.value=!1,a.value=0,t.value&&clearInterval(t.value),w("queryData")};return P(()=>{t.value&&clearInterval(t.value)}),x({openDialog:k,closeDialog:c}),(o,e)=>{const r=_("el-progress"),b=_("el-dialog");return v(),p(b,{modelValue:u.value,"onUpdate:modelValue":e[0]||(e[0]=B=>u.value=B),title:n.value,width:"40%","align-center":"","close-on-click-modal":!1,"before-close":c,"show-close":!1,draggable:""},{default:S(()=>[g("div",F,[g("div",N,[n.value==="导入作答信息进度"?(v(),q("div",U,"共"+m(f(i))+"个文件，正在导入第"+m(f(l)+1)+"个...",1)):h("",!0),d.value?(v(),p(r,{key:1,class:"progress-box","text-inside":!0,"stroke-width":15,striped:"","striped-flow":"",duration:"5",percentage:a.value,status:"success"},null,8,["percentage"])):h("",!0)])])]),_:1},8,["modelValue","title"])}}}),G=E(A,[["__scopeId","data-v-aa02c324"]]);export{G as default};
