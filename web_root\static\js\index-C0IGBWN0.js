import{p as L,g as N,a as H}from"./common-methods-BWkba4Bo.js";import{d as A}from"./downloadRequest-CdE2PBjt.js";import{c as P,a as q}from"./calculateTableHeight-BjE6OFD1.js";import{g as E}from"./roles-management-DlteFevS.js";import{aC as J,aQ as T,aR as U,d as $,l as o,P as B,n as M,ao as Q,r as p,o as I,c as G,e as h,b as i,h as f,f as K,T as X,_ as Y}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";J();const Z=g=>T.request("post",U("/v1/human_statistics/get_human_statistics_list"),{data:g}),w={class:"zf-first-box"},ee={class:"zf-second-box"},ae={class:"zf-flex-end"},te=$({name:"marking-workload",__name:"index",setup(g){const v=o(null),r=o(null),j=o({}),S=B({column:3,labelWidth:"70px",itemWidth:"250px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>L.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>s.value},{label:"所属角色",prop:"role_id",type:"select",defaultValue:"",placeholder:"请选择所属角色",clearable:!0,optionData:()=>y.value},{label:"用户名",prop:"user_name",type:"input",defaultValue:"",placeholder:"请输入用户名",clearable:!0}]}),l=o({field:[{prop:"user_name",label:"用户名",minWidth:"120px"},{prop:"name",label:"姓名",minWidth:"100px"},{prop:"id_card",label:"证件号码",minWidth:"120px"},{prop:"project_name",label:"所属资格",minWidth:"120px",formatter:e=>{let a=[];return e.official_mark_info&&e.official_mark_info.project_name&&(a.includes(e.official_mark_info.project_name)||a.push(e.official_mark_info.project_name)),e.repeat_mark_info&&e.repeat_mark_info.project_name&&(a.includes(e.repeat_mark_info.project_name)||a.push(e.repeat_mark_info.project_name)),e.mark_accept_info&&e.mark_accept_info.project_name&&(a.includes(e.mark_accept_info.project_name)||a.push(e.mark_accept_info.project_name)),a.join(",")}},{prop:"project_name",label:"所属科目",minWidth:"120px",formatter:e=>{let a=[];return e.official_mark_info&&e.official_mark_info.subject_name&&(a.includes(e.official_mark_info.subject_name)||a.push(e.official_mark_info.subject_name)),e.repeat_mark_info&&e.repeat_mark_info.subject_name&&(a.includes(e.repeat_mark_info.subject_name)||a.push(e.repeat_mark_info.subject_name)),e.mark_accept_info&&e.mark_accept_info.subject_name&&(a.includes(e.mark_accept_info.subject_name)||a.push(e.mark_accept_info.subject_name)),a.join(",")}},{prop:"role_name",label:"所属角色",minWidth:"120px",formatter:e=>{var t;if(e.role_info&&Array.isArray(e.role_info)){var a=e.role_info.map(n=>n.role_name)||[];return a.join(",")}return(t=e.role_info)==null?void 0:t.role_name}},{prop:"quality_count",label:"正评阅卷量(题)",minWidth:"120px",formatter:e=>{var a;return(a=e.official_mark_info)==null?void 0:a.count}},{prop:"re_evaluation_cnt",label:"复评量(卷)",minWidth:"120px",formatter:e=>{var a;return(a=e.repeat_mark_info)==null?void 0:a.count}},{prop:"subject_name",label:"验收量(卷)",formatter:e=>{var a;return(a=e.mark_accept_info)==null?void 0:a.count}}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),b=o([]),k=o([]),s=o([]);let x=null;o(""),M(()=>{_(),z(),P(x,v.value,l.value,!0,15),N().then(()=>{})}),Q(()=>{q(x)});const C=(e,a)=>{e.prop==="project_id"&&(a?(s.value=[],r.value.setCardData("subject_id",""),H(a).then(t=>{s.value=t||[]})):(s.value=[],r.value.setCardData("subject_id","")))},y=o([]),z=()=>{E({page_size:-1,system_user_type:2}).then(a=>{a.code&&a.code===200&&(a.data.data.forEach(t=>{t.label=t.role_name,t.value=t.role_id}),y.value=a.data.data)})},_=()=>{let{currentPage:e,pageSize:a}=l.value.pageOptions,t=JSON.parse(JSON.stringify(r.value.getAllCardData()));t.role_id=t.role_id?[t.role_id]:[],t.project_id=t.project_id?[t.project_id]:[],t.subject_id=t.subject_id?[t.subject_id]:[],t.current_page=e,t.page_size=a,Z(t).then(n=>{var d,m;n.code&&n.code===200&&(b.value=((d=n.data)==null?void 0:d.data)||n.data,b.value.forEach(c=>{c.id_card=c.id_card?c.id_card.replace(/(.{4}).*(.{4}$)/,"$1********$2"):""}),l.value.pageOptions.total=((m=n.data)==null?void 0:m.total)||n.data.length)})},D=e=>{k.value=e},R=e=>{l.value.pageOptions.pageSize=e,_()},V=e=>{l.value.pageOptions.currentPage=e,_()};function W(){s.value=[]}const u=o(!1),F=(e,a)=>{let t=JSON.parse(JSON.stringify(r.value.getAllCardData()));t.role_id=t.role_id?[t.role_id]:[],t.project_id=t.project_id?[t.project_id]:[],t.subject_id=t.subject_id?[t.subject_id]:[],t.user_id=k.value.map(n=>n.user_id)||[],u.value=!0,A("post","/v1/human_statistics/export_human_statistics_list","",{},t,"xlsx").then(n=>{u.value=!1}).catch(n=>{u.value=!1,X({type:"error",message:"接口异常,导出失败,原因:"+n.response.statusText})})};return(e,a)=>{const t=p("form-component"),n=p("el-card"),d=p("el-button"),m=p("Auth"),c=p("table-component");return I(),G("div",w,[h("div",ee,[i(n,null,{default:f(()=>[h("div",{ref_key:"formDivRef",ref:v},[i(t,{ref_key:"formRef",ref:r,modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=O=>j.value=O),"is-query-btn":!0,"form-options":S,onOnchangeFn:C,onQueryDataFn:_,onResetFields:W},null,8,["modelValue","form-options"])],512)]),_:1}),i(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:f(()=>[h("div",ae,[i(m,{value:"marking-workload/export"},{default:f(()=>[i(d,{type:"primary",onClick:a[1]||(a[1]=O=>F()),loading:u.value},{default:f(()=>[K("导出")]),_:1},8,["loading"])]),_:1})]),i(c,{minHeight:l.value.styleOptions.minHeight,"table-options":l.value,"table-data":b.value,onOnHandleSelectionChange:D,onOnHandleSizeChange:R,onOnHandleCurrentChange:V},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),ce=Y(te,[["__scopeId","data-v-e8b20073"]]);export{ce as default};
