@charset "UTF-8";.test-ques-card[data-v-c60422f2]{height:calc(100vh - 130px);width:100%}.progress-secret-box[data-v-c60422f2]{display:flex;justify-content:space-between}.progress-secret-box .marking-task-pro-box[data-v-c60422f2]{min-width:600px;width:calc(100% - 380px)}.progress-secret-box .secret-box[data-v-c60422f2]{margin:0 0 3px 3px;min-width:332px}.mark-all-box[data-v-c60422f2]{border-radius:5px;height:100%;width:100%}.manual-content-box[data-v-c60422f2]{display:flex;height:auto}.manual-content-box .manual-left[data-v-c60422f2]{height:auto;width:100%}.manual-content-box .history-right[data-v-c60422f2]{display:flex;height:auto;width:0}.ques-show-box[data-v-c60422f2]{height:calc(100vh - 236px);width:100%}.ques-show-box-op[data-v-c60422f2]{height:calc(100vh - 276px);width:100%}.op-mark-show-box[data-v-c60422f2]{align-items:center;background-color:#fff;display:flex;gap:10px;height:40px;justify-content:right;width:100%}.op-mark-show-box .op-mark-content[data-v-c60422f2]{align-items:center;display:flex;margin-right:54px}.op-mark-show-box .op-mark-content .op-score[data-v-c60422f2]{align-items:center;display:flex;justify-content:space-between;width:180px}.ques-detail-box[data-v-c60422f2]{width:calc(100% - 20px)}.manual-quality-box[data-v-c60422f2]{align-items:center;background-color:#fff;border:1px solid #9ca3af;border-radius:5px;display:flex;height:80px;justify-content:center;padding:3px 0}/*!*评分区域*!
.manual-right-box {
  display: flex;
  justify-content: flex-end;
  padding: 3px 0;

  //.marking-area {
  //  //border: 1px #9ca3af solid;
  //  border-radius: 5px;
  //  background-color: #FFFFFF;
  //}
}*//*!*快捷分数*!
.quick-mark-box {
  display: flex;
  justify-content: left;
  padding: 6px;
  flex-wrap: wrap;

  .quick-score {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 3px 3px 2px rgb(0 0 0 / 50%);
    border: 1px #E2E4E3 solid;
    border-radius: 5px;
    cursor: pointer;
    margin: 6px;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      background-color: var(--el-color-primary);
      color: #FFFFFF;
    }
  }
}*//*!*选中的快捷分数*!
.select-score {
  background-color: var(--el-color-primary);
  color: #FFFFFF;
}*//*!*快捷零分、满分*!
.quick-btn-box {
  display: flex;
  justify-content: space-between;
  padding: 12px;
}*//*!*自定义分数*!
.custom-score {
  display: flex;
  justify-content: space-between;
  padding: 12px;
}*/.drag-his-box[data-v-c60422f2]{cursor:col-resize;height:100%;width:3px}.vdr.active[data-v-c60422f2]:before{display:none!important}.drag-resize-box[data-v-c60422f2]{height:92vh;width:90vw}.history-box[data-v-c60422f2]{background:linear-gradient(-135deg,transparent 3px,#9ca3af 0);cursor:pointer;height:85px;letter-spacing:3px;padding:1px;width:23px;writing-mode:vertical-rl}.history-box div[data-v-c60422f2]{background:linear-gradient(-135deg,transparent 3px,#fff 0);height:100%;width:100%}.history-box .history-text[data-v-c60422f2]{padding-top:5px}
