var j=Object.defineProperty,A=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var W=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var k=(r,a,o)=>a in r?j(r,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[a]=o,v=(r,a)=>{for(var o in a||(a={}))F.call(a,o)&&k(r,o,a[o]);if(W)for(var o of W(a))L.call(a,o)&&k(r,o,a[o]);return r},w=(r,a)=>A(r,E(a));import q from"./add-group-AiffrH0Z.js";import Q from"./batch-set-member-BSVEYtgX.js";import{d as U,l as c,P as $,aN as x,n as I,ao as K,T as d,aV as z,r as g,o as J,c as X,e as y,b as i,h as _,f as V,_ as Y}from"./index-B63pSD2p.js";import{a as Z,d as ee,i as te}from"./group-member.vue_vue_type_script_setup_true_lang-CCOO0MKG.js";import{f as ae}from"./handleMethod-BIjqYEft.js";import{c as oe,a as ne}from"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./validate-Dc6ka3px.js";import"./fullscreen-exit-line-DVwpkItP.js";const le={class:"zf-first-box"},re={class:"zf-second-box"},ie={class:"zf-flex-end"},pe=U({name:"group",__name:"index",setup(r){const a=c(null),o=c(null),S=c(null),h=c({}),H=$({labelWidth:"68px",itemWidth:"200px",fields:[{label:"小组名称",prop:"group_name",type:"input",defaultValue:"",placeholder:"请输入小组名称",clearable:!0}]}),u=c({field:[{prop:"group_NO",label:"序号",minWidth:"55px",colType:""},{prop:"group_code",label:"小组编号",minWidth:"90px",colType:""},{prop:"group_name",label:"小组名称",minWidth:"220px",align:"left"},{prop:"group_level",label:"小组层级",minWidth:"90px",formatter:e=>ae(e.group_level,[{value:1,label:"科目级"},{value:3,label:"评卷小组级"},{value:2,label:"题组级"}])},{prop:"project_name111",label:"负责任务",minWidth:"120px"},{prop:"project_name",label:"资格名称",minWidth:"120px"},{prop:"subject_name",label:"科目名称",minWidth:"140px"},{prop:"is_used",label:"使用状态",minWidth:"86px",type:"switch",activeValue:1,inactiveValue:0,beforeChange:e=>P(e)},{prop:"u_name",label:"更新人",minWidth:"90px"},{prop:"updated_time",label:"更新时间",minWidth:"120px"},{prop:"",label:"操作",type:"template",minWidth:"180px",fixed:"right",templateGroup:[{title:()=>x("group/edit")?"编辑":"",clickBtn(e){b("edit",e)}},{title:()=>x("group/set-group")?"设置组员":"",clickBtn(e){b("edit",w(v({},e),{tab:"second"}))}},{title:()=>x("group/del")?"删除":"",clickBtn(e){M(e)}}]}],styleOptions:{isShowSort:!1,isShowSelection:!0,rowKey:"group_id"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),B=c([]),s=c([]);let C=null;I(()=>{oe(C,a.value,u.value),m()}),K(()=>{ne(C)});const m=()=>{let{currentPage:e,pageSize:n}=u.value.pageOptions,t=v({current_page:e,page_size:n},h.value);Z(t).then(l=>{l.code&&l.code===200?(l.data.data.forEach((f,p)=>{f.group_NO=p+1}),B.value=l.data.data,u.value.pageOptions.total=l.data.total):d.error(l.msg)})},T=e=>{u.value.pageOptions.pageSize=e,m()},D=e=>{u.value.pageOptions.currentPage=e,m()},R=e=>{s.value=e},b=(e="add",n)=>{o.value.openDialog(e,n)},G=()=>{if(!s.value.length)d.warning("至少选择一条数据！");else{const e=s.value.every(t=>t.group_level===s.value[0].group_level);s.value.every(t=>t.subject_id===s.value[0].subject_id)&&e?S.value.openDialog(s.value):d.warning("请选择相同科目下相同层级的小组！")}},M=e=>{z.confirm("确定删除该小组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let n={group_id:e.group_id};ee(n).then(t=>{t.code&&t.code===200?(m(),d.success(t.msg)):d.error(t.msg)})}).catch(()=>{})},P=e=>new Promise((n,t)=>{let l="";e.is_used?l="禁用":l="启用",z.confirm(`确定${l}该小组吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let f={group_id:e.group_id,is_used:e.is_used===0?1:0};te(f).then(p=>{p.code&&p.code===200?(d.success(p.msg),n(!0)):(d.error(p.msg),t(!1))}).catch(()=>{t(!1)})}).catch(()=>{t(!1)})});return(e,n)=>{const t=g("form-component"),l=g("el-card"),f=g("el-button"),p=g("Auth"),N=g("table-component");return J(),X("div",le,[y("div",re,[i(l,null,{default:_(()=>[y("div",{ref_key:"formDivRef",ref:a},[i(t,{ref:"formRef",modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=O=>h.value=O),"form-options":H,"is-query-btn":!0,onQueryDataFn:m},null,8,["modelValue","form-options"])],512)]),_:1}),i(l,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:_(()=>[y("div",ie,[i(p,{value:"group/batch"},{default:_(()=>[i(f,{type:"primary",onClick:G},{default:_(()=>[V("批量设置组员")]),_:1})]),_:1}),i(p,{value:"group/add"},{default:_(()=>[i(f,{type:"primary",onClick:n[1]||(n[1]=O=>b("add"))},{default:_(()=>[V("创建")]),_:1})]),_:1})]),i(N,{minHeight:u.value.styleOptions.minHeight,"table-options":u.value,"table-data":B.value,treeProps:{checkStrictly:!0},onOnHandleSizeChange:T,onOnHandleCurrentChange:D,onOnHandleSelectionChange:R},null,8,["minHeight","table-options","table-data"])]),_:1})]),i(q,{ref_key:"addGroupRef",ref:o,onQueryData:m},null,512),i(Q,{ref_key:"batchSetMemberRef",ref:S},null,512)])}}}),xe=Y(pe,[["__scopeId","data-v-32795f2c"]]);export{xe as default};
