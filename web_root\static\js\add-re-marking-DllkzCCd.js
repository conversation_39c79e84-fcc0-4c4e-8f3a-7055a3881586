import{S as ne}from"./index-BCxZUZMh.js";import{p as se,g as re,a as ie}from"./common-methods-BWkba4Bo.js";import{a as ue,c as ce}from"./re-marking-BVznCWni.js";import{d as de,l as o,P as pe,ao as me,r as i,o as d,g as k,h as n,c as C,b as t,f as p,y as z,e as u,F as P,p as fe,u as B,d5 as _e,t as ve,T as x,_ as ge}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const he={class:"re-form-box"},be={class:"top"},Ve={class:"form-box"},ye={class:"flex"},xe={style:{width:"76px","text-align":"right"}},ke={class:"bottom"},Ce={class:"re-table-box"},Fe={key:0,class:"footer-btn"},we=de({__name:"add-re-marking",emits:["queryListFn"],setup(Oe,{expose:E,emit:N}){const T=N,H=o("整卷复评"),v=o(!1),s=o({type:1,scoreForm:[{minVal:null,maxVal:null}]});o([]);const W=pe({labelWidth:"88px",itemWidth:"230px",inline:!0,rules:{project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>se.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>F.value}]}),F=o([]),m=o({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"score",label:"考生得分",minWidth:"160px"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}}),c=o([]),g=o("add"),h=o(!1);o({});const L=o(),b=o(),M=e=>{v.value=!0,g.value=e,re(),g.value==="result"||(h.value=!1,H.value="整卷复评")};let V=null;const A=()=>{V=new ResizeObserver(e=>{e.forEach(l=>{b.value&&(m.value.styleOptions.minHeight=L.value.clientHeight-b.value.clientHeight-20+"px")})}),b.value&&V.observe(b.value)},w=()=>{h.value=!1,v.value=!1,c.value=[],s.value={type:1,scoreForm:[{minVal:null,maxVal:null}]},f.value=!1,y.value=!1},y=o(!1),U=o(),$=()=>{U.value.formValidate().then(()=>{let e=s.value.scoreForm;e=e.filter(l=>l.minVal!==null&&l.minVal!==void 0&&l.minVal!==""||l.maxVal!==null&&l.maxVal!==void 0&&l.maxVal!==""),y.value=!0,h.value||(h.value=!0),A(),G(e)}).catch(()=>{x.warning("请按要求填写！")})},G=e=>{c.value=[],O.value=[];let l={ids_list:[s.value.project_id,s.value.subject_id],score_list:e&&e.length>0?e.map(a=>[a.minVal,a.maxVal]):[]};ue(l).then(a=>{a.code&&a.code==200?(a.data.stu_ids.forEach(S=>{c.value=c.value.concat(S.exam_stus)}),O.value=a.data.stu_ids):x.error(a.msg),y.value=!1}).catch(a=>{y.value=!1})},I=()=>{s.value.scoreForm.push({minVal:null,maxVal:null})},J=e=>{if(s.value.scoreForm.length==1)return x.warning("至少要保留一条数据"),!1;s.value.scoreForm.splice(e,1)},K=e=>{m.value.pageOptions.currentPage=1,m.value.pageOptions.pageSize=e},Q=e=>{m.value.pageOptions.currentPage=e},X=(e,l)=>{e.prop==="project_id"&&(F.value=[],s.value.subject_id&&(s.value.subject_id=null),l&&ie(l).then(a=>{F.value=a||[]}))},f=o(!1),O=o([]),Y=()=>{if(c.value.length==0){x.warning("暂无数据，无法保存！");return}f.value=!0;let e={stu_list:O.value};ce(e).then(l=>{l.code&&l.code==200?(v.value=!1,f.value=!1,T("queryListFn"),w()):(f.value=!1,x.error(l.msg))}).catch(l=>{f.value=!1})};return me(()=>{V&&(V.disconnect(),V=null)}),E({openDialog:M}),(e,l)=>{const a=i("el-text"),S=i("form-component"),D=i("el-input-number"),Z=i("el-form-item"),ee=i("el-form"),le=i("Remove"),q=i("el-icon"),ae=i("el-scrollbar"),j=i("el-button"),te=i("table-component"),oe=i("el-dialog");return d(),k(oe,{modelValue:v.value,"onUpdate:modelValue":l[1]||(l[1]=r=>v.value=r),title:H.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",width:"80%","close-on-click-modal":!1,"before-close":w,"destroy-on-close":""},{footer:n(()=>[g.value==="add"?(d(),C("div",Fe,[t(j,{onClick:w},{default:n(()=>[p("取消")]),_:1}),t(j,{type:"primary",onClick:Y,loading:f.value},{default:n(()=>[p("保存")]),_:1},8,["loading"])])):z("",!0)]),default:n(()=>[u("div",{class:"flex h-full",ref_key:"mainRef",ref:L,style:{height:"60vh"}},[u("div",he,[u("div",be,[t(a,{size:"large"},{default:n(()=>[p("复评条件")]),_:1})]),g.value==="add"?(d(),C(P,{key:0},[u("div",Ve,[t(ae,{always:""},{default:n(()=>[t(S,{ref_key:"formRef",ref:U,modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=r=>s.value=r),"form-options":W,"is-query-btn":!1,onOnchangeFn:X},null,8,["modelValue","form-options"]),(d(!0),C(P,null,fe(s.value.scoreForm,(r,R)=>(d(),C("div",ye,[t(ee,{modelValue:s.value.scoreForm[R],"onUpdate:modelValue":_=>s.value.scoreForm[R]=_,"label-width":`88px\r
                `},{default:n(()=>[t(Z,{label:"总得分"},{default:n(()=>[t(D,{modelValue:r.minVal,"onUpdate:modelValue":_=>r.minVal=_,style:{width:"95px","margin-right":"2px"},placeholder:"最小值",controls:!1,min:0},null,8,["modelValue","onUpdate:modelValue"]),t(a,null,{default:n(()=>[p("-")]),_:1}),t(D,{modelValue:r.maxVal,"onUpdate:modelValue":_=>r.maxVal=_,style:{width:"95px","margin-left":"2px"},placeholder:"最大值",controls:!1,min:0},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["modelValue","onUpdate:modelValue"]),t(q,{size:"20",style:{margin:"5px 0 0 12px",cursor:"pointer"},onClick:_=>J(R)},{default:n(()=>[t(le)]),_:2},1032,["onClick"])]))),256)),u("div",xe,[t(a,{style:{cursor:"pointer"},type:"primary",onClick:I},{default:n(()=>[t(q,null,{default:n(()=>[t(B(_e))]),_:1}),p("添加")]),_:1})])]),_:1})]),u("div",ke,[t(j,{type:"primary",onClick:$,loading:y.value},{default:n(()=>[p("复评查询")]),_:1},8,["loading"])])],64)):z("",!0)]),u("div",Ce,[u("div",{class:"top",ref_key:"titleRef",ref:b},[u("div",null,[t(a,{size:"large"},{default:n(()=>[p("查询结果")]),_:1})]),g.value!=="result"?(d(),k(a,{key:0},{default:n(()=>{var r;return[p("已查数据："+ve((r=c.value)!=null&&r.length?c.value.length:"-")+"条",1)]}),_:1})):z("",!0)],512),h.value?(d(),k(te,{key:1,minHeight:m.value.styleOptions.minHeight,"table-options":m.value,"table-data":c.value,onOnHandleSizeChange:K,onOnHandleCurrentChange:Q},null,8,["minHeight","table-options","table-data"])):(d(),k(B(ne),{key:0,title:"复评查询结果",stepList:["选择左侧复评条件","点击【复评查询】按钮，查询结果","保存查询结果，自动生成新一轮评阅任务"]}))])],512)]),_:1},8,["modelValue","title"])}}}),Le=ge(we,[["__scopeId","data-v-853390fd"]]);export{Le as default};
