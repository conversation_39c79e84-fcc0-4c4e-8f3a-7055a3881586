var X=Object.defineProperty;var R=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable;var k=(l,s,t)=>s in l?X(l,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[s]=t,o=(l,s)=>{for(var t in s||(s={}))$.call(s,t)&&k(l,t,s[t]);if(R)for(var t of R(s))A.call(s,t)&&k(l,t,s[t]);return l};import{u as B}from"./hooks-Cf_Naqnw.js";import{d as L,l as d,al as N,ah as I,aZ as _,B as V,b as n,X as j,w as q,j as T,a_ as z,ai as D,C as f,v as W,ax as Z}from"./index-B63pSD2p.js";const p={options:{type:Array,default:()=>[]},modelValue:{type:void 0,require:!1,default:"0"},block:{type:Boolean,default:!1},size:{type:String}},x=L({name:"ReSegmented",props:p,emits:["change","update:modelValue"],setup(l,{emit:s}){const t=d(0),g=d(0),{isDark:i}=N(),b=d(!1),c=d(-1),m=d(""),v=W(),r=I(l.modelValue)?_(l,"modelValue"):d(0);function M({option:e,index:a},u){e.disabled||(u.preventDefault(),I(l.modelValue)?s("update:modelValue",a):r.value=a,m.value="",s("change",{index:a,option:e}))}function w({option:e,index:a},u){u.preventDefault(),c.value=a,e.disabled||r.value===a?m.value="":m.value=i.value?"#1f1f1f":"rgba(0, 0, 0, 0.06)"}function C(e,a){a.preventDefault(),c.value=-1}function h(e=r.value){f(()=>{var u;const a=(u=v==null?void 0:v.proxy)==null?void 0:u.$refs[`labelRef${e}`];a&&(t.value=a.clientWidth,g.value=a.offsetLeft,b.value=!0)})}function y(){j(".pure-segmented",()=>{f(()=>{h(r.value)})})}l.block&&y(),V(()=>r.value,e=>{f(()=>{h(e)})},{immediate:!0}),V(()=>l.size,y);const S=()=>l.options.map((e,a)=>n("label",{ref:`labelRef${a}`,class:["pure-segmented-item",(e==null?void 0:e.disabled)&&"pure-segmented-item-disabled"],style:{background:c.value===a?m.value:"",color:!e.disabled&&(r.value===a||c.value===a)?i.value?"rgba(255, 255, 255, 0.85)":"rgba(0,0,0,.88)":""},onMouseenter:u=>w({option:e,index:a},u),onMouseleave:u=>C({option:e,index:a},u),onClick:u=>M({option:e,index:a},u)},[n("input",{type:"radio",name:"segmented"},null),q(n("div",{class:"pure-segmented-item-label"},[e.icon&&!z(e.label)?n("span",{class:"pure-segmented-item-icon",style:{marginRight:e.label?"6px":0}},[D(B(e.icon,o({},e==null?void 0:e.iconAttrs)))]):null,e.label?z(e.label)?D(e.label):n("span",null,[e.label]):null]),[[T("tippy"),{content:e==null?void 0:e.tip,zIndex:41e3}]])]));return()=>n("div",{class:{"pure-segmented":!0,"pure-segmented-block":l.block,"pure-segmented--large":l.size==="large","pure-segmented--small":l.size==="small"}},[n("div",{class:"pure-segmented-group"},[n("div",{class:"pure-segmented-item-selected",style:{width:`${t.value}px`,transform:`translateX(${g.value}px)`,display:b.value?"block":"none"}},null),S()])])}}),H=Z(x);export{H as R};
