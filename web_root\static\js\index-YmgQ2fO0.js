import{_ as N}from"./import-test-paper.vue_vue_type_script_setup_true_lang-BguCYaIE.js";import{d as q,l,i as A,P as M,aN as D,T as _,n as I,ao as J,aV as Q,r as u,o as U,c as G,e as f,b as r,h as c,f as j,u as O,F as $,_ as K}from"./index-B63pSD2p.js";import{g as w,e as X}from"./test-paper-management-DjV_45YZ.js";import{p as F,g as Y,a as Z}from"./common-methods-BWkba4Bo.js";import{d as ee}from"./downloadRequest-CdE2PBjt.js";import{c as te,a as ae}from"./calculateTableHeight-BjE6OFD1.js";import"./scoring-rules-BR2vQ7G3.js";import"./uploadRequest-IEYs8WTn.js";const oe={class:"zf-first-box"},ne={class:"zf-second-box"},le={class:"upload-btn-box"},re=q({name:"test-paper",__name:"index",setup(pe){const g=l(),V=A(),n=l(null),h=l(null),d=l([]),m=l([]),b=l({}),B=M({column:3,labelWidth:"68px",itemWidth:"250px",rules:{paper_name:[{trigger:["blur","change"],validator:(e,t,a)=>{if(t&&t.length>100)return a(new Error("试卷名称长度不能超过100！"));a()}}],c_name:[{trigger:["blur","change"],validator:(e,t,a)=>{if(t&&t.length>50)return a(new Error("创建人名称长度不能超过50！"));a()}}]},fields:[{label:"资格",prop:"project_id",type:"select",placeholder:"请选择资格",defaultValue:"",clearable:!0,optionData:()=>F.value},{label:"科目",prop:"subject_id",type:"select",placeholder:"请选择科目",clearable:!0,defaultValue:"",optionData:()=>d.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷名称",clearable:!0,optionData:()=>m.value},{label:"创建人",prop:"c_name",type:"input",defaultValue:"",placeholder:"请输入创建人",clearable:!0}]}),p=l({field:[{prop:"project_name",label:"所属资格",minWidth:"180px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"paper_name",label:"试卷名称",minWidth:"200px"},{prop:"total_score",label:"试卷总分",minWidth:"90px"},{prop:"mark_rule_name",label:"评分规则",minWidth:"160px"},{prop:"remark",label:"备注",minWidth:"200px"},{prop:"c_name",label:"创建人",minWidth:"120px"},{prop:"created_time",label:"创建时间",minWidth:"160px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"150px"},{prop:"",label:"操作",type:"template",minWidth:"160px",fixed:"right",templateGroup:[{title:()=>D("test-paper/edit")?"编辑":"",clickBtn(e){g.value.openDialog("01",e)}},{title:()=>"预览",clickBtn(e){V.push({path:"/test-paper-management/paper-detail/index",query:e})}},{title:()=>D("test-paper/delete")?"删除":"",clickBtn(e){e.lock_state===2?_.warning("该试卷已锁定！无法删除！"):P(e.paper_id)}}]}],styleOptions:{isShowSort:!0,minHeight:window.innerHeight-330+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});l([{format:"word",formatValue:"2"},{format:"excel",formatValue:"1"}]);let v=l([]),x=null;I(()=>{te(x,h.value,p.value),Y(),s()}),J(()=>{ae(x)});const W=(e,t)=>{e.prop==="project_id"?(d.value=[],m.value=[],n.value.getCardData("subject_id")&&n.value.setCardData("subject_id",null),n.value.getCardData("paper_code")&&n.value.setCardData("paper_code",null),t&&Z(t).then(a=>{d.value=a||[]})):e.prop==="subject_id"?(m.value=[],n.value.getCardData("paper_code")&&n.value.setCardData("paper_code",null),t&&y()):e.prop==="search_time"&&n.value.setCardData("search_time",t)},y=()=>{const{project_id:e,subject_id:t}=n.value.getAllCardData();w({project_id:e,subject_id:t,page_size:-1}).then(o=>{o.code&&o.code===200&&(o.data.data.forEach(i=>{i.label=i.paper_name,i.value=i.paper_code}),m.value=o.data.data)})},s=()=>{let e=JSON.parse(JSON.stringify(n.value.getAllCardData())),{currentPage:t,pageSize:a}=p.value.pageOptions;e.current_page=t,e.page_size=a,w(e).then(o=>{o.code&&o.code===200&&(v.value=o.data.data,p.value.pageOptions.total=o.data.total)})},k=()=>{ee("get","/v1/common/get_template_file","试卷模板",{temp_type:"1",temp_format:1},{},"xlsx").then(t=>{t&&t.data&&t.data.type=="application/json"&&_({message:"暂无模板信息！",type:"warning"})})},z=()=>{g.value.openDialog("02")},P=e=>{Q.confirm("确定删除该试卷吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{X({paper_id:e}).then(a=>{a.code&&a.code===200?(_.success(a.msg),s()):_.warning(a.msg)})}).catch(()=>{})},S=e=>{p.value.pageOptions.pageSize=e,s()},R=e=>{p.value.pageOptions.currentPage=e,s()},T=()=>{s(),y()};function H(){d.value=[]}return(e,t)=>{const a=u("form-component"),o=u("el-card"),i=u("el-button"),C=u("Auth"),L=u("table-component");return U(),G($,null,[f("div",oe,[f("div",ne,[r(o,null,{default:c(()=>[f("div",{ref_key:"formDivRef",ref:h},[r(a,{ref_key:"formRef",ref:n,modelValue:b.value,"onUpdate:modelValue":t[0]||(t[0]=E=>b.value=E),"is-query-btn":!0,"form-options":B,onQueryDataFn:s,onOnchangeFn:W,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:1}),r(o,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:c(()=>[f("div",le,[r(C,{value:"test-paper/download"},{default:c(()=>[r(i,{type:"primary",onClick:k},{default:c(()=>[j("下载模板")]),_:1})]),_:1}),r(C,{value:"test-paper/import"},{default:c(()=>[r(i,{type:"primary",onClick:z},{default:c(()=>[j("导入试卷")]),_:1})]),_:1})]),r(L,{minHeight:p.value.styleOptions.minHeight,"table-options":p.value,"table-data":O(v),onOnHandleSizeChange:S,onOnHandleCurrentChange:R},null,8,["minHeight","table-options","table-data"])]),_:1})])]),r(N,{ref_key:"importTestPaperRef",ref:g,projectList:O(F),onQueryData:T},null,8,["projectList"])],64)}}}),ge=K(re,[["__scopeId","data-v-8dbd85db"]]);export{ge as default};
