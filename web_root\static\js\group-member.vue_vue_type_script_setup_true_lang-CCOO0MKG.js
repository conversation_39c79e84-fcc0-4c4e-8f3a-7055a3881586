var nl=Object.defineProperty,ol=Object.defineProperties;var sl=Object.getOwnPropertyDescriptors;var Pe=Object.getOwnPropertySymbols;var ul=Object.prototype.hasOwnProperty,dl=Object.prototype.propertyIsEnumerable;var Ne=(e,l,t)=>l in e?nl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[l]=t,G=(e,l)=>{for(var t in l||(l={}))ul.call(l,t)&&Ne(e,t,l[t]);if(Pe)for(var t of Pe(l))dl.call(l,t)&&Ne(e,t,l[t]);return e},Ce=(e,l)=>ol(e,sl(l));import{_ as il,l as p,B as ae,C as me,aP as C,d4 as qe,n as cl,S as hl,r as I,o as _,c as H,e as V,t as W,b as P,h as b,F as re,p as ne,g as x,i6 as Le,f as ue,y as ce,ae as Ie,q as Ve,dd as pl,ac as fl,ad as gl,aQ as K,aR as z,d as _l,P as vl,u as Oe,T as Se}from"./index-B63pSD2p.js";function Te(e,l,t=[]){if(e.id===l)return t.push(e),t;if(e.children)for(const r of e.children){const v=Te(r,l,t.concat(e));if(v.length>0)return v}return[]}function Fe(e){if(!e.children)return[e];const l=[e];for(const t of e.children)l.push(...Fe(t));return l}function de(e=[],l=[]){const t=[];for(const r of e)for(const v of l){const g=Te(r,v);if(g.length>0){const D=g,O=Fe(g[g.length-1]);t.push(...D,...O)}}return t}function Me(e=[],l=[]){const t=[];for(const r of e)for(const v of l){const g=Te(r,v,t);g.length>0&&g.forEach(O=>{t.find(U=>U.id==O.id)||t.push(O)})}return t}function Be(e=[],l=[]){return l.push(e.id),e.children&&e.children.length>0&&e.children.forEach(t=>Be(t,l)),l}function ye(e=[]){const l=[];return e.forEach(t=>Be(t,l)),l}function ie(e){const l=new Map,t=[];for(const r of e)l.has(r.id)||(l.set(r.id,r),t.push(r));return t}function oe(e){if(typeof e=="object"&&e!==null&&(e.children=void 0,e.children&&Array.isArray(e.children)))for(const l of e.children)oe(l)}function we(e){const l=new Map(e.map(r=>[r.id,r])),t=[];return e.forEach(r=>{if(r.parentId){const v=l.get(r.parentId);v&&!v.children&&(v.children=[]),v&&v.children.push(r)}else t.push(r)}),t}function he(e=[],l){let t=0;for(const r of e)!r.children||r.children&&!r.children.length?r.hasOwnProperty(l)?r[l]==1?t++:t=t+0:t++:t+=he(r.children,l);return t}function We(e=[],l){let t=0;for(const r of e)(!r.children||r.children&&!r.children.length)&&(r.hasOwnProperty(l)?r[l]==1&&t++:t++);return t}const He=(e=[])=>e&&(e==null?void 0:e.map(l=>{var t;return l!=null&&l.children&&((t=l==null?void 0:l.children)==null?void 0:t.length)==0&&l.isSubject==0?l.isEnable=0:l.isEnable=l.isEnable===0?0:1,Ce(G({},l),{children:He(l.children)})})),Ue=(e=[])=>{if(e.length>0)return e.map((l,t)=>{if(l.disabled)return G({},l);if(l.children&&l.children.length>0){let r=[];l.children.forEach((g,D)=>{r.push(Ue([G({},l.children[D])])[0])}),r.every(g=>g.isEnable==0)?l.isEnable=0:l.isEnable=1,l.children=r}return G({},l)})},_e=e=>Ue(He(e)),$e=(e=[])=>e&&(e==null?void 0:e.map(l=>{var t,r;return(l!=null&&l.children&&((t=l==null?void 0:l.children)==null?void 0:t.length)==0||!(l!=null&&l.children))&&l.isSubject==0&&l.isEnable==1?l.hasRemove=1:l.hasRemove=0,Ce(G({},l),{children:l!=null&&l.children&&((r=l==null?void 0:l.children)!=null&&r.length)?$e(l.children):[]})})),je=(e=[])=>{if(e.length>0)return e.map((l,t)=>{if(l.hasRemove==1)return G({},l);if(l.children&&l.children.length>0){let r=[];l.children.forEach((g,D)=>{r.push(je([G({},l.children[D])])[0])}),r.every(g=>g.hasRemove==1)?l.hasRemove=1:l.hasRemove=0,l.children=r}return G({},l)})},Ge=(e=[])=>{for(let l=0;l<e.length;l++)e[l].hasRemove==1?e.splice(l,1):e[l].children&&Ge(e[l].children);return e},Ae=(e=[])=>Ge(je($e(e))),Ke=e=>(fl("data-v-fb15c69f"),e=e(),gl(),e),bl={style:{position:"absolute",top:"12px",right:"30px"}},ml={class:"mt-2 mb-4"},yl={key:0},wl={class:"w-button-container"},El={class:"w-transfer-panel w-target"},kl={style:{height:"40px","line-height":"40px","margin-bottom":"22px",position:"relative"},class:"w-border"},Cl={style:{position:"absolute",bottom:"-4px",right:"10px"}},Sl={class:"w-search-wrapper"},Tl={class:"mt-2 mb-4"},xl=Ke(()=>V("div",{class:"w-drag-handle"},[V("svg",{"data-v-86edbcbd":"",width:"16",height:"16",fill:"currentColor","aria-hidden":"true","data-icon":"holder",viewBox:"64 64 896 896"},[V("path",{d:"M300 276.5a56 56 0 1 0 56-97 56 56 0 0 0-56 97m0 284a56 56 0 1 0 56-97 56 56 0 0 0-56 97M640 228a56 56 0 1 0 112 0 56 56 0 0 0-112 0m0 284a56 56 0 1 0 112 0 56 56 0 0 0-112 0M300 844.5a56 56 0 1 0 56-97 56 56 0 0 0-56 97M640 796a56 56 0 1 0 112 0 56 56 0 0 0-112 0"})])],-1)),Dl=Ke(()=>V("span",{style:{color:"red"}},"*",-1)),Rl={key:0},Pl={__name:"index",props:{rightTitle:{type:String},listTabs:{type:[Object]},listButtons:{type:[Object]},isMulSelectRow:{type:Boolean,default:!1},leftColumns:{type:[Object]},rightColumns:{type:[Object]},rightTableList:{type:[Object]},leftWidth:{type:String},draggable:{type:Boolean,default:!1},showMsqNo:{type:Boolean,default:!1},showLeftMsqNo:{type:Boolean,default:!0},keyWord:{type:String,default:"isSubject"},defaultHeight:{type:String}},emits:["getRightTableList","changeSwitch","handleSetPermit","handleCurrentChange","updateIsOpenFn","updateIsEnableFn","search","pageChange","handleEditParams","handleDeleteParams","handleSetPermi","resetPassword","handleAddParams","handleSetProperty","handleSetStandard","handleSetTypeCount","handleSetEditor","handleCopyParams","handleDetail"],setup(e,{expose:l,emit:t}){const r=e,v=p({text:1,input:2,link:3,img:4,switch:5,select:6,multipleSelect:7,cascader:8,date:9,dateRange:10,tag:11,icon:12,custom:99}),g=t,D=p(""),O=p(""),U=p(""),X=p(""),Q=p([]),Y=p([]),Z=p({switchStatus:!1}),se=()=>(Z.switchStatus=!0,Z.switchStatus),ve=(a,n,s)=>{g("changeSwitch",a,n,s)},be=a=>{g("getRightTableList",f.value)},S=p(1);p((a=>Array.from({length:a},(n,s)=>({id:s+1,name:`Item ${s+1}`,checked:!1})))(10));const f=p([]),te=p([]),xe=p(r.listTabs),d=p(xe),m=p([]),w=p([]),R=p([]),y=p([]),A=p(1),F=p(null),E=p([]),L=p([]),T=p(0),$=p(0);ae(()=>r.listTabs,a=>{me(()=>{var s;d.value=C.cloneDeep(a),m.value=C.cloneDeep(a);let n=((s=a.find(o=>o.tab==S.value))==null?void 0:s.data)||[];L.value[S.value]=he(n,r.keyWord)})},{deep:!0,immediate:!0}),ae(()=>d.value,a=>{var s;let n=((s=a.find(o=>o.tab==S.value))==null?void 0:s.data)||[];L.value[S.value]=he(n,r.keyWord)},{deep:!0,immediate:!0}),ae(()=>r.rightTableList,a=>{a&&me(()=>{f.value=_e(C.cloneDeep(a)),te.value=C.cloneDeep(a),$.value=he(f.value,r.keyWord)})},{deep:!0,immediate:!0}),ae(()=>f.value,a=>{var n;$.value=he(a,r.keyWord),a!=null&&a.length&&((n=r.rightColumns)==null||n.forEach(s=>{s.uiType==6&&s.hasdefaultValue&&(a==null||a.forEach(o=>{var k,u;!o[s.prop]&&o[s.prop]!==void 0&&(o[s.prop]=(u=(k=s.data)==null?void 0:k[0])==null?void 0:u.value)}))}))},{deep:!0,immediate:!0});const ze=p(!1),ke=p(null);ae(()=>y.value,a=>{a.length&&(ze.value=a.some(n=>n&&n.length))},{deep:!0,immediate:!0}),ae(()=>U.value,a=>{a||pe()}),ae(()=>X.value,a=>{a||fe()});const pe=()=>{d.value=C.cloneDeep(m.value),U.value?d.value.forEach(a=>{a.tab==S.value&&(a.data=a.data.filter(n=>n[`${D.value}`].includes(U.value)))}):d.value=C.cloneDeep(m.value)},fe=()=>{f.value=C.cloneDeep(te.value),X.value?f.value=f.value.filter(a=>a[`${O.value}`].includes(X.value)):f.value=C.cloneDeep(te.value)},Qe=(a,n)=>{var o;S.value=a.props.name;let s=((o=d.value.find(k=>k.tab==S.value))==null?void 0:o.data)||[];L.value[S.value]=he(s,r.keyWord)},De=(a,n,s)=>{var o;(o=n==null?void 0:n.children)!=null&&o.length||g(a,n.apiPath,s,n)},Je=()=>{var s;let a=[];return(s=d.value)==null||s.forEach(o=>{o.tab==S.value&&(a=o.data)}),(a==null?void 0:a.every(o=>o.hasOwnProperty("isEnable")&&!o.isEnable))?"w-left-checked-table":""},Xe=()=>{var n;return((n=f.value)==null?void 0:n.every(s=>s.hasOwnProperty("isEnable")&&!s.isEnable))?"w-right-checked-table":""},Re=a=>a.hasOwnProperty("isEnable")?a.isEnable==1:!0,Ye=a=>{let n=[];d.value.forEach(u=>{u.tab==S.value&&(n=u.data)}),a.forEach(u=>{u.hasOwnProperty("isEnable")&&u.isEnable==0&&me(()=>{F.value&&F.value[0].toggleRowSelection(u,!1)})});let s=[];s=a.filter(u=>u.hasOwnProperty("isEnable")?u.isEnable==1:u),E.value[S.value]=We(s,r.keyWord),Q.value=s.map(u=>u.id);let o=de(n,[...new Set(Q.value)]);y.value[S.value]=o.filter(u=>u.hasOwnProperty("isEnable")?u.isEnable==1:u),y.value&&y.value.some(u=>u&&u.length>0)||(y.value=[])},Ze=a=>{a.forEach(o=>{o.hasOwnProperty("isEnable")&&o.isEnable==0&&me(()=>{ke.value&&ke.value.toggleRowSelection(o,!1)})});let n=[];n=a.filter(o=>o.hasOwnProperty("isEnable")?o.isEnable==1:o),T.value=We(n,r.keyWord),Y.value=n.map(o=>o.id);let s=de(C.cloneDeep(f.value),[...new Set(Y.value)]);R.value=s.filter(o=>o.hasOwnProperty("isEnable")?o.isEnable==1:o)},el=()=>{var u,j;E.value=[],U.value="",pe(),y.value.forEach(h=>{w.value.push(...h)});let a=ye(f.value),n=de(f.value,[...new Set(a)]),s=[...C.cloneDeep(n),...C.cloneDeep(w.value)];const o=ie(s);o.forEach(oe),f.value=we(o),f.value=_e(f.value);let k=C.cloneDeep(d.value);k.forEach(h=>{let N=ye(h.data),q=de(h.data,[...new Set(N)]);h.data=ie(q),h.data.forEach(oe)}),Q.value.forEach(h=>{k.forEach((N,q)=>{const B=N.data.findIndex(ee=>ee.id===h);B!==-1&&N.data.splice(B,1)})}),k.forEach(h=>{m.value.forEach(N=>{if(h.tab==N.tab){let q=h.data.map(ee=>ee.id),B=Me(N.data,[...new Set(q)]);h.data=ie(B),h.data.forEach(oe),h.data=we(h.data),h.data=Ae(h.data),h.data=_e(h.data)}})}),d.value=k,m.value=C.cloneDeep(d.value),te.value=C.cloneDeep(f.value),y.value=[],w.value=[],(u=f.value)!=null&&u.length&&((j=r.rightColumns)==null||j.forEach(h=>{var N;h.uiType==6&&h.hasdefaultValue&&((N=f.value)==null||N.forEach(q=>{var B,ee;q[h.prop]||(q[h.prop]=(ee=(B=h.data)==null?void 0:B[0])==null?void 0:ee.value)}))})),g("getRightTableList",f.value)},ll=()=>{T.value=0,X.value="",fe(),d.value.forEach(u=>{let j=ye(u.data),h=de(u.data,[...new Set(j)]),N=[...C.cloneDeep(h)];R.value.forEach(q=>{u.tab==q.tab&&N.push(C.cloneDeep(q))}),u.data=ie(N),u.data.forEach(oe),u.data=we(u.data),u.data.forEach((q,B)=>{q.msqNo=B+1}),u.data=_e(u.data)});let a=C.cloneDeep(f.value)||[],n=ye(a),s=de(a,[...new Set(n)]);a=ie(s),a.forEach(oe),Y.value.forEach(u=>{const j=a.findIndex(h=>h.id===u);j!==-1&&a.splice(j,1)});let o=a.map(u=>u.id),k=Me(te.value,[...new Set(o)]);a=ie(k),a.forEach(oe),a=we(a),a=Ae(a),f.value=_e(a),m.value=C.cloneDeep(d.value),te.value=C.cloneDeep(f.value),R.value=[],g("getRightTableList",f.value)};return qe(()=>{var a,n;D.value=r.leftColumns.length&&((a=r.leftColumns[0])==null?void 0:a.prop),O.value=r.rightColumns.length&&((n=r.rightColumns[0])==null?void 0:n.prop)}),qe(()=>{d.value.forEach(a=>{var n;(n=a==null?void 0:a.data)==null||n.forEach((s,o)=>{s.msqNo=o+1,s.checked=w.value.some(k=>k.id===s.id)})}),A.value=A.value+1}),l({getRightData:()=>f.value}),cl(()=>{const a=document.querySelector(".w-target .el-table__body-wrapper tbody");a&&hl.create(a,{animation:300,handle:".w-drag-handle",onEnd:({newIndex:n,oldIndex:s})=>{const o=f.value.splice(s,1)[0];f.value.splice(n,0,o),f.value.forEach((k,u)=>{k.msqNo=u+1}),g("getRightTableList",f.value)}})}),(a,n)=>{const s=I("el-option"),o=I("el-select"),k=I("Search"),u=I("el-button"),j=I("el-input"),h=I("el-table-column"),N=I("el-switch"),q=I("el-link"),B=I("el-table"),ee=I("el-tab-pane"),al=I("el-tabs"),tl=I("ArrowRightBold"),rl=I("ArrowLeftBold");return _(),H("div",{class:"w-transfer-component w-full",style:Ve({height:e.defaultHeight?e.defaultHeight:"500px"})},[V("div",{class:"w-transfer-panel w-source",style:Ve({width:`${e.leftWidth}`,minWidth:"300px",position:a.relative})},[V("span",bl,W(E.value[S.value]||0)+" / "+W(L.value[S.value]||0),1),P(al,{modelValue:S.value,"onUpdate:modelValue":n[2]||(n[2]=i=>S.value=i),onTabClick:Qe},{default:b(()=>[(_(!0),H(re,null,ne(d.value,(i,ge)=>(_(),x(ee,{key:ge,label:i.title,name:i.tab,style:{height:"100%"}},{label:b(()=>[V("div",null,W(i.title),1)]),default:b(()=>[V("div",ml,[P(j,{modelValue:U.value,"onUpdate:modelValue":n[1]||(n[1]=c=>U.value=c),placeholder:"请输入",class:"input-with-select",clearable:"",onClear:pe,onKeyup:Le(pe,["enter"])},{prepend:b(()=>[P(o,{"popper-class":"select-popper-box",modelValue:D.value,"onUpdate:modelValue":n[0]||(n[0]=c=>D.value=c),placeholder:"请选择",style:{width:"100px"}},{default:b(()=>[(_(!0),H(re,null,ne(e.leftColumns,(c,M)=>(_(),x(s,{label:c.label,value:c.prop,key:M},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),append:b(()=>[P(u,{onClick:pe},{default:b(()=>[P(k,{style:{width:"16px",height:"16px"}})]),_:1})]),_:1},8,["modelValue"])]),(_(),x(B,{border:"",data:i.data,style:{width:"100%",overflow:"auto",height:"calc(100% - 110px) !important"},key:A.value,"row-key":"id",ref_for:!0,ref_key:"leftTableRef",ref:F,id:"leftTableRef","reserve-selection":!0,"default-expand-all":"","scrollbar-always-on":"",onSelectionChange:Ye,"header-cell-class-name":Je},{default:b(()=>[P(h,{type:"selection",width:"40",selectable:Re}),e.showLeftMsqNo?(_(),x(h,{key:0,label:"序号",type:"",width:"60"},{default:b(c=>[ue(W(c.row.msqNo),1)]),_:1})):ce("",!0),(_(!0),H(re,null,ne(e.leftColumns,(c,M)=>(_(),x(h,{key:M,prop:c.prop,label:c.label,width:c.width,"show-overflow-tooltip":""},{default:b(J=>[c.uiType==v.value.text?(_(),H("span",yl,W(J.row[c.prop]),1)):c.uiType==v.value.switch&&f.value.length?(_(),x(N,{key:1,"model-value":J.row[c.prop],"inline-prompt":"","active-value":1,"inactive-value":0,"inactive-text":"禁用","active-text":"启用","before-change":se,onChange:le=>ve(le,J.row,c)},null,8,["model-value","onChange"])):c.uiType==v.value.link?(_(),x(q,{key:2,type:"primary",onClick:le=>De(c.webEvent,c,J.row)},{default:b(()=>[ue(W(J.row[c.prop]),1)]),_:2},1032,["onClick"])):c.uiType==v.value.select?(_(),x(o,{key:3,"popper-class":"select-popper-box",filterable:"",clearable:"",modelValue:J.row[c.prop],"onUpdate:modelValue":le=>J.row[c.prop]=le,size:"small"},{default:b(()=>[(_(!0),H(re,null,ne(c.data,le=>(_(),x(s,{label:le.name,value:le.value,key:le.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):c.uiType==v.value.custom?Ie(a.$slots,c.prop,{key:4,data:J,index:J.$index},void 0,!0):ce("",!0)]),_:2},1032,["prop","label","width"]))),128))]),_:2},1032,["data"]))]),_:2},1032,["label","name"]))),128))]),_:3},8,["modelValue"]),V("div",wl,[P(u,{type:"primary",class:"w-transfer-btn w-add-to-left",disabled:!y.value.length,onClick:el},{default:b(()=>[P(tl,{style:{width:"16px",height:"16px"}})]),_:1},8,["disabled"]),P(u,{type:"primary",class:"w-transfer-btn w-remove-from-left",disabled:!R.value.length,onClick:ll},{default:b(()=>[P(rl,{style:{width:"16px",height:"16px"}})]),_:1},8,["disabled"])])],4),V("div",El,[V("div",kl,[ue(W(e.rightTitle),1),V("span",Cl,W(T.value)+" / "+W($.value),1)]),V("div",Sl,[V("div",Tl,[P(j,{modelValue:X.value,"onUpdate:modelValue":n[4]||(n[4]=i=>X.value=i),placeholder:"请输入",class:"w-input-with-select",clearable:"",onClear:fe,onKeyup:Le(fe,["enter"])},{prepend:b(()=>[P(o,{"popper-class":"select-popper-box",modelValue:O.value,"onUpdate:modelValue":n[3]||(n[3]=i=>O.value=i),placeholder:"请选择",style:{width:"100px"}},{default:b(()=>[(_(!0),H(re,null,ne(e.rightColumns,(i,ge)=>(_(),x(s,{label:i.label,value:i.prop,key:ge},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),append:b(()=>[P(u,{onClick:fe},{default:b(()=>[P(k,{style:{width:"16px",height:"16px"}})]),_:1})]),_:1},8,["modelValue"])])]),P(B,{border:"",data:f.value,style:{width:"100%",overflow:"auto",height:"calc(100% - 120px) !important"},onSelectionChange:Ze,"row-key":"id","reserve-selection":"true","header-cell-class-name":Xe,"default-expand-all":"",ref_key:"rightTableRef",ref:ke,"scrollbar-always-on":""},{default:b(()=>[e.draggable?(_(),x(h,{key:0,align:"center",type:"",width:"40"},{default:b(({row:i})=>[xl]),_:1})):ce("",!0),P(h,{type:"selection",width:"40",align:"center",selectable:Re}),e.showMsqNo?(_(),x(h,{key:1,label:"序号",type:"",width:"60",align:"center"},{default:b(i=>[ue(W(i.row.msqNo),1)]),_:1})):ce("",!0),(_(!0),H(re,null,ne(e.rightColumns,(i,ge)=>(_(),x(h,{key:ge,prop:i.prop,label:i.label,width:i.width,"min-width":i.minWidth,"show-overflow-tooltip":""},pl({default:b(c=>[i.uiType==v.value.text?(_(),H("span",Rl,W(c.row[i.prop]),1)):i.uiType==v.value.switch&&f.value.length?(_(),x(N,{key:1,"model-value":c.row[i.prop],"inline-prompt":"","active-value":1,"inactive-value":0,"inactive-text":"禁用","active-text":"启用","before-change":se,onChange:M=>ve(M,c.row,i)},null,8,["model-value","onChange"])):i.uiType==v.value.link?(_(),x(q,{key:2,type:"primary",onClick:M=>De(i.webEvent,i,c.row)},{default:b(()=>[ue(W(c.row[i.prop]),1)]),_:2},1032,["onClick"])):i.uiType==v.value.select?(_(),x(o,{key:3,"popper-class":"select-popper-box",filterable:"",clearable:"",modelValue:c.row[i.prop],"onUpdate:modelValue":M=>c.row[i.prop]=M,onChange:be,size:"small"},{default:b(()=>[(_(!0),H(re,null,ne(i.data,M=>(_(),x(s,{label:M.name,value:M.value,key:M.value,disabled:M.isEnable==0},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):i.uiType==v.value.custom?Ie(a.$slots,i.prop,{key:4,data:c,index:c.$index},void 0,!0):ce("",!0)]),_:2},[i.required?{name:"header",fn:b(c=>[Dl,ue(" "+W(i.label),1)]),key:"0"}:void 0]),1032,["prop","label","width","min-width"]))),128))]),_:3},8,["data"])])],4)}}},Nl=il(Pl,[["__scopeId","data-v-fb15c69f"]]),Wl=e=>K.request("post",z("/v1/human_group/get_human_group"),{data:e}),Al=e=>K.request("post",z("/v1/human_group/create_human_group"),{data:e}),Fl=e=>K.request("post",z("/v1/human_group/update_human_group"),{data:e}),Bl=e=>K.request("post",z("/v1/human_group/get_parent_human_group"),{data:e}),Hl=e=>K.request("post",z("/v1/human_group/get_group_union_ques"),{data:e}),ql=e=>K.request("post",z("/v1/human_group/filter_human_group_member"),{data:e}),Ll=e=>K.request("post",z("/v1/human_group/set_human_group_member"),{data:e}),Il=e=>K.request("post",z("/v1/human_group/get_human_group_member"),{data:e}),Ul=e=>K.request("post",z("/v1/human_group/delete_human_group"),{data:e}),$l=e=>K.request("post",z("/v1/human_group/is_active_human_group"),{data:e}),jl=_l({__name:"group-member",props:{level:{type:String,default:1},parentFormData:{type:Object,default:()=>{}}},setup(e,{expose:l}){const t=e,r=p(null),v=p(null),g=p({leader_user_id_list:[]}),D=vl({itemWidth:"360px",fields:[{label:"组长",prop:"leader_user_id_list",type:"select",clearable:!0,defaultValue:[],filterable:!0,multiple:!0,isHidden:!0,placeholder:"请选择组长",props:{value:"user_id",label:"label"},optionData:()=>O.value},{label:"组长",prop:"user_id_list",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择组长",props:{value:"user_id",label:"label"},optionData:()=>O.value}]}),O=p([]);let U=[{prop:"name",label:"用户名"},{prop:"username",label:"账号"},{prop:"has_group_label",label:"入组状态",width:"86px"}],X=[{prop:"name",label:"用户名"},{prop:"username",label:"账号"}];const Q=p([{id:0,tab:1,title:"选择组员",data:[]}]),Y=p([]),Z=p(0),se=p();ae(()=>Z.value,()=>{Z.value===2&&(Q.value[0].data=Q.value[0].data.filter(d=>!Y.value.map(m=>m.id).includes(d.id)),Z.value=0)});const ve=()=>{se.value!==t.level&&(se.value=t.level,be(!0),t.level===3?(be(),D.rules={},D.fields.forEach(d=>{d.prop==="leader_user_id_list"?d.isHidden=!1:d.prop==="user_id_list"&&(d.isHidden=!0)})):(D.rules={user_id_list:[{required:!0,message:"请选择组长",trigger:["blur","change"]}]},D.fields.forEach(d=>{d.prop==="leader_user_id_list"?d.isHidden=!0:d.prop==="user_id_list"&&(d.isHidden=!1)})))},be=(d=!1)=>{var w,R;let m={parent_group_id:typeof t.parentFormData.parent_group_id=="string"?t.parentFormData.parent_group_id:(R=(w=t.parentFormData)==null?void 0:w.parent_group_id)==null?void 0:R[1],group_level:t.level,team_leader:d};ql(m).then(y=>{var A,F,E,L;y.code&&y.code===200?d?(y.data.data.forEach(T=>{let $=T.username;T.name&&($+=`（${T.name}）`),T.label=$}),O.value=y.data.data):((F=(A=y.data)==null?void 0:A.data)==null||F.forEach((T,$)=>{T.id=T.user_id,T.tab=1,T.sortOrder=$,T.has_group?T.has_group_label="已分配":T.has_group_label="未分配"}),(L=(E=y.data)==null?void 0:E.data)==null||L.sort((T,$)=>Number(T.has_group)-Number($.has_group)),Q.value[0].data=y.data.data,t.level===3&&(Z.value+=1)):Se.error(y.msg)})},S=d=>{Il({group_id:d}).then(w=>{var R,y,A,F;w.code&&w.code===200?t.level===1||t.level===2?g.value.user_id_list=(R=w.data.leader_user_list[0])==null?void 0:R.user_id:t.level===3&&(g.value.leader_user_id_list=w.data.leader_user_list.map(E=>E.user_id),(A=(y=w.data)==null?void 0:y.user_id_list)==null||A.forEach((E,L)=>{E.id=E.user_id,E.tab=1,E.sortOrder=L,E.has_group?E.has_group_label="已分配":E.has_group_label="未分配"}),Y.value=(F=w.data)==null?void 0:F.user_id_list,Z.value+=1):Se.error(w.msg)})},Ee=()=>{var m,w;let d={};return t.level===1||t.level===2?d.leader_user_id_list=g.value.user_id_list?[g.value.user_id_list]:[]:t.level===3&&(d.leader_user_id_list=g.value.leader_user_id_list,d.user_id_list=(w=(m=r.value)==null?void 0:m.getRightData())==null?void 0:w.map(R=>R.user_id)),d};return l({getHumanMember:ve,formData:g,setHumanGroupMember:d=>{let m=G({group_id_list:typeof d=="string"?[d]:d},Ee());return new Promise((w,R)=>{var y,A,F,E;if(t.level===1||t.level===2){if(((y=m.leader_user_id_list)==null?void 0:y.length)===0||((A=m.leader_user_id_list)==null?void 0:A[0])==null){R();return}}else if(t.level===3&&(!m.user_id_list||((F=m.user_id_list)==null?void 0:F.length)===0)&&((E=m.leader_user_id_list)==null?void 0:E.length)===0){R("请先设置成员！");return}Ll(m).then(L=>{L.code&&L.code===200?w(L):Se.error(L.msg)})})},getHumanGroupMember:S,clearData:()=>{g.value={leader_user_id_list:[]},se.value=0,Y.value=[],Q.value[0].data=[]},getMember:Ee,handleValidateForm:()=>v.value.formValidate()}),(d,m)=>{const w=I("form-component");return _(),H("div",null,[P(w,{ref_key:"formRef",ref:v,modelValue:g.value,"onUpdate:modelValue":m[0]||(m[0]=R=>g.value=R),"form-options":D,"is-query-btn":!1,onOnchangeFn:d.onchangeFn},null,8,["modelValue","form-options","onOnchangeFn"]),e.level===3?(_(),x(Nl,{key:0,ref_key:"transferRef",ref:r,rightTitle:"已选组员",listTabs:Q.value,leftWidth:"50%",leftColumns:Oe(U),rightColumns:Oe(X),rightTableList:Y.value,onGetRightTableList:d.getRightTableList,onChangeSwitch:d.changeSwitch},null,8,["listTabs","leftColumns","rightColumns","rightTableList","onGetRightTableList","onChangeSwitch"])):ce("",!0)])}}});export{jl as _,Wl as a,Hl as b,Al as c,Ul as d,Bl as g,$l as i,Fl as u};
