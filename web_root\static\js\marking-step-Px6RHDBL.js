import{d as $,l as s,n as B,r as d,o as b,c as y,e as m,q as L,F as P,p as z,U as A,t as K,b as u,h as c,f as p,i6 as U,_ as j}from"./index-B63pSD2p.js";import{f as O}from"./validate-Dc6ka3px.js";const G={class:"manual-right-box bg-[#fff] dark:bg-black eye-box drag-content-box"},H={class:"marking-area"},J=["onClick"],Q={class:"quick-btn-box"},X={class:"custom-score"},Y=$({__name:"marking-step",props:["quesInfo","score_step"],emits:["setMarkingPanelWidth"],setup(F,{expose:N,emit:R}){const v=F,I=R,x=s(!1),_=s(null),g=s(null),C=s(null),S={scoreInput:[{required:!0,message:"请输入评分分数",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,e,o)=>{if(O(e))o();else return o(new Error("请输入数字（至多两位小数）！"))}}]},r=s({scoreInput:null});s(null);const i=s([]),f=s(264);B(()=>{var e;const t=(e=g.value)==null?void 0:e.$el.querySelector("input");t&&(t.classList.add("no-drag"),t.classList.add("data-drag-cancel"),t.setAttribute("data-drag-cancel","drag"+Math.random()))});const w=t=>{x.value=!0;const{ques_order:e,ques_score_list:o,total_score:h}=Object.keys(v.quesInfo).length>0?v.quesInfo:t;if(e){let a=0;o!=null&&o.length?a=o.map(Number).reduce((V,T)=>V+T,0):a=Number(h);let l=[];for(let n=0;n<=a;n+=Number(v.score_step))l.push(D(n));l.length>0&&(l[l.length-1]!==a&&l.push(a),i.value=l,l.length<5&&(f.value=264),l.length>=20&&(f.value=602),I("setMarkingPanelWidth",f.value))}},D=t=>{let e=t.toFixed(1);return e.endsWith(".0")?parseInt(e):e.endsWith("0")?e.slice(0,-1):e},W=()=>{x.value=!1,r.value.scoreInput=null,i.value=[],_.value.clearValidate(),setTimeout(()=>{f.value=264},500)},k=t=>{r.value.scoreInput=t},q=()=>{r.value.scoreInput=null,setTimeout(()=>{_.value.clearValidate(),g.value.focus()},100)},E=()=>{I("markConfirmFn",r.value.scoreInput)},M=t=>{t.preventDefault(),C.value.ref.focus()};return N({openDialog:w,closeDialog:W,clearScoreFn:q}),(t,e)=>{const o=d("el-button"),h=d("el-input"),a=d("el-form-item"),l=d("el-form");return b(),y("div",G,[m("div",H,[m("div",{class:"quick-mark-box",style:L("width:"+f.value+"px")},[(b(!0),y(P,null,z(i.value,n=>(b(),y("div",{class:A(["quick-score",r.value.scoreInput===n?"select-score":""]),onClick:V=>k(n)},K(n),11,J))),256))],4),m("div",Q,[u(o,{type:"plain",onClick:q},{default:c(()=>[p("清除")]),_:1}),u(o,{type:"warning",onClick:e[0]||(e[0]=n=>k(0))},{default:c(()=>[p("零分")]),_:1}),u(o,{type:"success",onClick:e[1]||(e[1]=n=>k(i.value[i.value.length-1]))},{default:c(()=>[p("满分")]),_:1})]),m("div",X,[u(l,{model:r.value,rules:S,ref_key:"formRef",ref:_,"validate-on-rule-change":!1},{default:c(()=>[u(a,{prop:"scoreInput"},{default:c(()=>[u(h,{ref_key:"elInputRef",ref:g,clearable:"",modelValue:r.value.scoreInput,"onUpdate:modelValue":e[2]||(e[2]=n=>r.value.scoreInput=n),style:{width:"150px"},onKeyup:U(M,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),u(o,{ref_key:"scoreConfirmRef",ref:C,type:"primary",onClick:E},{default:c(()=>[p("确定 ")]),_:1},512)])])])}}}),te=j(Y,[["__scopeId","data-v-e55fbfa6"]]);export{te as default};
