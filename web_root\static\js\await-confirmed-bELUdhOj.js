var A=Object.defineProperty;var y=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var C=(t,e,a)=>e in t?A(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,S=(t,e)=>{for(var a in e||(e={}))I.call(e,a)&&C(t,a,e[a]);if(y)for(var a of y(e))L.call(e,a)&&C(t,a,e[a]);return t};import{d as U,l as n,P as T,n as E,ao as M,T as k,r as m,o as D,c as j,e as f,b as r,h as _,f as Q,u as $,aN as G,y as J,ac as K,ad as X,_ as Y}from"./index-B63pSD2p.js";import{g as Z}from"./plagiarizing-answer-DCON8hS9.js";import{c as ee,a as te}from"./calculateTableHeight-BjE6OFD1.js";import{p as ae,g as oe,a as le}from"./common-methods-BWkba4Bo.js";import ne from"./confirm-dialog-V4wtUUxX.js";import re from"./batch-confirm-dialog-OWMnJuCJ.js";import"./test-paper-management-DjV_45YZ.js";const ie=t=>(K("data-v-dfffb5c1"),t=t(),X(),t),se={class:"zf-first-box"},pe={class:"zf-second-box"},ue=ie(()=>f("div",{style:{height:"6px",background:"#e0e2e8"}},null,-1)),ce={class:"zf-flex-end"},de={class:"task-btn-box"},me=["onClick"],_e=U({name:"single-plagiarizing",__name:"await-confirmed",setup(t){const e=n(null),a=n(null),b=n(!1),g=n(!1),d=n({}),O=T({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>ae.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>s.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>s.value},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入考试密号",clearable:!0},{label:"考生密号",prop:"username2",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"评分使用",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评分使用",optionData:()=>s.value}]}),s=n([]),v=n([]),p=n({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_order",label:"题号",minWidth:"90px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"task_name",label:"考务评分",minWidth:"120px"},{prop:"region",label:"阅卷评分",minWidth:"160px"},{prop:"work_unit",label:"是否一致",minWidth:"120px"},{prop:"work_unit1",label:"最终评分",minWidth:"120px"},{prop:"role_name",label:"评分使用",minWidth:"120px",formatter:o=>"阅卷系统"},{prop:"operation",label:"操作",type:"slot",minWidth:"110px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),w=n([]);let x=null;E(()=>{oe(),ee(x,e.value,p.value,!0,58),h()}),M(()=>{te(x)});const V=(o,l)=>{o.prop==="project_id"&&(s.value=[],d.value.subject_id&&(d.value.subject_id=null),l&&le(l).then(u=>{s.value=u||[]}))},h=()=>{let{currentPage:o,pageSize:l}=p.value.pageOptions,u=S({current_page:o,page_size:l},d.value);Z(u).then(c=>{c.code&&c.code===200||k.error(c.msg)}),w.value=[{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"程序员(基础知识)",paper_name:"—",ques_order:"—",ques_code:"19490982231",task_name:"1",stu_secret_num:"2003939999020001",region:"0",work_unit:"否",work_unit1:"0"}]},W=o=>{p.value.pageOptions.pageSize=o,h()},z=o=>{p.value.pageOptions.currentPage=o,h()},F=o=>{v.value=o},q=()=>{b.value=!0},H=()=>{if(!v.value.length){k.warning("至少选择一条数据！");return}g.value=!0};function B(){s.value=[]}return(o,l)=>{const u=m("form-component"),c=m("el-card"),P=m("el-button"),R=m("Auth"),N=m("table-component");return D(),j("div",se,[f("div",pe,[r(c,null,{default:_(()=>[f("div",{ref_key:"formDivRef",ref:e},[r(u,{ref_key:"formRef",ref:a,modelValue:d.value,"onUpdate:modelValue":l[0]||(l[0]=i=>d.value=i),"form-options":O,"is-query-btn":!0,onOnchangeFn:V,onQueryDataFn:h,onResetFields:B},null,8,["modelValue","form-options"])],512)]),_:1}),ue,r(c,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:_(()=>[f("div",ce,[r(R,{value:"objective-check/batch-grade-confirmed"},{default:_(()=>[r(P,{type:"primary",onClick:H},{default:_(()=>[Q("批量成绩确定")]),_:1})]),_:1})]),r(N,{minHeight:p.value.styleOptions.minHeight,"table-options":p.value,"table-data":w.value,onOnHandleSizeChange:W,onOnHandleCurrentChange:z,onOnHandleSelectionChange:F},{operation:_(i=>[f("div",de,[$(G)("objective-check/grade-confirmed")?(D(),j("span",{key:0,class:"task-btn",onClick:fe=>q(i.row)},"成绩确定",8,me)):J("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),r(ne,{isShowConfirmDialog:b.value,"onUpdate:isShowConfirmDialog":l[1]||(l[1]=i=>b.value=i)},null,8,["isShowConfirmDialog"]),r(re,{isShowDialog:g.value,"onUpdate:isShowDialog":l[2]||(l[2]=i=>g.value=i)},null,8,["isShowDialog"])])}}}),Se=Y(_e,[["__scopeId","data-v-dfffb5c1"]]);export{Se as default};
