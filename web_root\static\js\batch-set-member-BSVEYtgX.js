import{_ as k}from"./group-member.vue_vue_type_script_setup_true_lang-CCOO0MKG.js";import{d as x,l as a,r as m,o as M,g as C,h as t,e as i,b as u,f as d,T as v,_ as y}from"./index-B63pSD2p.js";const B={class:"footer-btn"},F=x({__name:"batch-set-member",setup(N,{expose:f}){const g=a("批量设置组员"),o=a(!1),s=a([]),r=a(null),l=a(null),c=a({}),b=e=>{o.value=!0,r.value=e[0].group_level,s.value=e,c.value={group_level:r.value,parent_group_id:s.value[0].parent_group_id},setTimeout(()=>{l.value.getHumanMember()},100)},n=()=>{o.value=!1,l.value.clearData()},h=()=>{l.value.setHumanGroupMember(s.value.map(e=>e.group_id)).then(e=>{v.success(e.msg),n()}).catch(e=>{v.error(e.msg)})};return f({openDialog:b}),(e,p)=>{const _=m("el-button"),D=m("el-dialog");return M(),C(D,{modelValue:o.value,"onUpdate:modelValue":p[0]||(p[0]=V=>o.value=V),title:g.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",size:"70%","close-on-click-modal":!1,"before-close":n},{footer:t(()=>[i("div",B,[u(_,{onClick:n},{default:t(()=>[d("取消")]),_:1}),u(_,{type:"primary",onClick:h},{default:t(()=>[d("保存")]),_:1})])]),default:t(()=>[i("div",null,[u(k,{ref_key:"groupMemberRef",ref:l,level:r.value,parentFormData:c.value},null,8,["level","parentFormData"])])]),_:1},8,["modelValue","title"])}}}),H=y(F,[["__scopeId","data-v-f8217aee"]]);export{H as default};
