import{aQ as I,aR as S,d as N,i as A,l as g,n as G,ao as M,T,r as y,o as m,c as f,e as s,b as o,h as p,y as U,f as w,F as W,p as X,U as F,t as D,u as $,ac as j,ad as Y,_ as J}from"./index-B63pSD2p.js";import{P as Z}from"./index-Astsj93h.js";import H from"./importExamineAnswer-CdqPvE5P.js";import"./base-DyvdloLK.js";const K="/static/png/0-ohYwGQPs.png",O="/static/png/1-DqXUo9xs.png",P="/static/png/2-DQc5ZdP7.png",ee=i=>I.request("post",S("/v1/prepare/get_prepare_process_list"),{data:i},{},!1),te=i=>I.request("post",S("/v1/exception/calculate_ques_answer_similarity"),{data:i},{},!1),n=i=>(j("data-v-4bcc7cf5"),i=i(),Y(),i),se={class:"prepare-process"},ae={class:"section section-data"},re=n(()=>s("h4",null,"数据准备",-1)),oe={class:"arrow-row data-flex-row"},ne={class:"card-wrapper"},le=n(()=>s("img",{src:K,alt:"",class:"img_wf"},null,-1)),ie=n(()=>s("div",{class:"title"},"考试数据",-1)),pe={key:0,class:"progressDiv"},ce=n(()=>s("div",{class:"arrow-col"},[s("svg",{class:"arrow-svg",width:"40",height:"32",viewBox:"0 0 30 32",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[s("line",{x1:"2",y1:"16",x2:"26",y2:"16",stroke:"#BFBFBF","stroke-width":"3","stroke-dasharray":"5 5"}),s("polygon",{points:"26,7 30,16 26,25",fill:"#BFBFBF"})])],-1)),ue={class:"card-wrapper"},de=n(()=>s("img",{src:O,alt:"",class:"img_wf"},null,-1)),_e=n(()=>s("div",{class:"title"},"作答数据",-1)),ge=n(()=>s("div",{class:"arrow-col"},null,-1)),me={class:"card-wrapper"},fe=n(()=>s("img",{src:P,alt:"",class:"img_wf"},null,-1)),ve=n(()=>s("div",{class:"title"},"考生数据",-1)),he={class:"section section-score"},we=n(()=>s("h4",null,"评分准备",-1)),be={class:"score-row score-flex-row"},xe=["src"],ye={class:"title"},ke={key:0,class:"progressDiv"},Ce=N({name:"",__name:"prepareWorkFlow(bak)",setup(i){const c=A(),k=g([{title:"作答抄袭识别",img:new URL("/static/png/3-BurQUM8Q.png",import.meta.url).href,btnText:"查看",state:0,perCentValue:0},{title:"雷同卷识别",img:new URL("/static/png/4-DaXNb-js.png",import.meta.url).href,btnText:"开始识别",state:0,perCentValue:0},{title:"抄袭题干识别",img:new URL("/static/png/5-CDdIAu3u.png",import.meta.url).href,btnText:"开始识别",state:0,perCentValue:0},{title:"AI评分",img:new URL("/static/png/6-CJc-WceL.png",import.meta.url).href,btnText:"前往评分",state:0,perCentValue:0},{title:"客观题评分",img:new URL("/static/png/7-BYw2X6d9.png",import.meta.url).href,btnText:"前往评分",state:0,perCentValue:0}]);G(()=>{v()}),M(()=>{l.value&&clearTimeout(l.value)});const b=g([]),l=g(null),v=()=>{ee().then(a=>{if(a.code!=200)return T.error("后端接口异常:"+a.msg);b.value=a.data.map(t=>({process_status:t.process_status,process_type:t.process_type,progress:t.progress})),k.value.forEach((t,u)=>{var _;let d=u+2,e=(_=a.data)==null?void 0:_.find(B=>B.process_type==d);t.state=e==null?void 0:e.process_status,t.perCentValue=e==null?void 0:e.progress,u<3&&((e==null?void 0:e.process_status)==0?t.btnText="开始识别":(e==null?void 0:e.process_status)==1?t.btnText="识别中":(e==null?void 0:e.process_status)==2?t.btnText="查看":(e==null?void 0:e.process_status)==3&&(t.btnText="失败"))});var r=a.data.filter(t=>t.process_status==1);r&&r.length>0&&(l.value=setTimeout(()=>{v()},3e3))})},L=a=>{var r;return(r=b.value.find(t=>t.process_type==a))==null?void 0:r.process_status},R=a=>{var r;return(r=b.value.find(t=>t.process_type==a))==null?void 0:r.progress},h=g(!1),V=()=>{h.value=!0},q=g(null),z=()=>{C(),h.value=!1},C=()=>{l.value&&(clearTimeout(l.value),l.value=null),v()},E=(a,r)=>{a.state==1?x(r+3):r==2&&te().then(t=>{if(t.code!=200)return T.error("后端接口异常:"+t.msg);C()})},x=a=>{a==1&&c.push({path:"/response-management/examinees-management/index"}),a==2&&c.push({path:"/response-management/response/index"}),a==3&&c.push({path:"/abnormal-paper-management/plagiarizing-answer/index"}),a==4&&c.push({path:"/abnormal-paper-management/similar-paper/index"}),a==5&&c.push({path:"/abnormal-paper-management/plagiarzing-question-stem/index"}),a==6&&c.push({path:"/ai-calibration/calibrate/index"}),a==7&&c.push({path:"/formal-marking/objective-ques/index"})},Q=()=>{l.value&&(clearTimeout(l.value),l.value=null)};return(a,r)=>{const t=y("el-progress"),u=y("el-card"),d=y("el-button");return m(),f("div",se,[s("div",ae,[re,s("div",oe,[s("div",ne,[o(u,{class:"card"},{default:p(()=>[le,ie,L(1)==1?(m(),f("div",pe,[o(t,{"text-inside":!0,"stroke-width":15,percentage:R(1)},null,8,["percentage"])])):U("",!0)]),_:1}),o(d,{type:"primary",class:"btn",size:"small",onClick:V},{default:p(()=>[w(" 导入 ")]),_:1})]),ce,s("div",ue,[o(u,{class:"card"},{default:p(()=>[de,_e]),_:1}),o(d,{type:"primary",class:"btn",size:"small",onClick:r[0]||(r[0]=e=>x(1))},{default:p(()=>[w(" 查看 ")]),_:1})]),ge,s("div",me,[o(u,{class:"card"},{default:p(()=>[fe,ve]),_:1}),o(d,{type:"primary",class:"btn",size:"small",onClick:r[1]||(r[1]=e=>x(2))},{default:p(()=>[w(" 查看 ")]),_:1})])])]),s("div",he,[we,s("div",be,[(m(!0),f(W,null,X(k.value,(e,_)=>(m(),f("div",{class:"card-wrapper",key:_},[o(u,{class:F(["card",{cardGray:e.state==0}])},{default:p(()=>[s("img",{src:e.img,alt:"",class:F(["img_wf",{imgGray:e.state!=1}])},null,10,xe),s("div",ye,D(e.title),1),e.state==1?(m(),f("div",ke,[o(t,{"text-inside":!0,"stroke-width":15,percentage:e.perCentValue},null,8,["percentage"])])):U("",!0)]),_:2},1032,["class"]),o(d,{type:"primary",class:"btn",size:"small",disabled:e.disabled,onClick:B=>E(e,_)},{default:p(()=>[w(D(e.btnText),1)]),_:2},1032,["disabled","onClick"])]))),128))])]),o($(Z),{ref_key:"progressBarRef",ref:q,title:"导入作答成绩进度",apiInfo:{params:{record_type:3},url:"/v1/transfer/get_import_process"},onQueryData:v},null,512),o(H,{title:"导入作答数据",isShowDialog:h.value,"onUpdate:isShowDialog":r[2]||(r[2]=e=>h.value=e),onUploadBegin:z,onUploadSuccess:a.queryListFn,onUploadError:Q},null,8,["isShowDialog","onUploadSuccess"])])}}}),De=J(Ce,[["__scopeId","data-v-4bcc7cf5"]]);export{De as default};
