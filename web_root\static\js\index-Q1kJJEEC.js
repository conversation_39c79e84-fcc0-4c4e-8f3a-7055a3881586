var ue=Object.defineProperty,de=Object.defineProperties;var ve=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var _e=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable;var X=(r,o,n)=>o in r?ue(r,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[o]=n,b=(r,o)=>{for(var n in o||(o={}))_e.call(o,n)&&X(r,n,o[n]);if(H)for(var n of H(o))ge.call(o,n)&&X(r,n,o[n]);return r},Y=(r,o)=>de(r,ve(o));import fe from"./homepage-tabs-KBTJlgKv.js";import{b as Z,c as he,d as pe,e as me,f as be}from"./index-aLSsAUpX.js";import{S as ee}from"./scoreRelateLineChart-DOiNxsyg.js";import xe from"./basic-monitor-jamMEtXh.js";import De from"./table-monitor-D2MxPGP1.js";import ae from"./barChart-BwsIMmQR.js";import{P as te}from"./processChart-BH-8wiYU.js";import{d as ye,l,n as ke,a1 as se,a2 as Le,P as we,r as B,j as Fe,o as i,c as M,ae as Te,e as t,b as g,h as E,u as oe,aT as ne,w as A,g as h,f as z,t as G,C as le,N as Ke,ac as Me,ad as Qe,_ as Pe}from"./index-B63pSD2p.js";import"./useTag-CrO3NxXA.js";import"./index-C9GYnvBh.js";const x=r=>(Me("data-v-f2fdc301"),r=r(),Qe(),r),Be={class:"dashboard"},je={class:"tabs-cont"},Se={key:0,class:"h-full main-monitor"},Ce={class:"left"},Ae={class:"l-top dark:!bg-black eye-box echart-card"},qe={class:"echart-title"},Ie={class:"echart-wrap"},Ee={class:"l-bottom dark:!bg-black eye-box"},Je={class:"right",ref:"rightRef"},$e={class:"r-top dark:!bg-black eye-box"},Ne={class:"r-middle dark:!bg-black eye-box echart-card"},Ve={class:"echart-title"},ze=x(()=>t("div",null,"评分进度",-1)),Ge={class:"echart-wrap"},Re={class:"r-bottom1 dark:!bg-black eye-box echart-card"},Ue=x(()=>t("div",{class:"echart-title"},[t("div",null,"成绩情况")],-1)),Oe={class:"echart-wrap"},We={class:"scoreQingKuang"},He={class:"scoreQingKuang-item"},Xe={class:"scoreQK-cont"},Ye=x(()=>t("span",{class:"scoreQK-unit"},"%",-1)),Ze=x(()=>t("div",{class:"scoreQK-title"},"及格率",-1)),ea={class:"scoreQingKuang-item"},aa={class:"scoreQK-cont"},ta=x(()=>t("span",{class:"scoreQK-unit"},"%",-1)),sa=x(()=>t("div",{class:"scoreQK-title"},"满分率",-1)),oa={class:"scoreQingKuang-item"},na={class:"scoreQK-cont"},la=x(()=>t("span",{class:"scoreQK-unit"},"%",-1)),ia=x(()=>t("div",{class:"scoreQK-title"},"零分率",-1)),ra={class:"fullscreen-chart-wrapper"},ca=x(()=>t("template",null,null,-1)),ua=ye({__name:"index",setup(r){const o=l({task_type:1,round_count:"1",subject_id:"test"});ke(()=>{se.on("dataStatistics:reflushEcharts",e=>{o.value=e,e.subject_id&&ie()})}),Le(()=>{se.off("dataStatistics:reflushEcharts")});const n=l([]),w=l({}),F=l([]),T=l(!0),f={xData:["1题","2题","3题","4题","5题","6题","7题","8题","9题","10题"],yAxis:{minInterval:1,axisLabel:{formatter:function(e){return e+"份"}},valueFormatter:e=>e+"份"},series:[{name:"已阅量",data:[0,0,0,0,0,0,0,0,0,0],color:"#409eff"},{name:"未阅量",data:[0,0,0,0,0,0,0,0,0,0],color:"#20c7a6"}],showLegend:!0},Q={xData:["1题","2题","3题","4题","5题","6题","7题","8题","9题","10题"],yAxis:{axisLabel:{formatter:function(e){return e+"%"}},valueFormatter:e=>e+"%"},series:[{name:"得分率",data:[0,0,0,0,0,0,0,0,0,0],color:"#409eff"}],stacked:!1,showLegend:!1};n.value=f.xData,w.value=f.yAxis,F.value=f.series,T.value=f.showLegend;const p={xData:["0分","1分","2分","3分","4分","5分","6分","7分","8分","9分"],options:{tooltip:{valueFormatter:e=>e+"人"},grid:{top:10},legend:{show:!1},xAxis:{data:[],name:"分",nameGap:3,axisLabel:{formatter:e=>e}},yAxis:{minInterval:1,axisLabel:{formatter:function(e){return e+"人"}}}},series:[{name:"成绩百分比",label:{show:!1},areaStyle:{color:"rgba(64,158,255,0.2)"},color:"#409eff",data:[0,0,0,0,0,0,0,0,0,0]}]};p.options.xAxis.data=p.xData;const j=l(p.xData),q=l(p.options),S=l(p.series);function ie(){R(),I.value=!0,J.value=[],m.value[0].value="-",m.value[1].value="-",m.value[2].value="-",m.value[3].value="-",Z(b({},o.value)).then(e=>{var v,_,c,u,k,L,P,W;const a=(_=(v=e.data)==null?void 0:v.group_data)!=null?_:[],s=(u=(c=e.data)==null?void 0:c.total)!=null?u:{};a.forEach(V=>{J.value.push(b({title:`${V.subject_name}-${V.group_name}`},V))}),m.value[0].value=(k=s.ques_group_count)!=null?k:"-",m.value[1].value=(L=s.expert_total)!=null?L:"-",m.value[2].value=(P=s.exception_total)!=null?P:"-",m.value[3].value=(W=s.total_count_total)!=null?W:"-"}).finally(()=>{I.value=!1}),$.value=!0,D.value=0,he(b({},o.value)).then(e=>{var a,s;D.value=(s=(a=e.data)==null?void 0:a.progress)!=null?s:void 0}).finally(()=>{$.value=!1}),N.value=!0,y.value={},pe(b({},o.value)).then(e=>{var a;y.value=(a=e.data)!=null?a:{}}).finally(()=>{N.value=!1})}const C=l(!1),K=l("geTiPingFenQKMonitor");function re(e){const a=e.props.name;if(K.value!==a){switch(a){case"geTiPingFenQKMonitor":n.value=f.xData,w.value=f.yAxis,F.value=f.series,T.value=f.showLegend;break;case"geTiDefenLvMonitor":n.value=Q.xData,w.value=Q.yAxis,F.value=Q.series,T.value=Q.showLegend;break;case"chengJiFenBuMonitor":j.value=p.xData,q.value=p.options,S.value=p.series;break}le(()=>{R()})}}function R(){switch(C.value=!0,K.value){case"geTiPingFenQKMonitor":Z(b({},o.value)).then(e=>{var s,v,_,c;T.value=f.showLegend,w.value=f.yAxis;const a=(v=(s=e.data)==null?void 0:s.workload_data)!=null?v:{};n.value=(c=(_=a.x_data)==null?void 0:_.map(u=>u.ques_group_name))!=null?c:[],a.legend&&(F.value=a.legend.map((u,k)=>({name:u,data:a[`y${k+1}_data`].length!==0?a[`y${k+1}_data`]:[],color:k===0?"#409eff":"#20c7a6"})))}).finally(()=>{C.value=!1});break;case"geTiDefenLvMonitor":be(b({},o.value)).then(e=>{var s,v;T.value=Q.showLegend,w.value=Q.yAxis;const a=(s=e.data)!=null?s:{};n.value=(v=a.x_data)!=null?v:[],a.legend&&(F.value=a.legend.map((_,c)=>({name:_,data:a.y_data.length!==0?a.y_data:[],color:"#409eff"})))}).finally(()=>{C.value=!1});break;case"chengJiFenBuMonitor":me(b({},o.value)).then(e=>{var a,s,v,_,c;j.value=(v=(s=(a=e.data)==null?void 0:a.x_data)==null?void 0:s.map(u=>`${u}分`))!=null?v:[],q.value.xAxis={axisLabel:{formatter:u=>u.replace(/分/g,"")},name:"分",nameGap:3,data:j.value},S.value=[Y(b({},p.series[0]),{data:[]})],(c=(_=e.data)==null?void 0:_.y_data)==null||c.forEach(u=>{S.value[0].data.push(u||0)})}).finally(()=>{C.value=!1});break}}const m=l([{name:"阅卷题数",value:"-",image:"yyjl"},{name:"参与人员",value:"-",image:"wxl"},{name:"问题卷量",value:"-",image:"yxl"},{name:"阅卷总量(人题)",value:"-",image:"pjsd"}]),I=l(!1),J=l([]),$=l(!1),D=l(0),N=l(!1),y=l({}),d=we({visible:!1,type:"",title:""}),U=()=>{d.type="",d.visible=!1};function O(e){e=="geTiPingFenQKMonitor"?d.title="各题评分情况":e=="geTiDefenLvMonitor"?d.title="各题得分率":e=="chengJiFenBuMonitor"?d.title="成绩分布":e=="pingfenProcess"&&(d.title="评分进度"),d.visible=!0,le(()=>{d.type=e})}function ce(e,a){const s=`sId_${e.subject_id}_qgId_${e.ques_group_id}`;Ke().setItem(s,{group_name:e.group_name,ques_group_id:e.ques_group_id,subject_id:e.subject_id,subject_name:e.subject_name,project_id:o.value.project_id,pageName:"question"}),a({path:"/data-statistics/question-group-dashboard/index",query:{key:s}})}return(e,a)=>{const s=B("el-tab-pane"),v=B("el-tabs"),_=B("el-button"),c=B("el-empty"),u=B("DialogComponent"),k=B("el-card"),L=Fe("loading");return i(),M("div",Be,[Te(e.$slots,"tab",{},()=>[t("div",je,[g(fe,{maxUserRole:"7"})])],!0),o.value.subject_id?(i(),M("div",Se,[t("div",Ce,[t("div",Ae,[t("div",qe,[g(v,{modelValue:K.value,"onUpdate:modelValue":a[0]||(a[0]=P=>K.value=P),onTabClick:re},{default:E(()=>[g(s,{label:"各题评分情况",name:"geTiPingFenQKMonitor"}),g(s,{label:"各题得分率",name:"geTiDefenLvMonitor"}),g(s,{label:"成绩分布",name:"chengJiFenBuMonitor"})]),_:1},8,["modelValue"]),t("div",null,[g(_,{class:"fullscreen-btn",title:"全屏",icon:oe(ne),circle:"",text:"",onClick:a[1]||(a[1]=P=>O(K.value))},null,8,["icon"])])]),A((i(),M("div",Ie,[["geTiPingFenQKMonitor","geTiDefenLvMonitor"].includes(K.value)&&n.value.length!==0?(i(),h(ae,{key:0,xData:n.value,barWidth:"10%",yAxis:w.value,series:F.value,showLegend:T.value},null,8,["xData","yAxis","series","showLegend"])):["chengJiFenBuMonitor"].includes(K.value)&&j.value.length!==0?(i(),h(ee,{key:1,options:q.value,series:S.value},null,8,["options","series"])):(i(),h(c,{key:2,description:"暂无数据","image-size":80}))])),[[L,C.value]])]),A((i(),M("div",Ee,[g(De,{"is-subject":!0,tableListData:J.value,onOpenDetailPage:ce},null,8,["tableListData"])])),[[L,I.value]])]),t("div",Je,[A((i(),M("div",$e,[g(xe,{basicData:m.value},null,8,["basicData"])])),[[L,I.value]]),t("div",Ne,[t("div",Ve,[ze,t("div",null,[g(_,{class:"fullscreen-btn",title:"全屏",icon:oe(ne),circle:"",text:"",onClick:a[2]||(a[2]=P=>O("pingfenProcess"))},null,8,["icon"])])]),A((i(),M("div",Ge,[D.value!==void 0&&D.value!==null?(i(),h(te,{key:0,hiddenLegend:!0,data:D.value},null,8,["data"])):(i(),h(c,{key:1,description:"暂无数据","image-size":60}))])),[[L,$.value]])]),t("div",Re,[Ue,A((i(),M("div",Oe,[t("div",We,[t("div",He,[t("div",Xe,[z(G(y.value.pass_rate!==void 0?y.value.pass_rate.toFixed(2):"-"),1),Ye]),Ze]),t("div",ea,[t("div",aa,[z(G(y.value.full_score_rate!==void 0?y.value.full_score_rate.toFixed(2):"-"),1),ta]),sa]),t("div",oa,[t("div",na,[z(G(y.value.zero_score_rate!==void 0?y.value.zero_score_rate.toFixed(2):"-"),1),la]),ia])])])),[[L,N.value]])])],512),g(u,{isShowDialog:d.visible,onCloseDialog:U,beforeClose:U,title:d.title,fullscreen:!0,class:"rootDialogClass"},{content:E(()=>[t("div",ra,[["geTiPingFenQKMonitor","geTiDefenLvMonitor"].includes(d.type)&&n.value.length!==0?(i(),h(ae,{key:0,xData:n.value,barWidth:"10%",yAxis:w.value,series:F.value,showLegend:T.value},null,8,["xData","yAxis","series","showLegend"])):["chengJiFenBuMonitor"].includes(d.type)&&j.value.length!==0?(i(),h(ee,{key:1,options:q.value,series:S.value},null,8,["options","series"])):d.type==="pingfenProcess"&&D.value!==void 0&&D.value!==null?(i(),h(te,{key:2,hiddenLegend:!0,data:D.value,fullScreen:!0},null,8,["data"])):(i(),h(c,{key:3,description:"暂无数据"}))])]),footer:E(()=>[ca]),_:1},8,["isShowDialog","title"])])):(i(),h(k,{key:1,class:"noData"},{default:E(()=>[g(c,{description:"暂无数据"})]),_:1}))])}}}),ya=Pe(ua,[["__scopeId","data-v-f2fdc301"]]);export{ya as default};
