var V=Object.defineProperty;var h=Object.getOwnPropertySymbols;var W=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var g=(a,e,t)=>e in a?V(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,w=(a,e)=>{for(var t in e||(e={}))W.call(e,t)&&g(a,t,e[t]);if(h)for(var t of h(e))z.call(e,t)&&g(a,t,e[t]);return a};import{g as C}from"./plagiarizing-answer-DCON8hS9.js";import{c as D,a as F}from"./calculateTableHeight-BjE6OFD1.js";import{p as H,g as P,a as R}from"./common-methods-BWkba4Bo.js";import{d as I,l,P as B,n as L,ao as E,T as M,r as c,o as N,c as T,e as m,b as d,h as v,ac as U,ad as Q,_ as A}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const G=a=>(U("data-v-cf3bd7d5"),a=a(),Q(),a),J={class:"zf-first-box"},K={class:"zf-second-box"},X=G(()=>m("div",{style:{height:"6px",background:"#e0e2e8"}},null,-1)),Y=I({name:"single-plagiarizing",__name:"have-confirmed",setup(a){const e=l(null),t=l(null);l(!1);const u=l({}),y=B({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>H.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>i.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>i.value},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入考试密号",clearable:!0},{label:"考生密号",prop:"username2",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"评分使用",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评分使用",optionData:()=>i.value}]}),i=l([]),s=l({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_order",label:"题号",minWidth:"90px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"task_name",label:"考务评分",minWidth:"120px"},{prop:"region",label:"阅卷评分",minWidth:"160px"},{prop:"work_unit",label:"是否一致",minWidth:"120px"},{prop:"work_unit1",label:"最终评分",minWidth:"120px"},{prop:"role_name",label:"评分使用",minWidth:"120px",formatter:n=>"阅卷系统"}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),b=l([]);let f=null;L(()=>{P(),D(f,e.value,s.value,!1,58),_()}),E(()=>{F(f)});const x=(n,r)=>{n.prop==="project_id"&&(i.value=[],u.value.subject_id&&(u.value.subject_id=null),r&&R(r).then(o=>{i.value=o||[]}))},_=()=>{let{currentPage:n,pageSize:r}=s.value.pageOptions,o=w({current_page:n,page_size:r},u.value);C(o).then(p=>{p.code&&p.code===200||M.error(p.msg)}),b.value=[{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"程序员(基础知识)",paper_name:"—",ques_order:"—",ques_code:"19490982231",task_name:"1",stu_secret_num:"2003939999020001",region:"1",work_unit:"是",work_unit1:"1"},{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"程序员(基础知识)",paper_name:"—",ques_order:"—",ques_code:"19578012112",task_name:"1",stu_secret_num:"2003939999020002",region:"1",work_unit:"是",work_unit1:"1"},{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"程序员(基础知识)",paper_name:"—",ques_order:"—",ques_code:"19533319000",task_name:"1",stu_secret_num:"2003939999020006",region:"1",work_unit:"是",work_unit1:"1"},{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"程序员(基础知识)",paper_name:"—",ques_order:"—",ques_code:"19612348928",task_name:"1",stu_secret_num:"2003939999020041",region:"1",work_unit:"是",work_unit1:"1"}]},k=n=>{s.value.pageOptions.pageSize=n,_()},j=n=>{s.value.pageOptions.currentPage=n,_()};function q(){i.value=[]}return(n,r)=>{const o=c("form-component"),p=c("el-card"),S=c("table-component");return N(),T("div",J,[m("div",K,[d(p,null,{default:v(()=>[m("div",{ref_key:"formDivRef",ref:e},[d(o,{ref_key:"formRef",ref:t,modelValue:u.value,"onUpdate:modelValue":r[0]||(r[0]=O=>u.value=O),"form-options":y,"is-query-btn":!0,onOnchangeFn:x,onQueryDataFn:_,onResetFields:q},null,8,["modelValue","form-options"])],512)]),_:1}),X,d(p,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:v(()=>[d(S,{minHeight:s.value.styleOptions.minHeight,"table-options":s.value,"table-data":b.value,onOnHandleSizeChange:k,onOnHandleCurrentChange:j},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),re=A(Y,[["__scopeId","data-v-cf3bd7d5"]]);export{re as default};
