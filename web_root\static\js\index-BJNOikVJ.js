var G=Object.defineProperty,K=Object.defineProperties;var X=Object.getOwnPropertyDescriptors;var B=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var F=(r,o,e)=>o in r?G(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,H=(r,o)=>{for(var e in o||(o={}))Y.call(o,e)&&F(r,e,o[e]);if(B)for(var e of B(o))Z.call(o,e)&&F(r,e,o[e]);return r},N=(r,o)=>K(r,X(o));var O=(r,o,e)=>new Promise((q,f)=>{var w=i=>{try{v(e.next(i))}catch(_){f(_)}},b=i=>{try{v(e.throw(i))}catch(_){f(_)}},v=i=>i.done?q(i.value):Promise.resolve(i.value).then(w,b);v((e=e.apply(r,o)).next())});import $ from"./addQuestion-SJOqiL-C.js";import ee from"./editDrawer-xuXYNIdf.js";import te from"./aiError-BayNkAGI.js";import{g as ae,d as le,b as se}from"./question-DElFsEXd.js";import{d as oe,i as ne,l as n,P as re,aN as Q,T as g,aV as ue,n as ie,ao as pe,r as y,o as de,c as ce,e as W,b as c,h as j,f as _e,_ as me}from"./index-B63pSD2p.js";import{g as fe}from"./test-paper-management-DjV_45YZ.js";import{p as be,g as ve,a as ge}from"./common-methods-BWkba4Bo.js";import{g as he}from"./base-DyvdloLK.js";import{q as ye,g as qe}from"./rules-form-CST-rV3v.js";import{g as De}from"./marking-task-DrCSMu9U.js";import{a as xe}from"./formal-task-CUOmIYGE.js";import{c as Ce,a as Oe}from"./calculateTableHeight-BjE6OFD1.js";import"./TinymceEditor.vue_vue_type_style_index_0_lang-Dj-6iP6L.js";import"./util-DBFSI-P4.js";import"./op-mark-step-DW83lcNi.js";import"./handleImages-D-nd439N.js";import"./quesNum-CouueI57.js";import"./scoring-rules-BR2vQ7G3.js";import"./convertNumber-CmbNKqvY.js";import"./questionEdit-C5KtQM0G.js";const je=(r,o)=>{r.fields.forEach(e=>{["paper_id","ques_type_code_list"].includes(e.prop)&&(e.isHidden=!0),["business_id_list"].includes(e.prop)&&(e.isHidden=!1)}),o.field.forEach(e=>{["business_type_name"].includes(e.prop)&&(e.isHidden=!1),["ques_type_name"].includes(e.prop)&&(e.isHidden=!0)})},we={class:"zf-first-box"},Ve={class:"zf-second-box"},Se={class:"zf-flex-end"},We=oe({name:"question",__name:"index",setup(r){ne();const o=n(null),e=n(null),q=n(null),f=n(null),w=n(null),b=n([]),v=n([]),i=n([]),_=n([]),D=n({}),R=re({column:3,labelWidth:"110px",itemWidth:"200px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>be.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择所属科目",optionData:()=>b.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>i.value},{label:"试题名称",prop:"ques_name",type:"input",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请输入试题名称"},{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!0,clearable:!0,optionData:()=>ye},{label:"实际题型",prop:"bs_ques_type_name",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!1,clearable:!0,props:{value:"ques_type_name",label:"ques_type_name"},optionData:()=>_.value},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"使用考次",prop:"used_count_list",type:"doubleInput",defaultValue:[null,null],placeholder:"请输入",clearable:!0,width:"93.6px"},{label:"已添加评分标准",prop:"has_mark_point",type:"select",defaultValue:"",placeholder:"请选择是否已添加评分标准",clearable:!0,optionData:()=>[{label:"是",value:!0},{label:"否",value:!1}]}]}),p=n({field:[{prop:"ques_name",label:"试题名称",minWidth:"200px",formatter:t=>t.ques_name?t.ques_name:"-"},{prop:"ques_code",label:"试题编号",minWidth:"190px"},{prop:"small_ques_int",label:"试题序号",minWidth:"120px"},{prop:"ques_desc",label:"试题描述",minWidth:"240px",formatter:t=>t.ques_desc?t.ques_desc:"-"},{prop:"project_name",label:"资格",minWidth:"220px"},{prop:"subject_name",label:"科目",minWidth:"100px"},{prop:"ques_type_name",label:"试题类型",minWidth:"100px"},{prop:"business_type_name",label:"实际题型",minWidth:"100px",isHidden:!0},{prop:"used_count",label:"使用考次",minWidth:"90px"},{prop:"u_name",label:"更新人",minWidth:"100px"},{prop:"updated_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"opera",label:"操作",type:"template",minWidth:"160px",fixed:"right",templateGroup:[{title:()=>Q("question/preview")?"编辑":"",clickBtn:t=>O(this,null,function*(){var l,u;const a={ques_code:t.ques_code},s=yield ae(a);s.code&&s.code===200?((u=(l=s.data)==null?void 0:l.ques_detail)==null?void 0:u.from_tool)==0?f.value.openDrawer(s.data,"编辑"):q.value.openDrawer(t,"1"):g.error(s.msg)})},{title:t=>Q("question/delete")&&t.from_tool==0?"删除":"",clickBtn:t=>O(this,null,function*(){ue.confirm("确定删除该试题吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>O(this,null,function*(){const a={ques_code:t.ques_code},s=yield le(a);s.code&&s.code===200?(g.success(s.msg),m()):g.error(s.msg)})).catch(()=>{})})}]}],styleOptions:{isShowSort:!1,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),x=n([]),k=n([]);let z=null;const V=n(!0);ie(()=>{Ce(z,o.value,p.value,!0),ve(),qe(),m("first")}),pe(()=>{Oe(z)});const E=(t,a)=>{t.prop==="project_id"?(b.value=[],i.value=[],e.value.getCardData("subject_id")&&e.value.setCardData("subject_id",null),e.value.getCardData("paper_id")&&e.value.setCardData("paper_id",null),e.value.getCardData("bs_ques_type_name")&&e.value.setCardData("bs_ques_type_name",null),a&&ge(a).then(s=>{b.value=s||[]}),C()):t.prop==="subject_id"&&(i.value=[],v.value=[],e.value.getCardData("paper_id")&&e.value.setCardData("paper_id",null),e.value.getCardData("bs_ques_type_name")&&e.value.setCardData("bs_ques_type_name",null),a&&(V.value&&A(),P()),C())},P=()=>{let t={project_id:D.value.project_id,subject_id:D.value.subject_id,is_quality:0};he(t).then(a=>{a.code&&a.code===200?v.value=a.data:g.error(a.msg)})},A=()=>{const{project_id:t,subject_id:a}=e.value.getAllCardData();fe({project_id:t,subject_id:a,page_size:-1}).then(l=>{l.code&&l.code===200&&(l.data.data.forEach(u=>{u.label=u.paper_name,u.value=u.paper_id}),i.value=l.data.data)})},C=()=>{const{project_id:t,subject_id:a}=e.value.getAllCardData();let s={project_id:t,subject_id:a};V.value==0?xe(N(H({},s),{is_remove_duplicate:!0})).then(l=>{l.code&&l.code===200?_.value=l.data.data:g.error(l.msg)}):t&&a&&De(s).then(l=>{l.code&&l.code===200&&(_.value=l.data.business_id_list.map((u,h)=>({value:u,label:l.data.business_name_list[h]})))})},m=t=>{let a=JSON.parse(JSON.stringify(e.value.getAllCardData()));a.ques_type_code_list.length||delete a.ques_type_code_list;const[s,l]=a.used_count_list;if(s||l)if(s&&l&&Number(s)>Number(l)){const d=s;e.value.setCardData("used_count_list",[l,s]),a.used_count_list[0]=Number(l),a.used_count_list[1]=Number(d)}else a.used_count_list[0]=Number(s),a.used_count_list[1]=Number(l);else delete a.used_count_list;let{currentPage:u,pageSize:h}=p.value.pageOptions;a.current_page=u,a.page_size=h,se(a).then(d=>{d.code&&d.code===200?(V.value=d.data.is_paper,t==="first"&&d.data.is_paper===0&&je(R,p.value),x.value=d.data.ques_data,p.value.pageOptions.total=d.data.total,t==="first"&&C()):g.warning(d.msg)})},L=t=>{k.value=t},T=t=>{p.value.pageOptions.currentPage=1,p.value.pageOptions.pageSize=t,m()},S=t=>{p.value.pageOptions.currentPage=t,m()},I=n(null);function M(){b.value=[],C()}const J=()=>{f.value.openDrawer({},"创建")};return(t,a)=>{const s=y("form-component"),l=y("el-card"),u=y("el-button"),h=y("Auth"),d=y("table-component");return de(),ce("div",we,[W("div",Ve,[c(l,null,{default:j(()=>[W("div",{ref_key:"formDivRef",ref:o,class:"query-box"},[c(s,{ref_key:"formRef",ref:e,modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=U=>D.value=U),"form-options":R,"is-query-btn":!0,onQueryDataFn:m,onOnchangeFn:E,onResetFields:M},null,8,["modelValue","form-options"])],512)]),_:1}),c(l,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:j(()=>[W("div",Se,[c(h,{value:"question/create-question"},{default:j(()=>[c(u,{type:"primary",onClick:J},{default:j(()=>[_e("创建")]),_:1})]),_:1})]),c(d,{"min-height":p.value.styleOptions.minHeight,"table-options":p.value,"table-data":x.value,onOnHandleSelectionChange:L,onOnHandleSizeChange:T,onOnHandleCurrentChange:S},null,8,["min-height","table-options","table-data"])]),_:1})]),c($,{ref_key:"addQeustionRef",ref:f,onQueryData:m,onHandleCurrentChange:S,tableOptions:p.value,tableData:x.value},null,8,["tableOptions","tableData"]),c(ee,{ref_key:"editDrawerRef",ref:q,onQueryData:m,onHandleCurrentChange:S,tableOptions:p.value,tableData:x.value},null,8,["tableOptions","tableData"]),c(te,{ref_key:"aiErrorDrawerRef",ref:w,ques_id:I.value},null,8,["ques_id"])])}}}),Ze=me(We,[["__scopeId","data-v-e94e2a42"]]);export{Ze as default};
