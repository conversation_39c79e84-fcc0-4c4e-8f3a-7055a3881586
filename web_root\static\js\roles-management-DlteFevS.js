import{aQ as r,aR as t}from"./index-B63pSD2p.js";const s=e=>r.request("post",t("/v1/role/get_role"),{data:e}),u=e=>r.request("post",t("/v1/role/create_role"),{data:e}),l=e=>r.request("post",t("/v1/role/update_role"),{data:e}),a=e=>r.request("post",t("/v1/role/delete_role"),{data:e}),_=e=>r.request("post",t("/v1/user_module/get_role_module"),{data:e}),d=e=>r.request("post",t("/v1/user_module/update_role_module"),{data:e});export{_ as a,l as b,u as c,a as d,s as g,d as u};
