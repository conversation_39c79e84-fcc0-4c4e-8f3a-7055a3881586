import{g as y}from"./marking-paper-CSlVaOqn.js";import{d as k,l as s,r as p,o as z,g as H,h as r,e as t,b as d,f as g,u as M,T as q,ac as I,ad as P,_ as R}from"./index-B63pSD2p.js";const B="/static/jpg/test-R_SJtgMm.jpg",v=l=>(I("data-v-9853d710"),l=l(),P(),l),D={class:"flex"},N={class:"w-[360px]"},j={class:"sample-ques-box"},E={class:"top"},T=["src"],U={class:"parse"},F=v(()=>t("div",null," 考生回答了MVC架构包含Model（模型）、View（视图）和Controller（控制器）三种元素。 Model（模型）：负责管理应用的数据和业务逻辑。 View（视图）：负责展示数据，与用户进行交互。 Controller（控制器）：负责处理用户输入，控制模型和视图的交互。得满分。 ",-1)),J=v(()=>t("div",{class:"marking-score-box"},[t("div",{class:"score"},"6"),t("div",{class:"image"})],-1)),L=k({__name:"sample-ques",setup(l,{expose:f}){const b=s("查看样题"),n=s(!1),m=s(null),a=s({field:[{prop:"role_name",label:"考生密号",minWidth:"120px"},{prop:"ques_type_score",label:"分数",width:"80px"}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),i=s([{role_name:"125266399414787768321"}]),x=()=>{n.value=!0,_()},w=()=>{n.value=!1},_=()=>{const{current_page:o,page_size:c}=a.value.pageOptions;y({current_page:o,page_size:c}).then(e=>{e.code&&e.code===200?(a.value.pageOptions.total=e.data.total,e.data.list.length?(e.data.list.forEach(u=>u.role_name="125266399414787768321"),i.value=e.data.list):i.value=[{role_name:"125266399414787768321"}],m.value.onSetCurrentRow(i.value[0])):q.error(e.msg)})},C=o=>{a.value.pageOptions.pageSize=o,a.value.pageOptions.currentPage=1,_()},S=o=>{a.value.pageOptions.currentPage=o,_()};return f({openDialog:x}),(o,c)=>{const h=p("table-component"),e=p("el-button"),u=p("el-text"),O=p("el-dialog");return z(),H(O,{modelValue:n.value,"onUpdate:modelValue":c[0]||(c[0]=V=>n.value=V),title:b.value,"show-close":!0,width:"50%",style:{"min-width":"700px"},"align-center":"","append-to-body":"",draggable:"","close-on-click-modal":!1,"before-close":w},{default:r(()=>[t("div",D,[t("div",N,[d(h,{ref_key:"tableRef",ref:m,minHeight:a.value.styleOptions.minHeight,"table-options":a.value,"table-data":i.value,onOnHandleSizeChange:C,onOnHandleCurrentChange:S},null,8,["minHeight","table-options","table-data"])]),t("div",j,[t("div",E,[d(e,{style:{cursor:"text"},text:"",bg:""},{default:r(()=>[g("考生答案")]),_:1}),d(u,null,{default:r(()=>[g("当前考生：125266399414787768321")]),_:1})]),t("img",{class:"sample-img-box",src:M(B)},null,8,T),t("div",U,[d(e,{style:{cursor:"text","margin-bottom":"10px"},text:"",bg:""},{default:r(()=>[g("评分解析")]),_:1}),F]),J])])]),_:1},8,["modelValue","title"])}}}),A=R(L,[["__scopeId","data-v-9853d710"]]);export{A as default};
