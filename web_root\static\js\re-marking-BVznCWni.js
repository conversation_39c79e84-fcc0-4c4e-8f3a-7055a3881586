import{aQ as t,aR as s}from"./index-B63pSD2p.js";const a=e=>t.request("get",s("/v1/repeat_mark/get_repeat_task_round"),{data:e}),_=e=>t.request("post",s("/v1/user/get_user"),{data:e}),p=e=>t.request("post",s("/v1/repeat_mark/get_repeat_task_list"),{data:e}),u=e=>t.request("post",s("/v1/repeat_mark/get_stu_list"),{data:e}),o=e=>t.request("post",s("/v1/repeat_mark/create_repeat_mark"),{data:e}),n=e=>t.request("post",s("/v1/repeat_mark/update_task_state"),{data:e}),k=e=>t.request("post",s("/v1/repeat_mark/update_score_threshold"),{data:e}),c=e=>t.request("post",s("/v1/repeat_mark/sample_stu_list"),{data:e}),m=e=>t.request("post",s("/v1/repeat_mark/delete_repeat_task"),{data:e});export{u as a,a as b,o as c,_ as d,n as e,m as f,p as g,c as s,k as u};
