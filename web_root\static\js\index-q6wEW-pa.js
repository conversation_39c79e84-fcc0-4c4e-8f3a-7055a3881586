import{d as y,i as O,l as s,n as w,ao as C,T as S,r as p,o as c,c as _,e as u,b as d,h as r,t as z,u as H,f as j,_ as P}from"./index-B63pSD2p.js";import{t as R}from"./convertNumber-CmbNKqvY.js";import{g as D}from"./re-marking-BVznCWni.js";const N={class:"zf-first-box"},B={key:0},E={key:1},T={class:"task-btn-box"},V={class:"task-btn"},W=y({name:"re-task",__name:"index",setup(q){const h=O();s([{value:1,label:"未开始"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}]);const m=s(null),t=s({field:[{prop:"project_name",label:"所属资格"},{prop:"subject_name",label:"所属科目"},{prop:"round_count",label:"复评轮次",minWidth:"80px",type:"slot"},{prop:"repeat_task_count",label:"已复评量",minWidth:"80px"},{prop:"operation",label:"操作",minWidth:"80px",fixed:"right",type:"slot"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),b=s([]);let o=null;w(()=>{o=new ResizeObserver(e=>{e.forEach(i=>{t.value.styleOptions.minHeight=window.innerHeight-160+"px"})}),o.observe(m.value),l()}),C(()=>{o&&(o.disconnect(),o=null)});const l=()=>{let{currentPage:e,pageSize:i}=t.value.pageOptions;D({current_page:e,page_size:i,task_type:3,is_repeater:1}).then(a=>{a.code&&a.code===200?(b.value=a.data.repeat_tasks,t.value.pageOptions.total=a.data.total):S.error(a.msg)})},f=e=>{t.value.pageOptions.currentPage=1,t.value.pageOptions.pageSize=e,l()},v=e=>{t.value.pageOptions.currentPage=e,l()},k=e=>{h.push({path:"/manual-marking/start-re-marking/index",query:{type:1,repeat_task_id:e.repeat_task_id,subject_id:e.subject_id,project_id:e.project_id}})};return(e,i)=>{const g=p("el-button"),a=p("table-component"),x=p("el-card");return c(),_("div",N,[u("div",{ref_key:"formDivRef",ref:m},null,512),d(x,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:r(()=>[d(a,{minHeight:t.value.styleOptions.minHeight,"table-options":t.value,"table-data":b.value,onOnHandleSizeChange:f,onOnHandleCurrentChange:v},{round_count:r(n=>[n.row.round_count?(c(),_("span",B,"第"+z(H(R)(n.row.round_count))+"轮",1)):(c(),_("span",E))]),operation:r(n=>[u("div",T,[u("span",V,[d(g,{link:"",type:"primary",disabled:n.row.task_state==3||n.row.task_state==4,onClick:M=>k(n.row)},{default:r(()=>[j("开始复评")]),_:2},1032,["disabled","onClick"])])])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})])}}}),U=P(W,[["__scopeId","data-v-f2e249c4"]]);export{U as default};
