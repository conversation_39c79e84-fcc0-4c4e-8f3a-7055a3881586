import{aQ as t,aR as s}from"./index-B63pSD2p.js";const u=a=>t.request("post",s("/v1/manual_task/create_batch_manual_task"),{data:a}),n=a=>t.request("post",s("/v1/manual_task/get_manual_task"),{data:a}),_=a=>t.request("post",s("/v1/manual_task/update_manual_task"),{data:a}),r=a=>t.request("post",s("/v1/manual_task/delete_manual_task"),{data:a}),l=a=>t.request("post",s("/v1/manual_task/check_manual_task_finished"),{data:a}),m=a=>t.request("post",s("/v1/manual_task/terminate_manual_task"),{data:a}),k=a=>t.request("post",s("/v1/manual_task/launch_manual_task"),{data:a},{},!1,30*60*1e3),c=a=>t.request("post",s("/v1/paper/get_business_ques"),{data:a});export{n as a,l as b,u as c,r as d,c as g,k as l,m as t,_ as u};
