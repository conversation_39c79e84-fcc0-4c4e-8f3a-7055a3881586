var ke=Object.defineProperty;var ie=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,De=Object.prototype.propertyIsEnumerable;var de=(i,c,p)=>c in i?ke(i,c,{enumerable:!0,configurable:!0,writable:!0,value:p}):i[c]=p,K=(i,c)=>{for(var p in c||(c={}))xe.call(c,p)&&de(i,p,c[p]);if(ie)for(var p of ie(c))De.call(c,p)&&de(i,p,c[p]);return i};import{_ as Fe}from"./task-setting.vue_vue_type_script_setup_true_lang-DPPL36Ne.js";import we from"./add-task-name-qLvyNeQC.js";import{l as o,d as Se,P as je,n as Ve,ao as Oe,r as j,o as L,g as Te,h as V,e as n,t as He,c as A,b as m,u as W,f as X,y as ce,q as Ee,C as M,T as O,aV as Re,ac as Ie,ad as Ne,_ as Be}from"./index-B63pSD2p.js";import{g as Le,c as Pe}from"./test-paper-management-DjV_45YZ.js";import{g as ze,u as Ae}from"./marking-task-DrCSMu9U.js";import{F as We,E as Me}from"./fullscreen-exit-line-DVwpkItP.js";import{C as Qe}from"./close-line-DgsTxgUT.js";import"./index-P2PNQqmI.js";import"./validate-Dc6ka3px.js";import"./marking-group-select.vue_vue_type_script_setup_true_lang-BEZHpNlH.js";import"./common-methods-BWkba4Bo.js";import"./marking-mode-CLpbbjcA.js";const Q=o({field:[{prop:"ques_order",label:"题号",minWidth:"68px"},{prop:"ques_desc_show",label:"试题",minWidth:"170px"},{prop:"ques_code",label:"试题编号",minWidth:"150px"},{prop:"total_score",label:"试题总分",minWidth:"90px"},{prop:"manual_read_state",label:"分配状态",minWidth:"106px",sortable:!0}],styleOptions:{isShowSort:!0,setCellStyleFn({row:i,column:c}){if(c.label==="分配状态"&&i.manual_read_state==="已分配")return{background:"#FFF8DC"}}},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}}),Je=i=>(Ie("data-v-195725c7"),i=i(),Ne(),i),Ue={class:"bg-[#fff] dark:bg-[#141414]"},$e={class:"flex-bc"},Ge={class:"el-message-box__title"},Ke={class:"flex flex-c"},Xe={class:"zf-dialog-first-box"},Ye={class:"zf-dialog-second-box ques-task-box"},Ze={class:"set-task-box"},ea={class:"ques-select-box"},aa=Je(()=>n("div",{class:"zf-tit-box"},[n("i"),n("span",null,"试题选择")],-1)),sa={class:"flex"},la={key:0},ta={key:1,class:"footer-btn"},ua=Se({__name:"add-task",props:["projectList"],emits:["queryData"],setup(i,{expose:c,emit:p}){const J=p,_e=i,T=o("新增阅卷任务"),P=o(!1),H=o(!1),h=o(!1),g=o("01"),q=o({}),t=o({ques_order:""}),w=o({project_id:null,subject_id:null}),Y=o([]),y=o([]),C=o([]),z=o([]),Z=o({}),r=je({column:3,labelWidth:"80px",rules:{m_read_task_name:[{required:!0,message:"请输入任务名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(a,e,u)=>{if(e.length>100)return u(new Error("任务名称长度不能超过100！"));u()}}],project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],paper_id:[{required:!0,message:"请选择试卷",trigger:["blur","change"]}],ques_type_code:[{required:!0,message:"请选择题型",trigger:["blur","change"]}],ques_order:[{required:!0,message:"请选择分配题号",trigger:["blur","change"]}],business_id:[]},fields:[{label:"试卷",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,width:"250px",optionData:()=>y.value},{label:"试题类型",prop:"ques_type_code",type:"select",defaultValue:"",placeholder:"请选择试题类型",clearable:!0,width:"180px",optionData:()=>C.value},{label:"题型名称",prop:"business_id",type:"select",isHidden:!0,defaultValue:"",placeholder:"请选择题型名称",clearable:!0,width:"250px",optionData:()=>z.value},{label:"分配题号",prop:"ques_order",type:"input",defaultValue:null,placeholder:"请从下列列表中选择",clearable:!0,disabled:!0,width:"250px"},{label:"分配状态",prop:"read_state",type:"select",defaultValue:null,placeholder:"请选择状态",clearable:!0,width:"180px",optionData:()=>[{label:"全部",value:""},{label:"未分配",value:1},{label:"已分配",value:2}]}]}),l=o(null),k=o(null),E=o(null),R=o(null),ee=o(null),ae=o("392px"),x=o([]),d=o({}),U=o([]),v=o(!0);let I=null;Ve(()=>{I=new ResizeObserver(a=>{a.forEach(e=>{R.value&&$()})}),R.value&&I.observe(R.value)});const $=()=>{const a=h.value?100:400;ae.value=window.innerHeight-R.value.offsetHeight-a+"px"};Oe(()=>{I&&(I.disconnect(),I=null)});const pe=(a,e)=>{P.value=!0,g.value=a,setTimeout(()=>{l.value.clearValidateFn()},100),M(()=>{$()}),a==="01"?(T.value="新增阅卷任务",M(()=>{l.value.setCardData("read_state",1)}),Q.value.styleOptions.isShowSelection=!0):(a==="02"||a==="03")&&(Q.value.styleOptions.isShowSelection=!1,M(()=>{l.value.setCardData("read_state","")}),a==="03"?(T.value="阅卷任务详情",r.disabled=!0):T.value="编辑阅卷任务",q.value=e,M(()=>{w.value.subject_id=e.subject_id,w.value.project_id=e.project_id,t.value.project_id=e.project_id,t.value.subject_id=e.subject_id,t.value.ques_order=e.ques_order,t.value.ques_score_list=e.ques_score_list,t.value.total_score=e.total_score,t.value.ques_code=e.ques_code,r.fields.map(u=>{e.hasOwnProperty(u.prop)&&l.value.setCardData(u.prop,e[u.prop])}),ve(e),E.value.setTaskFormDataFn(e,a)}))},N=()=>{P.value=!1,h.value=!1,l.value.resetFieldsFn(),r.disabled=!1,k.value.onSetCurrentRow(),x.value=[],E.value.clearTaskFormFn(),H.value&&(J("queryData"),H.value=!1),t.value={},q.value={},Y.value=[],y.value=[],C.value=[]},ve=a=>{ue(a)},se=a=>{y.value.map(e=>{e.paper_id===a&&(C.value=e.ques_type_code_list.map((u,D)=>({value:u,label:e.ques_type_name_list[D]})))})},fe=(a,e)=>{a.prop==="paper_id"?(G(),e?se(e):C.value=[]):a.prop==="ques_type_code"?(l.value.getCardData("ques_order")&&l.value.setCardData("ques_order",""),l.value.getCardData("business_id")&&l.value.setCardData("business_id",null),z.value=[],d.value={},e?!v.value&&e==="F"?(le(),r.fields.forEach(u=>{u.prop==="business_id"&&(u.isHidden=!1)}),r.rules.business_id=[{required:!0,message:"请选择题型名称",trigger:["blur","change"]}]):(r.fields.forEach(u=>{u.prop==="business_id"&&(u.isHidden||(u.isHidden=!0))}),r.rules.business_id.length&&(r.rules.business_id=[]),B()):x.value=[]):(a.prop==="read_state"||a.prop==="business_id")&&B()},le=()=>{let a=K({business_id_list:y.value[0].business_id_list},w.value);ze(a).then(e=>{e.code&&e.code===200&&(z.value=e.data.business_id_list.map((u,D)=>({value:u,label:e.data.business_name_list[D]})))})},be=(a,e)=>{a.prop==="project_id"?(w.value.project_id=e,te(),G(),e||(Y.value=[],y.value=[],C.value=[]),t.value.project_id=e):a.prop==="subject_id"&&(w.value.subject_id=e,te(),G(),e?ue():(y.value=[],C.value=[]),t.value.subject_id=e)},te=()=>{l.value.getCardData("paper_id")&&l.value.setCardData("paper_id","")},G=()=>{l.value.getCardData("business_id")&&l.value.setCardData("business_id",null),z.value=[],l.value.getCardData("ques_type_code")&&l.value.setCardData("ques_type_code",""),l.value.getCardData("ques_order")&&l.value.setCardData("ques_order",""),x.value=[],k.value.onClearSelection(),d.value={},C.value=[],t.value.ques_order=""},ue=a=>{const{project_id:e,subject_id:u}=a||w.value;Le({project_id:e,subject_id:u,page_size:-1,is_create_task:!0}).then(f=>{if(f.code&&f.code===200){if(v.value=f.data.is_paper,v.value||(Q.value.field.forEach(s=>{s.prop==="ques_order"&&!s.isHidden&&(s.isHidden=!0)}),r.fields.forEach(s=>{s.prop==="ques_order"&&(s.label="试题编号")})),["02","03"].includes(g.value)){if(!v.value){l.value.setCardData("ques_order",t.value.ques_code);const s=l.value.getCardData("ques_type_code");s&&s==="F"&&(r.fields.forEach(_=>{_.prop==="business_id"&&(_.isHidden=!1)}),r.rules.business_id=[{required:!0,message:"请选择题型名称",trigger:["blur","change"]}])}B("edit")}if(f.data.is_paper===!1){const s=f.data.data[0];C.value=s.ques_type_code_list.map((_,S)=>({value:_,label:s.ques_type_name_list[S]})),r.fields.forEach(_=>{_.prop==="paper_id"&&(_.isHidden=!0)}),r.rules.paper_id=[],y.value=f.data.data,["02","03"].includes(g.value)&&(v.value||le())}else if(r.fields.forEach(s=>{s.prop==="paper_id"&&s.isHidden&&(s.isHidden=!1)}),r.rules.paper_id.length||(r.rules.paper_id=[{required:!0,message:"请选择试卷",trigger:["blur","change"]}]),f.data.data.forEach(s=>{s.label=s.paper_name,s.value=s.paper_id}),y.value=f.data.data,g.value==="02"||g.value==="03"){const{paper_id:s}=l.value.getAllCardData();se(s)}}})},B=a=>{let{paper_id:e,ques_type_code:u,read_state:D,business_id:f}=JSON.parse(JSON.stringify(l.value.getAllCardData()));if(v.value&&!e){O.warning("请选择试卷");return}let s=K({paper_id:e,ques_type_code:u,read_state:D,format_by_ques_type:!1,is_paper:v.value},w.value);s.read_state===""&&(s.read_state=null),v.value||(s.business_id=f),Pe(s).then(_=>{var S,F;_.code&&_.code===200&&(x.value=(S=_.data)==null?void 0:S.data,((F=x.value)==null?void 0:F.length)>0&&(x.value.forEach(b=>{var re,ne;b.ques_desc_show=(re=b.ques_desc)==null?void 0:re.text,((ne=Object.keys(d.value))==null?void 0:ne.length)>0&&k.value.onSetCurrentRow(b)}),a&&x.value.forEach(b=>{v.value?b.paper_id===q.value.paper_id&&b.ques_code===q.value.ques_code&&(k.value.onSetCurrentRow(b),d.value=b):b.ques_code===q.value.ques_code&&(k.value.onSetCurrentRow(b),d.value=b,t.value.total_score=b.total_score)})))})},ge=()=>{l.value.formValidate().then(()=>{E.value.getTaskInfoFn().then(a=>{let e=JSON.parse(JSON.stringify(l.value.getAllCardData()));if(e=Object.assign(e,a),v.value||(e.ques_order=null),g.value==="01"){if(!U.value.length){O.warning("至少选择一条数据！");return}ee.value.openDialog(U.value,e);return}Re.confirm(`当前分配的试题为：${d.value.ques_code}，确定保存吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{g.value==="01"||g.value==="02"&&(e.ques_code=d.value.ques_code,e.ques_id=d.value.ques_id,e.m_read_task_id=q.value.m_read_task_id,ye(e))}).catch(()=>{})})}).catch(()=>{O.warning("请按要求填写试题选择！")})},me=a=>{a?a.manual_read_state==="未分配"||a.paper_id===q.value.paper_id&&a.ques_code===q.value.ques_code?(d.value=a,v.value?(l.value.setCardData("ques_order",d.value.ques_order),t.value.ques_order=a.ques_order):(l.value.setCardData("ques_order",d.value.ques_code),t.value.ques_order=a.ques_code),t.value.ques_score_list=a.ques_score_list,t.value.total_score=a.total_score):(k.value.onSetCurrentRow(d.value),t.value.ques_order=d.value.ques_order,t.value.ques_score_list=d.value.ques_score_list,t.value.total_score=d.value.total_score,O.warning("已分配的题目不可再选择！")):(t.value.ques_order="",t.value.ques_score_list=[],t.value.total_score="")},qe=a=>{U.value=a,v.value?(l.value.setCardData("ques_order",a.map(e=>e.ques_order)),t.value.ques_order=a.ques_order):(l.value.setCardData("ques_order",a.map(e=>e.ques_code)),t.value.ques_order=a.ques_code)},he=()=>{E.value.clearTaskNameFn(),k.value.onSetCurrentRow(),B(),l.value.setCardData("ques_order","")},ye=a=>{Ae(a).then(e=>{e.code&&e.code===200?(O.success(e.msg),J("queryData"),N()):O.warning(e.msg)})},oe=()=>{h.value?h.value=!1:h.value=!0,$()},Ce=()=>{H.value?he():(N(),J("queryData"))};return c({openDialog:pe}),(a,e)=>{const u=j("iconify-icon-offline"),D=j("form-component"),f=j("table-component"),s=j("el-checkbox"),_=j("el-button"),S=j("el-dialog");return L(),Te(S,{modelValue:P.value,"onUpdate:modelValue":e[2]||(e[2]=F=>P.value=F),title:T.value,width:"1084px","align-center":"","before-close":N,"close-on-click-modal":!1,"show-close":!1,fullscreen:h.value,"destroy-on-close":"","append-to-body":"",draggable:""},{header:V(()=>[n("div",Ue,[n("div",$e,[n("div",Ge,[n("span",null,He(T.value),1)]),n("div",Ke,[h.value?(L(),A("i",{key:1,class:"cursor-pointer mr-[12px]",onClick:oe},[m(u,{icon:W(Me)},null,8,["icon"])])):(L(),A("i",{key:0,class:"cursor-pointer mr-[12px]",onClick:oe},[m(u,{icon:W(We)},null,8,["icon"])])),n("i",{class:"cursor-pointer text-[20px]",onClick:N},[m(u,{icon:W(Qe)},null,8,["icon"])])])])])]),footer:V(()=>[n("div",sa,[g.value==="01"?(L(),A("div",la,[m(s,{modelValue:H.value,"onUpdate:modelValue":e[1]||(e[1]=F=>H.value=F)},{default:V(()=>[X("连续新增")]),_:1},8,["modelValue"])])):ce("",!0),g.value==="01"||g.value==="02"?(L(),A("div",ta,[m(_,{onClick:N},{default:V(()=>[X("取消")]),_:1}),m(_,{type:"primary",onClick:ge},{default:V(()=>[X("确定")]),_:1})])):ce("",!0)])]),default:V(()=>[n("div",Xe,[n("div",Ye,[n("div",Ze,[m(Fe,{ref_key:"taskSettingRef",ref:E,quesInfo:t.value,taskInfo:q.value,projectList:_e.projectList,onChangeProAndSub:be},null,8,["quesInfo","taskInfo","projectList"])]),n("div",ea,[n("div",{ref_key:"topRef",ref:R},[aa,m(D,{ref_key:"formRef",ref:l,modelValue:Z.value,"onUpdate:modelValue":e[0]||(e[0]=F=>Z.value=F),"form-options":r,"is-query-btn":!1,onOnchangeFn:fe},null,8,["modelValue","form-options"])],512),n("div",{style:Ee({width:h.value?"100%":"680px"})},[m(f,{ref_key:"tableRef",ref:k,class:"table-flex-box",minHeight:ae.value,"table-options":W(Q),"table-data":x.value,onOnHandleCellClick:me,onOnHandleSelectionChange:qe},null,8,["minHeight","table-options","table-data"])],4)])])]),m(we,{ref_key:"addTaskNameRef",ref:ee,onCreateTask:Ce},null,512)]),_:1},8,["modelValue","title","fullscreen"])}}}),qa=Be(ua,[["__scopeId","data-v-195725c7"]]);export{qa as default};
