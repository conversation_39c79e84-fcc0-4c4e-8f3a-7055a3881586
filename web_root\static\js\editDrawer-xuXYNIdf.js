var xe=(q,e,N)=>new Promise((W,w)=>{var O=k=>{try{L(N.next(k))}catch(i){w(i)}},C=k=>{try{L(N.throw(k))}catch(i){w(i)}},L=k=>k.done?W(k.value):Promise.resolve(k.value).then(O,C);L((N=N.apply(q,e)).next())});import{_ as ie}from"./TinymceEditor.vue_vue_type_style_index_0_lang-Dj-6iP6L.js";import Te from"./op-mark-step-DW83lcNi.js";import{a as Ue}from"./convertNumber-CmbNKqvY.js";import{h as Y}from"./handleImages-D-nd439N.js";import{g as Ne}from"./questionEdit-C5KtQM0G.js";import{d as Ve,aS as Me,aO as be,l as g,r as b,o as t,c as o,g as F,h as y,F as x,p as U,u as z,f as E,t as h,y as m,q as we,b as d,e as a,ac as Ce,ad as $e,_ as Ee,G as Oe,P as He,B as Pe,aP as Be,n as Qe,ao as Ae,T as X,aV as Fe,C as Se}from"./index-B63pSD2p.js";import{g as ze,e as De}from"./question-DElFsEXd.js";import{i as We,g as je}from"./quesNum-CouueI57.js";import"./util-DBFSI-P4.js";const B=q=>(Ce("data-v-42d92b7a"),q=q(),$e(),q),Re=["id"],Ie={key:0},Je={key:0},Ke=B(()=>a("span",{class:"text-bold-box whitespace-nowrap"},"答案：",-1)),Ge={key:3,class:"flex"},Xe={key:0,class:"ques-editor-input"},Ye={key:1,class:"ques-editor-input"},Ze={key:1,class:"text-inline-box"},et=B(()=>a("span",{class:"text-bold-box whitespace-nowrap"},"答案：",-1)),tt={key:0},ot={key:1},lt={key:2},st={key:3},nt={key:0,class:"text-inline-box"},at={class:"text-inline-box"},ut=["innerHTML"],rt={key:1,class:"text-inline-box"},dt={class:"text-inline-box"},it=["innerHTML"],ct={key:4},_t={key:0},vt=["innerHTML"],pt={key:1},ht=["innerHTML"],mt={key:1},ft={class:"text-bold-box flex"},yt=B(()=>a("span",null,"答案分组",-1)),qt=B(()=>a("span",null,"：",-1)),gt={key:1},bt={key:2},wt={key:0},kt=B(()=>a("div",{class:"text-bold-box mb-[2px]"},"解析：",-1)),xt={class:"ques-editor-input"},Vt={key:1,class:"flex"},Et=B(()=>a("div",{class:"text-bold-box"},"解析：",-1)),St=["innerHTML"],Dt={key:1},Mt={key:3,class:"mt-[10px] mb-[10px]"},Ct=B(()=>a("div",{class:"text-bold-box"},"评分规则：",-1)),$t=["innerHTML"],Lt={key:4},Tt=B(()=>a("span",{class:"text-bold-box"},"权    重：",-1)),Ut={key:0},Nt={key:5,class:"mt-[10px]"},Ot={class:"text-bold-box"},Ht={class:"point-form"},Pt={class:"form-point-box"},Bt={key:0,style:{color:"red","font-size":"12px"}},Qt=B(()=>a("span",{class:"text-bold-box"},"评分标准：",-1)),At={key:0},Ft={key:0},zt={class:"small-ques-type"},Wt={key:6,class:"mt-1"},jt=B(()=>a("div",{class:"mark-step-text text-bold-box"},"评分步骤：",-1)),Rt=Ve({name:"question-answer",__name:"index",props:Me({isShowQuesCode:{type:Boolean,default:!0},isEdit:{type:Boolean,default:!1}},{questionDesc:{},questionDescModifiers:{}}),emits:["update:questionDesc"],setup(q){const e=be(q,"questionDesc"),N=["①","②","③","④","⑤","⑥","⑦","⑧","⑨","⑩","⑪","⑫","⑬","⑭","⑮","⑯","⑰","⑱","⑲","⑳","㉑","㉒","㉓","㉔","㉕","㉖","㉗","㉘","㉙","㉚","㉛","㉜","㉝","㉞","㉟","㊱","㊲","㊳","㊴","㊵","㊶","㊷","㊸","㊹","㊺","㊻","㊼","㊽","㊾","㊿"],W=g(!1),w=i=>{i.stopPropagation()},O=(i,u)=>{if(u==="01")e.value.ques_choices.forEach(f=>{f.code===i&&(e.value.standard_answer=[f.options.slice(0,1)],e.value.standard_answer_html=[f.options.slice(0,1)])});else if(u==="02")e.value.ques_choices.forEach(f=>{f.code===i&&(e.value.standard_answer=[f.code],e.value.standard_answer_html=[f.code])});else if(u==="03"){let f=[];e.value.ques_choices.forEach(S=>{i.forEach(V=>{S.code===V&&f.push(S.options.slice(0,1))})}),e.value.standard_answer=f,e.value.standard_answer_html=f}},C=()=>{let i={point:"",score:1};e.value.ques_mark_point||(e.value.ques_mark_point=[]),e.value.ques_mark_point.push(i),k(e.value)},L=i=>{e.value.ques_mark_point.splice(i,1),k(e.value)},k=i=>{var S;const u=Number(i.ques_score);let f=0;(S=i.ques_mark_point)!=null&&S.length&&i.ques_mark_point.forEach(V=>{f+=V.score}),i.ques_type_code=="E"?f<u?i.pointScoreError=!0:i.pointScoreError=!1:f!=u?i.pointScoreError=!0:i.pointScoreError=!1};return(i,u)=>{var pe,he,te,ae,I,$,R,me,ue,fe,re,ye,J,qe,oe;const f=b("el-radio"),S=b("el-radio-group"),V=b("el-checkbox"),se=b("el-checkbox-group"),H=b("InfoFilled"),P=b("el-icon"),j=b("el-tooltip"),Z=b("el-input"),ce=b("Plus"),_e=b("el-text"),ne=b("el-input-number"),ee=b("Delete"),ve=b("el-tag"),r=b("el-card");return t(),o("div",{class:"readOnly-box",id:"section"+e.value.level+e.value.small_ques_num},[e.value.ques_type_code!=="F"&&e.value.ques_type_code!=="G"?(t(),o("div",Ie,[q.isEdit?(t(),o("div",Je,[Ke,((pe=e.value)==null?void 0:pe.ques_type_code)==="A"?(t(),F(S,{key:0,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":u[0]||(u[0]=l=>e.value.standard_answer[0]=l),onChange:u[1]||(u[1]=(...l)=>O(...l,e.value.ques_type_code==="A"?"01":"02"))},{default:y(()=>[(t(!0),o(x,null,U(e.value.ques_choices,(l,_)=>(t(),F(f,{value:z(Ue)("A",_+1)},{default:y(()=>[E(h(e.value.ques_type_code==="A"?l.options.substring(0,1):l.options.substring(0,2)),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])):m("",!0),((he=e.value)==null?void 0:he.ques_type_code)==="B"?(t(),F(S,{key:1,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":u[2]||(u[2]=l=>e.value.standard_answer[0]=l),onChange:u[3]||(u[3]=(...l)=>O(...l,e.value.ques_type_code==="A"?"01":"02"))},{default:y(()=>[(t(!0),o(x,null,U(i.judgmentList,(l,_)=>(t(),F(f,{value:i.judgmentArr[_]},{default:y(()=>[E(h(l),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])):((te=e.value)==null?void 0:te.ques_type_code)==="C"?(t(),F(se,{key:2,modelValue:e.value.standard_choices_code,"onUpdate:modelValue":u[4]||(u[4]=l=>e.value.standard_choices_code=l),onChange:u[5]||(u[5]=(...l)=>O(...l,"03"))},{default:y(()=>[(t(!0),o(x,null,U(e.value.ques_choices,l=>(t(),F(V,{value:l.code,label:l.options.substring(0,1)},null,8,["value","label"]))),256))]),_:1},8,["modelValue"])):e.value.ques_type_code==="D"?(t(),o("div",Ge,[((ae=e.value.standard_answer_html)==null?void 0:ae.length)>0?(t(!0),o(x,{key:0},U(e.value.standard_answer_html,(l,_)=>(t(),o("div",{class:"ques-editor-input filling-input-box",style:we({width:`${1/e.value.standard_answer_html.length*100-.2}%`})},[d(ie,{modelValue:e.value.standard_answer_html[_],"onUpdate:modelValue":D=>e.value.standard_answer_html[_]=D,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])],4))),256)):(t(!0),o(x,{key:1},U(e.value.standard_answer,(l,_)=>(t(),o("div",{class:"ques-editor-input filling-input-box",style:we({width:`${1/e.value.standard_answer.length*100-.2}%`})},[d(ie,{modelValue:e.value.standard_answer[_],"onUpdate:modelValue":D=>e.value.standard_answer[_]=D,toolBarPosition:"bottom"},null,8,["modelValue","onUpdate:modelValue"])],4))),256))])):e.value.ques_type_code==="E"?(t(),o(x,{key:4},[((I=e.value.standard_answer_html)==null?void 0:I.length)>0?(t(),o("div",Xe,[d(ie,{modelValue:e.value.standard_answer_html[0],"onUpdate:modelValue":u[6]||(u[6]=l=>e.value.standard_answer_html[0]=l),toolBarPosition:"bottom"},null,8,["modelValue"])])):(t(),o("div",Ye,[d(ie,{modelValue:e.value.standard_answer[0],"onUpdate:modelValue":u[7]||(u[7]=l=>e.value.standard_answer[0]=l),toolBarPosition:"bottom"},null,8,["modelValue"])]))],64)):m("",!0)])):(t(),o("p",Ze,[et,e.value.ques_type_code==="A"?(t(),o("span",tt,h(e.value.standard_answer[0]),1)):m("",!0),e.value.ques_type_code==="B"?(t(),o("span",ot,h(e.value.standard_answer[0]==="1"?"正确":"错误"),1)):m("",!0),e.value.ques_type_code==="C"?(t(),o("span",lt,h(e.value.standard_answer.join("")),1)):m("",!0),e.value.ques_type_code==="D"?(t(),o("span",st,[(($=e.value.standard_answer_html)==null?void 0:$.length)>0?(t(),o("span",nt,[(t(!0),o(x,null,U(e.value.standard_answer_html,(l,_)=>(t(),o("span",at,[E(h(`(${_+1})`),1),a("span",{innerHTML:l},null,8,ut)]))),256))])):(t(),o("span",rt,[(t(!0),o(x,null,U(e.value.standard_answer,(l,_)=>(t(),o("span",dt,[E(h(`(${_+1})`),1),a("span",{innerHTML:l},null,8,it)]))),256))]))])):m("",!0),e.value.ques_type_code==="E"?(t(),o("span",ct,[((R=e.value.standard_answer_html)==null?void 0:R.length)>0?(t(),o("span",_t,[a("span",{innerHTML:z(Y)(e.value.standard_answer_html[0])},null,8,vt)])):(t(),o("span",pt,[a("span",{innerHTML:z(Y)(e.value.standard_answer[0])},null,8,ht)]))])):m("",!0)]))])):m("",!0),e.value.ques_type_code==="D"&&e.value.level<=2||e.value.level==2&&e.value.children.length>0?(t(),o("div",mt,[a("div",ft,[yt,d(j,{content:z(Ne)},{default:y(()=>[d(P,{class:"mt-[3px]"},{default:y(()=>[d(H)]),_:1})]),_:1},8,["content"]),qt]),q.isEdit?(t(),F(Z,{key:0,type:"text",modelValue:e.value.d_out_of_order_group,"onUpdate:modelValue":u[8]||(u[8]=l=>e.value.d_out_of_order_group=l),placeholder:"请输入答案分组",autosize:"",onKeydown:w},null,8,["modelValue"])):(t(),o("span",gt,h(e.value.d_out_of_order_group),1))])):m("",!0),e.value.ques_type_code!=="F"&&e.value.ques_type_code!=="G"&&e.value.level<=2?(t(),o("div",bt,[q.isEdit?(t(),o("div",wt,[kt,a("div",xt,[d(ie,{ref:"editorRef",modelValue:e.value.standard_parse,"onUpdate:modelValue":u[9]||(u[9]=l=>e.value.standard_parse=l),toolBarPosition:"bottom"},null,8,["modelValue"])])])):(t(),o("div",Vt,[Et,e.value.standard_parse!==null?(t(),o("div",{key:0,innerHTML:z(Y)(e.value.standard_parse)},null,8,St)):(t(),o("div",Dt,"无"))]))])):m("",!0),e.value.level==3||e.value.level==2||e.value.level==1&&e.value.ques_type_code!="A"&&e.value.ques_type_code!="B"&&e.value.ques_type_code!="C"?(t(),o("div",Mt,[Ct,q.isEdit?(t(),F(Z,{key:0,type:"textarea",modelValue:e.value.e_mark_rule,"onUpdate:modelValue":u[10]||(u[10]=l=>e.value.e_mark_rule=l),placeholder:"请输入评分规则",autosize:"",onKeydown:w},null,8,["modelValue"])):(t(),o(x,{key:1},[e.value.e_mark_rule?(t(),o("div",{key:0,innerHTML:z(Y)(e.value.e_mark_rule.replace(/\n/g,"<br/>"))},null,8,$t)):m("",!0)],64))])):m("",!0),e.value.ques_type_code==="D"&&((ue=(me=e.value)==null?void 0:me.weight)==null?void 0:ue.length)>0&&e.value.level<=2?(t(),o("p",Lt,[Tt,e.value.weight?(t(),o("span",Ut,h((fe=e.value.weight[0])==null?void 0:fe.join(":"))+"（总权重："+h((re=e.value.weight[1])==null?void 0:re.toString())+"）",1)):m("",!0)])):m("",!0),e.value.level==3||e.value.level==2&&((ye=e.value.children)==null?void 0:ye.length)==0||e.value.level==1&&e.value.ques_type_code!="A"&&e.value.ques_type_code!="B"&&e.value.ques_type_code!="C"?(t(),o("div",Nt,[q.isEdit?(t(),o(x,{key:0},[a("div",null,[a("div",Ot,[E(" 评分标准："),d(_e,{type:"primary",style:{cursor:"pointer"},onClick:u[11]||(u[11]=l=>C())},{default:y(()=>[d(P,{class:"point-icon-box-add"},{default:y(()=>[d(ce)]),_:1})]),_:1})]),a("div",Ht,[(t(!0),o(x,null,U(e.value.ques_mark_point,(l,_)=>(t(),o("div",Pt,[a("div",null,h(_+1)+"．",1),d(Z,{type:"textarea",modelValue:l.point,"onUpdate:modelValue":D=>l.point=D,class:"input-margin",placeholder:"请输入评分标准",autosize:"",onKeydown:w},null,8,["modelValue","onUpdate:modelValue"]),d(ne,{min:.1,modelValue:l.score,"onUpdate:modelValue":D=>l.score=D,class:"input-margin",style:{width:"190px"},onKeydown:w,onChange:u[12]||(u[12]=D=>k(e.value))},null,8,["modelValue","onUpdate:modelValue"]),E(" 分 "),d(P,{class:"point-icon-box point-del",onClick:D=>L(_)},{default:y(()=>[d(ee)]),_:2},1032,["onClick"])]))),256))])]),e.value.pointScoreError?(t(),o("span",Bt,"评分标准总分数需"+h(e.value.ques_type_code=="E"?"大于":"")+"等于分值："+h(e.value.ques_score),1)):m("",!0)],64)):(t(),o(x,{key:1},[a("p",null,[Qt,((J=e.value.ques_mark_point)==null?void 0:J.length)===0?(t(),o("span",At,"无")):m("",!0)]),((qe=e.value.ques_mark_point)==null?void 0:qe.length)>0?(t(),o("ul",Ft,[(t(!0),o(x,null,U(e.value.ques_mark_point,(l,_)=>(t(),o("li",null,h(N[_])+" "+h(l.point)+"("+h(l.score)+"分) ",1))),256))])):m("",!0)],64))])):m("",!0),a("div",null,[(t(!0),o(x,null,U((oe=e.value)==null?void 0:oe.children,(l,_)=>(t(),o("div",null,[d(r,{style:{"margin-bottom":"12px",overflow:"inherit"}},{header:y(()=>[a("div",zt,[a("div",null,[E(h(e.value.children[_].ques_order_new)+" ",1),d(ve,{type:"primary"},{default:y(()=>[E(h(e.value.children[_].ques_type_name),1)]),_:2},1024)]),a("div",null,"分值："+h(e.value.children[_].ques_score),1)])]),default:y(()=>[d(Le,{questionDesc:e.value.children[_],"onUpdate:questionDesc":D=>e.value.children[_]=D,isEdit:q.isEdit,isShowQuesCode:!1},null,8,["questionDesc","onUpdate:questionDesc","isEdit"])]),_:2},1024)]))),256))]),e.value.ques_type_code==="G"?(t(),o("div",Wt,[jt,d(Te,{opStepList:e.value.op_step_list,quesDetail:e.value,isMark:W.value},null,8,["opStepList","quesDetail","isMark"])])):m("",!0)],8,Re)}}}),Le=Ee(Rt,[["__scopeId","data-v-42d92b7a"]]),It={key:0,class:"readOnly-box"},Jt={class:"flex mt-[12px] mb-[10px]",style:{"justify-content":"space-between","align-items":"center"}},Kt={key:0},Gt={key:0,class:"text-inline-box"},Xt=["innerHTML"],Yt={key:1,class:"text-inline-box"},Zt=["innerHTML"],eo={key:2,class:""},to={class:"text-inline-box"},oo={key:0,class:"flex-c"},lo=["innerHTML"],so={key:1,class:"flex-c",style:{"min-height":"32px"}},no=["innerHTML"],ao={key:0,class:"border-b-[1px] border-dashed border-[#dcdfe6] m-[5px]"},uo=Ve({name:"ques-content",__name:"index",props:{questionDesc:{},questionDescModifiers:{}},emits:["update:questionDesc"],setup(q){const e=be(q,"questionDesc"),N=w=>{if(w==null)return"";const O=w.indexOf("（");return O!==-1?w.slice(O,w.length):w+"．"},W=w=>w.slice(2);return(w,O)=>{var k,i,u,f,S,V,se;const C=b("el-tag"),L=b("ques-content");return e.value.ques_desc?(t(),o("div",It,[a("div",Jt,[d(C,{type:"primary"},{default:y(()=>[E(h(e.value.ques_type_name),1)]),_:1}),e.value.ques_score!=null?(t(),o("div",Kt," 分值："+h(e.value.ques_score),1)):m("",!0)]),(i=(k=e.value)==null?void 0:k.ques_desc)!=null&&i.html?(t(),o("div",Gt,[E(h(N((u=e.value)==null?void 0:u.ques_order_new))+" ",1),a("span",{style:{flex:"1"},innerHTML:z(Y)(e.value.ques_desc.html)},null,8,Xt)])):(t(),o("div",Yt,[E(h(N((f=e.value)==null?void 0:f.ques_order_new))+" ",1),a("span",{style:{flex:"1"},innerHTML:(V=(S=e.value)==null?void 0:S.ques_desc)==null?void 0:V.text},null,8,Zt)])),e.value.ques_type_code==="A"||e.value.ques_type_code==="C"?(t(),o("div",eo,[(t(!0),o(x,null,U(e.value.ques_choices,H=>(t(),o("div",to,[H.optionShow?(t(),o("div",oo,[a("span",null,h(H.options.substring(0,2)),1),a("span",{innerHTML:z(Y)(H.optionShow)},null,8,lo)])):(t(),o("div",so,[a("span",null,h(H.options.substring(0,2)),1),a("span",{innerHTML:W(z(Y)(H.html))},null,8,no)]))]))),256))])):m("",!0),(t(!0),o(x,null,U((se=e.value)==null?void 0:se.children,(H,P)=>(t(),o("div",null,[H.ques_desc?(t(),o("div",ao)):m("",!0),d(L,{questionDesc:e.value.children[P],"onUpdate:questionDesc":j=>e.value.children[P]=j},null,8,["questionDesc","onUpdate:questionDesc"])]))),256))])):m("",!0)}}}),ro=Ee(uo,[["__scopeId","data-v-643b53e5"]]),io=q=>(Ce("data-v-7eb94a70"),q=q(),$e(),q),co={class:"ques-box every-task-box dark:!bg-black eye-box"},_o={class:"flex"},vo={ref:"formDivRef",class:"query-box"},po={key:0,style:{width:"140px","padding-left":"12px"}},ho=io(()=>a("div",{class:"title"},"试题导航",-1)),mo={class:"btn"},fo=Ve({__name:"editDrawer",props:{tableOptions:{},tableOptionsModifiers:{},tableData:{},tableDataModifiers:{}},emits:Me(["queryData","handleCurrentChange"],["update:tableOptions","update:tableData"]),setup(q,{expose:e,emit:N}){let W=Oe().smallQuesNumStyle;const w=N,O=be(q,"tableOptions"),C=be(q,"tableData"),L=g(!1),k=g(!1),i=g(null),u=g("1");g({});const f=He({column:3,labelWidth:"80px",itemWidth:"180px",rules:{ques_name:[{required:!0,message:"请输入试题名称",trigger:["blur","change"]}]},fields:[{label:"试题名称",prop:"ques_name",type:"input",defaultValue:"",placeholder:"请输入试题名称",clearable:!0},{label:"试题序号",prop:"small_ques_int",type:"input",defaultValue:"",disabled:!0,placeholder:"请输入试题序号",clearable:!0}]});g([]);const S=g(""),V=g({}),H={children:"children",label:"ques_order_new",class:(s,n)=>s.pointScoreError?"is-error":null},P=g(!1),j=g(null),Z=g(0),ce=g(0),_e=g(0),ne=g(window.innerWidth),ee=g(ne.value/2),ve=g(ne.value/2),r=g({}),pe=(s,n)=>{L.value=!0,u.value=n.toString(),window.addEventListener("keydown",re),R(s.ques_code)},he=()=>{k.value=!0},te=()=>{L.value=!1,r.value={},window.removeEventListener("keydown",re)},ae=g([]);Pe(()=>r.value,s=>{var n;ae.value=Be.cloneDeep(s.children),(n=ae.value)==null||n.forEach(c=>{var p;((p=c.children)==null?void 0:p.length)==1&&(c.children=[])})},{immediate:!0,deep:!0});const I=g({}),$=g(""),R=s=>{r.value={},I.value={},ze({ques_code:s}).then(c=>{var p,K,T,Q,v,M,G,le;if(c.code&&c.code===200){let ge=((p=c.data)==null?void 0:p.ques_detail)||{};$.value=ge.ques_code;const ke=je(W);if(We((K=c.data)==null?void 0:K.ques_detail.children),c.data.ques_detail.level=1,r.value=((T=c.data)==null?void 0:T.ques_detail)||{},r.value.ques_name=(Q=c.data)==null?void 0:Q.ques_detail.knowledge_show,r.value.children&&r.value.children.length>0){if(W!=0)for(let A in r.value.children)r.value.children[A].ques_order_new=ke[A],(M=(v=r.value.children[A])==null?void 0:v.children)!=null&&M.length&&r.value.children[A].children.forEach(de=>{de.ques_order_new=de.small_ques_num.toString()});else for(let A in r.value.children)r.value.children[A].ques_order_new=r.value.children[A].small_ques_num.toString(),(le=(G=r.value.children[A])==null?void 0:G.children)!=null&&le.length&&r.value.children[A].children.forEach(de=>{de.ques_order_new=de.small_ques_num.toString()});S.value=r.value.children[0].ques_id,V.value=r.value.children[0],I.value=JSON.parse(JSON.stringify(r.value))}else I.value=JSON.parse(JSON.stringify(r.value)),V.value=r.value}else X.warning(c.msg)})},me=(s,n)=>{s.level==3?V.value=n.parent.data:V.value=s;let c=s.small_ques_num;setTimeout(()=>{document.body.querySelector("#section"+s.level+c).scrollIntoView({behavior:"smooth"})},0)},ue=()=>{var c;let s=[i.value.formValidate()],n=!0;return(c=r.value.children)==null||c.forEach((p,K)=>{var T,Q;if((T=p==null?void 0:p.children)!=null&&T.length)p.children.forEach((v,M)=>{var ge;const G=Number(v.ques_score);let le=0;(ge=v.ques_mark_point)!=null&&ge.length&&v.ques_mark_point.forEach(ke=>{le+=ke.score}),v.ques_type_code=="E"?le<G?(v.pointScoreError=!0,n=!1):v.pointScoreError=!1:le!=G?(v.pointScoreError=!0,n=!1):v.pointScoreError=!1});else{const v=Number(p.ques_score);let M=0;(Q=p.ques_mark_point)!=null&&Q.length&&p.ques_mark_point.forEach(G=>{M+=G.score}),p.ques_type_code=="E"?M<v?(p.pointScoreError=!0,n=!1):p.pointScoreError=!1:M!=v?(p.pointScoreError=!0,n=!1):p.pointScoreError=!1}}),n?s.push(Promise.resolve(!0)):s.push(Promise.reject(!1)),Promise.all(s)},fe=()=>{ue().then(()=>{De(r.value).then(s=>{s.code&&s.code===200?(X.success(s.msg),w("queryData"),te()):X.warning(s.msg)})}).catch(s=>{})},re=s=>{s.stopPropagation(),ye()||(s.code==="ArrowUp"||s.code==="ArrowLeft"?J("pre"):(s.code==="ArrowDown"||s.code==="ArrowRight")&&J("next"))},ye=()=>{var s,n;return!!((s=window.tinymce)!=null&&s.activeEditor)&&((n=window.tinymce.activeEditor)==null?void 0:n.hasFocus())},J=s=>xe(this,null,function*(){JSON.stringify(I.value)!=JSON.stringify(r.value)?Fe.confirm("试题内容变动，是否保存？","提示",{showClose:!1,confirmButtonText:"保存后前往下一题",cancelButtonText:`${s==="next"?"下一题":"上一题"}`,type:"Info"}).then(()=>{qe(s)}).catch(()=>{oe(s)}):oe(s)}),qe=s=>{ue().then(()=>{De(r.value).then(n=>{n.code&&n.code===200?(X.success(n.msg),oe(s)):X.warning(n.msg)})})},oe=s=>{const{currentPage:n,pageSize:c,total:p}=O.value.pageOptions,K=C.value,T=Math.ceil(p/c);K.forEach((Q,v)=>xe(this,null,function*(){Q.ques_code===$.value&&(s==="pre"?v===0?n===1?X.warning("已经是第一题了！"):(w("handleCurrentChange",n-1),setTimeout(()=>{$.value=C.value[C.value.length-1].ques_code,R($.value)},500)):Se(()=>{$.value=C.value[v-1].ques_code,R($.value)}):s==="next"&&(v===C.value.length-1?n===T?X.warning("已经是最后一题了！"):(w("handleCurrentChange",n+1),setTimeout(()=>{$.value=C.value[0].ques_code,R($.value)},500)):Se(()=>{$.value=C.value[v+1].ques_code,R($.value)})))}))},l=s=>{P.value=!0,j.value=s,Z.value=event.clientX,ce.value=ee.value,_e.value=ve.value,document.addEventListener("mousemove",_),document.addEventListener("mouseup",D),document.body.style.cursor="col-resize",document.body.style.userSelect="none"},_=s=>{if(!P.value)return;const n=s.clientX-Z.value;if(j.value==="left"){const c=ce.value+n;c>=310&&c<=ne.value-500&&(ee.value=c)}else if(j.value==="right"){const c=_e.value-n;c>=200&&c<=500&&(ve.value=c)}},D=()=>{P.value=!1,j.value=null,document.removeEventListener("mousemove",_),document.removeEventListener("mouseup",D),document.body.style.cursor="",document.body.style.userSelect=""};return Qe(()=>{}),Ae(()=>{}),e({openDrawer:pe}),(s,n)=>{const c=b("form-component"),p=b("el-scrollbar"),K=b("el-tree"),T=b("el-button"),Q=b("el-dialog");return t(),o("div",null,[d(Q,{modelValue:L.value,"onUpdate:modelValue":n[6]||(n[6]=v=>L.value=v),title:"编辑","before-close":te,fullscreen:"","destroy-on-close":"","close-on-click-modal":!1,onOpen:he},{footer:y(()=>[a("div",mo,[d(T,{onClick:n[4]||(n[4]=v=>J("pre"))},{default:y(()=>[E("上一题")]),_:1}),d(T,{onClick:n[5]||(n[5]=v=>J("next"))},{default:y(()=>[E("下一题")]),_:1}),d(T,{onClick:te},{default:y(()=>[E("取消")]),_:1}),d(T,{type:"primary",onClick:fe},{default:y(()=>[E("保存")]),_:1})])]),default:y(()=>{var v;return[a("div",co,[a("div",_o,[a("div",{style:we({width:`${ee.value}px`})},[d(p,{always:"",height:"85vh",style:{"padding-right":"22px"}},{default:y(()=>[a("div",vo,[d(c,{ref_key:"formRef",ref:i,modelValue:r.value,"onUpdate:modelValue":n[0]||(n[0]=M=>r.value=M),"form-options":f,"is-query-btn":!1},null,8,["modelValue","form-options"])],512),d(ro,{questionDesc:r.value,"onUpdate:questionDesc":n[1]||(n[1]=M=>r.value=M)},null,8,["questionDesc"])]),_:1})],4),a("div",{class:"marking-line dark:!bg-black",onMousedown:n[2]||(n[2]=M=>l("left"))},null,32),a("div",{class:"flex",style:we({width:`calc(100% - ${ee.value}px)`})},[((v=r.value)==null?void 0:v.ques_type_code)==="F"?(t(),o("div",po,[d(p,{always:"","max-height":"85vh",style:{"padding-right":"12px"}},{default:y(()=>[ho,d(K,{data:r.value.children,"node-key":"ques_id","default-expand-all":"","highlight-current":"",props:H,"current-node-key":S.value,"expand-on-click-node":!1,onNodeClick:me},null,8,["data","current-node-key"])]),_:1})])):m("",!0),d(p,{always:"","max-height":"85vh",style:{flex:"1",padding:"0px 22px 12px"}},{default:y(()=>[k.value&&r.value&&Object.keys(r.value).length?(t(),F(Le,{key:0,questionDesc:V.value,"onUpdate:questionDesc":n[3]||(n[3]=M=>V.value=M),isEdit:!0,isShowQuesCode:!1},null,8,["questionDesc"])):m("",!0)]),_:1})],4)])])]}),_:1},8,["modelValue"])])}}}),Do=Ee(fo,[["__scopeId","data-v-7eb94a70"]]);export{Do as default};
