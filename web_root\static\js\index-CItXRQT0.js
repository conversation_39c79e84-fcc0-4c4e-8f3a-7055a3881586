var E=Object.defineProperty,Q=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var V=(t,l,o)=>l in t?E(t,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[l]=o,y=(t,l)=>{for(var o in l||(l={}))C.call(l,o)&&V(t,o,l[o]);if(x)for(var o of x(l))D.call(l,o)&&V(t,o,l[o]);return t},F=(t,l)=>Q(t,A(l));var R=(t,l)=>{var o={};for(var e in t)C.call(t,e)&&l.indexOf(e)<0&&(o[e]=t[e]);if(t!=null&&x)for(var e of x(t))l.indexOf(e)<0&&D.call(t,e)&&(o[e]=t[e]);return o};var O=(t,l,o)=>new Promise((e,v)=>{var m=s=>{try{p(o.next(s))}catch(f){v(f)}},_=s=>{try{p(o.throw(s))}catch(f){v(f)}},p=s=>s.done?e(s.value):Promise.resolve(s.value).then(m,_);p((o=o.apply(t,l)).next())});import{aC as I,aQ as k,aR as H,d as $,l as d,P as G,n as J,ao as K,r as h,o as X,c as Y,e as j,b,h as w,f as Z,aV as ee,T as S,C as te,_ as ae}from"./index-B63pSD2p.js";import{c as oe,a as ne}from"./calculateTableHeight-BjE6OFD1.js";import{p as le,g as re,a as se}from"./common-methods-BWkba4Bo.js";import{d as ie}from"./downloadRequest-CdE2PBjt.js";import{d as W}from"./validate-Dc6ka3px.js";import"./test-paper-management-DjV_45YZ.js";I();const ue=t=>k.request("post",H("/v1/user/get_user"),{data:y({},t)}),pe=t=>k.request("post",H("/v1/survey_monitor/quality_monitor"),{data:t}).then(l=>l),ce={class:"zf-first-box quality-monitor"},de={class:"zf-second-box"},me={class:"zf-flex-end"},_e={class:"task-btn-box"},fe=$({name:"quality-monitor",__name:"index",setup(t){const l=d(null),o=d(null),e=d({}),v=G({labelWidth:"68px",itemWidth:"160px",rules:{diff_count:[{trigger:["blur"],validator:(r,n,a)=>{if((!n[0]||W(n[0]))&&(!n[1]||W(n[1])))a();else return a(new Error("请输入数字！"))}},{trigger:["blur"],validator:(r,n,a)=>{if(!n[0]&&!n[1]||n[0]&&n[1])a();else return a(new Error("请输入完整的起止区间！"))}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>le.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>_.value},{label:"抽查人",prop:"name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评阅员",optionData:()=>p.value},{label:"通过率",prop:"diff_count",type:"doubleInput",defaultValue:[null,null],placeholder:"请输入",clearable:!0,labelWidth:55,unit:"%",isNomal:!0,width:"80px"}]}),m=()=>O(this,null,function*(){var u;const n=((u=(yield ue({page_size:"99999",user_type:1,system_user_type:2,role_id:"3"})).data)==null?void 0:u.data)||[];let a=n;e.value.project_id&&(a=n.filter(i=>i.project_id_list.includes(e.value.project_id))),e.value.subject_id&&(a=n.filter(i=>i.project_id_list.includes(e.value.project_id)&&i.subject_id_list.includes(e.value.subject_id)));const c=new Map(a.map(i=>[i.name,i]));p.value=[...c.values()].map(i=>({label:i.name,value:i.name}))}),_=d([]),p=d([]),s=d({field:[{prop:"project_name",label:"资格",minWidth:"180px"},{prop:"subject_name",label:"科目",minWidth:"220px"},{prop:"knowledge_show",label:"试题名称",minWidth:"140px"},{prop:"quality_user_name",label:"抽查人",minWidth:"90px"},{prop:"spot_check_count",label:"抽查量",minWidth:"90px"},{prop:"return_count",label:"返回重评数量",minWidth:"110px"},{prop:"change_count",label:"重评修改分数量",minWidth:"130px"},{prop:"pass_count",label:"通过质检量",minWidth:"100px"},{prop:"pass_rate",label:"质检通过率",minWidth:"100px",formatter:r=>r.pass_rate?`${r.pass_rate.toFixed(1)}%`:"-%"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),f=d([]);let q=null;J(()=>{re(),m(),oe(q,l.value,s.value),g()}),K(()=>{ne(q)});const M=(r,n)=>O(this,null,function*(){if(r.prop==="project_id"){if(_.value=[],p.value=[],e.value.subject_id&&(e.value.subject_id=null),e.value.name&&(e.value.name=null),n){const a=yield se(n);_.value=a||[]}m()}r.prop==="subject_id"&&(p.value=[],e.value.name&&(e.value.name=null),m())});function z(){let{currentPage:r,pageSize:n}=s.value.pageOptions;const i=e.value,{diff_count:a}=i,c=R(i,["diff_count"]),u=y({current_page:r,page_size:n},c);return(a[0]||a[1])&&(u.diff_count=["",""],u.diff_count[0]=a[0]?Number(a[0]):"",u.diff_count[1]=a[1]?Number(a[1]):""),u}const g=()=>{o.value.formValidate().then(()=>{pe(z()).then(r=>{var a,c,u;const n=((a=r.data)==null?void 0:a.data)||[];f.value=n,s.value.pageOptions.total=(u=(c=r.data)==null?void 0:c.total)!=null?u:0})})},N=()=>{o.value.formValidate().then(()=>{ee.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{ie("post","/v1/survey_monitor/quality_monitor_export",void 0,{},F(y({},z()),{page_size:-1}),"xlsx").then(r=>{r&&r.data&&r.data.type=="application/json"&&S({message:"暂无导出信息！",type:"warning"})}).catch(()=>{S({type:"error",message:"导出失败"})})}).catch(()=>{})})},P=r=>{s.value.pageOptions.pageSize=r,g()},T=r=>{s.value.pageOptions.currentPage=r,g()};function B(){_.value=[],p.value=[],te(()=>{m()})}return(r,n)=>{const a=h("form-component"),c=h("el-card"),u=h("el-button"),i=h("Auth"),L=h("table-component");return X(),Y("div",ce,[j("div",de,[b(c,null,{default:w(()=>[j("div",{ref_key:"formDivRef",ref:l},[b(a,{ref_key:"formRef",ref:o,modelValue:e.value,"onUpdate:modelValue":n[0]||(n[0]=U=>e.value=U),"form-options":v,"is-query-btn":!0,onOnchangeFn:M,onQueryDataFn:g,onResetFields:B},null,8,["modelValue","form-options"])],512)]),_:1}),b(c,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:w(()=>[j("div",me,[j("div",_e,[b(i,{value:"quality-monitor/export"},{default:w(()=>[b(u,{type:"primary",onClick:N},{default:w(()=>[Z(" 导出 ")]),_:1})]),_:1})])]),b(L,{minHeight:s.value.styleOptions.minHeight,"table-options":s.value,"table-data":f.value,onOnHandleSizeChange:P,onOnHandleCurrentChange:T},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),we=ae(fe,[["__scopeId","data-v-7056017b"]]);export{we as default};
