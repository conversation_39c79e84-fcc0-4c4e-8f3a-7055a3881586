import{d as p,l as i,az as g,u as f,B as w,n as x,j as I,w as R,o as k,c as B,e as S,C as V,_ as E}from"./index-B63pSD2p.js";const P={class:"frame","element-loading-text":"加载中..."},C=["src"],D=p({name:"FrameView",__name:"frameView",props:{frameInfo:{}},setup(v){var m,u,d;const o=v,s=i(!0),t=g(),n=i(""),l=i(null);(m=f(t.meta))!=null&&m.frameSrc&&(n.value=(u=f(t.meta))==null?void 0:u.frameSrc),((d=f(t.meta))==null?void 0:d.frameLoading)===!1&&c();function c(){s.value=!1}function h(){V(()=>{const e=f(l);if(!e)return;const a=e;a.attachEvent?a.attachEvent("onload",()=>{c()}):e.onload=()=>{c()}})}return w(()=>t.fullPath,e=>{var a,r,_;t.name==="Redirect"&&e.includes((a=o.frameInfo)==null?void 0:a.fullPath)&&(n.value=e,s.value=!0),((r=o.frameInfo)==null?void 0:r.fullPath)===e&&(n.value=(_=o.frameInfo)==null?void 0:_.frameSrc)}),x(()=>{h()}),(e,a)=>{const r=I("loading");return R((k(),B("div",P,[S("iframe",{ref_key:"frameRef",ref:l,src:n.value,class:"frame-iframe"},null,8,C)])),[[r,s.value]])}}}),j=E(D,[["__scopeId","data-v-6def3f1f"]]);export{j as default};
