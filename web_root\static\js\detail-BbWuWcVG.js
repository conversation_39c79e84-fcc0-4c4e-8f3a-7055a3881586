import{d as i,aS as _,aO as p,l as u,r as l,o as m,g as f,h as d,b as h,ac as b,ad as w,e,_ as V}from"./index-B63pSD2p.js";const a=o=>(b("data-v-0ff18bf4"),o=o(),w(),o),x=a(()=>e("h4",null,"1、性能测试指标（满分3分）",-1)),g=a(()=>e("p",null,"应答完整，列举了关键指标（响应时间、吞吐量、错误率）并解释清晰，得3分。",-1)),v=a(()=>e("h4",null,"2、测试场景设计（满分4分）",-1)),I=a(()=>e("p",null,"场景要素齐全（并发数、时长、预期指标），数值合理，得4分。",-1)),k=a(()=>e("h4",null,"3、问题排查（满分3分）",-1)),S=a(()=>e("p",null,"正确指出资源不足和数据库问题，得2分（少1条原因扣1分）。",-1)),y=i({__name:"detail",props:_({logId:{type:String,default:""}},{drawerVisible:{},drawerVisibleModifiers:{}}),emits:["update:drawerVisible"],setup(o){const t=p(o,"drawerVisible");return u({ip:"127.0.0.1",module_name:"正评管理",name:"admin超管",op_content:"新增xxx",op_time:"2025-08-05 11:58:21",op_type:"1",page_name:"作答管理"}),(B,s)=>{const n=l("el-scrollbar"),r=l("el-drawer");return m(),f(r,{modelValue:t.value,"onUpdate:modelValue":s[0]||(s[0]=c=>t.value=c),title:"AI评析",size:"40%",class:"subjective-check-ai-detail-drawer-container"},{default:d(()=>[h(n,{height:"calc(100% - 10px)",class:"dark:!bg-black eye-box"},{default:d(()=>[x,g,v,I,k,S]),_:1})]),_:1},8,["modelValue"])}}}),M=V(y,[["__scopeId","data-v-0ff18bf4"]]);export{M as default};
