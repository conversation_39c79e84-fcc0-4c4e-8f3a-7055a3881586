import{P as V}from"./index-Astsj93h.js";import{a as F}from"./response-management-BIkrM71A.js";import{p as R,g as W,a as B}from"./common-methods-BWkba4Bo.js";import{d as H,l,P as I,n as k,ao as E,T as L,r as m,j as N,w as q,o as A,c as J,e as g,b as i,h as x,u as D,ac as M,ad as Q,_ as T}from"./index-B63pSD2p.js";import{g as U}from"./test-paper-management-DjV_45YZ.js";import{c as G,a as K}from"./calculateTableHeight-BjE6OFD1.js";const X=d=>(M("data-v-d8befe9d"),d=d(),Q(),d),Y={class:"zf-first-box","element-loading-text":"数据导入中..."},Z={class:"zf-second-box"},$=X(()=>g("div",{class:"upload-btn-box"},null,-1)),ee=H({name:"response",__name:"index",setup(d){const C=l(!1),f=l([]),u=l([]);l(null);const v=l(null),n=l(null);l(null);const O=l(null);l("");const b=l({}),j=I({column:3,labelWidth:"70px",itemWidth:"210px",rules:{paper_name:[{trigger:["blur","change"],validator:(t,a,o)=>{if(a&&a.length>100)return o(new Error("所属试卷长度不能超过100！"));o()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>R.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>f.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷名称",clearable:!0,optionData:()=>u.value}]}),s=l({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"project_name",label:"所属资格",minWidth:"100px"},{prop:"subject_name",label:"所属科目",minWidth:"90px"},{prop:"paper_name",label:"试卷名称",minWidth:"120px"},{prop:"created_time",label:"导入时间",minWidth:"160px",sortable:!0}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let c=l([]),h=null;k(()=>{G(h,v.value,s.value,!1),p(),W()}),E(()=>{K(h)});const p=()=>{let{currentPage:t,pageSize:a}=s.value.pageOptions,o=JSON.parse(JSON.stringify(n.value.getAllCardData()));o.current_page=t,o.page_size=a,F(o).then(e=>{var r,_;e.code&&e.code===200?((_=(r=e.data)==null?void 0:r.data)==null?void 0:_.length)>0?(c.value=e.data.data,s.value.pageOptions.total=e.data.total):(c.value=[],s.value.pageOptions.total=0):(c.value=[],L.warning(e.msg))})},y=(t,a)=>{t.prop==="project_id"?(n.value.getCardData("subject_id")&&n.value.setCardData("subject_id",""),n.value.getCardData("paper_id")&&n.value.setCardData("paper_id",""),f.value=[],u.value=[],a&&B(a).then(o=>{f.value=o||[]})):t.prop==="subject_id"&&(u.value=[],n.value.getCardData("paper_id")&&n.value.setCardData("paper_id",""),a&&S())},S=()=>{const{project_id:t,subject_id:a}=n.value.getAllCardData();U({project_id:t,subject_id:a,page_size:-1}).then(e=>{e.code&&e.code===200&&(e.data.data.forEach(r=>{r.label=r.paper_name,r.value=r.paper_id}),u.value=e.data.data)})},z=t=>{s.value.pageOptions.pageSize=t,p()},w=t=>{s.value.pageOptions.currentPage=t,p()};return(t,a)=>{const o=m("form-component"),e=m("el-card"),r=m("table-component"),_=N("loading");return q((A(),J("div",Y,[g("div",Z,[i(e,null,{default:x(()=>[g("div",{ref_key:"formDivRef",ref:v},[i(o,{ref_key:"formRef",ref:n,modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=P=>b.value=P),"is-query-btn":!0,"form-options":j,onQueryDataFn:p,onOnchangeFn:y},null,8,["modelValue","form-options"])],512)]),_:1}),i(e,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:x(()=>[$,i(r,{minHeight:s.value.styleOptions.minHeight,"table-options":s.value,"table-data":D(c),onOnHandleSizeChange:z,onOnHandleCurrentChange:w},null,8,["minHeight","table-options","table-data"])]),_:1})]),i(D(V),{ref_key:"progressBarRef",ref:O,title:"导入作答成绩进度",apiInfo:{params:{record_type:3},url:"/v1/transfer/get_import_process"},onQueryData:p},null,512)])),[[_,C.value]])}}}),se=T(ee,[["__scopeId","data-v-d8befe9d"]]);export{se as default};
