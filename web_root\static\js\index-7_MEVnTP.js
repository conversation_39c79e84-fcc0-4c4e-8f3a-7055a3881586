import{aQ as P,aR as H,d as L,i as E,l as s,P as A,n as M,ao as Q,r as d,o as f,c as U,e as g,b as _,h as i,u as C,aN as w,g as k,f as q,y as D,aV as J,T as O,_ as $}from"./index-B63pSD2p.js";import{g as I}from"./test-paper-management-DjV_45YZ.js";import{p as G,g as K,a as X}from"./common-methods-BWkba4Bo.js";import{q as Y,g as Z}from"./rules-form-CST-rV3v.js";import{c as ee,a as ae}from"./calculateTableHeight-BjE6OFD1.js";import"./scoring-rules-BR2vQ7G3.js";const te=h=>P.request("post",H("/v1/prepare/delete_answer_sample"),{data:h}),oe={class:"zf-first-box"},le={class:"zf-second-box"},ne={style:{display:"flex","justify-content":"space-evenly"}},se=L({name:"samplepaper-manage",__name:"index",setup(h){const j=E(),b=s(null),l=s(null),m=s([]),u=s([]),v=s({}),S=A({column:3,labelWidth:"68px",itemWidth:"240px",rules:{},fields:[{label:"资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择资格",optionData:()=>G.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>m.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>u.value},{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!1,clearable:!0,optionData:()=>Y},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0}]}),r=s({field:[{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"ques_type_name",label:"题型",minWidth:"120px"},{prop:"ques_type_score",label:"题分",minWidth:"50px"},{prop:"answer_score",label:"评分",minWidth:"50px"},{prop:"ques_code",label:"试题编号",minWidth:"200px"},{prop:"progress",label:"考生密号",minWidth:"160px",showOverflowTooltip:!0},{prop:"opera",label:"操作",type:"slot",fixed:"right",minWidth:"158px",showOverflowTooltip:!0}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),y=s([]);s([]);let x=null;M(()=>{ee(x,b.value,r.value),K(),Z(),p()}),Q(()=>{ae(x)});const V=(a,e)=>{a.prop==="project_id"?(m.value=[],u.value=[],l.value.getCardData("subject_id")&&l.value.setCardData("subject_id",null),l.value.getCardData("paper_id")&&l.value.setCardData("paper_id",null),e&&X(e).then(t=>{m.value=t||[]})):a.prop==="subject_id"&&(u.value=[],l.value.getCardData("paper_id")&&l.value.setCardData("paper_id",null),e&&z())},z=()=>{const{project_id:a,subject_id:e}=l.value.getAllCardData();I({project_id:a,subject_id:e,page_size:-1}).then(n=>{n.code&&n.code===200&&(n.data.data.forEach(o=>{o.label=o.paper_name,o.value=o.paper_id}),u.value=n.data.data)})},p=a=>{var o;let e=JSON.parse(JSON.stringify(l.value.getAllCardData())),{currentPage:t,pageSize:n}=r.value.pageOptions;e.current_page=t,e.page_size=n,e.business_ques_type_id=(o=e.ques_type_code_list)==null?void 0:o.join(","),y.value=[{mark_id:"125271269767042039817",ques_id:"0000080000002920437AE1001D",subject_name:"系统架构设计师(案例分析)",paper_id:null,paper_name:"",ques_type_name:"问答题",ques_code:"900000001N99B64TP001",ques_order:null,mark_state:1,mark_state_str:"未评分",progress_count:"5/11",progress:"125266399415861510144",ques_type_score:25,answer_score:20,start_time:"2025-8-12 09:36:25",end_time:"2025-8-18 10:20:24"}],r.value.pageOptions.total=1},B=a=>{r.value.pageOptions.pageSize=a,p()},R=a=>{r.value.pageOptions.currentPage=a,p()},T=a=>{j.push({path:"/manual-marking/marking-paper/index"})},W=a=>{J.confirm("确定删除该样卷数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let e={answer_sample_id:a.answer_sample_id};te(e).then(t=>{t.code&&t.code===200?(O.success(t.msg),p()):O.warning(t.msg)})}).catch(()=>{})};return(a,e)=>{const t=d("form-component"),n=d("el-card"),o=d("el-link"),F=d("table-component");return f(),U("div",oe,[g("div",le,[_(n,null,{default:i(()=>[g("div",{ref_key:"formDivRef",ref:b,class:"query-box"},[_(t,{ref_key:"formRef",ref:l,modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=c=>v.value=c),"form-options":S,"is-query-btn":!0,onQueryDataFn:p,onOnchangeFn:V},null,8,["modelValue","form-options"])],512)]),_:1}),_(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:i(()=>[_(F,{"min-height":r.value.styleOptions.minHeight,"table-options":r.value,"table-data":y.value,onOnHandleSizeChange:B,onOnHandleCurrentChange:R},{opera:i(c=>[g("div",ne,[C(w)("samplepaper-manage/edit")?(f(),k(o,{key:0,type:"primary",onClick:N=>T(c.row)},{default:i(()=>[q("编辑 ")]),_:2},1032,["onClick"])):D("",!0),C(w)("samplepaper-manage/delete")?(f(),k(o,{key:1,type:"primary",onClick:N=>W(c.row)},{default:i(()=>[q("删除 ")]),_:2},1032,["onClick"])):D("",!0)])]),_:1},8,["min-height","table-options","table-data"])]),_:1})])])}}}),_e=$(se,[["__scopeId","data-v-02fbd9fb"]]);export{_e as default};
