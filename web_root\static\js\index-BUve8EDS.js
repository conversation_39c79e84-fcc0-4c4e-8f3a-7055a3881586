var R=Object.defineProperty;var y=Object.getOwnPropertySymbols;var P=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var x=(n,e,l)=>e in n?R(n,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[e]=l,q=(n,e)=>{for(var l in e||(e={}))P.call(e,l)&&x(n,l,e[l]);if(y)for(var l of y(e))M.call(e,l)&&x(n,l,e[l]);return n};import L from"./extract-data-DlHOJ1Us.js";import{f as Y}from"./handleMethod-BIjqYEft.js";import{p as B,g as E,a as N}from"./common-methods-BWkba4Bo.js";import{c as Q,a as T}from"./calculateTableHeight-BjE6OFD1.js";import{g as A}from"./start-marking-Cd3MpOOi.js";import{g as U}from"./base-DyvdloLK.js";import{d as I,l as o,P as G,n as J,ao as K,T as j,r as c,o as X,c as Z,e as b,b as p,h as f,f as $,_ as ee}from"./index-B63pSD2p.js";import"./index-BCxZUZMh.js";import"./test-paper-management-DjV_45YZ.js";const te={class:"zf-first-box"},ae={class:"zf-second-box"},le={class:"zf-flex-end"},oe=I({name:"score-quality",__name:"index",setup(n){o([{value:1,label:"未开始"},{value:2,label:"进行中"},{value:3,label:"已完成"}]);const e=o(null),l=o(null),v=o(null),u=o({}),D=G({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>B.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>d.value},{label:"考生密号",prop:"stu_secret_num",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"试题",prop:"knowledge_show_code",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择试题",props:{value:"ques_code",label:"ques_name"},optionData:()=>m.value},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"质检人",prop:"quality_user_name",type:"input",defaultValue:"",placeholder:"请输入质检人名称",clearable:!0},{label:"质检时间",prop:"review_time",type:"datetimerange",width:"420px",valueFormat:"YYYY-MM-DD HH:mm:ss",clearable:!0,defaultValue:"",optionData:()=>[]}]}),d=o([]),m=o([]),r=o({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"knowledge_show",label:"试题名称",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"mark_score_sum",label:"正评分",minWidth:"80px"},{prop:"quality_score_sum",label:"质检分",minWidth:"80px"},{prop:"quality_type",label:"质检类型",minWidth:"90px",formatter:t=>Y(t.quality_type,[{value:1,label:"质检通过"},{value:2,label:"修改提交"},{value:3,label:"退回重评"}])},{prop:"quality_user_name",label:"质检人",minWidth:"80px"},{prop:"mark_time",label:"质检时间",minWidth:"140px"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),h=o([]);let g=null;J(()=>{E(),Q(g,e.value,r.value),_()}),K(()=>{T(g)});const _=()=>{let{currentPage:t,pageSize:a}=r.value.pageOptions,i=q({current_page:t,page_size:a,is_show_self:0},u.value);A(i).then(s=>{s.code&&s.code===200?(h.value=s.data.data,r.value.pageOptions.total=s.data.total):j.error(s.msg)})},O=t=>{r.value.pageOptions.currentPage=1,r.value.pageOptions.pageSize=t,_()},V=t=>{r.value.pageOptions.currentPage=t,_()},W=()=>{v.value.openDialog("add")};function k(){d.value=[]}const w=(t,a)=>{t.prop==="project_id"?(d.value=[],m.value=[],u.value.subject_id&&(u.value.subject_id=null),a&&N(a).then(i=>{d.value=i||[]})):t.prop==="subject_id"&&(m.value=[],a&&z())},z=()=>{let t={project_id:u.value.project_id,subject_id:u.value.subject_id,is_quality:1};U(t).then(a=>{a.code&&a.code===200?m.value=a.data:j.error(a.msg)})};return(t,a)=>{const i=c("form-component"),s=c("el-card"),S=c("el-button"),C=c("Auth"),F=c("table-component");return X(),Z("div",te,[b("div",ae,[p(s,null,{default:f(()=>[b("div",{ref_key:"formDivRef",ref:e},[p(i,{ref_key:"formRef",ref:l,modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=H=>u.value=H),"form-options":D,"is-query-btn":!0,onOnchangeFn:w,onQueryDataFn:_,onResetFields:k},null,8,["modelValue","form-options"])],512)]),_:1}),p(s,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:f(()=>[b("div",le,[p(C,{value:"score-quality/extract"},{default:f(()=>[p(S,{type:"primary",onClick:W},{default:f(()=>[$("抽取质检")]),_:1})]),_:1})]),p(F,{minHeight:r.value.styleOptions.minHeight,"table-options":r.value,"table-data":h.value,onOnHandleSizeChange:O,onOnHandleCurrentChange:V},null,8,["minHeight","table-options","table-data"])]),_:1})]),p(L,{ref_key:"extractDataRef",ref:v},null,512)])}}}),fe=ee(oe,[["__scopeId","data-v-f194e9d7"]]);export{fe as default};
