import{c as ce}from"./validate-Dc6ka3px.js";import{g as de,p as pe}from"./intelligent-marking-CyZREtXG.js";import{h as S}from"./handleImages-D-nd439N.js";import{F as me,E as fe}from"./fullscreen-exit-line-DVwpkItP.js";import{d as ve,l as x,P as he,r as h,o as t,g as J,h as v,e as s,b as _,f as b,c as o,u as F,t as r,y,F as D,p as M,q as be,C as ye,T,ac as ge,ad as xe,_ as ke}from"./index-B63pSD2p.js";const i=L=>(ge("data-v-dfb17cfc"),L=L(),xe(),L),we={class:"fullscreen-content"},qe={class:"zf-dialog-first-box"},Ve={class:"zf-dialog-second-box"},Fe={class:"mark-box"},Ce={key:0,class:"mark-info-box"},De={key:0},Me=i(()=>s("div",{class:"text-bold whitespace-nowrap mt-[1px]"},"试题材料：",-1)),Te={class:"flex"},Le={class:"mt-[1px]"},Ne=["innerHTML"],He=i(()=>s("div",{class:"text-bold whitespace-nowrap mt-[1px]"},"试题描述：",-1)),$e={class:"flex"},Se={key:0},Ie=["innerHTML"],Pe={key:1},Ee=["innerHTML"],Be={key:2},Ue=i(()=>s("span",{class:"text-bold"},"参考答案：",-1)),Oe={key:3},ze=i(()=>s("span",{class:"text-bold"},"参考答案：",-1)),We=["innerHTML"],je={key:4},Re=i(()=>s("span",{class:"text-bold"},"参考答案：",-1)),Ae={key:5},Je=i(()=>s("span",{class:"text-bold"},"考生答案：",-1)),Qe={key:0},Ge={key:6},Ke=i(()=>s("span",{class:"text-bold"},"考生答案：",-1)),Xe=["innerHTML"],Ye={key:7},Ze=i(()=>s("span",{class:"text-bold"},"考生答案：",-1)),es={key:8},ss=i(()=>s("span",{class:"text-bold"},"评分规则：",-1)),ls={key:9},ts=i(()=>s("span",{class:"text-bold"},"评分标准：",-1)),os={key:10},as=i(()=>s("span",{class:"text-bold"},"系统评分解析：",-1)),ns=i(()=>s("span",{class:"text-bold"},"判断结果：",-1)),rs=i(()=>s("span",{class:"text-bold"},"试题分数：",-1)),us=i(()=>s("span",{class:"text-bold"},"考生得分：",-1)),is={key:11},_s=i(()=>s("span",{class:"text-bold"},"评分异常原因：",-1)),cs={class:"flex"},ds=i(()=>s("span",{class:"text-[#F56C6C] mr-[3px]"},"*",-1)),ps={key:0,class:"whitespace-nowrap leading-[32px]"},ms={class:"point-box"},fs=i(()=>s("div",{class:"tit-box"},"专家评分解析",-1)),vs={class:"point-form"},hs={class:"form-point"},bs={class:"footer-btn"},ys=ve({__name:"manual-scoring",emits:["queryData"],setup(L,{expose:Q,emit:G}){const K=G,X=x("评分详情"),N=x(!1),H=x(!1),Y=he({column:3,labelWidth:"108px",itemWidth:"250px",fields:[{label:"专家评分分数",prop:"profession_score",type:"input",defaultValue:"",placeholder:"请输入专家评分分数",isHidden:!1,clearable:!0}]}),C=x(null),e=x({}),P=x({}),Z=x({rules:{profession_score_list:[{trigger:["blur","change"],validator:(a,l,u)=>{if(l.length>0){for(let n=0;n<l.length;n++)if(l[n]){if(!ce(l[n]))return u(new Error(`第${n+1}个空请输入正数（至多两位小数）！`))}else return l.length===1?u(new Error("评分分数不能为空！")):u(new Error(`第${n+1}个空不能为空！`));u()}else return u(new Error("评分分数不能为空！"))}}]}}),k=x([]),p=x({total_score:null,profession_score_list:[],modify_reason:""}),ee=a=>{N.value=!0,se(a),P.value=a},I=()=>{N.value=!1,C.value.resetFields(),k.value=[],e.value={}},se=a=>{const{same_answer_group_id:l,paper_code:u,ques_code:n}=a;de({same_answer_group_id:l,paper_code:u,ques_code:n}).then(m=>{m.code&&m.code===200?(e.value=m.data.data,e.value.mark_role===2&&ye(()=>{C.value.setCardData("profession_score",e.value.stu_score),Y.fields.map(f=>{a.hasOwnProperty(f.prop)&&C.value.setCardData(f.prop,a[f.prop])})})):T.warning(m.msg)})},le=a=>{let l=[{label:"正确",value:1},{label:"错误",value:2},{label:"部分正确",value:3}],u="";return l.map(n=>{n.value===a&&(u=n.label)}),a===4&&(u=""),u},E=(a,l)=>{var g;const u=(g=e.value.score)==null?void 0:g.split("◎☆◎"),n=a==null?void 0:a.map((m,f)=>l?`第${f+1}个空答案：${m}（${Number(u[f])}分）`:`第${f+1}个空答案：${m}`);return n==null?void 0:n.join("</br>")},B=()=>{var a;if(p.value.profession_score_list.length>0){let l=(a=p.value.profession_score_list)==null?void 0:a.reduce((u,n)=>Number(u)+Number(n));return l=te(Number(l)),p.value.total_score=Number(l),l}return null},te=a=>{let l=a.toFixed(2);return l.endsWith(".0")?parseInt(a):l.endsWith("0")?l.slice(0,-1):l},oe=()=>{k.value.push("")},ae=a=>{k.value.splice(a,1)},U=()=>{H.value=!H.value},ne=()=>{C.value.validate(a=>{re()}).catch(()=>{T.warning("请按要求填写！")})},re=()=>{let a=JSON.parse(JSON.stringify(p.value)),{small_ques_order:l,ques_id:u,same_answer_group_id:n,total_score:g,ques_score_list:m}=e.value;if(a.total_score>g)T.warning("评分分数大于试题总分！");else{let{paper_code:f}=P.value,w={paper_code:f,ques_id:u,small_ques_order:l,total_standard_score:g,same_answer_group_id:n,ques_score_list:m};k.value.length>0&&(w.profession_parse=k.value),w=Object.assign(a,w),delete w.total_score,pe(w).then(q=>{q.code&&q.code===200?(T.success(q.msg),K("queryData"),I()):T.warning(q.msg)})}};return Q({openDialog:ee}),(a,l)=>{const u=h("iconify-icon-offline"),n=h("el-input"),g=h("el-form-item"),m=h("el-form"),f=h("Delete"),w=h("el-icon"),q=h("Plus"),ue=h("el-scrollbar"),O=h("el-button"),ie=h("el-dialog");return t(),J(ie,{modelValue:N.value,"onUpdate:modelValue":l[2]||(l[2]=$=>N.value=$),title:X.value,width:"50%","close-on-click-modal":!1,"before-close":I,draggable:"",fullscreen:H.value,"align-center":""},{footer:v(()=>[s("div",bs,[_(O,{type:"primary",onClick:ne},{default:v(()=>[b("确定")]),_:1}),_(O,{onClick:I},{default:v(()=>[b("取消")]),_:1})])]),default:v(()=>[s("div",we,[H.value?(t(),o("i",{key:1,class:"cursor-pointer mr-[12px]",onClick:U},[_(u,{icon:F(fe)},null,8,["icon"])])):(t(),o("i",{key:0,class:"cursor-pointer mr-[12px]",onClick:U},[_(u,{icon:F(me)},null,8,["icon"])]))]),s("div",qe,[s("div",Ve,[_(ue,{"max-height":"750px",always:""},{default:v(()=>{var $,z,W,j,R,A;return[s("div",Fe,[Object.keys(e.value).length>0?(t(),o("ul",Ce,[e.value.ques_material?(t(),o("li",De,[Me,s("div",Te,[s("span",Le,r(e.value.ques_material_order)+"．",1),s("span",{innerHTML:e.value.ques_material.html?F(S)(e.value.ques_material.html):F(S)(e.value.ques_material.text)},null,8,Ne)])])):y("",!0),s("li",null,[He,s("div",$e,[s("span",null,[b(r(e.value.ques_order),1),e.value.ques_material?y("",!0):(t(),o("span",Se,"．"))]),s("span",{innerHTML:e.value.ques_desc.html?F(S)(e.value.ques_desc.html):F(S)(e.value.ques_desc.text)},null,8,Ie)])]),e.value.ques_type_code!=="B"?(t(),o("span",Pe,[(t(!0),o(D,null,M(e.value.ques_choices,c=>(t(),o("li",null,[s("span",{class:"flex",innerHTML:c.options},null,8,Ee)]))),256))])):y("",!0),e.value.ques_type_code==="B"?(t(),o("li",Be,[Ue,s("span",null,r(e.value.standard_answer[0]==="1"?"正确":"错误"),1)])):e.value.ques_type_code==="D"?(t(),o("li",Oe,[ze,s("p",{innerHTML:E(e.value.standard_answer,!0)},null,8,We)])):(t(),o("li",je,[Re,b(r(($=e.value)!=null&&$.standard_answer?(z=e.value)==null?void 0:z.standard_answer[0]:null),1)])),e.value.ques_type_code==="B"?(t(),o("li",Ae,[Je,(W=e.value)!=null&&W.stu_answer?(t(),o("span",Qe,r(((R=(j=e.value)==null?void 0:j.stu_answer)==null?void 0:R[0])==="1"?"正确":"错误"),1)):y("",!0)])):e.value.ques_type_code==="D"?(t(),o("li",Ge,[Ke,s("p",{innerHTML:E(e.value.stu_answer)},null,8,Xe)])):(t(),o("li",Ye,[Ze,b(r(e.value.stu_answer?e.value.stu_answer[0]:null),1)])),e.value.e_mark_rule?(t(),o("li",es,[ss,s("span",null,r(e.value.e_mark_rule),1)])):y("",!0),((A=e.value.ques_mark_point)==null?void 0:A.length)>0?(t(),o("li",ls,[ts,(t(!0),o(D,null,M(e.value.ques_mark_point,(c,d)=>(t(),o("p",null,r(d+1)+"．"+r(c.point)+"（"+r(c.score)+"分）",1))),256))])):y("",!0),e.value.answer_parse?(t(),o("li",os,[as,(t(!0),o(D,null,M(e.value.answer_parse,(c,d)=>(t(),o("p",null,r(e.value.ques_type_code==="D"?`第${d+1}个空：`:`${d+1}．`)+r(c||"无"),1))),256))])):y("",!0),s("li",null,[ns,b(r(le(e.value.mark_result)),1)]),s("li",null,[rs,b(r(e.value.total_score)+r(e.value.total_score?" 分":""),1)]),s("li",null,[us,b(r(e.value.stu_score)+r(e.value.stu_score!==null?" 分":""),1)]),e.value.mark_state===3||e.value.mark_state===4?(t(),o("li",is,[_s,b(r(e.value.mark_fail_reason),1)])):y("",!0)])):y("",!0),_(m,{ref_key:"formRef",ref:C,model:p.value,class:"alter-box","label-width":"108px"},{default:v(()=>[s("div",cs,[_(g,{label:"专家评分分数",prop:"profession_score_list",rules:Z.value.rules.profession_score_list},{label:v(()=>[ds,b("专家评分分数")]),default:v(()=>{var c;return[e.value.ques_type_code==="D"&&((c=e.value.standard_answer)==null?void 0:c.length)>0?(t(!0),o(D,{key:0},M(e.value.standard_answer,(d,V)=>(t(),o("div",null,[s("span",null,"第 "+r(V+1)+" 空：",1),_(n,{modelValue:p.value.profession_score_list[V],"onUpdate:modelValue":_e=>p.value.profession_score_list[V]=_e,style:be([{"margin-right":"20px"},{width:e.value.standard_answer.length>1?"80px":"240px"}]),placeholder:"请输入",clearable:""},null,8,["modelValue","onUpdate:modelValue","style"])]))),256)):(t(),J(n,{key:1,modelValue:p.value.profession_score_list[0],"onUpdate:modelValue":l[0]||(l[0]=d=>p.value.profession_score_list[0]=d),style:{width:"240px"},placeholder:"请输入",clearable:""},null,8,["modelValue"]))]}),_:1},8,["rules"]),e.value.ques_type_code==="D"&&B()?(t(),o("div",ps," (共"+r(`${B()}`)+"分) ",1)):y("",!0)]),_(g,{label:"修改原因",prop:"modify_reason"},{default:v(()=>[_(n,{modelValue:p.value.modify_reason,"onUpdate:modelValue":l[1]||(l[1]=c=>p.value.modify_reason=c),placeholder:"请输入修改原因"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),s("div",ms,[fs,s("div",vs,[(t(!0),o(D,null,M(k.value,(c,d)=>(t(),o("div",hs,[s("div",null,r(d+1)+"．",1),_(n,{type:"input",modelValue:k.value[d],"onUpdate:modelValue":V=>k.value[d]=V,class:"input-margin",placeholder:"请输入评分解析",autosize:"",clearable:""},null,8,["modelValue","onUpdate:modelValue"]),_(w,{class:"point-icon-box point-del",onClick:V=>ae(d)},{default:v(()=>[_(f)]),_:2},1032,["onClick"])]))),256)),s("div",null,[_(w,{class:"point-icon-box",onClick:oe},{default:v(()=>[_(q)]),_:1})])])])])]}),_:1})])])]),_:1},8,["modelValue","title","fullscreen"])}}}),Vs=ke(ys,[["__scopeId","data-v-dfb17cfc"]]);export{Vs as default};
