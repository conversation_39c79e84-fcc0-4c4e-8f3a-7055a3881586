import{_ as Ue}from"./filter.vue_vue_type_script_setup_true_lang-Btf669J7.js";import{g as Qe,q as Ge,a as Je,M as Ke}from"./manual-BZr0GwT3.js";import{h as ke}from"./handleImages-D-nd439N.js";import{d as Xe,az as Ye,i as Ze,l as v,B as et,n as tt,ao as st,T as V,ai as H,aV as at,r as d,o as l,c as i,e as t,b as n,h as u,f as b,t as c,q as lt,F as S,g as F,y as g,u as xe,p as P,U as Ce,ac as nt,ad as ot,_ as it}from"./index-B63pSD2p.js";import"./validate-Dc6ka3px.js";const x=N=>(nt("data-v-6225d45a"),N=N(),ot(),N),rt={class:"zf-first-box"},ct={class:"zf-second-box"},_t=x(()=>t("div",{class:"title"},"定标试题",-1)),ut={class:"mb-[5px]"},dt={class:"description"},pt={class:"detail-tag"},vt=x(()=>t("span",null,"试题描述",-1)),ht={key:0,class:"flex mb-[6px]"},mt={key:0},ft=["innerHTML"],gt={class:"flex mb-[6px]"},yt={key:0,class:"whitespace-nowrap"},bt=["innerHTML"],qt={class:"description"},wt={class:"scores-total"},kt={class:"msg"},xt={class:"scores-total"},Ct={class:"msg"},St={key:0},Bt={key:1},Dt={class:"detail"},Ot=["onClick"],Vt={key:0},Ht={key:1},Nt={class:"d"},Tt={key:0},zt={class:"tkt-div-title"},Mt=x(()=>t("div",null,"得分点",-1)),It=["innerHTML"],Et={class:"flex justify-between"},Lt=x(()=>t("div",{class:"title"},"定标核对",-1)),Rt={class:"table-btn"},jt={class:"operaBtn"};const $t=x(()=>t("div",{class:"drawer-title"},[t("div",{class:"line"}),t("div",null,"考生答案")],-1)),Wt={class:"mb-[12px]"},At={class:"drawer-title"},Ft=x(()=>t("div",{class:"line"},null,-1)),Pt={class:"drawer-title"},Ut=x(()=>t("div",{class:"line"},null,-1)),Qt={class:"drawer-title"},Gt=x(()=>t("div",{class:"line"},null,-1)),Jt={key:0},Kt={key:1,class:"drawer-content"},Xt=Xe({name:"calibrate-detail",__name:"index",setup(N){const T=Ye(),Se=Ze(),a=v({});v(null);const z=v(null),U=v(null),y=v({field:[{prop:"same_answer_group_id",label:"分组ID",minWidth:"120px"},{prop:"answer_cluster",label:"聚类类别",minWidth:"260px"},{prop:"stu_count",label:"人数",minWidth:"80px",sortable:!0},{prop:"ai_score",label:"AI评分",minWidth:"100px",sortable:!0},{prop:"mark_state_str",label:"定标状态",minWidth:"90px"},{prop:"updated_time",label:"定标时间",minWidth:"150px"},{prop:"opera",label:"操作",type:"template",minWidth:"210px",templateGroup:[{title:()=>"重新定标",clickBtn(s){J("single",s)}},{title:()=>["2","3"].includes(p.value)?"人工定标":"",clickBtn(s){Ie(s)}},{title:()=>"评析",clickBtn(s){B.value=s,$.value=!0}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),M=v([]),D=v([]),B=v({}),p=v("2"),I=v(0),j=v(""),$=v(!1);let O=null;const Q=v(""),E=v(!1);et(()=>p.value,s=>{M.value=[],y.value.field.forEach(e=>{e.prop==="opera"?["1","4"].includes(s)?e.isHidden=!0:(e.minWidth="210px",e.isHidden=!1):e.prop==="updated_time"&&(["2","3"].includes(s)?e.isHidden=!1:e.isHidden=!0)})}),tt(()=>{const{flag:s}=T.query;s&&s==="error"&&(p.value="3"),W(),window.addEventListener("resize",W),Ne()});const W=()=>{y.value.styleOptions.minHeight=window.innerHeight-290+"px",Q.value=window.innerHeight-250+"px"};st(()=>{window.removeEventListener("resize",W),A()});const Be=()=>{w()},De=(s,e)=>{I.value!==e&&(I.value=e,j.value=s.small_ques_order,w())},Oe=s=>{D.value=s},Ve=s=>{y.value.pageOptions.pageSize=s,w()},He=s=>{y.value.pageOptions.currentPage=s,w()},Ne=()=>{const{ques_id:s,paper_id:e}=T.query;Qe({paper_id:e,ques_id:s}).then(h=>{var o,m,C;h.code&&h.code===200?(a.value=h.data||{},j.value=((C=(m=(o=h.data)==null?void 0:o.answer_info_list)==null?void 0:m[0])==null?void 0:C.small_ques_order)||"",w()):V.warning(h.msg)})},w=()=>{var h,o;const{ques_id:s}=T.query;let e={current_page:y.value.pageOptions.currentPage,page_size:y.value.pageOptions.pageSize,ques_id:s,mark_state:Number(p.value)};((o=(h=a.value)==null?void 0:h.ques_info)==null?void 0:o.ques_type_code)==="D"&&(e.small_ques_order=j.value);let r=K();r&&Object.keys(r).length&&Object.assign(e,r),Ge(e).then(m=>{m.code&&m.code===200?(M.value=m.data.data,p.value==="4"?O&&m.data.total===0&&(V.success("定标成功！"),A()):O&&A(),y.value.pageOptions.total=m.data.total):V.warning(m.msg)})},Te=()=>{z.value.openDrawer()},G=()=>{w()},J=(s,e)=>{let r={},h=H("p",null,["确定重新定标？"]);if(s==="batch"){if(!y.value.pageOptions.total){V.warning("当前筛选条件没有可重新定标的数据！");return}const o=D.value.length,m=o||y.value.pageOptions.total;h=H("p",null,[H("span",null,`确定${o?"":"根据筛选条件"}对${p.value==="3"?"异常评分数据":"已定标数据"}`),H("span",{style:"color: var(--el-color-primary)"},`（共${m}条）`),H("span",null,"重新定标")])}at({title:"提示",message:h,confirmButtonText:"优先定标",cancelButtonText:"取消",showCancelButton:!0,cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{if(s==="single")r.ques_id=e.ques_id,r.select_group_id_list=[e.same_answer_group_id];else if(s==="batch")if(D.value.length)r.ques_id=D.value[0].ques_id,r.select_group_id_list=D.value.map(o=>o.same_answer_group_id);else{r.ques_id=M.value[0].ques_id,r.mark_state=Number(p.value);let o=K();o&&Object.keys(o).length&&Object.assign(r,o)}Je(r).then(o=>{o.code&&o.code===200?(p.value="4",ze()):V.warning(o.msg)})}).catch(()=>{})},ze=()=>{w(),O=setInterval(()=>{w()},2e4)},A=()=>{O&&clearInterval(O)},Me=()=>{Se.push("/ai-calibration/calibrate/index")},K=()=>{var s;if(z.value){let e=(s=z.value)==null?void 0:s.getFormData();return e&&Object.keys(e).length&&(e.stu_score.value===""?e.stu_score=null:e.stu_score.value=Number(e.stu_score.value),Object.keys(e).forEach(r=>{e[r]===""&&(e[r]=null)})),e}return{}},Ie=s=>{const{paper_id:e}=T.query,{total_score:r}=a.value.ques_info;U.value.openDrawer(Object.assign({},s,{paper_id:e},{total_score:r}))},Ee=s=>{let e=/◎☆◎/g;return s.replace(e,"，")};return(s,e)=>{const r=d("el-text"),h=d("ArrowRight"),o=d("el-icon"),m=d("ArrowDown"),C=d("el-button"),Le=d("el-scrollbar"),X=d("el-col"),Re=d("Close"),L=d("el-tab-pane"),je=d("el-tabs"),$e=d("table-component"),Yt=d("el-checkbox"),We=d("el-row"),Ae=d("el-card"),Fe=d("el-drawer");return l(),i("div",rt,[t("div",ct,[n(Ae,null,{default:u(()=>[n(We,{class:"review-div"},{default:u(()=>[n(X,{class:"second-div",span:8,style:{padding:"0 8px"}},{default:u(()=>[_t,t("div",ut,[n(r,null,{default:u(()=>{var f,k;return[b("试题编号："+c((k=(f=a.value)==null?void 0:f.ques_info)==null?void 0:k.ques_code),1)]}),_:1})]),n(Le,{height:Q.value,always:""},{default:u(()=>{var f,k,R,Y,Z,ee,te,se,ae,le,ne,oe,ie,re,ce,_e,ue,de,pe,ve,he,me;return[t("div",dt,[n(C,{text:"info",size:"small",bg:"",class:"mb-[6px]",style:lt({cursor:(k=(f=a.value)==null?void 0:f.ques_info)!=null&&k.parent_ques_id?"pointer":"text"}),onClick:e[0]||(e[0]=_=>E.value=!E.value)},{default:u(()=>{var _,q;return[t("span",pt,[vt,(q=(_=a.value)==null?void 0:_.ques_info)!=null&&q.parent_ques_id?(l(),i(S,{key:0},[E.value?(l(),F(o,{key:1},{default:u(()=>[n(m)]),_:1})):(l(),F(o,{key:0},{default:u(()=>[n(h)]),_:1}))],64)):g("",!0)])]}),_:1},8,["style"]),E.value&&((Y=(R=a.value)==null?void 0:R.ques_info)!=null&&Y.parent_ques_id)?(l(),i("div",ht,[(ee=(Z=a.value)==null?void 0:Z.ques_info)!=null&&ee.parent_ques_order?(l(),i("div",mt,c((se=(te=a.value)==null?void 0:te.ques_info)==null?void 0:se.parent_ques_order)+"． ",1)):g("",!0),(ne=(le=(ae=a.value)==null?void 0:ae.ques_info)==null?void 0:le.ques_material)!=null&&ne.html?(l(),i("div",{key:1,innerHTML:xe(ke)(a.value.ques_info.ques_material.html)},null,8,ft)):g("",!0)])):g("",!0),t("div",gt,[(ie=(oe=a.value)==null?void 0:oe.ques_info)!=null&&ie.ques_order?(l(),i("div",yt,c((ce=(re=a.value)==null?void 0:re.ques_info)==null?void 0:ce.ques_order)+"． ",1)):g("",!0),(de=(ue=(_e=a.value)==null?void 0:_e.ques_info)==null?void 0:ue.ques_desc)!=null&&de.html?(l(),i("div",{key:1,innerHTML:xe(ke)(a.value.ques_info.ques_desc.html)},null,8,bt)):g("",!0)])]),t("div",qt,[n(C,{text:"info",size:"small",bg:"",style:{cursor:"text"}},{default:u(()=>[b("评分标准")]),_:1}),t("div",wt,[t("div",kt,[b(" 本题分值： "),n(r,{style:{"margin-right":"14px"},type:"primary"},{default:u(()=>{var _;return[b(c((_=a.value.ques_info)==null?void 0:_.total_score),1)]}),_:1}),((pe=a.value.ques_info)==null?void 0:pe.ques_type_code)==="D"?(l(),i(S,{key:0},[b(" 总权重： "),n(r,{type:"primary"},{default:u(()=>{var _,q;return[b(c(((_=a.value.ques_info)==null?void 0:_.total_weight)!==""?(q=a.value.ques_info)==null?void 0:q.total_weight:"暂无数据"),1)]}),_:1})],64)):g("",!0)])]),t("div",xt,[t("div",Ct,[b(" 评分规则： "),(he=(ve=a.value.ques_info)==null?void 0:ve.e_mark_rule)!=null&&he.length?(l(),i("span",St,c((me=a.value.ques_info)==null?void 0:me.e_mark_rule),1)):(l(),i("span",Bt,"无"))])]),t("div",Dt,[(l(!0),i(S,null,P(a.value.answer_info_list,(_,q)=>{var fe,ge,ye,be,qe;return l(),i("div",{class:Ce(["detail-item","msg",{"detail-item-active":q===I.value}]),key:q,onClick:we=>De(_,q)},[t("div",{class:Ce(["t",{active:q===I.value}])},[((fe=a.value.ques_info)==null?void 0:fe.ques_type_code)==="D"?(l(),i("div",Vt,"第"+c(Ee(_.small_ques_order))+"空 ",1)):((ge=a.value.ques_info)==null?void 0:ge.ques_type_code)==="E"?(l(),i("div",Ht,"第"+c(_.small_ques_order)+"小题",1)):g("",!0),t("div",null,[b(" 分值："+c(_.space_score)+"    ",1),((ye=a.value.ques_info)==null?void 0:ye.ques_type_code)==="D"?(l(),i(S,{key:0},[b(" 权重："+c(_.space_weight),1)],64)):g("",!0)])],2),t("div",Nt,[((be=a.value.ques_info)==null?void 0:be.ques_type_code)==="D"?(l(),i("div",Tt,"标准答案："+c(_.space_answer),1)):((qe=a.value.ques_info)==null?void 0:qe.ques_type_code)==="E"?(l(),i(S,{key:1},[t("div",zt,[Mt,t("div",null,"分值权重："+c(_.point_weight.join(":")),1)]),(l(!0),i(S,null,P(_.mark_point,(we,Pe)=>(l(),i("div",null,[t("span",null,c(Pe+1)+"、",1),t("span",{style:{"white-space":"break-spaces"},innerHTML:we},null,8,It)]))),256))],64)):g("",!0)])],10,Ot)}),128))])])]}),_:1},8,["height"])]),_:1}),n(X,{class:"third-div",span:16,style:{"padding-left":"8px","border-right":"none"}},{default:u(()=>[t("div",Et,[Lt,t("div",{class:"cursor-pointer",onClick:Me},[n(o,null,{default:u(()=>[n(Re)]),_:1})])]),t("div",Rt,[n(je,{style:{width:"320px"},modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=f=>p.value=f),onTabChange:Be},{default:u(()=>[n(L,{label:"已定标",name:"2"}),n(L,{label:"定标异常",name:"3"}),n(L,{label:"重新定标",name:"4"}),n(L,{label:"未定标",name:"1"})]),_:1},8,["modelValue"]),t("div",jt,[["2","3"].includes(p.value)?(l(),F(C,{key:0,type:"primary",onClick:e[2]||(e[2]=f=>J("batch"))},{default:u(()=>[b(" 批量重新定标 ")]),_:1})):g("",!0),n(C,{type:"primary",onClick:Te},{default:u(()=>[b(" 筛选 ")]),_:1})])]),n($e,{minHeight:y.value.styleOptions.minHeight,"table-options":y.value,"table-data":M.value,onOnHandleSelectionChange:Oe,onOnHandleSizeChange:Ve,onOnHandleCurrentChange:He},null,8,["minHeight","table-options","table-data"]),g("",!0)]),_:1})]),_:1})]),_:1})]),n(Fe,{modelValue:$.value,"onUpdate:modelValue":e[5]||(e[5]=f=>$.value=f),title:"评析"},{default:u(()=>[$t,t("div",Wt,c(B.value.answer_cluster),1),t("div",At,[Ft,t("div",null,"试题分数："+c(B.value.ques_score),1)]),t("div",Pt,[Ut,t("div",null,"考生得分："+c(B.value.ai_score),1)]),t("div",Qt,[Gt,t("div",null,c(p.value==="3"?"失败原因":"评价解析"),1)]),p.value==="3"?(l(),i("div",Jt,c(B.value.mark_fail_reason),1)):p.value==="2"?(l(),i("div",Kt,[(l(!0),i(S,null,P(B.value.ai_answer_parse,(f,k)=>(l(),i("div",null,c(k+1)+"、"+c(f),1))),256))])):g("",!0)]),_:1},8,["modelValue"]),n(Ue,{ref_key:"filterRef",ref:z,onSearchData:G},null,512),n(Ke,{ref_key:"manualRef",ref:U,onSearchData:G},null,512)])}}}),ls=it(Xt,[["__scopeId","data-v-6225d45a"]]);export{ls as default};
