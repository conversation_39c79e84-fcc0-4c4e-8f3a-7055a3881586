import{_ as F}from"./TinymceEditor.vue_vue_type_style_index_0_lang-Dj-6iP6L.js";import{a as Te}from"./convertNumber-CmbNKqvY.js";import{g as Re}from"./questionEdit-C5KtQM0G.js";import{d as Me,aO as ze,l as A,n as Ge,r as V,o,c as u,b as a,h as r,g as h,f as G,t as D,y as m,F as g,p as y,e as d,u as U,q as me,d5 as P,d6 as fe,d7 as je,aV as he,ac as Qe,ad as We,_ as Pe}from"./index-B63pSD2p.js";import"./util-DBFSI-P4.js";const O=I=>(Qe("data-v-b6c8542c"),I=I(),We(),I),He={key:0},Je={key:0,class:"ques-editor-input"},Ke={key:1,class:"ques-editor-input"},Xe={class:"ques-editor-input"},Ye={key:0,class:"ques-editor-input"},Ze={key:1,class:"ques-editor-input"},el={style:{display:"flex","align-items":"center"}},ll=O(()=>d("span",null,"答案分组",-1)),tl=O(()=>d("span",null,"：",-1)),ol={class:"ques-editor-input"},sl={key:0,class:"point-box"},al=O(()=>d("div",{class:"tit-box"},"评分标准：",-1)),ul={class:"point-form"},nl={class:"form-point-box"},rl={key:1},dl={key:2,class:"point-box"},_l=O(()=>d("div",{class:"tit-box"},"评分步骤：",-1)),pl={class:"step-list-wrapper"},il={class:"step-item"},cl={class:"step-content"},vl={class:"order-id"},ml=O(()=>d("span",{class:"add-text"},"添加步骤",-1)),fl={class:"step-weight-setting"},hl=O(()=>d("div",{class:"tit-box"},"步骤评分设置",-1)),gl={class:"tit-box total-weight-content"},Vl=O(()=>d("span",null,"总权重：",-1)),yl={class:"total-weight"},ql={class:"weight-options"},bl={class:"weight-item"},wl={key:0,class:"setting-item"},kl={key:0,class:"weight-input"},Cl=O(()=>d("div",null,"权重",-1)),Ul=["onClick"],Sl=["onClick"],xl={key:1,class:"note-text"},Dl=O(()=>d("span",{class:"add-text"},"添加步骤权重条件",-1)),Ol=Me({name:"question-edit",__name:"question-edit",props:{questionDesc:{},questionDescModifiers:{}},emits:["update:questionDesc"],setup(I){const e=ze(I,"questionDesc"),ge=["正确","错误"],Ve=["1","0"],ye=A(null),H=[{label:"单一步骤",value:1},{label:"满足以下所有步骤才得分",value:2},{label:"满足以下任一步骤即得分",value:3}],$=A([]),L=A([]),z=A([]),k=A([]);let q=A([]);const qe=()=>{for(const t of e.value.op_step_list){q.value.push(null),$.value.push(t.step_score_gene!=null);for(const l of t.op_step_group)L.value.push({label:l.order_id,value:l.order_id})}};Ge(()=>{e.value.ques_type_code==="G"&&(qe(),k.value=[],e.value.showStepOrderIdList=k,e.value.stepNoteTextList=q)});const be=()=>{let t=0;for(const l of e.value.op_step_list)l.step_score_gene!==null&&(t+=l.step_score_gene);return e.value.op_total_score_gene=t,t},J=(t,l)=>{t.logic===1?e.value.op_score_rule[l].length>1?q.value[l]="只允许选择一个步骤序号":e.value.op_score_rule[l].length===0?q.value[l]="请至少选择一个步骤序号":q.value[l]=null:e.value.op_score_rule[l].length<=1?q.value[l]="请至少选择两个步骤序号":q.value[l]=null},we=(t,l)=>{for(const s of H)t.logic==s.value&&(t.logic_text=s.label);J(t,l)},ke=(t,l)=>{z.value=[];for(const s of e.value.op_score_rule)for(const c of s)z.value.push({label:c,value:c});Ce(L,z),J(t,l)},Ce=(t,l)=>{const s=t.value.filter(v=>!l.value.some(b=>b.value===v.value)),c=l.value.filter(v=>!t.value.some(b=>b.value===v.value));k.value=[...s,...c]},K=()=>{let t=0;for(const l of e.value.op_step_list)for(const s of l.op_step_group)s.order_id>t&&(t=s.order_id);return t},Ue=()=>{let t=!0,l;for(const s of e.value.op_step_list)if(!s.show){l=s.op_step_group[0].order_id,s.show=!0,t=!1;break}if(t){l=K()+1;const s={logic:1,logic_text:"单一步骤",step_score_gene:0,group_score:0,is_score:1,show:!0,op_step_group:[{auto_id:null,order_id:l,description:"",is_score:1,step_score_gene:0}]};e.value.op_step_list.push(s)}L.value.push({label:l,value:l}),k.value.push({label:l,value:l}),q.value.push(null),$.value.push(!0)},Se=(t,l,s)=>{he.confirm("确定删除该评分步骤描述吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{e.value.op_score_rule=e.value.op_score_rule.map(v=>v.filter(b=>b!==t)).filter(v=>v.length>0),e.value.op_step_list[l].op_step_group.length>1?(e.value.op_step_list[l].op_step_group.splice(s,1),e.value.op_step_list[l].op_step_group.length==1&&(e.value.op_step_list[l].op_step_group[0].logic=1,e.value.op_step_list[l].op_step_group[0].logic_text="单一步骤")):(e.value.op_step_list.splice(l,1),q.value.pop()),L.value=L.value.filter(v=>v.value!==t),k.value=k.value.filter(v=>v.value!==t);let c=0;for(const v of e.value.op_step_list)for(const b of v.op_step_group){const T=b.order_id;b.order_id=++c,e.value.op_score_rule=e.value.op_score_rule.map(S=>S.map(w=>w===T?c:w)),L.value=L.value.map(S=>S.value===T?{label:c,value:c}:S),k.value=k.value.map(S=>S.value===T?{label:c,value:c}:S)}})},xe=t=>{$.value[t]=!$.value[t],e.value.op_step_list[t].is_score=$.value[t]?1:0},De=()=>{if(e.value.op_step_list.length<e.value.op_score_rule.length){const l={logic:1,logic_text:"单一步骤",step_score_gene:0,group_score:0,is_score:1,show:!1,op_step_group:[{auto_id:null,order_id:K()+1,description:"",is_score:1,step_score_gene:0}]};e.value.op_step_list.push(l)}else e.value.op_score_rule.push([])},Oe=t=>{const l=t.step_score_gene;for(const s of t.op_step_group)s.step_score_gene=l},Be=t=>{he.confirm("确定删除该权重条件吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{for(const l of e.value.op_score_rule[t])z.value.filter(s=>s.value!==l),k.value.push({label:l,value:l});e.value.op_score_rule.splice(t,1),q.value.splice(t,1),$.value.splice(t,1),e.value.op_step_list[t].step_score_gene=0})},Fe=t=>{if(!t)return;const l=t.indexOf("（");return l!==-1?t.slice(l,t.length):t+"．"},$e=()=>{var t,l,s;if(((t=e.value.weight)==null?void 0:t.length)>0){const c=parseFloat((s=(l=e.value)==null?void 0:l.weight[0])==null?void 0:s.reduce((v,b)=>Number(v)+Number(b),0).toFixed(3));return e.value.weight[1]=[c],c}else return null},Le=()=>{let t={point:"",score:1};e.value.ques_mark_point||(e.value.ques_mark_point=[]),e.value.ques_mark_point.push(t)},Ne=t=>{e.value.ques_mark_point.splice(t,1)},j=(t,l)=>{if(l==="01")e.value.ques_choices.forEach(s=>{s.code===t&&(e.value.standard_answer=[s.options.slice(0,1)],e.value.standard_answer_html=[s.options.slice(0,1)])});else if(l==="02")e.value.ques_choices.forEach(s=>{s.code===t&&(e.value.standard_answer=[s.code],e.value.standard_answer_html=[s.code])});else if(l==="03"){let s=[];e.value.ques_choices.forEach(c=>{t.forEach(v=>{c.code===v&&s.push(c.options.slice(0,1))})}),e.value.standard_answer=s,e.value.standard_answer_html=s}};return(t,l)=>{var ee,le,te,oe;const s=V("el-form-item"),c=V("el-radio"),v=V("el-radio-group"),b=V("el-checkbox"),T=V("el-checkbox-group"),S=V("InfoFilled"),w=V("el-icon"),X=V("el-tooltip"),R=V("el-input"),Q=V("el-input-number"),Ee=V("el-form"),Ae=V("Delete"),Ie=V("question-edit",!0),Y=V("el-option"),Z=V("el-select");return e.value?(o(),u("div",He,[a(Ee,{ref_key:"formRef",ref:ye,model:e.value,"label-position":"right","label-width":"100px"},{default:r(()=>{var p,i,n,B,M,se,ae,ue,ne,re,de,_e,pe,ie,ce;return[e.value.ques_order&&!e.value.ques_order.includes("（")?(o(),h(s,{key:0,label:"试题编号："},{default:r(()=>[G(D(e.value.ques_code),1)]),_:1})):m("",!0),a(s,{label:`${Fe((p=e.value)==null?void 0:p.ques_order)}`,prop:"paper_code"},{default:r(()=>{var f,C,N;return[(f=e.value)!=null&&f.ques_desc.html?(o(),u("div",Je,[a(F,{modelValue:e.value.ques_desc.html,"onUpdate:modelValue":l[0]||(l[0]=E=>e.value.ques_desc.html=E)},null,8,["modelValue"])])):!((C=e.value)!=null&&C.ques_desc.html)&&((N=e.value)!=null&&N.ques_desc.text)?(o(),u("div",Ke,[a(F,{modelValue:e.value.ques_desc.text,"onUpdate:modelValue":l[1]||(l[1]=E=>e.value.ques_desc.text=E)},null,8,["modelValue"])])):m("",!0)]}),_:1},8,["label"]),((i=e.value)==null?void 0:i.ques_type_code)==="A"||((n=e.value)==null?void 0:n.ques_type_code)==="C"?(o(!0),u(g,{key:1},y(e.value.ques_choices,f=>(o(),h(s,{label:`${f.options.substring(0,1)}．`,prop:f.options},{default:r(()=>[d("div",Xe,[a(F,{ref_for:!0,ref:"editorRef",modelValue:f.optionShow,"onUpdate:modelValue":C=>f.optionShow=C},null,8,["modelValue","onUpdate:modelValue"])])]),_:2},1032,["label","prop"]))),256)):m("",!0),((B=e.value)==null?void 0:B.ques_type_code)!=="F"&&((M=e.value)==null?void 0:M.ques_type_code)!=="G"?(o(),h(s,{key:2,label:"参考答案："},{default:r(()=>{var f,C,N,E,ve;return[((f=e.value)==null?void 0:f.ques_type_code)==="A"?(o(),h(v,{key:0,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":l[2]||(l[2]=_=>e.value.standard_answer[0]=_),onChange:l[3]||(l[3]=(..._)=>j(..._,e.value.ques_type_code==="A"?"01":"02"))},{default:r(()=>[(o(!0),u(g,null,y(e.value.ques_choices,(_,x)=>(o(),h(c,{value:U(Te)("A",x+1)},{default:r(()=>[G(D(e.value.ques_type_code==="A"?_.options.substring(0,1):_.options.substring(0,2)),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])):m("",!0),((C=e.value)==null?void 0:C.ques_type_code)==="B"?(o(),h(v,{key:1,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":l[4]||(l[4]=_=>e.value.standard_answer[0]=_),onChange:l[5]||(l[5]=(..._)=>j(..._,e.value.ques_type_code==="A"?"01":"02"))},{default:r(()=>[(o(),u(g,null,y(ge,(_,x)=>a(c,{value:Ve[x]},{default:r(()=>[G(D(_),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"])):((N=e.value)==null?void 0:N.ques_type_code)==="C"?(o(),h(T,{key:2,modelValue:e.value.standard_choices_code,"onUpdate:modelValue":l[6]||(l[6]=_=>e.value.standard_choices_code=_),onChange:l[7]||(l[7]=(..._)=>j(..._,"03"))},{default:r(()=>[(o(!0),u(g,null,y(e.value.ques_choices,_=>(o(),h(b,{value:_.code,label:_.options.substring(0,1)},null,8,["value","label"]))),256))]),_:1},8,["modelValue"])):e.value.ques_type_code==="D"?(o(),u(g,{key:3},[((E=e.value.standard_answer_html)==null?void 0:E.length)>0?(o(!0),u(g,{key:0},y(e.value.standard_answer_html,(_,x)=>(o(),u("div",{class:"ques-editor-input filling-input-box",style:me({width:`${1/e.value.standard_answer_html.length*100-.3}%`})},[a(F,{modelValue:e.value.standard_answer_html[x],"onUpdate:modelValue":W=>e.value.standard_answer_html[x]=W},null,8,["modelValue","onUpdate:modelValue"])],4))),256)):(o(!0),u(g,{key:1},y(e.value.standard_answer,(_,x)=>(o(),u("div",{class:"ques-editor-input filling-input-box",style:me({width:`${1/e.value.standard_answer.length*100-.3}%`})},[a(F,{modelValue:e.value.standard_answer[x],"onUpdate:modelValue":W=>e.value.standard_answer[x]=W},null,8,["modelValue","onUpdate:modelValue"])],4))),256))],64)):e.value.ques_type_code==="E"?(o(),u(g,{key:4},[((ve=e.value.standard_answer_html)==null?void 0:ve.length)>0?(o(),u("div",Ye,[a(F,{modelValue:e.value.standard_answer_html[0],"onUpdate:modelValue":l[8]||(l[8]=_=>e.value.standard_answer_html[0]=_)},null,8,["modelValue"])])):(o(),u("div",Ze,[a(F,{modelValue:e.value.standard_answer[0],"onUpdate:modelValue":l[9]||(l[9]=_=>e.value.standard_answer[0]=_)},null,8,["modelValue"])]))],64)):m("",!0)]}),_:1})):m("",!0),e.value.ques_type_code==="D"?(o(),h(s,{key:3,label:"答案分组："},{label:r(()=>[d("div",el,[ll,a(X,{content:U(Re)},{default:r(()=>[a(w,null,{default:r(()=>[a(S)]),_:1})]),_:1},8,["content"]),tl])]),default:r(()=>[a(R,{type:"text",modelValue:e.value.d_out_of_order_group,"onUpdate:modelValue":l[10]||(l[10]=f=>e.value.d_out_of_order_group=f),class:"input-margin",placeholder:"请输入答案分组",autosize:""},null,8,["modelValue"])]),_:1})):m("",!0),((se=e.value)==null?void 0:se.ques_type_code)!=="F"?(o(),h(s,{key:4,label:"难易程度：",prop:(ae=e.value)==null?void 0:ae.ques_difficulty},{default:r(()=>[a(R,{modelValue:e.value.ques_difficulty,"onUpdate:modelValue":l[11]||(l[11]=f=>e.value.ques_difficulty=f),placeholder:"请输入难易程度",autosize:""},null,8,["modelValue"])]),_:1},8,["prop"])):m("",!0),((ue=e.value)==null?void 0:ue.ques_type_code)!=="F"&&((ne=e.value)==null?void 0:ne.ques_type_code)!=="G"?(o(),h(s,{key:5,label:"答案解析：",prop:(re=e.value)==null?void 0:re.standard_parse},{default:r(()=>[d("div",ol,[a(F,{ref:"editorRef",modelValue:e.value.standard_parse,"onUpdate:modelValue":l[12]||(l[12]=f=>e.value.standard_parse=f)},null,8,["modelValue"])])]),_:1},8,["prop"])):m("",!0),e.value.total_score!=null?(o(),h(s,{key:6,label:"试题分数："},{default:r(()=>[d("span",null,D(e.value.total_score)+"分",1)]),_:1})):m("",!0),((de=e.value)==null?void 0:de.ques_type_code)==="E"||((_e=e.value)==null?void 0:_e.ques_type_code)==="D"?(o(),h(s,{key:7,label:"评分规则：",prop:e.value.e_mark_rule},{default:r(()=>[a(R,{type:"textarea",modelValue:e.value.e_mark_rule,"onUpdate:modelValue":l[13]||(l[13]=f=>e.value.e_mark_rule=f),placeholder:"请输入评分规则",autosize:""},null,8,["modelValue"])]),_:1},8,["prop"])):m("",!0),((pe=e.value)==null?void 0:pe.ques_type_code)==="D"&&((ce=(ie=e.value)==null?void 0:ie.weight)==null?void 0:ce.length)>0?(o(),h(s,{key:8,label:"权    重：",prop:e.value.weight[0]},{default:r(()=>[(o(!0),u(g,null,y(e.value.weight[0],(f,C)=>(o(),h(Q,{min:.1,type:"input",modelValue:e.value.weight[0][C],"onUpdate:modelValue":N=>e.value.weight[0][C]=N,class:"input-margin mr-[5px]"},null,8,["modelValue","onUpdate:modelValue"]))),256)),d("span",null,"（总权重："+D($e())+"）",1)]),_:1},8,["prop"])):m("",!0)]}),_:1},8,["model"]),((ee=e.value)==null?void 0:ee.ques_type_code)==="E"?(o(),u("div",sl,[al,d("div",ul,[(o(!0),u(g,null,y(e.value.ques_mark_point,(p,i)=>(o(),u("div",nl,[d("div",null,D(i+1)+"．",1),a(R,{type:"text",modelValue:p.point,"onUpdate:modelValue":n=>p.point=n,class:"input-margin",placeholder:"请输入评分标准",autosize:""},null,8,["modelValue","onUpdate:modelValue"]),a(Q,{min:.1,modelValue:p.score,"onUpdate:modelValue":n=>p.score=n,class:"input-margin"},null,8,["modelValue","onUpdate:modelValue"]),G(" 分 "),a(w,{class:"point-icon-box point-del",onClick:n=>Ne(i)},{default:r(()=>[a(Ae)]),_:2},1032,["onClick"])]))),256)),d("div",null,[a(w,{class:"point-icon-box",onClick:l[14]||(l[14]=p=>Le())},{default:r(()=>[a(U(P))]),_:1})])])])):m("",!0),((le=e.value)==null?void 0:le.ques_type_code)==="F"?(o(),u("div",rl,[(o(!0),u(g,null,y((te=e.value)==null?void 0:te.children,(p,i)=>(o(),u("div",null,[a(Ie,{questionDesc:e.value.children[i],"onUpdate:questionDesc":n=>e.value.children[i]=n},null,8,["questionDesc","onUpdate:questionDesc"])]))),256))])):m("",!0),((oe=e.value)==null?void 0:oe.ques_type_code)==="G"?(o(),u("div",dl,[_l,d("div",pl,[(o(!0),u(g,null,y(e.value.op_step_list,(p,i)=>(o(),u("div",null,[p.show?(o(!0),u(g,{key:0},y(p.op_step_group,(n,B)=>(o(),u("div",il,[d("div",cl,[d("span",vl,D(n.order_id),1),a(R,{class:"description",modelValue:n.description,"onUpdate:modelValue":M=>n.description=M},null,8,["modelValue","onUpdate:modelValue"]),a(w,{class:"remove-btn",color:"#aeaeae",onClick:M=>Se(n.order_id,i,B)},{default:r(()=>[a(U(fe))]),_:2},1032,["onClick"])])]))),256)):m("",!0)]))),256)),d("div",{class:"add-step-wrapper",onClick:Ue},[a(w,{color:"#1f8bff"},{default:r(()=>[a(U(P))]),_:1}),ml]),d("div",fl,[hl,d("div",gl,[Vl,d("span",yl,D(be()),1)]),d("div",ql,[(o(!0),u(g,null,y(e.value.op_step_list,(p,i)=>(o(),u("div",bl,[e.value.op_score_rule.length>i?(o(),u("div",wl,[a(Z,{style:{width:"220px"},modelValue:p.logic,"onUpdate:modelValue":n=>p.logic=n,placeholder:"请选择评分步骤类型",disabled:!p.is_score,onChange:n=>we(p,i)},{default:r(()=>[(o(),u(g,null,y(H,(n,B)=>a(Y,{key:B,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"]),a(Z,{style:{width:"220px"},modelValue:e.value.op_score_rule[i],"onUpdate:modelValue":n=>e.value.op_score_rule[i]=n,multiple:"",placeholder:"请选择评分步骤序号","no-data-text":"所有评分步骤均已被选择",onChange:n=>ke(p,i)},{default:r(()=>[(o(!0),u(g,null,y(k.value,(n,B)=>(o(),h(Y,{key:B,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),$.value[i]?(o(),u("div",kl,[Cl,a(Q,{"controls-position":"right",modelValue:p.step_score_gene,"onUpdate:modelValue":n=>p.step_score_gene=n,onChange:n=>Oe(p)},null,8,["modelValue","onUpdate:modelValue","onChange"])])):m("",!0),d("div",{class:"btn-content",onClick:n=>xe(i)},[a(X,{content:"点击切换是否参与评分",placement:"right"},{default:r(()=>[a(w,{color:"#aeaeae"},{default:r(()=>[a(U(je))]),_:1})]),_:1})],8,Ul),d("div",{class:"btn-content",onClick:n=>Be(i)},[a(w,{color:"#aeaeae"},{default:r(()=>[a(U(fe))]),_:1})],8,Sl)])):m("",!0),U(q)[i]!=null?(o(),u("div",xl,D(U(q)[i]),1)):m("",!0)]))),256))]),d("div",{class:"add-step-wrapper",onClick:De},[a(w,{color:"#1f8bff"},{default:r(()=>[a(U(P))]),_:1}),Dl])])])])):m("",!0)])):m("",!0)}}}),El=Pe(Ol,[["__scopeId","data-v-b6c8542c"]]);export{El as default};
