const a=t=>{let n=[];return t.forEach(r=>{var e,i;(e=r.children)!=null&&e.length&&(r.ques_mark_point=r.ques_mark_point.concat(a(r.children))),(i=r.ques_mark_point)!=null&&i.length&&(n=n.concat(r.ques_mark_point))}),n},h=t=>{var n;if((n=t==null?void 0:t.children)!=null&&n.length)t.children.forEach(r=>{var e;(e=r.children)!=null&&e.length&&(r.ques_mark_point=a(r.children))});else{let r=JSON.parse(JSON.stringify(t));t.children=[r],t.noChildren=!0}return t};export{h};
