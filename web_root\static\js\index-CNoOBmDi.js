import f from"./homepage-tabs-KBTJlgKv.js";import b from"./index-Bd-gjKVb.js";import _ from"./index-Q1kJJEEC.js";import h from"./index-CXJAwNKD.js";import{d as k,aC as v,n as x,I as g,E as D,r as G,o as i,c as d,e as p,g as C,h as R,b as l,u as y,s as S,_ as j}from"./index-B63pSD2p.js";import"./index-aLSsAUpX.js";import"./useTag-CrO3NxXA.js";import"./basic-monitor-jamMEtXh.js";import"./table-monitor-D2MxPGP1.js";import"./barChart-BwsIMmQR.js";import"./processChart-BH-8wiYU.js";import"./index-C9GYnvBh.js";import"./scoreRelateLineChart-DOiNxsyg.js";const B={class:"zf-first-box data-monitor"},U={class:"zf-second-box"},q={class:"tabs-cont"},w={key:1,class:"noData dark:!bg-black eye-box"},I=k({__name:"index",setup(z){const u=v(),{roles:r}=u,m=r.includes("1")?"1":r.includes("7")?"7":r.includes("5")?"5":"";x(()=>{var t,c;const s=(c=((t=g().flatteningRoutes.find(o=>o.path==="/data-statistics"))!=null?t:{}).children)!=null?c:[];s.find(o=>o.name==="marking-group-dashboard")?a("markingGroupDashboard"):s.find(o=>o.name==="project-group-dashboard")?a("projectGroupDashboard"):s.find(o=>o.name==="question-group-dashboard")?a("questionGroupDashboard"):a("null")});const e=D(null);function a(n){switch(n){case"markingGroupDashboard":e.value=b;break;case"projectGroupDashboard":e.value=_;break;case"questionGroupDashboard":e.value=h;break;default:e.value=null;break}}return(n,s)=>{const t=G("el-empty");return i(),d("div",B,[p("div",U,[e.value?(i(),C(S(e.value),{key:0},{tab:R(()=>[p("div",q,[l(f,{maxUserRole:y(m)},null,8,["maxUserRole"])])]),_:1})):(i(),d("div",w,[l(t,{description:"暂无数据"})]))])])}}}),O=j(I,[["__scopeId","data-v-efe56907"]]);export{O as default};
