import{aQ as V,aR as w,d as B,l as p,P,ao as C,r as d,o as n,g,h as D,e as c,f as T,c as _,t as N,y as q,_ as S}from"./index-B63pSD2p.js";const U=(a,r)=>V.request("post",w(`${r}`),{data:a},{},!1),$={class:"zf-dialog-first-box"},z={class:"zf-dialog-second-box"},R={class:"step-box"},j={key:0},E={key:1},F=B({__name:"index",props:{title:{type:String,default:"进度"},apiInfo:{type:Object,default:()=>{}},internalTime:{type:Number,default:5e3}},emits:["queryData","initStart"],setup(a,{expose:r,emit:f}){const m=p(!0),v=f,i=a,o=p(!1),e=P({percentage:0,success_count:0,total:0});let s=p(null);const h=l=>{o.value=!0,x()},x=()=>{var l;s.value&&clearInterval(s.value),s.value=setInterval(()=>{U(i.apiInfo.params,i.apiInfo.url).then(t=>{t.code&&t.code===200&&(e.percentage=t.data.progress,e.success_count=t.data.success_count,e.total=t.data.total_count,t.data.progress===100&&setTimeout(()=>{u()},1e3))})},(l=i.internalTime)!=null?l:5e3)},b=()=>{e.percentage=100,setTimeout(()=>{u()},300)},u=()=>{o.value=!1,e.percentage=0,e.total=0,e.success_count=0,s.value&&clearInterval(s.value),v("queryData")};return C(()=>{s.value&&clearInterval(s.value)}),r({openDialog:h,manualClose:b}),(l,t)=>{const I=d("el-progress"),y=d("el-dialog");return n(),g(y,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=k=>o.value=k),title:a.title,width:"40%","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1,"before-close":u,"show-close":!1,draggable:""},{default:D(()=>[c("div",$,[c("div",z,[c("div",R,[c("p",null,[T(" 总进度： "),e.total?(n(),_("span",j,N(`${e.success_count} / ${e.total}`),1)):(n(),_("span",E,"加载中..."))])]),m.value?(n(),g(I,{key:0,class:"progress-box","text-inside":!0,"stroke-width":15,striped:"",percentage:e.percentage,status:"success"},null,8,["percentage"])):q("",!0)])])]),_:1},8,["modelValue","title"])}}}),L=S(F,[["__scopeId","data-v-b65e9c81"]]),Q=L;export{Q as P};
