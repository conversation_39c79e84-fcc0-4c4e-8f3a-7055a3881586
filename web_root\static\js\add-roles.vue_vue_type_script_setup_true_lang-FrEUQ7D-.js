import{c as N,b as k}from"./roles-management-DlteFevS.js";import{C as E}from"./validate-Dc6ka3px.js";import{d as O,l as t,P as R,r as f,o as T,g as B,h as u,e as p,b as m,f as V,C as z,T as s}from"./index-B63pSD2p.js";const J={class:"zf-dialog-first-box"},P={class:"zf-dialog-second-box"},S={class:"footer-btn"},I=O({__name:"add-roles",emits:["queryData"],setup(U,{expose:x,emit:y}){const g=y,c=t("添加角色"),n=t(!1),_=t({}),v=R({column:3,labelWidth:"108px",itemWidth:"250px",rules:{role_name:[{required:!0,message:"请输入角色名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(o,e,l)=>{if(E(e)){if(e.length>40)return l(new Error("角色名称不能超过40个字！"));l()}else return l(new Error("请输入中文！"))}}],role_desc:[{trigger:["blur","change"],validator:(o,e,l)=>{if(e&&e.length>255)return l(new Error("描述长度不能超过255！"));l()}}]},fields:[{label:"角色名称",prop:"role_name",type:"input",defaultValue:"",placeholder:"请输入角色名称",isHidden:!1,clearable:!0},{label:"描述",prop:"role_desc",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入描述"}]}),a=t(null),h=t({}),r=t("01"),C=(o,e)=>{n.value=!0,r.value=o,r.value==="01"?c.value="添加角色":r.value==="02"&&(c.value="编辑角色",h.value=e,z(()=>{v.fields.map(l=>{e.hasOwnProperty(l.prop)&&a.value.setCardData(l.prop,e[l.prop])})}))},i=()=>{n.value=!1,a.value.resetFieldsFn()},w=()=>{a.value.formValidate().then(()=>{let o=JSON.parse(JSON.stringify(a.value.getAllCardData()));r.value==="01"?D(o):r.value==="02"&&(o.role_id=h.value.role_id,F(o))}).catch(()=>{s.warning("请按要求填写！")})},D=o=>{N(o).then(e=>{e.code&&e.code===200?(s.success(e.msg),g("queryData"),i()):s.warning(e.msg)})},F=o=>{k(o).then(e=>{e.code&&e.code===200?(s.success(e.msg),g("queryData"),i()):s.warning(e.msg)})};return x({openDialog:C}),(o,e)=>{const l=f("form-component"),b=f("el-button"),q=f("el-dialog");return T(),B(q,{modelValue:n.value,"onUpdate:modelValue":e[1]||(e[1]=d=>n.value=d),title:c.value,width:"440px","align-center":"","close-on-click-modal":!1,"before-close":i,draggable:""},{footer:u(()=>[p("div",S,[m(b,{onClick:i},{default:u(()=>[V("取消")]),_:1}),m(b,{type:"primary",onClick:w},{default:u(()=>[V("确定")]),_:1})])]),default:u(()=>[p("div",J,[p("div",P,[m(l,{ref_key:"formRef",ref:a,modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=d=>_.value=d),"form-options":v,"is-query-btn":!1},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}});export{I as _};
