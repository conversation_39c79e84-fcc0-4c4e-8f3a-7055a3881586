import{d as M,l as t,P as q,n as E,r as g,j as F,w as S,o as N,c as B,e as c,b as f,h as Q,f as Y,u as j,z as J,C as L,T as W,_ as U}from"./index-B63pSD2p.js";import G from"./historical-ques-ov7VZ4qN.js";import{e as K}from"./manual-B17ad5iK.js";import"./test-question-CNvwcOEo.js";import"./question-info-CiM3dtIw.js";import"./handleImages-D-nd439N.js";import"./op-mark-step-DW83lcNi.js";const X={class:"history-content"},Z={style:{width:"100%",height:"100%"}},$={class:"query-condition-box"},ee={class:"history-query"},te=M({__name:"historical-record",props:["taskInfo","pathArr"],emits:["showHistory"],setup(d,{expose:C,emit:D}){const l=d,b=D,a=t(!1),y=t(null),k=t(null),n=t(null),w=t("550px"),u=t(!1),O=t({}),T=q({column:3,itemWidth:"100%",inline:!0,rules:{},fields:[{label:"考生密号",prop:"stu_secret_num",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入考生密号"},{label:"评分时间",prop:"markTime",type:"daterange",placeholder:"请选择评分时间",defaultValue:"",valueFormat:"YYYY-MM-DD",clearable:!0,popperOption:{modifiers:[{name:"flip",options:{fallbackPlacements:["bottom"],allowedAutoPlacements:["bottom"]}}]}}]}),o=t({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"230px",type:"template",showOverflowTooltip:!1,clickBtn:e=>{P(e)}},{prop:"created_time",label:"评分时间",width:"160px"},{prop:"expert_mark_score",label:()=>{const{path:e}=l.taskInfo;return e===l.pathArr[2]?"结果":"得分"},width:"56px"}],styleOptions:{isShowSort:!1},pageOptions:{layout:"total, sizes, ->, prev, pager, next",pageSizes:[20,50,100,200,500],isShowPage:!0,currentPage:1,pageSize:20,total:0}});let h=t([]),x=null;E(()=>{x=new ResizeObserver(e=>{e.forEach(i=>{n.value&&H()})}),n.value&&x.observe(n.value)});const A=()=>{a.value?(a.value=!1,b("showHistory",a.value)):(a.value=!0,L(()=>{H()}),r(),b("showHistory",a.value))},H=()=>{w.value=window.innerHeight-n.value.offsetHeight-234+"px"},I=()=>{a.value&&r()},r=()=>{u.value=!0;let e=JSON.parse(JSON.stringify(y.value.getAllCardData())),{currentPage:i,pageSize:_}=o.value.pageOptions,p={m_read_task_id:l.taskInfo.m_read_task_id,current_page:i,page_size:_};e.markTime&&e.markTime.length>0&&(e.start_time=e.markTime[0],e.end_time=e.markTime[1]),delete e.markTime,p=Object.assign(e,p),K(p).then(s=>{if(s.code&&s.code===200){u.value=!1,h.value=s.data.data,o.value.pageOptions.total=s.data.total;const{path:v}=l.taskInfo;v===l.pathArr[2]&&h.value.forEach(m=>{[{label:"通过",value:1},{label:"不通过",value:2}].forEach(z=>{m.expert_mark_score===z.value&&(m.expert_mark_score=z.label)})})}else u.value=!1,W.warning(s.msg)})},P=e=>{e.current_page=o.value.pageOptions.currentPage,e.page_size=o.value.pageOptions.pageSize,k.value.openDialog(e)},R=e=>{o.value.pageOptions.pageSize=e,r()},V=e=>{o.value.pageOptions.currentPage=e,r()};return C({queryHistoryFn:I,openHistoryFn:A}),(e,i)=>{const _=g("form-component"),p=g("el-button"),s=g("table-component"),v=F("loading");return S((N(),B("div",X,[c("div",Z,[S(c("div",null,[c("div",{ref_key:"topRef",ref:n,class:"history-form"},[c("div",$,[f(_,{ref_key:"formRef",ref:y,modelValue:O.value,"onUpdate:modelValue":i[0]||(i[0]=m=>O.value=m),"form-options":T,"is-query-btn":!1},null,8,["modelValue","form-options"])]),c("div",ee,[f(p,{type:"primary",size:"small",onClick:r},{default:Q(()=>[Y("查询")]),_:1})])],512),f(s,{minHeight:w.value,style:{"margin-bottom":"5px"},"table-options":o.value,"table-data":j(h),onOnHandleSizeChange:R,onOnHandleCurrentChange:V},null,8,["minHeight","table-options","table-data"])],512),[[J,a.value]])]),f(G,{ref_key:"historicalQuesRef",ref:k,taskInfo:d.taskInfo,pathArr:d.pathArr,onQueryData:r},null,8,["taskInfo","pathArr"])])),[[v,u.value]])}}}),ce=U(te,[["__scopeId","data-v-6f230c1d"]]);export{ce as default};
