import{S as _e}from"./index-BCxZUZMh.js";import{c as de}from"./checkInfo-mJiqvlDC.js";import{v as ve}from"./paper-verrification-C986OvHz.js";import{d as pe}from"./downloadRequest-CdE2PBjt.js";import{d as fe,l as n,m as F,n as me,ao as xe,r as d,o as c,g,h as p,e as l,c as C,F as K,p as P,U as ge,b as f,y as h,t as R,f as y,u as Q,T as W,ac as he,ad as ye,_ as be}from"./index-B63pSD2p.js";const X=[{prop:"stu_secret_num",name:"考生密号"},{prop:"ques_id",name:"试题编号"},{prop:"is_answer",name:"是否有作答"},{prop:"stu_score",name:"得分"}],ke=[{prop:"stu_secret_num",name:"考生密号"},{prop:"ques_id",name:"试题编号"},{prop:"ques_score",name:"试题得分"},{prop:"stu_score",name:"考生得分"},{prop:"error",name:"异常情况"}],we=[{prop:"stu_secret_num",name:"考生密号"},{prop:"subject_score",name:"科目总分"},{prop:"stu_score",name:"考生得分"},{prop:"error",name:"异常情况"}],q=L=>(he("data-v-a97cc7c8"),L=L(),ye(),L),Ce={class:"veerfication-item dark:!bg-black eye-box"},Le=["onClick"],De={class:"msg"},Ve={class:"veerfication-result dark:!bg-black eye-box"},Se={class:"title"},Ee=q(()=>l("span",null,"核验结果",-1)),Ie={class:"b_color"},Fe={class:"b_color"},Re={key:1,class:"scuccess-info"},je=["src"],Be=q(()=>l("div",null,"真棒，每项检测都通过啦！",-1)),qe={key:2,class:"error-div"},Ne={class:"msg export_div"},Te=q(()=>l("span",null,"错误原因",-1)),Me={class:"msg"},Ue={class:"error-table"},ze=fe({__name:"check-content",emits:["checkOver"],setup(L,{expose:Y,emit:Z}){const ee=Z,j=n(!1),D=n(null),m=n(1),r=n([{name:"考生试题有作答无成绩",index:1,isLoading:!1,state:1},{name:"考生试题有成绩无作答",index:2,isLoading:!1,state:1},{name:"得分超过试题满分或出现负分",index:3,isLoading:!1,state:1},{name:"总分超过试卷满分或负分",index:4,isLoading:!1,state:1}]);n([]);const ae=n(),te=n();n(0);const b=n(!1),t=n(null),e=n(null),se=()=>{var i;e.value=null,t.value=null,b.value=!0,r.value.forEach(a=>{a.isLoading=!0});let s={subject_id:(i=D.value)==null?void 0:i.subject_id};ve(s).then(a=>{var u,_,v,S,w,E,I,o,x,B,N,T,M,U,z,O,$,A,G,H;if(b.value=!1,r.value.forEach(J=>{J.isLoading=!1,J.state=2}),ee("checkOver"),!a||a.code!=200)return W.error("后端接口异常,"+a.msg);t.value=a.data,((v=(_=(u=a.data)==null?void 0:u.no_answer_no_score)==null?void 0:_.data)==null?void 0:v.length)>0&&(r.value[0].state=3,e.value=r.value[0]),((E=(w=(S=a.data)==null?void 0:S.answer_no_score)==null?void 0:w.data)==null?void 0:E.length)>0&&(r.value[1].state=3,!e.value&&(e.value=r.value[1])),((x=(o=(I=a.data)==null?void 0:I.score_exceeds_max)==null?void 0:o.data)==null?void 0:x.length)>0&&(r.value[2].state=3,!e.value&&(e.value=r.value[2])),((T=(N=(B=a.data)==null?void 0:B.total_score_exceeds_max)==null?void 0:N.data)==null?void 0:T.length)>0&&(r.value[3].state=3,!e.value&&(e.value=r.value[3])),((U=(M=a.data.no_answer_no_score)==null?void 0:M.data)==null?void 0:U.length)==0&&((O=(z=a.data.answer_no_score)==null?void 0:z.data)==null?void 0:O.length)==0&&((A=($=a.data.score_exceeds_max)==null?void 0:$.data)==null?void 0:A.length)==0&&((H=(G=a.data.total_score_exceeds_max)==null?void 0:G.data)==null?void 0:H.length)==0?m.value=2:m.value=3}).catch(a=>{b.value=!1,r.value.forEach(u=>{u.isLoading=!1,u.state=1})})},oe=s=>{s.state==3&&(e.value=s)},k=F(()=>{if(e.value&&t.value){if(e.value.index==1){if(t.value.no_answer_no_score&&t.value.no_answer_no_score.data&&t.value.no_answer_no_score.data.length>0)return t.value.no_answer_no_score}else if(e.value.index==2){if(t.value.answer_no_score&&t.value.answer_no_score.data&&t.value.answer_no_score.data.length>0)return t.value.answer_no_score}else if(e.value.index==3){if(t.value.score_exceeds_max&&t.value.score_exceeds_max.data&&t.value.score_exceeds_max.data.length>0)return t.value.score_exceeds_max}else if(e.value.index==4&&t.value.total_score_exceeds_max&&t.value.total_score_exceeds_max.data&&t.value.total_score_exceeds_max.data.length>0)return t.value.total_score_exceeds_max}}),le=F(()=>{var s;return k.value?(s=k.value)==null?void 0:s.error:""}),ne=F(()=>{if(e.value){if(e.value.index==1)return X;if(e.value.index==2)return X;if(e.value.index==3)return ke;if(e.value.index==4)return we}return[]}),re=F(()=>{var s;return k.value?((s=k.value)==null?void 0:s.data)||[]:[]}),ce=s=>{ue(),j.value=!0,D.value=s},ue=()=>{t.value=n(null),e.value=n(null),r.value.forEach(s=>{s.isLoading=!1,s.state=1}),m.value=1},V=n(!1),ie=(s,i)=>{var _;let a="no_answer_no_score";e.value.index==1?a="no_answer_no_score":e.value.index==2?a="answer_no_score":e.value.index==3?a="score_exceeds_max":e.value.index==4&&(a="total_score_exceeds_max");let u={data:((_=k.value)==null?void 0:_.data)||[],business_class:a};V.value=!0,pe("post","/v1/paper_verification/get_verify_results_file_response","",{},u,"xlsx").then(v=>{V.value=!1}).catch(v=>{V.value=!1,W({type:"error",message:"后端接口异常,导出失败,原因:"+v.response.statusText})})};return me(()=>{}),xe(()=>{}),Y({openDialog:ce}),(s,i)=>{const a=d("el-button"),u=d("CircleCheckFilled"),_=d("el-icon"),v=d("CircleCloseFilled"),S=d("el-text"),w=d("el-table-column"),E=d("el-table"),I=d("el-dialog");return c(),g(I,{title:"阅卷核验","close-on-click-modal":"false","destroy-on-close":"",modelValue:j.value,"onUpdate:modelValue":i[1]||(i[1]=o=>j.value=o),width:"80%"},{default:p(()=>[l("div",{ref_key:"mainRef",ref:ae,class:"paper-verrification"},[l("div",Ce,[(c(!0),C(K,null,P(r.value,(o,x)=>(c(),C("div",{class:ge([o.state==3?"errorDiv":"",e.value==o?"active":""]),key:x,onClick:B=>oe(o)},[l("div",De,[f(a,{class:"loading-icon",type:"primary",text:"",loading:o.isLoading},null,8,["loading"]),o.state==2?(c(),g(_,{key:0,class:"success-icon"},{default:p(()=>[f(u)]),_:1})):h("",!0),o.state==3?(c(),g(_,{key:1,class:"error-icon"},{default:p(()=>[f(v)]),_:1})):h("",!0),l("div",null,R(o.name),1)]),o.state==3?(c(),g(S,{key:0,type:"error",style:{cursor:"pointer"}},{default:p(()=>[y("查看")]),_:1})):h("",!0)],10,Le))),128)),f(a,{class:"operBtn",type:"primary",onClick:se,loading:b.value,disabled:b.value},{default:p(()=>[y("开始检验")]),_:1},8,["loading","disabled"])]),l("div",Ve,[l("div",Se,[Ee,l("span",null,[y("(资格："),l("span",Ie,R(D.value.project_name),1),y("，科目："),l("span",Fe,R(D.value.subject_name),1),y(")")])]),m.value==1?(c(),g(Q(_e),{key:0,title:"阅卷核验",stepList:["点击【开始核验】按钮，系统自动核验","不通过核验项，点击【查看】按钮，查看详情"]})):h("",!0),m.value==2?(c(),C("div",Re,[l("img",{src:Q(de)},null,8,je),Be])):h("",!0),m.value==3?(c(),C("div",qe,[l("div",{ref_key:"errorInfoRef",ref:te,class:"errorDiv",style:{padding:"4px 8px","margin-bottom":"12px","border-left":"2px solid red !important"}},[l("div",Ne,[Te,f(a,{type:"primary",onClick:i[0]||(i[0]=o=>ie()),loading:V.value,size:"small"},{default:p(()=>[y("导出")]),_:1},8,["loading"])]),l("div",Me,R(le.value),1)],512),l("div",Ue,[f(E,{border:"",data:re.value,"scrollbar-always-on":"",stripe:"",height:"100%"},{default:p(()=>[f(w,{type:"index",label:"序号",width:"60",align:"center",fixed:"left"}),(c(!0),C(K,null,P(ne.value,(o,x)=>(c(),g(w,{prop:o.prop,key:x,label:o.name,align:"center"},null,8,["prop","label"]))),128))]),_:1},8,["data"])])])):h("",!0)])],512)]),_:1},8,["modelValue"])}}}),Je=be(ze,[["__scopeId","data-v-a97cc7c8"]]);export{Je as default};
