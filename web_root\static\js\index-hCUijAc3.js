import c from"./prepareWorkFlow-B7DlmR2y.js";import{d as _,l as m,r as t,o as p,c as d,e as i,b as e,h as r,_ as b}from"./index-B63pSD2p.js";import"./calculateTableHeight-BjE6OFD1.js";import"./examinees-management-aJjscxsO.js";import"./index-Astsj93h.js";import"./importExamineAnswer-CdqPvE5P.js";import"./base-DyvdloLK.js";const f={class:"zf-first-box"},u={class:"zf-second-box"},x=_({name:"prepare-workFlow",__name:"index",setup(k){const o=m("PrePareWorkFlow");return(v,a)=>{const s=t("el-tab-pane"),n=t("el-tabs");return p(),d("div",f,[i("div",u,[e(n,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=l=>o.value=l),class:"monitor-container dark:!bg-black eye-box"},{default:r(()=>[e(s,{label:"数据准备",name:"PrePareWorkFlow"},{default:r(()=>[e(c)]),_:1})]),_:1},8,["modelValue"])])])}}}),N=b(x,[["__scopeId","data-v-73cb7512"]]);export{N as default};
