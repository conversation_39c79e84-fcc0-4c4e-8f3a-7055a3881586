const f=i=>{let l=[];return i==1&&(l=["（1）","（2）","（3）","（4）","（5）","（6）","（7）","（8）","（9）","（10）"]),i==2&&(l=["1","2","3","4","5","6","7","8","9","10"]),i==3&&(l=["1）","2）","3）","4）","5）","6）","7）","8）","9）","10）"]),i==4&&(l=["a","b","c","d","e","f","g","h","i","j"]),i==5&&(l=["问题一","问题二","问题三","问题四","问题五","问题六","问题七","问题八","问题九","问题十"]),i==6&&(l=["问题1","问题2","问题3","问题4","问题5","问题6","问题7","问题8","问题9","问题10"]),i==7&&(l=["小题一","小题二","小题三","小题四","小题五","小题六","小题七","小题八","小题九","小题十"]),i==8&&(l=["一","二","三","四","五","六","七","八","九","十"]),l},r=i=>{for(let l in i)if(i[l].level=2,i[l].ques_order_new=i[l].ques_order,i[l].children&&i[l].children.length>0)for(let n in i[l].children)i[l].children[n].ques_order_new=i[l].children[n].ques_order,i[l].children[n].level=3};export{f as g,r as i};
