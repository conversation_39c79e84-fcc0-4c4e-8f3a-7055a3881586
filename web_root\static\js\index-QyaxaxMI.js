import{p as A,a as R,g as Z}from"./common-methods-BWkba4Bo.js";import{g as ee}from"./test-paper-management-DjV_45YZ.js";import{a as ae,l as te,b as ne,t as oe,d as le}from"./marking-task-DrCSMu9U.js";import{d as se,az as re,l as r,P as ie,n as ce,C as pe,ao as de,T as s,r as _,j as ue,o as m,c as h,e as k,b as i,h as d,w as _e,g as me,f as j,u as g,aN as y,y as w,aV as B,_ as ge}from"./index-B63pSD2p.js";import{c as fe,a as he}from"./calculateTableHeight-BjE6OFD1.js";const ke={class:"zf-first-box"},be={class:"zf-second-box"},ve={class:"upload-btn-box"},xe={class:"task-btn-box"},Ce=["onClick"],ye=["onClick"],we=["onClick"],De=["onClick"],Oe=["onClick"],je=se({name:"marking-task",__name:"index",setup(Be){re();const S=r(null),l=r(null),b=r(null),P=r(null),f=r(!1),v=r([]),x=r([]),T=r({}),H=ie({column:3,labelWidth:"70px",itemWidth:"160px",rules:{m_read_task_name:[{trigger:["blur","change"],validator:(a,t,e)=>{if(t&&t.length>100)return e(new Error("任务名称长度不能超过100！"));e()}}],paper_name:[{trigger:["blur","change"],validator:(a,t,e)=>{if(t&&t.length>100)return e(new Error("试卷名称长度不能超过100！"));e()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>A.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>v.value},{label:"任务名称",prop:"m_read_task_name",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入任务名称"},{label:"所属试卷",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择所属试卷",clearable:!0,optionData:()=>x.value}]}),u=r({field:[{prop:"m_read_task_name",label:"任务名称",minWidth:"120px"},{prop:"project_name",label:"所属资格",minWidth:"100px"},{prop:"subject_name",label:"所属科目",minWidth:"100px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_type_name",label:"题型",minWidth:"100px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"manual_process_name",label:"阅卷模式",minWidth:"110px"},{prop:"manual_group_name_list",label:"阅卷小组",minWidth:"150px"},{prop:"c_name",label:"创建人",minWidth:"100px"},{prop:"progress",label:"进度",type:"progress",minWidth:"150px"},{prop:"task_state",label:"状态",minWidth:"80px"},{prop:"created_time",label:"创建时间",minWidth:"150px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"150px",sortable:!0},{prop:"remark",label:"备注",minWidth:"120px"},{prop:"operation",label:"操作",type:"slot",minWidth:"210px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let W=r([]);const D=r([]);let F=null;ce(()=>{fe(F,S.value,u.value),pe(()=>{const{project_id:a,subject_id:t}=l.value.getAllCardData();a&&R(a).then(e=>{v.value=e||[]}),t&&z()}),Z(),c()}),de(()=>{he(F)});const E=(a,t)=>{a.prop==="project_id"?(v.value=[],x.value=[],l.value.getCardData("subject_id")&&l.value.setCardData("subject_id",null),l.value.getCardData("paper_code")&&l.value.setCardData("paper_code",null),t&&R(t).then(e=>{v.value=e||[]})):a.prop==="subject_id"?(x.value=[],l.value.getCardData("paper_code")&&l.value.setCardData("paper_code",null),t&&z()):a.prop==="search_time"&&l.value.setCardData("search_time",t)},z=()=>{const{project_id:a,subject_id:t}=l.value.getAllCardData();ee({project_id:a,subject_id:t,page_size:-1}).then(n=>{n.code&&n.code===200&&(n.data.data.forEach(o=>{o.label=o.paper_name,o.value=o.paper_code}),x.value=n.data.data)})},c=()=>{let a=JSON.parse(JSON.stringify(l.value.getAllCardData())),{currentPage:t,pageSize:e}=u.value.pageOptions,n={current_page:t,page_size:e};n=Object.assign(a,n),ae(n).then(o=>{o.code&&o.code===200?(W.value=o.data.data,u.value.pageOptions.total=o.data.total):s.warning(o.msg)})},J=()=>{P.value.openDialog()},L=()=>{b.value.openDialog("01")},M=()=>{if(!D.value.length){s.warning("至少选择一条数据！");return}V()},V=a=>{B.confirm("确定发起阅卷任务吗？","确认提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"info"}).then(()=>{let t={m_read_task_id_list:a?[a.m_read_task_id]:D.value.map(e=>e.m_read_task_id)};f.value=!0,te(t).then(e=>{e.code&&e.code===200?(s.success(e.msg),c(),f.value=!1):(s.warning(e.msg),f.value=!1)}).catch(()=>{f.value=!1})}).catch(()=>{})},N=(a,t)=>{a==="02"&&t.lock_state===2?s.warning("该任务已锁定!不可编辑!"):b.value.openDialog(a,JSON.parse(JSON.stringify(t)))},q=a=>{if(a.progress<100){s.warning("该任务未完成，不允许结束任务");return}let t={m_read_task_id:a.m_read_task_id};ne(t).then(e=>{e.code&&e.code===200?(B.confirm(e.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{oe(t).then(n=>{n.code&&n.code===200?(s.success(n.msg),c()):s.warning(n.msg)})}).catch(()=>{}),c()):s.warning(e.msg)}).catch(()=>{})},$=a=>{a.lock_state===2?s.warning("该任务已被锁定!不可删除!"):B.confirm("确定删除该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let t={m_read_task_id:a.m_read_task_id};le(t).then(e=>{e.code&&e.code===200?(s.success(e.msg),c()):s.warning(e.msg)})}).catch(()=>{})},Q=()=>{l.value.resetFieldsFn(),u.value.pageOptions.currentPage=1,c()},U=a=>{u.value.pageOptions.pageSize=a,c()},I=a=>{u.value.pageOptions.currentPage=a,c()},G=a=>{D.value=a};return(a,t)=>{const e=_("form-component"),n=_("el-card"),o=_("el-button"),O=_("Auth"),K=_("table-component"),X=_("add-task"),Y=ue("loading");return m(),h("div",ke,[k("div",be,[i(n,null,{default:d(()=>[k("div",{ref_key:"formDivRef",ref:S},[i(e,{ref_key:"formRef",ref:l,modelValue:T.value,"onUpdate:modelValue":t[0]||(t[0]=p=>T.value=p),"form-options":H,"is-query-btn":!0,onOnchangeFn:E,onQueryDataFn:c},null,8,["modelValue","form-options"])],512)]),_:1}),_e((m(),me(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:d(()=>[k("div",ve,[i(O,{value:"marking-task/batchAdd"},{default:d(()=>[i(o,{type:"primary",onClick:J},{default:d(()=>[j("批量创建")]),_:1})]),_:1}),i(O,{value:"marking-task/add"},{default:d(()=>[i(o,{type:"primary",onClick:L},{default:d(()=>[j("创建")]),_:1})]),_:1}),i(O,{value:"marking-task/batchStart"},{default:d(()=>[i(o,{type:"primary",onClick:M},{default:d(()=>[j("批量发起阅卷任务")]),_:1})]),_:1})]),i(K,{minHeight:u.value.styleOptions.minHeight,"table-options":u.value,"table-data":g(W),onOnHandleSizeChange:U,onOnHandleCurrentChange:I,onOnHandleSelectionChange:G},{operation:d(p=>[k("div",xe,[p.row.task_state_num===1&&g(y)("marking-task/start")?(m(),h("span",{key:0,class:"task-btn",onClick:C=>V(p.row)},"开始",8,Ce)):w("",!0),g(y)("marking-task/edit")?(m(),h("span",{key:1,class:"task-btn",onClick:C=>N("02",p.row)},"编辑",8,ye)):w("",!0),k("span",{class:"task-btn",onClick:C=>N("03",p.row)},"详情",8,we),g(y)("marking-task/end")&&p.row.task_state_num!==1?(m(),h("span",{key:2,class:"task-btn",onClick:C=>q(p.row)},"结束",8,De)):w("",!0),g(y)("marking-task/delete")?(m(),h("span",{key:3,class:"task-btn",onClick:C=>$(p.row)},"删除",8,Oe)):w("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})),[[Y,f.value]])]),i(X,{ref_key:"addTaskRef",ref:b,"project-list":g(A),onQueryData:Q},null,8,["project-list"])])}}}),Ve=ge(je,[["__scopeId","data-v-53eb8a5d"]]);export{Ve as default};
