import{aQ as e,aR as s}from"./index-B63pSD2p.js";const a=t=>e.request("post",s("/v1/exam_stu/get_stu"),{data:t}),u=t=>e.request("post",s("/v1/exam_stu/create_stu"),{data:t}),o=t=>e.request("post",s("/v1/exam_stu/update_stu"),{data:t}),_=t=>e.request("post",s("/v1/exam_stu/get_import_stu_progress"),{data:t},{},!1),p=t=>e.request("delete",s("/v1/transfer/clear_imported_data"),{params:t}),n=t=>e.request("post",s("/v1/transfer/import_record_list"),{params:t});export{a,p as b,u as c,_ as g,n as i,o as u};
