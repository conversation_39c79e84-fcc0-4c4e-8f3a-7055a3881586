import{a as d,b as n}from"./test-paper-management-DjV_45YZ.js";import{l as p}from"./index-B63pSD2p.js";const s=p([]),u=()=>{let c={page_size:-1};return new Promise(r=>{d(c).then(t=>{var a;t.code&&t.code===200&&((a=t.data.data)==null||a.forEach(e=>{e.label=e.project_name,e.value=e.project_id}),s.value=t.data.data,r())})})},_=c=>{let r={page_size:-1,project_id:c};return new Promise(t=>{n(r).then(a=>{var e;a.code&&a.code===200&&((e=a.data.data)==null||e.forEach(o=>{o.label=o.subject_name,o.value=o.subject_id}),t(a.data.data))})})};export{_ as a,u as g,s as p};
