import{_ as r,a as h}from"./judge-copy-BAX6vJkk.js";import{d as m,aO as g,r as a,o as v,g as b,h as t,e,b as c,f as d,ac as y,ad as w,_ as S}from"./index-B63pSD2p.js";const s=o=>(y("data-v-26ccdc58"),o=o(),w(),o),V={class:"flex border-t border-b border-1 border-gray-300 single-detail-container"},C=s(()=>e("div",{class:"flex-1 p-5 flex flex-col"},[e("section",{class:"flex-1"},[e("div",null,"考生密号（参考考生）：2003939999020008"),e("img",{src:r})]),e("p",{class:"flex justify-end"},[e("span",null,"作答字数：67")])],-1)),D={class:"flex-1 p-5 flex flex-col border-l border-1 border-gray-300 relative"},I={class:"flex-1"},j=s(()=>e("span",null,"考生密号（对比考生）：2003939999020001",-1)),k=s(()=>e("div",{class:"flex flex-1 justify-end"},[e("p",{class:"text-[16px]"},[d("答案相似度"),e("span",{class:"text-red-500 text-[18px]"},"100%")])],-1)),N=s(()=>e("img",{src:r},null,-1)),B=s(()=>e("img",{class:"errorImage absolute",style:{width:"150px",bottom:"100px",right:"40px"},src:h},null,-1)),M=s(()=>e("p",{class:"flex justify-end"},[e("span",null,"作答字数：67")],-1)),J={style:{flex:"auto"}},O=m({__name:"multi-judge",props:{isShowDialog:{},isShowDialogModifiers:{}},emits:["update:isShowDialog"],setup(o){const l=g(o,"isShowDialog"),_=()=>{l.value=!1},p=()=>{l.value=!1};return(T,n)=>{const u=a("hander"),i=a("el-button"),f=a("el-dialog");return v(),b(f,{title:"判定",modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=x=>l.value=x),fullscreen:""},{footer:t(()=>[e("div",J,[c(i,{onClick:_},{default:t(()=>[d("未抄袭")]),_:1}),c(i,{type:"primary",onClick:p},{default:t(()=>[d("判定抄袭")]),_:1})])]),default:t(()=>[e("div",V,[C,e("div",D,[e("section",I,[c(u,{class:"flex"},{default:t(()=>[j,k]),_:1}),N,B]),M])])]),_:1},8,["modelValue"])}}}),q=S(O,[["__scopeId","data-v-26ccdc58"]]);export{q as default};
