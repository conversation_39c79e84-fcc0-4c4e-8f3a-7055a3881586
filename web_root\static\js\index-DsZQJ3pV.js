var se=Object.defineProperty,re=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var P=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,pe=Object.prototype.propertyIsEnumerable;var $=(s,l,i)=>l in s?se(s,l,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[l]=i,A=(s,l)=>{for(var i in l||(l={}))ue.call(l,i)&&$(s,i,l[i]);if(P)for(var i of P(l))pe.call(l,i)&&$(s,i,l[i]);return s},N=(s,l)=>re(s,ie(l));import de from"./batch-add-CKTplHOz.js";import{p as Q,g as ce,a as _e}from"./common-methods-BWkba4Bo.js";import{g as me}from"./test-paper-management-DjV_45YZ.js";import{a as fe,e as he,l as be,p as ke,f as ve,h as ge}from"./formal-task-CUOmIYGE.js";import{f as ye}from"./handleMethod-BIjqYEft.js";import{d as xe,az as Ce,l as c,P as we,m as Te,n as je,ao as Be,T as _,r as B,j as We,o as b,c as x,e as W,b as k,h as f,w as Oe,g as z,u as p,aN as m,f as g,y,aV as O,_ as Se}from"./index-B63pSD2p.js";import{c as Ve,a as De}from"./calculateTableHeight-BjE6OFD1.js";import"./setting-task-URsozwG-.js";import"./marking-mode-CLpbbjcA.js";import"./fullscreen-exit-line-DVwpkItP.js";import"./question-DElFsEXd.js";const He={class:"zf-first-box"},ze={class:"zf-second-box"},Fe={class:"upload-btn-box"},Re={class:"task-btn-box"},qe={key:0,class:"task-btn"},Le={key:1,class:"task-btn"},Me={key:2,class:"task-btn"},Ee={key:3,class:"task-btn"},Pe={key:4,class:"task-btn"},$e=xe({name:"formal-task",__name:"index",props:{entrance:{type:String,default:"formal-task"}},setup(s){const l=s;Ce();const i=c(null),U=c(null),F=c(null),C=c(!1),w=c([]),S=c([]),T=c([]),R=[{value:1,label:"未开始"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"}];c([]);const o=c({}),I=we({column:3,labelWidth:"70px",itemWidth:"160px",rules:{task_name:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>100)return t(new Error("任务名称长度不能超过100！"));t()}}],paper_name:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>100)return t(new Error("试卷名称长度不能超过100！"));t()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>Q.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属科目",optionData:()=>w.value},{label:"任务名称",prop:"task_name",type:"input",clearable:!0,defaultValue:"",placeholder:"请输入任务名称"},{label:"题型",prop:"business_type_name",type:"select",defaultValue:"",placeholder:"请选择题型",clearable:!0,props:{value:"ques_type_name",label:"ques_type_name"},optionData:()=>T.value},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"任务状态",prop:"round_state_list",type:"select",defaultValue:"",multiple:!0,placeholder:"请选择任务状态",collapseTags:!0,clearable:!0,optionData:()=>R}]}),v=c({field:[{prop:"task_name",label:"任务名称",minWidth:"120px"},{prop:"project_name",label:"所属资格",minWidth:"100px"},{prop:"subject_name",label:"所属科目",minWidth:"100px"},{prop:"business_type_name",label:"题型",minWidth:"100px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"group_name_list",label:"阅卷小组",minWidth:"150px"},{prop:"process_name",label:"阅卷模式",minWidth:"110px"},{prop:"round_count",label:"轮次",minWidth:"100px"},{prop:"round_state",label:"任务状态",minWidth:"100px",formatter:e=>ye(e.round_state,R)},{prop:"try_mark_ques_num",label:"作答数",minWidth:"100px",isHidden:!0},{prop:"allow_diff_score",label:"允许偏差分数",minWidth:"110px",isHidden:!0},{prop:"process",label:"任务进度",type:"progress",minWidth:"150px"},{prop:"name",label:"创建人",minWidth:"100px"},{prop:"created_time",label:"创建时间",minWidth:"150px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"150px",sortable:!0},{prop:"operation",label:"操作",type:"slot",minWidth:"220px",fixed:"right",showOverflowTooltip:!0}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let q=c([]);const V=c([]);let L=null;const u=Te(()=>{if(l.entrance==="formal-task")return 1;if(l.entrance==="trial-task")return v.value.field.forEach(e=>{e.prop==="try_mark_ques_num"||e.prop==="allow_diff_score"?e.isHidden=!1:e.prop==="process_name"&&(e.isHidden=!0)}),2});je(()=>{Ve(L,i.value,v.value),ce(),h(),H()}),Be(()=>{De(L)});const D=e=>e.round_state===3||e.round_state===5,G=(e,a)=>{e.prop==="project_id"?(w.value=[],S.value=[],T.value=[],o.value.subject_id&&(o.value.subject_id=null),o.value.paper_code&&(o.value.paper_code=null),o.value.business_type_name&&(o.value.business_type_name=null),a&&_e(a).then(t=>{w.value=t||[]}),H()):e.prop==="subject_id"&&(S.value=[],T.value=[],o.value.paper_code&&(o.value.paper_code=null),o.value.business_type_name&&(o.value.business_type_name=null),a&&(J(),H()))},J=()=>{const{project_id:e,subject_id:a}=o.value;me({project_id:e,subject_id:a,page_size:-1}).then(n=>{n.code&&n.code===200&&(n.data.data.forEach(d=>{d.label=d.paper_name,d.value=d.paper_code}),S.value=n.data.data)})},H=()=>{const{project_id:e,subject_id:a}=o.value;fe({project_id:e,subject_id:a,is_remove_duplicate:!0}).then(n=>{n.code&&n.code===200?T.value=n.data.data:_.error(n.msg)})},h=()=>{let{currentPage:e,pageSize:a}=v.value.pageOptions,t=N(A({},o.value),{current_page:e,page_size:a,task_type:u.value});he(t).then(n=>{n.code&&n.code===200?(q.value=n.data.data,v.value.pageOptions.total=n.data.total):_.warning(n.msg)})},M=(e,a)=>{F.value.openDialog(e,a)},K=()=>{if(!V.value.length){_.warning("至少选择一条数据！");return}E()},E=e=>{O.confirm("确定开始任务吗？","确认提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{let a={task_type:u.value,launch_list:e?[{task_id:e.task_id,round_id:e.round_id}]:V.value.map(t=>({task_id:t.task_id,round_id:t.round_id}))};C.value=!0,be(a).then(t=>{t.code&&t.code===200?(_.success(t.msg),h(),C.value=!1):(_.warning(t.msg),C.value=!1)}).catch(()=>{C.value=!1})}).catch(()=>{})},X=e=>{O.confirm("确定暂停任务吗？","确认提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{let a={task_type:u.value,task_id:e.task_id,round_id:e.round_id};ke(a).then(t=>{t.code&&t.code===200?(_.success(t.msg),h()):_.error(t.msg)})}).catch(()=>{})},Y=e=>{O.confirm("确定继续任务吗？","确认提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{let a={task_type:u.value,task_id:e.task_id,round_id:e.round_id};ve(a).then(t=>{t.code&&t.code===200?(_.success(t.msg),h()):_.error(t.msg)})}).catch(()=>{})},Z=e=>{e.lock_state===2?_.warning("该任务已被锁定!不可删除!"):O.confirm("确定删除该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a={task_type:u.value,task_id:e.task_id,round_id:e.round_id};ge(a).then(t=>{t.code&&t.code===200?(_.success(t.msg),h()):_.warning(t.msg)})}).catch(()=>{})},ee=e=>{v.value.pageOptions.pageSize=e,h()},te=e=>{v.value.pageOptions.currentPage=e,h()},ae=e=>{V.value=e};function ne(){w.value=[]}return(e,a)=>{const t=B("form-component"),n=B("el-card"),d=B("el-button"),le=B("table-component"),oe=We("loading");return b(),x("div",He,[W("div",ze,[k(n,null,{default:f(()=>[W("div",{ref_key:"formDivRef",ref:i},[k(t,{ref_key:"formRef",ref:U,modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=r=>o.value=r),"form-options":I,"is-query-btn":!0,onOnchangeFn:G,onQueryDataFn:h,onResetFields:ne},null,8,["modelValue","form-options"])],512)]),_:1}),Oe((b(),z(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:f(()=>[W("div",Fe,[s.entrance==="formal-task"&&p(m)("formal-task/batch-add")||s.entrance==="trial-task"&&p(m)("trial-task/batch-add")?(b(),z(d,{key:0,type:"primary",onClick:a[1]||(a[1]=r=>M("add"))},{default:f(()=>[g("批量创建 ")]),_:1})):y("",!0),s.entrance==="formal-task"&&p(m)("formal-task/batch-start")?(b(),z(d,{key:1,type:"primary",onClick:K},{default:f(()=>[g("批量开始 ")]),_:1})):y("",!0)]),k(le,{minHeight:v.value.styleOptions.minHeight,"table-options":v.value,"table-data":p(q),onOnHandleSizeChange:ee,onOnHandleCurrentChange:te,onOnHandleSelectionChange:ae},{operation:f(r=>[W("div",Re,[u.value===1&&p(m)("formal-task/start")||u.value===2&&p(m)("trial-task/start")?(b(),x("span",qe,[k(d,{link:"",type:"primary",disabled:D(r.row)||r.row.round_state!==1,onClick:j=>E(r.row)},{default:f(()=>[g("开始")]),_:2},1032,["disabled","onClick"])])):y("",!0),u.value===1&&p(m)("formal-task/start")||u.value===2&&p(m)("trial-task/start")?(b(),x("span",Le,[k(d,{link:"",type:"primary",disabled:D(r.row)||r.row.round_state!==2,onClick:j=>X(r.row)},{default:f(()=>[g("暂停")]),_:2},1032,["disabled","onClick"])])):y("",!0),u.value===1&&p(m)("formal-task/start")||u.value===2&&p(m)("trial-task/start")?(b(),x("span",Me,[k(d,{link:"",type:"primary",disabled:D(r.row)||r.row.round_state!==4,onClick:j=>Y(r.row)},{default:f(()=>[g("继续")]),_:2},1032,["disabled","onClick"])])):y("",!0),p(m)(u.value===1&&"formal-task/edit")?(b(),x("span",Ee,[k(d,{link:"",type:"primary",onClick:j=>M("edit",r.row)},{default:f(()=>[g("编辑")]),_:2},1032,["onClick"])])):y("",!0),p(m)("formal-task/delete")?(b(),x("span",Pe,[k(d,{link:"",type:"primary",onClick:j=>Z(r.row)},{default:f(()=>[g("删除")]),_:2},1032,["onClick"])])):y("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})),[[oe,C.value]])]),k(de,{ref_key:"batchAddRef",ref:F,projectList:p(Q),taskType:u.value,onQueryData:h},null,8,["projectList","taskType"])])}}}),tt=Se($e,[["__scopeId","data-v-800d5194"]]);export{tt as default};
