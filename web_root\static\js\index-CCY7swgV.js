import{d as O,i as x,l as r,n as y,ao as w,T as C,r as l,o as S,c as z,e as p,b as c,h as _,f as H,_ as P}from"./index-B63pSD2p.js";import{g as R}from"./re-marking-BVznCWni.js";import{t as j}from"./task-CIWl3ohF.js";const B={class:"zf-first-box"},D={class:"task-btn-box"},E={class:"task-btn"},m=5,N=O({name:"management-task",__name:"index",setup(T){const b=x(),d=r(null),t=r({field:j,styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let n=null;y(()=>{n=new ResizeObserver(e=>{e.forEach(i=>{t.value.styleOptions.minHeight=window.innerHeight-160+"px"})}),n.observe(d.value),s()}),w(()=>{n&&(n.disconnect(),n=null)});const u=r([]),s=()=>{let{currentPage:e,pageSize:i}=t.value.pageOptions;R({current_page:e,page_size:i,task_type:m,is_repeater:1}).then(a=>{a.code&&a.code===200?(u.value=a.data.repeat_tasks,t.value.pageOptions.total=a.data.total):C.error(a.msg)})},f=e=>{t.value.pageOptions.currentPage=1,t.value.pageOptions.pageSize=e,s()},h=e=>{t.value.pageOptions.currentPage=e,s()},k=e=>{b.push({path:"/manual-marking/start-re-marking/index",query:{type:m,repeat_task_id:e.repeat_task_id,subject_id:e.subject_id,project_id:e.project_id}})};return(e,i)=>{const g=l("el-button"),a=l("table-component"),v=l("el-card");return S(),z("div",B,[p("div",{ref_key:"formDivRef",ref:d},null,512),c(v,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:_(()=>[c(a,{minHeight:t.value.styleOptions.minHeight,"table-options":t.value,"table-data":u.value,onOnHandleSizeChange:f,onOnHandleCurrentChange:h},{operation:_(o=>[p("div",D,[p("span",E,[c(g,{link:"",type:"primary",disabled:o.row.task_state==1||o.row.task_state==3||o.row.task_state==4,onClick:V=>k(o.row)},{default:_(()=>[H("开始验收")]),_:2},1032,["disabled","onClick"])])])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})])}}}),I=P(N,[["__scopeId","data-v-e4878bd0"]]);export{I as default};
