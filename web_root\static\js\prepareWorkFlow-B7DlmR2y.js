import{c as I,a as O}from"./calculateTableHeight-BjE6OFD1.js";import{i as T,b as z}from"./examinees-management-aJjscxsO.js";import{P as U}from"./index-Astsj93h.js";import V from"./importExamineAnswer-CdqPvE5P.js";import{d as A,l as a,n as M,ao as N,T as d,r as i,o as w,c as q,e as p,b as t,h as r,f as y,g as H,y as j,u as G,F as L,aV as J,_ as K}from"./index-B63pSD2p.js";import"./base-DyvdloLK.js";function Q(){function e(){return((1+Math.random())*65536|0).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()}const X={class:"zf-first-box"},Y={class:"zf-second-box"},Z=A({name:"workFlow",__name:"prepareWorkFlow",setup(e){const _=a(null),B=a(null),l=a(null),c=a({field:[{prop:"specialty_name",label:"资格",minWidth:"120px"},{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"exam_time_code",label:"场次",minWidth:"60px"},{prop:"stu_count",label:"报考人数",minWidth:"80px"},{prop:"backcount",label:"参考人数",minWidth:"80px"},{prop:"absentcount",label:"缺考人数",minWidth:"80px"},{prop:"blank_image",label:"空白图片",minWidth:"80px"},{prop:"img_count",label:"图片数量",minWidth:"80px"},{prop:"answer_count",label:"作答数量"}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!1,currentPage:1,pageSize:100,total:0}}),f=a([]);let g=null;M(()=>{I(g,_.value,c.value,!0,8),m()}),N(()=>{O(g)});const m=n=>{T({}).then(o=>{if(!o||o.code!=200)return d.error("后端异常,原因:"+o.msg);f.value=o.data||[]})},s=a(!1),b=a(null),k=()=>{b.value=Q(),s.value=!0},S=()=>{l.value.openDialog()},P=()=>{S()},C=()=>{l.value.manualClose()},D=()=>{l.value.manualClose(),m()},h=()=>({process_id:b.value}),W=()=>({params:h(),url:"/v1/transfer/get_import_process"}),F=()=>{J.confirm("确定初始化数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{z({}).then(n=>{n.code&&n.code===200?(d.success(n.msg),m()):d.warning(n.msg)})}).catch(()=>{})};return(n,u)=>{const o=i("el-card"),x=i("el-button"),v=i("Auth"),E=i("table-component");return w(),q(L,null,[p("div",X,[p("div",Y,[t(o,{class:"formDiv"},{default:r(()=>[p("div",{ref_key:"formDivRef",ref:_,class:"query-box"},null,512)]),_:1}),t(o,null,{default:r(()=>[p("div",{class:"upload-btn-box",ref_key:"btnBoxRef",ref:B},[t(v,{value:"prepare-workFlow/import"},{default:r(()=>[t(x,{style:{"margin-right":"12px"},type:"primary",onClick:k},{default:r(()=>[y("导入")]),_:1})]),_:1}),t(v,{value:"prepare-workFlow/init"},{default:r(()=>[t(x,{style:{"margin-right":"12px"},type:"primary",onClick:F},{default:r(()=>[y("清空数据")]),_:1})]),_:1})],512),t(E,{"min-height":c.value.styleOptions.minHeight,"table-options":c.value,"table-data":f.value},null,8,["min-height","table-options","table-data"])]),_:1})])]),s.value?(w(),H(V,{key:0,title:"导入作答数据",isShowDialog:s.value,"onUpdate:isShowDialog":u[0]||(u[0]=R=>s.value=R),onUploadBegin:P,onUploadError:C,onUploadSuccess:D,getParams:h},null,8,["isShowDialog"])):j("",!0),t(G(U),{ref_key:"progressBarRef",ref:l,title:"导入进度",apiInfo:W(),internalTime:"2000"},null,8,["apiInfo"])],64)}}}),re=K(Z,[["__scopeId","data-v-dcd25b6a"]]);export{re as default};
