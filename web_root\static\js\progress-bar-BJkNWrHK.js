import{aQ as i,aR as l,d as P,l as g,P as V,ao as j,r as b,o as p,c as h,e as s,f as d,t as C,b as T,g as q,h as x,y as F,aV as c,T as m,_ as R}from"./index-B63pSD2p.js";const ee=n=>i.request("post",l("/v1/op_mark/get_op_ques_list"),{data:n},{},!0,10*60*1e3),N=n=>i.request("post",l("/v1/op_mark/get_op_mark_progress"),{data:n},{},!1),te=n=>i.request("get",l("/v1/op_mark/create_op_mark_record"),{},{},!0),ne=n=>i.request("post",l("/v1/op_mark/op_engine_mark"),{data:n},{},!0,6*60*1e3,"数据获取中"),z=n=>i.request("post",l("/v1/op_mark/pause_op_engine_mark"),{data:n},{},!0,6*60*1e3,"正在暂停中"),S=n=>i.request("post",l("/v1/op_mark/cancel_op_engine_mark"),{data:n},{},!0,6*60*1e3,"正在取消中"),U=n=>i.request("post",l("/v1/op_mark/continue_op_engine_mark"),{data:n},{},!0,6*60*1e3,"正在加载中"),$=n=>{const f={record_id:n};return new Promise((v,_)=>{N(f).then(r=>{r.code&&r.code===200&&r.data.record_id&&v(r.data)})})},L={key:0},Q={class:"zf-dialog-first-box"},A={class:"zf-dialog-second-box"},G={class:"progress-wrapper"},H={class:"step-box"},J={key:0},K={key:1},W={class:"function-area"},X=P({__name:"progress-bar",emits:["queryData","deleteRecordId"],setup(n,{expose:f,emit:v}){g("import-test-paper");const _=v,r=g(!1),u=g(!0),e=V({record_id:null,percentage:0,finish_count:0,total_count:0,running_state:1,state_str:"数据获取中"});let o=g(null);const I=()=>{u.value=!1},w=a=>{r.value=!0,Object.assign(e,a),e.running_state!==4&&B(a)},B=a=>{o.value&&clearInterval(o.value),o.value=setInterval(()=>{$(a.record_id).then(t=>{t&&(Object.assign(e,t),e.running_state===4&&(u.value=!1),e.running_state===6&&y(),e.running_state===7&&setTimeout(()=>{y(),c.alert("操作题评分已完成。","提示",{confirmButtonText:"确定"})},1e3))}).catch(t=>{m.warning("获取进度失败")})},5e3)},O=()=>{c.confirm("确定暂停操作题评分吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{const a={record_id:e.record_id};z(a).then(t=>{t&&t.code&&t.code===200?(u.value=!1,o.value&&clearInterval(o.value),_("queryData"),c.alert("操作题评分已暂停。","提示",{confirmButtonText:"确定"})):m.alert(t.msg)})}).catch(()=>{})},D=()=>{c.confirm("确定继续操作题评分吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{const a={record_id:e.record_id};U(a).then(t=>{u.value=!0,t&&t.code&&t.code===200?(B({record_id:e.record_id}),c.alert("操作题评分已继续。","提示",{confirmButtonText:"确定"})):m.alert(t.msg)})}).catch(()=>{})},M=()=>{c.confirm("确定取消操作题评分吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{const a={record_id:e.record_id};S(a).then(t=>{t&&t.code&&t.code===200?(_("deleteRecordId"),_("queryData"),c.alert("操作题评分已取消。","提示",{confirmButtonText:"确定"})):m.alert(t.msg)})}).catch(()=>{})},y=()=>{r.value=!1,e.percentage=0,e.record_id=null,e.finish_count=0,e.total_count=0,e.state_str="数据获取中",o.value&&clearInterval(o.value),_("queryData"),_("deleteRecordId")};return j(()=>{o.value&&clearInterval(o.value)}),f({openDialog:w,showCancelBtnFn:I}),(a,t)=>{const E=b("el-progress"),k=b("el-button");return r.value?(p(),h("div",L,[s("div",Q,[s("div",A,[s("div",G,[s("div",H,[s("div",null,[d(" 状态："),s("span",null,C(e.state_str),1)]),s("div",null,[d(" 进度： "),e.total_count?(p(),h("span",J,C(`${e.finish_count} / ${e.total_count}`),1)):(p(),h("span",K,"加载中..."))])]),T(E,{class:"progress-box","text-inside":!0,"stroke-width":15,striped:"",percentage:e.percentage,status:"success"},null,8,["percentage"])]),s("div",W,[u.value?(p(),q(k,{key:0,type:"primary",onClick:O},{default:x(()=>[d("暂停评分")]),_:1})):(p(),q(k,{key:1,type:"primary",onClick:D},{default:x(()=>[d("继续评分")]),_:1})),T(k,{type:"primary",onClick:M},{default:x(()=>[d("取消评分")]),_:1})])])])])):F("",!0)}}}),Y=R(X,[["__scopeId","data-v-3290043f"]]),ae=Object.freeze(Object.defineProperty({__proto__:null,default:Y},Symbol.toStringTag,{value:"Module"}));export{Y as P,$ as a,te as c,ee as g,ne as o,ae as p};
