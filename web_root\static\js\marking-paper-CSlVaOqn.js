import{aQ as t,aR as s}from"./index-B63pSD2p.js";const _=(e,r=!0)=>t.request("post",s("/v1/prepare/get_answer_sample_list"),{data:e},{},r),u=(e,r=!0)=>t.request("post",s("/v1/exception/create_answer_exception"),{data:e},{},r),n=e=>t.request("post",s("/v1/ques_manage/get_ques_detail"),{data:e}),o=e=>t.request("post",s("/v1/human_mark/reviewer_mark_stu"),{data:e}),p=e=>t.request("post",s("/v1/official_mark/get_step_by_ques_code"),{data:e});export{p as a,n as b,u as c,_ as g,o as r};
