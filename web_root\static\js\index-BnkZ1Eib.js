var R=Object.defineProperty;var v=Object.getOwnPropertySymbols;var H=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var x=(r,a,t)=>a in r?R(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,j=(r,a)=>{for(var t in a||(a={}))H.call(a,t)&&x(r,t,a[t]);if(v)for(var t of v(a))P.call(a,t)&&x(r,t,a[t]);return r};import{d as B,i as L,l as s,P as N,n as A,ao as M,r as p,o as T,c as U,e as m,b as o,h as l,f as b,_ as G}from"./index-B63pSD2p.js";import{f as I}from"./handleMethod-BIjqYEft.js";import{c as Q,a as J}from"./calculateTableHeight-BjE6OFD1.js";import{p as K,g as X,a as Y}from"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";const Z={class:"zf-first-box"},$={class:"zf-second-box"},ee={class:"zf-flex-end"},te=B({name:"objective-ques",__name:"index",setup(r){const a=L(),t=s(null),w=s(null),d=s({}),y=N({labelWidth:"68px",itemWidth:"160px",rules:{username:[{trigger:["blur","change"],validator:(e,n,i)=>{if(n&&n.length>50)return i(new Error("账号长度不能超过50！"));i()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>K.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>u.value},{label:"试卷",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择试卷",optionData:()=>u.value},{label:"题型",prop:"subject_id1",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择题型",optionData:()=>u.value},{label:"评分状态",prop:"subject_id1",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评分状态",optionData:()=>[]},{label:"试题编号",prop:"username",type:"input",defaultValue:"",placeholder:"请输入小组名称",clearable:!0}]}),u=s([]),c=s({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"id_card_encipher1",label:"题型",minWidth:"140px"},{prop:"ques_code",label:"试题编号",minWidth:"90px"},{prop:"role_name",label:"评分状态",minWidth:"120px",formatter:e=>I(e.role_name,[{value:1,label:"未评分"},{value:2,label:"评分中"},{value:3,label:"已评分"}])},{prop:"region",label:"评分情况",minWidth:"160px"},{prop:"work_unit",label:"评分进度",minWidth:"120px",type:"progress"},{prop:"work_unit1",label:"评分开始时间",minWidth:"120px"},{prop:"work_unit2",label:"评分结束时间",minWidth:"120px"},{prop:"",label:"操作",type:"template",minWidth:"120px",fixed:"right",templateGroup:[{title:e=>e.role_name===3?"评分结果":e.role_name===1?"暂停评分":"",clickBtn(e){e.role_name===3&&a.push("/grade-management/objective-check/index")}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:3}}),D=s([{project_name:"程序员",subject_name:"程序员(基础知识)",paper_name:"-",id_card_encipher:1,id_card_encipher1:"单选题",ques_code:"0063089EF68827F08D476D0E6E",role_name:2,region:"10/100",work_unit:10,work_unit1:"2025-08-18 14:20"},{project_name:"程序员",subject_name:"程序员(基础知识)",paper_name:"-",id_card_encipher:2,id_card_encipher1:"单选题",ques_code:"0063089EF68827F08D476D0E6ED",role_name:2,region:"40/100",work_unit:40,work_unit1:"2025-08-18 14:20"},{project_name:"程序员",subject_name:"程序员(基础知识)",paper_name:"-",id_card_encipher:3,id_card_encipher1:"单选题",ques_code:"0063089EF68827F08D476D0E6ED",role_name:3,region:"100/100",work_unit:100,work_unit1:"2025-08-18 14:20",work_unit2:"2025-08-18 15:00"}]);let f=null;A(()=>{X(),Q(f,t.value,c.value),_()}),M(()=>{J(f)});const k=(e,n)=>{e.prop==="project_id"&&(u.value=[],d.value.subject_id&&(d.value.subject_id=null),n&&Y(n).then(i=>{u.value=i||[]}))},_=()=>{let{currentPage:e,pageSize:n}=c.value.pageOptions;j({current_page:e,page_size:n},d.value)},F=e=>{c.value.pageOptions.pageSize=e,_()},O=e=>{c.value.pageOptions.currentPage=e,_()};function V(){u.value=[]}return(e,n)=>{const i=p("form-component"),h=p("el-card"),W=p("el-button"),g=p("el-dropdown-item"),C=p("el-dropdown-menu"),z=p("el-dropdown"),E=p("Auth"),S=p("table-component");return T(),U("div",Z,[m("div",$,[o(h,null,{default:l(()=>[m("div",{ref_key:"formDivRef",ref:t},[o(i,{ref_key:"formRef",ref:w,modelValue:d.value,"onUpdate:modelValue":n[0]||(n[0]=q=>d.value=q),"form-options":y,"is-query-btn":!0,onOnchangeFn:k,onQueryDataFn:_,onResetFields:V},null,8,["modelValue","form-options"])],512)]),_:1}),o(h,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:l(()=>[m("div",ee,[o(E,{value:"objective-ques/batchMarking"},{default:l(()=>[o(z,{onCommand:e.handleCommand},{dropdown:l(()=>[o(C,null,{default:l(()=>[o(g,{command:"start"},{default:l(()=>[b("批量开始")]),_:1}),o(g,{command:"pause"},{default:l(()=>[b("批量暂停")]),_:1})]),_:1})]),default:l(()=>[o(W,{type:"primary"},{default:l(()=>[b(" 批量评分 ")]),_:1})]),_:1},8,["onCommand"])]),_:1})]),o(S,{minHeight:c.value.styleOptions.minHeight,"table-options":c.value,"table-data":D.value,onOnHandleSizeChange:F,onOnHandleCurrentChange:O},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),ie=G(te,[["__scopeId","data-v-3c1699e4"]]);export{ie as default};
