import{l as E,m as xe,d as ei,aS as nn,G as rn,aO as Me,az as ti,aa as sn,B as Nt,C as Xe,n as ni,ao as ii,a4 as on,T as ue,r as he,j as ri,w as ln,o as S,c as L,b as y,h as C,e as R,q as ot,f as B,t as P,y as z,ae as an,z as si,g as _e,F as pe,u as V,p as lt,U as yt,i6 as un,aV as oi,ac as li,ad as ai,_ as ui}from"./index-B63pSD2p.js";import{V as ci}from"./vue-drag-resize-CJIVu41Q.js";import{R as fi}from"./index-BzG0ERft.js";import di from"./problem-CRChWLev.js";import{z as hi,Z as pi,a as mi,b as vi,r as gi,T as _i,c as ki,A as wi,i as bi}from"./anticlockwise-2-line-Iit2_C-u.js";import{R as Ni}from"./reply-fill-DeI84Ty7.js";import{a as yi}from"./base-DyvdloLK.js";import{a as qi,c as Ei}from"./marking-paper-CSlVaOqn.js";import{c as Mi}from"./checkInfo-mJiqvlDC.js";import{g as Si}from"./quesNum-CouueI57.js";/*!
 *  decimal.js v10.4.3
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
 *  MIT Licence
 */var Ze=9e15,Ae=1e9,qt="0123456789abcdef",ut="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",ct="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Et={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-Ze,maxE:Ze,crypto:!1},hn,ke,q=!0,dt="[DecimalError] ",Te=dt+"Invalid argument: ",pn=dt+"Precision limit exceeded",mn=dt+"crypto unavailable",vn="[object Decimal]",ie=Math.floor,X=Math.pow,Ci=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,Ti=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,Ai=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,gn=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,fe=1e7,b=7,Ri=9007199254740991,xi=ut.length-1,Mt=ct.length-1,m={toStringTag:vn};m.absoluteValue=m.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),k(e)};m.ceil=function(){return k(new this.constructor(this),this.e+1,2)};m.clampedTo=m.clamp=function(e,t){var n,i=this,r=i.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(Te+t);return n=i.cmp(e),n<0?e:i.cmp(t)>0?t:new r(i)};m.comparedTo=m.cmp=function(e){var t,n,i,r,s=this,o=s.d,l=(e=new s.constructor(e)).d,u=s.s,a=e.s;if(!o||!l)return!u||!a?NaN:u!==a?u:o===l?0:!o^u<0?1:-1;if(!o[0]||!l[0])return o[0]?u:l[0]?-a:0;if(u!==a)return u;if(s.e!==e.e)return s.e>e.e^u<0?1:-1;for(i=o.length,r=l.length,t=0,n=i<r?i:r;t<n;++t)if(o[t]!==l[t])return o[t]>l[t]^u<0?1:-1;return i===r?0:i>r^u<0?1:-1};m.cosine=m.cos=function(){var e,t,n=this,i=n.constructor;return n.d?n.d[0]?(e=i.precision,t=i.rounding,i.precision=e+Math.max(n.e,n.sd())+b,i.rounding=1,n=Li(i,Nn(i,n)),i.precision=e,i.rounding=t,k(ke==2||ke==3?n.neg():n,e,t,!0)):new i(1):new i(NaN)};m.cubeRoot=m.cbrt=function(){var e,t,n,i,r,s,o,l,u,a,c=this,d=c.constructor;if(!c.isFinite()||c.isZero())return new d(c);for(q=!1,s=c.s*X(c.s*c,1/3),!s||Math.abs(s)==1/0?(n=te(c.d),e=c.e,(s=(e-n.length+1)%3)&&(n+=s==1||s==-2?"0":"00"),s=X(n,1/3),e=ie((e+1)/3)-(e%3==(e<0?-1:2)),s==1/0?n="5e"+e:(n=s.toExponential(),n=n.slice(0,n.indexOf("e")+1)+e),i=new d(n),i.s=c.s):i=new d(s.toString()),o=(e=d.precision)+3;;)if(l=i,u=l.times(l).times(l),a=u.plus(c),i=Z(a.plus(c).times(l),a.plus(u),o+2,1),te(l.d).slice(0,o)===(n=te(i.d)).slice(0,o))if(n=n.slice(o-3,o+1),n=="9999"||!r&&n=="4999"){if(!r&&(k(l,e+1,0),l.times(l).times(l).eq(c))){i=l;break}o+=4,r=1}else{(!+n||!+n.slice(1)&&n.charAt(0)=="5")&&(k(i,e+1,1),t=!i.times(i).times(i).eq(c));break}return q=!0,k(i,e,d.rounding,t)};m.decimalPlaces=m.dp=function(){var e,t=this.d,n=NaN;if(t){if(e=t.length-1,n=(e-ie(this.e/b))*b,e=t[e],e)for(;e%10==0;e/=10)n--;n<0&&(n=0)}return n};m.dividedBy=m.div=function(e){return Z(this,new this.constructor(e))};m.dividedToIntegerBy=m.divToInt=function(e){var t=this,n=t.constructor;return k(Z(t,new n(e),0,1,1),n.precision,n.rounding)};m.equals=m.eq=function(e){return this.cmp(e)===0};m.floor=function(){return k(new this.constructor(this),this.e+1,3)};m.greaterThan=m.gt=function(e){return this.cmp(e)>0};m.greaterThanOrEqualTo=m.gte=function(e){var t=this.cmp(e);return t==1||t===0};m.hyperbolicCosine=m.cosh=function(){var e,t,n,i,r,s=this,o=s.constructor,l=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return l;n=o.precision,i=o.rounding,o.precision=n+Math.max(s.e,s.sd())+4,o.rounding=1,r=s.d.length,r<32?(e=Math.ceil(r/3),t=(1/pt(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),s=$e(o,1,s.times(t),new o(1),!0);for(var u,a=e,c=new o(8);a--;)u=s.times(s),s=l.minus(u.times(c.minus(u.times(c))));return k(s,o.precision=n,o.rounding=i,!0)};m.hyperbolicSine=m.sinh=function(){var e,t,n,i,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(t=s.precision,n=s.rounding,s.precision=t+Math.max(r.e,r.sd())+4,s.rounding=1,i=r.d.length,i<3)r=$e(s,2,r,r,!0);else{e=1.4*Math.sqrt(i),e=e>16?16:e|0,r=r.times(1/pt(5,e)),r=$e(s,2,r,r,!0);for(var o,l=new s(5),u=new s(16),a=new s(20);e--;)o=r.times(r),r=r.times(l.plus(o.times(u.times(o).plus(a))))}return s.precision=t,s.rounding=n,k(r,t,n,!0)};m.hyperbolicTangent=m.tanh=function(){var e,t,n=this,i=n.constructor;return n.isFinite()?n.isZero()?new i(n):(e=i.precision,t=i.rounding,i.precision=e+7,i.rounding=1,Z(n.sinh(),n.cosh(),i.precision=e,i.rounding=t)):new i(n.s)};m.inverseCosine=m.acos=function(){var e,t=this,n=t.constructor,i=t.abs().cmp(1),r=n.precision,s=n.rounding;return i!==-1?i===0?t.isNeg()?ce(n,r,s):new n(0):new n(NaN):t.isZero()?ce(n,r+4,s).times(.5):(n.precision=r+6,n.rounding=1,t=t.asin(),e=ce(n,r+4,s).times(.5),n.precision=r,n.rounding=s,e.minus(t))};m.inverseHyperbolicCosine=m.acosh=function(){var e,t,n=this,i=n.constructor;return n.lte(1)?new i(n.eq(1)?0:NaN):n.isFinite()?(e=i.precision,t=i.rounding,i.precision=e+Math.max(Math.abs(n.e),n.sd())+4,i.rounding=1,q=!1,n=n.times(n).minus(1).sqrt().plus(n),q=!0,i.precision=e,i.rounding=t,n.ln()):new i(n)};m.inverseHyperbolicSine=m.asinh=function(){var e,t,n=this,i=n.constructor;return!n.isFinite()||n.isZero()?new i(n):(e=i.precision,t=i.rounding,i.precision=e+2*Math.max(Math.abs(n.e),n.sd())+6,i.rounding=1,q=!1,n=n.times(n).plus(1).sqrt().plus(n),q=!0,i.precision=e,i.rounding=t,n.ln())};m.inverseHyperbolicTangent=m.atanh=function(){var e,t,n,i,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,t=s.rounding,i=r.sd(),Math.max(i,e)<2*-r.e-1?k(new s(r),e,t,!0):(s.precision=n=i-r.e,r=Z(r.plus(1),new s(1).minus(r),n+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=t,r.times(.5))):new s(NaN)};m.inverseSine=m.asin=function(){var e,t,n,i,r=this,s=r.constructor;return r.isZero()?new s(r):(t=r.abs().cmp(1),n=s.precision,i=s.rounding,t!==-1?t===0?(e=ce(s,n+4,i).times(.5),e.s=r.s,e):new s(NaN):(s.precision=n+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=n,s.rounding=i,r.times(2)))};m.inverseTangent=m.atan=function(){var e,t,n,i,r,s,o,l,u,a=this,c=a.constructor,d=c.precision,h=c.rounding;if(a.isFinite()){if(a.isZero())return new c(a);if(a.abs().eq(1)&&d+4<=Mt)return o=ce(c,d+4,h).times(.25),o.s=a.s,o}else{if(!a.s)return new c(NaN);if(d+4<=Mt)return o=ce(c,d+4,h).times(.5),o.s=a.s,o}for(c.precision=l=d+10,c.rounding=1,n=Math.min(28,l/b+2|0),e=n;e;--e)a=a.div(a.times(a).plus(1).sqrt().plus(1));for(q=!1,t=Math.ceil(l/b),i=1,u=a.times(a),o=new c(a),r=a;e!==-1;)if(r=r.times(u),s=o.minus(r.div(i+=2)),r=r.times(u),o=s.plus(r.div(i+=2)),o.d[t]!==void 0)for(e=t;o.d[e]===s.d[e]&&e--;);return n&&(o=o.times(2<<n-1)),q=!0,k(o,c.precision=d,c.rounding=h,!0)};m.isFinite=function(){return!!this.d};m.isInteger=m.isInt=function(){return!!this.d&&ie(this.e/b)>this.d.length-2};m.isNaN=function(){return!this.s};m.isNegative=m.isNeg=function(){return this.s<0};m.isPositive=m.isPos=function(){return this.s>0};m.isZero=function(){return!!this.d&&this.d[0]===0};m.lessThan=m.lt=function(e){return this.cmp(e)<0};m.lessThanOrEqualTo=m.lte=function(e){return this.cmp(e)<1};m.logarithm=m.log=function(e){var t,n,i,r,s,o,l,u,a=this,c=a.constructor,d=c.precision,h=c.rounding,g=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),n=e.d,e.s<0||!n||!n[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(n=a.d,a.s<0||!n||!n[0]||a.eq(1))return new c(n&&!n[0]?-1/0:a.s!=1?NaN:n?0:1/0);if(t)if(n.length>1)s=!0;else{for(r=n[0];r%10===0;)r/=10;s=r!==1}if(q=!1,l=d+g,o=Ce(a,l),i=t?ft(c,l+10):Ce(e,l),u=Z(o,i,l,1),Je(u.d,r=d,h))do if(l+=10,o=Ce(a,l),i=t?ft(c,l+10):Ce(e,l),u=Z(o,i,l,1),!s){+te(u.d).slice(r+1,r+15)+1==1e14&&(u=k(u,d+1,0));break}while(Je(u.d,r+=10,h));return q=!0,k(u,d,h)};m.minus=m.sub=function(e){var t,n,i,r,s,o,l,u,a,c,d,h,g=this,T=g.constructor;if(e=new T(e),!g.d||!e.d)return!g.s||!e.s?e=new T(NaN):g.d?e.s=-e.s:e=new T(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(a=g.d,h=e.d,l=T.precision,u=T.rounding,!a[0]||!h[0]){if(h[0])e.s=-e.s;else if(a[0])e=new T(g);else return new T(u===3?-0:0);return q?k(e,l,u):e}if(n=ie(e.e/b),c=ie(g.e/b),a=a.slice(),s=c-n,s){for(d=s<0,d?(t=a,s=-s,o=h.length):(t=h,n=c,o=a.length),i=Math.max(Math.ceil(l/b),o)+2,s>i&&(s=i,t.length=1),t.reverse(),i=s;i--;)t.push(0);t.reverse()}else{for(i=a.length,o=h.length,d=i<o,d&&(o=i),i=0;i<o;i++)if(a[i]!=h[i]){d=a[i]<h[i];break}s=0}for(d&&(t=a,a=h,h=t,e.s=-e.s),o=a.length,i=h.length-o;i>0;--i)a[o++]=0;for(i=h.length;i>s;){if(a[--i]<h[i]){for(r=i;r&&a[--r]===0;)a[r]=fe-1;--a[r],a[i]+=fe}a[i]-=h[i]}for(;a[--o]===0;)a.pop();for(;a[0]===0;a.shift())--n;return a[0]?(e.d=a,e.e=ht(a,n),q?k(e,l,u):e):new T(u===3?-0:0)};m.modulo=m.mod=function(e){var t,n=this,i=n.constructor;return e=new i(e),!n.d||!e.s||e.d&&!e.d[0]?new i(NaN):!e.d||n.d&&!n.d[0]?k(new i(n),i.precision,i.rounding):(q=!1,i.modulo==9?(t=Z(n,e.abs(),0,3,1),t.s*=e.s):t=Z(n,e,0,i.modulo,1),t=t.times(e),q=!0,n.minus(t))};m.naturalExponential=m.exp=function(){return St(this)};m.naturalLogarithm=m.ln=function(){return Ce(this)};m.negated=m.neg=function(){var e=new this.constructor(this);return e.s=-e.s,k(e)};m.plus=m.add=function(e){var t,n,i,r,s,o,l,u,a,c,d=this,h=d.constructor;if(e=new h(e),!d.d||!e.d)return!d.s||!e.s?e=new h(NaN):d.d||(e=new h(e.d||d.s===e.s?d:NaN)),e;if(d.s!=e.s)return e.s=-e.s,d.minus(e);if(a=d.d,c=e.d,l=h.precision,u=h.rounding,!a[0]||!c[0])return c[0]||(e=new h(d)),q?k(e,l,u):e;if(s=ie(d.e/b),i=ie(e.e/b),a=a.slice(),r=s-i,r){for(r<0?(n=a,r=-r,o=c.length):(n=c,i=s,o=a.length),s=Math.ceil(l/b),o=s>o?s+1:o+1,r>o&&(r=o,n.length=1),n.reverse();r--;)n.push(0);n.reverse()}for(o=a.length,r=c.length,o-r<0&&(r=o,n=c,c=a,a=n),t=0;r;)t=(a[--r]=a[r]+c[r]+t)/fe|0,a[r]%=fe;for(t&&(a.unshift(t),++i),o=a.length;a[--o]==0;)a.pop();return e.d=a,e.e=ht(a,i),q?k(e,l,u):e};m.precision=m.sd=function(e){var t,n=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Te+e);return n.d?(t=_n(n.d),e&&n.e+1>t&&(t=n.e+1)):t=NaN,t};m.round=function(){var e=this,t=e.constructor;return k(new t(e),e.e+1,t.rounding)};m.sine=m.sin=function(){var e,t,n=this,i=n.constructor;return n.isFinite()?n.isZero()?new i(n):(e=i.precision,t=i.rounding,i.precision=e+Math.max(n.e,n.sd())+b,i.rounding=1,n=Fi(i,Nn(i,n)),i.precision=e,i.rounding=t,k(ke>2?n.neg():n,e,t,!0)):new i(NaN)};m.squareRoot=m.sqrt=function(){var e,t,n,i,r,s,o=this,l=o.d,u=o.e,a=o.s,c=o.constructor;if(a!==1||!l||!l[0])return new c(!a||a<0&&(!l||l[0])?NaN:l?o:1/0);for(q=!1,a=Math.sqrt(+o),a==0||a==1/0?(t=te(l),(t.length+u)%2==0&&(t+="0"),a=Math.sqrt(t),u=ie((u+1)/2)-(u<0||u%2),a==1/0?t="5e"+u:(t=a.toExponential(),t=t.slice(0,t.indexOf("e")+1)+u),i=new c(t)):i=new c(a.toString()),n=(u=c.precision)+3;;)if(s=i,i=s.plus(Z(o,s,n+2,1)).times(.5),te(s.d).slice(0,n)===(t=te(i.d)).slice(0,n))if(t=t.slice(n-3,n+1),t=="9999"||!r&&t=="4999"){if(!r&&(k(s,u+1,0),s.times(s).eq(o))){i=s;break}n+=4,r=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(k(i,u+1,1),e=!i.times(i).eq(o));break}return q=!0,k(i,u,c.rounding,e)};m.tangent=m.tan=function(){var e,t,n=this,i=n.constructor;return n.isFinite()?n.isZero()?new i(n):(e=i.precision,t=i.rounding,i.precision=e+10,i.rounding=1,n=n.sin(),n.s=1,n=Z(n,new i(1).minus(n.times(n)).sqrt(),e+10,0),i.precision=e,i.rounding=t,k(ke==2||ke==4?n.neg():n,e,t,!0)):new i(NaN)};m.times=m.mul=function(e){var t,n,i,r,s,o,l,u,a,c=this,d=c.constructor,h=c.d,g=(e=new d(e)).d;if(e.s*=c.s,!h||!h[0]||!g||!g[0])return new d(!e.s||h&&!h[0]&&!g||g&&!g[0]&&!h?NaN:!h||!g?e.s/0:e.s*0);for(n=ie(c.e/b)+ie(e.e/b),u=h.length,a=g.length,u<a&&(s=h,h=g,g=s,o=u,u=a,a=o),s=[],o=u+a,i=o;i--;)s.push(0);for(i=a;--i>=0;){for(t=0,r=u+i;r>i;)l=s[r]+g[i]*h[r-i-1]+t,s[r--]=l%fe|0,t=l/fe|0;s[r]=(s[r]+t)%fe|0}for(;!s[--o];)s.pop();return t?++n:s.shift(),e.d=s,e.e=ht(s,n),q?k(e,d.precision,d.rounding):e};m.toBinary=function(e,t){return At(this,2,e,t)};m.toDecimalPlaces=m.toDP=function(e,t){var n=this,i=n.constructor;return n=new i(n),e===void 0?n:(se(e,0,Ae),t===void 0?t=i.rounding:se(t,0,8),k(n,e+n.e+1,t))};m.toExponential=function(e,t){var n,i=this,r=i.constructor;return e===void 0?n=me(i,!0):(se(e,0,Ae),t===void 0?t=r.rounding:se(t,0,8),i=k(new r(i),e+1,t),n=me(i,!0,e+1)),i.isNeg()&&!i.isZero()?"-"+n:n};m.toFixed=function(e,t){var n,i,r=this,s=r.constructor;return e===void 0?n=me(r):(se(e,0,Ae),t===void 0?t=s.rounding:se(t,0,8),i=k(new s(r),e+r.e+1,t),n=me(i,!1,e+i.e+1)),r.isNeg()&&!r.isZero()?"-"+n:n};m.toFraction=function(e){var t,n,i,r,s,o,l,u,a,c,d,h,g=this,T=g.d,N=g.constructor;if(!T)return new N(g);if(a=n=new N(1),i=u=new N(0),t=new N(i),s=t.e=_n(T)-g.e-1,o=s%b,t.d[0]=X(10,o<0?b+o:o),e==null)e=s>0?t:a;else{if(l=new N(e),!l.isInt()||l.lt(a))throw Error(Te+l);e=l.gt(t)?s>0?t:a:l}for(q=!1,l=new N(te(T)),c=N.precision,N.precision=s=T.length*b*2;d=Z(l,t,0,1,1),r=n.plus(d.times(i)),r.cmp(e)!=1;)n=i,i=r,r=a,a=u.plus(d.times(r)),u=r,r=t,t=l.minus(d.times(r)),l=r;return r=Z(e.minus(n),i,0,1,1),u=u.plus(r.times(a)),n=n.plus(r.times(i)),u.s=a.s=g.s,h=Z(a,i,s,1).minus(g).abs().cmp(Z(u,n,s,1).minus(g).abs())<1?[a,i]:[u,n],N.precision=c,q=!0,h};m.toHexadecimal=m.toHex=function(e,t){return At(this,16,e,t)};m.toNearest=function(e,t){var n=this,i=n.constructor;if(n=new i(n),e==null){if(!n.d)return n;e=new i(1),t=i.rounding}else{if(e=new i(e),t===void 0?t=i.rounding:se(t,0,8),!n.d)return e.s?n:e;if(!e.d)return e.s&&(e.s=n.s),e}return e.d[0]?(q=!1,n=Z(n,e,0,t,1).times(e),q=!0,k(n)):(e.s=n.s,n=e),n};m.toNumber=function(){return+this};m.toOctal=function(e,t){return At(this,8,e,t)};m.toPower=m.pow=function(e){var t,n,i,r,s,o,l=this,u=l.constructor,a=+(e=new u(e));if(!l.d||!e.d||!l.d[0]||!e.d[0])return new u(X(+l,a));if(l=new u(l),l.eq(1))return l;if(i=u.precision,s=u.rounding,e.eq(1))return k(l,i,s);if(t=ie(e.e/b),t>=e.d.length-1&&(n=a<0?-a:a)<=Ri)return r=kn(u,l,n,i),e.s<0?new u(1).div(r):k(r,i,s);if(o=l.s,o<0){if(t<e.d.length-1)return new u(NaN);if(e.d[t]&1||(o=1),l.e==0&&l.d[0]==1&&l.d.length==1)return l.s=o,l}return n=X(+l,a),t=n==0||!isFinite(n)?ie(a*(Math.log("0."+te(l.d))/Math.LN10+l.e+1)):new u(n+"").e,t>u.maxE+1||t<u.minE-1?new u(t>0?o/0:0):(q=!1,u.rounding=l.s=1,n=Math.min(12,(t+"").length),r=St(e.times(Ce(l,i+n)),i),r.d&&(r=k(r,i+5,1),Je(r.d,i,s)&&(t=i+10,r=k(St(e.times(Ce(l,t+n)),t),t+5,1),+te(r.d).slice(i+1,i+15)+1==1e14&&(r=k(r,i+1,0)))),r.s=o,q=!0,u.rounding=s,k(r,i,s))};m.toPrecision=function(e,t){var n,i=this,r=i.constructor;return e===void 0?n=me(i,i.e<=r.toExpNeg||i.e>=r.toExpPos):(se(e,1,Ae),t===void 0?t=r.rounding:se(t,0,8),i=k(new r(i),e,t),n=me(i,e<=i.e||i.e<=r.toExpNeg,e)),i.isNeg()&&!i.isZero()?"-"+n:n};m.toSignificantDigits=m.toSD=function(e,t){var n=this,i=n.constructor;return e===void 0?(e=i.precision,t=i.rounding):(se(e,1,Ae),t===void 0?t=i.rounding:se(t,0,8)),k(new i(n),e,t)};m.toString=function(){var e=this,t=e.constructor,n=me(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+n:n};m.truncated=m.trunc=function(){return k(new this.constructor(this),this.e+1,1)};m.valueOf=m.toJSON=function(){var e=this,t=e.constructor,n=me(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+n:n};function te(e){var t,n,i,r=e.length-1,s="",o=e[0];if(r>0){for(s+=o,t=1;t<r;t++)i=e[t]+"",n=b-i.length,n&&(s+=Se(n)),s+=i;o=e[t],i=o+"",n=b-i.length,n&&(s+=Se(n))}else if(o===0)return"0";for(;o%10===0;)o/=10;return s+o}function se(e,t,n){if(e!==~~e||e<t||e>n)throw Error(Te+e)}function Je(e,t,n,i){var r,s,o,l;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=b,r=0):(r=Math.ceil((t+1)/b),t%=b),s=X(10,b-t),l=e[r]%s|0,i==null?t<3?(t==0?l=l/100|0:t==1&&(l=l/10|0),o=n<4&&l==99999||n>3&&l==49999||l==5e4||l==0):o=(n<4&&l+1==s||n>3&&l+1==s/2)&&(e[r+1]/s/100|0)==X(10,t-2)-1||(l==s/2||l==0)&&(e[r+1]/s/100|0)==0:t<4?(t==0?l=l/1e3|0:t==1?l=l/100|0:t==2&&(l=l/10|0),o=(i||n<4)&&l==9999||!i&&n>3&&l==4999):o=((i||n<4)&&l+1==s||!i&&n>3&&l+1==s/2)&&(e[r+1]/s/1e3|0)==X(10,t-3)-1,o}function at(e,t,n){for(var i,r=[0],s,o=0,l=e.length;o<l;){for(s=r.length;s--;)r[s]*=t;for(r[0]+=qt.indexOf(e.charAt(o++)),i=0;i<r.length;i++)r[i]>n-1&&(r[i+1]===void 0&&(r[i+1]=0),r[i+1]+=r[i]/n|0,r[i]%=n)}return r.reverse()}function Li(e,t){var n,i,r;if(t.isZero())return t;i=t.d.length,i<32?(n=Math.ceil(i/3),r=(1/pt(4,n)).toString()):(n=16,r="2.3283064365386962890625e-10"),e.precision+=n,t=$e(e,1,t.times(r),new e(1));for(var s=n;s--;){var o=t.times(t);t=o.times(o).minus(o).times(8).plus(1)}return e.precision-=n,t}var Z=function(){function e(i,r,s){var o,l=0,u=i.length;for(i=i.slice();u--;)o=i[u]*r+l,i[u]=o%s|0,l=o/s|0;return l&&i.unshift(l),i}function t(i,r,s,o){var l,u;if(s!=o)u=s>o?1:-1;else for(l=u=0;l<s;l++)if(i[l]!=r[l]){u=i[l]>r[l]?1:-1;break}return u}function n(i,r,s,o){for(var l=0;s--;)i[s]-=l,l=i[s]<r[s]?1:0,i[s]=l*o+i[s]-r[s];for(;!i[0]&&i.length>1;)i.shift()}return function(i,r,s,o,l,u){var a,c,d,h,g,T,N,Y,$,oe,F,U,Pe,ae,Re,ve,we,Fe,re,be,Ne=i.constructor,Ve=i.s==r.s?1:-1,W=i.d,I=r.d;if(!W||!W[0]||!I||!I[0])return new Ne(!i.s||!r.s||(W?I&&W[0]==I[0]:!I)?NaN:W&&W[0]==0||!I?Ve*0:Ve/0);for(u?(g=1,c=i.e-r.e):(u=fe,g=b,c=ie(i.e/g)-ie(r.e/g)),re=I.length,we=W.length,$=new Ne(Ve),oe=$.d=[],d=0;I[d]==(W[d]||0);d++);if(I[d]>(W[d]||0)&&c--,s==null?(ae=s=Ne.precision,o=Ne.rounding):l?ae=s+(i.e-r.e)+1:ae=s,ae<0)oe.push(1),T=!0;else{if(ae=ae/g+2|0,d=0,re==1){for(h=0,I=I[0],ae++;(d<we||h)&&ae--;d++)Re=h*u+(W[d]||0),oe[d]=Re/I|0,h=Re%I|0;T=h||d<we}else{for(h=u/(I[0]+1)|0,h>1&&(I=e(I,h,u),W=e(W,h,u),re=I.length,we=W.length),ve=re,F=W.slice(0,re),U=F.length;U<re;)F[U++]=0;be=I.slice(),be.unshift(0),Fe=I[0],I[1]>=u/2&&++Fe;do h=0,a=t(I,F,re,U),a<0?(Pe=F[0],re!=U&&(Pe=Pe*u+(F[1]||0)),h=Pe/Fe|0,h>1?(h>=u&&(h=u-1),N=e(I,h,u),Y=N.length,U=F.length,a=t(N,F,Y,U),a==1&&(h--,n(N,re<Y?be:I,Y,u))):(h==0&&(a=h=1),N=I.slice()),Y=N.length,Y<U&&N.unshift(0),n(F,N,U,u),a==-1&&(U=F.length,a=t(I,F,re,U),a<1&&(h++,n(F,re<U?be:I,U,u))),U=F.length):a===0&&(h++,F=[0]),oe[d++]=h,a&&F[0]?F[U++]=W[ve]||0:(F=[W[ve]],U=1);while((ve++<we||F[0]!==void 0)&&ae--);T=F[0]!==void 0}oe[0]||oe.shift()}if(g==1)$.e=c,hn=T;else{for(d=1,h=oe[0];h>=10;h/=10)d++;$.e=d+c*g-1,k($,l?s+$.e+1:s,o,T)}return $}}();function k(e,t,n,i){var r,s,o,l,u,a,c,d,h,g=e.constructor;e:if(t!=null){if(d=e.d,!d)return e;for(r=1,l=d[0];l>=10;l/=10)r++;if(s=t-r,s<0)s+=b,o=t,c=d[h=0],u=c/X(10,r-o-1)%10|0;else if(h=Math.ceil((s+1)/b),l=d.length,h>=l)if(i){for(;l++<=h;)d.push(0);c=u=0,r=1,s%=b,o=s-b+1}else break e;else{for(c=l=d[h],r=1;l>=10;l/=10)r++;s%=b,o=s-b+r,u=o<0?0:c/X(10,r-o-1)%10|0}if(i=i||t<0||d[h+1]!==void 0||(o<0?c:c%X(10,r-o-1)),a=n<4?(u||i)&&(n==0||n==(e.s<0?3:2)):u>5||u==5&&(n==4||i||n==6&&(s>0?o>0?c/X(10,r-o):0:d[h-1])%10&1||n==(e.s<0?8:7)),t<1||!d[0])return d.length=0,a?(t-=e.e+1,d[0]=X(10,(b-t%b)%b),e.e=-t||0):d[0]=e.e=0,e;if(s==0?(d.length=h,l=1,h--):(d.length=h+1,l=X(10,b-s),d[h]=o>0?(c/X(10,r-o)%X(10,o)|0)*l:0),a)for(;;)if(h==0){for(s=1,o=d[0];o>=10;o/=10)s++;for(o=d[0]+=l,l=1;o>=10;o/=10)l++;s!=l&&(e.e++,d[0]==fe&&(d[0]=1));break}else{if(d[h]+=l,d[h]!=fe)break;d[h--]=0,l=1}for(s=d.length;d[--s]===0;)d.pop()}return q&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function me(e,t,n){if(!e.isFinite())return bn(e);var i,r=e.e,s=te(e.d),o=s.length;return t?(n&&(i=n-o)>0?s=s.charAt(0)+"."+s.slice(1)+Se(i):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):r<0?(s="0."+Se(-r-1)+s,n&&(i=n-o)>0&&(s+=Se(i))):r>=o?(s+=Se(r+1-o),n&&(i=n-r-1)>0&&(s=s+"."+Se(i))):((i=r+1)<o&&(s=s.slice(0,i)+"."+s.slice(i)),n&&(i=n-o)>0&&(r+1===o&&(s+="."),s+=Se(i))),s}function ht(e,t){var n=e[0];for(t*=b;n>=10;n/=10)t++;return t}function ft(e,t,n){if(t>xi)throw q=!0,n&&(e.precision=n),Error(pn);return k(new e(ut),t,1,!0)}function ce(e,t,n){if(t>Mt)throw Error(pn);return k(new e(ct),t,n,!0)}function _n(e){var t=e.length-1,n=t*b+1;if(t=e[t],t){for(;t%10==0;t/=10)n--;for(t=e[0];t>=10;t/=10)n++}return n}function Se(e){for(var t="";e--;)t+="0";return t}function kn(e,t,n,i){var r,s=new e(1),o=Math.ceil(i/b+4);for(q=!1;;){if(n%2&&(s=s.times(t),fn(s.d,o)&&(r=!0)),n=ie(n/2),n===0){n=s.d.length-1,r&&s.d[n]===0&&++s.d[n];break}t=t.times(t),fn(t.d,o)}return q=!0,s}function cn(e){return e.d[e.d.length-1]&1}function wn(e,t,n){for(var i,r=new e(t[0]),s=0;++s<t.length;)if(i=new e(t[s]),i.s)r[n](i)&&(r=i);else{r=i;break}return r}function St(e,t){var n,i,r,s,o,l,u,a=0,c=0,d=0,h=e.constructor,g=h.rounding,T=h.precision;if(!e.d||!e.d[0]||e.e>17)return new h(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(q=!1,u=T):u=t,l=new h(.03125);e.e>-2;)e=e.times(l),d+=5;for(i=Math.log(X(2,d))/Math.LN10*2+5|0,u+=i,n=s=o=new h(1),h.precision=u;;){if(s=k(s.times(e),u,1),n=n.times(++c),l=o.plus(Z(s,n,u,1)),te(l.d).slice(0,u)===te(o.d).slice(0,u)){for(r=d;r--;)o=k(o.times(o),u,1);if(t==null)if(a<3&&Je(o.d,u-i,g,a))h.precision=u+=10,n=s=l=new h(1),c=0,a++;else return k(o,h.precision=T,g,q=!0);else return h.precision=T,o}o=l}}function Ce(e,t){var n,i,r,s,o,l,u,a,c,d,h,g=1,T=10,N=e,Y=N.d,$=N.constructor,oe=$.rounding,F=$.precision;if(N.s<0||!Y||!Y[0]||!N.e&&Y[0]==1&&Y.length==1)return new $(Y&&!Y[0]?-1/0:N.s!=1?NaN:Y?0:N);if(t==null?(q=!1,c=F):c=t,$.precision=c+=T,n=te(Y),i=n.charAt(0),Math.abs(s=N.e)<15e14){for(;i<7&&i!=1||i==1&&n.charAt(1)>3;)N=N.times(e),n=te(N.d),i=n.charAt(0),g++;s=N.e,i>1?(N=new $("0."+n),s++):N=new $(i+"."+n.slice(1))}else return a=ft($,c+2,F).times(s+""),N=Ce(new $(i+"."+n.slice(1)),c-T).plus(a),$.precision=F,t==null?k(N,F,oe,q=!0):N;for(d=N,u=o=N=Z(N.minus(1),N.plus(1),c,1),h=k(N.times(N),c,1),r=3;;){if(o=k(o.times(h),c,1),a=u.plus(Z(o,new $(r),c,1)),te(a.d).slice(0,c)===te(u.d).slice(0,c))if(u=u.times(2),s!==0&&(u=u.plus(ft($,c+2,F).times(s+""))),u=Z(u,new $(g),c,1),t==null)if(Je(u.d,c-T,oe,l))$.precision=c+=T,a=o=N=Z(d.minus(1),d.plus(1),c,1),h=k(N.times(N),c,1),r=l=1;else return k(u,$.precision=F,oe,q=!0);else return $.precision=F,u;u=a,r+=2}}function bn(e){return String(e.s*e.s/0)}function Ct(e,t){var n,i,r;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(i=t.search(/e/i))>0?(n<0&&(n=i),n+=+t.slice(i+1),t=t.substring(0,i)):n<0&&(n=t.length),i=0;t.charCodeAt(i)===48;i++);for(r=t.length;t.charCodeAt(r-1)===48;--r);if(t=t.slice(i,r),t){if(r-=i,e.e=n=n-i-1,e.d=[],i=(n+1)%b,n<0&&(i+=b),i<r){for(i&&e.d.push(+t.slice(0,i)),r-=b;i<r;)e.d.push(+t.slice(i,i+=b));t=t.slice(i),i=b-t.length}else i-=r;for(;i--;)t+="0";e.d.push(+t),q&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function Pi(e,t){var n,i,r,s,o,l,u,a,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),gn.test(t))return Ct(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(Ti.test(t))n=16,t=t.toLowerCase();else if(Ci.test(t))n=2;else if(Ai.test(t))n=8;else throw Error(Te+t);for(s=t.search(/p/i),s>0?(u=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),s=t.indexOf("."),o=s>=0,i=e.constructor,o&&(t=t.replace(".",""),l=t.length,s=l-s,r=kn(i,new i(n),s,s*2)),a=at(t,n,fe),c=a.length-1,s=c;a[s]===0;--s)a.pop();return s<0?new i(e.s*0):(e.e=ht(a,c),e.d=a,q=!1,o&&(e=Z(e,r,l*4)),u&&(e=e.times(Math.abs(u)<54?X(2,u):Ue.pow(2,u))),q=!0,e)}function Fi(e,t){var n,i=t.d.length;if(i<3)return t.isZero()?t:$e(e,2,t,t);n=1.4*Math.sqrt(i),n=n>16?16:n|0,t=t.times(1/pt(5,n)),t=$e(e,2,t,t);for(var r,s=new e(5),o=new e(16),l=new e(20);n--;)r=t.times(t),t=t.times(s.plus(r.times(o.times(r).minus(l))));return t}function $e(e,t,n,i,r){var s,o,l,u,a=e.precision,c=Math.ceil(a/b);for(q=!1,u=n.times(n),l=new e(i);;){if(o=Z(l.times(u),new e(t++*t++),a,1),l=r?i.plus(o):i.minus(o),i=Z(o.times(u),new e(t++*t++),a,1),o=l.plus(i),o.d[c]!==void 0){for(s=c;o.d[s]===l.d[s]&&s--;);if(s==-1)break}s=l,l=i,i=o,o=s}return q=!0,o.d.length=c+1,o}function pt(e,t){for(var n=e;--t;)n*=e;return n}function Nn(e,t){var n,i=t.s<0,r=ce(e,e.precision,1),s=r.times(.5);if(t=t.abs(),t.lte(s))return ke=i?4:1,t;if(n=t.divToInt(r),n.isZero())ke=i?3:2;else{if(t=t.minus(n.times(r)),t.lte(s))return ke=cn(n)?i?2:3:i?4:1,t;ke=cn(n)?i?1:4:i?3:2}return t.minus(r).abs()}function At(e,t,n,i){var r,s,o,l,u,a,c,d,h,g=e.constructor,T=n!==void 0;if(T?(se(n,1,Ae),i===void 0?i=g.rounding:se(i,0,8)):(n=g.precision,i=g.rounding),!e.isFinite())c=bn(e);else{for(c=me(e),o=c.indexOf("."),T?(r=2,t==16?n=n*4-3:t==8&&(n=n*3-2)):r=t,o>=0&&(c=c.replace(".",""),h=new g(1),h.e=c.length-o,h.d=at(me(h),10,r),h.e=h.d.length),d=at(c,10,r),s=u=d.length;d[--u]==0;)d.pop();if(!d[0])c=T?"0p+0":"0";else{if(o<0?s--:(e=new g(e),e.d=d,e.e=s,e=Z(e,h,n,i,0,r),d=e.d,s=e.e,a=hn),o=d[n],l=r/2,a=a||d[n+1]!==void 0,a=i<4?(o!==void 0||a)&&(i===0||i===(e.s<0?3:2)):o>l||o===l&&(i===4||a||i===6&&d[n-1]&1||i===(e.s<0?8:7)),d.length=n,a)for(;++d[--n]>r-1;)d[n]=0,n||(++s,d.unshift(1));for(u=d.length;!d[u-1];--u);for(o=0,c="";o<u;o++)c+=qt.charAt(d[o]);if(T){if(u>1)if(t==16||t==8){for(o=t==16?4:3,--u;u%o;u++)c+="0";for(d=at(c,r,t),u=d.length;!d[u-1];--u);for(o=1,c="1.";o<u;o++)c+=qt.charAt(d[o])}else c=c.charAt(0)+"."+c.slice(1);c=c+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)c="0"+c;c="0."+c}else if(++s>u)for(s-=u;s--;)c+="0";else s<u&&(c=c.slice(0,s)+"."+c.slice(s))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function fn(e,t){if(e.length>t)return e.length=t,!0}function Vi(e){return new this(e).abs()}function Di(e){return new this(e).acos()}function Ii(e){return new this(e).acosh()}function Oi(e,t){return new this(e).plus(t)}function Bi(e){return new this(e).asin()}function Zi(e){return new this(e).asinh()}function $i(e){return new this(e).atan()}function Ui(e){return new this(e).atanh()}function Hi(e,t){e=new this(e),t=new this(t);var n,i=this.precision,r=this.rounding,s=i+4;return!e.s||!t.s?n=new this(NaN):!e.d&&!t.d?(n=ce(this,s,1).times(t.s>0?.25:.75),n.s=e.s):!t.d||e.isZero()?(n=t.s<0?ce(this,i,r):new this(0),n.s=e.s):!e.d||t.isZero()?(n=ce(this,s,1).times(.5),n.s=e.s):t.s<0?(this.precision=s,this.rounding=1,n=this.atan(Z(e,t,s,1)),t=ce(this,s,1),this.precision=i,this.rounding=r,n=e.s<0?n.minus(t):n.plus(t)):n=this.atan(Z(e,t,s,1)),n}function zi(e){return new this(e).cbrt()}function Wi(e){return k(e=new this(e),e.e+1,2)}function ji(e,t,n){return new this(e).clamp(t,n)}function Qi(e){if(!e||typeof e!="object")throw Error(dt+"Object expected");var t,n,i,r=e.defaults===!0,s=["precision",1,Ae,"rounding",0,8,"toExpNeg",-Ze,0,"toExpPos",0,Ze,"maxE",0,Ze,"minE",-Ze,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(n=s[t],r&&(this[n]=Et[n]),(i=e[n])!==void 0)if(ie(i)===i&&i>=s[t+1]&&i<=s[t+2])this[n]=i;else throw Error(Te+n+": "+i);if(n="crypto",r&&(this[n]=Et[n]),(i=e[n])!==void 0)if(i===!0||i===!1||i===0||i===1)if(i)if(typeof crypto!="undefined"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[n]=!0;else throw Error(mn);else this[n]=!1;else throw Error(Te+n+": "+i);return this}function Xi(e){return new this(e).cos()}function Ki(e){return new this(e).cosh()}function yn(e){var t,n,i;function r(s){var o,l,u,a=this;if(!(a instanceof r))return new r(s);if(a.constructor=r,dn(s)){a.s=s.s,q?!s.d||s.e>r.maxE?(a.e=NaN,a.d=null):s.e<r.minE?(a.e=0,a.d=[0]):(a.e=s.e,a.d=s.d.slice()):(a.e=s.e,a.d=s.d?s.d.slice():s.d);return}if(u=typeof s,u==="number"){if(s===0){a.s=1/s<0?-1:1,a.e=0,a.d=[0];return}if(s<0?(s=-s,a.s=-1):a.s=1,s===~~s&&s<1e7){for(o=0,l=s;l>=10;l/=10)o++;q?o>r.maxE?(a.e=NaN,a.d=null):o<r.minE?(a.e=0,a.d=[0]):(a.e=o,a.d=[s]):(a.e=o,a.d=[s]);return}else if(s*0!==0){s||(a.s=NaN),a.e=NaN,a.d=null;return}return Ct(a,s.toString())}else if(u!=="string")throw Error(Te+s);return(l=s.charCodeAt(0))===45?(s=s.slice(1),a.s=-1):(l===43&&(s=s.slice(1)),a.s=1),gn.test(s)?Ct(a,s):Pi(a,s)}if(r.prototype=m,r.ROUND_UP=0,r.ROUND_DOWN=1,r.ROUND_CEIL=2,r.ROUND_FLOOR=3,r.ROUND_HALF_UP=4,r.ROUND_HALF_DOWN=5,r.ROUND_HALF_EVEN=6,r.ROUND_HALF_CEIL=7,r.ROUND_HALF_FLOOR=8,r.EUCLID=9,r.config=r.set=Qi,r.clone=yn,r.isDecimal=dn,r.abs=Vi,r.acos=Di,r.acosh=Ii,r.add=Oi,r.asin=Bi,r.asinh=Zi,r.atan=$i,r.atanh=Ui,r.atan2=Hi,r.cbrt=zi,r.ceil=Wi,r.clamp=ji,r.cos=Xi,r.cosh=Ki,r.div=Gi,r.exp=Ji,r.floor=Yi,r.hypot=er,r.ln=tr,r.log=nr,r.log10=rr,r.log2=ir,r.max=sr,r.min=or,r.mod=lr,r.mul=ar,r.pow=ur,r.random=cr,r.round=fr,r.sign=dr,r.sin=hr,r.sinh=pr,r.sqrt=mr,r.sub=vr,r.sum=gr,r.tan=_r,r.tanh=kr,r.trunc=wr,e===void 0&&(e={}),e&&e.defaults!==!0)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<i.length;)e.hasOwnProperty(n=i[t++])||(e[n]=this[n]);return r.config(e),r}function Gi(e,t){return new this(e).div(t)}function Ji(e){return new this(e).exp()}function Yi(e){return k(e=new this(e),e.e+1,3)}function er(){var e,t,n=new this(0);for(q=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)n.d&&(n=n.plus(t.times(t)));else{if(t.s)return q=!0,new this(1/0);n=t}return q=!0,n.sqrt()}function dn(e){return e instanceof Ue||e&&e.toStringTag===vn||!1}function tr(e){return new this(e).ln()}function nr(e,t){return new this(e).log(t)}function ir(e){return new this(e).log(2)}function rr(e){return new this(e).log(10)}function sr(){return wn(this,arguments,"lt")}function or(){return wn(this,arguments,"gt")}function lr(e,t){return new this(e).mod(t)}function ar(e,t){return new this(e).mul(t)}function ur(e,t){return new this(e).pow(t)}function cr(e){var t,n,i,r,s=0,o=new this(1),l=[];if(e===void 0?e=this.precision:se(e,1,Ae),i=Math.ceil(e/b),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(i));s<i;)r=t[s],r>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:l[s++]=r%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(i*=4);s<i;)r=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((t[s+3]&127)<<24),r>=214e7?crypto.randomBytes(4).copy(t,s):(l.push(r%1e7),s+=4);s=i/4}else throw Error(mn);else for(;s<i;)l[s++]=Math.random()*1e7|0;for(i=l[--s],e%=b,i&&e&&(r=X(10,b-e),l[s]=(i/r|0)*r);l[s]===0;s--)l.pop();if(s<0)n=0,l=[0];else{for(n=-1;l[0]===0;n-=b)l.shift();for(i=1,r=l[0];r>=10;r/=10)i++;i<b&&(n-=b-i)}return o.e=n,o.d=l,o}function fr(e){return k(e=new this(e),e.e+1,this.rounding)}function dr(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function hr(e){return new this(e).sin()}function pr(e){return new this(e).sinh()}function mr(e){return new this(e).sqrt()}function vr(e,t){return new this(e).sub(t)}function gr(){var e=0,t=arguments,n=new this(t[e]);for(q=!1;n.s&&++e<t.length;)n=n.plus(t[e]);return q=!0,k(n,this.precision,this.rounding)}function _r(e){return new this(e).tan()}function kr(e){return new this(e).tanh()}function wr(e){return k(e=new this(e),e.e+1,1)}m[Symbol.for("nodejs.util.inspect.custom")]=m.toString;m[Symbol.toStringTag]="Decimal";var Ue=m.constructor=yn(Et);ut=new Ue(ut);ct=new Ue(ct);const br={width:24,height:24,body:'<path fill="currentColor" d="M12 22a9 9 0 1 1 0-18a9 9 0 0 1 0 18Zm1-9V8h-2v7h5v-2h-3ZM1.745 6.283l3.536-3.536l1.414 1.414L3.16 7.697L1.746 6.283Zm16.97-3.536l3.536 3.536l-1.414 1.414l-3.536-3.536l1.415-1.414Z"/>'},Nr=br,yr={width:24,height:24,body:'<path fill="currentColor" d="M3 3h9.382a1 1 0 0 1 .894.553L14 5h6a1 1 0 0 1 1 1v11a1 1 0 0 1-1 1h-6.382a1 1 0 0 1-.894-.553L12 16H5v6H3V3Z"/>'},qr=yr,Ke=E(!1),Tt=E(0),et=E(0),Ge=E(0),Le=E(null),Ye=E([]),qn=E(0),Er=xe(()=>{const e=Math.floor(et.value/1e3),t=Math.floor(e/60),n=e%60;return`${t.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`}),Vs=xe(()=>Math.floor(et.value/1e3));xe(()=>Ye.value.length===0?null:[...Ye.value].sort((e,t)=>e.lapTime-t.lapTime)[0]);xe(()=>Ye.value.length===0?null:[...Ye.value].sort((e,t)=>t.lapTime-e.lapTime)[0]);const Ds=e=>{if(Ke.value)return;const t=e*1e3;et.value=t,qn.value=t},En=e=>{Tt.value||(Tt.value=e,Ge.value=e);const t=e-Ge.value;et.value+=t,Ge.value=e,Le.value=requestAnimationFrame(En)},Is=()=>{Ke.value?(cancelAnimationFrame(Le.value),Le.value=null):(Ge.value=0,Le.value=requestAnimationFrame(En)),Ke.value=!Ke.value},Os=()=>{Le.value&&(cancelAnimationFrame(Le.value),Le.value=null),Ke.value=!1,Tt.value=0,et.value=0,Ge.value=0,Ye.value=[],qn.value=0},Mr="/static/png/image-error-J59V3g7c.png",Sr="/static/png/answer-location-error-CVpl5qe6.png",Cr="/static/png/answer-merge-DJPg3e-N.png",Tr="/static/png/other-D5HBflgb.png",Ar=e=>(li("data-v-8622ddc7"),e=e(),ai(),e),Rr={class:"marking-paper-box"},xr={class:"left-top"},Lr={class:"fixed-top dark:!bg-black eye-box"},Pr={class:"marking-top"},Fr={class:"operation-box center dark:!bg-black eye-box"},Vr={class:"pt-[4px] pb-[4px]"},Dr={key:2,class:"ml-[20px]"},Ir={key:0,class:"operation-btn"},Or={key:0,class:"marking-answer-pane"},Br=["id"],Zr={class:"top"},$r={class:"score-title"},Ur={class:"score-title"},Hr={class:"score-title"},zr={key:0,class:"score-title"},Wr=["onClick"],jr=["src"],Qr={class:"error-tag-box"},Xr=["src"],Kr=["src"],Gr={class:"text"},Jr={key:0,class:"marking-bottom"},Yr={class:"flex"},es={class:"speed"},ts={class:"right"},ns={key:0,class:"timer"},is={class:"mt-[5px]"},rs={class:"title"},ss={class:"zero-box"},os={key:0,class:"mark-panel-box"},ls=["onClick"],as={key:0,style:{display:"inline-block","margin-top":"5px",width:"211px"}},us={key:1},cs=["onClick"],fs={style:{display:"inline-block","min-width":"46px"}},ds={style:{"margin-left":"32px"}},hs={key:0,style:{"margin-bottom":"8px","margin-right":"4px"}},ps={style:{"margin-left":"12px"}},ms={class:"ml-[4px]"},vs={class:"mt-[8px] mb-[6px]"},gs={class:"next"},_s={key:0},ks={class:"mr-[10px]"},ws={class:"mr-[10px]"},bs={key:0,class:"marking-score-box"},Ns={class:"score"},ys=Ar(()=>R("div",{class:"image"},null,-1)),qs=ei({__name:"index",props:nn({isLoadNoMark:{type:String,default:!1},paperTotalScore:Object},{currentQues:{},currentQuesModifiers:{},noMarkPaperList:{},noMarkPaperListModifiers:{},quesInfo:{},quesInfoModifiers:{},markValue:{},markValueModifiers:{},noMarkStuList:{},noMarkStuListModifiers:{},curIndex:{},curIndexModifiers:{},isFocusNext:{},isFocusNextModifiers:{},reMarkingFlag:{},reMarkingFlagModifiers:{}}),emits:nn(["commitMark","leftExpand","goBackToTask","changeMarkValue"],["update:currentQues","update:noMarkPaperList","update:quesInfo","update:markValue","update:noMarkStuList","update:curIndex","update:isFocusNext","update:reMarkingFlag"]),setup(e,{expose:t,emit:n}){let i=rn().smallQuesNumStyle;const r=Me(e,"currentQues"),s=Me(e,"noMarkPaperList"),o=n,l=ti(),u=xe(()=>{var p;const f=Number((p=l.query)==null?void 0:p.type)||0;return f===1?{tableTitle:"已复评考生",singleQuesTit:["复评前分数","复评分","差异分"],paperTotal:["复评前总分","复评总分","差异分"],state:"复评"}:f===2?{tableTitle:"已处理考生",singleQuesTit:["小题得分"],paperTotal:[],state:"问题卷"}:f===3?{tableTitle:"已质检考生",singleQuesTit:["小题得分"],paperTotal:[],state:"质检"}:f===4||f===5?{tableTitle:"已验考生",singleQuesTit:["得分","验收分","差异分"],paperTotal:["最终得分","验收总分","差异分"],state:"验收"}:f===6?{tableTitle:"已评考生",singleQuesTit:["小题得分"],paperTotal:[],state:"试评"}:{tableTitle:"已评考生",singleQuesTit:["小题得分"],paperTotal:[],state:"正评"}}),a=E(360),c=E(420),d=E(),h=E(!0),g=E(!1),T=E(null),N=E(null);E(null);const Y=E(null);E(null),E(null),E(null);const $=E(null),oe=E(null);E(null);const F=E(null),U=E(null),Pe=E(null),ae=E(null),Re=E(!1),ve=E(null),we=E(0),Fe=E(0),re=E(0);let be=null;const Ne=E(600),Ve=window.innerWidth,W=window.innerHeight,I=E(Ve-800),Mn=E(W-370),Sn=E(800);E({error:!1,errorType:null});const _=Me(e,"quesInfo"),K=Me(e,"markValue"),O=Me(e,"noMarkStuList"),G=Me(e,"curIndex"),ye=Me(e,"isFocusNext"),De=Me(e,"reMarkingFlag"),Cn=[{label:"得分点评分",value:"point"},{label:"总分评分",value:"total"}],Tn=xe(()=>{var f;return(f=sn())==null?void 0:f.name}),An=xe(()=>{var f;return(f=sn())==null?void 0:f.username}),ee=E(null),de=E(null);E("false");const tt=E(.5),He=E(!1),ze=E(!1);Nt(()=>O.value,f=>{f.length?ee.value=O.value[G.value].answer_data.findIndex(p=>p.isActive):ee.value=0},{deep:!0});const nt=E([]);Nt(()=>[ee.value,_.value,tt.value],()=>{Xe(()=>{Rn()})},{deep:!0});const Rn=()=>{var f,p,v,M,D,H;if(_.value&&Object.keys(_.value).length&&ee.value!=null&&tt.value){let J=0;K.value==="total"?J=(p=(f=_.value)==null?void 0:f.children[ee.value])==null?void 0:p.ques_score:J=(H=(D=(M=(v=_.value)==null?void 0:v.children[ee.value])==null?void 0:M.ques_mark_point)==null?void 0:D.find(j=>j.isActive))==null?void 0:H.score;const A=Number(tt.value);let x=[];for(let j=0;j<=Number(J);j+=Number(A))x.push(Wn(j));nt.value=x,U.value&&(Ne.value=window.innerHeight-235-U.value.clientHeight)}else nt.value=[]};Nt(()=>_.value,f=>{var p,v,M,D,H;if(_.value){if(K.value==="total")if(_.value.children.find(A=>A.marking_score==null||A.marking_score===""))de.value=null;else{const A=(v=(p=_.value)==null?void 0:p.children)==null?void 0:v.map(j=>Number(j.marking_score)),x=Number(It(A));de.value=Rt(x,Number(_.value.ques_score))}else if(K.value==="point"){let J=!1,A=[];if((M=_.value)!=null&&M.children.length)if((H=(D=_.value)==null?void 0:D.children)==null||H.forEach(x=>{var j;if((j=x.ques_mark_point)!=null&&j.length)if(x.ques_mark_point.find(qe=>qe.marking_score==null||qe.marking_score===""))J=!0;else{const qe=x.ques_mark_point.reduce((rt,st)=>rt+Number(st.marking_score),0),it=Number(x.ques_score);A.push(qe>it?it:qe)}}),J)de.value=null;else{if(A.length<=0){de.value=null;return}const x=Number(It(A));de.value=Rt(x,Number(_.value.ques_score))}}xn()}},{deep:!0}),ni(()=>{xt(),Sn.value=Ve-c.value-260,Ln(),jn();const f=Number(l.query.type);[1,4,5].includes(f)||Ft()}),ii(()=>{Pn(be),document.removeEventListener("mousemove",vt),document.removeEventListener("mouseup",gt)});const xn=()=>{if(l.query.type==3&&O.value.length){const f=De.value&&l.query.type==3;if(K.value==="point"){let p=[];_.value.children.forEach(M=>{let D=[];M.ques_mark_point.forEach(H=>{D.push(H.marking_score)}),p.push(D)});const v=O.value[G.value].answer_data.map(M=>f?M.quality_point_score_list:M.mark_point_score_list);on(p,v)?(ze.value=!1,He.value=!0):(ze.value=!0,He.value=!1)}else if(K.value==="total"){const p=_.value.children.map(M=>M.marking_score),v=O.value[G.value].answer_data.map(M=>f?M.quality_score:M.mark_score);on(p,v)?(He.value=!0,ze.value=!1):(He.value=!1,ze.value=!0)}}},mt=f=>{if(De.value){const p=O.value[G.value].quality_type;if(f===1)return!![2,3].includes(p);if(f===2)return!![3].includes(p);if(f===3)return!![1,2,3].includes(p)}return!1},Rt=(f,p)=>f>p?p:f,Ln=()=>{be=new ResizeObserver(f=>{f.forEach(p=>{T.value&&(U.value&&(Ne.value=window.innerHeight-235-U.value.clientHeight),xt(),o("changeTableHeight",window.innerHeight-170))})}),T.value&&be.observe(T.value)},xt=()=>{var f;["1","4","5"].includes((f=l.query)==null?void 0:f.type)?d.value=W-220:d.value=W-160},Pn=f=>{f&&(f.disconnect(),f=null)},Fn=f=>{Re.value=!0,ve.value=f,we.value=event.clientX,Fe.value=a.value,re.value=c.value,document.addEventListener("mousemove",vt),document.addEventListener("mouseup",gt),document.body.style.cursor="col-resize",document.body.style.userSelect="none"},vt=f=>{if(!Re.value)return;const p=f.clientX-we.value;if(ve.value==="left"){const v=Fe.value+p;v>=310&&v<=500&&(a.value=v)}else if(ve.value==="right"){const v=re.value-p;v>=200&&v<=500&&(c.value=v)}},gt=()=>{Re.value=!1,ve.value=null,document.removeEventListener("mousemove",vt),document.removeEventListener("mouseup",gt),document.body.style.cursor="",document.body.style.userSelect=""},Lt=()=>{h.value=!h.value,Pt(),h.value||o("leftExpand")},Vn=()=>{g.value=!g.value,Pt()},Pt=()=>{Xe(()=>{var f;(f=oe.value)==null||f.resizeCanvas()})},Ft=f=>{var v;let p={task_type:((v=l.query)==null?void 0:v.type)==6?2:1,ques_code:f||l.query.ques_code};qi(p).then(M=>{M.code&&M.code===200?tt.value=M.data.mark_score_step||0:ue.error(M.msg)})},Dn=({option:f})=>{const{value:p}=f;K.value=p,Xe(()=>{p==="point"?(_t(),_.value.children[ee.value].ques_mark_point[0].inputRef.focus(),_.value.children[ee.value].ques_mark_point[0].isActive=!0):p==="total"&&_.value.children[ee.value].inputRef.focus()}),o("changeMarkValue")},_t=()=>{_.value.children.forEach(f=>{f.ques_mark_point.forEach(p=>p.isActive=!1)})},Ie=f=>{var p,v,M,D,H;if(_.value&&Object.keys(_.value).length){if((M=(v=(p=_.value.children)==null?void 0:p[f])==null?void 0:v.ques_mark_point)==null?void 0:M.find(A=>A.marking_score==null||A.marking_score===""))return null;if((D=_.value.children[f])!=null&&D.ques_mark_point){const A=(H=_.value.children[f].ques_mark_point)==null?void 0:H.reduce((j,Oe)=>j+Oe.marking_score,0),x=Number(_.value.children[f].ques_score);return A>x?x:A}return null}return null},In=(f,p)=>{var v,M,D,H,J,A;if(K.value=="point")return Ie(p)===null||Ie(p)===void 0||Ie(p)===""?null:Math.abs(Number(f.official_score)-Ie(p));if(K.value=="total"){let x=(D=(M=(v=_.value)==null?void 0:v.children)==null?void 0:M[p])==null?void 0:D.marking_score;return x===null||x==null||x===""?null:Math.abs(Number(f.official_score)-((A=(J=(H=_.value)==null?void 0:H.children)==null?void 0:J[p])==null?void 0:A.marking_score))}},Vt=(f,p)=>{const v=Number(_.value.children[ee.value].ques_score);if(p==null||p==="")ue.warning("请输入分数！");else if(p>Number(v))ue.warning(`分数不能大于${v}！`);else{let M=_.value.children.findIndex(D=>D.marking_score==null||D.marking_score==="");M!==-1?(_.value.children[M].inputRef.focus(),kt(M)):(f&&f.preventDefault(),ye.value=!0,F.value.ref.focus())}},Dt=(f,p,v,M)=>{const D=v[p].marking_score,H=v[p].score;if(D==null||D==="")ue.warning("请输入分数！");else if(D>Number(H))ue.warning(`分数不能大于！${H}`);else{const J=v.findIndex(A=>A.marking_score==null||A.marking_score==="");if(J!==-1)v[J].inputRef.focus(),v.forEach(A=>{A.isActive=!1}),v[J].isActive=!0;else{let A;for(let x=0;x<_.value.children.length;x++)if(A=_.value.children[x].ques_mark_point.findIndex(j=>j.marking_score==null||j.marking_score===""),A!==-1){_.value.children[x].ques_mark_point[A].inputRef.focus(),_.value.children[M].ques_mark_point.forEach(j=>j.isActive=!1),_.value.children[x].ques_mark_point[A].isActive=!0,ye.value=!1,Xe(()=>{kt(x)});return}else f&&f.preventDefault(),ye.value=!0,F.value.ref.focus()}}},On=f=>{ye.value=!1;const p=Number(f);if(K.value==="total")_.value.children[ee.value].marking_score=p,Vt(null,p);else{let v=0;_.value.children[ee.value].ques_mark_point.forEach((M,D)=>{M.isActive&&(v=D,M.marking_score=p)}),Dt(null,v,_.value.children[ee.value].ques_mark_point,ee.value)}},kt=f=>{var p,v,M,D;if((v=(p=O.value[G.value])==null?void 0:p.answer_data)==null||v.forEach(H=>H.isActive=!1),O.value[G.value]){O.value[G.value].answer_data[f].isActive=!0;const H=(D=(M=O.value[G.value])==null?void 0:M.answer_data[f])==null?void 0:D.answer_id;zn(H)}},Bn=f=>{ye.value=!1,O.value[G.value].answer_data.forEach(p=>{p.isActive=!1}),f.isActive=!0,Xe(()=>{K.value==="point"&&(_t(),_.value.children[ee.value].ques_mark_point[0].isActive=!0)})},We=f=>{if(O.value.length){const p=Zn();de.value!==null?de.value>Number(_.value.ques_score)?ue.warning(`评分分数${de.value}大于题目分数${Number(_.value.ques_score)}！`):(["0","6"].includes(l.query.type)&&p.length&&(De.value,wt()),Ot(f)):["0","6"].includes(l.query.type)&&p.length?(wt(),Ot(f)):ue.warning("请先完成评分！")}else ue.warning("暂无考生数据！")},Zn=()=>O.value[G.value].answer_data.filter(f=>f.exception_type!=null).map(f=>({answer_id:f.answer_id,exception_type:f.exception_type})),$n=()=>O.value[G.value].answer_data.map(f=>({answer_id:f.answer_id,exception_type:f.exception_type==null?0:f.exception_type})),wt=()=>{let f={task_id:l.query.task_id,round_id:l.query.round_id,exception_data:$n()};Ei(f).then(p=>{p.code&&p.code===200?ue.success(p.msg):ue.error(p.msg)})},Un=f=>{ye.value=!1,kt(f)},Hn=(f,p)=>{_t(),p[f].isActive=!0},zn=f=>{const p=document.getElementById(f);p&&p.scrollIntoView({behavior:"smooth",block:"nearest"})},Wn=f=>{let p=f.toFixed(1);return p.endsWith(".0")?parseInt(p):p.endsWith("0")?p.slice(0,-1):p},It=f=>f.reduce((v,M)=>v.plus(new Ue(M)),new Ue(0)).toString().replace(/\.0+$/,"").replace(/(\.\d*?)0+$/,"$1"),bt=E(.01),jn=()=>{let f={project_id:l.query.project_id,subject_id:l.query.subject_id};yi(f).then(p=>{p.code&&p.code===200?bt.value=Number(p.data.data[0].subject_score_interval)||.01:ue.error(p.msg)})},Qn=()=>{o("goBackToTask"),De.value=!1,h.value=!0,_.value.children.forEach(f=>{f.marking_score=null,f.ques_mark_point.forEach(p=>{p.marking_score=null})})},Xn=(f,p)=>{f?$.value.openDialog(l.query,p):oi.confirm("确定取消设置问题卷吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{p.exception_type=null}).catch(()=>{})},Kn=()=>{K.value==="point"?_.value.children.forEach(f=>{f.ques_mark_point.forEach(p=>{p.marking_score=p.score})}):_.value.children.forEach(f=>{f.marking_score=f.ques_score})},Gn=()=>{K.value==="point"?_.value.children.forEach(f=>{f.ques_mark_point.forEach(p=>{p.marking_score=0})}):_.value.children.forEach(f=>{f.marking_score=0})},Ot=(f=0)=>{o("commitMark",f)},Jn=()=>{O.value[G.value].answer_data[0].isActive=!0,K.value==="total"?_.value.children[0].inputRef.focus():_.value.children[0].ques_mark_point[0].inputRef.focus()},Yn=f=>{if(f===1)return Mr;if(f===2)return Sr;if(f===3)return Cr;if(f===4)return Tr},Bt=f=>{let p=f.key;["E","e","+","-"].includes(p)&&f.preventDefault()},Zt=f=>(i||(i=rn().smallQuesNumStyle),Si(i)[f]);return t({leftCollapsed:h,getStepByQuesCode:Ft,focusInput:Jn,titleData:u}),(f,p)=>{const v=he("el-text"),M=he("ArrowLeft"),D=he("el-icon"),H=he("DArrowRight"),J=he("el-tag"),A=he("iconify-icon-offline"),x=he("el-button"),j=he("el-checkbox"),Oe=he("el-scrollbar"),qe=he("el-input-number"),it=ri("watermark");return ln((S(),L("div",{class:"marking-page-box zf-first-box dark:!bg-black eye-box",ref_key:"markPageRef",ref:T},[y(Oe,{always:""},{default:C(()=>{var rt,st,$t,Ut,Ht,zt,Wt,jt,Qt,Xt,Kt,Gt;return[R("div",Rr,[R("div",{class:"",style:ot({width:h.value?"0px":`${a.value}px`})},[ln(R("div",{class:"marking-left dark:!bg-black eye-box",ref_key:"leftRef",ref:N},[R("div",xr,[y(v,null,{default:C(()=>[B(P(u.value.tableTitle),1)]),_:1}),h.value?z("",!0):(S(),L("div",{key:0,class:"marking-expand-box",onClick:Lt},[y(D,{class:"icon left dark:!bg-[#141414] eye-box"},{default:C(()=>[y(M)]),_:1})]))]),an(f.$slots,"leftTable",{},void 0,!0)],512),[[si,!h.value]])],4),h.value?z("",!0):(S(),L("div",{key:0,class:"marking-line dark:!bg-black",onMousedown:p[0]||(p[0]=w=>Fn("left"))},null,32)),R("div",{class:"marking-center",style:ot({"margin-left":h.value?0:"5px"})},[R("div",Lr,[R("div",Pr,[h.value?(S(),L("div",{key:0,class:"operation-box left dark:!bg-black eye-box",onClick:Lt},[y(v,null,{default:C(()=>[B(P(u.value.tableTitle)+" ",1),y(D,{color:"var(--el-color-primary)"},{default:C(()=>[y(H)]),_:1},8,["color"])]),_:1})])):z("",!0),R("div",Fr,[R("span",Vr,[De.value?(S(),_e(J,{key:0,effect:"light",type:"warning"},{default:C(()=>{var w;return[B(P(u.value.tableTitle)+"："+P((w=O.value[G.value])==null?void 0:w.stu_secret_num),1)]}),_:1})):(S(),L(pe,{key:1},[(rt=O.value[G.value])!=null&&rt.is_again_mark?(S(),_e(J,{key:0,effect:"light",type:"warning"},{default:C(()=>{var w;return[B("退回重评考生："+P((w=O.value[G.value])==null?void 0:w.stu_secret_num),1)]}),_:1})):(S(),_e(v,{key:1},{default:C(()=>{var w;return[B("当前考生："+P((w=O.value[G.value])==null?void 0:w.stu_secret_num),1)]}),_:1}))],64)),De.value?(S(),L("span",Dr,[y(x,{link:"",type:"primary",onClick:Qn},{default:C(()=>[y(A,{icon:V(Ni)},null,8,["icon"]),B("返回"+P(u.value.state),1)]),_:1})])):z("",!0),((st=V(l).query)==null?void 0:st.type)==1||(($t=V(l).query)==null?void 0:$t.type)==4||((Ut=V(l).query)==null?void 0:Ut.type)==5?(S(),L(pe,{key:3},[y(v,{class:"paper-total"},{default:C(()=>{var w;return[B(P(u.value.paperTotal[0])+"："+P((w=e.paperTotalScore)==null?void 0:w.oldTotalScore),1)]}),_:1}),y(v,{class:"paper-total"},{default:C(()=>{var w;return[B(P(u.value.paperTotal[1])+"："+P((w=e.paperTotalScore)==null?void 0:w.totalScore),1)]}),_:1}),y(v,{class:"paper-total"},{default:C(()=>{var w;return[B(P(u.value.paperTotal[2])+"："+P((w=e.paperTotalScore)==null?void 0:w.diffScore),1)]}),_:1})],64)):z("",!0)]),O.value.length?(S(),L("div",Ir,[y(v,{class:"icon dark:!bg-black eye-box",title:"放大",size:"large",onClick:V(hi)},{default:C(()=>[y(A,{icon:V(pi)},null,8,["icon"])]),_:1},8,["onClick"]),y(v,{class:"icon dark:!bg-black eye-box",title:"缩小",size:"large",onClick:V(mi)},{default:C(()=>[y(A,{icon:V(vi)},null,8,["icon"])]),_:1},8,["onClick"]),y(v,{class:"icon dark:!bg-black eye-box",title:"自适应",size:"large",onClick:V(gi)},{default:C(()=>[y(A,{icon:V(_i)},null,8,["icon"])]),_:1},8,["onClick"]),y(v,{class:"icon dark:!bg-black eye-box",title:"旋转",size:"large",onClick:V(ki)},{default:C(()=>[y(A,{icon:V(wi)},null,8,["icon"])]),_:1},8,["onClick"])])):z("",!0)]),g.value?(S(),L("div",{key:1,class:"operation-box right dark:!bg-black eye-box",onClick:Vn},[y(v,null,{default:C(()=>[B("试题信息 "),y(D,null,{default:C(()=>[y(M)]),_:1})]),_:1})])):z("",!0)]),an(f.$slots,"centerContent",{},void 0,!0)]),y(Oe,{always:"","max-height":d.value},{default:C(()=>[O.value.length?(S(),L("div",Or,[(S(!0),L(pe,null,lt(O.value[G.value].answer_data,(w,le)=>{var je;return S(),L("div",{id:w.answer_id,key:w.answer_id,class:"every-answer"},[R("div",Zr,[y(v,null,{default:C(()=>{var Q,Ee,Qe,ne,Be,ge,Jt,Yt,en,tn;return[(Q=_.value)!=null&&Q.noChildren?z("",!0):(S(),_e(v,{key:0,type:"primary"},{default:C(()=>[y(A,{icon:V(qr)},null,8,["icon"])]),_:1})),((Ee=V(l).query)==null?void 0:Ee.type)==1||((Qe=V(l).query)==null?void 0:Qe.type)==4||((ne=V(l).query)==null?void 0:ne.type)==5?(S(),L(pe,{key:1},[R("span",$r,P(u.value.singleQuesTit[0])+"： "+P(w.official_score),1),R("span",Ur,P(u.value.singleQuesTit[1])+"："+P(K.value==="total"?(Jt=(ge=(Be=_.value)==null?void 0:Be.children)==null?void 0:ge[le])==null?void 0:Jt.marking_score:Ie(le)),1),R("span",Hr,P(u.value.singleQuesTit[2])+"："+P(In(w,le)),1)],64)):(S(),L(pe,{key:2},[_.value.noChildren?z("",!0):(S(),L("span",zr,P(u.value.singleQuesTit[0])+"："+P(K.value==="total"?(tn=(en=(Yt=_.value)==null?void 0:Yt.children)==null?void 0:en[le])==null?void 0:tn.marking_score:Ie(le)),1))],64))]}),_:2},1024),["0","6"].includes((je=V(l).query)==null?void 0:je.type)?(S(),_e(j,{key:0,class:"check",modelValue:w.error,"onUpdate:modelValue":Q=>w.error=Q,label:"问题卷",onChange:Q=>Xn(Q,w)},null,8,["modelValue","onUpdate:modelValue","onChange"])):z("",!0)]),R("div",{ref_for:!0,ref_key:"container",ref:ae,class:yt(["stu-answer-img",w.isActive?"active":""]),onClick:Q=>Bn(w)},[R("img",{ref_for:!0,ref_key:"imageRef",ref:Pe,src:w.answer_image_path,style:ot(w.isActive?V(bi):"")},null,12,jr),R("div",Qr,[R("img",{src:Yn(w.exception_type)},null,8,Xr)])],10,Wr)],8,Br)}),128))])):z("",!0),e.isLoadNoMark&&O.value.length===0?(S(),L("div",{key:1,class:"mark-empty-box dark:!bg-black eye-box",style:ot({height:d.value+"px"})},[R("img",{src:V(Mi),class:"image"},null,8,Kr),R("div",Gr,[y(v,{size:"large"},{default:C(()=>[B(P(u.value.state)+"任务完成！ ",1)]),_:1})])],4)):z("",!0)]),_:1},8,["max-height"]),O.value.length?(S(),L("div",Jr,[R("div",Yr,[R("div",es,[((Ht=V(l).query)==null?void 0:Ht.type)==0||((zt=V(l).query)==null?void 0:zt.type)==6?(S(),_e(v,{key:0},{default:C(()=>[B("已阅量：-- ")]),_:1})):z("",!0)])]),R("div",ts,[((Wt=V(l).query)==null?void 0:Wt.type)==0||((jt=V(l).query)==null?void 0:jt.type)==6?(S(),L("div",ns,[R("i",is,[y(A,{icon:V(Nr)},null,8,["icon"])]),y(v,null,{default:C(()=>[B(P(V(Er)),1)]),_:1})])):z("",!0),y(v,null,{default:C(()=>{var w,le;return[B("作答字数："+P((le=(w=O.value[G.value])==null?void 0:w.answer_data[ee.value])==null?void 0:le.word_count),1)]}),_:1})])])):z("",!0)],4),O.value.length?(S(),L("div",{key:1,class:"marking-right dark:!bg-black eye-box",ref_key:"rightRef",ref:Y},[R("div",rs,[y(v,{size:"large"},{default:C(()=>[B("评分卡")]),_:1}),y(V(fi),{class:"select-none",modelValue:K.value==="point"?0:1,options:Cn,onChange:Dn},null,8,["modelValue"])]),R("div",ss,[y(v,null,{default:C(()=>[B("本题分值： "),y(v,{type:"primary"},{default:C(()=>{var w;return[B(P(Number((w=_.value)==null?void 0:w.ques_score)),1)]}),_:1})]),_:1})]),R("div",null,[y(Oe,{always:"","max-height":Ne.value},{default:C(()=>{var w,le,je;return[(le=(w=_.value)==null?void 0:w.children)!=null&&le.length?(S(),L("div",os,[(S(!0),L(pe,null,lt((je=_.value)==null?void 0:je.children,(Q,Ee)=>{var Qe;return S(),L("div",{class:"ques-point-box",onClick:ne=>Un(Ee)},[K.value==="point"?(S(),L(pe,{key:0},[_.value.noChildren?z("",!0):(S(),L("span",as,P(Zt(Ee)),1)),_.value.noChildren?z("",!0):(S(),L("span",us,P(Q.ques_score?Number(Q.ques_score):"")+"分",1)),(S(!0),L(pe,null,lt(Q.ques_mark_point,(ne,Be)=>(S(),L("div",{class:yt(["point-item",!ye.value&&ne.isActive?"active":""]),onClick:ge=>Hn(Be,Q.ques_mark_point)},[R("span",fs," （"+P(Be+1)+"） ",1),y(qe,{ref_for:!0,ref:ge=>ne.inputRef=ge,modelValue:ne.marking_score,"onUpdate:modelValue":ge=>ne.marking_score=ge,size:"small",controls:!1,min:0,max:ne.score,step:bt.value,"step-strictly":"",style:{width:"132px"},onKeydown:[Bt,un(ge=>Dt(ge,Be,Q.ques_mark_point,Ee),["enter"])]},null,8,["modelValue","onUpdate:modelValue","max","step","onKeydown"]),R("span",ds,P(ne.score)+"分",1)],10,cs))),256))],64)):(S(),L("div",{key:1,class:yt(["point-item",!ye.value&&K.value==="total"&&Ee===ee.value?"active":""])},[(Qe=_.value)!=null&&Qe.noChildren?z("",!0):(S(),L("span",hs,P(Zt(Ee)),1)),y(qe,{ref_for:!0,ref:ne=>Q.inputRef=ne,modelValue:Q.marking_score,"onUpdate:modelValue":ne=>Q.marking_score=ne,size:"small",controls:!1,min:0,max:Number(Q.ques_score),step:bt.value,"step-strictly":"",style:{width:"132px"},onKeydown:[Bt,un(ne=>Vt(ne,Q.marking_score),["enter"])]},null,8,["modelValue","onUpdate:modelValue","max","step","onKeydown"]),R("span",ps,P(Number(Q.ques_score))+"分",1)],2))],8,ls)}),256))])):z("",!0)]}),_:1},8,["max-height"]),nt.value?(S(),L("div",{key:0,ref_key:"quickScoreRef",ref:U,class:"quick-btn"},[R("div",ms,[y(x,{type:"success",onClick:Kn},{default:C(()=>[B("   本题满分    ")]),_:1}),y(x,{type:"warning",onClick:Gn},{default:C(()=>[B("   本题零分    ")]),_:1})]),R("div",vs,[y(v,null,{default:C(()=>[B("评分步长")]),_:1})]),(S(!0),L(pe,null,lt(nt.value,w=>(S(),_e(v,{class:"score-box dark:!bg-[#141414] eye-box",onClick:le=>On(w)},{default:C(()=>[B(P(w),1)]),_:2},1032,["onClick"]))),256))],512)):z("",!0),R("div",gs,[((Qt=V(l).query)==null?void 0:Qt.type)==3?(S(),L("div",_s,[R("span",ks,[y(x,{onClick:p[1]||(p[1]=w=>We(3)),disabled:mt(3)},{default:C(()=>[B("退回重评 ")]),_:1},8,["disabled"])]),R("span",ws,[y(x,{ref_key:"nextStuBtnRef",ref:F,onClick:p[2]||(p[2]=w=>We(2)),disabled:mt(2)||He.value},{default:C(()=>[B("修改提交 ")]),_:1},8,["disabled"])]),y(x,{type:"primary",onClick:p[3]||(p[3]=w=>We(1)),disabled:mt(1)||ze.value},{default:C(()=>[B("质检通过 ")]),_:1},8,["disabled"])])):(S(),L(pe,{key:1},[((Xt=V(l).query)==null?void 0:Xt.type)==1||((Kt=V(l).query)==null?void 0:Kt.type)==4||((Gt=V(l).query)==null?void 0:Gt.type)==5?(S(),_e(x,{key:0,ref_key:"nextStuBtnRef",ref:F,type:"primary",onClick:We},{default:C(()=>[B(P(r.value==s.value.length-1?"提交":"下一题"),1)]),_:1},512)):(S(),_e(x,{key:1,ref_key:"nextStuBtnRef",ref:F,type:"primary",onClick:We},{default:C(()=>[B("提交本题 ")]),_:1},512))],64)),y(v,{size:"small",tag:"p",style:{color:"#a2a9b2","margin-top":"5px"}},{default:C(()=>[B("提示：键盘评分，点击【回车】即可提交 ")]),_:1})])])],512)):z("",!0),y(V(ci),{isActive:!0,isResizable:!1,z:100,parentLimitation:!0,x:I.value,y:Mn.value,onResizing:w=>!1,onDragging:w=>!1},{default:C(()=>{var w;return[de.value!=null&&((w=O.value)!=null&&w.length)?(S(),L("div",bs,[R("div",Ns,P(de.value),1),ys])):z("",!0)]}),_:1},8,["x","y"])])]}),_:3}),y(di,{ref_key:"problemRef",ref:$},null,512)])),[[it,{type:"bright",content:`${An.value} ${Tn.value}`,width:300,height:300}]])}}}),Es=ui(qs,[["__scopeId","data-v-8622ddc7"]]),Bs=Object.freeze(Object.defineProperty({__proto__:null,default:Es},Symbol.toStringTag,{value:"Module"}));export{Ue as D,Es as M,Vs as a,Bs as i,Os as r,Ds as s,Is as t};
