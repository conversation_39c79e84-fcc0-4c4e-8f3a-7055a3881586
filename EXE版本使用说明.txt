===============================================
卓帆考试系统一键启动器 - EXE版本使用说明
===============================================

🎉 恭喜！您已经获得了独立的exe启动器！

📁 文件说明
-----------
✓ 卓帆考试系统一键启动器.exe - GUI图形界面版本（推荐）
✓ 卓帆考试系统简易启动器.exe - 命令行版本（备用）

两个版本功能完全相同，如果GUI版本有兼容性问题，可使用命令行版本。

🚀 使用方法
-----------
1. 确保以下程序文件在同一目录中：
   ✓ Redis-x64-3.0.504\redis-server.exe
   ✓ 卓帆电子化考试阅卷管理系统V1.0.exe
   ✓ 定时任务V1.0.exe

2. 选择并运行启动器：

   【GUI版本】双击：卓帆考试系统一键启动器.exe
   - 图形界面，操作直观
   - 点击"一键启动"按钮启动所有程序
   - 点击"一键关闭"按钮关闭所有程序
   - 点击"刷新状态"查看当前运行状态

   【命令行版本】双击：卓帆考试系统简易启动器.exe
   - 命令行界面，兼容性更好
   - 输入数字选择操作：1-启动，2-关闭，3-状态，4-退出

⚡ 启动顺序
-----------
启动：Redis → 主程序 → 数据服务
关闭：数据服务 → 主程序 → Redis

✨ 优势特点
-----------
✓ 无需安装Python环境
✓ 无需安装任何依赖包
✓ 双击即可运行
✓ 完整的图形界面
✓ 实时状态监控
✓ 详细操作日志

🔧 故障排除
-----------
1. 如果程序无法启动：
   - 检查是否有杀毒软件拦截
   - 尝试以管理员身份运行
   - 确保程序文件路径正确

2. 如果某个服务启动失败：
   - 查看日志区域的错误信息
   - 检查端口是否被占用
   - 确保有足够的系统权限

📋 系统要求
-----------
✓ Windows 7/8/10/11 (64位)
✓ 无需Python环境
✓ 无需额外安装包

💡 使用提示
-----------
- 首次运行可能需要几秒钟加载
- 建议以管理员身份运行以获得最佳兼容性
- 关闭启动器不会影响已启动的程序
- 可以将exe文件复制到其他电脑使用

===============================================
版本：1.0 | 生成日期：2025-09-08
===============================================
