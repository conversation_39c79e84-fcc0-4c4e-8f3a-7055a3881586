import{d as P,l as s,n as z,r as p,o as I,c as y,e as v,q as A,F as K,p as U,U as j,t as O,b as r,h as c,f as _,i6 as G,T as x,_ as H}from"./index-B63pSD2p.js";import{c as J}from"./validate-Dc6ka3px.js";const Q={class:"manual-right-box bg-[#fff] dark:bg-black eye-box drag-content-box"},X={class:"marking-area"},Y=["onClick"],Z={class:"quick-btn-box"},ee={class:"custom-score"},te=P({__name:"index",props:["quesInfo","score_step"],emits:["setMarkingPanelWidth"],setup(V,{expose:M,emit:N}){const g=V,C=N,q=s(!1),u=s(null),m=s(null),w=s(null),R={scoreInput:[{required:!0,message:"请输入评分分数",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,e,o)=>{if(J(e))o();else return o(new Error("请输入数字（至多两位小数）！"))}}]},l=s({scoreInput:null});s(null);const i=s([]),f=s(264);z(()=>{var e;const t=(e=m.value)==null?void 0:e.$el.querySelector("input");t&&(t.classList.add("no-drag"),t.classList.add("data-drag-cancel"),t.setAttribute("data-drag-cancel","drag"+Math.random()))});const S=t=>{q.value=!0;const{ques_order:e,ques_score_list:o,total_score:k,ques_code:b}=Object.keys(g.quesInfo).length>0?g.quesInfo:t;if(e||b){let a=0;o!=null&&o.length?a=o.map(Number).reduce((B,L)=>B+L,0):a=Number(k);let n=[];for(let d=0;d<=a;d+=Number(g.score_step))n.push(T(d));n.length>0&&(n[n.length-1]!==a&&n.push(a),i.value=n,n.length<5&&(f.value=264),n.length>=20&&(f.value=602),C("setMarkingPanelWidth",f.value))}},T=t=>{let e=t.toFixed(1);return e.endsWith(".0")?parseInt(e):e.endsWith("0")?e.slice(0,-1):e},D=()=>{q.value=!1,l.value.scoreInput=null,i.value=[],u.value.clearValidate(),setTimeout(()=>{f.value=264},500)},h=t=>{l.value.scoreInput=t},F=()=>{l.value.scoreInput=null,setTimeout(()=>{u.value.clearValidate(),m.value.focus()},100)},E=()=>{u.value&&u.value.validate(t=>{t?C("markConfirmFn",l.value.scoreInput):x.warning("请按要求填写!")})},W=()=>{setTimeout(()=>{m.value.focus()},100)},$=t=>{if(l.value.scoreInput==null){x.warning("评分分数不能为空！");return}u.value.validate(e=>{e?(t.preventDefault(),w.value.ref.focus()):x.warning("请按要求填写!")})};return M({openDialog:S,closeDialog:D,clearScoreFn:F,focusInput:W}),(t,e)=>{const o=p("el-button"),k=p("el-input"),b=p("el-form-item"),a=p("el-form");return I(),y("div",Q,[v("div",X,[v("div",{class:"quick-mark-box",style:A("width:"+f.value+"px")},[(I(!0),y(K,null,U(i.value,n=>(I(),y("div",{class:j(["quick-score",l.value.scoreInput===n?"select-score":""]),onClick:d=>h(n)},O(n),11,Y))),256))],4),v("div",Z,[r(o,{type:"plain",onClick:F},{default:c(()=>[_("清除")]),_:1}),r(o,{type:"warning",onClick:e[0]||(e[0]=n=>h(0))},{default:c(()=>[_("零分")]),_:1}),r(o,{type:"success",onClick:e[1]||(e[1]=n=>h(i.value[i.value.length-1]))},{default:c(()=>[_("满分")]),_:1})]),v("div",ee,[r(a,{model:l.value,rules:R,ref_key:"formRef",ref:u,"validate-on-rule-change":!1},{default:c(()=>[r(b,{prop:"scoreInput"},{default:c(()=>[r(k,{ref_key:"elInputRef",ref:m,clearable:"",modelValue:l.value.scoreInput,"onUpdate:modelValue":e[2]||(e[2]=n=>l.value.scoreInput=n),style:{width:"150px"},onKeyup:G($,["enter"])},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),r(o,{ref_key:"scoreConfirmRef",ref:w,type:"primary",onClick:E},{default:c(()=>[_("确定 ")]),_:1},512)])])])}}}),le=H(te,[["__scopeId","data-v-19e656f6"]]);export{le as M};
