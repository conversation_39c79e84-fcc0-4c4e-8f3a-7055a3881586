import{aQ as t,aR as r}from"./index-B63pSD2p.js";const s=a=>t.request("post",r("/v1/official_mark/get_mark_task_list"),{data:a}),e=a=>t.request("post",r("/v1/ques_manage/all_business_ques_type"),{data:a}),u=a=>t.request("post",r("/v1/human_group/get_small_human_group"),{data:a}),n=a=>t.request("post",r("/v1/official_mark/check_human_task_exist"),{data:a}),m=a=>t.request("post",r("/v1/official_mark/create_human_mark_task"),{data:a}),o=a=>t.request("post",r("/v1/official_mark/update_human_mark_round"),{data:a}),k=a=>t.request("post",r("/v1/official_mark/delete_human_mark_round"),{data:a}),c=a=>t.request("post",r("/v1/official_mark/launch_human_mark_task"),{data:a}),i=a=>t.request("post",r("/v1/official_mark/pause_human_mark_round"),{data:a}),p=a=>t.request("post",r("/v1/official_mark/continue_human_mark_round"),{data:a}),h=a=>t.request("post",r("/v1/try_mark/create_try_mark_task"),{data:a});export{e as a,m as b,n as c,h as d,s as e,p as f,u as g,k as h,c as l,i as p,o as u};
