import{d,r as l,o as e,c as n,F as r,p as _,e as s,b as g,h as m,f as u,t as c,ac as x,ad as h,_ as v}from"./index-B63pSD2p.js";const y="/static/png/yyjl-DS-RUOKh.png",f="/static/png/yxl-DYI8Ie8C.png",j="/static/png/wxl-D6v_FFuo.png",w="/static/png/pjsd-AEkQEG8Q.png",D="/static/png/zxsd-DHIAjiXn.png",b="/static/png/zdsd-Dg6uEUrm.png",I="/static/png/wtjl-D98U3jtp.png",S=t=>(x("data-v-d761917f"),t=t(),h(),t),k={class:"basic-view"},z={class:"basic-num"},B=["src"],C=S(()=>s("div",{class:"split-line"},null,-1)),E=d({__name:"basic-monitor",props:{basicData:Array},setup(t){const o={yyjl:y,wxl:j,yxl:f,pjsd:w,zxsd:D,zdsd:b,wtjl:I};return(F,A)=>{const i=l("el-text");return e(),n("div",k,[(e(!0),n(r,null,_(t.basicData,(a,p)=>(e(),n("div",{class:"basic-item",key:p},[s("div",null,[g(i,{type:"info",style:{"white-space":"nowrap"}},{default:m(()=>[u(c(a.name),1)]),_:2},1024),s("div",z,c(a.value),1)]),s("img",{src:o[a.image],alt:"Image"},null,8,B),C]))),128))])}}}),U=v(E,[["__scopeId","data-v-d761917f"]]);export{U as default};
