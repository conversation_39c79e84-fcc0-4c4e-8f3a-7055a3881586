import{a as w}from"./intelligent-marking-CyZREtXG.js";import{d as D,aS as T,l as n,aO as v,P as S,ao as $,r as h,o as l,g as k,h as P,e as s,t as i,f as B,c as d,y as q,aV as z,_ as E}from"./index-B63pSD2p.js";const O={class:"zf-dialog-first-box"},U={class:"zf-dialog-second-box"},j={class:"step-box"},F={key:0},A={key:1},G={key:0},H={key:1},J=D({__name:"progress-bar",props:{batchCount:{},batchCountModifiers:{},currentBatch:{},currentBatchModifiers:{},oneBatchNum:{},oneBatchNumModifiers:{}},emits:T(["queryData","initStart"],["update:batchCount","update:currentBatch","update:oneBatchNum"]),setup(_,{expose:b,emit:x}){n("import-test-paper");const c=v(_,"batchCount"),a=v(_,"currentBatch"),m=v(_,"oneBatchNum"),p=n(!0),g=x,y=n("阅卷进度"),r=n(!1),e=S({percentage:0,curr_mark_count:0,total:0,total_mark_count:0});let o=n(null);const C=u=>{r.value=!0,N(u)},N=u=>{o.value&&clearInterval(o.value),o.value=setInterval(()=>{w(u).then(t=>{t.code&&t.code===200&&(Object.assign(e,t.data),e.total_mark_count=(a.value-1)*m.value+t.data.curr_mark_count,a.value<c.value&&t.data.percentage===100&&(e.percentage=0,e.curr_mark_count=0,g("initStart",a.value)),t.data.percentage===100&&(c.value>1&&(a.value+=1),a.value===c.value?setTimeout(()=>{f(),z.alert("智能阅卷已完成。","提示",{confirmButtonText:"确定"})},1e3):setTimeout(()=>{p.value=!1,e.percentage=0,e.curr_mark_count=0,setTimeout(()=>{p.value=!0},20)},1e3)))})},5e3)},f=()=>{r.value=!1,a.value=1,e.percentage=0,e.execute_step=null,e.total=0,e.curr_mark_count=0,e.total_mark_count=0,o.value&&clearInterval(o.value),g("queryData")};return $(()=>{o.value&&clearInterval(o.value)}),b({openDialog:C}),(u,t)=>{const V=h("el-progress"),I=h("el-dialog");return l(),k(I,{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=M=>r.value=M),title:y.value,width:"40%","align-center":"","close-on-click-modal":!1,"before-close":f,draggable:""},{default:P(()=>[s("div",O,[s("div",U,[s("div",j,[s("p",null,"共 "+i(c.value)+" 批，当前批数："+i(a.value),1),s("p",null,[B(" 当前批次进度： "),e.total?(l(),d("span",F,i(`${e!=null&&e.curr_mark_count?e.curr_mark_count:0} / ${m.value>e.total?e.total:m.value}`),1)):(l(),d("span",A,"加载中..."))]),s("p",null,[B(" 总进度： "),e.total?(l(),d("span",G,i(`${e.total_mark_count} / ${e.total}`),1)):(l(),d("span",H,"加载中..."))])]),p.value?(l(),k(V,{key:0,class:"progress-box","text-inside":!0,"stroke-width":15,striped:"",percentage:e.percentage,status:"success"},null,8,["percentage"])):q("",!0)])])]),_:1},8,["modelValue","title"])}}}),Q=E(J,[["__scopeId","data-v-1e89ba91"]]);export{Q as default};
