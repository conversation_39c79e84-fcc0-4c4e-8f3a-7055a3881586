var R=Object.defineProperty;var S=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var j=(t,e,a)=>e in t?R(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,V=(t,e)=>{for(var a in e||(e={}))A.call(e,a)&&j(t,a,e[a]);if(S)for(var a of S(e))L.call(e,a)&&j(t,a,e[a]);return t};import{g as T}from"./plagiarizing-answer-DCON8hS9.js";import{c as U,a as E}from"./calculateTableHeight-BjE6OFD1.js";import{p as Q,g as M,a as K}from"./common-methods-BWkba4Bo.js";import $ from"./confirm-dialog-PI9yzxf5.js";import G from"./detail-BbWuWcVG.js";import{d as J,l as r,P as X,n as Y,ao as Z,T as w,r as f,o as ee,c as te,e as d,b as o,h as s,f as D,ac as ae,ad as ne,_ as le}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const oe=t=>(ae("data-v-c0045e4e"),t=t(),ne(),t),re={class:"zf-first-box"},ie={class:"zf-second-box"},se=oe(()=>d("div",{style:{height:"6px",background:"#e0e2e8"}},null,-1)),ue={class:"zf-flex-end"},pe={class:"task-btn-box"},ce=["onClick"],_e=J({name:"subjective-check",__name:"index",setup(t){const e=r(null),a=r(null),h=r(!1),g=r(!1),m=r({}),O=X({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>Q.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>u.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>u.value},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入考试密号",clearable:!0},{label:"考生密号",prop:"username2",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"评分使用",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评分使用",optionData:()=>u.value}]}),u=r([]),v=r([]),p=r({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_order",label:"题号",minWidth:"90px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"task_name",label:"第一轮分数",minWidth:"100px"},{prop:"region",label:"第二轮分数",minWidth:"100px"},{prop:"work_unit1",label:"最终评分",minWidth:"100px"},{prop:"role_name",label:"评分使用",minWidth:"120px",formatter:n=>"第一轮"},{prop:"work_unit",label:"是否一致",minWidth:"120px"},{prop:"ai_score",label:"AI评分",minWidth:"120px",type:"slot"},{prop:"role_name",label:"AI质量",minWidth:"120px",formatter:n=>"合格"}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),y=r([]);let x=null;Y(()=>{M(),U(x,e.value,p.value,!0),b()}),Z(()=>{E(x)});const W=(n,l)=>{n.prop==="project_id"&&(u.value=[],m.value.subject_id&&(m.value.subject_id=null),l&&K(l).then(c=>{u.value=c||[]}))},b=()=>{let{currentPage:n,pageSize:l}=p.value.pageOptions,c=V({current_page:n,page_size:l},m.value);T(c).then(_=>{_.code&&_.code===200||w.error(_.msg)}),y.value=[{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"系统架构设计师（案例分析）",paper_name:"—",ques_order:"—",ques_code:"900000001N99B1qQK001",task_name:"15",stu_secret_num:"2003939999020001",region:"12",work_unit:"否",work_unit1:"15"},{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"系统架构设计师（案例分析）",paper_name:"—",ques_order:"—",ques_code:"900000001N99B9iNQ001",task_name:"13",stu_secret_num:"2003939999020002",region:"14",work_unit:"否",work_unit1:"14"},{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"系统架构设计师（案例分析）",paper_name:"—",ques_order:"—",ques_code:"900000001N99B64TP001",task_name:"13",stu_secret_num:"2003939999020006",region:"10",work_unit:"否",work_unit1:"13"}]},q=n=>{p.value.pageOptions.pageSize=n,b()},z=n=>{p.value.pageOptions.currentPage=n,b()},F=n=>{v.value=n},H=()=>{if(!v.value.length){w.warning("至少选择一条数据！");return}h.value=!0},I=()=>{if(!v.value.length){w.warning("至少选择一条数据！");return}},N=()=>{g.value=!0};function P(){u.value=[]}return(n,l)=>{const c=f("form-component"),_=f("el-card"),k=f("el-button"),C=f("Auth"),B=f("table-component");return ee(),te("div",re,[d("div",ie,[o(_,null,{default:s(()=>[d("div",{ref_key:"formDivRef",ref:e},[o(c,{ref_key:"formRef",ref:a,modelValue:m.value,"onUpdate:modelValue":l[0]||(l[0]=i=>m.value=i),"form-options":O,"is-query-btn":!0,onOnchangeFn:W,onQueryDataFn:b,onResetFields:P},null,8,["modelValue","form-options"])],512)]),_:1}),se,o(_,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:s(()=>[d("div",ue,[o(C,{value:"subjective-check/grade-confirmed"},{default:s(()=>[o(k,{type:"primary",onClick:H},{default:s(()=>[D("成绩确定")]),_:1})]),_:1}),o(C,{value:"subjective-check/export"},{default:s(()=>[o(k,{type:"primary",onClick:I},{default:s(()=>[D("导出")]),_:1})]),_:1})]),o(B,{minHeight:p.value.styleOptions.minHeight,"table-options":p.value,"table-data":y.value,onOnHandleSizeChange:q,onOnHandleCurrentChange:z,onOnHandleSelectionChange:F},{ai_score:s(i=>[d("div",pe,[d("span",{class:"task-btn",onClick:de=>N(i.row)},"1",8,ce)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),o($,{isShowConfirmDialog:h.value,"onUpdate:isShowConfirmDialog":l[1]||(l[1]=i=>h.value=i)},null,8,["isShowConfirmDialog"]),o(G,{drawerVisible:g.value,"onUpdate:drawerVisible":l[2]||(l[2]=i=>g.value=i)},null,8,["drawerVisible"])])}}}),xe=le(_e,[["__scopeId","data-v-c0045e4e"]]);export{xe as default};
