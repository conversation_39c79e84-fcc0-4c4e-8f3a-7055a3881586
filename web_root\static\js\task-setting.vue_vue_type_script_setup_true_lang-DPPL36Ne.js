import{M as re}from"./index-P2PNQqmI.js";import{_ as ie}from"./marking-group-select.vue_vue_type_script_setup_true_lang-BEZHpNlH.js";import{a as R}from"./common-methods-BWkba4Bo.js";import{a as oe}from"./marking-mode-CLpbbjcA.js";import{b as se}from"./validate-Dc6ka3px.js";import{d as ue,l as m,P as ne,n as ce,B as L,r as h,o as V,c as pe,b as p,h as n,e as b,g as P,f as de,s as _e,T as F}from"./index-B63pSD2p.js";const fe={class:"mr-[5px]"},me=b("div",{class:"zf-tit-box"},[b("i"),b("span",null,"任务设置")],-1),he={class:"flex justify-between mr-[1px]"},be=b("span",{class:"h-[30px]"},"%",-1),ge={class:"flex"},ve=b("span",{class:"h-[30px]"},"%",-1),ye=b("span",null,"-",-1),qe=b("span",{class:"h-[30px]"},"%",-1),we=["innerHTML"],Pe=ue({__name:"task-setting",props:["quesInfo","taskInfo","projectList"],emits:["changeProAndSub"],setup(E,{expose:z,emit:O}){const w=E,T=O,r=m(null),H=m(null),j=m(null),S=m({}),s=m({thSwitch:!0,arbPercentage:null,disabled:!0,min:.01,max:1,pointDiff:1}),v=m({min:.1,max:1,disabled:!0}),y=m(1),I=m(280),k={options1:[{label:"最高分",value:1},{label:"最低分",value:2},{label:"平均分",value:3}],options2:[{label:"最高分",value:1},{label:"最低分",value:2},{label:"平均分",value:3},{label:"去除最高和最低分取平均分",value:4}]},d=m({upperLimit:null,lowerLimit:null,iconInfo:{icon:"infoFilled",info:"所选阅卷小组存在AI质检时，需设置质检阈值的差值上限和差值下限。<br/>差值上限：允许最终得分比质检得分，高出最大百分比；<br/>差值下限：允许最终得分比质检得分，低出最大百分比。",color:""}}),x=m([]),M=m({}),i=ne({column:3,labelWidth:"92px",itemWidth:"246px",inline:!0,rules:{m_read_task_name:[],project_id:[{required:!0,message:"请选择项目",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择科目",trigger:["blur","change"]}],manual_process_id:[{required:!0,message:"请选择阅卷模式",trigger:["blur","change"]}],m_read_group_id_list:[{required:!0,message:"请选择阅卷小组",trigger:["blur","change"]}],manual_group_name_list:[{required:!0,message:"请选择阅卷小组",trigger:["blur","change"]}],quality_ratio:[],fetch_score_way:[],fetch_score_option:[],score_step:[{required:!0,message:"请设置任务步长",trigger:["blur","change"],type:Number}]},fields:[{label:"任务名称",prop:"m_read_task_name",type:"input",defaultValue:"",placeholder:"请输入任务名称",clearable:!0,isHidden:!0},{label:"项目",prop:"project_id",type:"select",defaultValue:null,placeholder:"请选择项目",clearable:!0,optionData:()=>w.projectList},{label:"科目",prop:"subject_id",type:"select",defaultValue:null,placeholder:"请选择科目",clearable:!0,optionData:()=>S.value},{label:"阅卷模式",prop:"manual_process_id",type:"select",defaultValue:null,placeholder:"请选择阅卷模式",clearable:!0,optionData:()=>x.value},{label:"阅卷小组",prop:"manual_group_name_list",type:"input",defaultValue:[],placeholder:"请选择阅卷小组",clearable:!0,suffixIcon:"MoreFilled",readonly:!0,isHidden:!1},{label:"阅卷小组",prop:"m_read_group_id_list",type:"select",defaultValue:[],placeholder:"请选择阅卷小组",clearable:!0,suffixIcon:"MoreFilled",readonly:!0,isHidden:!0,multiple:!0,optionData:()=>[]},{label:"评分步长",prop:"score_step",type:"template",defaultValue:1,placeholder:"",clearable:!0,multiple:!0},{label:"取分方式",prop:"fetch_score_way",type:"select",defaultValue:1,placeholder:"请选择取分方式",isHidden:!0,icon:"infoFilled",iconStyle:{info:"最终结果取分方式: <br/>最高分: 取所有评分中的最高分<br/>最低分: 取所有评分中的最低分<br/>平均分: 取所有评分求平均分<br/>（注：如有仲裁则取仲裁分数）",size:20},width:"222px",clearable:!0,optionData:()=>[{label:"最高分",value:1},{label:"最低分",value:2},{label:"平均分",value:3}]},{label:"平均范围",prop:"fetch_score_scope",type:"select",defaultValue:1,isHidden:!0,placeholder:"请选择平均范围",icon:"infoFilled",iconStyle:{info:"平均分计算方式: <br/>全分组: 所有评分的平均分<br/>高分组: 取评分最高的两个评分求平均分<br/>中间组: 去掉最高分和最低分求平均分<br/>低分组: 取评分最低的两个评分求平均分",size:20},width:"222px",clearable:!0,optionData:()=>[{label:"全分组",value:1},{label:"高分组",value:2},{label:"中间组",value:3},{label:"低分组",value:4}]},{label:"分值计算",prop:"fetch_score_option",type:"select",defaultValue:3,isHidden:!0,placeholder:"请选择分值计算",icon:"infoFilled",iconStyle:{info:"最终结果计算方式: <br/>四舍五入: 1.553 -> 1.55<br/>向上舍入: 1.553 -> 1.56<br/>向下舍入: 1.553 -> 1.55",size:20},width:"222px",clearable:!0,optionData:()=>[{label:"四舍五入",value:3},{label:"向上舍入",value:2},{label:"向下舍入",value:1}]},{label:"仲裁阈值",prop:"arbitrate_threshold",type:"template",isHidden:!0,defaultValue:null,placeholder:"",clearable:!0},{label:"分差取值",prop:"arbitrate_score_diff",type:"select",defaultValue:1,isHidden:!0,placeholder:"请选择分差取值",icon:"infoFilled",iconStyle:{info:"评分专家人数>3时的分差取值: <br/>平均值: 所有评分的平均值<br/>最大偏差: 最高分和最低分的差值<br/>最小偏差: 最低两个评分的差值<br/>",size:20},width:"222px",clearable:!0,optionData:()=>[{label:"平均值",value:1},{label:"最大偏差",value:3},{label:"最小偏差",value:2}]},{label:"质检比例",prop:"quality_ratio",type:"input",defaultValue:"",placeholder:"请输入质检比例",isHidden:!0,clearable:!0,suffix:"%",width:"222px",icon:"infoFilled",iconStyle:{info:"质检抽样比例",size:20}},{label:"质检阈值",prop:"qualityLimit",type:"template",placeholder:"请选择质检条件",isHidden:!0,width:"84px"},{label:"备注",prop:"remark",rows:2,type:"textarea",defaultValue:"",placeholder:"请输入备注",clearable:!0}]}),_={quality_ratio:[{required:!0,message:"请输入质检比例",trigger:["blur","change"]},{trigger:["blur","change"],validator:(a,e,t)=>{if(se(e))t();else return t(new Error("请输入100以内正整数！"))}}],fetch_score_way:[{required:!0,message:"请选择取分方式",trigger:["blur","change"]}],fetch_score_option:[{required:!0,message:"请选择分值计算",trigger:["blur","change"]}],fetch_score_scope:[{required:!0,message:"平均范围",trigger:["blur","change"]}],qualityLimit:[{required:!0,message:"",trigger:["blur","change"]}],upperLimit:[{required:!0,message:"请输入差值上限",trigger:["blur","change"]}],lowerLimit:[{required:!0,message:"请输入差值下限",trigger:["blur","change"]}],arbitrate_threshold:[{required:!0,message:"请输入仲裁阈值",trigger:["blur","change"]}],arbitrate_score_diff:[{required:!0,message:"请选择分差取值",trigger:["blur","change"]}]},q=["fetch_score_way","fetch_score_option","fetch_score_scope","arbitrate_threshold","quality_ratio","arbitrate_score_diff","qualityLimit","upperLimit","lowerLimit","quality_reevaluation"];ce(()=>{W()}),L(()=>w.quesInfo.ques_order,a=>{A(a)}),L(()=>w.quesInfo.ques_code,a=>{A(a)});const A=a=>{if(a){s.value.disabled=!1;const{ques_score_list:e,total_score:t}=w.quesInfo;let l=0;e!=null&&e.length?l=e.map(Number).reduce((u,c)=>u+c,0):l=Number(t),s.value.max=l,v.value.max=l,v.value.disabled=!1}else v.value.disabled=!0};L(()=>[s.value.thSwitch,s.value.arbPercentage,s.value.pointDiff],([a,e,t],[l,o,u])=>{a?r.value.setCardData("arbitrate_threshold",s.value.arbPercentage):(r.value.setCardData("arbitrate_threshold",s.value.pointDiff),s.value.pointDiff&&setTimeout(()=>{r.value.clearValidateFn("arbitrate_threshold")},100)),e!==o&&r.value.setCardData("arbitrate_threshold",s.value.arbPercentage),t!==u&&r.value.setCardData("arbitrate_threshold",s.value.pointDiff)},{deep:!0}),L(()=>[d.value.upperLimit,d.value.lowerLimit],([a,e],[t,l])=>{a!==t&&r.value.setCardData("upperLimit",a),e!==l&&r.value.setCardData("lowerLimit",e),e&&e&&r.value.setCardData("qualityLimit",[a,e])},{deep:!0});const G=(a,e)=>{R(a.project_id).then(l=>{S.value=l||[]}),i.rules.m_read_task_name=[{required:!0,message:"请输入任务名称",trigger:["blur","change"]}];const t=["qualityLimit","upperLimit","lowerLimit","fetch_score_scope","fetch_score_option"];i.fields.map(l=>{l.prop==="m_read_task_name"&&(l.isHidden=!1),q.forEach(o=>{t.includes(o)||l.prop===o&&a[o]&&(l.isHidden=!1,i.rules[o]=_[o]),a.quality_ratio&&l.prop==="quality_reevaluation"&&(l.isHidden=!1),e==="03"&&(l.disabled=!0)}),a.fetch_score_way&&(a.fetch_score_way===3?["fetch_score_scope","fetch_score_option"].forEach(u=>{l.prop===u&&a[u]&&(l.isHidden=!1,i.rules[u]=_[u])}):a.fetch_score_way===4&&l.prop==="fetch_score_way"&&(l.optionData=()=>k.options2)),l.prop==="qualityLimit"&&a.quality_upper_limit&&a.quality_lower_limit&&(l.isHidden=!1,["qualityLimit","upperLimit","lowerLimit"].forEach(u=>i.rules[u]=_[u]),d.value.upperLimit=a.quality_upper_limit,d.value.lowerLimit=a.quality_lower_limit,r.value.setCardData("upperLimit",a.quality_upper_limit),r.value.setCardData("lowerLimit",a.quality_lower_limit),r.value.setCardData("qualityLimit",[a.quality_upper_limit,a.quality_lower_limit])),a.hasOwnProperty(l.prop)&&r.value.setCardData(l.prop,a[l.prop])}),r.value.setCardData("m_read_group_id_list",a.manual_group_id_list),a.arbitrate_threshold_type&&a.arbitrate_threshold&&(a.arbitrate_threshold_type===1?(s.value.thSwitch=!0,s.value.arbPercentage=a.arbitrate_threshold):a.arbitrate_threshold_type===2&&(s.value.thSwitch=!1,s.value.pointDiff=a.arbitrate_threshold))},W=()=>{oe({page_size:-1}).then(e=>{var t,l,o;e.code&&e.code===200&&((l=(t=e.data)==null?void 0:t.data)==null||l.forEach(u=>{u.label=u.process_name,u.value=u.process_id}),x.value=(o=e.data)==null?void 0:o.data)})},B=(a,e)=>{a.prop==="project_id"?(C(),r.value.getCardData("subject_id")&&r.value.setCardData("subject_id",null),e&&R(e).then(t=>{S.value=t||[]}),T("changeProAndSub",a,e)):a.prop==="subject_id"?(C(),T("changeProAndSub",a,e)):a.prop==="manual_process_id"?(r.value.setCardData("fetch_score_way",1),r.value.setCardData("fetch_score_option",3),r.value.setCardData("fetch_score_scope",1),C(),e?$(e):X()):a.prop==="fetch_score_way"&&(r.value.setCardData("fetch_score_option",3),r.value.setCardData("fetch_score_scope",1),e&&K(e),setTimeout(()=>{r.value.clearValidateFn()},100))},U=a=>{var e;if(a.prop==="manual_group_name_list"){const{project_id:t,subject_id:l,manual_process_id:o}=(e=r==null?void 0:r.value)==null?void 0:e.getAllCardData();if(t&&l&&o){const u={project_id:t,subject_id:l,manual_process_id:o},c=r.value.getCardData("m_read_group_id_list");j.value.openDialog(u,c)}else t||F.warning("请先选择项目！"),l||F.warning("请先选择科目！"),o||F.warning("请先选择阅卷模式！")}},J=a=>{let e=[],t=[];a.forEach(l=>{e.push(l.manual_group_id),t.push(l.manual_group_name)}),r.value.setCardData("m_read_group_id_list",e),r.value.setCardData("manual_group_name_list",t),Q(a)},Q=a=>{i.fields.forEach(e=>{if(e.prop==="qualityLimit"){const t=a.filter(o=>o.quality_ai_num),l=["qualityLimit","upperLimit","lowerLimit"];t.length>0?(l.forEach(o=>{i.rules[o]=_[o]}),e.isHidden=!1):(e.isHidden=!0,l.forEach(o=>{i.rules[o]=[]}))}}),setTimeout(()=>{r.value.clearValidateFn()},100)},$=a=>{const e=x.value.filter(t=>t.value===a)[0];i.fields.forEach(t=>{t.prop==="quality_ratio"?e.has_quality?(t.isHidden=!1,i.rules.quality_ratio=_.quality_ratio):(t.isHidden=!0,i.rules.quality_ratio=[]):t.prop==="fetch_score_way"?e.has_fetch_score?(t.isHidden=!1,e.expert_num>2?t.optionData=()=>k.options2:t.optionData=()=>k.options1,i.rules.fetch_score_way=_.fetch_score_way):(t.isHidden=!0,i.rules.fetch_score_way=[]):t.prop==="fetch_score_option"?e.expert_num>2?(t.isHidden=!1,i.rules.fetch_score_option=_.fetch_score_option):(t.isHidden=!0,i.rules.fetch_score_option=[]):t.prop==="quality_reevaluation"?e.has_quality?(t.isHidden=!1,i.rules.quality_reevaluation=_.quality_reevaluation):(t.isHidden=!0,i.rules.quality_reevaluation=[]):t.prop==="arbitrate_threshold"?e.arbitrator_num?(t.isHidden=!1,i.rules.arbitrate_threshold=_.arbitrate_threshold,s.value.arbPercentage=e.arbitrate_threshold,r.value.setCardData("arbitrate_threshold",e.arbitrate_threshold)):(t.isHidden=!0,i.rules.arbitrate_threshold=[]):t.prop==="arbitrate_score_diff"&&(e.arbitrator_num&&e.expert_num>2?(t.isHidden=!1,i.rules.arbitrate_score_diff=_.arbitrate_score_diff):(t.isHidden=!0,i.rules.arbitrate_score_diff=[]))}),setTimeout(()=>{r.value.clearValidateFn()},100)},K=a=>{i.fields.forEach(e=>{if(e.prop==="fetch_score_option")a===3?(e.isHidden=!1,i.rules.fetch_score_option=_.fetch_score_option):(e.isHidden=!0,i.rules.fetch_score_option=[]);else if(e.prop==="fetch_score_scope"){const t=r.value.getCardData("manual_process_id"),l=x.value.filter(o=>o.value===t);a===3&&l[0].expert_num>2?(e.isHidden=!1,i.rules.fetch_score_scope=_.fetch_score_scope):(e.isHidden=!0,i.rules.fetch_score_scope=[])}})},X=()=>{i.fields.forEach(a=>{q.includes(a.prop)&&(a.isHidden=!0)}),q.forEach(a=>{i.rules[a]=[]}),setTimeout(()=>{r.value.clearValidateFn()},100)},C=()=>{var e,t;((e=r.value.getCardData("m_read_group_id_list"))==null?void 0:e.length)>0&&r.value.setCardData("m_read_group_id_list",[]),((t=r.value.getCardData("manual_group_name_list"))==null?void 0:t.length)>0&&r.value.setCardData("manual_group_name_list",[]);const a=["qualityLimit","upperLimit","upperLimit"];i.fields.forEach(l=>{l.prop==="qualityLimit"&&!l.isHidden&&(l.isHidden=!0)}),a.forEach(l=>{var o;((o=i.rules[l])==null?void 0:o.length)>0&&(i.rules[l]=[])})},Y=()=>{H.value.openDialog()},Z=()=>{H.value.closeDialog()},ee=a=>{I.value=a+20};return z({getTaskInfoFn:()=>new Promise(a=>{r.value.formValidate().then(()=>{let e=JSON.parse(JSON.stringify(r.value.getAllCardData()));e.score_step=y.value;const{upperLimit:t,lowerLimit:l}=d.value;e.quality_upper_limit=t,e.quality_lower_limit=l,["quality_ratio","quality_reevaluation","quality_lower_limit","quality_upper_limit"].forEach(c=>{e[c]?e[c]=Number(e[c]):delete e[c]}),e.arbitrate_threshold_type=s.value.thSwitch?1:2;const u=i.fields.filter(c=>c.isHidden).map(c=>c.prop);u.length>0&&u.forEach(c=>{c!=="m_read_group_id_list"&&delete e[c]}),delete e.manual_group_name_list,delete e.qualityLimit,delete e.lowerLimit,delete e.upperLimit,a(e)}).catch(()=>{F.warning("请按要求填写任务设置！")})}),setTaskFormDataFn:G,clearTaskFormFn:()=>{y.value=1,d.value.upperLimit=null,d.value.lowerLimit=null,s.value.thSwitch=!0,s.value.arbPercentage=null,s.value.pointDiff=1,i.fields.forEach(a=>{q.includes(a.prop)&&(a.isHidden=!0),a.disabled=!1}),q.forEach(a=>i.rules[a]=[]),setTimeout(()=>{r.value.resetFieldsFn()},100)},clearTaskNameFn:()=>{r.value.setCardData("m_read_task_name","")}}),(a,e)=>{const t=h("el-input"),l=h("el-input-number"),o=h("el-switch"),u=h("el-button"),c=h("el-popover"),N=h("el-form-item"),D=h("el-col"),ae=h("el-icon"),te=h("el-tooltip"),le=h("form-component");return V(),pe("div",fe,[me,p(le,{ref_key:"formRef",ref:r,modelValue:M.value,"onUpdate:modelValue":e[6]||(e[6]=g=>M.value=g),"form-options":i,"is-query-btn":!1,onOnchangeFn:B,onOnclickSuffixFn:U},{arbitrate_threshold:n(({scope:g})=>[b("div",he,[s.value.thSwitch?(V(),P(t,{key:0,disabled:g.disabled,modelValue:s.value.arbPercentage,"onUpdate:modelValue":e[0]||(e[0]=f=>s.value.arbPercentage=f),style:{width:"153px","margin-right":"10px"},placeholder:"仲裁阈值",clearable:""},{suffix:n(()=>[be]),_:2},1032,["disabled","modelValue"])):(V(),P(l,{key:1,min:s.value.min,max:s.value.max,disabled:s.value.disabled,style:{width:"153px","margin-right":"10px"},modelValue:s.value.pointDiff,"onUpdate:modelValue":e[1]||(e[1]=f=>s.value.pointDiff=f)},null,8,["min","max","disabled","modelValue"])),p(o,{disabled:g.disabled,modelValue:s.value.thSwitch,"onUpdate:modelValue":e[2]||(e[2]=f=>s.value.thSwitch=f),"active-text":"百分比","inactive-text":"分   差","inline-prompt":"",style:{"--el-switch-on-color":"var(--el-color-primary)","--el-switch-off-color":"var(--el-color-primary)","margin-left":"19px"}},null,8,["disabled","modelValue"])])]),score_step:n(({scope:g})=>[p(l,{disabled:g.disabled,min:v.value.min,max:v.value.max,style:{width:"152px","margin-right":"5px"},modelValue:y.value,"onUpdate:modelValue":e[3]||(e[3]=f=>y.value=f)},null,8,["disabled","min","max","modelValue"]),p(c,{width:I.value,trigger:"click",onShow:Y,onHide:Z},{reference:n(()=>[p(u,{disabled:v.value.disabled,type:"primary"},{default:n(()=>[de("评分预览")]),_:1},8,["disabled"])]),default:n(()=>[p(re,{ref_key:"markingStepRef",ref:H,quesInfo:E.quesInfo,score_step:y.value,onSetMarkingPanelWidth:ee},null,8,["quesInfo","score_step"])]),_:1},8,["width"])]),qualityLimit:n(({scope:g})=>[b("div",ge,[p(D,{span:10},{default:n(()=>[p(N,{prop:"upperLimit"},{default:n(()=>[p(t,{modelValue:d.value.upperLimit,"onUpdate:modelValue":e[4]||(e[4]=f=>d.value.upperLimit=f),style:{width:"108px"},placeholder:"差值上限",clearable:"",disabled:g.disabled},{suffix:n(()=>[ve]),_:2},1032,["modelValue","disabled"])]),_:2},1024)]),_:2},1024),p(D,{span:2,class:"text-center"},{default:n(()=>[ye]),_:1}),p(D,{span:10},{default:n(()=>[p(N,{prop:"lowerLimit"},{default:n(()=>[p(t,{modelValue:d.value.lowerLimit,"onUpdate:modelValue":e[5]||(e[5]=f=>d.value.lowerLimit=f),style:{width:"108px"},placeholder:"差值下限",clearable:"",disabled:g.disabled},{suffix:n(()=>[qe]),_:2},1032,["modelValue","disabled"])]),_:2},1024)]),_:2},1024),p(D,{span:1,class:"flex items-center"},{default:n(()=>[p(te,{effect:"light"},{content:n(()=>[b("span",{innerHTML:d.value.iconInfo.info},null,8,we)]),default:n(()=>[p(ae,{class:"ml-1",size:"20px",color:d.value.iconInfo.color},{default:n(()=>[(V(),P(_e(d.value.iconInfo.icon)))]),_:1},8,["color"])]),_:1})]),_:1})])]),_:1},8,["modelValue","form-options"]),p(ie,{ref_key:"markingGroupSelectRef",ref:j,onGetSelectedGroup:J},null,512)])}}});export{Pe as _};
