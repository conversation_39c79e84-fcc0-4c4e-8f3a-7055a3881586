import{d as P,aS as y,aO as R,r as g,o as s,c as u,F as v,p as q,b as d,h as c,f as _,t as p,i6 as V,g as x,y as b,T as f,_ as C}from"./index-B63pSD2p.js";const E={class:"ques-point-box"},S={class:"point-item"},B={class:"ques-point-box"},F=P({name:"score-point",__name:"score-point",props:y({quesLevel:{},markMode:{default:"point"},subQuesIndex:{default:null}},{modelValue:{},modelModifiers:{}}),emits:y(["nextStu"],["update:modelValue"]),setup(w,{emit:N}){const m=N,o=R(w,"modelValue"),M=(e,a,t,r)=>{t==null||t===""?f.warning("请输入分数！"):t>Number(r)?f.warning(`分数不能大于！${r}`):a+1===o.value.children.length?(e.preventDefault(),m("nextStu")):(o.value.children[a+1].inputRef.focus(),m("nextPoint"))},L=(e,a,t,r,k)=>{t==null||t===""?f.warning("请输入分数！"):t>Number(r)?f.warning(`分数不能大于！${r}`):a+1===o.value.ques_mark_point.length?m("nextQues",k):(o.value.ques_mark_point[a+1].inputRef.focus(),m("nextPoint"))},U=e=>{e!==o.value.children.length-1?o.value.children[e+1].ques_mark_point[0].inputRef.focus():(o.value.children[e].marking_score=o.value.children[e].ques_mark_point.reduce((a,t)=>a+Number(t.marking_score),0),m("pointGoNext"))},K=()=>{o.value.children.findIndex(e=>e.marking_socre==null||e.marking_socre==="")};return(e,a)=>{var h;const t=g("el-text"),r=g("el-input-number"),k=g("score-point",!0);return s(),u("div",E,[o.value.ques_mark_point?(s(!0),u(v,{key:0},q(o.value.ques_mark_point,(n,i)=>(s(),u("div",S,[d(t,{style:{display:"inline-block","min-width":"46px"}},{default:c(()=>[_(" （"+p(e.subQuesIndex!==0?e.subQuesIndex+1:i+1)+"） ",1)]),_:2},1024),d(r,{ref_for:!0,ref:l=>n.inputRef=l,modelValue:n.marking_score,"onUpdate:modelValue":l=>n.marking_score=l,size:"small",controls:!1,min:0,max:n.score,style:{width:"87px"},onKeydown:V(l=>L(l,i,n.marking_score,n.score,e.subQuesIndex),["enter"])},null,8,["modelValue","onUpdate:modelValue","max","onKeydown"]),d(t,{style:{"margin-left":"32px"}},{default:c(()=>[_("小点分："+p(n.score),1)]),_:2},1024)]))),256)):(s(!0),u(v,{key:1},q((h=o.value)==null?void 0:h.children,(n,i)=>(s(),u("div",B,[e.quesLevel===0?(s(),x(t,{key:0,style:{"margin-bottom":"8px"}},{default:c(()=>[_(" 问题"+p(n.ques_order),1)]),_:2},1024)):b("",!0),e.markMode==="point"?(s(),x(k,{key:1,modelValue:o.value.children[i],"onUpdate:modelValue":l=>o.value.children[i]=l,markMode:e.markMode,quesLevel:e.quesLevel+1,subQuesIndex:i,onNextQues:U,onPointGoNext:K},null,8,["modelValue","onUpdate:modelValue","markMode","quesLevel","subQuesIndex"])):(s(),u(v,{key:2},[d(r,{ref_for:!0,ref:l=>n.inputRef=l,modelValue:n.marking_score,"onUpdate:modelValue":l=>n.marking_score=l,size:"small",controls:!1,min:0,max:Number(n.ques_score),style:{width:"87px"},onKeydown:V(l=>M(l,i,n.marking_score,n.ques_score),["enter"])},null,8,["modelValue","onUpdate:modelValue","max","onKeydown"]),d(t,{style:{"margin-left":"12px"}},{default:c(()=>[_("小题分："+p(n.ques_score),1)]),_:2},1024)],64))]))),256))])}}}),z=C(F,[["__scopeId","data-v-9913d518"]]);export{z as default};
