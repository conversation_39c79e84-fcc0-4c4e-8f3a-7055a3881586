import{d as R,l as s,P as S,r as i,o as h,g as w,h as n,e as _,b as d,f as x,c as U,F as z,p as A,u as J,C as M,N as q,R as D,T as p,ij as W,ik as K,_ as L}from"./index-B63pSD2p.js";import{s as G,e as H}from"./validate-Dc6ka3px.js";const Q=["ep:lollipop","ep:home-filled","ep:menu","ep:histogram","ri:terminal-window-line","ri:openai-line","ri:user-fill","ri:file-paper-2-line","ri:group-line","ri:book-read-fill","ri:git-close-pull-request-fill","ri:article-fill","ri:key-2-fill","ri:book-mark-fill","ri:bookmark-3-fill"],X={class:"zf-dialog-first-box"},Y={class:"zf-dialog-second-box"},Z={class:"ml-[10px]"},$={class:"footer-btn"},ee=R({__name:"add-modules",emits:["queryData"],setup(le,{expose:I,emit:C}){const y=C,c=s("添加菜单"),m=s(!1),t=s("01"),o=s({}),k=s({}),v=S({column:3,labelWidth:"140px",itemWidth:"290px",rules:{module_name:[{required:!0,message:"请输入名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(l,e,a)=>{if(e.length>30)return a(new Error("名称长度不能超过30！"));a()}}],web_path:[{required:!0,message:"请输入地址",trigger:["blur","change"]},{trigger:["blur","change"],validator:(l,e,a)=>{if(G(e))a();else return a(new Error("请以 / 开头！"))}}],rank:[{required:!0,message:"请输入排序",trigger:["blur","change"]},{trigger:["blur","change"],validator:(l,e,a)=>{if(H(e))a();else return a(new Error("请输入正整数！"))}}],show_link:[{required:!0,message:"请选择是否展示在菜单栏",trigger:["blur","change"]}]},fields:[{label:"名称",prop:"module_name",type:"input",defaultValue:"",placeholder:"请输入名称",clearable:!0},{label:"地址",prop:"web_path",type:"input",defaultValue:"",placeholder:"请输入地址",clearable:!0},{label:"图标",prop:"icon",type:"template",readonly:!0,suffixShow:!0,defaultValue:"",placeholder:"请选择图标",clearable:!0,disabled:!0},{label:"排序",prop:"rank",type:"input",defaultValue:"",placeholder:"请输入排序",clearable:!0},{label:"是否展示在菜单栏",prop:"show_link",type:"select",defaultValue:!0,placeholder:"请选择是否展示在菜单栏",clearable:!0,optionData:()=>[{label:"是",value:!0},{label:"否",value:!1}]}]}),f=s(null),u=s(""),b=s(!1),F=(l,e)=>{m.value=!0,t.value=l,l==="01"?c.value="添加同级菜单":l==="02"?(c.value="添加子级菜单",o.value=e):l==="03"?(c.value="添加菜单",o.value=e):l==="04"&&(c.value="编辑菜单",o.value=e,u.value=e.icon,e.module_flag===0x6171ec909c66e&&(b.value=!0),M(()=>{v.fields.forEach(a=>{e.hasOwnProperty(a.prop)&&f.value.setCardData(a.prop,e[a.prop]),a.prop==="web_path"&&(q().getItem(D).roles.includes("1")||q().getItem(D).roles.includes("2")?a.disabled=!1:a.disabled=!0)})}))},g=()=>{m.value=!1,f.value.resetFieldsFn(),u.value="",b.value=!1,v.fields.forEach(l=>{l.disabled=!1})},N=()=>{f.value.formValidate().then(()=>{let l=JSON.parse(JSON.stringify(f.value.getAllCardData()));l.rank=Number(l.rank),l.icon=u.value,t.value==="01"||t.value==="02"||t.value==="03"?(t.value==="01"?l.flag=1:t.value==="02"?(l.flag=2,l.module_id=o.value.module_id):t.value==="03"&&(l.flag=2,l.module_id=o.value.parent_module_id),E(l)):t.value==="04"&&(l.flag=o.value.flag,l.module_id=o.value.module_id,O(l))}).catch(()=>{p.warning("请按要求填写！")})},E=l=>{W(l).then(e=>{if(e.code&&e.code===200){p.success(e.msg);let a=null;o.value&&(a=o.value.module_flag),y("queryData",a),g()}else p.warning(e.msg)})},O=l=>{K(l).then(e=>{if(e.code&&e.code===200){p.success(e.msg);let a=null;o.value&&(a=o.value.module_flag),y("queryData",a),g()}else p.warning(e.msg)})};return I({openDialog:F}),(l,e)=>{const a=i("IconifyIconOffline"),T=i("el-option"),B=i("el-select"),P=i("form-component"),V=i("el-button"),j=i("el-dialog");return h(),w(j,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=r=>m.value=r),title:c.value,width:"500px",draggable:"","align-center":"","close-on-click-modal":!1,"before-close":g},{footer:n(()=>[_("div",$,[d(V,{onClick:g},{default:n(()=>[x("取消")]),_:1}),d(V,{type:"primary",onClick:N},{default:n(()=>[x("确定")]),_:1})])]),default:n(()=>[_("div",X,[_("div",Y,[d(P,{ref_key:"formRef",ref:f,modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=r=>k.value=r),"form-options":v,"is-query-btn":!1},{icon:n(()=>[d(B,{modelValue:u.value,"onUpdate:modelValue":e[0]||(e[0]=r=>u.value=r),placeholder:"请选择图标",disabled:b.value,style:{width:"261px"},clearable:""},{default:n(()=>[(h(!0),U(z,null,A(J(Q),r=>(h(),w(T,{key:r,label:r,value:r},{default:n(()=>[d(a,{class:"icon-options",icon:r},null,8,["icon"])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),_("i",Z,[d(a,{class:"select-icon-box",icon:u.value},null,8,["icon"])])]),_:1},8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}}),re=L(ee,[["__scopeId","data-v-7214a567"]]);export{re as default};
