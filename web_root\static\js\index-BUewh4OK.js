import{t as v,M as X,r as E,s as Z,a as ee}from"./index-CaNJCtth.js";import{d as ae,az as te,l as p,n as le,ao as ue,C as N,T as m,N as re,r as ie,o as oe,c as se,b as T,h as ne,aV as _e}from"./index-B63pSD2p.js";import{f as H}from"./handleMethod-BIjqYEft.js";import{b as pe,r as ce}from"./marking-paper-CSlVaOqn.js";import{g as de,a as ve,b as me,q as fe,h as ge}from"./start-marking-Cd3MpOOi.js";import{c as ye}from"./base-DyvdloLK.js";import{h as qe}from"./marking-Da5XG12E.js";import"./vue-drag-resize-CJIVu41Q.js";import"./index-BzG0ERft.js";import"./hooks-Cf_Naqnw.js";import"./problem-CRChWLev.js";import"./anticlockwise-2-line-Iit2_C-u.js";import"./reply-fill-DeI84Ty7.js";import"./checkInfo-mJiqvlDC.js";import"./quesNum-CouueI57.js";const Pe=ae({__name:"index",setup(ke){const t=te(),y=p(null),f=p("point"),s=p({}),l=p(0),w=p(!1),r=p([]),n=p({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px",resizeable:!0},{prop:"mark_score_sum",label:"分数",minWidth:"68px"},{prop:"quality_type",label:"质检类型",minWidth:"86px",isHidden:!0,formatter:a=>H(a.quality_type,[{value:1,label:"质检通过"},{value:2,label:"修改提交"},{value:3,label:"退回重评"}])},{prop:"mark_time",label:"评卷时间",minWidth:"120px",sortable:!0}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,layout:"total, sizes, ->, prev, pager, next",currentPage:1,pageSize:10,total:0}}),L=[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px",resizeable:!0},{prop:"mark_score",label:"分数",minWidth:"68px"},{prop:"created_time",label:"评卷时间",minWidth:"120px",sortable:!0}],P=[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px",resizeable:!0},{prop:"quality_score_sum",label:"分数",minWidth:"68px"},{prop:"quality_type",label:"质检类型",minWidth:"86px",formatter:a=>H(a.quality_type,[{value:1,label:"质检通过"},{value:2,label:"修改提交"},{value:3,label:"退回重评"}])},{prop:"quality_time",label:"质检时间",minWidth:"120px",sortable:!0}],h=p([]),d=p(!1),b=p([]),M=p(0),z=p(!1),O=p(0);le(()=>{W(),q(),n.value.styleOptions.minHeight=window.innerHeight-208,t.query.type==2?n.value.field=L:t.query.type==3&&(n.value.field=P)}),ue(()=>{t.query.type==3});const I=a=>{n.value.styleOptions.minHeight=a},W=()=>{let a={ques_code:t.query.ques_code};pe(a).then(e=>{e.code&&e.code===200?(s.value=qe(e.data.ques_detail),f.value==="point"?N(()=>{var o,u,_,i;(i=(_=(u=(o=s.value)==null?void 0:o.children)==null?void 0:u[l.value])==null?void 0:_.ques_mark_point[0])!=null&&i.inputRef&&(s.value.children[l.value].ques_mark_point[0].inputRef.focus(),s.value.children[l.value].ques_mark_point[0].isActive=!0)}):N(()=>{s.value.children[l.value].inputRef.focus(),s.value.children[l.value].isActive=!0}),t.query.type==3&&O.value===0&&r.value.length&&(O.value++,k(r.value[l.value]))):m.error(e.msg)})},F=a=>{b.value.length===0&&r.value.length&&(b.value=JSON.parse(JSON.stringify(r.value)),M.value=l.value),d.value=!0,r.value=[a],k(a),l.value=0,E(),Z(a.cost_time),v()},k=a=>{var o,u;const e=d.value&&t.query.type==3;a.answer_data[0].mark_point_score_list.length>0?(f.value="point",(o=s.value.children)==null||o.forEach((_,i)=>{var c;(c=_.ques_mark_point)==null||c.forEach((x,g)=>{let C=e?a.answer_data[i].quality_point_score_list:a.answer_data[i].mark_point_score_list;x.marking_score=C[g]})})):(f.value="total",(u=s.value.children)==null||u.forEach((_,i)=>{let c=e?a.answer_data[i].quality_score:a.answer_data[i].mark_score;_.marking_score=c}))},R=a=>{n.value.pageOptions.currentPage=1,n.value.pageOptions.pageSize=a,q(1)},A=a=>{n.value.pageOptions.currentPage=a,q(1)},q=(a=0)=>{a===1&&t.query.type==3?V():a===1&&t.query.type==2?Q():a===0&&t.query.type==2?B():U(a)},U=(a=0)=>{var _,i;let e={ques_code:(_=t.query)==null?void 0:_.ques_code,round_id:(i=t.query)==null?void 0:i.round_id},o="/v1/human_mark/get_mark_stu_list";(Number(t.query.type)||0)===3?(o="/v1/human_quality/get_no_quality_stu_list",e.stu_secret_num_list=re().getItem("stu_secret_num_list")):e.stu_type=a,a===0?(e.current_page=1,e.page_size=10):(e.current_page=n.value.pageOptions.currentPage,e.page_size=n.value.pageOptions.pageSize),ye(e,o).then(c=>{c.code&&c.code===200?j(a,c):m.error(c.msg)})},j=(a,e)=>{var u,_;z.value=!0;let o=e.data.data?e.data.data:e.data.items||[];a===0?(l.value=0,r.value=o,r.value.length&&((_=(u=r.value[l.value])==null?void 0:u.answer_data)!=null&&_[0]&&(r.value[l.value].answer_data[0].isActive=!0),t.query.type==0||t.query.type==6?r.value[l.value].answer_data.forEach(i=>{i.error&&(i.error=!1)}):t.query.type==3&&s.value&&Object.keys(s.value).length&&(O.value++,k(r.value[l.value])),(t.query.type==0||t.query.type==6)&&v())):(h.value=o,n.value.pageOptions.total=e.data.total)},V=()=>{let a={current_page:n.value.pageOptions.currentPage,page_size:n.value.pageOptions.pageSize,project_id:t.query.project_id,subject_id:t.query.subject_id,is_show_self:1};de(a).then(e=>{e.code&&e.code===200?(h.value=e.data.data,n.value.pageOptions.total=e.data.total):m.error(e.msg)})},B=()=>{let a={stu_secret_num:t.query.stu_secret_num};ve(a).then(e=>{e.code&&e.code===200?j(0,e):m.error(e.msg)})},Q=()=>{let a={current_page:n.value.pageOptions.currentPage,page_size:n.value.pageOptions.pageSize,project_id:t.query.project_id,subject_id:t.query.subject_id,ques_code:t.query.ques_code,handler_state:1,is_show_self:1};me(a).then(e=>{e.code&&e.code===200?(h.value=e.data.items,n.value.pageOptions.total=e.data.total):m.error(e.msg)})},D=a=>{let e={round_id:t.query.round_id,mark_info:[]},o=[];r.value[l.value].answer_data.forEach((u,_)=>{let i={answer_id:u.answer_id,mark_score:Number(s.value.children[_].marking_score)};if(f.value==="point"){i.mark_point_score_list=s.value.children[_].ques_mark_point.filter(g=>g.marking_score!=null).map(g=>g.marking_score);const c=i.mark_point_score_list.reduce((g,C)=>g+C,0),x=Number(s.value.children[_].ques_score);i.mark_score=c>x?x:c}t.query.type==3?(i.distri_id=u.distri_id,i.person_distri_id=u.person_distri_id,i.ques_id=s.value.children[_].ques_id,d.value&&(i.quality_id=u.quality_id)):t.query.type==2&&(i.answer_exception_id=u.answer_exception_id),o.push(i)}),e.mark_info=o,t.query.type==3?(e.quality_type=a,J(e)):t.query.type==2?$(e):G(e)},G=a=>{var o;a.cost_time=ee.value,a.mark_type=d.value?2:1;const e=(o=r.value[l.value])==null?void 0:o.is_again_mark;e&&(a.is_again_mark=e),ce(a).then(u=>{u.code&&u.code===200?S():(v(),m.error(u.msg))}).catch(()=>{v()})},J=a=>{a.project_id=t.query.project_id,a.subject_id=t.query.subject_id,a.ques_code=t.query.ques_code,a.reviewer_id=r.value[l.value].reviewer_id,a.quality_mark_type=d.value?2:1,a.stu_secret_num=r.value[l.value].stu_secret_num,fe(a).then(e=>{e.code&&e.code===200?S():(v(),m.error(e.msg))}).catch(()=>{v()})},$=a=>{a.round_id=r.value[l.value].round_id,a.ques_code=r.value[l.value].ques_code,a.stu_secret_num=r.value[l.value].stu_secret_num;const e=r.value[l.value].task_id;a.task_id=e||r.value[l.value].answer_data[0].task_id,ge(a).then(o=>{o.code&&o.code===200?S():m.error(o.msg)})},S=()=>{if(w.value=!1,d.value){r.value=b.value,l.value=M.value,d.value=!1;const a=y.value.titleData.state;_e.alert(`已经返回${a}。`,"提示",{confirmButtonText:"知道了",callback:()=>{}}),y.value.leftCollapsed=!0}else l.value<r.value.length-1?(l.value++,r.value[l.value].answer_data[0].isActive=!0,f.value==="point"&&(s.value.children.forEach(a=>{a.ques_mark_point.forEach(e=>e.isActive=!1)}),s.value.children[0].ques_mark_point[0].isActive=!0),(t.query.type==0||t.query.type==6)&&r.value[l.value].answer_data.forEach(a=>{a.error&&(a.error=!1)})):q();s.value.children.forEach(a=>{a.marking_score=null,a.ques_mark_point.forEach(e=>{e.marking_score=null})}),t.query.type==3&&k(r.value[l.value]),y.value.focusInput(),E(),v(),y.value.leftCollapsed||q(1)},Y=()=>{r.value=b.value,l.value=M.value,d.value=!1,y.value.leftCollapsed=!0,["0","6"].includes(t.query.type)?(E(),v()):t.query.type==3&&N(()=>{k(r.value[l.value])})},K=()=>{};return(a,e)=>{const o=ie("table-component");return oe(),se("div",null,[T(X,{ref_key:"markingPaperRef",ref:y,markValue:f.value,"onUpdate:markValue":e[0]||(e[0]=u=>f.value=u),quesInfo:s.value,"onUpdate:quesInfo":e[1]||(e[1]=u=>s.value=u),noMarkStuList:r.value,"onUpdate:noMarkStuList":e[2]||(e[2]=u=>r.value=u),curIndex:l.value,"onUpdate:curIndex":e[3]||(e[3]=u=>l.value=u),isFocusNext:w.value,"onUpdate:isFocusNext":e[4]||(e[4]=u=>w.value=u),reMarkingFlag:d.value,"onUpdate:reMarkingFlag":e[5]||(e[5]=u=>d.value=u),isLoadNoMark:z.value,onCommitMark:D,onLeftExpand:e[6]||(e[6]=u=>q(1)),onChangeTableHeight:I,onGoBackToTask:Y,onSetProblem:K},{leftTable:ne(()=>[T(o,{minHeight:n.value.styleOptions.minHeight,"table-options":n.value,"table-data":h.value,onOnHandleRowClick:F,onOnHandleSizeChange:R,onOnHandleCurrentChange:A},null,8,["minHeight","table-options","table-data"])]),_:1},8,["markValue","quesInfo","noMarkStuList","curIndex","isFocusNext","reMarkingFlag","isLoadNoMark"])])}}});export{Pe as default};
