import{S as U}from"./index-BCxZUZMh.js";import{c as O}from"./checkInfo-mJiqvlDC.js";import{g as A}from"./paper-verrification-C986OvHz.js";import{d as M,l as s,n as j,ao as q,r as a,o as e,g as n,h as c,e as t,c as u,F as I,p as S,U as G,b as _,y as r,t as J,f as V,u as b,ac as K,ad as P,_ as Q}from"./index-B63pSD2p.js";const W=[{prop:"",name:"考生密号"},{prop:"",name:"试卷"},{prop:"",name:"试题编号"},{prop:"",name:"是否有作答"},{prop:"",name:"得分"}],m=p=>(K("data-v-c70666f8"),p=p(),P(),p),X={class:"veerfication-item dark:!bg-black eye-box"},Y={class:"msg"},Z={class:"veerfication-result dark:!bg-black eye-box"},$=m(()=>t("div",{class:"title"},"核验结果",-1)),ee={key:1,class:"scuccess-info"},te=["src"],oe=m(()=>t("div",null,"真棒，每项检测都通过啦！",-1)),se={key:2,class:"error-div"},ae=m(()=>t("div",{class:"msg"},"错误原因",-1)),le=m(()=>t("div",{class:"msg"}," 错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因错误原因 ",-1)),ne=[ae,le],ce=M({__name:"check-content",setup(p,{expose:w}){const f=s(!1),v=s(3),k=s([{name:"考生试题有作答无成绩",isLoading:!1,state:1},{name:"考生试题有成绩无作答",isLoading:!1,state:1},{name:"得分超过试题满分或出现负分",isLoading:!1,state:1},{name:"总分超过试卷满分或负分",isLoading:!1,state:1}]);s([]);const y=s(),i=s(),x=s(0);let d=null;const F=()=>{d=new ResizeObserver(l=>{l.forEach(h=>{i.value&&(x.value=y.value.clientHeight-i.value.clientHeight-85+"px")})}),i.value&&d.observe(i.value)},g=s(!1),B=()=>{g.value=!0,k.value.forEach(l=>{l.isLoading=!0}),A().then(l=>{})},D=l=>{f.value=!0,setTimeout(()=>{F()},200)};return j(()=>{}),q(()=>{d&&(d.disconnect(),d=null)}),w({openDialog:D}),(l,h)=>{const C=a("el-button"),R=a("CircleCheckFilled"),L=a("el-icon"),H=a("CircleCloseFilled"),N=a("el-text"),z=a("el-table-column"),E=a("el-table"),T=a("el-dialog");return e(),n(T,{title:"阅卷核验","close-on-click-modal":"false","destroy-on-close":"",modelValue:f.value,"onUpdate:modelValue":h[0]||(h[0]=o=>f.value=o),width:"80%"},{default:c(()=>[t("div",{ref_key:"mainRef",ref:y,class:"paper-verrification"},[t("div",X,[(e(!0),u(I,null,S(k.value,o=>(e(),u("div",{class:G([o.state==3?"errorDiv":""])},[t("div",Y,[_(C,{class:"loading-icon",type:"primary",text:"",loading:o.isLoading},null,8,["loading"]),o.state==2?(e(),n(L,{key:0,class:"success-icon"},{default:c(()=>[_(R)]),_:1})):r("",!0),o.state==3?(e(),n(L,{key:1,class:"error-icon"},{default:c(()=>[_(H)]),_:1})):r("",!0),t("div",null,J(o.name),1)]),o.state==3?(e(),n(N,{key:0,type:"error",style:{cursor:"pointer"}},{default:c(()=>[V("查看")]),_:1})):r("",!0)],2))),256)),_(C,{class:"operBtn",type:"primary",onClick:B,loading:g.value,disabled:g.value},{default:c(()=>[V("开始检验")]),_:1},8,["loading","disabled"])]),t("div",Z,[$,v.value==1?(e(),n(b(U),{key:0,title:"阅卷核验",stepList:["点击【开始核验】按钮，系统自动核验","不通过核验项，点击【查看】按钮，查看详情"]})):r("",!0),v.value==2?(e(),u("div",ee,[t("img",{src:b(O)},null,8,te),oe])):r("",!0),v.value==3?(e(),u("div",se,[t("div",{ref_key:"errorInfoRef",ref:i,class:"errorDiv",style:{padding:"4px 8px","margin-bottom":"12px","border-left":"2px solid red !important"}},ne,512),_(E,{border:"","scrollbar-always-on":"",stripe:"",height:x.value},{default:c(()=>[(e(!0),u(I,null,S(b(W),o=>(e(),n(z,{label:o.name,align:"center"},null,8,["label"]))),256))]),_:1},8,["height"])])):r("",!0)])],512)]),_:1},8,["modelValue"])}}}),_e=Q(ce,[["__scopeId","data-v-c70666f8"]]);export{_e as default};
