var h=Object.defineProperty,g=Object.defineProperties;var x=Object.getOwnPropertyDescriptors;var l=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var p=(s,e,t)=>e in s?h(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,c=(s,e)=>{for(var t in e||(e={}))z.call(e,t)&&p(s,t,e[t]);if(l)for(var t of l(e))R.call(e,t)&&p(s,t,e[t]);return s},d=(s,e)=>g(s,x(e));import{aR as k,b5 as w,b6 as L,b7 as C,T as r,aK as T}from"./index-B63pSD2p.js";const U={xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",zip:"application/zip",zfstb:"application/zfstb",zfcsb:"application/zfcsb",sqlite:"application/octet-stream"};function q(s="get",e,t,n={},i={},u="zip"){let f=k(e),m=w();return L(d(c({timeout:4*60*60*1e3,method:s,url:f},s==="get"?{params:n}:{data:i}),{responseType:"blob",headers:{Authorization:C(m.accessToken)}})).then(o=>{if(o&&o.data&&o.data.type=="application/json")return o.data.text().then(a=>{const b=JSON.parse(a);r.warning(b.msg)}),o;{let a=t||N(o.headers.get("Content-disposition"));return y(o,U[u],a),o}}).catch(o=>{var a;if(o.response){if(o.response.status===401){r.warning("登录已过期！请重新登录！"),T.push("/login");return}else if(o.response.status===422){r.error("参数错误！");return}else if(o.response.status===500){r.error("系统错误！请稍后重试！");return}throw o}else r.error(`${e}报错：`+((a=o.response)==null?void 0:a.statusText))})}function y(s,e,t){const n=document.createElement("a");let i=new Blob([s.data],{type:e});n.setAttribute("download",t),n.href=URL.createObjectURL(i),document.body.appendChild(n),n.click(),document.body.removeChild(n)}function N(s){const e=s.split("; ");for(let t of e)if(t.startsWith("filename*=")){const[,n,i]=t.slice(9).split("'");return decodeURIComponent(i.replace(/"/g,""),n)}return null}export{q as d};
