import{h as g}from"./handleImages-D-nd439N.js";import{o as t,c as n,e as s,d as V,l as A,r as j,f as x,t as o,F as u,b as y,u as r,y as d,p as _,ac as R,ad as O,_ as Q}from"./index-B63pSD2p.js";const W={xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",class:"icon",viewBox:"0 0 1024 1024"},z=s("path",{fill:"#67C23A",d:"M107.99 530.065s145.95 134.74 252.219 291.305C576.63 460.492 909.634 262.602 909.634 262.602s27.354-85.793-30.638-56.646c0 0-236.3 99.317-538.515 368.873-103.13-99.467-162.84-124.28-162.84-124.28-74.66-36.094-69.651 79.516-69.651 79.516"},null,-1),P=[z];function G(e,l){return t(),n("svg",W,[...P])}const J={render:G},K={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",class:"icon",viewBox:"0 0 1024 1024"},U=s("path",{fill:"#E6A23C",d:"M953.85 97.45h-98.647s-101.575 67.702-235.165 197.304a1579 1579 0 0 0-36.912-47.05s-92.578-91.077-221.046-59.176c0 0 80.727 72.976 180.76 184.692-61.207 65.183-125.912 141.501-188.677 228.507L137.22 441.646l-71.024 64.032s197.238 256.123 287.967 420.248c0 0 108.017-210.7 277.658-448.326 66.164 81.76 133.019 174.796 185.645 270.796l18.03-67.21s-42.524-125.624-144.329-284.299c78.409-103.48 167.185-208.12 262.683-299.438"},null,-1),X=[U];function Y(e,l){return t(),n("svg",K,[...X])}const Z={render:Y},ee={xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",class:"icon",viewBox:"0 0 1024 1024"},te=s("path",{fill:"red",d:"M972.749 154.522c10.47 13.081-154.752 140.467-333.107 297.216 170.24 211.737 183.193 356.172 168.243 382.336C790.042 865.229 671.77 705.05 529.664 551.09c-30.31 28.288-59.98 56.935-88.269 85.53-136.755 138.06-215.757 309.094-240.41 333.568-56.78 77.26-352.511-62.618 85.735-440.448 42.573-36.71 82.509-69.146 121.037-99.456C281.958 317.747 157.85 242.176 85.76 204.109c-87.117-46.003-24.14-295.066 345.037 31.488 38.835 34.355 73.062 67.481 104.73 99.737C810.341 144.128 954.572 131.84 972.748 154.522"},null,-1),ne=[te];function se(e,l){return t(),n("svg",ee,[...ne])}const oe={render:se},c=e=>(R("data-v-f3372ddb"),e=e(),O(),e),ie={class:"grade-score-box bg-[#F2F2F2] dark:bg-black eye-box"},de=c(()=>s("span",{class:"grade-text-bold"},"试题序号：",-1)),ce=c(()=>s("span",{class:"grade-text-bold"},"试题分数：",-1)),le=c(()=>s("span",{class:"grade-text-bold"},"考生得分：",-1)),ue={class:"stu-score-box"},ae={class:"flex flex-column items-center"},re=c(()=>s("span",{class:"grade-text-bold"},"判断结果：",-1)),_e={key:0},he={key:1},fe={key:2},qe={key:0},Ie={key:1},ke=c(()=>s("div",{class:"grade-text-bold"},"试题描述：",-1)),ye={class:"grade-text-inline"},ge={key:0,class:"mt-[0.5px]"},xe=["innerHTML"],ve=["innerHTML"],be={key:0},me=["innerHTML"],we=["innerHTML"],Le={key:1},Me={key:0,class:"pt-[3px] pb-[3px]"},$e=c(()=>s("span",{class:"grade-text-bold"},"参考答案：",-1)),He={key:0},Te={key:1},Ce=c(()=>s("br",null,null,-1)),Be=["innerHTML"],Fe={key:2},pe={class:"pt-[3px] pb-[3px]"},De=c(()=>s("span",{class:"grade-text-bold"},"评分规则：",-1)),Se={key:0},Ae={key:1},Ee={key:1,class:"pt-[3px] pb-[3px]"},Ne=c(()=>s("span",{class:"grade-text-bold"},"评分标准：",-1)),Ve={key:0},je={key:1,class:"point-list"},Re={key:2,class:"pt-[3px] pb-[3px] answer-bg-box"},Oe=c(()=>s("span",{class:"grade-text-bold"},"考生答案：",-1)),Qe={key:0},We={key:1},ze=c(()=>s("br",null,null,-1)),Pe=["innerHTML"],Ge={key:2},Je={key:3,class:"pt-[3px] pb-[3px]"},Ke=c(()=>s("span",{class:"grade-text-bold"},"评分评析：",-1)),Ue={class:"answer-parse-list"},Xe={key:0},Ye={key:0},Ze={key:2},et={class:"ml-[32px]"},tt={key:0},nt=V({name:"question-desc",__name:"question-desc",props:["questionInfo","checkList","studentInfo"],setup(e){A([]);const l=A(!1),v=(I,k)=>{const h=I==null?void 0:I.map((f,q)=>k?`第${q+1}个空答案：${f}`:`第${q+1}个空答案：${f}`);return h==null?void 0:h.join("</br>")};return(I,k)=>{var f,q,b,m,w,L,M,$,H,T,C,B,F,p,D,S;const h=j("question-desc",!0);return t(),n("div",null,[s("div",ie,[s("div",null,[de,x(o(e.questionInfo.ques_order)+"（ID："+o(e.questionInfo.ques_code)+"） ",1)]),e.questionInfo.ques_type_code!=="F"?(t(),n(u,{key:0},[s("div",null,[ce,s("span",null,o(e.questionInfo.ques_score),1)]),s("div",null,[le,s("span",ue,o(e.questionInfo.stu_score),1)]),s("div",ae,[re,e.questionInfo.mark_result===1?(t(),n("i",_e,[y(r(J))])):e.questionInfo.mark_result===2?(t(),n("i",he,[y(r(oe))])):e.questionInfo.mark_result===3?(t(),n("i",fe,[y(r(Z))])):d("",!0)]),s("div",null,[s("span",{class:"grade-detail-bold",onClick:k[0]||(k[0]=i=>l.value=!l.value)},[l.value?(t(),n("span",Ie,"隐藏评析")):(t(),n("span",qe,"查看评析"))])])],64)):d("",!0)]),s("div",null,[ke,s("div",ye,[(f=e.questionInfo)!=null&&f.ques_order?(t(),n("div",ge,o((q=e.questionInfo)==null?void 0:q.ques_order)+"．",1)):d("",!0),(m=(b=e.questionInfo)==null?void 0:b.ques_desc)!=null&&m.html?(t(),n("div",{key:1,innerHTML:r(g)((L=(w=e.questionInfo)==null?void 0:w.ques_desc)==null?void 0:L.html)},null,8,xe)):(t(),n("div",{key:2,innerHTML:($=(M=e.questionInfo)==null?void 0:M.ques_desc)==null?void 0:$.text},null,8,ve))]),e.questionInfo.ques_type_code!=="B"?(t(),n("div",be,[(t(!0),n(u,null,_((H=e.questionInfo)==null?void 0:H.ques_choices,i=>(t(),n("div",null,[i.html?(t(),n("p",{key:0,class:"grade-text-inline",innerHTML:r(g)(i.html)},null,8,me)):(t(),n("p",{key:1,class:"grade-text-inline",innerHTML:r(g)(i.options)},null,8,we))]))),256))])):d("",!0),e.questionInfo.ques_type_code!=="F"?(t(),n("div",Le,[e.checkList.includes(1)&&e.questionInfo.ques_type_code!=="E"?(t(),n("div",Me,[$e,e.questionInfo.ques_type_code==="B"?(t(),n("span",He,o(e.questionInfo.standard_answer[0]==="1"?"正确":"错误"),1)):e.questionInfo.ques_type_code==="D"?(t(),n("span",Te,[Ce,s("span",{innerHTML:v(e.questionInfo.standard_answer)},null,8,Be)])):(t(),n("span",Fe,o((T=e.questionInfo.standard_answer)==null?void 0:T.toString()),1))])):d("",!0),s("div",pe,[De,((C=e.questionInfo.e_mark_rule)==null?void 0:C.length)===0?(t(),n("span",Se,"无")):(t(),n("span",Ae,o(e.questionInfo.e_mark_rule),1))]),e.questionInfo.ques_type_code==="E"?(t(),n("div",Ee,[Ne,((B=e.questionInfo.ques_mark_point)==null?void 0:B.length)===0?(t(),n("span",Ve,"无")):(t(),n("ul",je,[(t(!0),n(u,null,_(e.questionInfo.ques_mark_point,(i,a)=>(t(),n("li",null,o(a+1)+". "+o(i.point)+" ("+o(i.score)+"分) ",1))),256))]))])):d("",!0),e.checkList.includes(2)?(t(),n("div",Re,[Oe,e.questionInfo.ques_type_code==="B"&&e.questionInfo.stu_answer?(t(),n("span",Qe,o(e.questionInfo.stu_answer[0]==="1"?"正确":"错误"),1)):e.questionInfo.ques_type_code==="D"?(t(),n("span",We,[ze,s("span",{innerHTML:v(e.questionInfo.stu_answer)},null,8,Pe)])):(t(),n("span",Ge,o((F=e.questionInfo.stu_answer)==null?void 0:F.toString()),1))])):d("",!0),l.value?(t(),n("div",Je,[Ke,s("ul",Ue,[e.questionInfo.ques_type_code==="D"?(t(!0),n(u,{key:0},_(e.questionInfo.answer_parse,(i,a)=>(t(),n("li",null,[(t(!0),n(u,null,_(i,(E,N)=>(t(),n("span",null,[e.studentInfo.flag==="AI"?(t(),n("span",Xe,o(N+1)+". ",1)):d("",!0),x(o(E),1)]))),256))]))),256)):(t(!0),n(u,{key:1},_(e.questionInfo.answer_parse[0],(i,a)=>(t(),n("li",null,[e.studentInfo.flag==="AI"?(t(),n("span",Ye,o(a+1)+". ",1)):d("",!0),x(o(i),1)]))),256))])])):d("",!0)])):d("",!0),(p=e.questionInfo)!=null&&p.children&&((D=e.questionInfo)==null?void 0:D.children.length)>0?(t(),n("div",Ze,[(t(!0),n(u,null,_((S=e.questionInfo)==null?void 0:S.children,(i,a)=>(t(),n("div",et,[y(h,{"question-info":i,checkList:e.checkList,studentInfo:e.studentInfo},null,8,["question-info","checkList","studentInfo"]),a!==e.questionInfo.children.length-1?(t(),n("br",tt)):d("",!0)]))),256))])):d("",!0)])])}}}),st=Q(nt,[["__scopeId","data-v-f3372ddb"]]),dt=Object.freeze(Object.defineProperty({__proto__:null,default:st},Symbol.toStringTag,{value:"Module"}));export{Z as H,st as Q,J as R,oe as W,dt as q};
