var d=Object.defineProperty,p=Object.defineProperties;var v=Object.getOwnPropertyDescriptors;var _=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(r,t,e)=>t in r?d(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,s=(r,t)=>{for(var e in t||(t={}))q.call(t,e)&&i(r,e,t[e]);if(_)for(var e of _(t))c.call(t,e)&&i(r,e,t[e]);return r},o=(r,t)=>p(r,v(t));import{aC as m,aQ as u,aR as a}from"./index-B63pSD2p.js";const n=m(),I=r=>u.request("post",a("/v1/official_mark/get_task_list"),{data:o(s({},r),{user_id:n.userId})}),f=r=>u.request("post",a("/v1/official_mark/get_ques_group_list"),{data:o(s({},r),{user_id:n.userId})}),l=r=>u.request("post",a("/v1/user/get_user"),{data:s({},r)}),k=r=>u.request("post",a("/v1/survey_monitor/project_survey_monitor"),{data:o(s({},r),{user_id:n.userId})}),D=r=>u.request("post",a("/v1/survey_monitor/ques_group_survey_monitor"),{data:o(s({},r),{user_id:n.userId})}),M=r=>u.request("post",a("/v1/survey_monitor/group_survey_monitor"),{data:o(s({},r),{user_id:n.userId})}),b=r=>u.request("post",a("/v1/survey_monitor/person_survey_monitor"),{data:o(s({},r),{user_id:n.userId})});export{l as a,M as b,f as c,k as d,D as e,I as g,b as q};
