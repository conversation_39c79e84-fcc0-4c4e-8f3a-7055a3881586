@echo off
chcp 65001 >nul
title 卓帆电子化考试阅卷管理系统 - 一键启动器

echo ==========================================
echo 卓帆电子化考试阅卷管理系统 - 一键启动器
echo ==========================================
echo.

echo 正在检查Python环境...

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python环境！
    echo 请先安装Python 3.6或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过

:: 检查并安装依赖
echo 正在检查依赖包...
python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    pip install psutil
    if %errorlevel% neq 0 (
        echo 错误：依赖包安装失败！
        echo 请手动运行：pip install psutil
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo.

:: 选择启动器版本
echo 请选择启动器版本：
echo 1. GUI图形界面版本（推荐）
echo 2. 命令行版本
echo.
set /p choice="请输入选项 (1 或 2): "

if "%choice%"=="1" (
    echo 正在启动GUI版本...
    python -c "import tkinter" >nul 2>&1
    if %errorlevel% neq 0 (
        echo 警告：GUI环境不可用，自动切换到命令行版本
        python "简易启动器.py"
    ) else (
        python "一键启动器.py"
    )
) else if "%choice%"=="2" (
    echo 正在启动命令行版本...
    python "简易启动器.py"
) else (
    echo 无效选项，使用默认GUI版本...
    python "一键启动器.py"
)

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出错！
    pause
)
