var U=Object.defineProperty;var k=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var P=(a,r,o)=>r in a?U(a,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[r]=o,T=(a,r)=>{for(var o in r||(r={}))z.call(r,o)&&P(a,o,r[o]);if(k)for(var o of k(r))I.call(r,o)&&P(a,o,r[o]);return a};import{g as M}from"./common-methods-BWkba4Bo.js";import{i as R,g as W}from"./validate-Dc6ka3px.js";import{e as $,f as G,h as J}from"./user-management-B1vGxPiG.js";import{p as K}from"./index-CMAj5lxj.js";import{d as Q,l as u,P as X,n as Y,r as j,o as Z,g as ee,h,e as F,b as V,f as q,C as te,T as g,_ as le}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const re={class:"zf-dialog-first-box"},oe={class:"zf-dialog-second-box"},ae={class:"footer-btn"},se=Q({__name:"add-users",props:{rolesList:{type:Array,default:()=>{}},defaultPassword:{type:String,default:""},regionList:{type:Array,default:()=>{}},makingStaffFlag:{type:Boolean,default:!1}},emits:["queryData"],setup(a,{expose:r,emit:o}){const n=a,w=o,v=u("创建"),b=u(!1),x=u([]),S={username:[{required:!0,message:"请输入账号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(e,t,l)=>{if(t.length>50)return l(new Error("账号长度不能超过50！"));l()}}],name:[{required:!0,message:"请输入姓名",trigger:["blur","change"]},{trigger:["blur","change"],validator:(e,t,l)=>{if(t.length>50)return l(new Error("姓名长度不能超过50！"));l()}}],role_id:[{required:!0,message:"请选择用户角色",trigger:["blur","change"]}],id_card:[{trigger:["blur","change"],validator:(e,t,l)=>{if(!t||R(t))l();else return l(new Error("请输入正确的身份证号（18位）！"))}}],phone:[{trigger:["blur","change"],validator:(e,t,l)=>{if(!t||W(t))l();else return l(new Error("请正确的手机号码（11位）！"))}}]},D=[{label:"用户名",prop:"name",type:"input",defaultValue:"",placeholder:"请输入用户名",isHidden:!1,clearable:!0},{label:"用户账号",prop:"username",type:"input",defaultValue:"",placeholder:"请输入用户账号",isHidden:!1,clearable:!0},{label:"所属角色",prop:"role_id",type:"select",clearable:!0,defaultValue:[],multiple:!0,placeholder:"请选择所属角色",optionData:()=>[]},{label:"手机号",prop:"phone",type:"input",defaultValue:"",placeholder:"请输入手机号",isHidden:!1,clearable:!0},{label:"身份证号",prop:"id_card",type:"input",defaultValue:"",placeholder:"请输入身份证号",isHidden:!1,clearable:!0}],p=u({}),_=X({column:3,inline:!0,labelWidth:"108px",itemWidth:"250px",rules:S,fields:D}),i=u(null),H=u({}),s=u("01");Y(()=>{n.makingStaffFlag&&M()});const B=(e,t)=>{b.value=!0,s.value=e,s.value==="01"?v.value="创建":s.value==="02"?(v.value="编辑",H.value=t,t.project_id_list&&C(t.project_id_list),te(()=>{var d,c,f;_.fields.map(m=>{m.prop==="username"&&t.is_used&&(m.disabled=!0),t.hasOwnProperty(m.prop)&&i.value.setCardData(m.prop,t[m.prop])});const l=[(d=t.province_code)!=null?d:"",(c=t.city_code)!=null?c:"",(f=t.district_code)!=null?f:""].filter(Boolean);i.value.setCardData("region",l),setTimeout(()=>{i.value.clearValidateFn()},100)})):s.value==="04"&&(v.value="创建"),_.fields.map(l=>{(l.prop==="project_id_list"||l.prop==="subject_id_list")&&(n.makingStaffFlag?l.isHidden&&(l.isHidden=!1):l.isHidden||(l.isHidden=!0)),l.prop==="role_id"?l.optionData=()=>n.rolesList:l.prop==="defaultPwd"?l.text=()=>`<span style="color: #F56C6C">${n.defaultPassword}</span>`:l.prop==="region"&&(l.optionData=()=>n.regionList)}),setTimeout(()=>{i.value.clearValidateFn()},100)},y=()=>{b.value=!1,_.fields=D,_.fields.map(e=>{e.prop==="username"&&(e.disabled=!1)}),i.value.resetFieldsFn()},L=(e,t)=>{if((s.value==="01"||s.value==="04")&&e.prop==="name"&&t){const l=K(t,{toneType:"none",type:"array"}),d=l[0]+l.slice(1).map(c=>c[0]).join("");i.value.setCardData("username",d)}},E=(e,t)=>{e.prop==="project_id_list"&&(x.value=[],p.value.subject_id_list&&(p.value.subject_id_list=null),t&&C())},A=()=>{i.value.formValidate().then(()=>{let e=T({},p.value);e.region&&e.region.length>0&&(e.province_code=e.region[0],e.city_code=e.region[1]?e.region[1]:null,e.district_code=e.region[2]?e.region[2]:null,delete e.region),n.makingStaffFlag?e.system_user_type=2:e.system_user_type=1,(!e.project_id_list||e.project_id_list.length===0)&&delete e.project_id_list,(!e.subject_id_list||e.subject_id_list.length===0)&&delete e.subject_id_list,delete e.defaultPwd,s.value==="01"||s.value==="04"?N(e):s.value==="02"&&(e.user_id=H.value.user_id,e.role_id_list=e.role_id,delete e.role_id,delete e.username,O(e))}).catch(()=>{g.warning("请按要求填写！")})},N=e=>{$(e).then(t=>{t.code&&t.code===200?(g.success(t.msg),w("queryData"),y()):g.warning(t.msg)})},O=e=>{G(e).then(t=>{t.code&&t.code===200?(g.success(t.msg),w("queryData"),y()):g.warning(t.msg)})},C=e=>{let t={project_id_list:e||p.value.project_id_list};J(t).then(l=>{l.code&&l.code===200?x.value=l.data.data:g.error(l.msg)})};return r({openDialog:B}),(e,t)=>{const l=j("form-component"),d=j("el-button"),c=j("el-dialog");return Z(),ee(c,{modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=f=>b.value=f),title:v.value,width:"440px","close-on-click-modal":!1,"before-close":y,"align-center":"",draggable:""},{footer:h(()=>[F("div",ae,[V(d,{onClick:y},{default:h(()=>[q("取消")]),_:1}),V(d,{type:"primary",onClick:A},{default:h(()=>[q("确定")]),_:1})])]),default:h(()=>[F("div",re,[F("div",oe,[V(l,{ref_key:"formRef",ref:i,modelValue:p.value,"onUpdate:modelValue":t[0]||(t[0]=f=>p.value=f),"form-options":_,"is-query-btn":!1,onOnblurFn:L,onOnchangeFn:E},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}}),ge=le(se,[["__scopeId","data-v-841b8ff4"]]);export{ge as default};
