import{c as D,u as N}from"./marking-mode-CLpbbjcA.js";import{d as R,i as O,l as s,P,r as m,o as W,g as q,h as i,e as f,b as _,f as w,C as B,T as l}from"./index-B63pSD2p.js";const J={class:"zf-dialog-first-box"},T={class:"zf-dialog-second-box"},z={class:"footer-btn"},A=R({__name:"add-mode",emits:["queryData"],setup(E,{expose:x,emit:S}){const g=O(),c=s("创建阅卷模式"),n=s(!1),d=s("01"),u=s({}),v=s({}),h=P({column:3,labelWidth:"108px",itemWidth:"250px",rules:{process_name:[{required:!0,message:"请输入模式名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(o,e,a)=>{if(e.length>100)return a(new Error("阅卷模式名称长度不能超过100！"));a()}}]},fields:[{label:"模式名称",prop:"process_name",type:"input",defaultValue:"",placeholder:"请输入模式名称",clearable:!0}]}),t=s(null),k=(o,e)=>{n.value=!0,d.value=o,u.value=e,o==="01"?c.value="创建阅卷模式":o==="02"&&(c.value="编辑阅卷模式",B(()=>{h.fields.map(a=>{e.hasOwnProperty(a.prop)&&t.value.setCardData(a.prop,e[a.prop])})}))},r=()=>{n.value=!1,t.value.resetFieldsFn()},V=()=>{t.value.formValidate().then(()=>{let o=JSON.parse(JSON.stringify(t.value.getAllCardData()));o.process_json=u.value.processJson,d.value==="01"?y(o):d.value==="02"&&(o.process_id=u.value.process_id,C(o))}).catch(()=>{l.warning("请按要求填写！")})},y=o=>{D(o).then(e=>{e.code&&e.code===200?(l.success(e.msg),r(),g.push({path:"/base-management/marking-mode/index"})):l.warning(e.msg)})},C=o=>{N(o).then(e=>{e.code&&e.code===200?(l.success(e.msg),r(),g.push({path:"/base-management/marking-mode/index"})):l.warning(e.msg)})};return x({openDialog:k}),(o,e)=>{const a=m("form-component"),b=m("el-button"),F=m("el-dialog");return W(),q(F,{modelValue:n.value,"onUpdate:modelValue":e[1]||(e[1]=p=>n.value=p),title:c.value,width:"440px","align-center":"","close-on-click-modal":!1,"before-close":r,draggable:""},{footer:i(()=>[f("div",z,[_(b,{onClick:r},{default:i(()=>[w("取消")]),_:1}),_(b,{type:"primary",onClick:V},{default:i(()=>[w("确定")]),_:1})])]),default:i(()=>[f("div",J,[f("div",T,[_(a,{ref_key:"formRef",ref:t,modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=p=>v.value=p),"form-options":h,"is-query-btn":!1},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}});export{A as _};
