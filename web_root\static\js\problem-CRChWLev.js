import{d as k,l as a,P as D,r as c,o as w,g as C,h as s,e as f,b as p,f as v,T as N,_ as O}from"./index-B63pSD2p.js";const q={class:"footer-btn"},B=k({__name:"problem",emits:["setProblem"],setup(P,{expose:b,emit:J}){const g=a("提交问题卷的原因"),t=a(!1),r=a(null),l=a({}),y=D({labelWidth:"100px",inline:!0,rules:{exception_type:{required:!0,message:"请选择问题卷类型",trigger:["blur","change"]}},fields:[{label:"问题卷类型",prop:"exception_type",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择问题卷类型",optionData:()=>[{value:1,label:"图像错误"},{value:2,label:"作答位置错误"},{value:3,label:"作答合并"},{value:4,label:"其他"}]}]}),u=a({}),o=a({}),x=(n,e)=>{t.value=!0,u.value=n,o.value=e},d=()=>{l.value={},r.value.resetFieldsFn(),t.value=!1},_=()=>{o.value.exception_type=null,o.value.error=!1,d()},V=()=>{r.value.formValidate().then(()=>{u.value.task_id,o.value.answer_id,u.value.round_id,l.value.exception_type,o.value.exception_type=JSON.parse(JSON.stringify(l.value.exception_type)),d()}).catch(()=>{N.warning("请按要求填写！")})};return b({openDialog:x}),(n,e)=>{const h=c("form-component"),m=c("el-button"),F=c("el-dialog");return w(),C(F,{modelValue:t.value,"onUpdate:modelValue":e[1]||(e[1]=i=>t.value=i),title:g.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",width:"30%","close-on-click-modal":!1,"before-close":_},{footer:s(()=>[f("div",q,[p(m,{onClick:_},{default:s(()=>[v("取消")]),_:1}),p(m,{type:"primary",onClick:V},{default:s(()=>[v("保存")]),_:1})])]),default:s(()=>[f("div",null,[p(h,{ref_key:"formRef",ref:r,modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=i=>l.value=i),"form-options":y,"is-query-btn":!1,onOnchangeFn:n.onchangeFn,onQueryDataFn:n.queryListFn},null,8,["modelValue","form-options","onOnchangeFn","onQueryDataFn"])])]),_:1},8,["modelValue","title"])}}}),R=O(B,[["__scopeId","data-v-c4c945c2"]]);export{R as default};
