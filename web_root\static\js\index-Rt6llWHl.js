import{_ as oe}from"./TinymceEditor.vue_vue_type_style_index_0_lang-Dj-6iP6L.js";import fe from"./op-mark-step-DW83lcNi.js";import{a as ye}from"./convertNumber-CmbNKqvY.js";import{h as U}from"./handleImages-D-nd439N.js";import{g as qe}from"./questionEdit-C5KtQM0G.js";import{d as he,aS as ge,aO as xe,l as K,r as q,o as t,c as s,t as h,y as v,f as F,e as i,u as C,F as b,p as M,g as H,h as x,q as ve,b as p,ac as ke,ad as be,_ as me,az as we,i as Se,n as Ve,U as Ce,a4 as Ne,aV as ce,H as Q,T as R,b3 as $e}from"./index-B63pSD2p.js";import Te from"./question-edit-CKclD8CL.js";import{R as Oe}from"./reply-fill-DeI84Ty7.js";import{c as Me,u as pe}from"./test-paper-management-DjV_45YZ.js";import"./util-DBFSI-P4.js";const N=$=>(ke("data-v-a578a7f2"),$=$(),be(),$),Le={class:"readOnly-box"},Ee={key:0,class:"text-bold-box ques-code-bottom"},Fe={key:1,class:"text-inline-box"},Je=["innerHTML"],He={key:2,class:"text-inline-box"},De=["innerHTML"],Be={key:3,class:""},Qe={class:"text-inline-box"},Ue={key:0,class:"flex-c"},Ae=["innerHTML"],ze={key:1,class:"flex-c",style:{"min-height":"32px"}},Pe=["innerHTML"],je={key:4,class:"pt-[8px]"},Re={key:0},Ge=N(()=>i("span",{class:"text-bold-box whitespace-nowrap"},"参考答案：",-1)),Ze={key:3,class:"flex"},Ie={key:0,class:"ques-editor-input"},Ke={key:1,class:"ques-editor-input"},We={key:1,class:"text-inline-box"},Xe=N(()=>i("span",{class:"text-bold-box whitespace-nowrap"},"参考答案：",-1)),Ye={key:0},et={key:1},tt={key:2},st={key:3},nt={key:0,class:"text-inline-box"},ot={class:"text-inline-box"},at=["innerHTML"],lt={key:1,class:"text-inline-box"},ut={class:"text-inline-box"},it=["innerHTML"],dt={key:4},_t={key:0},rt=["innerHTML"],ct={key:1},pt=["innerHTML"],vt={key:5,class:"pt-[8px] pb-[4px]"},ht={class:"text-bold-box flex"},mt=N(()=>i("span",null,"答案分组",-1)),ft=N(()=>i("span",null,"：",-1)),yt={key:1},qt={key:6},gt=N(()=>i("span",{class:"text-bold-box"},"难易程度：",-1)),xt={key:7},kt={key:0},bt=N(()=>i("div",{class:"text-bold-box mb-[2px]"},"答案解析：",-1)),wt={class:"ques-editor-input"},St={key:1,class:"flex"},Vt=N(()=>i("div",{class:"text-bold-box"},"答案解析：",-1)),Ct=["innerHTML"],Nt={key:1},$t={key:8},Tt=N(()=>i("span",{class:"text-bold-box"},"试题分数：",-1)),Ot={key:9,class:"mt-[10px]"},Mt=N(()=>i("div",{class:"text-bold-box"},"评分规则：",-1)),Lt=["innerHTML"],Et={key:10},Ft=N(()=>i("span",{class:"text-bold-box"},"权    重：",-1)),Jt={key:0},Ht={key:11,class:"mt-[10px]"},Dt={key:0,class:"point-box"},Bt=N(()=>i("div",{class:"text-bold-box"},"评分标准：",-1)),Qt={class:"point-form"},Ut={class:"form-point-box"},At=N(()=>i("span",{class:"text-bold-box"},"评分标准：",-1)),zt={key:0},Pt={key:0},jt={key:12},Rt=N(()=>i("div",{class:"border-b-[1px] border-dashed border-[#dcdfe6] m-[5px]"},null,-1)),Gt={class:"mt-[12px] mb-[10px]"},Zt={key:13,class:"mt-1"},It=N(()=>i("div",{class:"mark-step-text text-bold-box"},"评分步骤：",-1)),Kt=he({name:"question-read",__name:"index",props:ge({isShowQuesCode:{type:Boolean,default:!0},isEdit:{type:Boolean,default:!1}},{questionDesc:{},questionDescModifiers:{}}),emits:["update:questionDesc"],setup($){const e=xe($,"questionDesc"),W=["①","②","③","④","⑤","⑥","⑦","⑧","⑨","⑩","⑪","⑫","⑬","⑭","⑮","⑯","⑰","⑱","⑲","⑳","㉑","㉒","㉓","㉔","㉕","㉖","㉗","㉘","㉙","㉚","㉛","㉜","㉝","㉞","㉟","㊱","㊲","㊳","㊴","㊵","㊶","㊷","㊸","㊹","㊺","㊻","㊼","㊽","㊾","㊿"],T=K(!1),u=y=>{if(y==null)return"";const _=y.indexOf("（");return _!==-1?y.slice(_,y.length):y+"．"},D=(y,_)=>{if(_==="01")e.value.ques_choices.forEach(g=>{g.code===y&&(e.value.standard_answer=[g.options.slice(0,1)],e.value.standard_answer_html=[g.options.slice(0,1)])});else if(_==="02")e.value.ques_choices.forEach(g=>{g.code===y&&(e.value.standard_answer=[g.code],e.value.standard_answer_html=[g.code])});else if(_==="03"){let g=[];e.value.ques_choices.forEach(A=>{y.forEach(G=>{A.code===G&&g.push(A.options.slice(0,1))})}),e.value.standard_answer=g,e.value.standard_answer_html=g}},w=()=>{let y={point:"",score:1};e.value.ques_mark_point||(e.value.ques_mark_point=[]),e.value.ques_mark_point.push(y)},_e=y=>{e.value.ques_mark_point.splice(y,1)},J=y=>y.slice(2);return(y,_)=>{var se,n,o,l,r,d,c,V,k,S,m,O,L,ne,ue,I,ie,de,E,P;const g=q("el-radio"),A=q("el-radio-group"),G=q("el-checkbox"),X=q("el-checkbox-group"),Z=q("InfoFilled"),j=q("el-icon"),ae=q("el-tooltip"),Y=q("el-input"),re=q("el-input-number"),le=q("Delete"),ee=q("Plus"),te=q("el-tag"),z=q("question-read");return t(),s("div",Le,[$.isShowQuesCode&&e.value.ques_order&&!((se=e.value.ques_order)!=null&&se.includes("（"))?(t(),s("div",Ee," 试题编号："+h(e.value.ques_code),1)):v("",!0),(n=e.value)!=null&&n.ques_desc.html?(t(),s("div",Fe,[F(h(u((o=e.value)==null?void 0:o.ques_order))+" ",1),i("span",{innerHTML:C(U)(e.value.ques_desc.html)},null,8,Je)])):(t(),s("div",He,[F(h(u((l=e.value)==null?void 0:l.ques_order))+" ",1),i("span",{innerHTML:e.value.ques_desc.text},null,8,De)])),e.value.ques_type_code==="A"||e.value.ques_type_code==="C"?(t(),s("div",Be,[(t(!0),s(b,null,M(e.value.ques_choices,a=>(t(),s("div",Qe,[a.optionShow?(t(),s("div",Ue,[i("span",null,h(a.options.substring(0,2)),1),i("span",{innerHTML:C(U)(a.optionShow)},null,8,Ae)])):(t(),s("div",ze,[i("span",null,h(a.options.substring(0,2)),1),i("span",{innerHTML:J(C(U)(a.html))},null,8,Pe)]))]))),256))])):v("",!0),e.value.ques_type_code!=="F"&&e.value.ques_type_code!=="G"?(t(),s("div",je,[$.isEdit?(t(),s("div",Re,[Ge,((r=e.value)==null?void 0:r.ques_type_code)==="A"?(t(),H(A,{key:0,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":_[0]||(_[0]=a=>e.value.standard_answer[0]=a),onChange:_[1]||(_[1]=(...a)=>D(...a,e.value.ques_type_code==="A"?"01":"02"))},{default:x(()=>[(t(!0),s(b,null,M(e.value.ques_choices,(a,f)=>(t(),H(g,{value:C(ye)("A",f+1)},{default:x(()=>[F(h(e.value.ques_type_code==="A"?a.options.substring(0,1):a.options.substring(0,2)),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])):v("",!0),((d=e.value)==null?void 0:d.ques_type_code)==="B"?(t(),H(A,{key:1,modelValue:e.value.standard_answer[0],"onUpdate:modelValue":_[2]||(_[2]=a=>e.value.standard_answer[0]=a),onChange:_[3]||(_[3]=(...a)=>D(...a,e.value.ques_type_code==="A"?"01":"02"))},{default:x(()=>[(t(!0),s(b,null,M(y.judgmentList,(a,f)=>(t(),H(g,{value:y.judgmentArr[f]},{default:x(()=>[F(h(a),1)]),_:2},1032,["value"]))),256))]),_:1},8,["modelValue"])):((c=e.value)==null?void 0:c.ques_type_code)==="C"?(t(),H(X,{key:2,modelValue:e.value.standard_choices_code,"onUpdate:modelValue":_[4]||(_[4]=a=>e.value.standard_choices_code=a),onChange:_[5]||(_[5]=(...a)=>D(...a,"03"))},{default:x(()=>[(t(!0),s(b,null,M(e.value.ques_choices,a=>(t(),H(G,{value:a.code,label:a.options.substring(0,1)},null,8,["value","label"]))),256))]),_:1},8,["modelValue"])):e.value.ques_type_code==="D"?(t(),s("div",Ze,[((V=e.value.standard_answer_html)==null?void 0:V.length)>0?(t(!0),s(b,{key:0},M(e.value.standard_answer_html,(a,f)=>(t(),s("div",{class:"ques-editor-input filling-input-box",style:ve({width:`${1/e.value.standard_answer_html.length*100-.2}%`})},[p(oe,{modelValue:e.value.standard_answer_html[f],"onUpdate:modelValue":B=>e.value.standard_answer_html[f]=B},null,8,["modelValue","onUpdate:modelValue"])],4))),256)):(t(!0),s(b,{key:1},M(e.value.standard_answer,(a,f)=>(t(),s("div",{class:"ques-editor-input filling-input-box",style:ve({width:`${1/e.value.standard_answer.length*100-.2}%`})},[p(oe,{modelValue:e.value.standard_answer[f],"onUpdate:modelValue":B=>e.value.standard_answer[f]=B},null,8,["modelValue","onUpdate:modelValue"])],4))),256))])):e.value.ques_type_code==="E"?(t(),s(b,{key:4},[((k=e.value.standard_answer_html)==null?void 0:k.length)>0?(t(),s("div",Ie,[p(oe,{modelValue:e.value.standard_answer_html[0],"onUpdate:modelValue":_[6]||(_[6]=a=>e.value.standard_answer_html[0]=a)},null,8,["modelValue"])])):(t(),s("div",Ke,[p(oe,{modelValue:e.value.standard_answer[0],"onUpdate:modelValue":_[7]||(_[7]=a=>e.value.standard_answer[0]=a)},null,8,["modelValue"])]))],64)):v("",!0)])):(t(),s("p",We,[Xe,e.value.ques_type_code==="A"?(t(),s("span",Ye,h(e.value.standard_answer[0]),1)):v("",!0),e.value.ques_type_code==="B"?(t(),s("span",et,h(e.value.standard_answer[0]==="1"?"正确":"错误"),1)):v("",!0),e.value.ques_type_code==="C"?(t(),s("span",tt,h(e.value.standard_answer.join("")),1)):v("",!0),e.value.ques_type_code==="D"?(t(),s("span",st,[((S=e.value.standard_answer_html)==null?void 0:S.length)>0?(t(),s("span",nt,[(t(!0),s(b,null,M(e.value.standard_answer_html,(a,f)=>(t(),s("span",ot,[F(h(`(${f+1})`),1),i("span",{innerHTML:a},null,8,at)]))),256))])):(t(),s("span",lt,[(t(!0),s(b,null,M(e.value.standard_answer,(a,f)=>(t(),s("span",ut,[F(h(`(${f+1})`),1),i("span",{innerHTML:a},null,8,it)]))),256))]))])):v("",!0),e.value.ques_type_code==="E"?(t(),s("span",dt,[((m=e.value.standard_answer_html)==null?void 0:m.length)>0?(t(),s("span",_t,[i("span",{innerHTML:C(U)(e.value.standard_answer_html[0])},null,8,rt)])):(t(),s("span",ct,[i("span",{innerHTML:C(U)(e.value.standard_answer[0])},null,8,pt)]))])):v("",!0)]))])):v("",!0),e.value.ques_type_code==="D"?(t(),s("div",vt,[i("div",ht,[mt,p(ae,{content:C(qe)},{default:x(()=>[p(j,{class:"mt-[3px]"},{default:x(()=>[p(Z)]),_:1})]),_:1},8,["content"]),ft]),$.isEdit?(t(),H(Y,{key:0,type:"text",modelValue:e.value.d_out_of_order_group,"onUpdate:modelValue":_[8]||(_[8]=a=>e.value.d_out_of_order_group=a),class:"input-margin",placeholder:"请输入答案分组",autosize:""},null,8,["modelValue"])):(t(),s("span",yt,h(e.value.d_out_of_order_group),1))])):v("",!0),e.value.ques_type_code!=="F"?(t(),s("p",qt,[gt,F(" "+h(e.value.ques_difficulty?e.value.ques_difficulty:"无"),1)])):v("",!0),e.value.ques_type_code!=="F"&&e.value.ques_type_code!=="G"?(t(),s("div",xt,[$.isEdit?(t(),s("div",kt,[bt,i("div",wt,[p(oe,{ref:"editorRef",modelValue:e.value.standard_parse,"onUpdate:modelValue":_[9]||(_[9]=a=>e.value.standard_parse=a)},null,8,["modelValue"])])])):(t(),s("div",St,[Vt,e.value.standard_parse!==null?(t(),s("div",{key:0,innerHTML:C(U)(e.value.standard_parse)},null,8,Ct)):(t(),s("div",Nt,"无"))]))])):v("",!0),e.value.total_score!=null?(t(),s("p",$t,[Tt,i("span",null,h(e.value.total_score)+"分",1)])):v("",!0),e.value.ques_type_code==="E"||((O=e.value)==null?void 0:O.ques_type_code)==="D"&&e.value.e_mark_rule?(t(),s("div",Ot,[Mt,$.isEdit?(t(),H(Y,{key:0,type:"textarea",modelValue:e.value.e_mark_rule,"onUpdate:modelValue":_[10]||(_[10]=a=>e.value.e_mark_rule=a),placeholder:"请输入评分规则",autosize:""},null,8,["modelValue"])):(t(),s(b,{key:1},[e.value.e_mark_rule?(t(),s("div",{key:0,innerHTML:C(U)(e.value.e_mark_rule.replace(/\n/g,"<br/>"))},null,8,Lt)):v("",!0)],64))])):v("",!0),e.value.ques_type_code==="D"&&((ne=(L=e.value)==null?void 0:L.weight)==null?void 0:ne.length)>0?(t(),s("p",Et,[Ft,e.value.weight?(t(),s("span",Jt,h((ue=e.value.weight[0])==null?void 0:ue.join(":"))+"（总权重："+h((I=e.value.weight[1])==null?void 0:I.toString())+"）",1)):v("",!0)])):v("",!0),e.value.ques_type_code==="E"?(t(),s("div",Ht,[$.isEdit?(t(),s(b,{key:0},[((ie=e.value)==null?void 0:ie.ques_type_code)==="E"?(t(),s("div",Dt,[Bt,i("div",Qt,[(t(!0),s(b,null,M(e.value.ques_mark_point,(a,f)=>(t(),s("div",Ut,[i("div",null,h(f+1)+"．",1),p(Y,{type:"text",modelValue:a.point,"onUpdate:modelValue":B=>a.point=B,class:"input-margin",placeholder:"请输入评分标准",autosize:""},null,8,["modelValue","onUpdate:modelValue"]),p(re,{min:.1,modelValue:a.score,"onUpdate:modelValue":B=>a.score=B,class:"input-margin"},null,8,["modelValue","onUpdate:modelValue"]),F(" 分 "),p(j,{class:"point-icon-box point-del",onClick:B=>_e(f)},{default:x(()=>[p(le)]),_:2},1032,["onClick"])]))),256)),i("div",null,[p(j,{class:"point-icon-box",onClick:_[11]||(_[11]=a=>w())},{default:x(()=>[p(ee)]),_:1})])])])):v("",!0)],64)):(t(),s(b,{key:1},[i("p",null,[At,((de=e.value.ques_mark_point)==null?void 0:de.length)===0?(t(),s("span",zt,"无")):v("",!0)]),((E=e.value.ques_mark_point)==null?void 0:E.length)>0?(t(),s("ul",Pt,[(t(!0),s(b,null,M(e.value.ques_mark_point,(a,f)=>(t(),s("li",null,h(W[f])+" "+h(a.point)+"("+h(a.score)+"分) ",1))),256))])):v("",!0)],64))])):v("",!0),e.value.ques_type_code==="F"?(t(),s("div",jt,[(t(!0),s(b,null,M((P=e.value)==null?void 0:P.children,(a,f)=>(t(),s("div",null,[Rt,i("div",Gt,[p(te,{type:"primary"},{default:x(()=>[F(h(a.ques_type_name),1)]),_:2},1024)]),p(z,{questionDesc:e.value.children[f],"onUpdate:questionDesc":B=>e.value.children[f]=B,isEdit:$.isEdit},null,8,["questionDesc","onUpdate:questionDesc","isEdit"])]))),256))])):v("",!0),e.value.ques_type_code==="G"?(t(),s("div",Zt,[It,p(fe,{opStepList:e.value.op_step_list,quesDetail:e.value,isMark:T.value},null,8,["opStepList","quesDetail","isMark"])])):v("",!0)])}}}),Wt=me(Kt,[["__scopeId","data-v-a578a7f2"]]),Xt={width:24,height:24,body:'<path fill="currentColor" d="M7 19v-6h10v6h2V7.828L16.172 5H5v14h2ZM4 3h13l4 4v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Zm5 12v4h6v-4H9Z"/>'},Yt=Xt,es={width:24,height:24,body:'<path fill="currentColor" d="M6.414 15.89L16.556 5.748l-1.414-1.414L5 14.476v1.414h1.414Zm.829 2H3v-4.243L14.435 2.212a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414L7.243 17.89ZM3 19.89h18v2H3v-2Z"/>'},ts=es,ss={class:"zf-first-box"},ns={class:"zf-second-box"},os={key:0,class:"empty-box"},as={class:"empty-text"},ls={key:1,class:"card-box"},us={class:"nav-left"},is={class:"paper-name-box"},ds={class:"nav-list"},_s={class:"question-btn-list"},rs=["onClick"],cs={class:"details-right"},ps={class:"icon-btn-box"},vs={key:0,class:"icon-box"},hs={title:"保存"},ms={title:"编辑"},fs={title:"预览"},ys={class:"pre-next-btn"},qs={key:0,class:"edit-box"},gs={key:1,class:"edit-box"},xs=he({__name:"index",setup($){const e=we(),W=Se(),T=K(),u=K({}),D=K({}),w=K({}),_e=["一","二","三","四","五","六","七","八","九","十","十一","十二","十三","十四","十五","十六","十七","十八","十九","二十","二十一","二十二","二十三","二十四","二十五","二十六","二十七","二十八","二十九","三十"],J=K("01");Ve(()=>{if(e.query.paper_code)A(!0);else return});const y=n=>{let o=`（共${n.data.length}题`;if(n.type_code==="D"||n.type_code==="E"||n.type_code==="F"||n.type_code==="G")o+="）";else{const l=n.data[0].total_score,r=`，每题${l}分，共${l*n.data.length}分）`;o+=r}return o},_=(n,o)=>{Ne(D.value,u.value)?g(n,o):ce.confirm("当前题目未保存，是否保存？","提示",{confirmButtonText:"是",cancelButtonText:"否",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{Z().then(l=>{l&&setTimeout(()=>{g(n,o)},200)})}).catch(()=>{g(n,o)})},g=(n,o)=>{T.value=n,w.value.map(l=>{l.type===o&&l.data.forEach(r=>{r.ques_id===n&&(u.value={},u.value=Q(r),setTimeout(()=>{u.value=Q(r),z()},10))})})},A=n=>{let o={paper_id:e.query.paper_id};Me(o).then(l=>{var r,d,c;if(l.code&&l.code===200){if(w.value=l.data.data,n)w.value.length>0&&(T.value=w.value[0].data[0].ques_id,u.value=JSON.parse(JSON.stringify(w.value[0].data[0])),z());else if(T.value){const{ques_type_code:V}=u.value,k=(c=(d=(r=w.value)==null?void 0:r.filter(S=>S.type_code===V)[0])==null?void 0:d.data)==null?void 0:c.filter(S=>S.ques_id===T.value)[0];u.value=JSON.parse(JSON.stringify(k)),z()}}})},G=()=>{JSON.stringify(u.value)===JSON.stringify(D.value)?W.push("/test-paper-management/test-paper/index"):ce.confirm("当前题目未保存，是否保存？","提示",{confirmButtonText:"是",cancelButtonText:"否",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{Z().then(n=>{n&&setTimeout(()=>{W.push("/test-paper-management/test-paper/index")},200)})}).catch(()=>{W.push("/test-paper-management/test-paper/index")})},X=n=>{n==="01"?Z().then(o=>{o&&(J.value=n)}):J.value=n},Z=()=>new Promise(n=>{let{ques_type_code:o,ques_score_list:l,ques_mark_point:r,weight:d}=JSON.parse(JSON.stringify(u.value));if(o==="E")if(r.length>0){let c=[];if(r.map((V,k)=>{V.score>Number(l[0])&&c.push(k+1)}),c.length>0){const V=`评分标准${c.join("、")}超过题目总分（${Number(l[0])}分）`;R.warning(V)}else j(n)}else j(n);else if(o==="G"){let{op_step_list:c,op_score_rule:V,showStepOrderIdList:k,stepNoteTextList:S}=JSON.parse(JSON.stringify(u.value));if(S.some(m=>m!=null)){R.warning("请按要求填写评分步骤权重设置");return}if(k.length>0){let m=[];for(const O of k)m.push(O.value);R.warning(`请补充评分步骤 ${m.sort((O,L)=>O-L).join("、")} 的权重设置`);return}j(n)}else j(n)}),j=n=>{const{ques_type_code:o,children:l}=u.value,r=ae(u.value);if(o==="F")Promise.all([Y(r),re(l)]).then(d=>{R.success(d[1]),D.value=JSON.parse(JSON.stringify(u.value)),n(!0),A()});else{if(o==="G"){const d=[],c={1:[],2:[],3:[]},V=u.value.op_total_score_gene;for(const[k,S]of u.value.op_step_list.entries()){const m=u.value.op_score_rule[k];m!=null&&c[S.logic].push(m);for(const O of S.op_step_group)O.is_score=S.is_score,d.push(O)}r.op_step_list=d,r.op_score_rule_dict=c,r.op_total_score_gene=V}pe(r).then(d=>{d.code&&d.code===200?(R.success(d.msg),D.value=JSON.parse(JSON.stringify(u.value)),n(!0),A()):R.warning(d.msg)})}},ae=n=>{let{ques_id:o,ques_order:l,ques_code:r,ques_desc:d,ques_difficulty:c,standard_answer:V,standard_answer_html:k,standard_parse:S,e_mark_rule:m,ques_mark_point:O,ques_choices:L,weight:ne,total_score:ue,standard_choices_code:I,knowledge_group_id:ie,d_out_of_order_group:de}=JSON.parse(JSON.stringify(n)),E={ques_id:o,ques_order:l,ques_code:r,ques_desc:d,ques_difficulty:c,standard_answer:V,standard_answer_html:k,standard_parse:S,e_mark_rule:m,ques_mark_point:O,weight:ne,total_score:ue,knowledge_group_id:ie,d_out_of_order_group:de};return E.ques_desc.html&&(E.ques_desc.html=se(E.ques_desc.html)),I&&I.length>0&&(E.standard_choices_code=I),E.paper_id=e.query.paper_id,L.length>0&&L.forEach(P=>{P.optionShow=se(P.optionShow),P.html=P.options.substring(0,2)+P.optionShow,delete P.optionShow}),E.ques_choices=L,E.standard_answer||(E.standard_answer=[]),E},Y=n=>new Promise(o=>{pe(n).then(l=>{l.code&&l.code===200&&o()})}),re=n=>{let o=1;return new Promise(l=>{n.forEach(r=>{const d=ae(r);pe(d).then(c=>{c.code&&c.code===200&&(o++,o===n.length&&l(c.msg))})})})},le=n=>{JSON.stringify(u.value)===JSON.stringify(D.value)?n==="01"?ee():n==="02"&&te():ce.confirm("当前题目未保存，是否保存？","提示",{confirmButtonText:"是",cancelButtonText:"否",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{Z().then(o=>{o&&setTimeout(()=>{n==="01"?ee():n==="02"&&te()},200)})}).catch(()=>{n==="01"?ee():n==="02"&&te()})},ee=()=>{let n=JSON.parse(JSON.stringify(T.value)),o=w.value[0].data[0];n===o.ques_id?R.warning("已是第一题！"):w.value.map((l,r)=>{if(l.type===u.value.ques_type_name)if(n!==l.data[0].ques_id)l.data.map((d,c)=>{d.ques_id===n&&(T.value=l.data[c-1].ques_id,u.value=Q(l.data[c-1]),setTimeout(()=>{u.value=Q(l.data[c-1]),z()},10))});else{let d=w.value[r-1].data;T.value=d[d.length-1].ques_id,u.value=Q(d[d.length-1]),setTimeout(()=>{u.value=Q(d[d.length-1]),z()},10)}})},te=()=>{let n=JSON.parse(JSON.stringify(T.value)),o=w.value[w.value.length-1].data;n===o[o.length-1].ques_id?R.warning("已是最后一题！"):w.value.map((l,r)=>{if(l.type===u.value.ques_type_name)if(n!==l.data[l.data.length-1].ques_id)l.data.map((d,c)=>{n===d.ques_id&&(T.value=l.data[c+1].ques_id,u.value=Q(l.data[c+1]),setTimeout(()=>{u.value=Q(l.data[c+1]),z()},10))});else{let d=w.value[r+1].data;T.value=d[0].ques_id,u.value=Q(d[0]),setTimeout(()=>{u.value=Q(d[0]),z()},10)}})},z=()=>{u.value.ques_choices&&u.value.ques_choices.length>0&&u.value.ques_choices.forEach(n=>{n.optionShow=JSON.parse(JSON.stringify(n.html?n.html.substring(2):n.options.substring(2))),n.optionShow=U(n.optionShow)}),u.value.ques_type_name==="组合题"&&u.value.children&&u.value.children.length>0&&u.value.children.forEach(n=>{n.ques_desc.html&&(n.ques_desc.html=U(n.ques_desc.html)),n.ques_choices.forEach(o=>{o.optionShow=JSON.parse(JSON.stringify(o.html?o.html.substring(2):o.options.substring(2))),o.optionShow=U(o.optionShow)})}),D.value=JSON.parse(JSON.stringify(u.value))},se=n=>n.replace(new RegExp($e,"g"),"");return(n,o)=>{const l=q("IconifyIconOffline"),r=q("el-tooltip"),d=q("el-empty"),c=q("el-scrollbar"),V=q("Auth"),k=q("el-button"),S=q("el-card");return t(),s("div",ss,[i("div",ns,[p(S,null,{default:x(()=>[Object.keys(u.value).length===0?(t(),s("div",os,[i("div",{class:"empty-back-btn",onClick:G},[p(r,{content:"返回"},{default:x(()=>[p(l,{icon:C(Oe)},null,8,["icon"])]),_:1})]),i("div",as,[p(d,{description:"暂无试题"})])])):(t(),s("div",ls,[i("div",us,[i("div",is,h(C(e).query.paper_name),1),p(c,{height:"76vh",always:""},{default:x(()=>[(t(!0),s(b,null,M(w.value,(m,O)=>(t(),s("div",ds,[i("p",null,h(_e[O])+"、"+h(m.type)+" "+h(y(m)),1),i("div",_s,[(t(!0),s(b,null,M(m.data,L=>(t(),s("div",{class:Ce(["question-btn",L.ques_id===T.value?"question-btn-checked":""]),onClick:ne=>_(L.ques_id,m.type)},h(L.ques_order),11,rs))),256))])]))),256))]),_:1})]),i("div",cs,[i("div",ps,[Object.keys(u.value).length!==0&&C(e).query.lock_state==="1"?(t(),s("div",vs,[i("span",hs,[J.value==="02"||J.value==="03"?(t(),H(l,{key:0,class:"blue-icon",icon:C(Yt),title:"保存",onClick:o[0]||(o[0]=m=>X("01"))},null,8,["icon"])):v("",!0)]),p(V,{value:"paper-detail/edit"},{default:x(()=>[i("span",ms,[J.value==="01"||J.value==="03"?(t(),H(l,{key:0,class:"blue-icon",icon:C(ts),onClick:o[1]||(o[1]=m=>X("02"))},null,8,["icon"])):v("",!0)])]),_:1}),i("span",fs,[J.value==="02"?(t(),H(l,{key:0,class:"blue-icon",icon:"ri:search-line",onClick:o[2]||(o[2]=m=>X("03"))})):v("",!0)])])):v("",!0),i("div",ys,[p(k,{size:"small",type:"primary",onClick:o[3]||(o[3]=m=>le("01"))},{default:x(()=>[F("上一题 ")]),_:1}),p(k,{size:"small",type:"primary",onClick:o[4]||(o[4]=m=>le("02"))},{default:x(()=>[F("下一题 ")]),_:1}),p(k,{size:"small",type:"primary",onClick:G},{default:x(()=>[F("返回 ")]),_:1})])]),p(c,{"max-height":"93%",always:""},{default:x(()=>[J.value==="01"||J.value==="03"?(t(),s("div",qs,[p(Wt,{questionDesc:u.value,"onUpdate:questionDesc":o[5]||(o[5]=m=>u.value=m)},null,8,["questionDesc"])])):(t(),s("div",gs,[p(Te,{questionDesc:u.value,"onUpdate:questionDesc":o[6]||(o[6]=m=>u.value=m)},null,8,["questionDesc"])]))]),_:1})])]))]),_:1})])])}}}),Ms=me(xs,[["__scopeId","data-v-53d9c51a"]]);export{Ms as default};
