import{aQ as r,aR as t}from"./index-B63pSD2p.js";const u=e=>r.request("post",t("/v1/mark_rule/get_mark_rule"),{data:e}),_=e=>r.request("post",t("/v1/ques_type/get_ques_type"),{data:e}),s=e=>r.request("post",t("/v1/mark_rule/create_mark_rule"),{data:e}),l=e=>r.request("get",t("/v1/mark_rule/get_mark_rule_detail"),{params:e}),m=e=>r.request("post",t("/v1/mark_rule/delete_mark_rule"),{data:e}),k=e=>r.request("post",t("/v1/mark_rule/update_mark_rule"),{data:e});export{l as a,u as b,s as c,m as d,_ as g,k as u};
