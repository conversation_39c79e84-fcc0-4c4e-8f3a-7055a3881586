import{aQ as U,aR as E,d as L,l as e,P as M,r as d,o as Q,g as j,h as g,e as O,b as m,f as w,u as A,C as z,T as D}from"./index-B63pSD2p.js";const I=x=>U.request("post",E("/v1/manual_group/get_manual_group"),{data:x}),K={class:"zf-dialog-first-box"},X={class:"zf-dialog-second-box"},Y={class:"footer-btn"},$=L({__name:"marking-group-select",emits:["getSelectedGroup"],setup(x,{expose:N,emit:P}){const R=P,W=e("选择阅卷小组"),u=e(!1);e("01");const C=e({}),F=M({column:3,labelWidth:"108px",itemWidth:"250px",rules:{},fields:[{label:"阅卷小组名称",prop:"manual_group_name",type:"input",defaultValue:"",placeholder:"请输入阅卷小组名称",clearable:!0}]}),o=e({field:[{prop:"manual_group_name",label:"阅卷小组名称",minWidth:"120px"},{prop:"quality_name",label:"质检人员",minWidth:"160px"},{prop:"arbitrator_name",label:"仲裁人员",minWidth:"160px"},{prop:"expert_name",label:"阅卷人员",minWidth:"160px"}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let p=e([]);const f=e(null),_=e(null),y=e({}),i=e([]),v=e([]),G=(a,t)=>{u.value=!0,y.value=a,t&&(i.value=JSON.parse(JSON.stringify(t))),z(()=>{c()})},b=()=>{u.value=!1,f.value.resetFieldsFn(),_.value.onClearSelection(),p.value=[],o.value.pageOptions.total=0,o.value.pageOptions.currentPage!==1&&(o.value.pageOptions.currentPage=1)},c=()=>{const{currentPage:a,pageSize:t}=o.value.pageOptions,h={current_page:a,page_size:t},S=f.value.getAllCardData();let l=JSON.parse(JSON.stringify(y.value));l.is_create_manual_task=!0,l=Object.assign(h,S,l),I(l).then(n=>{var r;if(n.code&&n.code===200){if(p.value=n.data.data,o.value.pageOptions.total=n.data.total,((r=i.value)==null?void 0:r.length)>0){const V=p.value.filter(s=>{var k;return(k=i.value)==null?void 0:k.includes(s.manual_group_id)});z(()=>{V.forEach(s=>{_.value.onToggleRowSelection(s,!0)})});const H=V.map(s=>s.manual_group_id);i.value=i.value.filter(s=>!H.includes(s))}}else D.warning(n.msg)})},q=a=>{v.value=a},J=()=>{v.value.length>0?(R("getSelectedGroup",v.value),b()):D("选择不能为空！")},T=a=>{o.value.pageOptions.pageSize=a,c()},B=a=>{o.value.pageOptions.currentPage=a,c()};return N({openDialog:G}),(a,t)=>{const h=d("form-component"),S=d("table-component"),l=d("el-button"),n=d("el-dialog");return Q(),j(n,{modelValue:u.value,"onUpdate:modelValue":t[1]||(t[1]=r=>u.value=r),title:W.value,width:"1000px","before-close":b,"close-on-click-modal":!1,draggable:"","append-to-body":"","align-center":""},{footer:g(()=>[O("div",Y,[m(l,{type:"primary",onClick:J},{default:g(()=>[w("确定")]),_:1}),m(l,{onClick:b},{default:g(()=>[w("取消")]),_:1})])]),default:g(()=>[O("div",K,[O("div",X,[m(h,{ref_key:"formRef",ref:f,modelValue:C.value,"onUpdate:modelValue":t[0]||(t[0]=r=>C.value=r),"form-options":F,"is-query-btn":!0,onQueryDataFn:c},null,8,["modelValue","form-options"]),m(S,{ref_key:"tableRef",ref:_,"table-options":o.value,"table-data":A(p),"min-height":"460px",onOnHandleSizeChange:T,onOnHandleCurrentChange:B,onOnHandleSelectionChange:q},null,8,["table-options","table-data"])])])]),_:1},8,["modelValue","title"])}}});export{$ as _};
