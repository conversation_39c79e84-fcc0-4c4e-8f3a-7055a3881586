var ye=Object.defineProperty,De=Object.defineProperties;var we=Object.getOwnPropertyDescriptors;var ce=Object.getOwnPropertySymbols;var ke=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable;var de=(v,i,l)=>i in v?ye(v,i,{enumerable:!0,configurable:!0,writable:!0,value:l}):v[i]=l,m=(v,i)=>{for(var l in i||(i={}))ke.call(i,l)&&de(v,l,i[l]);if(ce)for(var l of ce(i))Le.call(i,l)&&de(v,l,i[l]);return v},x=(v,i)=>De(v,we(i));import Se from"./homepage-tabs-KBTJlgKv.js";import{g as ze,h as Ce,i as Me,j as We,k as Ae,l as Te,m as Be}from"./index-aLSsAUpX.js";import{S as ee}from"./scoreRelateLineChart-DOiNxsyg.js";import Ee from"./basic-monitor-jamMEtXh.js";import ve from"./barChart-BwsIMmQR.js";import{d as Ie,l as t,n as Oe,a1 as pe,a2 as Ze,C as ge,T as He,P as Je,r as w,j as Re,o as s,c as y,ae as $e,e as r,b as h,w as Z,u as ae,aT as te,q as je,h as T,F as qe,p as Fe,g as f,ac as Pe,ad as Ge,_ as Ve}from"./index-B63pSD2p.js";import"./useTag-CrO3NxXA.js";import"./index-C9GYnvBh.js";const H=v=>(Pe("data-v-c00c542f"),v=v(),Ge(),v),Ue={class:"dashboard"},Ne={class:"tabs-cont"},Qe={key:0,class:"h-full main-monitor"},Xe={class:"top dark:!bg-black eye-box"},Ye={class:"echart-title"},Ke=H(()=>r("div",null,"各评卷员评分情况",-1)),ea={class:"b-right"},aa={class:"br-top dark:!bg-black eye-box echart-card"},ta={class:"echart-title",style:{"margin-bottom":"12px"}},la=H(()=>r("div",null,"阅卷工作量",-1)),oa={class:"echart-wrap"},ia={class:"br-bottom dark:!bg-black eye-box echart-card"},na={class:"echart-title"},sa=H(()=>r("div",null,"问题卷情况",-1)),ua={class:"echart-wrap"},ra={class:"fullscreen-chart-wrapper"},ca=H(()=>r("template",null,null,-1)),da={class:"echart-wrap"},va=H(()=>r("template",null,null,-1)),pa=Ie({__name:"index",setup(v){const i=t({task_type:1,round_count:"1",subject_id:"test"});Oe(()=>{U(),me(oe),pe.on("dataStatistics:reflushEcharts",e=>{i.value=e,e.subject_id&&fe()}),window.addEventListener("resize",U)}),Ze(()=>{he(oe),pe.off("dataStatistics:reflushEcharts"),window.addEventListener("resize",U)});const l=t([{name:"阅卷总量(人题)",value:"-",image:"yyjl"},{name:"已阅卷量",value:"-",image:"yxl"},{name:"平均速度(份/时)",value:"-",image:"wtjl"},{name:"单题最长评分时长(秒)",value:"-",image:"zdsd"},{name:"单体最短评分时长(秒))",value:"-",image:"zxsd"},{name:"平均分",value:"-",image:"pjsd"}]),F=t(!1);function fe(){F.value=!0,l.value[0].value="-",l.value[1].value="-",l.value[2].value="-",l.value[3].value="-",l.value[4].value="-",l.value[5].value="-",ze(m({},i.value)).then(e=>{l.value[0].value=e.totalCount,l.value[1].value=e.reviewedCound,l.value[2].value=e.averageSpeed,l.value[3].value=e.maxSpeed,l.value[4].value=e.minSpeed,l.value[5].value=e.averageScore}).finally(()=>{F.value=!1}),P.value=!0,B.value=[],Ce(x(m({},i.value),{page_size:-1,round_state_list:[]})).then(e=>{var a,o;B.value=(o=(a=e.data)==null?void 0:a.data)!=null?o:[]}).finally(()=>{P.value=!1}),L.value=!0,d.options.xAxis.data=d.xData,z.value=d.xData,I.value=d.options,C.value=d.series,Me(m({},i.value)).then(e=>{var c,u;const a=(c=e.data)!=null?c:[],o=[];a.forEach(p=>{const n=new Date(p),A=("0"+(n.getMonth()+1)).slice(-2),K=("0"+n.getDate()).slice(-2);o.push({label:`${A}月${K}日`,value:p,time:n.getTime()})}),se.value=o,o.find(p=>p.value===S.value)||(S.value=(u=o[0])==null?void 0:u.value),le()}).catch(()=>{L.value=!1}),X.value=!0,W.value=M.xData,q.value=M.series,We(m({},i.value)).then(e=>{var c,u,p,n;const a=(u=(c=e.data)==null?void 0:c.x_data)!=null?u:[],o=(n=(p=e.data)==null?void 0:p.y_data)!=null?n:[];W.value=a.map(A=>xe.get(A)),q.value[0].data=o}).finally(()=>{X.value=!1})}function le(){L.value=!0,d.options.xAxis.data=d.xData,z.value=[],I.value=d.options,C.value=d.series,S.value?Ae(x(m({},i.value),{mark_time:S.value,user_id:void 0})).then(e=>{var a,o,c,u,p;e.code===200&&(z.value=(c=(o=(a=e.data)==null?void 0:a.x_data)==null?void 0:o.map(n=>`时间段：${n.replace("-",":00-")}:00`))!=null?c:[],I.value.xAxis={axisLabel:{formatter:n=>n.replace(/时间段：/,"").replace(/:00/g,"")},name:"时",nameGap:3,data:z.value},C.value=[x(m({},d.series[0]),{data:[]})],(p=(u=e.data)==null?void 0:u.y_data)==null||p.forEach(n=>{C.value[0].data.push(n||0)}))}).finally(()=>{L.value=!1}):L.value=!1}const P=t(!1),B=t([]),J=t({field:[{prop:"name",label:"评阅员",minWidth:"100px"},{prop:"reviewed_count",label:"已阅量",minWidth:"85px",sortable:!0},{prop:"average_speed1",label:"平均速度(份/时)",minWidth:"90px",sortable:!0},{prop:"average_speed2",label:"平均评分时间(秒/份)",minWidth:"110px",sortable:!0},{prop:"max_speed",label:"最长评分时间(秒)",minWidth:"110px",sortable:!0},{prop:"min_speed",label:"最短评分时间(秒)",minWidth:"110px",sortable:!0},{prop:"",label:"操作",type:"template",fixed:!1,minWidth:"80px",showOverflowTooltip:!1,templateGroup:[{title:()=>"查看",clickBtn(e){_e(e)}}]}],styleOptions:{minHeight:"100",emptyImageSize:80,isShowSort:!0},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}});let oe=null;const E=t(),G=t(1),V=t();function U(){V.value&&(G.value=V.value.offsetWidth/1698)}const me=(e=null)=>{e=new ResizeObserver(a=>{a.forEach(o=>{E.value&&(J.value.styleOptions.minHeight=(E.value.offsetHeight-60)/G.value+"px")})}),E.value&&e.observe(E.value)},he=e=>{e&&(e.disconnect(),e=null)},N=t(!1),Q=t(!1),ie=t("pingyuewendingxingMonitor"),R=t(""),$=t(!1),_={xData:["08:00","09:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00"],options:{xAxis:{axisLabel:{show:!1},axisTick:{show:!1},data:[]}},series:[{name:"评阅员平均分",data:[0,0,0,0,0,0,0,0,0,0,0]},{name:"小组平均分",data:[0,0,0,0,0,0,0,0,0,0,0]}]};_.options.xAxis.data=_.xData;const k=t(_.xData),j=t(_.options),D=t(_.series);function ne(){N.value=!1,Q.value=!1}function _e(e){ie.value="pingyuewendingxingMonitor",R.value=e.user_id,N.value=!0,j.value.xAxis={axisLabel:{show:!1},axisTick:{show:!1},data:[]},k.value=_.xData,D.value=_.series,be(),ge(()=>{Q.value=!0})}function be(){if(R.value)switch($.value=!0,ie.value){case"pingfenyizhixingMonitor":Be(x(m({},i.value),{user_id:R.value})).then(e=>{var a,o,c;e.code===200&&(k.value=(o=(a=e.data)==null?void 0:a.map(u=>u.date))!=null?o:[],j.value.xAxis={axisLabel:{show:!1},axisTick:{show:!1},data:k.value},D.value=[x(m({},_.series[0]),{data:[]}),x(m({},_.series[1]),{data:[]})],(c=e.data)==null||c.forEach(u=>{D.value[0].data.push(u.person_average_value||0),D.value[1].data.push(u.group_average_value||0)}))}).finally(()=>{$.value=!1});break;case"pingyuewendingxingMonitor":Te(x(m({},i.value),{user_id:R.value})).then(e=>{var a,o,c,u;e.code===200&&(k.value=(o=(a=e.data)==null?void 0:a.x_data)!=null?o:[],j.value.xAxis={data:k.value},D.value=[{name:"当前分数",data:[]},{name:"上次分数",data:[]},{name:"差异数据",data:[]}],(c=e.data)!=null&&c.legend&&(D.value=(u=e.data)==null?void 0:u.legend.map((p,n)=>({name:p,data:e.data[`y${n+1}_data`].length!==0?e.data[`y${n+1}_data`]:[0,0,0,0,0]}))))}).finally(()=>{$.value=!1});break}else He.warning("请选择评阅员！")}const L=t(!1),se=t([]),S=t(""),d={xData:["8","9","10","11","12","13","14","15","16"],options:{tooltip:{valueFormatter:e=>e+"份"},grid:{top:10},legend:{show:!1},xAxis:{axisLabel:{formatter:e=>e},data:[],name:"时",nameGap:3},yAxis:{minInterval:1,axisLabel:{formatter:function(e){return e+"份"}}}},series:[{name:"阅卷工作量",label:{show:!0,formatter:"{c}份",color:"#5087ec"},color:"#5087ec",smooth:!1,data:[0,0,0,0,0,0,0,0,0]}]};d.options.xAxis.data=d.xData;const z=t(d.xData),I=t(d.options),C=t(d.series),xe=new Map([[1,"图像错误"],[2,"作答位置错误"],[3,"作答合并"],[4,"其他"]]),X=t(!1),M={xData:["图像错误","作答位置错误","作答合并","其他"],yAxis:{minInterval:1,axisLabel:{formatter:function(e){return e+"份"},fontSize:10},valueFormatter:e=>e+"份"},series:[{name:"问题卷",data:[0,0,0,0],color:"#5894ff",label:{show:!0,position:"top",color:"#5894ff",formatter:"{c}份"}}]},W=t(M.xData),q=t(M.series),g=Je({visible:!1,type:"",title:""}),ue=()=>{g.type="",g.visible=!1};function Y(e){e=="wentiJuanMonitor"?g.title="问题卷情况":e=="gongzuoliangMonitor"?g.title="阅卷工作量":e=="pingfenProcess"&&(g.title="各评卷员评分情况"),g.visible=!0,ge(()=>{g.type=e})}return(e,a)=>{var re;const o=w("el-button"),c=w("table-component"),u=w("el-option"),p=w("el-select"),n=w("el-empty"),A=w("DialogComponent"),K=w("el-card"),O=Re("loading");return s(),y("div",Ue,[$e(e.$slots,"tab",{},()=>[r("div",Ne,[h(Se,{maxUserRole:"5"})])],!0),i.value.subject_id?(s(),y("div",Qe,[Z((s(),y("div",Xe,[h(Ee,{basicData:l.value},null,8,["basicData"])])),[[O,F.value]]),r("div",{class:"bottom",ref_key:"bottomRef",ref:V},[r("div",{class:"b-left dark:!bg-black eye-box echart-card",ref_key:"tableWrapRef",ref:E},[r("div",Ye,[Ke,r("div",null,[h(o,{class:"fullscreen-btn",title:"全屏",icon:ae(te),circle:"",text:"",onClick:a[0]||(a[0]=b=>Y("pingfenProcess"))},null,8,["icon"])])]),Z((s(),y("div",{class:"echart-wrap",style:je({zoom:G.value})},[h(c,{minHeight:(re=J.value.styleOptions)==null?void 0:re.minHeight,"table-options":J.value,"table-data":B.value},null,8,["minHeight","table-options","table-data"])],4)),[[O,P.value]])],512),r("div",ea,[r("div",aa,[r("div",ta,[la,r("div",null,[h(p,{modelValue:S.value,"onUpdate:modelValue":a[1]||(a[1]=b=>S.value=b),placeholder:"请选择",style:{width:"100px"},"popper-class":"monitor-select",onChange:le},{default:T(()=>[(s(!0),y(qe,null,Fe(se.value,b=>(s(),f(u,{label:b.label,value:b.value,key:b.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),h(o,{class:"fullscreen-btn",title:"全屏",icon:ae(te),circle:"",text:"",onClick:a[2]||(a[2]=b=>Y("gongzuoliangMonitor"))},null,8,["icon"])])]),Z((s(),y("div",oa,[z.value.length!==0?(s(),f(ee,{key:0,options:I.value,series:C.value},null,8,["options","series"])):(s(),f(n,{key:1,description:"暂无数据","image-size":80}))])),[[O,L.value]])]),r("div",ia,[r("div",na,[sa,r("div",null,[h(o,{class:"fullscreen-btn",title:"全屏",icon:ae(te),circle:"",text:"",onClick:a[3]||(a[3]=b=>Y("wentiJuanMonitor"))},null,8,["icon"])])]),Z((s(),y("div",ua,[W.value.length!==0?(s(),f(ve,{key:0,xData:W.value,barWidth:"10%",yAxis:M.yAxis,series:q.value,showLegend:!1},null,8,["xData","yAxis","series"])):(s(),f(n,{key:1,description:"暂无数据","image-size":80}))])),[[O,X.value]])])])],512),h(A,{isShowDialog:g.visible,onCloseDialog:ue,beforeClose:ue,title:g.title,fullscreen:!0,class:"rootDialogClass"},{content:T(()=>[r("div",ra,[g.type==="wentiJuanMonitor"&&W.value.length!==0?(s(),f(ve,{key:0,xData:W.value,barWidth:"10%",yAxis:M.yAxis,series:q.value,showLegend:!1},null,8,["xData","yAxis","series"])):g.type==="gongzuoliangMonitor"&&z.value.length!==0?(s(),f(ee,{key:1,options:I.value,series:C.value},null,8,["options","series"])):g.type==="pingfenProcess"&&B.value.length!==0?(s(),f(c,{key:2,minHeight:"100%","table-options":J.value,"table-data":B.value},null,8,["table-options","table-data"])):(s(),f(n,{key:3,description:"暂无数据"}))])]),footer:T(()=>[ca]),_:1},8,["isShowDialog","title"]),h(A,{isShowDialog:N.value,onCloseDialog:ne,beforeClose:ne,title:"评阅稳定性",width:"800",class:"rootDialogClass"},{content:T(()=>[Z((s(),y("div",da,[Q.value&&k.value.length!==0?(s(),f(ee,{key:0,options:j.value,series:D.value},null,8,["options","series"])):(s(),f(n,{key:1,description:"暂无数据","image-size":80}))])),[[O,$.value]])]),footer:T(()=>[va]),_:1},8,["isShowDialog"])])):(s(),f(K,{key:1,class:"noData"},{default:T(()=>[h(n,{description:"暂无数据"})]),_:1}))])}}}),wa=Ve(pa,[["__scopeId","data-v-c00c542f"]]);export{wa as default};
