var S=Object.defineProperty;var b=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var v=(a,o,e)=>o in a?S(a,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[o]=e,g=(a,o)=>{for(var e in o||(o={}))V.call(o,e)&&v(a,e,o[e]);if(b)for(var e of b(o))B.call(o,e)&&v(a,e,o[e]);return a};var y=(a,o,e)=>new Promise((i,r)=>{var d=s=>{try{l(e.next(s))}catch(n){r(n)}},c=s=>{try{l(e.throw(s))}catch(n){r(n)}},l=s=>s.done?i(s.value):Promise.resolve(s.value).then(d,c);l((e=e.apply(a,o)).next())});import{c as E}from"./base-DyvdloLK.js";import{d as F,n as I,ao as k,P as U,l as C,B as N,r as _,o as P,g as q,h as u,e as A,b as h,f as x,T as D,_ as M}from"./index-B63pSD2p.js";const T={class:"form_wrapper"},Z=F({__name:"importExamineAnswer",props:{isShowDialog:{type:Boolean,required:!0,default:!1},title:{type:String,default:""},ZIndex:{required:!1,type:Number,default:2001},getParams:{type:Function,default:()=>{}}},emits:["update:isShowDialog","closeDialog"],setup(a,{emit:o}){const e=a,i=o;I(()=>{}),k(()=>{});const r=U({visible:!1}),d=()=>{r.visible=!1,i("update:isShowDialog",!1),i("closeDialog")},c=()=>y(this,null,function*(){var m;if(!(yield l==null?void 0:l.value.IsFormValid()))return;let w=((m=l.value)==null?void 0:m.formValue)||{},f="/v1/transfer/upload-folder",p={folder_path:w.folder_path};if(typeof e.getParams=="function"){let t=e.getParams();p=g(g({},p),t)}i("uploadBegin"),E(p,f).then(t=>{t&&t.code&&t.code===200?(D.success(t.msg),i("uploadSuccess"),d()):(D.warning(t.msg||"上传失败，后端返回null"),i("uploadError"))}).catch(t=>{D.error((t==null?void 0:t.msg)||"上传失败"),i("uploadError")})}),l=C(null),s=C([{label:"文件夹路径",type:"input",placeholder:"请输入文件夹路径",required:!0,prop:"folder_path",rules:[{pattern:/^[a-zA-Z]:(\\[^\\/:*?"<>|]+)*\\?$/,message:"请输入正确的文件夹路径",trigger:"blur"}]}]);return N(()=>e.isShowDialog,n=>{r.visible=n},{deep:!0,immediate:!0}),(n,w)=>{const f=_("DynamicFormComponent"),p=_("el-button"),m=_("DialogComponent");return P(),q(m,{isShowDialog:r.visible,width:"650px",onCloseDialog:d,onSure:c,beforeClose:d,title:a.title,fullscreen:!1},{content:u(()=>[A("div",T,[h(f,{list:s.value,ref_key:"formFilterRef",ref:l},null,8,["list"])])]),footer:u(()=>[h(p,{onClick:d,disabled:n.examineeUploading},{default:u(()=>[x("取消")]),_:1},8,["disabled"]),h(p,{type:"primary",onClick:c,loading:n.examineeUploading},{default:u(()=>[x("确认")]),_:1},8,["loading"])]),_:1},8,["isShowDialog","title"])}}}),G=M(Z,[["__scopeId","data-v-1d6d5312"]]);export{G as default};
