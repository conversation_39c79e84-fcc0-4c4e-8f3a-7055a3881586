import{d as H,l as c,B as Q,r as n,o as w,g as F,h as t,e as r,b as s,f as N,y as W,t as M,c as X,F as Y,p as Z,ip as ee,T as v,_ as le}from"./index-B63pSD2p.js";import{a as oe,u as ae}from"./roles-management-DlteFevS.js";import{i as te}from"./user-management-B1vGxPiG.js";const ne={class:"zf-dialog-first-box"},se={class:"zf-dialog-second-box"},ue={class:"tree-show-box"},ce={class:"node-box"},de={class:"node-text"},ie={class:"footer-btn"},re=H({__name:"permission-config",emits:["queryData"],setup(_e,{expose:B,emit:fe}){const O=c("功能权限配置"),h=c(!1),f=c(null),k=c(""),U={label:"module_name",children:"children"},_=c([]),C=c([]),A=[{label:"一级",value:1},{label:"二级",value:2},{label:"三级",value:3}],D=c(1),p=c({}),g=c("01");Q(k,l=>{f.value.filter(l)});const E=(l,e)=>{h.value=!0,g.value=l,p.value=e,g.value==="01"?T():g.value==="02"&&L()},b=()=>{var e;h.value=!1,C.value=[],k.value="",f.value.setCheckedNodes([]);const l=(e=f.value.store)==null?void 0:e.nodesMap;if(l)for(let o in l)l[o].expanded=!1;D.value=1},T=()=>{let l={user_id:p.value.user_id};ee(l).then(e=>{var o;e.code&&e.code===200?((o=e.data)==null?void 0:o.length)>0?(_.value=[{module_flag:"checkAll",module_name:"全选",children:e.data}],R()):_.value=[]:v.warning(e.msg)})},L=()=>{let l={role_id_list:[p.value.role_id]};oe(l).then(e=>{e.code&&e.code===200&&(_.value=[{module_flag:"checkAll",module_name:"全选",children:e.data}],R())})},R=()=>{var l;if(((l=_.value)==null?void 0:l.length)>0){let e=[];_.value[0].children.map(o=>{var d,i;o.children&&o.children.length>0?o.children.map(a=>{var m,x;a.show===!0&&((m=a.func_point)==null?void 0:m.length)===0&&e.push(a.module_flag),((x=a.func_point)==null?void 0:x.length)>0&&(a.children=a.func_point,a.func_point.map(y=>{y.show===!0&&e.push(y.module_flag)}))}):(o.show===!0&&((d=o.func_point)==null?void 0:d.length)===0&&e.push(o.module_flag),((i=o.func_point)==null?void 0:i.length)>0&&(o.children=o.func_point,o.func_point.map(a=>{a.show===!0&&e.push(a.module_flag)})))}),C.value=e}},S=(l,e)=>l?e.module_name.includes(l):!0,z=l=>{var e,o;if(((e=_.value)==null?void 0:e.length)>0){let d=function(a){a.expanded=a.level<=l,a.childNodes.length!==0&&a.childNodes.forEach(m=>{d(m)})};const i=(o=f.value)==null?void 0:o.root;i&&d(i)}},J=()=>{let l=JSON.parse(JSON.stringify(f.value.getCheckedKeys())),e=l.indexOf("checkAll");e!==-1&&l.splice(e,1);let o={module_flag:l};g.value==="01"?(o.user_id=p.value.user_id,P(o)):g.value==="02"&&(o.role_id=p.value.role_id,q(o))},P=l=>{te(l).then(e=>{e.code&&e.code===200?(v.success(e.msg),b()):v.warning(e.msg)})},q=l=>{ae(l).then(e=>{e.code&&e.code===200?(v.success(e.msg),b()):v.warning(e.msg)})};return B({openDialog:E}),(l,e)=>{const o=n("el-input"),d=n("el-form-item"),i=n("el-form"),a=n("IconifyIconOffline"),m=n("DocumentRemove"),x=n("el-icon"),y=n("el-tree"),K=n("el-scrollbar"),$=n("el-radio"),j=n("el-radio-group"),I=n("el-button"),G=n("el-dialog");return w(),F(G,{modelValue:h.value,"onUpdate:modelValue":e[2]||(e[2]=u=>h.value=u),title:O.value,width:"440px","close-on-click-modal":!1,"before-close":b,draggable:""},{footer:t(()=>[r("div",ie,[s(I,{onClick:b},{default:t(()=>[N("取消")]),_:1}),s(I,{type:"primary",onClick:J},{default:t(()=>[N("确定")]),_:1})])]),default:t(()=>[r("div",ne,[r("div",se,[r("div",ue,[s(i,null,{default:t(()=>[s(d,{label:"搜索内容"},{default:t(()=>[s(o,{placeholder:"请输入内容搜索",modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=u=>k.value=u),style:{"margin-bottom":"5px"},clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),s(K,{class:"tree-check-box",always:""},{default:t(()=>[s(y,{ref_key:"treeRef",ref:f,data:_.value,props:U,"default-checked-keys":C.value,"default-expanded-keys":["checkAll"],"show-checkbox":"","node-key":"module_flag","filter-node-method":S},{default:t(({node:u,data:V})=>[r("div",ce,[r("div",de,[V.flag===1?(w(),F(a,{key:0,icon:V.icon,style:{"margin-right":"3px"}},null,8,["icon"])):V.flag===2?(w(),F(x,{key:1,style:{"margin-right":"3px"}},{default:t(()=>[s(m)]),_:1})):W("",!0),r("span",null,M(u.label),1)])])]),_:1},8,["data","default-checked-keys"])]),_:1})]),r("div",null,[s(i,null,{default:t(()=>[s(d,{label:"展开层级"},{default:t(()=>[s(j,{modelValue:D.value,"onUpdate:modelValue":e[1]||(e[1]=u=>D.value=u)},{default:t(()=>[(w(),X(Y,null,Z(A,(u,V)=>s($,{value:u.value,onClick:me=>z(u.value)},{default:t(()=>[N(M(u.label),1)]),_:2},1032,["value","onClick"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})])])])]),_:1},8,["modelValue","title"])}}}),he=le(re,[["__scopeId","data-v-117ab589"]]);export{he as default};
