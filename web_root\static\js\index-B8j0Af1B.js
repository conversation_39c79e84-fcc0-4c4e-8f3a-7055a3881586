import{g as V,u as k,d as T,_ as R}from"./add-subject.vue_vue_type_script_setup_true_lang-CT8r7S_w.js";import{p as E,g as H,a as N}from"./common-methods-BWkba4Bo.js";import{d as A,l,P as L,aN as O,n as M,ao as U,T as d,aV as q,r as p,o as J,c as Q,e as h,b as i,h as m,f as G,u as I,_ as $}from"./index-B63pSD2p.js";import{c as K,a as X}from"./calculateTableHeight-BjE6OFD1.js";import"./test-paper-management-DjV_45YZ.js";const Y={class:"zf-first-box"},Z={class:"zf-second-box"},ee={class:"upload-btn-box"},te=A({name:"subject-management",__name:"index",setup(ae){const C=l([{label:"抽参",value:0},{label:"抽卷",value:1}]),v=l(null),_=l(null),b=l(null),x=l({}),S=L({column:3,labelWidth:"110px",itemWidth:"240px",rules:{subject_name:[{trigger:["blur","change"],validator:(e,t,a)=>{if(t&&t.length>50)return a(new Error("所属科目长度不能超过50！"));a()}}],c_name:[{trigger:["blur","change"],validator:(e,t,a)=>{if(t&&t.length>50)return a(new Error("创建人名称长度不能超过50！"));a()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",defaultValue:"",placeholder:"请选择所属资格",clearable:!0,optionData:()=>E.value},{label:"科目名称",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择科目名称",optionData:()=>j.value},{label:"考试模式",prop:"exam_mode",type:"select",multiple:!0,collapseTags:!0,clearable:!0,defaultValue:"",placeholder:"请选择考试模式",optionData:()=>[{label:"抽参",value:0},{label:"抽卷",value:1}]}]}),j=l([]),o=l({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"科目名称",minWidth:"120px"},{prop:"subject_code",label:"编码",minWidth:"80px"},{prop:"exam_mode_name",label:"考试模式",minWidth:"120px"},{prop:"spy_num",label:"回评卷每次分发量",minWidth:"140px"},{prop:"is_active",label:"使用状态",type:"switch",minWidth:"100px",inactiveText:"禁用",activeText:"启用",inlinePrompt:!0,beforeChange:e=>W(e),onchange:e=>{}},{prop:"remark",label:"备注",minWidth:"160px",formatter:e=>{var t;return(t=e.remark)!=null?t:"-"}},{prop:"u_user_name",label:"更新人",minWidth:"120px"},{prop:"updated_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"operation",label:"操作",type:"template",minWidth:"160px",templateGroup:[{title:()=>O("subject-management/edit")?"编辑":"",clickBtn(e){b.value.openDialog("02",e)}},{title:()=>O("subject-management/delete")?"删除":"",clickBtn(e){z(e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let f=l([]),y=null;M(()=>{K(y,v.value,o.value),H(),r()}),U(()=>{X(y)});const D=(e,t)=>{e.prop==="project_id"&&(_.value.setCardData("subject_id",""),t&&N(t).then(a=>{j.value=a||[]}))},r=()=>{let e=JSON.parse(JSON.stringify(_.value.getAllCardData())),{currentPage:t,pageSize:a}=o.value.pageOptions,s={current_page:t,page_size:a};s=Object.assign(e,s),V(s).then(n=>{n.code&&n.code===200&&(f.value=n.data.data,o.value.pageOptions.total=n.data.total,f.value.forEach(c=>{let u=C.value.filter(g=>c.exam_mode==g.value);u&&u.length>0?c.exam_mode_name=u[0].label:c.exam_mode_name=""}))})},W=e=>new Promise((t,a)=>{B(e),t(!0)}),B=e=>{const{subject_id:t,is_active:a}=e;k({subject_id:t,is_active:!a}).then(n=>{n.code&&n.code===200?(d.success(n.msg),r()):d.warning(n.msg)})},w=()=>{b.value.openDialog("01")},z=e=>{q.confirm("确定删除该科目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let t={subject_id:e.subject_id};T(t).then(a=>{a.code&&a.code===200?(d.success(a.msg),r()):d.warning(a.msg)})}).catch(()=>{})},F=e=>{o.value.pageOptions.currentPage=1,o.value.pageOptions.pageSize=e,r()},P=e=>{o.value.pageOptions.currentPage=e,r()};return(e,t)=>{const a=p("form-component"),s=p("el-card"),n=p("el-button"),c=p("Auth"),u=p("table-component");return J(),Q("div",Y,[h("div",Z,[i(s,null,{default:m(()=>[h("div",{ref_key:"formDivRef",ref:v},[i(a,{ref_key:"formRef",ref:_,modelValue:x.value,"onUpdate:modelValue":t[0]||(t[0]=g=>x.value=g),"form-options":S,"is-query-btn":!0,onQueryDataFn:r,onOnchangeFn:D},null,8,["modelValue","form-options"])],512)]),_:1}),i(s,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:m(()=>[i(c,{value:"subject-management/add"},{default:m(()=>[h("div",ee,[i(n,{type:"primary",onClick:w},{default:m(()=>[G("创建")]),_:1})])]),_:1}),i(u,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":I(f),onOnHandleSizeChange:F,onOnHandleCurrentChange:P},null,8,["minHeight","table-options","table-data"])]),_:1})]),i(R,{ref_key:"addSubjectRef",ref:b,onQueryData:r},null,512)])}}}),ie=$(te,[["__scopeId","data-v-b42d722e"]]);export{ie as default};
