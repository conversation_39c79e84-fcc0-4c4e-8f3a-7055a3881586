import{a1 as o,C as m,N as p,aA as h}from"./index-B63pSD2p.js";function C(){return{layout:p().getItem(`${h()}layout`),configure:p().getItem(`${h()}configure`)}}const T=(t=null,n,s,l=!0,u=0)=>{function a(){m(()=>{var e;if(n.offsetWidth!==0){const{configure:c,layout:S}=C();let g=123;c.hideFooter||(g+=29);const r=l?42:0,d=(e=s.pageOptions)!=null&&e.isShowPage?0:36,y=document.querySelector("section[class^=app-main"),i=getComputedStyle(y),f=parseInt(i.height)-parseInt(i.paddingTop)-parseInt(i.paddingBottom);s.styleOptions.minHeight=f-g-r-n.clientHeight-u+d+"px"}})}t=new ResizeObserver(e=>{e.forEach(c=>{a()})}),n&&(t.observe(n),o.on("hideTagChange",e=>{a()}),o.on("hideFooterChange",e=>{a()}),o.on("setLayoutModel",e=>{a()}))},I=t=>{t&&(t.disconnect(),t=null,o.off("hideTagChange"),o.off("hideFooterChange"),o.off("setLayoutModel"))};export{I as a,T as c};
