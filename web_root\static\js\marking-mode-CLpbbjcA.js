import{aQ as r,aR as o}from"./index-B63pSD2p.js";const _=e=>r.request("post",o("/v1/work_flow/get_read_work_flow_process"),{data:e}),t=e=>r.request("post",o("/v1/work_flow/create_read_work_flow_process"),{data:e}),w=e=>r.request("get",o("/v1/work_flow/get_single_process"),{params:e}),a=e=>r.request("post",o("/v1/work_flow/delete_read_work_flow_process"),{data:e}),p=e=>r.request("post",o("/v1/work_flow/update_read_work_flow_process"),{data:e});export{_ as a,t as c,a as d,w as g,p as u};
