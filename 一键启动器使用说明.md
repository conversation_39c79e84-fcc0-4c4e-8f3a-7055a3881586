# 卓帆电子化考试阅卷管理系统 - 一键启动器

## 功能介绍

本启动器为卓帆电子化考试阅卷管理系统提供便捷的一键启动和关闭功能。

### 主要功能

1. **一键启动**：按照正确顺序启动所有必需的程序
   - 首先启动 Redis 服务
   - 然后启动主程序（卓帆电子化考试阅卷管理系统V1.0.exe）
   - 最后启动数据服务程序（定时任务V1.0.exe）

2. **一键关闭**：按照安全顺序关闭所有程序
   - 首先关闭数据服务程序
   - 然后关闭主程序
   - 最后关闭 Redis 服务

3. **状态监控**：实时显示各个程序的运行状态

4. **操作日志**：详细记录所有操作过程和结果

## 系统要求

- Windows 操作系统
- Python 3.6 或更高版本
- 所需的程序文件：
  - `Redis-x64-3.0.504\redis-server.exe`
  - `卓帆电子化考试阅卷管理系统V1.0.exe`
  - `定时任务V1.0.exe`

## 安装和使用

### 方法一：使用批处理文件（推荐）

1. 双击运行 `启动器.bat`
2. 系统会自动检查Python环境和依赖包
3. 如果缺少依赖，会自动安装
4. 启动器界面会自动打开

### 方法二：手动运行Python脚本

1. 确保已安装Python 3.6+
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
   或者：
   ```bash
   pip install psutil
   ```
3. 运行启动器：
   ```bash
   python 一键启动器.py
   ```

## 界面说明

### 系统状态区域
- **Redis**: 显示Redis服务的运行状态
- **主程序**: 显示主程序的运行状态  
- **数据服务**: 显示数据服务程序的运行状态

状态显示：
- 🔴 红色 "未启动" - 程序未运行
- 🟢 绿色 "运行中" - 程序正在运行

### 操作按钮
- **一键启动**: 按顺序启动所有程序
- **一键关闭**: 按顺序关闭所有程序
- **刷新状态**: 手动刷新程序运行状态

### 操作日志
显示详细的操作过程和结果，包括：
- 启动/关闭操作的时间戳
- 每个程序的启动/关闭状态
- 错误信息（如果有）

## 注意事项

1. **启动顺序很重要**：必须先启动Redis，再启动主程序，最后启动数据服务程序
2. **关闭顺序也很重要**：按相反顺序关闭，确保数据安全
3. **权限要求**：某些情况下可能需要管理员权限运行
4. **防火墙设置**：确保防火墙允许这些程序运行
5. **端口占用**：确保Redis等服务所需的端口没有被其他程序占用

## 故障排除

### 常见问题

1. **Python未安装**
   - 下载并安装Python：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **依赖包安装失败**
   - 手动运行：`pip install psutil`
   - 如果pip不可用，先运行：`python -m ensurepip`

3. **程序文件未找到**
   - 检查程序文件是否在正确位置
   - 确保文件名完全匹配

4. **程序启动失败**
   - 检查是否有足够的系统权限
   - 查看操作日志中的错误信息
   - 确保没有其他程序占用相同端口

5. **无法关闭程序**
   - 某些程序可能需要手动关闭
   - 可以通过任务管理器强制结束进程

### 日志文件
所有操作都会在界面的日志区域显示，如需保存日志，可以从界面复制相关内容。

## 技术支持

如遇到问题，请：
1. 查看操作日志中的错误信息
2. 检查系统要求是否满足
3. 参考故障排除部分
4. 联系技术支持团队

---

**版本**: 1.0  
**更新日期**: 2025-09-08
