function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = []
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var Kv=Object.defineProperty,Zv=Object.defineProperties;var Qv=Object.getOwnPropertyDescriptors;var Bm=Object.getOwnPropertySymbols,Jv=Object.getPrototypeOf,t1=Object.prototype.hasOwnProperty,e1=Object.prototype.propertyIsEnumerable,s1=Reflect.get;var Vm=(d,t)=>(t=Symbol[d])?t:Symbol.for("Symbol."+d),oe=Math.pow,Ap=(d,t,e)=>t in d?Kv(d,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):d[t]=e,Ge=(d,t)=>{for(var e in t||(t={}))t1.call(t,e)&&Ap(d,e,t[e]);if(Bm)for(var e of Bm(t))e1.call(t,e)&&Ap(d,e,t[e]);return d},vi=(d,t)=>Zv(d,Qv(t));var O=(d,t,e)=>(Ap(d,typeof t!="symbol"?t+"":t,e),e),Sp=(d,t,e)=>{if(!t.has(d))throw TypeError("Cannot "+e)};var r=(d,t,e)=>(Sp(d,t,"read from private field"),e?e.call(d):t.get(d)),p=(d,t,e)=>{if(t.has(d))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(d):t.set(d,e)},m=(d,t,e,s)=>(Sp(d,t,"write to private field"),s?s.call(d,e):t.set(d,e),e);var Fe=(d,t,e,s)=>({set _(i){m(d,t,i,e)},get _(){return r(d,t,s)}}),y=(d,t,e)=>(Sp(d,t,"access private method"),e),yi=(d,t,e)=>s1(Jv(d),e,t);var H=(d,t,e)=>new Promise((s,i)=>{var n=l=>{try{o(e.next(l))}catch(h){i(h)}},a=l=>{try{o(e.throw(l))}catch(h){i(h)}},o=l=>l.done?s(l.value):Promise.resolve(l.value).then(n,a);o((e=e.apply(d,t)).next())});var zm=(d,t,e)=>(t=d[Vm("asyncIterator")])?t.call(d):(d=d[Vm("iterator")](),t={},e=(s,i)=>(i=d[s])&&(t[s]=n=>new Promise((a,o,l)=>(n=i.call(d,n),l=n.done,Promise.resolve(n.value).then(h=>a({value:h,done:l}),o)))),e("next"),e("return"),t);import{k as Ce,o as P,c as $,e as D,d as _t,l as nt,m as rt,X as dp,n as Ws,F as Le,p as ks,q as De,g as lt,s as Il,u as E,t as Jt,b as R,v as w0,_ as ae,x as fs,r as Q,f as Tt,y as ct,w as Zt,z as rn,A as mi,B as Is,W as A0,C as ka,S as i1,D as n1,i as r1,E as Np,G as th,H as a1,I as Ze,J as Cp,h as q,K as o1,N as Ts,L as Ia,M as l1,O as Zn,P as om,Q as Um,R as Pa,T as xd,j as Qn,U as Ft,V as S0,Y as C0,Z as h1,$ as cu,a0 as x0,a1 as Me,a2 as lm,a3 as hm,a4 as xp,a5 as Ep,a6 as nr,a7 as c1,a8 as d1,a9 as u1,aa as f1,ab as p1,ac as cm,ad as dm,ae as eh,af as bd,ag as Gm,ah as Op,ai as rr,aj as g1,ak as m1,al as E0,am as Ci,an as T0,ao as b1,ap as Na,aq as v1,ar as y1,as as _1,at as w1,au as A1,av as S1,aw as $p,ax as C1,ay as x1,az as E1,aA as T1,aB as k1}from"./index-B63pSD2p.js";import{u as Hp,a as up,_ as um,b as fm,t as I1,l as k0,c as P1}from"./logo-DT3YnI87.js";import{d as I0,u as P0,a as R0,S as R1}from"./useTag-CrO3NxXA.js";import{m as M1}from"./index-CMAj5lxj.js";import{u as hr}from"./hooks-Cf_Naqnw.js";import{c as L1}from"./user-management-B1vGxPiG.js";import{u as jm}from"./pageCache-DQQfxtZI.js";import{R as Tp}from"./index-BzG0ERft.js";import{u as D1}from"./util-DBFSI-P4.js";const F1={width:1024,height:1024,body:'<path fill="currentColor" d="M513.28 448a64 64 0 1 1 76.544 49.728A96 96 0 0 0 768 448h64a160 160 0 0 1-320 0h1.28zm-126.976-29.696a256 256 0 1 0 43.52-180.48A256 256 0 0 1 832 448h-64a192 192 0 0 0-381.696-29.696zm105.664 249.472L285.696 874.048a96 96 0 0 1-135.68-135.744l206.208-206.272a320 320 0 1 1 135.744 135.744zm-54.464-36.032a321.92 321.92 0 0 1-45.248-45.248L195.2 783.552a32 32 0 1 0 45.248 45.248l197.056-197.12z"/>'},N1={width:1024,height:1024,body:'<path fill="currentColor" d="M512 128L128 447.936V896h255.936V640H640v256h255.936V447.936z"/>'},O1={width:1024,height:1024,body:'<path fill="currentColor" d="M160 448a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32H160zm448 0a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32H608zM160 896a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32H160zm448 0a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32H608z"/>'},$1={width:1024,height:1024,body:'<path fill="currentColor" d="M416 896V128h192v768H416zm-288 0V448h192v448H128zm576 0V320h192v576H704z"/>'},M0={width:24,height:24,body:'<path fill="currentColor" d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617Zm-2.006-.742A6.977 6.977 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.977 6.977 0 0 0 4.875-1.975l.15-.15Z"/>'},H1={width:24,height:24,body:'<path fill="currentColor" d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10Zm0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16ZM11 7h2v2h-2V7Zm0 4h2v6h-2v-6Z"/>'},B1={width:24,height:24,body:'<path fill="currentColor" d="M20 10H4v9h16v-9ZM3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Zm2 3v2h2V6H5Zm4 0v2h2V6H9Zm-4 5h3v5H5v-5Z"/>'},V1={width:24,height:24,body:'<path fill="currentColor" d="M11 4c.649 0 1.25.206 1.741.556c-1.292.673-2.48 1.52-3.741 2.248l1 1.732c1.575-.91 3.052-2.05 4.76-2.706a3.001 3.001 0 0 1 4.057 3.09C17.588 8.137 16.26 7.531 15 6.804l-1 1.732c1.575.91 3.302 1.618 4.723 2.77a3.001 3.001 0 0 1-.648 5.058C18.14 14.908 18 13.455 18 12h-2c0 1.818.25 3.668-.037 5.476a3.001 3.001 0 0 1-4.705 1.967c1.293-.673 2.482-1.52 3.742-2.247l-1-1.732c-1.575.91-3.052 2.05-4.76 2.706a3.001 3.001 0 0 1-4.057-3.09c1.23.782 2.557 1.388 3.817 2.116l1-1.732c-1.575-.91-3.302-1.618-4.724-2.77a3.001 3.001 0 0 1 .649-5.058C5.86 9.092 6 10.545 6 12h2c0-1.818-.25-3.669.037-5.476A3.001 3.001 0 0 1 11 4Zm3.793-.258a5.001 5.001 0 0 0-8.548 1.71a5.001 5.001 0 0 0-2.793 8.258a5.001 5.001 0 0 0 5.756 6.548a5.001 5.001 0 0 0 8.548-1.71a5.001 5.001 0 0 0 2.793-8.258a5.001 5.001 0 0 0-5.756-6.548Z"/>'},z1={width:24,height:24,body:'<path fill="currentColor" d="M4 22a8 8 0 1 1 16 0H4Zm8-9c-3.315 0-6-2.685-6-6s2.685-6 6-6s6 2.685 6 6s-2.685 6-6 6Z"/>'},U1={width:24,height:24,body:'<path fill="currentColor" d="M20 2a3 3 0 0 1 3 3v2h-2v12a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3v-2h16v2a1 1 0 0 0 .883.993L18 20a1 1 0 0 0 .993-.883L19 19v-4H3V5a3 3 0 0 1 3-3h14Z"/>'},G1={width:24,height:24,body:'<path fill="currentColor" d="M2 22a8 8 0 1 1 16 0H2Zm8-9c-3.315 0-6-2.685-6-6s2.685-6 6-6s6 2.685 6 6s-2.685 6-6 6Zm7.363 2.233A7.505 7.505 0 0 1 22.983 22H20c0-2.61-1-4.986-2.637-6.767Zm-2.023-2.276A7.98 7.98 0 0 0 18 7a7.964 7.964 0 0 0-1.015-3.903A5 5 0 0 1 21 8a4.999 4.999 0 0 1-5.66 4.957Z"/>'},j1={width:24,height:24,body:'<path fill="currentColor" d="M2 3.993A1 1 0 0 1 2.992 3h18.016c.548 0 .992.445.992.993v16.014a1 1 0 0 1-.992.993H2.992A.993.993 0 0 1 2 20.007V3.993ZM12 5v14h8V5h-8Zm1 2h6v2h-6V7Zm0 3h6v2h-6v-2Z"/>'},W1={width:24,height:24,body:'<path fill="currentColor" d="M3 6a3 3 0 1 1 4 2.83v6.34a3.001 3.001 0 1 1-2 0V8.83A3.001 3.001 0 0 1 3 6Zm12.293-2.707a1 1 0 0 1 1.414 0L18 4.586l1.293-1.293a1 1 0 1 1 1.414 1.414L19.414 6l1.293 1.293a1 1 0 0 1-1.414 1.414L18 7.414l-1.293 1.293a1 1 0 1 1-1.414-1.414L16.586 6l-1.293-1.293a1 1 0 0 1 0-1.414ZM18 10a1 1 0 0 1 1 1v4.17a3.001 3.001 0 1 1-2 0V11a1 1 0 0 1 1-1Z"/>'},q1={width:24,height:24,body:'<path fill="currentColor" d="M20 22H4a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1ZM7 6v4h4V6H7Zm0 6v2h10v-2H7Zm0 4v2h10v-2H7Zm6-9v2h4V7h-4Z"/>'},X1={width:24,height:24,body:'<path fill="currentColor" d="m10.313 11.566l7.94-7.94l2.121 2.12l-1.414 1.415l2.121 2.121l-3.535 3.536l-2.121-2.121l-2.99 2.99a5.002 5.002 0 0 1-7.97 5.848a5 5 0 0 1 5.848-7.97Zm-.899 5.848a2 2 0 1 0-2.828-2.828a2 2 0 0 0 2.828 2.828Z"/>'},Y1={width:24,height:24,body:'<path fill="currentColor" d="M20 22H6.5A3.5 3.5 0 0 1 3 18.5V5a3 3 0 0 1 3-3h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1Zm-1-2v-3H6.5a1.5 1.5 0 0 0 0 3H19ZM10 4v8l3.5-2l3.5 2V4h-7Z"/>'},K1={width:24,height:24,body:'<path fill="currentColor" d="M4 2h16a1 1 0 0 1 1 1v19.276a.5.5 0 0 1-.704.457L12 19.03l-8.296 3.702A.5.5 0 0 1 3 22.276V3a1 1 0 0 1 1-1Zm8 11.5l2.939 1.545l-.561-3.272l2.377-2.318l-3.285-.478L12 6l-1.47 2.977l-3.285.478l2.377 2.318l-.56 3.272L12 13.5Z"/>'};Ce("ep:lollipop",F1);Ce("ep:home-filled",N1);Ce("ep:menu",O1);Ce("ep:histogram",$1);Ce("ri:search-line",M0);Ce("ri:information-line",H1);Ce("ri:terminal-window-line",B1);Ce("ri:openai-line",V1);Ce("ri:user-fill",z1);Ce("ri:file-paper-2-line",U1);Ce("ri:group-line",G1);Ce("ri:book-read-fill",j1);Ce("ri:git-close-pull-request-fill",W1);Ce("ri:article-fill",q1);Ce("ri:key-2-fill",X1);Ce("ri:book-mark-fill",Y1);Ce("ri:bookmark-3-fill",K1);const Z1={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20","aria-hidden":"true",class:"iconify iconify--ant-design",viewBox:"0 0 1024 1024"},Q1=D("path",{fill:"currentColor",d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 0 0 0 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8"},null,-1),J1=[Q1];function ty(d,t){return P(),$("svg",Z1,[...J1])}const L0={render:ty},ey=["onMouseenter"],sy={class:"result-item-title"},iy=_t({__name:"SearchResult",props:{value:{},options:{}},emits:["update:value","enter"],setup(d,{expose:t,emit:e}){const s=nt(),i=nt(),n=e,a=w0(),o=d,l=rt(()=>b=>({background:(b==null?void 0:b.path)===h.value?Hp().epThemeColor:"",color:b.path===h.value?"#fff":"",fontSize:b.path===h.value?"16px":"14px"})),h=rt({get(){return o.value},set(b){n("update:value",b)}});function c(b){return H(this,null,function*(){h.value=b.path})}function u(){n("enter")}function f(){i.value=window.innerHeight-window.innerHeight/10-140}dp(s,f);function g(b){var A;const v=(A=a==null?void 0:a.proxy)==null?void 0:A.$refs[`resultItemRef${b}`];if(!v)return 0;const w=v[0].offsetTop+128;return w>i.value?w-i.value:0}return Ws(()=>{f()}),t({handleScroll:g}),(b,v)=>(P(),$("div",{ref_key:"resultRef",ref:s,class:"result"},[(P(!0),$(Le,null,ks(b.options,(_,w)=>{var A,C;return P(),$("div",{key:_.path,ref_for:!0,ref:"resultItemRef"+w,class:"result-item dark:bg-[#1d1d1d]",style:De(l.value(_)),onClick:u,onMouseenter:S=>c(_)},[(P(),lt(Il(E(hr)((A=_.meta)==null?void 0:A.icon)))),D("span",sy,Jt((C=_.meta)==null?void 0:C.title),1),R(E(L0))],44,ey)}),128))],512))}}),ny=ae(iy,[["__scopeId","data-v-168a78d7"]]),ry={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20","aria-hidden":"true",class:"iconify iconify--mdi",viewBox:"0 0 24 24"},ay=D("path",{fill:"currentColor",d:"M1 7h6v2H3v2h4v2H3v2h4v2H1zm10 0h4v2h-4v2h2a2 2 0 0 1 2 2v2c0 1.11-.89 2-2 2H9v-2h4v-2h-2a2 2 0 0 1-2-2V9c0-1.1.9-2 2-2m8 0h2a2 2 0 0 1 2 2v1h-2V9h-2v6h2v-1h2v1c0 1.11-.89 2-2 2h-2a2 2 0 0 1-2-2V9c0-1.1.9-2 2-2"},null,-1),oy=[ay];function ly(d,t){return P(),$("svg",ry,[...oy])}const hy={render:ly},cy={width:24,height:24,body:'<path fill="currentColor" d="M13 7.828V20h-2V7.828l-5.364 5.364l-1.414-1.414L12 4l7.778 7.778l-1.414 1.414L13 7.828Z"/>'},dy={width:24,height:24,body:'<path fill="currentColor" d="m13 16.172l5.364-5.364l1.414 1.414L12 20l-7.778-7.778l1.414-1.414L11 16.172V4h2v12.172Z"/>'},uy={class:"search-footer text-[#333] dark:text-white"},fy={class:"search-footer-item"},py={class:"search-footer-item"},gy={class:"search-footer-item"},my={key:0,class:"search-footer-total"},by=_t({__name:"SearchFooter",props:{total:{default:0}},setup(d){const t=d,{device:e}=fs();return(s,i)=>{const n=Q("IconifyIconOffline");return P(),$("div",uy,[D("span",fy,[R(E(L0),{class:"icon"}),Tt(" 确认 ")]),D("span",py,[R(n,{icon:E(cy),class:"icon"},null,8,["icon"]),R(n,{icon:E(dy),class:"icon"},null,8,["icon"]),Tt(" 切换 ")]),D("span",gy,[R(E(hy),{class:"icon"}),Tt(" 关闭 ")]),E(e)!=="mobile"&&t.total>0?(P(),$("p",my," 共"+Jt(t.total)+"项 ",1)):ct("",!0)])}}}),vy=ae(by,[["__scopeId","data-v-875c28ff"]]),yy={width:1024,height:1024,body:'<path fill="currentColor" d="m512 747.84l228.16 119.936a6.4 6.4 0 0 0 9.28-6.72l-43.52-254.08l184.512-179.904a6.4 6.4 0 0 0-3.52-10.88l-255.104-37.12L517.76 147.904a6.4 6.4 0 0 0-11.52 0L392.192 379.072l-255.104 37.12a6.4 6.4 0 0 0-3.52 10.88L318.08 606.976l-43.584 254.08a6.4 6.4 0 0 0 9.28 6.72L512 747.84zM313.6 924.48a70.4 70.4 0 0 1-102.144-74.24l37.888-220.928L88.96 472.96A70.4 70.4 0 0 1 128 352.896l221.76-32.256l99.2-200.96a70.4 70.4 0 0 1 126.208 0l99.2 200.96l221.824 32.256a70.4 70.4 0 0 1 39.04 120.064L774.72 629.376l37.888 220.928a70.4 70.4 0 0 1-102.144 74.24L512 820.096l-198.4 104.32z"/>'},_y={class:"history-item-title"},wy=_t({__name:"SearchHistoryItem",props:{item:{}},emits:["collectItem","deleteItem"],setup(d,{emit:t}){const e=t;function s(n){e("collectItem",n)}function i(n){e("deleteItem",n)}return(n,a)=>{var l,h;const o=Q("IconifyIconOffline");return P(),$(Le,null,[(P(),lt(Il(E(hr)((l=n.item.meta)==null?void 0:l.icon)))),D("span",_y,Jt((h=n.item.meta)==null?void 0:h.title),1),Zt(R(o,{icon:E(yy),class:"w-[18px] h-[18px] mr-2 hover:text-[#d7d5d4]",onClick:a[0]||(a[0]=mi(c=>s(n.item),["stop"]))},null,8,["icon"]),[[rn,n.item.type==="history"]]),R(o,{icon:E(I0),class:"w-[18px] h-[18px] hover:text-[#d7d5d4] cursor-pointer",onClick:a[1]||(a[1]=mi(c=>i(n.item),["stop"]))},null,8,["icon"])],64)}}}),Wm=ae(wy,[["__scopeId","data-v-8fca2e1d"]]),Ay=["onMouseenter"],Sy={class:"collect-container"},Cy=["onMouseenter"],xy=_t({__name:"SearchHistory",props:{value:{},options:{}},emits:["update:value","enter","collect","delete","drag"],setup(d,{expose:t,emit:e}){const s=nt(),i=nt(),n=nt(!1),a=e,o=w0(),l=d,h=rt(()=>x=>({background:(x==null?void 0:x.path)===u.value?Hp().epThemeColor:"",color:x.path===u.value?"#fff":"",fontSize:x.path===u.value?"16px":"14px"})),c=rt(()=>({color:Hp().epThemeColor,fontWeight:500})),u=rt({get(){return l.value},set(x){a("update:value",x)}});Is(()=>l.value,x=>{x&&n.value&&A0(100).then(()=>n.value=!1)});const f=rt(()=>l.options.filter(x=>x.type==="history")),g=rt(()=>l.options.filter(x=>x.type==="collect"));function b(x){a("collect",x)}function v(x){n.value=!0,a("delete",x)}function _(x){return H(this,null,function*(){n.value||(u.value=x.path)})}function w(){a("enter")}function A(){i.value=window.innerHeight-window.innerHeight/10-140}dp(s,A);function C(x){var L;const k=(L=o==null?void 0:o.proxy)==null?void 0:L.$refs[`historyItemRef${x}`];if(!k)return 0;const M=(n1(k)?k[0]:k).offsetTop+128;return M>i.value?M-i.value:0}const S=x=>{a("drag",{oldIndex:x.oldIndex,newIndex:x.newIndex})};let T=null;return Is(g,x=>{x.length>1&&ka(()=>{const k=document.querySelector(".collect-container");!k||T||(T=i1.create(k,{animation:160,onStart:I=>{I.item.style.cursor="move"},onEnd:I=>{I.item.style.cursor="pointer"},onUpdate:S}),A())})},{deep:!0,immediate:!0}),t({handleScroll:C}),(x,k)=>(P(),$("div",{ref_key:"historyRef",ref:s,class:"history"},[f.value.length?(P(),$(Le,{key:0},[D("div",{style:De(c.value)},"搜索历史",4),(P(!0),$(Le,null,ks(f.value,(I,M)=>(P(),$("div",{key:I.path,ref_for:!0,ref:"historyItemRef"+M,class:"history-item dark:bg-[#1d1d1d]",style:De(h.value(I)),onClick:w,onMouseenter:L=>_(I)},[R(Wm,{item:I,onDeleteItem:v,onCollectItem:b},null,8,["item"])],44,Ay))),128))],64)):ct("",!0),g.value.length?(P(),$(Le,{key:1},[D("div",{style:De(c.value)}," 收藏"+Jt(g.value.length>1?"（可拖拽排序）":""),5),D("div",Sy,[(P(!0),$(Le,null,ks(g.value,(I,M)=>(P(),$("div",{key:I.path,ref_for:!0,ref:"historyItemRef"+(M+f.value.length),class:"history-item dark:bg-[#1d1d1d]",style:De(h.value(I)),onClick:w,onMouseenter:L=>_(I)},[R(Wm,{item:I,onDeleteItem:v},null,8,["item"])],44,Cy))),128))])],64)):ct("",!0)],512))}}),Ey=ae(xy,[["__scopeId","data-v-f1a53dda"]]),Ty={class:"search-content"},qm="history",ky="collect",_i="menu-search-history",on="menu-search-collect",Iy=_t({__name:"SearchModal",props:{value:{type:Boolean}},emits:["update:value"],setup(d,{emit:t}){const{device:e}=fs(),s=t,i=d,n=r1(),a=nt(""),o=nt(),l=nt(),h=nt(),c=nt(""),u=nt(""),f=Np([]),g=Np([]),b=o1(M,300),v=th().MenuSearchHistory,_=nt(null),w=rt(()=>a1(Ze().wholeMenus)),A=rt({get(){return i.value},set(U){s("update:value",U)}});Is(()=>i.value,U=>{U&&Et()});const C=rt(()=>a.value&&f.value.length>0),S=rt(()=>!a.value&&g.value.length>0),T=rt(()=>!a.value&&g.value.length===0||a.value&&f.value.length===0);function x(U){return Ts().getItem(U)||[]}function k(U,j){Ts().setItem(U,j)}function I(U){const j=[];function Y(N){N.forEach(V=>{j.push(V),V.children&&Y(V.children)})}return Y(U),j}function M(){var j;const U=I(w.value);f.value=U.filter(Y=>{var N,V;return a.value?((N=Y.meta)==null?void 0:N.title.toLocaleLowerCase().includes(a.value.toLocaleLowerCase().trim()))||!Ia(M1((V=Y.meta)==null?void 0:V.title.toLocaleLowerCase(),a.value.toLocaleLowerCase().trim())):!1}),f.value=L(f.value),c.value=((j=f.value)==null?void 0:j.length)>0?f.value[0].path:""}function L(U){U.reverse();const j=new Map,Y=U.filter(N=>{const V=j.has(N.module_name);return V||j.set(N.module_name,!0),!V});return Y.reverse(),Y}function F(){A.value=!1,setTimeout(()=>{f.value=[],u.value="",a.value=""},200)}function it(U){const Y=(f.value.length?o.value:l.value).handleScroll(U);h.value.setScrollTop(Y)}function at(){const U=f.value.length>0,j=U?f.value:g.value,Y=U?c.value:u.value;return{options:j,currentPath:Y,isResultOptions:U}}function J(U,j){j?c.value=f.value[U].path:u.value=g.value[U].path,it(U)}function tt(){const{options:U,currentPath:j,isResultOptions:Y}=at();if(U.length===0)return;const V=(U.findIndex(Nt=>Nt.path===j)-1+U.length)%U.length;J(V,Y)}function B(){const{options:U,currentPath:j,isResultOptions:Y}=at();if(U.length===0)return;const V=(U.findIndex(Nt=>Nt.path===j)+1)%U.length;J(V,Y)}function G(){const{options:U,currentPath:j,isResultOptions:Y}=at();if(U.length===0||j==="")return;const N=U.findIndex(V=>V.path===j);N!==-1&&(Y?ge():xe(),n.push(U[N].path),F())}function kt(U){const j=U.type===qm?_i:on;let Y=x(j);Y=Y.filter(N=>N.path!==U.path),k(j,Y),Et()}function Bt(U){let j=x(_i),Y=x(on);j=j.filter(N=>N.path!==U.path),k(_i,j),Y.some(N=>N.path===U.path)||(Y.unshift(vi(Ge({},U),{type:ky})),k(on,Y)),Et()}function ge(){const{path:U,meta:j}=f.value.find(ee=>ee.path===c.value),Y=x(_i),V=x(on).some(ee=>ee.path===U),Nt=Y.findIndex(ee=>ee.path===U);V||(Nt!==-1&&Y.splice(Nt,1),Y.length>=v&&Y.pop(),Y.unshift({path:U,meta:j,type:qm}),Ts().setItem(_i,Y))}function xe(){let U=x(_i);const j=U.findIndex(Y=>Y.path===u.value);if(j!==-1){const[Y]=U.splice(j,1);U.unshift(Y),k(_i,U)}}function Et(){var Y;const U=x(_i),j=x(on);g.value=[...U,...j],u.value=(Y=g.value[0])==null?void 0:Y.path}function At(U){const j=x(on),[Y]=j.splice(U.oldIndex,1);j.splice(U.newIndex,0,Y),Ts().setItem(on,j),g.value=[...x(_i),...x(on)],u.value=Y.path}return Cp("Enter",G),Cp("ArrowUp",tt),Cp("ArrowDown",B),(U,j)=>{const Y=Q("IconifyIconOffline"),N=Q("el-input"),V=Q("el-empty"),Nt=Q("el-scrollbar"),ee=Q("el-dialog");return P(),lt(ee,{modelValue:A.value,"onUpdate:modelValue":j[3]||(j[3]=Vt=>A.value=Vt),top:"5vh",class:"pure-search-dialog","show-close":!1,width:E(e)==="mobile"?"80vw":"40vw","before-close":F,style:{borderRadius:"6px"},"append-to-body":"",onOpened:j[4]||(j[4]=Vt=>_.value.focus()),onClosed:j[5]||(j[5]=Vt=>_.value.blur())},{footer:q(()=>[R(vy,{total:f.value.length},null,8,["total"])]),default:q(()=>[R(N,{ref_key:"inputRef",ref:_,modelValue:a.value,"onUpdate:modelValue":j[0]||(j[0]=Vt=>a.value=Vt),size:"large",clearable:"",placeholder:"搜索菜单（支持拼音搜索）",onInput:E(b)},{prefix:q(()=>[R(Y,{icon:E(M0),class:"text-primary w-[24px] h-[24px]"},null,8,["icon"])]),_:1},8,["modelValue","onInput"]),D("div",Ty,[R(Nt,{ref_key:"scrollbarRef",ref:h,"max-height":"calc(90vh - 140px)"},{default:q(()=>[T.value?(P(),lt(V,{key:0,description:"暂无搜索结果"})):ct("",!0),S.value?(P(),lt(Ey,{key:1,ref_key:"historyRef",ref:l,value:u.value,"onUpdate:value":j[1]||(j[1]=Vt=>u.value=Vt),options:g.value,onClick:G,onDelete:kt,onCollect:Bt,onDrag:At},null,8,["value","options"])):ct("",!0),C.value?(P(),lt(ny,{key:2,ref_key:"resultRef",ref:o,value:c.value,"onUpdate:value":j[2]||(j[2]=Vt=>c.value=Vt),options:f.value,onClick:G},null,8,["value","options"])):ct("",!0)]),_:1},512)])]),_:1},8,["modelValue","width"])}}}),Py=ae(Iy,[["__scopeId","data-v-b4e94d1b"]]),pm=_t({__name:"index",setup(d){const{bool:t,toggle:e}=up();function s(){e()}return(i,n)=>{const a=Q("IconifyIconOffline");return P(),$("div",null,[D("div",{class:"search-container w-[40px] h-[34px] flex-c cursor-pointer navbar-bg-hover",onClick:s},[R(a,{icon:"ri:search-line"})]),R(E(Py),{value:E(t),"onUpdate:value":n[0]||(n[0]=o=>l1(t)?t.value=o:null)},null,8,["value"])])}}}),Ry={key:0,class:"flex justify-center items-center"},Bp=_t({__name:"extraIcon",props:{extraIcon:{type:String,default:""}},setup(d){const t=d;return(e,s)=>t.extraIcon?(P(),$("div",Ry,[(P(),lt(Il(E(hr)(Zn(t.extraIcon))),{class:"w-[30px] h-[30px]"}))])):ct("",!0)}}),My={class:"zf-dialog-first-box"},Ly={class:"zf-dialog-second-box"},Dy={key:0,style:{color:"var(--el-color-primary)","margin-bottom":"10px"}},Fy={key:0,class:"flex justify-end mr-[50px]"},Ny={key:1,class:"footer-btn"},gm=_t({__name:"change-password",props:["show","alreadyLogin","entrance"],emits:["closeChangePassword"],setup(d,{emit:t}){const{logout:e}=fs(),s=d,i=t,n=nt("修改密码");nt(!1);const a=rt({get(){return s.show},set(f){i("closeChangePassword",f)}});Ws(()=>{s.alreadyLogin===!1&&(l.fields.forEach(f=>{f.prop==="raw_password"&&(f.isHidden=!0)}),l.rules.raw_password=[])});const o=nt({}),l=om({column:3,labelWidth:"108px",itemWidth:"250px",rules:{raw_password:[{required:!0,message:"请输入原密码",trigger:["blur","change"]}],new_password:[{required:!0,message:"请输入新密码",trigger:["blur","change"]}],confirm_password:[{required:!0,message:"请输入确认密码",trigger:["blur","change"]}]},fields:[{label:"原密码",prop:"raw_password",type:"input",defaultValue:"",placeholder:"请输入原密码",showPassword:!0,isHidden:!1,clearable:!0},{label:"新密码",prop:"new_password",type:"input",defaultValue:"",placeholder:"请输入新密码",showPassword:!0,isHidden:!1,clearable:!0},{label:"确认密码",prop:"confirm_password",type:"input",defaultValue:"",placeholder:"请输入确认密码",showPassword:!0,isHidden:!1,clearable:!0}]}),h=nt(null),c=()=>{a.value=!1,h.value.resetFieldsFn()},u=()=>{h.value.formValidate().then(()=>{var g;let f=JSON.parse(JSON.stringify(h.value.getAllCardData()));f.confirm_password===f.new_password?(f.raw_password&&(f.raw_password=Um(f.raw_password)),f.new_password=Um(f.new_password),f.user_id=(g=Ts().getItem(Pa))==null?void 0:g.userId,delete f.confirm_password,L1(f).then(b=>{if(b.code&&b.code===200){xd.success(b.msg);const v=Ts().getItem(Pa);v.already_login||(v.already_login=!0,Ts().setItem(Pa,v)),c()}else xd.warning(b.msg)})):xd.warning("两次密码不一致！")}).catch(()=>{xd.warning("请按要求填写！")})};return(f,g)=>{const b=Q("form-component"),v=Q("el-button"),_=Q("el-dialog");return P(),lt(_,{modelValue:a.value,"onUpdate:modelValue":g[1]||(g[1]=w=>a.value=w),title:n.value,width:"440px",style:{"z-index":"9999"},"align-center":"","append-to-body":!0,"close-on-click-modal":!1,"close-on-press-escape":!1,draggable:"","show-close":d.alreadyLogin,"before-close":c},{footer:q(()=>[d.alreadyLogin===!1?(P(),$("div",Fy,[d.entrance==="forgetPassWord"?(P(),lt(v,{key:0,onClick:c},{default:q(()=>[Tt("取消")]),_:1})):(P(),lt(v,{key:1,onClick:E(e)},{default:q(()=>[Tt("退出登录")]),_:1},8,["onClick"])),R(v,{type:"primary",onClick:u},{default:q(()=>[Tt("确定")]),_:1})])):(P(),$("div",Ny,[R(v,{onClick:c},{default:q(()=>[Tt("取消")]),_:1}),R(v,{type:"primary",onClick:u},{default:q(()=>[Tt("确定")]),_:1})]))]),default:q(()=>[D("div",My,[D("div",Ly,[d.alreadyLogin===!1?(P(),$("div",Dy,"提示：首次登录请先修改密码 ")):ct("",!0),R(b,{ref_key:"formRef",ref:h,modelValue:o.value,"onUpdate:modelValue":g[0]||(g[0]=w=>o.value=w),"form-options":l,"is-query-btn":!1},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title","show-close"])}}}),mm=_t({__name:"fullScreen",setup(d){const t=nt(),{toggle:e,isFullscreen:s,Fullscreen:i,ExitFullscreen:n}=fs();return s.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),Is(s,a=>{t.value=a?n:i},{immediate:!0}),(a,o)=>{const l=Q("IconifyIconOffline");return P(),$("span",{class:"fullscreen-icon navbar-bg-hover",onClick:o[0]||(o[0]=(...h)=>E(e)&&E(e)(...h))},[R(l,{icon:t.value},null,8,["icon"])])}}}),bm={width:24,height:24,body:'<path fill="currentColor" d="M12.005 22.003c-5.523 0-10-4.477-10-10s4.477-10 10-10s10 4.477 10 10s-4.477 10-10 10Zm0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16Zm-5-7h9v2h-4v3l-5-5Zm5-4v-3l5 5h-9v-2h4Z"/>'},vm={width:24,height:24,body:'<path fill="currentColor" d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2a9.985 9.985 0 0 1 8 4h-2.71a8 8 0 1 0 .001 12h2.71A9.985 9.985 0 0 1 12 22Zm7-6v-3h-8v-2h8V8l5 4l-5 4Z"/>'},ym={width:24,height:24,body:'<path fill="currentColor" d="M18 8h2a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h2V7a6 6 0 1 1 12 0v1ZM5 10v10h14V10H5Zm6 4h2v2h-2v-2Zm-4 0h2v2H7v-2Zm8 0h2v2h-2v-2Zm1-6V7a4 4 0 0 0-8 0v1h8Z"/>'},_m={width:24,height:24,body:'<path fill="currentColor" d="M3.34 17a10.017 10.017 0 0 1-.979-2.326a3 3 0 0 0 .003-5.347a9.99 9.99 0 0 1 2.5-4.337a3 3 0 0 0 4.632-2.674a9.99 9.99 0 0 1 5.007.003a3 3 0 0 0 4.632 2.671a10.056 10.056 0 0 1 2.503 4.336a3 3 0 0 0-.002 5.347a9.99 9.99 0 0 1-2.501 4.337a3 3 0 0 0-4.632 2.674a9.99 9.99 0 0 1-5.007-.002a3 3 0 0 0-4.631-2.672A10.018 10.018 0 0 1 3.339 17Zm5.66.196a4.992 4.992 0 0 1 2.25 2.77c.499.047 1 .048 1.499.002a4.993 4.993 0 0 1 2.25-2.772a4.993 4.993 0 0 1 3.526-.564c.29-.408.54-.843.748-1.298A4.993 4.993 0 0 1 18 12c0-1.26.47-2.437 1.273-3.334a8.152 8.152 0 0 0-.75-1.298A4.993 4.993 0 0 1 15 6.804a4.993 4.993 0 0 1-2.25-2.77c-.5-.047-1-.048-1.5-.001A4.993 4.993 0 0 1 9 6.804a4.993 4.993 0 0 1-3.526.564c-.29.408-.54.843-.747 1.298A4.993 4.993 0 0 1 6 12c0 1.26-.471 2.437-1.273 3.334a8.16 8.16 0 0 0 .75 1.298A4.993 4.993 0 0 1 9 17.196ZM12 15a3 3 0 1 1 0-6a3 3 0 0 1 0 6Zm0-2a1 1 0 1 0 0-2a1 1 0 0 0 0 2Z"/>'},Oy={key:0,class:"horizontal-header"},$y={class:"select-none"},Hy={class:"horizontal-header-right"},By={class:"el-dropdown-link navbar-bg-hover select-none"},Vy={class:"user-text-box"},zy={key:0,class:"dark:text-white user-name-text"},Uy={key:0},Gy={key:1,class:"dark:text-white"},jy=_t({__name:"mixNav",setup(d){const t=nt(),e=nt(null),{route:s,device:i,logout:n,onPanel:a,resolvePath:o,username:l,name:h,userAvatar:c,getDivStyle:u,avatarsStyle:f}=fs();function g(x){var M,L,F;const k=Ze().wholeMenus,I=S0(x,k)[0];e.value=Ia((M=s.meta)==null?void 0:M.activePath)?(F=(L=C0(I,k))==null?void 0:L.children[0])==null?void 0:F.path:s.meta.activePath}Ws(()=>{g(s.path)}),ka(()=>{var x;(x=t.value)==null||x.handleResize()}),Is(()=>[s.path,Ze().wholeMenus],()=>{g(s.path)});const{bool:b,toggle:v,setFalse:_}=up(),w=nt(!1);Ws(()=>{sessionStorage.getItem("roleInfo")?w.value=!0:w.value=!1});function A(){v()}function C(){v()}function S(){_()}function T(){_()}return(x,k)=>{const I=Q("IconifyIconOffline"),M=Q("el-menu-item"),L=Q("el-menu"),F=Q("el-dropdown-item"),it=Q("el-dropdown-menu"),at=Q("el-dropdown"),J=Qn("loading");return E(i)!=="mobile"?Zt((P(),$("div",Oy,[R(L,{ref_key:"menuRef",ref:t,router:"",mode:"horizontal","popper-class":"pure-scrollbar",class:"horizontal-header-menu","default-active":e.value},{default:q(()=>[(P(!0),$(Le,null,ks(E(Ze)().wholeMenus,tt=>(P(),lt(M,{key:tt.path,index:E(o)(tt)||tt.redirect},{title:q(()=>[Zn(tt.meta.icon)?(P(),$("div",{key:0,class:Ft(["sub-menu-icon",tt.meta.icon])},[R(I,{icon:tt.meta&&Zn(tt.meta.icon)},null,8,["icon"])],2)):ct("",!0),D("div",{style:De(E(u))},[D("span",$y,Jt(tt.meta.title),1),R(Bp,{extraIcon:tt.meta.extraIcon},null,8,["extraIcon"])],4)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"]),D("div",Hy,[R(pm,{id:"header-search"}),R(mm,{id:"full-screen"}),R(at,{trigger:"click"},{dropdown:q(()=>[R(it,{class:"logout"},{default:q(()=>[w.value?(P(),lt(F,{key:0,onClick:A},{default:q(()=>[R(I,{icon:E(bm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 切换角色 ")]),_:1})):ct("",!0),R(F,{onClick:C},{default:q(()=>[R(I,{icon:E(ym),style:{margin:"5px"}},null,8,["icon"]),Tt(" 修改密码 ")]),_:1}),R(F,{onClick:E(n)},{default:q(()=>[R(I,{icon:E(vm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 退出系统 ")]),_:1},8,["onClick"])]),_:1})]),default:q(()=>[D("div",By,[D("div",Vy,[E(h)?(P(),$("p",zy,[Tt(Jt(E(h)),1),E(Ts)().getItem(E(Pa)).user_type===2?(P(),$("span",Uy,"（AI）")):ct("",!0)])):ct("",!0),E(l)?(P(),$("p",Gy)):ct("",!0)])])]),_:1}),D("span",{class:"set-icon navbar-bg-hover",title:"打开项目配置",onClick:k[0]||(k[0]=(...tt)=>E(a)&&E(a)(...tt))},[R(I,{icon:E(_m)},null,8,["icon"])])]),R(um,{show:E(b),onCloseChangeRole:S},null,8,["show"]),R(gm,{show:E(b),onCloseChangePassword:T},null,8,["show"])])),[[J,E(Ze)().wholeMenus.length===0]]):ct("",!0)}}}),Wy=ae(jy,[["__scopeId","data-v-59583650"]]),ze=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),Vp=[.001,0,0,.001,0,0],kp=1.35,ds={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},hn={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},qy="pdfjs_internal_editor_",ot={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},vt={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},Xy={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},Ee={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},Ld={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},Yt={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},Oa={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},fp={ERRORS:0,WARNINGS:1,INFOS:5},du={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},Ed={moveTo:0,lineTo:1,curveTo:2,closePath:3},Yy={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let pp=fp.WARNINGS;function Ky(d){Number.isInteger(d)&&(pp=d)}function Zy(){return pp}function gp(d){pp>=fp.INFOS&&console.log(`Info: ${d}`)}function ht(d){pp>=fp.WARNINGS&&console.log(`Warning: ${d}`)}function xt(d){throw new Error(d)}function Qt(d,t){d||xt(t)}function Qy(d){switch(d==null?void 0:d.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function D0(d,t=null,e=null){if(!d)return null;if(e&&typeof d=="string"){if(e.addDefaultProtocol&&d.startsWith("www.")){const i=d.match(/\./g);(i==null?void 0:i.length)>=2&&(d=`http://${d}`)}if(e.tryConvertEncoding)try{d=i_(d)}catch(i){}}const s=t?URL.parse(d,t):URL.parse(d);return Qy(s)?s:null}function F0(d,t,e=!1){const s=URL.parse(d);return s?(s.hash=t,s.href):e&&D0(d,"http://example.com")?d.split("#",1)[0]+`${t?`#${t}`:""}`:""}function gt(d,t,e,s=!1){return Object.defineProperty(d,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const Da=function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class Xm extends Da{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Ip extends Da{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class zp extends Da{constructor(t){super(t,"InvalidPDFException")}}class uu extends Da{constructor(t,e,s){super(t,"ResponseException"),this.status=e,this.missing=s}}class Jy extends Da{constructor(t){super(t,"FormatError")}}class Jn extends Da{constructor(t){super(t,"AbortException")}}function N0(d){(typeof d!="object"||(d==null?void 0:d.length)===void 0)&&xt("Invalid argument for bytesToString");const t=d.length,e=8192;if(t<e)return String.fromCharCode.apply(null,d);const s=[];for(let i=0;i<t;i+=e){const n=Math.min(i+e,t),a=d.subarray(i,n);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function vd(d){typeof d!="string"&&xt("Invalid argument for stringToBytes");const t=d.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=d.charCodeAt(s)&255;return e}function t_(d){return String.fromCharCode(d>>24&255,d>>16&255,d>>8&255,d&255)}function e_(){const d=new Uint8Array(4);return d[0]=1,new Uint32Array(d.buffer,0,1)[0]===1}function s_(){try{return new Function(""),!0}catch(d){return!1}}class Se{static get isLittleEndian(){return gt(this,"isLittleEndian",e_())}static get isEvalSupported(){return gt(this,"isEvalSupported",s_())}static get isOffscreenCanvasSupported(){return gt(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas!="undefined")}static get isImageDecoderSupported(){return gt(this,"isImageDecoderSupported",typeof ImageDecoder!="undefined")}static get platform(){if(typeof navigator!="undefined"&&typeof(navigator==null?void 0:navigator.platform)=="string"&&typeof(navigator==null?void 0:navigator.userAgent)=="string"){const{platform:t,userAgent:e}=navigator;return gt(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}return gt(this,"platform",{isAndroid:!1,isLinux:!1,isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){var t,e;return gt(this,"isCSSRoundSupported",(e=(t=globalThis.CSS)==null?void 0:t.supports)==null?void 0:e.call(t,"width: round(1.5px, 1px)"))}}const Pp=Array.from(Array(256).keys(),d=>d.toString(16).padStart(2,"0"));var Ya,Dd,rh,Up;class K{static makeHexColor(t,e,s){return`#${Pp[t]}${Pp[e]}${Pp[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,s=0){const i=t[s],n=t[s+1];t[s]=i*e[0]+n*e[2]+e[4],t[s+1]=i*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,s=0){const i=e[0],n=e[1],a=e[2],o=e[3],l=e[4],h=e[5];for(let c=0;c<6;c+=2){const u=t[s+c],f=t[s+c+1];t[s+c]=u*i+f*a+l,t[s+c+1]=u*n+f*o+h}}static applyInverseTransform(t,e){const s=t[0],i=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(s*e[3]-i*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-s*e[1]+i*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,s){const i=e[0],n=e[1],a=e[2],o=e[3],l=e[4],h=e[5],c=t[0],u=t[1],f=t[2],g=t[3];let b=i*c+l,v=b,_=i*f+l,w=_,A=o*u+h,C=A,S=o*g+h,T=S;if(n!==0||a!==0){const x=n*c,k=n*f,I=a*u,M=a*g;b+=I,w+=I,_+=M,v+=M,A+=x,T+=x,S+=k,C+=k}s[0]=Math.min(s[0],b,_,v,w),s[1]=Math.min(s[1],A,S,C,T),s[2]=Math.max(s[2],b,_,v,w),s[3]=Math.max(s[3],A,S,C,T)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const s=t[0],i=t[1],n=t[2],a=t[3],o=oe(s,2)+oe(i,2),l=s*n+i*a,h=oe(n,2)+oe(a,2),c=(o+h)/2,u=Math.sqrt(oe(c,2)-(o*h-oe(l,2)));e[0]=Math.sqrt(c+u||1),e[1]=Math.sqrt(c-u||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[s,n,i,a]}static pointBoundingBox(t,e,s){s[0]=Math.min(s[0],t),s[1]=Math.min(s[1],e),s[2]=Math.max(s[2],t),s[3]=Math.max(s[3],e)}static rectBoundingBox(t,e,s,i,n){n[0]=Math.min(n[0],t,s),n[1]=Math.min(n[1],e,i),n[2]=Math.max(n[2],t,s),n[3]=Math.max(n[3],e,i)}static bezierBoundingBox(t,e,s,i,n,a,o,l,h){h[0]=Math.min(h[0],t,o),h[1]=Math.min(h[1],e,l),h[2]=Math.max(h[2],t,o),h[3]=Math.max(h[3],e,l),y(this,rh,Up).call(this,t,s,n,o,e,i,a,l,3*(-t+3*(s-n)+o),6*(t-2*s+n),3*(s-t),h),y(this,rh,Up).call(this,t,s,n,o,e,i,a,l,3*(-e+3*(i-a)+l),6*(e-2*i+a),3*(i-e),h)}}Ya=new WeakSet,Dd=function(t,e,s,i,n,a,o,l,h,c){if(h<=0||h>=1)return;const u=1-h,f=h*h,g=f*h,b=u*(u*(u*t+3*h*e)+3*f*s)+g*i,v=u*(u*(u*n+3*h*a)+3*f*o)+g*l;c[0]=Math.min(c[0],b),c[1]=Math.min(c[1],v),c[2]=Math.max(c[2],b),c[3]=Math.max(c[3],v)},rh=new WeakSet,Up=function(t,e,s,i,n,a,o,l,h,c,u,f){if(Math.abs(h)<1e-12){Math.abs(c)>=1e-12&&y(this,Ya,Dd).call(this,t,e,s,i,n,a,o,l,-u/c,f);return}const g=oe(c,2)-4*u*h;if(g<0)return;const b=Math.sqrt(g),v=2*h;y(this,Ya,Dd).call(this,t,e,s,i,n,a,o,l,(-c+b)/v,f),y(this,Ya,Dd).call(this,t,e,s,i,n,a,o,l,(-c-b)/v,f)},p(K,Ya),p(K,rh);function i_(d){return decodeURIComponent(escape(d))}let Rp=null,Ym=null;function n_(d){return Rp||(Rp=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Ym=new Map([["ﬅ","ſt"]])),d.replaceAll(Rp,(t,e,s)=>e?e.normalize("NFKC"):Ym.get(s))}function O0(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const d=new Uint8Array(32);return crypto.getRandomValues(d),N0(d)}const wm="pdfjs_internal_id_";function r_(d,t,e){if(!Array.isArray(e)||e.length<2)return!1;const[s,i,...n]=e;if(!d(s)&&!Number.isInteger(s)||!t(i))return!1;const a=n.length;let o=!0;switch(i.name){case"XYZ":if(a<2||a>3)return!1;break;case"Fit":case"FitB":return a===0;case"FitH":case"FitBH":case"FitV":case"FitBV":if(a>1)return!1;break;case"FitR":if(a!==4)return!1;o=!1;break;default:return!1}for(const l of n)if(!(typeof l=="number"||o&&l===null))return!1;return!0}function Ue(d,t,e){return Math.min(Math.max(d,t),e)}function $0(d){return Uint8Array.prototype.toBase64?d.toBase64():btoa(N0(d))}function a_(d){return Uint8Array.fromBase64?Uint8Array.fromBase64(d):vd(atob(d))}typeof Promise.try!="function"&&(Promise.try=function(d,...t){return new Promise(e=>{e(d(...t))})});typeof Math.sumPrecise!="function"&&(Math.sumPrecise=function(d){return d.reduce((t,e)=>t+e,0)});const wi="http://www.w3.org/2000/svg",ar=class ar{};O(ar,"CSS",96),O(ar,"PDF",72),O(ar,"PDF_TO_CSS_UNITS",ar.CSS/ar.PDF);let tr=ar;function yd(d,t="text"){return H(this,null,function*(){if($a(d,document.baseURI)){const e=yield fetch(d);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",d,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})})}class _d{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:n=0,offsetY:a=0,dontFlip:o=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=n,this.offsetY=a,s*=e;const l=(t[2]+t[0])/2,h=(t[3]+t[1])/2;let c,u,f,g;switch(i%=360,i<0&&(i+=360),i){case 180:c=-1,u=0,f=0,g=1;break;case 90:c=0,u=1,f=1,g=0;break;case 270:c=0,u=-1,f=-1,g=0;break;case 0:c=1,u=0,f=0,g=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}o&&(f=-f,g=-g);let b,v,_,w;c===0?(b=Math.abs(h-t[1])*s+n,v=Math.abs(l-t[0])*s+a,_=(t[3]-t[1])*s,w=(t[2]-t[0])*s):(b=Math.abs(l-t[0])*s+n,v=Math.abs(h-t[1])*s+a,_=(t[2]-t[0])*s,w=(t[3]-t[1])*s),this.transform=[c*s,u*s,f*s,g*s,b-c*s*l-f*s*h,v-u*s*l-g*s*h],this.width=_,this.height=w}get rawDims(){const t=this.viewBox;return gt(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:n=!1}={}){return new _d({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}convertToViewportPoint(t,e){const s=[t,e];return K.applyTransform(s,this.transform),s}convertToViewportRectangle(t){const e=[t[0],t[1]];K.applyTransform(e,this.transform);const s=[t[2],t[3]];return K.applyTransform(s,this.transform),[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){const s=[t,e];return K.applyInverseTransform(s,this.transform),s}}class Am extends Da{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function mp(d){const t=d.length;let e=0;for(;e<t&&d[e].trim()==="";)e++;return d.substring(e,e+5).toLowerCase()==="data:"}function Sm(d){return typeof d=="string"&&/\.pdf$/i.test(d)}function o_(d){return[d]=d.split(/[#?]/,1),d.substring(d.lastIndexOf("/")+1)}function l_(d,t="document.pdf"){if(typeof d!="string")return t;if(mp(d))return ht('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const e=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=e.exec(d);let n=s.exec(i[1])||s.exec(i[2])||s.exec(i[3]);if(n&&(n=n[0],n.includes("%")))try{n=s.exec(decodeURIComponent(n))[0]}catch(a){}return n||t}class Km{constructor(){O(this,"started",Object.create(null));O(this,"times",[])}time(t){t in this.started&&ht(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||ht(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:n}of this.times)t.push(`${s.padEnd(e)} ${n-i}ms
`);return t.join("")}}function $a(d,t){const e=t?URL.parse(d,t):URL.parse(d);return(e==null?void 0:e.protocol)==="http:"||(e==null?void 0:e.protocol)==="https:"}function qs(d){d.preventDefault()}function Kt(d){d.preventDefault(),d.stopPropagation()}var ah;class Cm{static toDateObject(t){if(!t||typeof t!="string")return null;r(this,ah)||m(this,ah,new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=r(this,ah).exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const h=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let u=parseInt(e[9],10)||0;return u=u>=0&&u<=59?u:0,h==="-"?(a+=c,o+=u):h==="+"&&(a-=c,o-=u),new Date(Date.UTC(s,i,n,a,o,l))}}ah=new WeakMap,p(Cm,ah,void 0);function h_(d,{scale:t=1,rotation:e=0}){const{width:s,height:i}=d.attributes.style,n=[0,0,parseInt(s),parseInt(i)];return new _d({viewBox:n,userUnit:1,scale:t,rotation:e})}function xm(d){if(d.startsWith("#")){const t=parseInt(d.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return d.startsWith("rgb(")?d.slice(4,-1).split(",").map(t=>parseInt(t)):d.startsWith("rgba(")?d.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(ht(`Not a valid color format: "${d}"`),[0,0,0])}function c_(d){const t=document.createElement("span");t.style.visibility="hidden",t.style.colorScheme="only light",document.body.append(t);for(const e of d.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;d.set(e,xm(s))}t.remove()}function Dt(d){const{a:t,b:e,c:s,d:i,e:n,f:a}=d.getTransform();return[t,e,s,i,n,a]}function Ks(d){const{a:t,b:e,c:s,d:i,e:n,f:a}=d.getTransform().invertSelf();return[t,e,s,i,n,a]}function Ra(d,t,e=!1,s=!0){if(t instanceof _d){const{pageWidth:i,pageHeight:n}=t.rawDims,{style:a}=d,o=Se.isCSSRoundSupported,l=`var(--total-scale-factor) * ${i}px`,h=`var(--total-scale-factor) * ${n}px`,c=o?`round(down, ${l}, var(--scale-round-x))`:`calc(${l})`,u=o?`round(down, ${h}, var(--scale-round-y))`:`calc(${h})`;!e||t.rotation%180===0?(a.width=c,a.height=u):(a.width=u,a.height=c)}s&&d.setAttribute("data-main-rotation",t.rotation)}class an{constructor(){const{pixelRatio:t}=an;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,s,i){let n=1/0,a=1/0,o=1/0;s>0&&(n=Math.sqrt(s/(t*e))),i!==-1&&(a=i/t,o=i/e);const l=Math.min(n,a,o);return this.sx>l||this.sy>l?(this.sx=l,this.sy=l,!0):!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}}const Gp=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];var cn,cr,vs,dn,oh,Ka,lh,_u,H0,wu,B0,Au,V0,dr,Fl,Su,z0,Za,Fd;const xi=class xi{constructor(t){p(this,wu);p(this,Au);p(this,dr);p(this,Su);p(this,Za);p(this,cn,null);p(this,cr,null);p(this,vs,void 0);p(this,dn,null);p(this,oh,null);p(this,Ka,null);m(this,vs,t),r(xi,lh)||m(xi,lh,Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"}))}render(){const t=m(this,cn,document.createElement("div"));t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=r(this,vs)._uiManager._signal;t.addEventListener("contextmenu",qs,{signal:e}),t.addEventListener("pointerdown",y(xi,_u,H0),{signal:e});const s=m(this,dn,document.createElement("div"));s.className="buttons",t.append(s);const i=r(this,vs).toolbarPosition;if(i){const{style:n}=t,a=r(this,vs)._uiManager.direction==="ltr"?1-i[0]:i[0];n.insetInlineEnd=`${100*a}%`,n.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return y(this,Su,z0).call(this),t}get div(){return r(this,cn)}hide(){var t;r(this,cn).classList.add("hidden"),(t=r(this,cr))==null||t.hideDropdown()}show(){var t;r(this,cn).classList.remove("hidden"),(t=r(this,oh))==null||t.shown()}addAltText(t){return H(this,null,function*(){const e=yield t.render();y(this,dr,Fl).call(this,e),r(this,dn).prepend(e,r(this,Za,Fd)),m(this,oh,t)})}addColorPicker(t){m(this,cr,t);const e=t.renderButton();y(this,dr,Fl).call(this,e),r(this,dn).prepend(e,r(this,Za,Fd))}addEditSignatureButton(t){return H(this,null,function*(){const e=m(this,Ka,yield t.renderEditButton(r(this,vs)));y(this,dr,Fl).call(this,e),r(this,dn).prepend(e,r(this,Za,Fd))})}updateEditSignatureButton(t){r(this,Ka)&&(r(this,Ka).title=t)}remove(){var t;r(this,cn).remove(),(t=r(this,cr))==null||t.destroy(),m(this,cr,null)}};cn=new WeakMap,cr=new WeakMap,vs=new WeakMap,dn=new WeakMap,oh=new WeakMap,Ka=new WeakMap,lh=new WeakMap,_u=new WeakSet,H0=function(t){t.stopPropagation()},wu=new WeakSet,B0=function(t){r(this,vs)._focusEventsAllowed=!1,Kt(t)},Au=new WeakSet,V0=function(t){r(this,vs)._focusEventsAllowed=!0,Kt(t)},dr=new WeakSet,Fl=function(t){const e=r(this,vs)._uiManager._signal;t.addEventListener("focusin",y(this,wu,B0).bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",y(this,Au,V0).bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",qs,{signal:e})},Su=new WeakSet,z0=function(){const{editorType:t,_uiManager:e}=r(this,vs),s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",r(xi,lh)[t]),y(this,dr,Fl).call(this,s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),r(this,dn).append(s)},Za=new WeakSet,Fd=function(){const t=document.createElement("div");return t.className="divider",t},p(xi,_u),p(xi,lh,null);let jp=xi;var hh,ur,fr,Cu,U0,xu,G0,Eu,j0;class d_{constructor(t){p(this,Cu);p(this,xu);p(this,Eu);p(this,hh,null);p(this,ur,null);p(this,fr,void 0);m(this,fr,t)}show(t,e,s){const[i,n]=y(this,xu,G0).call(this,e,s),{style:a}=r(this,ur)||m(this,ur,y(this,Cu,U0).call(this));t.append(r(this,ur)),a.insetInlineEnd=`${100*i}%`,a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){r(this,ur).remove()}}hh=new WeakMap,ur=new WeakMap,fr=new WeakMap,Cu=new WeakSet,U0=function(){const t=m(this,ur,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",qs,{signal:r(this,fr)._signal});const e=m(this,hh,document.createElement("div"));return e.className="buttons",t.append(e),y(this,Eu,j0).call(this),t},xu=new WeakSet,G0=function(t,e){let s=0,i=0;for(const n of t){const a=n.y+n.height;if(a<s)continue;const o=n.x+(e?n.width:0);if(a>s){i=o,s=a;continue}e?o>i&&(i=o):o<i&&(i=o)}return[e?1-i:i,s]},Eu=new WeakSet,j0=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=r(this,fr)._signal;t.addEventListener("contextmenu",qs,{signal:s}),t.addEventListener("click",()=>{r(this,fr).highlightSelection("floating_button")},{signal:s}),r(this,hh).append(t)};function Em(d,t,e){for(const s of e)t.addEventListener(s,d[s].bind(d))}var Tu;class u_{constructor(){p(this,Tu,0)}get id(){return`${qy}${Fe(this,Tu)._++}`}}Tu=new WeakMap;var Qa,ch,ke,Ja,Nd;const Dm=class Dm{constructor(){p(this,Ja);p(this,Qa,O0());p(this,ch,0);p(this,ke,null)}static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),i=new Image;i.src=t;const n=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return gt(this,"_isSVGFittingCanvas",n)}getFromFile(t){return H(this,null,function*(){const{lastModified:e,name:s,size:i,type:n}=t;return y(this,Ja,Nd).call(this,`${e}_${s}_${i}_${n}`,t)})}getFromUrl(t){return H(this,null,function*(){return y(this,Ja,Nd).call(this,t,t)})}getFromBlob(t,e){return H(this,null,function*(){const s=yield e;return y(this,Ja,Nd).call(this,t,s)})}getFromId(t){return H(this,null,function*(){r(this,ke)||m(this,ke,new Map);const e=r(this,ke).get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)})}getFromCanvas(t,e){r(this,ke)||m(this,ke,new Map);let s=r(this,ke).get(t);if(s!=null&&s.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${r(this,Qa)}_${Fe(this,ch)._++}`,refCounter:1,isSvg:!1},r(this,ke).set(t,s),r(this,ke).set(s.id,s),s}getSvgUrl(t){const e=r(this,ke).get(t);return e!=null&&e.isSvg?e.svgUrl:null}deleteId(t){var i;r(this,ke)||m(this,ke,new Map);const e=r(this,ke).get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const n=new OffscreenCanvas(s.width,s.height);n.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=n.convertToBlob()}(i=s.close)==null||i.call(s),e.bitmap=null}isValidId(t){return t.startsWith(`image_${r(this,Qa)}_`)}};Qa=new WeakMap,ch=new WeakMap,ke=new WeakMap,Ja=new WeakSet,Nd=function(t,e){return H(this,null,function*(){r(this,ke)||m(this,ke,new Map);let s=r(this,ke).get(t);if(s===null)return null;if(s!=null&&s.bitmap)return s.refCounter+=1,s;try{s||(s={bitmap:null,id:`image_${r(this,Qa)}_${Fe(this,ch)._++}`,refCounter:0,isSvg:!1});let i;if(typeof e=="string"?(s.url=e,i=yield yd(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const n=Dm._isSVGFittingCanvas,a=new FileReader,o=new Image,l=new Promise((h,c)=>{o.onload=()=>{s.bitmap=o,s.isSvg=!0,h()},a.onload=()=>H(this,null,function*(){const u=s.svgUrl=a.result;o.src=(yield n)?`${u}#svgView(preserveAspectRatio(none))`:u}),o.onerror=a.onerror=c});a.readAsDataURL(i),yield l}else s.bitmap=yield createImageBitmap(i);s.refCounter=1}catch(i){ht(i),s=null}return r(this,ke).set(t,s),s&&r(this,ke).set(s.id,s),s})};let Wp=Dm;var zt,un,dh,Lt;class f_{constructor(t=128){p(this,zt,[]);p(this,un,!1);p(this,dh,void 0);p(this,Lt,-1);m(this,dh,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:o=!1}){if(i&&t(),r(this,un))return;const l={cmd:t,undo:e,post:s,type:n};if(r(this,Lt)===-1){r(this,zt).length>0&&(r(this,zt).length=0),m(this,Lt,0),r(this,zt).push(l);return}if(a&&r(this,zt)[r(this,Lt)].type===n){o&&(l.undo=r(this,zt)[r(this,Lt)].undo),r(this,zt)[r(this,Lt)]=l;return}const h=r(this,Lt)+1;h===r(this,dh)?r(this,zt).splice(0,1):(m(this,Lt,h),h<r(this,zt).length&&r(this,zt).splice(h)),r(this,zt).push(l)}undo(){if(r(this,Lt)===-1)return;m(this,un,!0);const{undo:t,post:e}=r(this,zt)[r(this,Lt)];t(),e==null||e(),m(this,un,!1),m(this,Lt,r(this,Lt)-1)}redo(){if(r(this,Lt)<r(this,zt).length-1){m(this,Lt,r(this,Lt)+1),m(this,un,!0);const{cmd:t,post:e}=r(this,zt)[r(this,Lt)];t(),e==null||e(),m(this,un,!1)}}hasSomethingToUndo(){return r(this,Lt)!==-1}hasSomethingToRedo(){return r(this,Lt)<r(this,zt).length-1}cleanType(t){if(r(this,Lt)!==-1){for(let e=r(this,Lt);e>=0;e--)if(r(this,zt)[e].type!==t){r(this,zt).splice(e+1,r(this,Lt)-e),m(this,Lt,e);return}r(this,zt).length=0,m(this,Lt,-1)}}destroy(){m(this,zt,null)}}zt=new WeakMap,un=new WeakMap,dh=new WeakMap,Lt=new WeakMap;var ku,W0;class wd{constructor(t){p(this,ku);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=Se.platform;for(const[s,i,n={}]of t)for(const a of s){const o=a.startsWith("mac+");e&&o?(this.callbacks.set(a.slice(4),{callback:i,options:n}),this.allKeys.add(a.split("+").at(-1))):!e&&!o&&(this.callbacks.set(a,{callback:i,options:n}),this.allKeys.add(a.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(y(this,ku,W0).call(this,e));if(!s)return;const{callback:i,options:{bubbles:n=!1,args:a=[],checker:o=null}}=s;o&&!o(t,e)||(i.bind(t,...a,e)(),n||Kt(e))}}ku=new WeakSet,W0=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const Iu=class Iu{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return c_(t),gt(this,"_colors",t)}convert(t){const e=xm(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((n,a)=>n===e[a]))return Iu._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?K.makeHexColor(...e):t}};O(Iu,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let qp=Iu;var to,Qe,ie,me,eo,Ti,so,ys,fn,pn,io,pr,Js,Ms,gr,uh,fh,no,ph,ti,gn,ro,mn,ei,Pu,bn,gh,vn,mr,br,yn,mh,le,wt,ki,_n,vr,bh,vh,wn,si,Ii,yh,_s,ao,Od,_h,Xp,Ru,q0,Mu,X0,oo,$d,Lu,Y0,Du,K0,Fu,Z0,wh,Yp,Nu,Q0,Ah,Kp,Sh,Zp,Ou,J0,be,Ne,Ls,Ai,$u,tb,Hu,eb,Ch,Qp,Bu,sb,yr,Nl,xh,Jp;const Wa=class Wa{constructor(t,e,s,i,n,a,o,l,h,c,u,f,g,b){p(this,ao);p(this,_h);p(this,Ru);p(this,Mu);p(this,oo);p(this,Lu);p(this,Du);p(this,Fu);p(this,wh);p(this,Nu);p(this,Ah);p(this,Sh);p(this,Ou);p(this,be);p(this,Ls);p(this,$u);p(this,Hu);p(this,Ch);p(this,Bu);p(this,yr);p(this,xh);p(this,to,new AbortController);p(this,Qe,null);p(this,ie,new Map);p(this,me,new Map);p(this,eo,null);p(this,Ti,null);p(this,so,null);p(this,ys,new f_);p(this,fn,null);p(this,pn,null);p(this,io,0);p(this,pr,new Set);p(this,Js,null);p(this,Ms,null);p(this,gr,new Set);O(this,"_editorUndoBar",null);p(this,uh,!1);p(this,fh,!1);p(this,no,!1);p(this,ph,null);p(this,ti,null);p(this,gn,null);p(this,ro,null);p(this,mn,!1);p(this,ei,null);p(this,Pu,new u_);p(this,bn,!1);p(this,gh,!1);p(this,vn,null);p(this,mr,null);p(this,br,null);p(this,yn,null);p(this,mh,null);p(this,le,ot.NONE);p(this,wt,new Set);p(this,ki,null);p(this,_n,null);p(this,vr,null);p(this,bh,null);p(this,vh,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1});p(this,wn,[0,0]);p(this,si,null);p(this,Ii,null);p(this,yh,null);p(this,_s,null);const v=this._signal=r(this,to).signal;m(this,Ii,t),m(this,yh,e),m(this,eo,s),m(this,_n,i),this._eventBus=n,n._on("editingaction",this.onEditingAction.bind(this),{signal:v}),n._on("pagechanging",this.onPageChanging.bind(this),{signal:v}),n._on("scalechanging",this.onScaleChanging.bind(this),{signal:v}),n._on("rotationchanging",this.onRotationChanging.bind(this),{signal:v}),n._on("setpreference",this.onSetPreference.bind(this),{signal:v}),n._on("switchannotationeditorparams",_=>this.updateParams(_.type,_.value),{signal:v}),y(this,Lu,Y0).call(this),y(this,Ou,J0).call(this),y(this,wh,Yp).call(this),m(this,Ti,a.annotationStorage),m(this,ph,a.filterFactory),m(this,vr,o),m(this,ro,l||null),m(this,uh,h),m(this,fh,c),m(this,no,u),m(this,mh,f||null),this.viewParameters={realScale:tr.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=g||null,this._supportsPinchToZoom=b!==!1}static get _keyboardManager(){const t=Wa.prototype,e=a=>r(a,Ii).contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&a.hasSomethingToControl(),s=(a,{target:o})=>{if(o instanceof HTMLInputElement){const{type:l}=o;return l!=="text"&&l!=="number"}return!0},i=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return gt(this,"_keyboardManager",new wd([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&r(a,Ii).contains(o)&&!a.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&r(a,Ii).contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}destroy(){var t,e,s,i,n,a,o,l;(t=r(this,_s))==null||t.resolve(),m(this,_s,null),(e=r(this,to))==null||e.abort(),m(this,to,null),this._signal=null;for(const h of r(this,me).values())h.destroy();r(this,me).clear(),r(this,ie).clear(),r(this,gr).clear(),(s=r(this,yn))==null||s.clear(),m(this,Qe,null),r(this,wt).clear(),r(this,ys).destroy(),(i=r(this,eo))==null||i.destroy(),(n=r(this,_n))==null||n.destroy(),(a=r(this,ei))==null||a.hide(),m(this,ei,null),(o=r(this,br))==null||o.destroy(),m(this,br,null),r(this,ti)&&(clearTimeout(r(this,ti)),m(this,ti,null)),r(this,si)&&(clearTimeout(r(this,si)),m(this,si,null)),(l=this._editorUndoBar)==null||l.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return r(this,mh)}get useNewAltTextFlow(){return r(this,fh)}get useNewAltTextWhenAddingImage(){return r(this,no)}get hcmFilter(){return gt(this,"hcmFilter",r(this,vr)?r(this,ph).addHCMFilter(r(this,vr).foreground,r(this,vr).background):"none")}get direction(){return gt(this,"direction",getComputedStyle(r(this,Ii)).direction)}get highlightColors(){return gt(this,"highlightColors",r(this,ro)?new Map(r(this,ro).split(",").map(t=>t.split("=").map(e=>e.trim()))):null)}get highlightColorNames(){return gt(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),m(this,pn,t)}setMainHighlightColorPicker(t){m(this,br,t)}editAltText(t,e=!1){var s;(s=r(this,eo))==null||s.editAltText(this,t,e)}getSignature(t){var e;(e=r(this,_n))==null||e.getSignature({uiManager:this,editor:t})}get signatureManager(){return r(this,_n)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){switch(t){case"enableNewAltTextWhenAddingImage":m(this,no,e);break}}onPageChanging({pageNumber:t}){m(this,io,t-1)}focusMainContainer(){r(this,Ii).focus()}findParent(t,e){for(const s of r(this,me).values()){const{x:i,y:n,width:a,height:o}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=n&&e<=n+o)return s}return null}disableUserSelect(t=!1){r(this,yh).classList.toggle("noUserSelect",t)}addShouldRescale(t){r(this,gr).add(t)}removeShouldRescale(t){r(this,gr).delete(t)}onScaleChanging({scale:t}){var e;this.commitOrRemove(),this.viewParameters.realScale=t*tr.PDF_TO_CSS_UNITS;for(const s of r(this,gr))s.onScaleChanging();(e=r(this,pn))==null||e.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a}=e,o=e.toString(),h=y(this,ao,Od).call(this,e).closest(".textLayer"),c=this.getSelectionBoxes(h);if(!c)return;e.empty();const u=y(this,_h,Xp).call(this,h),f=r(this,le)===ot.NONE,g=()=>{u==null||u.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:c,anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:a,text:o}),f&&this.showAllEditors("highlight",!0,!0)};if(f){this.switchToMode(ot.HIGHLIGHT,g);return}g()}addToAnnotationStorage(t){!t.isEmpty()&&r(this,Ti)&&!r(this,Ti).has(t.id)&&r(this,Ti).setValue(t.id,t)}blur(){if(this.isShiftKeyDown=!1,r(this,mn)&&(m(this,mn,!1),y(this,oo,$d).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of r(this,wt))if(e.div.contains(t)){m(this,mr,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!r(this,mr))return;const[t,e]=r(this,mr);m(this,mr,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}addEditListeners(){y(this,wh,Yp).call(this),y(this,Ah,Kp).call(this)}removeEditListeners(){y(this,Nu,Q0).call(this),y(this,Sh,Zp).call(this)}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of r(this,Ms))if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of r(this,Ms))if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){var s;if(t.preventDefault(),(s=r(this,Qe))==null||s.commitOrRemove(),!this.hasSelection)return;const e=[];for(const i of r(this,wt)){const n=i.serialize(!0);n&&e.push(n)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}paste(t){return H(this,null,function*(){t.preventDefault();const{clipboardData:e}=t;for(const n of e.items)for(const a of r(this,Ms))if(a.isHandlingMimeForPasting(n.type)){a.paste(n,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(n){ht(`paste: "${n.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const n=[];for(const l of s){const h=yield i.deserialize(l);if(!h)return;n.push(h)}const a=()=>{for(const l of n)y(this,Ch,Qp).call(this,l);y(this,xh,Jp).call(this,n)},o=()=>{for(const l of n)l.remove()};this.addCommands({cmd:a,undo:o,mustExec:!0})}catch(n){ht(`paste: "${n.message}".`)}})}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),r(this,le)!==ot.NONE&&!this.isEditorHandlingKeyboard&&Wa._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,r(this,mn)&&(m(this,mn,!1),y(this,oo,$d).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}setEditingState(t){t?(y(this,Du,K0).call(this),y(this,Ah,Kp).call(this),y(this,be,Ne).call(this,{isEditing:r(this,le)!==ot.NONE,isEmpty:y(this,yr,Nl).call(this),hasSomethingToUndo:r(this,ys).hasSomethingToUndo(),hasSomethingToRedo:r(this,ys).hasSomethingToRedo(),hasSelectedEditor:!1})):(y(this,Fu,Z0).call(this),y(this,Sh,Zp).call(this),y(this,be,Ne).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!r(this,Ms)){m(this,Ms,t);for(const e of r(this,Ms))y(this,Ls,Ai).call(this,e.defaultPropertiesToUpdate)}}getId(){return r(this,Pu).id}get currentLayer(){return r(this,me).get(r(this,io))}getLayer(t){return r(this,me).get(t)}get currentPageIndex(){return r(this,io)}addLayer(t){r(this,me).set(t.pageIndex,t),r(this,bn)?t.enable():t.disable()}removeLayer(t){r(this,me).delete(t.pageIndex)}updateMode(t,e=null,s=!1){return H(this,null,function*(){var i,n,a;if(r(this,le)!==t&&!(r(this,_s)&&(yield r(this,_s).promise,!r(this,_s)))){if(m(this,_s,Promise.withResolvers()),(i=r(this,pn))==null||i.commitOrRemove(),m(this,le,t),t===ot.NONE){this.setEditingState(!1),y(this,Hu,eb).call(this),(n=this._editorUndoBar)==null||n.hide(),r(this,_s).resolve();return}t===ot.SIGNATURE&&(yield(a=r(this,_n))==null?void 0:a.loadSignatures()),this.setEditingState(!0),yield y(this,$u,tb).call(this),this.unselectAll();for(const o of r(this,me).values())o.updateMode(t);if(!e){s&&this.addNewEditorFromKeyboard(),r(this,_s).resolve();return}for(const o of r(this,ie).values())o.annotationElementId===e?(this.setSelected(o),o.enterInEditMode()):o.unselect();r(this,_s).resolve()}})}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==r(this,le)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){var s;if(r(this,Ms)){switch(t){case vt.CREATE:this.currentLayer.addNewEditor(e);return;case vt.HIGHLIGHT_DEFAULT_COLOR:(s=r(this,br))==null||s.updateColor(e);break;case vt.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(r(this,bh)||m(this,bh,new Map)).set(t,e),this.showAllEditors("highlight",e);break}for(const i of r(this,wt))i.updateParams(t,e);for(const i of r(this,Ms))i.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){var n,a;for(const o of r(this,ie).values())o.editorType===t&&o.show(e);((a=(n=r(this,bh))==null?void 0:n.get(vt.HIGHLIGHT_SHOW_ALL))!=null?a:!0)!==e&&y(this,Ls,Ai).call(this,[[vt.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(r(this,gh)!==t){m(this,gh,t);for(const e of r(this,me).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of r(this,ie).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return r(this,ie).get(t)}addEditor(t){r(this,ie).set(t.id,t)}removeEditor(t){var e,s;t.div.contains(document.activeElement)&&(r(this,ti)&&clearTimeout(r(this,ti)),m(this,ti,setTimeout(()=>{this.focusMainContainer(),m(this,ti,null)},0))),r(this,ie).delete(t.id),t.annotationElementId&&((e=r(this,yn))==null||e.delete(t.annotationElementId)),this.unselect(t),(!t.annotationElementId||!r(this,pr).has(t.annotationElementId))&&((s=r(this,Ti))==null||s.remove(t.id))}addDeletedAnnotationElement(t){r(this,pr).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return r(this,pr).has(t)}removeDeletedAnnotationElement(t){r(this,pr).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){r(this,Qe)!==t&&(m(this,Qe,t),t&&y(this,Ls,Ai).call(this,t.propertiesToUpdate))}updateUI(t){r(this,Bu,sb)===t&&y(this,Ls,Ai).call(this,t.propertiesToUpdate)}updateUIForDefaultProperties(t){y(this,Ls,Ai).call(this,t.defaultPropertiesToUpdate)}toggleSelected(t){if(r(this,wt).has(t)){r(this,wt).delete(t),t.unselect(),y(this,be,Ne).call(this,{hasSelectedEditor:this.hasSelection});return}r(this,wt).add(t),t.select(),y(this,Ls,Ai).call(this,t.propertiesToUpdate),y(this,be,Ne).call(this,{hasSelectedEditor:!0})}setSelected(t){var e;(e=r(this,pn))==null||e.commitOrRemove();for(const s of r(this,wt))s!==t&&s.unselect();r(this,wt).clear(),r(this,wt).add(t),t.select(),y(this,Ls,Ai).call(this,t.propertiesToUpdate),y(this,be,Ne).call(this,{hasSelectedEditor:!0})}isSelected(t){return r(this,wt).has(t)}get firstSelectedEditor(){return r(this,wt).values().next().value}unselect(t){t.unselect(),r(this,wt).delete(t),y(this,be,Ne).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return r(this,wt).size!==0}get isEnterHandled(){return r(this,wt).size===1&&this.firstSelectedEditor.isEnterHandled}undo(){var t;r(this,ys).undo(),y(this,be,Ne).call(this,{hasSomethingToUndo:r(this,ys).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:y(this,yr,Nl).call(this)}),(t=this._editorUndoBar)==null||t.hide()}redo(){r(this,ys).redo(),y(this,be,Ne).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:r(this,ys).hasSomethingToRedo(),isEmpty:y(this,yr,Nl).call(this)})}addCommands(t){r(this,ys).add(t),y(this,be,Ne).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:y(this,yr,Nl).call(this)})}cleanUndoStack(t){r(this,ys).cleanType(t)}delete(){var n;this.commitOrRemove();const t=(n=this.currentLayer)==null?void 0:n.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...r(this,wt)],s=()=>{var a;(a=this._editorUndoBar)==null||a.show(i,e.length===1?e[0].editorType:e.length);for(const o of e)o.remove()},i=()=>{for(const a of e)y(this,Ch,Qp).call(this,a)};this.addCommands({cmd:s,undo:i,mustExec:!0})}commitOrRemove(){var t;(t=r(this,Qe))==null||t.commitOrRemove()}hasSomethingToControl(){return r(this,Qe)||this.hasSelection}selectAll(){for(const t of r(this,wt))t.commit();y(this,xh,Jp).call(this,r(this,ie).values())}unselectAll(){var t;if(!(r(this,Qe)&&(r(this,Qe).commitOrRemove(),r(this,le)!==ot.NONE))&&!((t=r(this,pn))!=null&&t.commitOrRemove())&&this.hasSelection){for(const e of r(this,wt))e.unselect();r(this,wt).clear(),y(this,be,Ne).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;r(this,wn)[0]+=t,r(this,wn)[1]+=e;const[i,n]=r(this,wn),a=[...r(this,wt)],o=1e3;r(this,si)&&clearTimeout(r(this,si)),m(this,si,setTimeout(()=>{m(this,si,null),r(this,wn)[0]=r(this,wn)[1]=0,this.addCommands({cmd:()=>{for(const l of a)r(this,ie).has(l.id)&&(l.translateInPage(i,n),l.translationDone())},undo:()=>{for(const l of a)r(this,ie).has(l.id)&&(l.translateInPage(-i,-n),l.translationDone())},mustExec:!1})},o));for(const l of a)l.translateInPage(t,e),l.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),m(this,Js,new Map);for(const t of r(this,wt))r(this,Js).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!r(this,Js))return!1;this.disableUserSelect(!1);const t=r(this,Js);m(this,Js,null);let e=!1;for(const[{x:i,y:n,pageIndex:a},o]of t)o.newX=i,o.newY=n,o.newPageIndex=a,e||(e=i!==o.savedX||n!==o.savedY||a!==o.savedPageIndex);if(!e)return!1;const s=(i,n,a,o)=>{if(r(this,ie).has(i.id)){const l=r(this,me).get(o);l?i._setParentAndPosition(l,n,a):(i.pageIndex=o,i.x=n,i.y=a)}};return this.addCommands({cmd:()=>{for(const[i,{newX:n,newY:a,newPageIndex:o}]of t)s(i,n,a,o)},undo:()=>{for(const[i,{savedX:n,savedY:a,savedPageIndex:o}]of t)s(i,n,a,o)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(r(this,Js))for(const s of r(this,Js).keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){var t;return((t=this.getActive())==null?void 0:t.shouldGetKeyboardEvents())||r(this,wt).size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return r(this,Qe)===t}getActive(){return r(this,Qe)}getMode(){return r(this,le)}get imageManager(){return gt(this,"imageManager",new Wp)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let h=0,c=e.rangeCount;h<c;h++)if(!t.contains(e.getRangeAt(h).commonAncestorContainer))return null;const{x:s,y:i,width:n,height:a}=t.getBoundingClientRect();let o;switch(t.getAttribute("data-main-rotation")){case"90":o=(h,c,u,f)=>({x:(c-i)/a,y:1-(h+u-s)/n,width:f/a,height:u/n});break;case"180":o=(h,c,u,f)=>({x:1-(h+u-s)/n,y:1-(c+f-i)/a,width:u/n,height:f/a});break;case"270":o=(h,c,u,f)=>({x:1-(c+f-i)/a,y:(h-s)/n,width:f/a,height:u/n});break;default:o=(h,c,u,f)=>({x:(h-s)/n,y:(c-i)/a,width:u/n,height:f/a});break}const l=[];for(let h=0,c=e.rangeCount;h<c;h++){const u=e.getRangeAt(h);if(!u.collapsed)for(const{x:f,y:g,width:b,height:v}of u.getClientRects())b===0||v===0||l.push(o(f,g,b,v))}return l.length===0?null:l}addChangedExistingAnnotation({annotationElementId:t,id:e}){(r(this,so)||m(this,so,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){var e;(e=r(this,so))==null||e.delete(t)}renderAnnotationElement(t){var i;const e=(i=r(this,so))==null?void 0:i.get(t.data.id);if(!e)return;const s=r(this,Ti).getRawValue(e);s&&(r(this,le)===ot.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}setMissingCanvas(t,e,s){var n;const i=(n=r(this,yn))==null?void 0:n.get(t);i&&(i.setCanvas(e,s),r(this,yn).delete(t))}addMissingCanvas(t,e){(r(this,yn)||m(this,yn,new Map)).set(t,e)}};to=new WeakMap,Qe=new WeakMap,ie=new WeakMap,me=new WeakMap,eo=new WeakMap,Ti=new WeakMap,so=new WeakMap,ys=new WeakMap,fn=new WeakMap,pn=new WeakMap,io=new WeakMap,pr=new WeakMap,Js=new WeakMap,Ms=new WeakMap,gr=new WeakMap,uh=new WeakMap,fh=new WeakMap,no=new WeakMap,ph=new WeakMap,ti=new WeakMap,gn=new WeakMap,ro=new WeakMap,mn=new WeakMap,ei=new WeakMap,Pu=new WeakMap,bn=new WeakMap,gh=new WeakMap,vn=new WeakMap,mr=new WeakMap,br=new WeakMap,yn=new WeakMap,mh=new WeakMap,le=new WeakMap,wt=new WeakMap,ki=new WeakMap,_n=new WeakMap,vr=new WeakMap,bh=new WeakMap,vh=new WeakMap,wn=new WeakMap,si=new WeakMap,Ii=new WeakMap,yh=new WeakMap,_s=new WeakMap,ao=new WeakSet,Od=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},_h=new WeakSet,Xp=function(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of r(this,me).values())if(s.hasTextLayer(t))return s;return null},Ru=new WeakSet,q0=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=y(this,ao,Od).call(this,t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(r(this,ei)||m(this,ei,new d_(this)),r(this,ei).show(s,i,this.direction==="ltr"))},Mu=new WeakSet,X0=function(){var n,a,o;const t=document.getSelection();if(!t||t.isCollapsed){r(this,ki)&&((n=r(this,ei))==null||n.hide(),m(this,ki,null),y(this,be,Ne).call(this,{hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===r(this,ki))return;const i=y(this,ao,Od).call(this,t).closest(".textLayer");if(!i){r(this,ki)&&((a=r(this,ei))==null||a.hide(),m(this,ki,null),y(this,be,Ne).call(this,{hasSelectedText:!1}));return}if((o=r(this,ei))==null||o.hide(),m(this,ki,e),y(this,be,Ne).call(this,{hasSelectedText:!0}),!(r(this,le)!==ot.HIGHLIGHT&&r(this,le)!==ot.NONE)&&(r(this,le)===ot.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),m(this,mn,this.isShiftKeyDown),!this.isShiftKeyDown)){const l=r(this,le)===ot.HIGHLIGHT?y(this,_h,Xp).call(this,i):null;l==null||l.toggleDrawing();const h=new AbortController,c=this.combinedSignal(h),u=f=>{f.type==="pointerup"&&f.button!==0||(h.abort(),l==null||l.toggleDrawing(!0),f.type==="pointerup"&&y(this,oo,$d).call(this,"main_toolbar"))};window.addEventListener("pointerup",u,{signal:c}),window.addEventListener("blur",u,{signal:c})}},oo=new WeakSet,$d=function(t=""){r(this,le)===ot.HIGHLIGHT?this.highlightSelection(t):r(this,uh)&&y(this,Ru,q0).call(this)},Lu=new WeakSet,Y0=function(){document.addEventListener("selectionchange",y(this,Mu,X0).bind(this),{signal:this._signal})},Du=new WeakSet,K0=function(){if(r(this,gn))return;m(this,gn,new AbortController);const t=this.combinedSignal(r(this,gn));window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})},Fu=new WeakSet,Z0=function(){var t;(t=r(this,gn))==null||t.abort(),m(this,gn,null)},wh=new WeakSet,Yp=function(){if(r(this,vn))return;m(this,vn,new AbortController);const t=this.combinedSignal(r(this,vn));window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})},Nu=new WeakSet,Q0=function(){var t;(t=r(this,vn))==null||t.abort(),m(this,vn,null)},Ah=new WeakSet,Kp=function(){if(r(this,fn))return;m(this,fn,new AbortController);const t=this.combinedSignal(r(this,fn));document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})},Sh=new WeakSet,Zp=function(){var t;(t=r(this,fn))==null||t.abort(),m(this,fn,null)},Ou=new WeakSet,J0=function(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})},be=new WeakSet,Ne=function(t){Object.entries(t).some(([s,i])=>r(this,vh)[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(r(this,vh),t)}),r(this,le)===ot.HIGHLIGHT&&t.hasSelectedEditor===!1&&y(this,Ls,Ai).call(this,[[vt.HIGHLIGHT_FREE,!0]]))},Ls=new WeakSet,Ai=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},$u=new WeakSet,tb=function(){return H(this,null,function*(){if(!r(this,bn)){m(this,bn,!0);const t=[];for(const e of r(this,me).values())t.push(e.enable());yield Promise.all(t);for(const e of r(this,ie).values())e.enable()}})},Hu=new WeakSet,eb=function(){if(this.unselectAll(),r(this,bn)){m(this,bn,!1);for(const t of r(this,me).values())t.disable();for(const t of r(this,ie).values())t.disable()}},Ch=new WeakSet,Qp=function(t){const e=r(this,me).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},Bu=new WeakSet,sb=function(){let t=null;for(t of r(this,wt));return t},yr=new WeakSet,Nl=function(){if(r(this,ie).size===0)return!0;if(r(this,ie).size===1)for(const t of r(this,ie).values())return t.isEmpty();return!1},xh=new WeakSet,Jp=function(t){for(const e of r(this,wt))e.unselect();r(this,wt).clear();for(const e of t)e.isEmpty()||(r(this,wt).add(e),e.select());y(this,be,Ne).call(this,{hasSelectedEditor:this.hasSelection})},O(Wa,"TRANSLATE_SMALL",1),O(Wa,"TRANSLATE_BIG",10);let Ma=Wa;var he,ii,Ds,lo,ni,Je,ho,ri,qe,Pi,_r,ai,An,wr,Ol,co,Hd;const Oe=class Oe{constructor(t){p(this,wr);p(this,co);p(this,he,null);p(this,ii,!1);p(this,Ds,null);p(this,lo,null);p(this,ni,null);p(this,Je,null);p(this,ho,!1);p(this,ri,null);p(this,qe,null);p(this,Pi,null);p(this,_r,null);p(this,ai,!1);m(this,qe,t),m(this,ai,t._uiManager.useNewAltTextFlow),r(Oe,An)||m(Oe,An,Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"}))}static initialize(t){var e;(e=Oe._l10n)!=null||(Oe._l10n=t)}render(){return H(this,null,function*(){const t=m(this,Ds,document.createElement("button"));t.className="altText",t.tabIndex="0";const e=m(this,lo,document.createElement("span"));t.append(e),r(this,ai)?(t.classList.add("new"),t.setAttribute("data-l10n-id",r(Oe,An).missing),e.setAttribute("data-l10n-id",r(Oe,An)["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=r(this,qe)._uiManager._signal;t.addEventListener("contextmenu",qs,{signal:s}),t.addEventListener("pointerdown",n=>n.stopPropagation(),{signal:s});const i=n=>{n.preventDefault(),r(this,qe)._uiManager.editAltText(r(this,qe)),r(this,ai)&&r(this,qe)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:r(this,wr,Ol)}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",n=>{n.target===t&&n.key==="Enter"&&(m(this,ho,!0),i(n))},{signal:s}),yield y(this,co,Hd).call(this),t})}finish(){r(this,Ds)&&(r(this,Ds).focus({focusVisible:r(this,ho)}),m(this,ho,!1))}isEmpty(){return r(this,ai)?r(this,he)===null:!r(this,he)&&!r(this,ii)}hasData(){return r(this,ai)?r(this,he)!==null||!!r(this,Pi):this.isEmpty()}get guessedText(){return r(this,Pi)}setGuessedText(t){return H(this,null,function*(){r(this,he)===null&&(m(this,Pi,t),m(this,_r,yield Oe._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t})),y(this,co,Hd).call(this))})}toggleAltTextBadge(t=!1){var e;if(!r(this,ai)||r(this,he)){(e=r(this,ri))==null||e.remove(),m(this,ri,null);return}if(!r(this,ri)){const s=m(this,ri,document.createElement("div"));s.className="noAltTextBadge",r(this,qe).div.append(s)}r(this,ri).classList.toggle("hidden",!t)}serialize(t){let e=r(this,he);return!t&&r(this,Pi)===e&&(e=r(this,_r)),{altText:e,decorative:r(this,ii),guessedText:r(this,Pi),textWithDisclaimer:r(this,_r)}}get data(){return{altText:r(this,he),decorative:r(this,ii)}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:n=!1}){s&&(m(this,Pi,s),m(this,_r,i)),!(r(this,he)===t&&r(this,ii)===e)&&(n||(m(this,he,t),m(this,ii,e)),y(this,co,Hd).call(this))}toggle(t=!1){r(this,Ds)&&(!t&&r(this,Je)&&(clearTimeout(r(this,Je)),m(this,Je,null)),r(this,Ds).disabled=!t)}shown(){r(this,qe)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:r(this,wr,Ol)}})}destroy(){var t,e;(t=r(this,Ds))==null||t.remove(),m(this,Ds,null),m(this,lo,null),m(this,ni,null),(e=r(this,ri))==null||e.remove(),m(this,ri,null)}};he=new WeakMap,ii=new WeakMap,Ds=new WeakMap,lo=new WeakMap,ni=new WeakMap,Je=new WeakMap,ho=new WeakMap,ri=new WeakMap,qe=new WeakMap,Pi=new WeakMap,_r=new WeakMap,ai=new WeakMap,An=new WeakMap,wr=new WeakSet,Ol=function(){return r(this,he)&&"added"||r(this,he)===null&&this.guessedText&&"review"||"missing"},co=new WeakSet,Hd=function(){return H(this,null,function*(){var i,n,a;const t=r(this,Ds);if(!t)return;if(r(this,ai)){if(t.classList.toggle("done",!!r(this,he)),t.setAttribute("data-l10n-id",r(Oe,An)[r(this,wr,Ol)]),(i=r(this,lo))==null||i.setAttribute("data-l10n-id",r(Oe,An)[`${r(this,wr,Ol)}-label`]),!r(this,he)){(n=r(this,ni))==null||n.remove();return}}else{if(!r(this,he)&&!r(this,ii)){t.classList.remove("done"),(a=r(this,ni))==null||a.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=r(this,ni);if(!e){m(this,ni,e=document.createElement("span")),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${r(this,qe).id}`;const o=100,l=r(this,qe)._uiManager._signal;l.addEventListener("abort",()=>{clearTimeout(r(this,Je)),m(this,Je,null)},{once:!0}),t.addEventListener("mouseenter",()=>{m(this,Je,setTimeout(()=>{m(this,Je,null),r(this,ni).classList.add("show"),r(this,qe)._reportTelemetry({action:"alt_text_tooltip"})},o))},{signal:l}),t.addEventListener("mouseleave",()=>{var h;r(this,Je)&&(clearTimeout(r(this,Je)),m(this,Je,null)),(h=r(this,ni))==null||h.classList.remove("show")},{signal:l})}r(this,ii)?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=r(this,he)),e.parentNode||t.append(e);const s=r(this,qe).getElementForAltText();s==null||s.setAttribute("aria-describedby",e.id)})},p(Oe,An,null),O(Oe,"_l10n",null);let fu=Oe;var uo,Ar,Eh,Th,kh,Ih,Ph,Ri,Sr,Mi,Cr,Li,Vu,ib,zu,nb,Uu,rb;const Fm=class Fm{constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:n=null,onPinchEnd:a=null,signal:o}){p(this,Vu);p(this,zu);p(this,Uu);p(this,uo,void 0);p(this,Ar,!1);p(this,Eh,null);p(this,Th,void 0);p(this,kh,void 0);p(this,Ih,void 0);p(this,Ph,void 0);p(this,Ri,null);p(this,Sr,void 0);p(this,Mi,null);p(this,Cr,void 0);p(this,Li,null);m(this,uo,t),m(this,Eh,s),m(this,Th,e),m(this,kh,i),m(this,Ih,n),m(this,Ph,a),m(this,Cr,new AbortController),m(this,Sr,AbortSignal.any([o,r(this,Cr).signal])),t.addEventListener("touchstart",y(this,Vu,ib).bind(this),{passive:!1,signal:r(this,Sr)})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/an.pixelRatio}destroy(){var t,e;(t=r(this,Cr))==null||t.abort(),m(this,Cr,null),(e=r(this,Ri))==null||e.abort(),m(this,Ri,null)}};uo=new WeakMap,Ar=new WeakMap,Eh=new WeakMap,Th=new WeakMap,kh=new WeakMap,Ih=new WeakMap,Ph=new WeakMap,Ri=new WeakMap,Sr=new WeakMap,Mi=new WeakMap,Cr=new WeakMap,Li=new WeakMap,Vu=new WeakSet,ib=function(t){var i,n,a;if((i=r(this,Th))!=null&&i.call(this))return;if(t.touches.length===1){if(r(this,Ri))return;const o=m(this,Ri,new AbortController),l=AbortSignal.any([r(this,Sr),o.signal]),h=r(this,uo),c={capture:!0,signal:l,passive:!1},u=f=>{var g;f.pointerType==="touch"&&((g=r(this,Ri))==null||g.abort(),m(this,Ri,null))};h.addEventListener("pointerdown",f=>{f.pointerType==="touch"&&(Kt(f),u(f))},c),h.addEventListener("pointerup",u,c),h.addEventListener("pointercancel",u,c);return}if(!r(this,Li)){m(this,Li,new AbortController);const o=AbortSignal.any([r(this,Sr),r(this,Li).signal]),l=r(this,uo),h={signal:o,capture:!1,passive:!1};l.addEventListener("touchmove",y(this,zu,nb).bind(this),h);const c=y(this,Uu,rb).bind(this);l.addEventListener("touchend",c,h),l.addEventListener("touchcancel",c,h),h.capture=!0,l.addEventListener("pointerdown",Kt,h),l.addEventListener("pointermove",Kt,h),l.addEventListener("pointercancel",Kt,h),l.addEventListener("pointerup",Kt,h),(n=r(this,kh))==null||n.call(this)}if(Kt(t),t.touches.length!==2||(a=r(this,Eh))!=null&&a.call(this)){m(this,Mi,null);return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),m(this,Mi,{touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY})},zu=new WeakSet,nb=function(t){var S;if(!r(this,Mi)||t.touches.length!==2)return;Kt(t);let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:n}=e,{screenX:a,screenY:o}=s,l=r(this,Mi),{touch0X:h,touch0Y:c,touch1X:u,touch1Y:f}=l,g=u-h,b=f-c,v=a-i,_=o-n,w=Math.hypot(v,_)||1,A=Math.hypot(g,b)||1;if(!r(this,Ar)&&Math.abs(A-w)<=Fm.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(l.touch0X=i,l.touch0Y=n,l.touch1X=a,l.touch1Y=o,!r(this,Ar)){m(this,Ar,!0);return}const C=[(i+a)/2,(n+o)/2];(S=r(this,Ih))==null||S.call(this,C,A,w)},Uu=new WeakSet,rb=function(t){var e;t.touches.length>=2||(r(this,Li)&&(r(this,Li).abort(),m(this,Li,null),(e=r(this,Ph))==null||e.call(this)),r(this,Mi)&&(Kt(t),m(this,Mi,null),m(this,Ar,!1)))};let pu=Fm;var xr,Fs,Pt,fo,Sn,Rh,Er,ve,Tr,Di,Cn,Mh,kr,ts,Lh,Ir,Fi,oi,po,go,ws,Pr,Dh,Gu,Fh,tg,Nh,eg,mo,Bd,ju,ab,Wu,ob,Oh,sg,bo,Vd,$h,ig,qu,lb,Xu,hb,Yu,cb,Hh,ng,Ku,db,Bh,rg,Zu,ub,Qu,fb,Ju,pb,Vh,ag,Rr,$l;const ft=class ft{constructor(t){p(this,Fh);p(this,mo);p(this,ju);p(this,Wu);p(this,Oh);p(this,bo);p(this,$h);p(this,qu);p(this,Xu);p(this,Yu);p(this,Hh);p(this,Ku);p(this,Bh);p(this,Zu);p(this,Qu);p(this,Ju);p(this,Vh);p(this,Rr);p(this,xr,null);p(this,Fs,null);p(this,Pt,null);p(this,fo,!1);p(this,Sn,null);p(this,Rh,"");p(this,Er,!1);p(this,ve,null);p(this,Tr,null);p(this,Di,null);p(this,Cn,null);p(this,Mh,"");p(this,kr,!1);p(this,ts,null);p(this,Lh,!1);p(this,Ir,!1);p(this,Fi,!1);p(this,oi,null);p(this,po,0);p(this,go,0);p(this,ws,null);p(this,Pr,null);O(this,"_isCopy",!1);O(this,"_editToolbar",null);O(this,"_initialOptions",Object.create(null));O(this,"_initialData",null);O(this,"_isVisible",!0);O(this,"_uiManager",null);O(this,"_focusEventsAllowed",!0);p(this,Dh,!1);p(this,Gu,ft._zIndex++);this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:n,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[n,a];const[o,l]=this.parentDimensions;this.x=t.x/o,this.y=t.y/l,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const t=ft.prototype._resizeWithKeyboard,e=Ma.TRANSLATE_SMALL,s=Ma.TRANSLATE_BIG;return gt(this,"_resizerKeyboardManager",new wd([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],ft.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return gt(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new p_({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){var i;if((i=ft._l10n)!=null||(ft._l10n=t),ft._l10nResizer||(ft._l10nResizer=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"})),ft._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);ft._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){xt("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return r(this,Dh)}set _isDraggable(t){var e;m(this,Dh,t),(e=this.div)==null||e.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=r(this,Gu)}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):y(this,Rr,$l).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(r(this,kr)?m(this,kr,!1):this.parent.setSelected(this))}focusout(t){var s;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const e=t.relatedTarget;e!=null&&e.closest(`#${this.id}`)||(t.preventDefault(),(s=this.parent)!=null&&s.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[n,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/n,this.y=(e+i)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[s,i]=this.parentDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this._onTranslated()}translate(t,e){y(this,Fh,tg).call(this,this.parentDimensions,t,e)}translateInPage(t,e){r(this,ts)||m(this,ts,[this.x,this.y,this.width,this.height]),y(this,Fh,tg).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){r(this,ts)||m(this,ts,[this.x,this.y,this.width,this.height]);const{div:s,parentDimensions:[i,n]}=this;if(this.x+=t/i,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:u,y:f}=this.div.getBoundingClientRect();this.parent.findNewParent(this,u,f)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:o}=this;const[l,h]=this.getBaseTranslation();a+=l,o+=h;const{style:c}=s;c.left=`${(100*a).toFixed(2)}%`,c.top=`${(100*o).toFixed(2)}%`,this._onTranslating(a,o),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!r(this,ts)&&(r(this,ts)[0]!==this.x||r(this,ts)[1]!==this.y)}get _hasBeenResized(){return!!r(this,ts)&&(r(this,ts)[2]!==this.width||r(this,ts)[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=ft,i=s/t,n=s/e;switch(this.rotation){case 90:return[-i,n];case 180:return[i,n];case 270:return[i,-n];default:return[-i,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:n,y:a,width:o,height:l}=this;if(o*=s,l*=i,n*=s,a*=i,this._mustFixPosition)switch(t){case 0:n=Ue(n,0,s-o),a=Ue(a,0,i-l);break;case 90:n=Ue(n,0,s-l),a=Ue(a,o,i);break;case 180:n=Ue(n,o,s),a=Ue(a,l,i);break;case 270:n=Ue(n,l,s),a=Ue(a,0,i-o);break}this.x=n/=s,this.y=a/=i;const[h,c]=this.getBaseTranslation();n+=h,a+=c,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(t,e){var s;return y(s=ft,Nh,eg).call(s,t,e,this.parentRotation)}pageTranslationToScreen(t,e){var s;return y(s=ft,Nh,eg).call(s,t,e,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/s).toFixed(2)}%`,r(this,Er)||(n.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),n=!r(this,Er)&&e.endsWith("%");if(i&&n)return;const[a,o]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),!r(this,Er)&&!n&&(t.height=`${(100*parseFloat(e)/o).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}_onResized(){}static _round(t){return Math.round(t*1e4)/1e4}_onResizing(){}altTextFinish(){var t;(t=r(this,Pt))==null||t.finish()}addEditToolbar(){return H(this,null,function*(){return this._editToolbar||r(this,Ir)?this._editToolbar:(this._editToolbar=new jp(this),this.div.append(this._editToolbar.render()),r(this,Pt)&&(yield this._editToolbar.addAltText(r(this,Pt))),this._editToolbar)})}removeEditToolbar(){var t;this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,(t=r(this,Pt))==null||t.destroy())}addContainer(t){var s;const e=(s=this._editToolbar)==null?void 0:s.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}addAltTextButton(){return H(this,null,function*(){r(this,Pt)||(fu.initialize(ft._l10n),m(this,Pt,new fu(this)),r(this,xr)&&(r(this,Pt).data=r(this,xr),m(this,xr,null)),yield this.addEditToolbar())})}get altTextData(){var t;return(t=r(this,Pt))==null?void 0:t.data}set altTextData(t){r(this,Pt)&&(r(this,Pt).data=t)}get guessedAltText(){var t;return(t=r(this,Pt))==null?void 0:t.guessedText}setGuessedAltText(t){return H(this,null,function*(){var e;yield(e=r(this,Pt))==null?void 0:e.setGuessedText(t)})}serializeAltText(t){var e;return(e=r(this,Pt))==null?void 0:e.serialize(t)}hasAltText(){return!!r(this,Pt)&&!r(this,Pt).isEmpty()}hasAltTextData(){var t,e;return(e=(t=r(this,Pt))==null?void 0:t.hasData())!=null?e:!1}render(){var a;const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=r(this,fo)?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),y(this,Bh,rg).call(this);const[e,s]=this.parentDimensions;this.parentRotation%180!==0&&(t.style.maxWidth=`${(100*s/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/s).toFixed(2)}%`);const[i,n]=this.getInitialTranslation();return this.translate(i,n),Em(this,t,["keydown","pointerdown"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(r(this,Pr)||m(this,Pr,new pu({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:y(this,qu,lb).bind(this),onPinching:y(this,Xu,hb).bind(this),onPinchEnd:y(this,Yu,cb).bind(this),signal:this._uiManager._signal}))),(a=this._uiManager._editorUndoBar)==null||a.hide(),t}pointerdown(t){const{isMac:e}=Se.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(m(this,kr,!0),this._isDraggable){y(this,Ku,db).call(this,t);return}y(this,Hh,ng).call(this,t)}get isSelected(){return this._uiManager.isSelected(this)}_onStartDragging(){}_onStopDragging(){}moveInDOM(){r(this,oi)&&clearTimeout(r(this,oi)),m(this,oi,setTimeout(()=>{var t;m(this,oi,null),(t=this.parent)==null||t.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[n,a]=this.pageDimensions,[o,l]=this.pageTranslation,h=t/i,c=e/i,u=this.x*n,f=this.y*a,g=this.width*n,b=this.height*a;switch(s){case 0:return[u+h+o,a-f-c-b+l,u+h+g+o,a-f-c+l];case 90:return[u+c+o,a-f+h+l,u+c+b+o,a-f+h+g+l];case 180:return[u-h-g+o,a-f+c+l,u-h+o,a-f+c+b+l];case 270:return[u-c-b+o,a-f-h-g+l,u-c+o,a-f-h+l];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,n,a]=t,o=n-s,l=a-i;switch(this.rotation){case 0:return[s,e-a,o,l];case 90:return[s,e-i,l,o];case 180:return[n,e-i,o,l];case 270:return[n,e-a,l,o];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){m(this,Ir,!0)}disableEditMode(){m(this,Ir,!1)}isInEditMode(){return r(this,Ir)}shouldGetKeyboardEvents(){return r(this,Fi)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:n,innerWidth:a}=window;return e<a&&i>0&&t<n&&s>0}rebuild(){y(this,Bh,rg).call(this)}rotate(t){}resize(){}serializeDeleted(){var t;return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:((t=this._initialData)==null?void 0:t.popupRef)||""}}serialize(t=!1,e=null){xt("An editor must be serializable")}static deserialize(t,e,s){return H(this,null,function*(){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s});i.rotation=t.rotation,m(i,xr,t.accessibilityData),i._isCopy=t.isCopy||!1;const[n,a]=i.pageDimensions,[o,l,h,c]=i.getRectInCurrentCoords(t.rect,a);return i.x=o/n,i.y=l/a,i.width=h/n,i.height=c/a,i})}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){var t,e;if((t=r(this,Cn))==null||t.abort(),m(this,Cn,null),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),r(this,oi)&&(clearTimeout(r(this,oi)),m(this,oi,null)),y(this,Rr,$l).call(this),this.removeEditToolbar(),r(this,ws)){for(const s of r(this,ws).values())clearTimeout(s);m(this,ws,null)}this.parent=null,(e=r(this,Pr))==null||e.destroy(),m(this,Pr,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(y(this,ju,ab).call(this),r(this,ve).classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),m(this,Di,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const e=r(this,ve).children;if(!r(this,Fs)){m(this,Fs,Array.from(e));const a=y(this,Zu,ub).bind(this),o=y(this,Qu,fb).bind(this),l=this._uiManager._signal;for(const h of r(this,Fs)){const c=h.getAttribute("data-resizer-name");h.setAttribute("role","spinbutton"),h.addEventListener("keydown",a,{signal:l}),h.addEventListener("blur",o,{signal:l}),h.addEventListener("focus",y(this,Ju,pb).bind(this,c),{signal:l}),h.setAttribute("data-l10n-id",ft._l10nResizer[c])}}const s=r(this,Fs)[0];let i=0;for(const a of e){if(a===s)break;i++}const n=(360-this.rotation+this.parentRotation)%360/90*(r(this,Fs).length/4);if(n!==i){if(n<i)for(let o=0;o<i-n;o++)r(this,ve).append(r(this,ve).firstChild);else if(n>i)for(let o=0;o<n-i;o++)r(this,ve).firstChild.before(r(this,ve).lastChild);let a=0;for(const o of e){const h=r(this,Fs)[a++].getAttribute("data-resizer-name");o.setAttribute("data-l10n-id",ft._l10nResizer[h])}}y(this,Vh,ag).call(this,0),m(this,Fi,!0),r(this,ve).firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}_resizeWithKeyboard(t,e){r(this,Fi)&&y(this,$h,ig).call(this,r(this,Mh),{deltaX:t,deltaY:e,fromKeyboard:!0})}_stopResizingWithKeyboard(){y(this,Rr,$l).call(this),this.div.focus()}select(){var t,e,s;if(this.makeResizable(),(t=this.div)==null||t.classList.add("selectedEditor"),!this._editToolbar){this.addEditToolbar().then(()=>{var i,n;(i=this.div)!=null&&i.classList.contains("selectedEditor")&&((n=this._editToolbar)==null||n.show())});return}(e=this._editToolbar)==null||e.show(),(s=r(this,Pt))==null||s.toggleAltTextBadge(!1)}unselect(){var t,e,s,i,n;(t=r(this,ve))==null||t.classList.add("hidden"),(e=this.div)==null||e.classList.remove("selectedEditor"),(s=this.div)!=null&&s.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),(i=this._editToolbar)==null||i.hide(),(n=r(this,Pt))==null||n.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return r(this,Lh)}set isEditing(t){m(this,Lh,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){m(this,Er,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){r(this,ws)||m(this,ws,new Map);const{action:s}=t;let i=r(this,ws).get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),r(this,ws).delete(s),r(this,ws).size===0&&m(this,ws,null)},ft._telemetryTimeout),r(this,ws).set(s,i);return}t.type||(t.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),m(this,fo,!1)}disable(){this.div&&(this.div.tabIndex=-1),m(this,fo,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;(e==null?void 0:e.nodeName)==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}};xr=new WeakMap,Fs=new WeakMap,Pt=new WeakMap,fo=new WeakMap,Sn=new WeakMap,Rh=new WeakMap,Er=new WeakMap,ve=new WeakMap,Tr=new WeakMap,Di=new WeakMap,Cn=new WeakMap,Mh=new WeakMap,kr=new WeakMap,ts=new WeakMap,Lh=new WeakMap,Ir=new WeakMap,Fi=new WeakMap,oi=new WeakMap,po=new WeakMap,go=new WeakMap,ws=new WeakMap,Pr=new WeakMap,Dh=new WeakMap,Gu=new WeakMap,Fh=new WeakSet,tg=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()},Nh=new WeakSet,eg=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},mo=new WeakSet,Bd=function(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}},ju=new WeakSet,ab=function(){if(r(this,ve))return;m(this,ve,document.createElement("div")),r(this,ve).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");r(this,ve).append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",y(this,Wu,ob).bind(this,s),{signal:e}),i.addEventListener("contextmenu",qs,{signal:e}),i.tabIndex=-1}this.div.prepend(r(this,ve))},Wu=new WeakSet,ob=function(t,e){var c;e.preventDefault();const{isMac:s}=Se.platform;if(e.button!==0||e.ctrlKey&&s)return;(c=r(this,Pt))==null||c.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,m(this,Tr,[e.screenX,e.screenY]);const n=new AbortController,a=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",y(this,$h,ig).bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",Kt,{passive:!1,signal:a}),window.addEventListener("contextmenu",qs,{signal:a}),m(this,Di,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const o=this.parent.div.style.cursor,l=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const h=()=>{var u;n.abort(),this.parent.togglePointerEvents(!0),(u=r(this,Pt))==null||u.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=o,this.div.style.cursor=l,y(this,bo,Vd).call(this)};window.addEventListener("pointerup",h,{signal:a}),window.addEventListener("blur",h,{signal:a})},Oh=new WeakSet,sg=function(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[n,a]=this.parentDimensions;this.setDims(n*s,a*i),this.fixAndSetPosition(),this._onResized()},bo=new WeakSet,Vd=function(){if(!r(this,Di))return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=r(this,Di);m(this,Di,null);const n=this.x,a=this.y,o=this.width,l=this.height;n===t&&a===e&&o===s&&l===i||this.addCommands({cmd:y(this,Oh,sg).bind(this,n,a,o,l),undo:y(this,Oh,sg).bind(this,t,e,s,i),mustExec:!0})},$h=new WeakSet,ig=function(t,e){const[s,i]=this.parentDimensions,n=this.x,a=this.y,o=this.width,l=this.height,h=ft.MIN_SIZE/s,c=ft.MIN_SIZE/i,u=y(this,mo,Bd).call(this,this.rotation),f=(B,G)=>[u[0]*B+u[2]*G,u[1]*B+u[3]*G],g=y(this,mo,Bd).call(this,360-this.rotation),b=(B,G)=>[g[0]*B+g[2]*G,g[1]*B+g[3]*G];let v,_,w=!1,A=!1;switch(t){case"topLeft":w=!0,v=(B,G)=>[0,0],_=(B,G)=>[B,G];break;case"topMiddle":v=(B,G)=>[B/2,0],_=(B,G)=>[B/2,G];break;case"topRight":w=!0,v=(B,G)=>[B,0],_=(B,G)=>[0,G];break;case"middleRight":A=!0,v=(B,G)=>[B,G/2],_=(B,G)=>[0,G/2];break;case"bottomRight":w=!0,v=(B,G)=>[B,G],_=(B,G)=>[0,0];break;case"bottomMiddle":v=(B,G)=>[B/2,G],_=(B,G)=>[B/2,0];break;case"bottomLeft":w=!0,v=(B,G)=>[0,G],_=(B,G)=>[B,0];break;case"middleLeft":A=!0,v=(B,G)=>[0,G/2],_=(B,G)=>[B,G/2];break}const C=v(o,l),S=_(o,l);let T=f(...S);const x=ft._round(n+T[0]),k=ft._round(a+T[1]);let I=1,M=1,L,F;if(e.fromKeyboard)({deltaX:L,deltaY:F}=e);else{const{screenX:B,screenY:G}=e,[kt,Bt]=r(this,Tr);[L,F]=this.screenToPageTranslation(B-kt,G-Bt),r(this,Tr)[0]=B,r(this,Tr)[1]=G}if([L,F]=b(L/s,F/i),w){const B=Math.hypot(o,l);I=M=Math.max(Math.min(Math.hypot(S[0]-C[0]-L,S[1]-C[1]-F)/B,1/o,1/l),h/o,c/l)}else A?I=Ue(Math.abs(S[0]-C[0]-L),h,1)/o:M=Ue(Math.abs(S[1]-C[1]-F),c,1)/l;const it=ft._round(o*I),at=ft._round(l*M);T=f(..._(it,at));const J=x-T[0],tt=k-T[1];r(this,ts)||m(this,ts,[this.x,this.y,this.width,this.height]),this.width=it,this.height=at,this.x=J,this.y=tt,this.setDims(s*it,i*at),this.fixAndSetPosition(),this._onResizing()},qu=new WeakSet,lb=function(){var t;m(this,Di,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height}),(t=r(this,Pt))==null||t.toggle(!1),this.parent.togglePointerEvents(!1)},Xu=new WeakSet,hb=function(t,e,s){let n=.7*(s/e)+1-.7;if(n===1)return;const a=y(this,mo,Bd).call(this,this.rotation),o=(x,k)=>[a[0]*x+a[2]*k,a[1]*x+a[3]*k],[l,h]=this.parentDimensions,c=this.x,u=this.y,f=this.width,g=this.height,b=ft.MIN_SIZE/l,v=ft.MIN_SIZE/h;n=Math.max(Math.min(n,1/f,1/g),b/f,v/g);const _=ft._round(f*n),w=ft._round(g*n);if(_===f&&w===g)return;r(this,ts)||m(this,ts,[c,u,f,g]);const A=o(f/2,g/2),C=ft._round(c+A[0]),S=ft._round(u+A[1]),T=o(_/2,w/2);this.x=C-T[0],this.y=S-T[1],this.width=_,this.height=w,this.setDims(l*_,h*w),this.fixAndSetPosition(),this._onResizing()},Yu=new WeakSet,cb=function(){var t;(t=r(this,Pt))==null||t.toggle(!0),this.parent.togglePointerEvents(!0),y(this,bo,Vd).call(this)},Hh=new WeakSet,ng=function(t){const{isMac:e}=Se.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},Ku=new WeakSet,db=function(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,n=this._uiManager.combinedSignal(i),a={capture:!0,passive:!1,signal:n},o=h=>{i.abort(),m(this,Sn,null),m(this,kr,!1),this._uiManager.endDragSession()||y(this,Hh,ng).call(this,h),s&&this._onStopDragging()};e&&(m(this,po,t.clientX),m(this,go,t.clientY),m(this,Sn,t.pointerId),m(this,Rh,t.pointerType),window.addEventListener("pointermove",h=>{s||(s=!0,this._onStartDragging());const{clientX:c,clientY:u,pointerId:f}=h;if(f!==r(this,Sn)){Kt(h);return}const[g,b]=this.screenToPageTranslation(c-r(this,po),u-r(this,go));m(this,po,c),m(this,go,u),this._uiManager.dragSelectedEditors(g,b)},a),window.addEventListener("touchmove",Kt,a),window.addEventListener("pointerdown",h=>{h.pointerType===r(this,Rh)&&(r(this,Pr)||h.isPrimary)&&o(h),Kt(h)},a));const l=h=>{if(!r(this,Sn)||r(this,Sn)===h.pointerId){o(h);return}Kt(h)};window.addEventListener("pointerup",l,{signal:n}),window.addEventListener("blur",l,{signal:n})},Bh=new WeakSet,rg=function(){if(r(this,Cn)||!this.div)return;m(this,Cn,new AbortController);const t=this._uiManager.combinedSignal(r(this,Cn));this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})},Zu=new WeakSet,ub=function(t){ft._resizerKeyboardManager.exec(this,t)},Qu=new WeakSet,fb=function(t){var e;r(this,Fi)&&((e=t.relatedTarget)==null?void 0:e.parentNode)!==r(this,ve)&&y(this,Rr,$l).call(this)},Ju=new WeakSet,pb=function(t){m(this,Mh,r(this,Fi)?t:"")},Vh=new WeakSet,ag=function(t){if(r(this,Fs))for(const e of r(this,Fs))e.tabIndex=t},Rr=new WeakSet,$l=function(){m(this,Fi,!1),y(this,Vh,ag).call(this,-1),y(this,bo,Vd).call(this)},p(ft,Nh),O(ft,"_l10n",null),O(ft,"_l10nResizer",null),O(ft,"_borderLineWidth",-1),O(ft,"_colorManager",new qp),O(ft,"_zIndex",1),O(ft,"_telemetryTimeout",1e3);let It=ft;class p_ extends It{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const Zm=3285377520,bs=4294901760,Zs=65535;class gb{constructor(t){this.h1=t?t&4294967295:Zm,this.h2=t?t&4294967295:Zm}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let v=0,_=t.length;v<_;v++){const w=t.charCodeAt(v);w<=255?e[s++]=w:(e[s++]=w>>>8,e[s++]=w&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,n=s-i*4,a=new Uint32Array(e.buffer,0,i);let o=0,l=0,h=this.h1,c=this.h2;const u=3432918353,f=461845907,g=u&Zs,b=f&Zs;for(let v=0;v<i;v++)v&1?(o=a[v],o=o*u&bs|o*g&Zs,o=o<<15|o>>>17,o=o*f&bs|o*b&Zs,h^=o,h=h<<13|h>>>19,h=h*5+3864292196):(l=a[v],l=l*u&bs|l*g&Zs,l=l<<15|l>>>17,l=l*f&bs|l*b&Zs,c^=l,c=c<<13|c>>>19,c=c*5+3864292196);switch(o=0,n){case 3:o^=e[i*4+2]<<16;case 2:o^=e[i*4+1]<<8;case 1:o^=e[i*4],o=o*u&bs|o*g&Zs,o=o<<15|o>>>17,o=o*f&bs|o*b&Zs,i&1?h^=o:c^=o}this.h1=h,this.h2=c}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&bs|t*36045&Zs,e=e*4283543511&bs|((e<<16|t>>>16)*2950163797&bs)>>>16,t^=e>>>1,t=t*444984403&bs|t*60499&Zs,e=e*3301882366&bs|((e<<16|t>>>16)*3120437893&bs)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const og=Object.freeze({map:null,hash:"",transfer:void 0});var Mr,Lr,ye,tf,mb;class Tm{constructor(){p(this,tf);p(this,Mr,!1);p(this,Lr,null);p(this,ye,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=r(this,ye).get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return r(this,ye).get(t)}remove(t){if(r(this,ye).delete(t),r(this,ye).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of r(this,ye).values())if(e instanceof It)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=r(this,ye).get(t);let i=!1;if(s!==void 0)for(const[n,a]of Object.entries(e))s[n]!==a&&(i=!0,s[n]=a);else i=!0,r(this,ye).set(t,e);i&&y(this,tf,mb).call(this),e instanceof It&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return r(this,ye).has(t)}get size(){return r(this,ye).size}resetModified(){r(this,Mr)&&(m(this,Mr,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new bb(this)}get serializable(){if(r(this,ye).size===0)return og;const t=new Map,e=new gb,s=[],i=Object.create(null);let n=!1;for(const[a,o]of r(this,ye)){const l=o instanceof It?o.serialize(!1,i):o;l&&(t.set(a,l),e.update(`${a}:${JSON.stringify(l)}`),n||(n=!!l.bitmap))}if(n)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:og}get editorStats(){var s;let t=null;const e=new Map;for(const i of r(this,ye).values()){if(!(i instanceof It))continue;const n=i.telemetryFinalData;if(!n)continue;const{type:a}=n;e.has(a)||e.set(a,Object.getPrototypeOf(i).constructor),t||(t=Object.create(null));const o=t[a]||(t[a]=new Map);for(const[l,h]of Object.entries(n)){if(l==="type")continue;let c=o.get(l);c||(c=new Map,o.set(l,c));const u=(s=c.get(h))!=null?s:0;c.set(h,u+1)}}for(const[i,n]of e)t[i]=n.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){m(this,Lr,null)}get modifiedIds(){if(r(this,Lr))return r(this,Lr);const t=[];for(const e of r(this,ye).values())!(e instanceof It)||!e.annotationElementId||!e.serialize()||t.push(e.annotationElementId);return m(this,Lr,{ids:new Set(t),hash:t.join(",")})}[Symbol.iterator](){return r(this,ye).entries()}}Mr=new WeakMap,Lr=new WeakMap,ye=new WeakMap,tf=new WeakSet,mb=function(){r(this,Mr)||(m(this,Mr,!0),typeof this.onSetModified=="function"&&this.onSetModified())};var zh;class bb extends Tm{constructor(e){super();p(this,zh,void 0);const{map:s,hash:i,transfer:n}=e.serializable,a=structuredClone(s,n?{transfer:n}:null);m(this,zh,{map:a,hash:i,transfer:n})}get print(){xt("Should not call PrintAnnotationStorage.print")}get serializable(){return r(this,zh)}get modifiedIds(){return gt(this,"modifiedIds",{ids:new Set,hash:""})}}zh=new WeakMap;var vo;class g_{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){p(this,vo,new Set);this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),r(this,vo).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}loadSystemFont(i){return H(this,arguments,function*({systemFontInfo:t,disableFontFace:e,_inspectFont:s}){if(!(!t||r(this,vo).has(t.loadedName))){if(Qt(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:n,src:a,style:o}=t,l=new FontFace(n,a,o);this.addNativeFontFace(l);try{yield l.load(),r(this,vo).add(n),s==null||s(t)}catch(h){ht(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(l)}return}xt("Not implemented: loadSystemFont without the Font Loading API.")}})}bind(t){return H(this,null,function*(){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){yield this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{yield s.loaded}catch(i){throw ht(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;yield new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}})}get isFontLoadingAPISupported(){var e;const t=!!((e=this._document)!=null&&e.fonts);return gt(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){return gt(this,"isSyncFontLoadingSupported",ze||Se.platform.isFirefox)}_queueLoadingCallback(t){function e(){for(Qt(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const n=s.shift();setTimeout(n.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return gt(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(S,T){return S.charCodeAt(T)<<24|S.charCodeAt(T+1)<<16|S.charCodeAt(T+2)<<8|S.charCodeAt(T+3)&255}function i(S,T,x,k){const I=S.substring(0,T),M=S.substring(T+x);return I+k+M}let n,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const l=o.getContext("2d");let h=0;function c(S,T){if(++h>30){ht("Load test font never loaded."),T();return}if(l.font="30px "+S,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0){T();return}setTimeout(c.bind(null,S,T))}const u=`lt${Date.now()}${this.loadTestFontId++}`;let f=this._loadTestFont;f=i(f,976,u.length,u);const b=16,v=1482184792;let _=s(f,b);for(n=0,a=u.length-3;n<a;n+=4)_=_-v+s(u,n)|0;n<u.length&&(_=_-v+s(u+"XXX",n)|0),f=i(f,b,4,t_(_));const w=`url(data:font/opentype;base64,${btoa(f)});`,A=`@font-face {font-family:"${u}";src:${w}}`;this.insertRule(A);const C=this._document.createElement("div");C.style.visibility="hidden",C.style.width=C.style.height="10px",C.style.position="absolute",C.style.top=C.style.left="0px";for(const S of[t.loadedName,u]){const T=this._document.createElement("span");T.textContent="Hi",T.style.fontFamily=S,C.append(T)}this._document.body.append(C),c(u,()=>{C.remove(),e.complete()})}}vo=new WeakMap;class m_{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const s in t)this[s]=t[s];this._inspectFont=e}createNativeFontFace(){var e;if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const s={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(s.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,s)}return(e=this._inspectFont)==null||e.call(this,this),t}createFontFaceRule(){var s;if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${$0(this.data)});`;let e;if(!this.cssFontInfo)e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;else{let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}return(s=this._inspectFont)==null||s.call(this,this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(a){ht(`getPathGenerator - ignoring character: "${a}".`)}const n=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=n}}const Td={DATA:1,ERROR:2},Xt={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function Qm(){}function je(d){if(d instanceof Jn||d instanceof zp||d instanceof Xm||d instanceof uu||d instanceof Ip)return d;switch(d instanceof Error||typeof d=="object"&&d!==null||xt('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),d.name){case"AbortException":return new Jn(d.message);case"InvalidPDFException":return new zp(d.message);case"PasswordException":return new Xm(d.message,d.code);case"ResponseException":return new uu(d.message,d.status,d.missing);case"UnknownErrorException":return new Ip(d.message,d.details)}return new Ip(d.message,d.toString())}var yo,ef,vb,sf,yb,nf,_b,_o,zd;class Hl{constructor(t,e,s){p(this,ef);p(this,sf);p(this,nf);p(this,_o);p(this,yo,new AbortController);this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",y(this,ef,vb).bind(this),{signal:r(this,yo).signal})}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){n.reject(a)}return n.promise}sendWithStream(t,e,s,i){const n=this.streamId++,a=this.sourceName,o=this.targetName,l=this.comObj;return new ReadableStream({start:h=>{const c=Promise.withResolvers();return this.streamControllers[n]={controller:h,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},l.postMessage({sourceName:a,targetName:o,action:t,streamId:n,data:e,desiredSize:h.desiredSize},i),c.promise},pull:h=>{const c=Promise.withResolvers();return this.streamControllers[n].pullCall=c,l.postMessage({sourceName:a,targetName:o,stream:Xt.PULL,streamId:n,desiredSize:h.desiredSize}),c.promise},cancel:h=>{Qt(h instanceof Error,"cancel must have a valid reason");const c=Promise.withResolvers();return this.streamControllers[n].cancelCall=c,this.streamControllers[n].isClosed=!0,l.postMessage({sourceName:a,targetName:o,stream:Xt.CANCEL,streamId:n,reason:je(h)}),c.promise}},s)}destroy(){var t;(t=r(this,yo))==null||t.abort(),m(this,yo,null)}}yo=new WeakMap,ef=new WeakSet,vb=function({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){y(this,nf,_b).call(this,t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===Td.DATA)i.resolve(t.data);else if(t.callback===Td.ERROR)i.reject(je(t.reason));else throw new Error("Unexpected callback case");return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,n=this.comObj;Promise.try(e,t.data).then(function(a){n.postMessage({sourceName:s,targetName:i,callback:Td.DATA,callbackId:t.callbackId,data:a})},function(a){n.postMessage({sourceName:s,targetName:i,callback:Td.ERROR,callbackId:t.callbackId,reason:je(a)})});return}if(t.streamId){y(this,sf,yb).call(this,t);return}e(t.data)},sf=new WeakSet,yb=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this,o=this.actionHandler[t.action],l={enqueue(h,c=1,u){if(this.isCancelled)return;const f=this.desiredSize;this.desiredSize-=c,f>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:i,stream:Xt.ENQUEUE,streamId:e,chunk:h},u)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Xt.CLOSE,streamId:e}),delete a.streamSinks[e])},error(h){Qt(h instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Xt.ERROR,streamId:e,reason:je(h)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};l.sinkCapability.resolve(),l.ready=l.sinkCapability.promise,this.streamSinks[e]=l,Promise.try(o,t.data,l).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Xt.START_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:Xt.START_COMPLETE,streamId:e,reason:je(h)})})},nf=new WeakSet,_b=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,a=this.streamControllers[e],o=this.streamSinks[e];switch(t.stream){case Xt.START_COMPLETE:t.success?a.startCall.resolve():a.startCall.reject(je(t.reason));break;case Xt.PULL_COMPLETE:t.success?a.pullCall.resolve():a.pullCall.reject(je(t.reason));break;case Xt.PULL:if(!o){n.postMessage({sourceName:s,targetName:i,stream:Xt.PULL_COMPLETE,streamId:e,success:!0});break}o.desiredSize<=0&&t.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=t.desiredSize,Promise.try(o.onPull||Qm).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Xt.PULL_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:Xt.PULL_COMPLETE,streamId:e,reason:je(h)})});break;case Xt.ENQUEUE:if(Qt(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case Xt.CLOSE:if(Qt(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),y(this,_o,zd).call(this,a,e);break;case Xt.ERROR:Qt(a,"error should have stream controller"),a.controller.error(je(t.reason)),y(this,_o,zd).call(this,a,e);break;case Xt.CANCEL_COMPLETE:t.success?a.cancelCall.resolve():a.cancelCall.reject(je(t.reason)),y(this,_o,zd).call(this,a,e);break;case Xt.CANCEL:if(!o)break;const l=je(t.reason);Promise.try(o.onCancel||Qm,l).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Xt.CANCEL_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:Xt.CANCEL_COMPLETE,streamId:e,reason:je(h)})}),o.sinkCapability.reject(l),o.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},_o=new WeakSet,zd=function(t,e){return H(this,null,function*(){var s,i,n;yield Promise.allSettled([(s=t.startCall)==null?void 0:s.promise,(i=t.pullCall)==null?void 0:i.promise,(n=t.cancelCall)==null?void 0:n.promise]),delete this.streamControllers[e]})};var Uh;class wb{constructor({enableHWA:t=!1}){p(this,Uh,!1);m(this,Uh,t)}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!r(this,Uh)})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){xt("Abstract method `_createCanvas` called.")}}Uh=new WeakMap;class b_ extends wb{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class Ab{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}fetch(e){return H(this,arguments,function*({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const s=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(s).then(i=>({cMapData:i,isCompressed:this.isCompressed})).catch(i=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${s}`)})})}_fetch(t){return H(this,null,function*(){xt("Abstract method `_fetch` called.")})}}class Jm extends Ab{_fetch(t){return H(this,null,function*(){const e=yield yd(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):vd(e)})}}class Sb{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,n){return"none"}destroy(t=!1){}}var Dr,wo,Ni,Oi,Ie,Fr,Nr,ce,Te,Or,Bl,xn,Ha,Ao,Ud,En,Ba,rf,Cb,Gh,lg,Tn,Va,$r,Vl,Hr,zl,jh,hg,Br,Ul;class v_ extends Sb{constructor({docId:e,ownerDocument:s=globalThis.document}){super();p(this,ce);p(this,Or);p(this,xn);p(this,Ao);p(this,En);p(this,rf);p(this,Gh);p(this,Tn);p(this,$r);p(this,Hr);p(this,jh);p(this,Br);p(this,Dr,void 0);p(this,wo,void 0);p(this,Ni,void 0);p(this,Oi,void 0);p(this,Ie,void 0);p(this,Fr,void 0);p(this,Nr,0);m(this,Oi,e),m(this,Ie,s)}addFilter(e){if(!e)return"none";let s=r(this,ce,Te).get(e);if(s)return s;const[i,n,a]=y(this,Ao,Ud).call(this,e),o=e.length===1?i:`${i}${n}${a}`;if(s=r(this,ce,Te).get(o),s)return r(this,ce,Te).set(e,s),s;const l=`g_${r(this,Oi)}_transfer_map_${Fe(this,Nr)._++}`,h=y(this,En,Ba).call(this,l);r(this,ce,Te).set(e,h),r(this,ce,Te).set(o,h);const c=y(this,Tn,Va).call(this,l);return y(this,Hr,zl).call(this,i,n,a,c),h}addHCMFilter(e,s){var b;const i=`${e}-${s}`,n="base";let a=r(this,Or,Bl).get(n);if((a==null?void 0:a.key)===i||(a?((b=a.filter)==null||b.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},r(this,Or,Bl).set(n,a)),!e||!s))return a.url;const o=y(this,Br,Ul).call(this,e);e=K.makeHexColor(...o);const l=y(this,Br,Ul).call(this,s);if(s=K.makeHexColor(...l),r(this,xn,Ha).style.color="",e==="#000000"&&s==="#ffffff"||e===s)return a.url;const h=new Array(256);for(let v=0;v<=255;v++){const _=v/255;h[v]=_<=.03928?_/12.92:oe((_+.055)/1.055,2.4)}const c=h.join(","),u=`g_${r(this,Oi)}_hcm_filter`,f=a.filter=y(this,Tn,Va).call(this,u);y(this,Hr,zl).call(this,c,c,c,f),y(this,Gh,lg).call(this,f);const g=(v,_)=>{const w=o[v]/255,A=l[v]/255,C=new Array(_+1);for(let S=0;S<=_;S++)C[S]=w+S/_*(A-w);return C.join(",")};return y(this,Hr,zl).call(this,g(0,5),g(1,5),g(2,5),f),a.url=y(this,En,Ba).call(this,u),a.url}addAlphaFilter(e){let s=r(this,ce,Te).get(e);if(s)return s;const[i]=y(this,Ao,Ud).call(this,[e]),n=`alpha_${i}`;if(s=r(this,ce,Te).get(n),s)return r(this,ce,Te).set(e,s),s;const a=`g_${r(this,Oi)}_alpha_map_${Fe(this,Nr)._++}`,o=y(this,En,Ba).call(this,a);r(this,ce,Te).set(e,o),r(this,ce,Te).set(n,o);const l=y(this,Tn,Va).call(this,a);return y(this,jh,hg).call(this,i,l),o}addLuminosityFilter(e){let s=r(this,ce,Te).get(e||"luminosity");if(s)return s;let i,n;if(e?([i]=y(this,Ao,Ud).call(this,[e]),n=`luminosity_${i}`):n="luminosity",s=r(this,ce,Te).get(n),s)return r(this,ce,Te).set(e,s),s;const a=`g_${r(this,Oi)}_luminosity_map_${Fe(this,Nr)._++}`,o=y(this,En,Ba).call(this,a);r(this,ce,Te).set(e,o),r(this,ce,Te).set(n,o);const l=y(this,Tn,Va).call(this,a);return y(this,rf,Cb).call(this,l),e&&y(this,jh,hg).call(this,i,l),o}addHighlightHCMFilter(e,s,i,n,a){var A;const o=`${s}-${i}-${n}-${a}`;let l=r(this,Or,Bl).get(e);if((l==null?void 0:l.key)===o||(l?((A=l.filter)==null||A.remove(),l.key=o,l.url="none",l.filter=null):(l={key:o,url:"none",filter:null},r(this,Or,Bl).set(e,l)),!s||!i))return l.url;const[h,c]=[s,i].map(y(this,Br,Ul).bind(this));let u=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),f=Math.round(.2126*c[0]+.7152*c[1]+.0722*c[2]),[g,b]=[n,a].map(y(this,Br,Ul).bind(this));f<u&&([u,f,g,b]=[f,u,b,g]),r(this,xn,Ha).style.color="";const v=(C,S,T)=>{const x=new Array(256),k=(f-u)/T,I=C/255,M=(S-C)/(255*T);let L=0;for(let F=0;F<=T;F++){const it=Math.round(u+F*k),at=I+F*M;for(let J=L;J<=it;J++)x[J]=at;L=it+1}for(let F=L;F<256;F++)x[F]=x[L-1];return x.join(",")},_=`g_${r(this,Oi)}_hcm_${e}_filter`,w=l.filter=y(this,Tn,Va).call(this,_);return y(this,Gh,lg).call(this,w),y(this,Hr,zl).call(this,v(g[0],b[0],5),v(g[1],b[1],5),v(g[2],b[2],5),w),l.url=y(this,En,Ba).call(this,_),l.url}destroy(e=!1){var s,i,n,a;e&&((s=r(this,Fr))!=null&&s.size)||((i=r(this,Ni))==null||i.parentNode.parentNode.remove(),m(this,Ni,null),(n=r(this,wo))==null||n.clear(),m(this,wo,null),(a=r(this,Fr))==null||a.clear(),m(this,Fr,null),m(this,Nr,0))}}Dr=new WeakMap,wo=new WeakMap,Ni=new WeakMap,Oi=new WeakMap,Ie=new WeakMap,Fr=new WeakMap,Nr=new WeakMap,ce=new WeakSet,Te=function(){return r(this,wo)||m(this,wo,new Map)},Or=new WeakSet,Bl=function(){return r(this,Fr)||m(this,Fr,new Map)},xn=new WeakSet,Ha=function(){if(!r(this,Ni)){const e=r(this,Ie).createElement("div"),{style:s}=e;s.visibility="hidden",s.contain="strict",s.width=s.height=0,s.position="absolute",s.top=s.left=0,s.zIndex=-1;const i=r(this,Ie).createElementNS(wi,"svg");i.setAttribute("width",0),i.setAttribute("height",0),m(this,Ni,r(this,Ie).createElementNS(wi,"defs")),e.append(i),i.append(r(this,Ni)),r(this,Ie).body.append(e)}return r(this,Ni)},Ao=new WeakSet,Ud=function(e){if(e.length===1){const h=e[0],c=new Array(256);for(let f=0;f<256;f++)c[f]=h[f]/255;const u=c.join(",");return[u,u,u]}const[s,i,n]=e,a=new Array(256),o=new Array(256),l=new Array(256);for(let h=0;h<256;h++)a[h]=s[h]/255,o[h]=i[h]/255,l[h]=n[h]/255;return[a.join(","),o.join(","),l.join(",")]},En=new WeakSet,Ba=function(e){if(r(this,Dr)===void 0){m(this,Dr,"");const s=r(this,Ie).URL;s!==r(this,Ie).baseURI&&(mp(s)?ht('#createUrl: ignore "data:"-URL for performance reasons.'):m(this,Dr,F0(s,"")))}return`url(${r(this,Dr)}#${e})`},rf=new WeakSet,Cb=function(e){const s=r(this,Ie).createElementNS(wi,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),e.append(s)},Gh=new WeakSet,lg=function(e){const s=r(this,Ie).createElementNS(wi,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(s)},Tn=new WeakSet,Va=function(e){const s=r(this,Ie).createElementNS(wi,"filter");return s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("id",e),r(this,xn,Ha).append(s),s},$r=new WeakSet,Vl=function(e,s,i){const n=r(this,Ie).createElementNS(wi,s);n.setAttribute("type","discrete"),n.setAttribute("tableValues",i),e.append(n)},Hr=new WeakSet,zl=function(e,s,i,n){const a=r(this,Ie).createElementNS(wi,"feComponentTransfer");n.append(a),y(this,$r,Vl).call(this,a,"feFuncR",e),y(this,$r,Vl).call(this,a,"feFuncG",s),y(this,$r,Vl).call(this,a,"feFuncB",i)},jh=new WeakSet,hg=function(e,s){const i=r(this,Ie).createElementNS(wi,"feComponentTransfer");s.append(i),y(this,$r,Vl).call(this,i,"feFuncA",e)},Br=new WeakSet,Ul=function(e){return r(this,xn,Ha).style.color=e,xm(getComputedStyle(r(this,xn,Ha)).getPropertyValue("color"))};class xb{constructor({baseUrl:t=null}){this.baseUrl=t}fetch(e){return H(this,arguments,function*({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const s=`${this.baseUrl}${t}`;return this._fetch(s).catch(i=>{throw new Error(`Unable to load font data at: ${s}`)})})}_fetch(t){return H(this,null,function*(){xt("Abstract method `_fetch` called.")})}}class t0 extends xb{_fetch(t){return H(this,null,function*(){const e=yield yd(t,"arraybuffer");return new Uint8Array(e)})}}class Eb{constructor({baseUrl:t=null}){this.baseUrl=t}fetch(e){return H(this,arguments,function*({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const s=`${this.baseUrl}${t}`;return this._fetch(s).catch(i=>{throw new Error(`Unable to load wasm data at: ${s}`)})})}_fetch(t){return H(this,null,function*(){xt("Abstract method `_fetch` called.")})}}class e0 extends Eb{_fetch(t){return H(this,null,function*(){const e=yield yd(t,"arraybuffer");return new Uint8Array(e)})}}ze&&ht("Please use the `legacy` build in Node.js environments.");function km(d){return H(this,null,function*(){const e=yield process.getBuiltinModule("fs").promises.readFile(d);return new Uint8Array(e)})}class y_ extends Sb{}class __ extends wb{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class w_ extends Ab{_fetch(t){return H(this,null,function*(){return km(t)})}}class A_ extends xb{_fetch(t){return H(this,null,function*(){return km(t)})}}class S_ extends Eb{_fetch(t){return H(this,null,function*(){return km(t)})}}const Ae={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function cg(d,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),d.clip(i)}class Im{isModifyingCurrentTransform(){return!1}getPattern(){xt("Abstract method `getPattern` called.")}}class C_ extends Im{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let n;if(i===Ae.STROKE||i===Ae.FILL){const a=e.current.getClippedPathBoundingBox(i,Dt(t))||[0,0,0,0],o=Math.ceil(a[2]-a[0])||1,l=Math.ceil(a[3]-a[1])||1,h=e.cachedCanvases.getCanvas("pattern",o,l),c=h.context;c.clearRect(0,0,c.canvas.width,c.canvas.height),c.beginPath(),c.rect(0,0,c.canvas.width,c.canvas.height),c.translate(-a[0],-a[1]),s=K.transform(s,[1,0,0,1,a[0],a[1]]),c.transform(...e.baseTransform),this.matrix&&c.transform(...this.matrix),cg(c,this._bbox),c.fillStyle=this._createGradient(c),c.fill(),n=t.createPattern(h.canvas,"no-repeat");const u=new DOMMatrix(s);n.setTransform(u)}else cg(t,this._bbox),n=this._createGradient(t);return n}}function Mp(d,t,e,s,i,n,a,o){const l=t.coords,h=t.colors,c=d.data,u=d.width*4;let f;l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=n,n=a,a=f),l[s+1]>l[i+1]&&(f=s,s=i,i=f,f=a,a=o,o=f),l[e+1]>l[s+1]&&(f=e,e=s,s=f,f=n,n=a,a=f);const g=(l[e]+t.offsetX)*t.scaleX,b=(l[e+1]+t.offsetY)*t.scaleY,v=(l[s]+t.offsetX)*t.scaleX,_=(l[s+1]+t.offsetY)*t.scaleY,w=(l[i]+t.offsetX)*t.scaleX,A=(l[i+1]+t.offsetY)*t.scaleY;if(b>=A)return;const C=h[n],S=h[n+1],T=h[n+2],x=h[a],k=h[a+1],I=h[a+2],M=h[o],L=h[o+1],F=h[o+2],it=Math.round(b),at=Math.round(A);let J,tt,B,G,kt,Bt,ge,xe;for(let Et=it;Et<=at;Et++){if(Et<_){const N=Et<b?0:(b-Et)/(b-_);J=g-(g-v)*N,tt=C-(C-x)*N,B=S-(S-k)*N,G=T-(T-I)*N}else{let N;Et>A?N=1:_===A?N=0:N=(_-Et)/(_-A),J=v-(v-w)*N,tt=x-(x-M)*N,B=k-(k-L)*N,G=I-(I-F)*N}let At;Et<b?At=0:Et>A?At=1:At=(b-Et)/(b-A),kt=g-(g-w)*At,Bt=C-(C-M)*At,ge=S-(S-L)*At,xe=T-(T-F)*At;const U=Math.round(Math.min(J,kt)),j=Math.round(Math.max(J,kt));let Y=u*Et+U*4;for(let N=U;N<=j;N++)At=(J-N)/(J-kt),At<0?At=0:At>1&&(At=1),c[Y++]=tt-(tt-Bt)*At|0,c[Y++]=B-(B-ge)*At|0,c[Y++]=G-(G-xe)*At|0,c[Y++]=255}}function x_(d,t,e){const s=t.coords,i=t.colors;let n,a;switch(t.type){case"lattice":const o=t.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(n=0;n<l;n++){let c=n*o;for(let u=0;u<h;u++,c++)Mp(d,e,s[c],s[c+1],s[c+o],i[c],i[c+1],i[c+o]),Mp(d,e,s[c+o+1],s[c+1],s[c+o],i[c+o+1],i[c+1],i[c+o])}break;case"triangles":for(n=0,a=s.length;n<a;n+=3)Mp(d,e,s[n],s[n+1],s[n+2],i[n],i[n+1],i[n+2]);break;default:throw new Error("illegal figure")}}class E_ extends Im{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,s){const o=Math.floor(this._bounds[0]),l=Math.floor(this._bounds[1]),h=Math.ceil(this._bounds[2])-o,c=Math.ceil(this._bounds[3])-l,u=Math.min(Math.ceil(Math.abs(h*t[0]*1.1)),3e3),f=Math.min(Math.ceil(Math.abs(c*t[1]*1.1)),3e3),g=h/u,b=c/f,v={coords:this._coords,colors:this._colors,offsetX:-o,offsetY:-l,scaleX:1/g,scaleY:1/b},_=u+2*2,w=f+2*2,A=s.getCanvas("mesh",_,w),C=A.context,S=C.createImageData(u,f);if(e){const x=S.data;for(let k=0,I=x.length;k<I;k+=4)x[k]=e[0],x[k+1]=e[1],x[k+2]=e[2],x[k+3]=255}for(const x of this._figures)x_(S,x,v);return C.putImageData(S,2,2),{canvas:A.canvas,offsetX:o-2*g,offsetY:l-2*b,scaleX:g,scaleY:b}}isModifyingCurrentTransform(){return!0}getPattern(t,e,s,i){cg(t,this._bbox);const n=new Float32Array(2);if(i===Ae.SHADING)K.singularValueDecompose2dScale(Dt(t),n);else if(this.matrix){K.singularValueDecompose2dScale(this.matrix,n);const[o,l]=n;K.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=o,n[1]*=l}else K.singularValueDecompose2dScale(e.baseTransform,n);const a=this._createMeshCanvas(n,i===Ae.SHADING?null:this._background,e.cachedCanvases);return i!==Ae.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class T_ extends Im{getPattern(){return"hotpink"}}function k_(d){switch(d[0]){case"RadialAxial":return new C_(d);case"Mesh":return new E_(d);case"Dummy":return new T_}throw new Error(`Unknown IR type: ${d[0]}`)}const s0={COLORED:1,UNCOLORED:2},af=class af{constructor(t,e,s,i){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=s,this.baseTransform=i}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:n,color:a,canvasGraphicsFactory:o}=this;let{xstep:l,ystep:h}=this;l=Math.abs(l),h=Math.abs(h),gp("TilingType: "+n);const c=e[0],u=e[1],f=e[2],g=e[3],b=f-c,v=g-u,_=new Float32Array(2);K.singularValueDecompose2dScale(this.matrix,_);const[w,A]=_;K.singularValueDecompose2dScale(this.baseTransform,_);const C=w*_[0],S=A*_[1];let T=b,x=v,k=!1,I=!1;const M=Math.ceil(l*C),L=Math.ceil(h*S),F=Math.ceil(b*C),it=Math.ceil(v*S);M>=F?T=l:k=!0,L>=it?x=h:I=!0;const at=this.getSizeAndScale(T,this.ctx.canvas.width,C),J=this.getSizeAndScale(x,this.ctx.canvas.height,S),tt=t.cachedCanvases.getCanvas("pattern",at.size,J.size),B=tt.context,G=o.createCanvasGraphics(B);if(G.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(G,i,a),B.translate(-at.scale*c,-J.scale*u),G.transform(at.scale,0,0,J.scale,0,0),B.save(),this.clipBbox(G,c,u,f,g),G.baseTransform=Dt(G.ctx),G.executeOperatorList(s),G.endDrawing(),B.restore(),k||I){const kt=tt.canvas;k&&(T=l),I&&(x=h);const Bt=this.getSizeAndScale(T,this.ctx.canvas.width,C),ge=this.getSizeAndScale(x,this.ctx.canvas.height,S),xe=Bt.size,Et=ge.size,At=t.cachedCanvases.getCanvas("pattern-workaround",xe,Et),U=At.context,j=k?Math.floor(b/l):0,Y=I?Math.floor(v/h):0;for(let N=0;N<=j;N++)for(let V=0;V<=Y;V++)U.drawImage(kt,xe*N,Et*V,xe,Et,0,0,xe,Et);return{canvas:At.canvas,scaleX:Bt.scale,scaleY:ge.scale,offsetX:c,offsetY:u}}return{canvas:tt.canvas,scaleX:at.scale,scaleY:J.scale,offsetX:c,offsetY:u}}getSizeAndScale(t,e,s){const i=Math.max(af.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*s);return n>=i?n=i:s=n/t,{scale:s,size:n}}clipBbox(t,e,s,i,n){const a=i-e,o=n-s;t.ctx.rect(e,s,a,o),K.axialAlignedBoundingBox([e,s,i,n],Dt(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,n=t.current;switch(e){case s0.COLORED:const a=this.ctx;i.fillStyle=a.fillStyle,i.strokeStyle=a.strokeStyle,n.fillColor=a.fillStyle,n.strokeColor=a.strokeStyle;break;case s0.UNCOLORED:const o=K.makeHexColor(s[0],s[1],s[2]);i.fillStyle=o,i.strokeStyle=o,n.fillColor=o,n.strokeColor=o;break;default:throw new Jy(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,s,i){let n=s;i!==Ae.SHADING&&(n=K.transform(n,e.baseTransform),this.matrix&&(n=K.transform(n,this.matrix)));const a=this.createPatternCanvas(e);let o=new DOMMatrix(n);o=o.translate(a.offsetX,a.offsetY),o=o.scale(1/a.scaleX,1/a.scaleY);const l=t.createPattern(a.canvas,"repeat");return l.setTransform(o),l}};O(af,"MAX_PATTERN_SIZE",3e3);let dg=af;function I_({src:d,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:n=4294967295,inverseDecode:a=!1}){const o=Se.isLittleEndian?4278190080:255,[l,h]=a?[n,o]:[o,n],c=s>>3,u=s&7,f=d.length;e=new Uint32Array(e.buffer);let g=0;for(let b=0;b<i;b++){for(const _=t+c;t<_;t++){const w=t<f?d[t]:255;e[g++]=w&128?h:l,e[g++]=w&64?h:l,e[g++]=w&32?h:l,e[g++]=w&16?h:l,e[g++]=w&8?h:l,e[g++]=w&4?h:l,e[g++]=w&2?h:l,e[g++]=w&1?h:l}if(u===0)continue;const v=t<f?d[t++]:255;for(let _=0;_<u;_++)e[g++]=v&1<<7-_?h:l}return{srcPos:t,destPos:g}}const i0=16,n0=100,P_=15,r0=10,Ke=16,Lp=new DOMMatrix,us=new Float32Array(2),ja=new Float32Array([1/0,1/0,-1/0,-1/0]);function R_(d,t){if(d._removeMirroring)throw new Error("Context is already forwarding operations.");d.__originalSave=d.save,d.__originalRestore=d.restore,d.__originalRotate=d.rotate,d.__originalScale=d.scale,d.__originalTranslate=d.translate,d.__originalTransform=d.transform,d.__originalSetTransform=d.setTransform,d.__originalResetTransform=d.resetTransform,d.__originalClip=d.clip,d.__originalMoveTo=d.moveTo,d.__originalLineTo=d.lineTo,d.__originalBezierCurveTo=d.bezierCurveTo,d.__originalRect=d.rect,d.__originalClosePath=d.closePath,d.__originalBeginPath=d.beginPath,d._removeMirroring=()=>{d.save=d.__originalSave,d.restore=d.__originalRestore,d.rotate=d.__originalRotate,d.scale=d.__originalScale,d.translate=d.__originalTranslate,d.transform=d.__originalTransform,d.setTransform=d.__originalSetTransform,d.resetTransform=d.__originalResetTransform,d.clip=d.__originalClip,d.moveTo=d.__originalMoveTo,d.lineTo=d.__originalLineTo,d.bezierCurveTo=d.__originalBezierCurveTo,d.rect=d.__originalRect,d.closePath=d.__originalClosePath,d.beginPath=d.__originalBeginPath,delete d._removeMirroring},d.save=function(){t.save(),this.__originalSave()},d.restore=function(){t.restore(),this.__originalRestore()},d.translate=function(e,s){t.translate(e,s),this.__originalTranslate(e,s)},d.scale=function(e,s){t.scale(e,s),this.__originalScale(e,s)},d.transform=function(e,s,i,n,a,o){t.transform(e,s,i,n,a,o),this.__originalTransform(e,s,i,n,a,o)},d.setTransform=function(e,s,i,n,a,o){t.setTransform(e,s,i,n,a,o),this.__originalSetTransform(e,s,i,n,a,o)},d.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},d.rotate=function(e){t.rotate(e),this.__originalRotate(e)},d.clip=function(e){t.clip(e),this.__originalClip(e)},d.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},d.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},d.bezierCurveTo=function(e,s,i,n,a,o){t.bezierCurveTo(e,s,i,n,a,o),this.__originalBezierCurveTo(e,s,i,n,a,o)},d.rect=function(e,s,i,n){t.rect(e,s,i,n),this.__originalRect(e,s,i,n)},d.closePath=function(){t.closePath(),this.__originalClosePath()},d.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class M_{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function kd(d,t,e,s,i,n,a,o,l,h){const[c,u,f,g,b,v]=Dt(d);if(u===0&&f===0){const A=a*c+b,C=Math.round(A),S=o*g+v,T=Math.round(S),x=(a+l)*c+b,k=Math.abs(Math.round(x)-C)||1,I=(o+h)*g+v,M=Math.abs(Math.round(I)-T)||1;return d.setTransform(Math.sign(c),0,0,Math.sign(g),C,T),d.drawImage(t,e,s,i,n,0,0,k,M),d.setTransform(c,u,f,g,b,v),[k,M]}if(c===0&&g===0){const A=o*f+b,C=Math.round(A),S=a*u+v,T=Math.round(S),x=(o+h)*f+b,k=Math.abs(Math.round(x)-C)||1,I=(a+l)*u+v,M=Math.abs(Math.round(I)-T)||1;return d.setTransform(0,Math.sign(u),Math.sign(f),0,C,T),d.drawImage(t,e,s,i,n,0,0,M,k),d.setTransform(c,u,f,g,b,v),[M,k]}d.drawImage(t,e,s,i,n,a,o,l,h);const _=Math.hypot(c,u),w=Math.hypot(f,g);return[_*l,w*h]}class a0{constructor(t,e){O(this,"alphaIsShape",!1);O(this,"fontSize",0);O(this,"fontSizeScale",1);O(this,"textMatrix",null);O(this,"textMatrixScale",1);O(this,"fontMatrix",Vp);O(this,"leading",0);O(this,"x",0);O(this,"y",0);O(this,"lineX",0);O(this,"lineY",0);O(this,"charSpacing",0);O(this,"wordSpacing",0);O(this,"textHScale",1);O(this,"textRenderingMode",Ee.FILL);O(this,"textRise",0);O(this,"fillColor","#000000");O(this,"strokeColor","#000000");O(this,"patternFill",!1);O(this,"patternStroke",!1);O(this,"fillAlpha",1);O(this,"strokeAlpha",1);O(this,"lineWidth",1);O(this,"activeSMask",null);O(this,"transferMaps","none");this.clipBox=new Float32Array([0,0,t,e]),this.minMax=ja.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=Ae.FILL,e=null){const s=this.minMax.slice();if(t===Ae.STROKE){e||xt("Stroke bounding box must include transform."),K.singularValueDecompose2dScale(e,us);const i=us[0]*this.lineWidth/2,n=us[1]*this.lineWidth/2;s[0]-=i,s[1]-=n,s[2]+=i,s[3]+=n}return s}updateClipFromPath(){const t=K.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(ja,0)}getClippedPathBoundingBox(t=Ae.FILL,e=null){return K.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function o0(d,t){if(t instanceof ImageData){d.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%Ke,n=(e-i)/Ke,a=i===0?n:n+1,o=d.createImageData(s,Ke);let l=0,h;const c=t.data,u=o.data;let f,g,b,v;if(t.kind===Ld.GRAYSCALE_1BPP){const _=c.byteLength,w=new Uint32Array(u.buffer,0,u.byteLength>>2),A=w.length,C=s+7>>3,S=4294967295,T=Se.isLittleEndian?4278190080:255;for(f=0;f<a;f++){for(b=f<n?Ke:i,h=0,g=0;g<b;g++){const x=_-l;let k=0;const I=x>C?s:x*8-7,M=I&-8;let L=0,F=0;for(;k<M;k+=8)F=c[l++],w[h++]=F&128?S:T,w[h++]=F&64?S:T,w[h++]=F&32?S:T,w[h++]=F&16?S:T,w[h++]=F&8?S:T,w[h++]=F&4?S:T,w[h++]=F&2?S:T,w[h++]=F&1?S:T;for(;k<I;k++)L===0&&(F=c[l++],L=128),w[h++]=F&L?S:T,L>>=1}for(;h<A;)w[h++]=0;d.putImageData(o,0,f*Ke)}}else if(t.kind===Ld.RGBA_32BPP){for(g=0,v=s*Ke*4,f=0;f<n;f++)u.set(c.subarray(l,l+v)),l+=v,d.putImageData(o,0,g),g+=Ke;f<a&&(v=s*i*4,u.set(c.subarray(l,l+v)),d.putImageData(o,0,g))}else if(t.kind===Ld.RGB_24BPP)for(b=Ke,v=s*b,f=0;f<a;f++){for(f>=n&&(b=i,v=s*b),h=0,g=v;g--;)u[h++]=c[l++],u[h++]=c[l++],u[h++]=c[l++],u[h++]=255;d.putImageData(o,0,f*Ke)}else throw new Error(`bad image kind: ${t.kind}`)}function l0(d,t){if(t.bitmap){d.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%Ke,n=(e-i)/Ke,a=i===0?n:n+1,o=d.createImageData(s,Ke);let l=0;const h=t.data,c=o.data;for(let u=0;u<a;u++){const f=u<n?Ke:i;({srcPos:l}=I_({src:h,srcPos:l,dest:c,width:s,height:f,nonBlackColor:0})),d.putImageData(o,0,u*Ke)}}function Pl(d,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)d[s]!==void 0&&(t[s]=d[s]);d.setLineDash!==void 0&&(t.setLineDash(d.getLineDash()),t.lineDashOffset=d.lineDashOffset)}function Id(d){d.strokeStyle=d.fillStyle="#000000",d.fillRule="nonzero",d.globalAlpha=1,d.lineWidth=1,d.lineCap="butt",d.lineJoin="miter",d.miterLimit=10,d.globalCompositeOperation="source-over",d.font="10px sans-serif",d.setLineDash!==void 0&&(d.setLineDash([]),d.lineDashOffset=0);const{filter:t}=d;t!=="none"&&t!==""&&(d.filter="none")}function h0(d,t){if(t)return!0;K.singularValueDecompose2dScale(d,us);const e=Math.fround(an.pixelRatio*tr.PDF_TO_CSS_UNITS);return us[0]<=e&&us[1]<=e}const L_=["butt","round","square"],D_=["miter","round","bevel"],F_={},c0={};var Wh,ug,qh,fg,Xh,pg;const Nm=class Nm{constructor(t,e,s,i,n,{optionalContentConfig:a,markedContentStack:o=null},l,h){p(this,Wh);p(this,qh);p(this,Xh);this.ctx=t,this.current=new a0(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=o||[],this.optionalContentConfig=a,this.cachedCanvases=new M_(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=l,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=h,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,n,a),this.ctx.fillStyle=o,s){const l=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform(...Dt(this.compositeCtx))}this.ctx.save(),Id(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=Dt(this.ctx)}executeOperatorList(t,e,s,i){const n=t.argsArray,a=t.fnArray;let o=e||0;const l=n.length;if(l===o)return o;const h=l-o>r0&&typeof s=="function",c=h?Date.now()+P_:0;let u=0;const f=this.commonObjs,g=this.objs;let b;for(;;){if(i!==void 0&&o===i.nextBreakPoint)return i.breakIt(o,s),o;if(b=a[o],b!==du.dependency)this[b].apply(this,n[o]);else for(const v of n[o]){const _=v.startsWith("g_")?f:g;if(!_.has(v))return _.get(v,s),o}if(o++,o===l)return o;if(h&&++u>r0){if(Date.now()>c)return s(),o;u=0}}}endDrawing(){y(this,Wh,ug).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement!="undefined"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),y(this,qh,fg).call(this)}_scaleImage(t,e){var f,g;const s=(f=t.width)!=null?f:t.displayWidth,i=(g=t.height)!=null?g:t.displayHeight;let n=Math.max(Math.hypot(e[0],e[1]),1),a=Math.max(Math.hypot(e[2],e[3]),1),o=s,l=i,h="prescale1",c,u;for(;n>2&&o>1||a>2&&l>1;){let b=o,v=l;n>2&&o>1&&(b=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o/2),n/=o/b),a>2&&l>1&&(v=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l)/2,a/=l/v),c=this.cachedCanvases.getCanvas(h,b,v),u=c.context,u.clearRect(0,0,b,v),u.drawImage(t,0,0,o,l,0,0,b,v),t=c.canvas,o=b,l=v,h=h==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:o,paintHeight:l}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,n=this.current.fillColor,a=this.current.patternFill,o=Dt(e);let l,h,c,u;if((t.bitmap||t.data)&&t.count>1){const M=t.bitmap||t.data.buffer;h=JSON.stringify(a?o:[o.slice(0,4),n]),l=this._cachedBitmapsMap.get(M),l||(l=new Map,this._cachedBitmapsMap.set(M,l));const L=l.get(h);if(L&&!a){const F=Math.round(Math.min(o[0],o[2])+o[4]),it=Math.round(Math.min(o[1],o[3])+o[5]);return{canvas:L,offsetX:F,offsetY:it}}c=L}c||(u=this.cachedCanvases.getCanvas("maskCanvas",s,i),l0(u.context,t));let f=K.transform(o,[1/s,0,0,-1/i,0,0]);f=K.transform(f,[1,0,0,1,0,-i]);const g=ja.slice();K.axialAlignedBoundingBox([0,0,s,i],f,g);const[b,v,_,w]=g,A=Math.round(_-b)||1,C=Math.round(w-v)||1,S=this.cachedCanvases.getCanvas("fillCanvas",A,C),T=S.context,x=b,k=v;T.translate(-x,-k),T.transform(...f),c||(c=this._scaleImage(u.canvas,Ks(T)),c=c.img,l&&a&&l.set(h,c)),T.imageSmoothingEnabled=h0(Dt(T),t.interpolate),kd(T,c,0,0,c.width,c.height,0,0,s,i),T.globalCompositeOperation="source-in";const I=K.transform(Ks(T),[1,0,0,1,-x,-k]);return T.fillStyle=a?n.getPattern(e,this,I,Ae.FILL):n,T.fillRect(0,0,s,i),l&&!a&&(this.cachedCanvases.delete("fillCanvas"),l.set(h,S.canvas)),{canvas:S.canvas,offsetX:Math.round(x),offsetY:Math.round(k)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=L_[t]}setLineJoin(t){this.ctx.lineJoin=D_[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=i.context;n.setTransform(this.suspendedCtx.getTransform()),Pl(this.suspendedCtx,n),R_(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),Pl(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const n=i[0],a=i[1],o=i[2]-n,l=i[3]-a;o===0||l===0||(this.genericComposeSMask(e.context,s,o,l,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,n,a,o,l,h,c,u){let f=t.canvas,g=l-c,b=h-u;if(a){const _=K.makeHexColor(...a);if(g<0||b<0||g+s>f.width||b+i>f.height){const w=this.cachedCanvases.getCanvas("maskExtension",s,i),A=w.context;A.drawImage(f,-g,-b),A.globalCompositeOperation="destination-atop",A.fillStyle=_,A.fillRect(0,0,s,i),A.globalCompositeOperation="source-over",f=w.canvas,g=b=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const w=new Path2D;w.rect(g,b,s,i),t.clip(w),t.globalCompositeOperation="destination-atop",t.fillStyle=_,t.fillRect(g,b,s,i),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),n==="Alpha"&&o?e.filter=this.filterFactory.addAlphaFilter(o):n==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(o));const v=new Path2D;v.rect(l,h,s,i),e.clip(v),e.globalCompositeOperation="destination-in",e.drawImage(f,g,b,s,i,l,h,s,i),e.restore()}save(){this.inSMaskMode&&Pl(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){if(this.stateStack.length===0){this.inSMaskMode&&this.endSMaskMode();return}this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&Pl(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}transform(t,e,s,i,n,a){this.ctx.transform(t,e,s,i,n,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){let[i]=e;if(!s){i||(i=e[0]=new Path2D),this[t](i);return}if(!(i instanceof Path2D)){const n=e[0]=new Path2D;for(let a=0,o=i.length;a<o;)switch(i[a++]){case Ed.moveTo:n.moveTo(i[a++],i[a++]);break;case Ed.lineTo:n.lineTo(i[a++],i[a++]);break;case Ed.curveTo:n.bezierCurveTo(i[a++],i[a++],i[a++],i[a++],i[a++],i[a++]);break;case Ed.closePath:n.closePath();break;default:ht(`Unrecognized drawing path operator: ${i[a-1]}`);break}i=n}K.axialAlignedBoundingBox(s,Dt(this.ctx),this.current.minMax),this[t](i)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const s=this.ctx,i=this.current.strokeColor;if(s.globalAlpha=this.current.strokeAlpha,this.contentVisible)if(typeof i=="object"&&(i!=null&&i.getPattern)){const n=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.strokeStyle=i.getPattern(s,this,Ks(s),Ae.STROKE),n){const a=new Path2D;a.addPath(t,s.getTransform().invertSelf().multiplySelf(n)),t=a}this.rescaleAndStroke(t,!1),s.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(Ae.STROKE,Dt(this.ctx))),s.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const s=this.ctx,i=this.current.fillColor,n=this.current.patternFill;let a=!1;if(n){const l=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.fillStyle=i.getPattern(s,this,Ks(s),Ae.FILL),l){const h=new Path2D;h.addPath(t,s.getTransform().invertSelf().multiplySelf(l)),t=h}a=!0}const o=this.current.getClippedPathBoundingBox();this.contentVisible&&o!==null&&(this.pendingEOFill?(s.fill(t,"evenodd"),this.pendingEOFill=!1):s.fill(t)),a&&s.restore(),e&&this.consumePath(t,o)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=F_}eoClip(){this.pendingClip=c0}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0)return;const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:n,x:a,y:o,fontSize:l,path:h}of t)s.addPath(h,new DOMMatrix(n).preMultiplySelf(i).translate(a,o).scale(l,-l));e.clip(s),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){var c;const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||Vp,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&ht("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const n=s.loadedName||"sans-serif",a=((c=s.systemFontInfo)==null?void 0:c.css)||`"${n}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const l=s.italic?"italic":"normal";let h=e;e<i0?h=i0:e>n0&&(h=n0),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i,n){const a=this.ctx,o=this.current,l=o.font,h=o.textRenderingMode,c=o.fontSize/o.fontSizeScale,u=h&Ee.FILL_STROKE_MASK,f=!!(h&Ee.ADD_TO_PATH_FLAG),g=o.patternFill&&!l.missingFile,b=o.patternStroke&&!l.missingFile;let v;if((l.disableFontFace||f||g||b)&&(v=l.getPathGenerator(this.commonObjs,t)),l.disableFontFace||g||b){a.save(),a.translate(e,s),a.scale(c,-c);let _;if((u===Ee.FILL||u===Ee.FILL_STROKE)&&(i?(_=a.getTransform(),a.setTransform(...i),a.fill(y(this,Xh,pg).call(this,v,_,i))):a.fill(v)),u===Ee.STROKE||u===Ee.FILL_STROKE)if(n){_||(_=a.getTransform()),a.setTransform(...n);const{a:w,b:A,c:C,d:S}=_,T=K.inverseTransform(n),x=K.transform([w,A,C,S,0,0],T);K.singularValueDecompose2dScale(x,us),a.lineWidth*=Math.max(us[0],us[1])/c,a.stroke(y(this,Xh,pg).call(this,v,_,n))}else a.lineWidth/=c,a.stroke(v);a.restore()}else(u===Ee.FILL||u===Ee.FILL_STROKE)&&a.fillText(t,e,s),(u===Ee.STROKE||u===Ee.FILL_STROKE)&&a.strokeText(t,e,s);f&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:Dt(a),x:e,y:s,fontSize:c,path:v})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return gt(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const n=this.ctx,a=e.fontSizeScale,o=e.charSpacing,l=e.wordSpacing,h=e.fontDirection,c=e.textHScale*h,u=t.length,f=s.vertical,g=f?1:-1,b=s.defaultVMetrics,v=i*e.fontMatrix[0],_=e.textRenderingMode===Ee.FILL&&!s.disableFontFace&&!e.patternFill;n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),h>0?n.scale(c,-1):n.scale(c,1);let w,A;if(e.patternFill){n.save();const k=e.fillColor.getPattern(n,this,Ks(n),Ae.FILL);w=Dt(n),n.restore(),n.fillStyle=k}if(e.patternStroke){n.save();const k=e.strokeColor.getPattern(n,this,Ks(n),Ae.STROKE);A=Dt(n),n.restore(),n.strokeStyle=k}let C=e.lineWidth;const S=e.textMatrixScale;if(S===0||C===0){const k=e.textRenderingMode&Ee.FILL_STROKE_MASK;(k===Ee.STROKE||k===Ee.FILL_STROKE)&&(C=this.getSinglePixelWidth())}else C/=S;if(a!==1&&(n.scale(a,a),C/=a),n.lineWidth=C,s.isInvalidPDFjsFont){const k=[];let I=0;for(const M of t)k.push(M.unicode),I+=M.width;n.fillText(k.join(""),0,0),e.x+=I*v*c,n.restore(),this.compose();return}let T=0,x;for(x=0;x<u;++x){const k=t[x];if(typeof k=="number"){T+=g*k*i/1e3;continue}let I=!1;const M=(k.isSpace?l:0)+o,L=k.fontChar,F=k.accent;let it,at,J=k.width;if(f){const B=k.vmetric||b,G=-(k.vmetric?B[1]:J*.5)*v,kt=B[2]*v;J=B?-B[0]:J,it=G/a,at=(T+kt)/a}else it=T/a,at=0;if(s.remeasure&&J>0){const B=n.measureText(L).width*1e3/i*a;if(J<B&&this.isFontSubpixelAAEnabled){const G=J/B;I=!0,n.save(),n.scale(G,1),it/=G}else J!==B&&(it+=(J-B)/2e3*i/a)}if(this.contentVisible&&(k.isInFont||s.missingFile)){if(_&&!F)n.fillText(L,it,at);else if(this.paintChar(L,it,at,w,A),F){const B=it+i*F.offset.x/a,G=at-i*F.offset.y/a;this.paintChar(F.fontChar,B,G,w,A)}}const tt=f?J*v-M*h:J*v+M*h;T+=tt,I&&n.restore()}f?e.y-=T:e.x+=T*c,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,n=s.fontSize,a=s.fontDirection,o=i.vertical?1:-1,l=s.charSpacing,h=s.wordSpacing,c=s.textHScale*a,u=s.fontMatrix||Vp,f=t.length,g=s.textRenderingMode===Ee.INVISIBLE;let b,v,_,w;if(!(g||n===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),s.textMatrix&&e.transform(...s.textMatrix),e.translate(s.x,s.y+s.textRise),e.scale(c,a),b=0;b<f;++b){if(v=t[b],typeof v=="number"){w=o*v*n/1e3,this.ctx.translate(w,0),s.x+=w*c;continue}const A=(v.isSpace?h:0)+l,C=i.charProcOperatorList[v.operatorListId];C?this.contentVisible&&(this.save(),e.scale(n,n),e.transform(...u),this.executeOperatorList(C),this.restore()):ht(`Type3 character "${v.operatorListId}" is not available.`);const S=[v.width,0];K.applyTransform(S,u),_=S[0]*n+A,e.translate(_,0),s.x+=_*c}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,n,a){const o=new Path2D;o.rect(s,i,n-s,a-i),this.ctx.clip(o),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=this.baseTransform||Dt(this.ctx),i={createCanvasGraphics:n=>new Nm(n,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new dg(t,this.ctx,i,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){this.ctx.strokeStyle=this.current.strokeColor=K.makeHexColor(t,e,s),this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t,e,s){this.ctx.fillStyle=this.current.fillColor=K.makeHexColor(t,e,s),this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=k_(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,Ks(e),Ae.SHADING);const i=Ks(e);if(i){const{width:n,height:a}=e.canvas,o=ja.slice();K.axialAlignedBoundingBox([0,0,n,a],i,o);const[l,h,c,u]=o;this.ctx.fillRect(l,h,c-l,u-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){xt("Should not call beginInlineImage")}beginImageData(){xt("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=Dt(this.ctx),e)){K.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[s,i,n,a]=e,o=new Path2D;o.rect(s,i,n-s,a-i),this.ctx.clip(o),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||gp("TODO: Support non-isolated groups."),t.knockout&&ht("Knockout groups not supported.");const s=Dt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=ja.slice();K.axialAlignedBoundingBox(t.bbox,Dt(e),i);const n=[0,0,e.canvas.width,e.canvas.height];i=K.intersect(i,n)||[0,0,0,0];const a=Math.floor(i[0]),o=Math.floor(i[1]),l=Math.max(Math.ceil(i[2])-a,1),h=Math.max(Math.ceil(i[3])-o,1);this.current.startNewPathAndClipBox([0,0,l,h]);let c="groupAt"+this.groupLevel;t.smask&&(c+="_smask_"+this.smaskCounter++%2);const u=this.cachedCanvases.getCanvas(c,l,h),f=u.context;f.translate(-a,-o),f.transform(...s);let g=new Path2D;const[b,v,_,w]=t.bbox;if(g.rect(b,v,_-b,w-v),t.matrix){const A=new Path2D;A.addPath(g,new DOMMatrix(t.matrix)),g=A}f.clip(g),t.smask?this.smaskStack.push({canvas:u.canvas,context:f,offsetX:a,offsetY:o,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,o),e.save()),Pl(e,f),this.ctx=f,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=Dt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const n=ja.slice();K.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i,n),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(n)}}beginAnnotation(t,e,s,i,n){if(y(this,Wh,ug).call(this),Id(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const a=e[2]-e[0],o=e[3]-e[1];if(n&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=a,e[3]=o,K.singularValueDecompose2dScale(Dt(this.ctx),us);const{viewportScale:l}=this,h=Math.ceil(a*this.outputScaleX*l),c=Math.ceil(o*this.outputScaleY*l);this.annotationCanvas=this.canvasFactory.create(h,c);const{canvas:u,context:f}=this.annotationCanvas;this.annotationCanvasMap.set(t,u),this.annotationCanvas.savedCtx=this.ctx,this.ctx=f,this.ctx.save(),this.ctx.setTransform(us[0],0,0,-us[1],0,o*us[1]),Id(this.ctx)}else{Id(this.ctx),this.endPath();const l=new Path2D;l.rect(e[0],e[1],a,o),this.ctx.clip(l)}}this.current=new a0(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),y(this,qh,fg).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this._createMaskCanvas(t),n=i.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(n,i.offsetX,i.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const l=Dt(o);o.transform(e,s,i,n,0,0);const h=this._createMaskCanvas(t);o.setTransform(1,0,0,1,h.offsetX-l[4],h.offsetY-l[5]);for(let c=0,u=a.length;c<u;c+=2){const f=K.transform(l,[e,s,i,n,a[c],a[c+1]]);o.drawImage(h.canvas,f[4],f[5])}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const n of t){const{data:a,width:o,height:l,transform:h}=n,c=this.cachedCanvases.getCanvas("maskCanvas",o,l),u=c.context;u.save();const f=this.getObject(a,n);l0(u,f),u.globalCompositeOperation="source-in",u.fillStyle=i?s.getPattern(u,this,Ks(e),Ae.FILL):s,u.fillRect(0,0,o,l),u.restore(),e.save(),e.transform(...h),e.scale(1,-1),kd(e,c.canvas,0,0,o,l,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){ht("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){ht("Dependent image isn't ready yet");return}const a=n.width,o=n.height,l=[];for(let h=0,c=i.length;h<c;h+=2)l.push({transform:[e,0,0,s,i[h],i[h+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(n,l)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,n=this.cachedCanvases.getCanvas("inlineImage",s,i),a=n.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;this.save();const{filter:n}=i;n!=="none"&&n!==""&&(i.filter="none"),i.scale(1/e,-1/s);let a;if(t.bitmap)a=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)a=t;else{const h=this.cachedCanvases.getCanvas("inlineImage",e,s).context;o0(h,t),a=this.applyTransferMapsToCanvas(h)}const o=this._scaleImage(a,Ks(i));i.imageSmoothingEnabled=h0(Dt(i),t.interpolate),kd(i,o.img,0,0,o.paintWidth,o.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const n=t.width,a=t.height,l=this.cachedCanvases.getCanvas("inlineImage",n,a).context;o0(l,t),i=this.applyTransferMapsToCanvas(l)}for(const n of e)s.save(),s.transform(...n.transform),s.scale(1,-1),kd(s,i,n.x,n.y,n.w,n.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const s=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const i=this.ctx;this.pendingClip&&(s||(this.pendingClip===c0?i.clip(t,"evenodd"):i.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=Dt(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:n}=this.ctx.getTransform();let a,o;if(s===0&&i===0){const l=Math.abs(e),h=Math.abs(n);if(l===h)if(t===0)a=o=1/l;else{const c=l*t;a=o=c<1?1/c:1}else if(t===0)a=1/l,o=1/h;else{const c=l*t,u=h*t;a=c<1?1/c:1,o=u<1?1/u:1}}else{const l=Math.abs(e*n-s*i),h=Math.hypot(e,s),c=Math.hypot(i,n);if(t===0)a=c/l,o=h/l;else{const u=t*l;a=c>u?c/u:1,o=h>u?h/u:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=o}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:s,current:{lineWidth:i}}=this,[n,a]=this.getScaleForStroking();if(n===a){s.lineWidth=(i||1)*n,s.stroke(t);return}const o=s.getLineDash();e&&s.save(),s.scale(n,a),Lp.a=1/n,Lp.d=1/a;const l=new Path2D;if(l.addPath(t,Lp),o.length>0){const h=Math.max(n,a);s.setLineDash(o.map(c=>c/h)),s.lineDashOffset/=h}s.lineWidth=i||1,s.stroke(l),e&&s.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};Wh=new WeakSet,ug=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},qh=new WeakSet,fg=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}},Xh=new WeakSet,pg=function(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i};let qa=Nm;for(const d in du)qa.prototype[d]!==void 0&&(qa.prototype[du[d]]=qa.prototype[d]);var Yh,Kh;class bi{static get workerPort(){return r(this,Yh)}static set workerPort(t){if(!(typeof Worker!="undefined"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");m(this,Yh,t)}static get workerSrc(){return r(this,Kh)}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");m(this,Kh,t)}}Yh=new WeakMap,Kh=new WeakMap,p(bi,Yh,null),p(bi,Kh,"");var So,Zh;class N_{constructor({parsedData:t,rawData:e}){p(this,So,void 0);p(this,Zh,void 0);m(this,So,t),m(this,Zh,e)}getRaw(){return r(this,Zh)}get(t){var e;return(e=r(this,So).get(t))!=null?e:null}[Symbol.iterator](){return r(this,So).entries()}}So=new WeakMap,Zh=new WeakMap;const za=Symbol("INTERNAL");var Qh,Jh,tc,Co;class O_{constructor(t,{name:e,intent:s,usage:i,rbGroups:n}){p(this,Qh,!1);p(this,Jh,!1);p(this,tc,!1);p(this,Co,!0);m(this,Qh,!!(t&ds.DISPLAY)),m(this,Jh,!!(t&ds.PRINT)),this.name=e,this.intent=s,this.usage=i,this.rbGroups=n}get visible(){if(r(this,tc))return r(this,Co);if(!r(this,Co))return!1;const{print:t,view:e}=this.usage;return r(this,Qh)?(e==null?void 0:e.viewState)!=="OFF":r(this,Jh)?(t==null?void 0:t.printState)!=="OFF":!0}_setVisible(t,e,s=!1){t!==za&&xt("Internal method `_setVisible` called."),m(this,tc,s),m(this,Co,e)}}Qh=new WeakMap,Jh=new WeakMap,tc=new WeakMap,Co=new WeakMap;var kn,Ct,xo,Eo,ec,gg;class $_{constructor(t,e=ds.DISPLAY){p(this,ec);p(this,kn,null);p(this,Ct,new Map);p(this,xo,null);p(this,Eo,null);if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,m(this,Eo,t.order);for(const s of t.groups)r(this,Ct).set(s.id,new O_(e,s));if(t.baseState==="OFF")for(const s of r(this,Ct).values())s._setVisible(za,!1);for(const s of t.on)r(this,Ct).get(s)._setVisible(za,!0);for(const s of t.off)r(this,Ct).get(s)._setVisible(za,!1);m(this,xo,this.getHash())}}isVisible(t){if(r(this,Ct).size===0)return!0;if(!t)return gp("Optional content group not defined."),!0;if(t.type==="OCG")return r(this,Ct).has(t.id)?r(this,Ct).get(t.id).visible:(ht(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return y(this,ec,gg).call(this,t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!r(this,Ct).has(e))return ht(`Optional content group not found: ${e}`),!0;if(r(this,Ct).get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!r(this,Ct).has(e))return ht(`Optional content group not found: ${e}`),!0;if(!r(this,Ct).get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!r(this,Ct).has(e))return ht(`Optional content group not found: ${e}`),!0;if(!r(this,Ct).get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!r(this,Ct).has(e))return ht(`Optional content group not found: ${e}`),!0;if(r(this,Ct).get(e).visible)return!1}return!0}return ht(`Unknown optional content policy ${t.policy}.`),!0}return ht(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){var n;const i=r(this,Ct).get(t);if(!i){ht(`Optional content group not found: ${t}`);return}if(s&&e&&i.rbGroups.length)for(const a of i.rbGroups)for(const o of a)o!==t&&((n=r(this,Ct).get(o))==null||n._setVisible(za,!1,!0));i._setVisible(za,!!e,!0),m(this,kn,null)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const n=r(this,Ct).get(i);if(n)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!n.visible,e);break}}m(this,kn,null)}get hasInitialVisibility(){return r(this,xo)===null||this.getHash()===r(this,xo)}getOrder(){return r(this,Ct).size?r(this,Eo)?r(this,Eo).slice():[...r(this,Ct).keys()]:null}getGroup(t){return r(this,Ct).get(t)||null}getHash(){if(r(this,kn)!==null)return r(this,kn);const t=new gb;for(const[e,s]of r(this,Ct))t.update(`${e}:${s.visible}`);return m(this,kn,t.hexdigest())}[Symbol.iterator](){return r(this,Ct).entries()}}kn=new WeakMap,Ct=new WeakMap,xo=new WeakMap,Eo=new WeakMap,ec=new WeakSet,gg=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const n=t[i];let a;if(Array.isArray(n))a=y(this,ec,gg).call(this,n);else if(r(this,Ct).has(n))a=r(this,Ct).get(n).visible;else return ht(`Optional content group not found: ${n}`),!0;switch(s){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return s==="And"};class H_{constructor(t,{disableRange:e=!1,disableStream:s=!1}){Qt(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:n,progressiveDone:a,contentDispositionFilename:o}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=o,(n==null?void 0:n.length)>0){const l=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(l)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((l,h)=>{this._onReceiveData({begin:l,chunk:h})}),t.addProgressListener((l,h)=>{this._onProgress({loaded:l,total:h})}),t.addProgressiveReadListener(l=>{this._onReceiveData({chunk:l})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(n){return n._begin!==t?!1:(n._enqueue(s),!0)});Qt(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var t,e;return(e=(t=this._fullRequestReader)==null?void 0:t._loaded)!=null?e:0}_onProgress(t){var e,s,i,n;t.total===void 0?(s=(e=this._rangeReaders[0])==null?void 0:e.onProgress)==null||s.call(e,{loaded:t.loaded}):(n=(i=this._fullRequestReader)==null?void 0:i.onProgress)==null||n.call(i,{loaded:t.loaded,total:t.total})}_onProgressiveDone(){var t;(t=this._fullRequestReader)==null||t.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){Qt(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new B_(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new V_(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeReaders.slice(0))s.cancel(t);this._pdfDataRangeTransport.abort()}}class B_{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=Sm(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}read(){return H(this,null,function*(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise})}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class V_{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}read(){return H(this,null,function*(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise})}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function z_(d){let t=!0,e=s("filename\\*","i").exec(d);if(e){e=e[1];let c=o(e);return c=unescape(c),c=l(c),c=h(c),n(c)}if(e=a(d),e){const c=h(e);return n(c)}if(e=s("filename","i").exec(d),e){e=e[1];let c=o(e);return c=h(c),n(c)}function s(c,u){return new RegExp("(?:^|;)\\s*"+c+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',u)}function i(c,u){if(c){if(!/^[\x00-\xFF]+$/.test(u))return u;try{const f=new TextDecoder(c,{fatal:!0}),g=vd(u);u=f.decode(g),t=!1}catch(f){}}return u}function n(c){return t&&/[\x80-\xff]/.test(c)&&(c=i("utf-8",c),t&&(c=i("iso-8859-1",c))),c}function a(c){const u=[];let f;const g=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(f=g.exec(c))!==null;){let[,v,_,w]=f;if(v=parseInt(v,10),v in u){if(v===0)break;continue}u[v]=[_,w]}const b=[];for(let v=0;v<u.length&&v in u;++v){let[_,w]=u[v];w=o(w),_&&(w=unescape(w),v===0&&(w=l(w))),b.push(w)}return b.join("")}function o(c){if(c.startsWith('"')){const u=c.slice(1).split('\\"');for(let f=0;f<u.length;++f){const g=u[f].indexOf('"');g!==-1&&(u[f]=u[f].slice(0,g),u.length=f+1),u[f]=u[f].replaceAll(/\\(.)/g,"$1")}c=u.join('"')}return c}function l(c){const u=c.indexOf("'");if(u===-1)return c;const f=c.slice(0,u),b=c.slice(u+1).replace(/^[^']*'/,"");return i(f,b)}function h(c){return!c.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(c)?c:c.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(u,f,g,b){if(g==="q"||g==="Q")return b=b.replaceAll("_"," "),b=b.replaceAll(/=([0-9a-fA-F]{2})/g,function(v,_){return String.fromCharCode(parseInt(_,16))}),i(f,b);try{b=atob(b)}catch(v){}return i(f,b)})}return""}function Tb(d,t){const e=new Headers;if(!d||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function bp(d){var t,e;return(e=(t=URL.parse(d))==null?void 0:t.origin)!=null?e:null}function kb({responseHeaders:d,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},n=parseInt(d.get("Content-Length"),10);return!Number.isInteger(n)||(i.suggestedLength=n,n<=2*e)||s||!t||d.get("Accept-Ranges")!=="bytes"||(d.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function Ib(d){const t=d.get("Content-Disposition");if(t){let e=z_(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch(s){}if(Sm(e))return e}return null}function Ad(d,t){return new uu(`Unexpected server response (${d}) while retrieving PDF "${t}".`,d,d===404||d===0&&t.startsWith("file:"))}function Pb(d){return d===200||d===206}function Rb(d,t,e){return{method:"GET",headers:d,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function Mb(d){return d instanceof Uint8Array?d.buffer:d instanceof ArrayBuffer?d:(ht(`getArrayBuffer - unexpected data format: ${d}`),new Uint8Array(d).buffer)}class d0{constructor(t){O(this,"_responseOrigin",null);this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=Tb(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t,e;return(e=(t=this._fullRequestReader)==null?void 0:t._loaded)!=null?e:0}getFullReader(){return Qt(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new U_(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new G_(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class U_{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,Rb(s,this._withCredentials,this._abortController)).then(n=>{if(t._responseOrigin=bp(n.url),!Pb(n.status))throw Ad(n.status,i);this._reader=n.body.getReader(),this._headersCapability.resolve();const a=n.headers,{allowRangeRequests:o,suggestedLength:l}=kb({responseHeaders:a,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=o,this._contentLength=l||this._contentLength,this._filename=Ib(a),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new Jn("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}read(){return H(this,null,function*(){var s;yield this._headersCapability.promise;const{value:t,done:e}=yield this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:Mb(t),done:!1})})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}class G_{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${s-1}`);const a=i.url;fetch(a,Rb(n,this._withCredentials,this._abortController)).then(o=>{const l=bp(o.url);if(l!==t._responseOrigin)throw new Error(`Expected range response-origin "${l}" to match "${t._responseOrigin}".`);if(!Pb(o.status))throw Ad(o.status,a);this._readCapability.resolve(),this._reader=o.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}read(){return H(this,null,function*(){var s;yield this._readCapability.promise;const{value:t,done:e}=yield this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:Mb(t),done:!1})})}cancel(t){var e;(e=this._reader)==null||e.cancel(t),this._abortController.abort()}}const Dp=200,Fp=206;function j_(d){const t=d.response;return typeof t!="string"?t:vd(t).buffer}class W_{constructor({url:t,httpHeaders:e,withCredentials:s}){O(this,"_responseOrigin",null);this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=Tb(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[n,a]of this.headers)e.setRequestHeader(n,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=Fp):i.expectedStatus=Dp,e.responseType="arraybuffer",Qt(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){var i;const s=this.pendingRequests[t];s&&((i=s.onProgress)==null||i.call(s,e))}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const n=i.status||Dp;if(!(n===Dp&&s.expectedStatus===Fp)&&n!==s.expectedStatus){s.onError(i.status);return}const o=j_(i);if(n===Fp){const l=i.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(l);h?s.onDone({begin:parseInt(h[1],10),chunk:o}):(ht('Missing or invalid "Content-Range" header.'),s.onError(0))}else o?s.onDone({begin:0,chunk:o}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class q_{constructor(t){this._source=t,this._manager=new W_(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return Qt(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new X_(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new Y_(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class X_{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=bp(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(o=>{const[l,...h]=o.split(": ");return[l,h.join(": ")]}):[]),{allowRangeRequests:n,suggestedLength:a}=kb({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=Ib(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Ad(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){var e;(e=this.onProgress)==null||e.call(this,{loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}read(){return H(this,null,function*(){if(yield this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise})}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Y_{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){var e;const t=bp((e=this._manager.getRequestXhr(this._requestId))==null?void 0:e.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){var t;(t=this.onClosed)==null||t.call(this,this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){var e;(e=this._storedError)!=null||(this._storedError=Ad(t,this._url));for(const s of this._requests)s.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){var e;this.isStreamingSupported||(e=this.onProgress)==null||e.call(this,{loaded:t.loaded})}get isStreamingSupported(){return!1}read(){return H(this,null,function*(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise})}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const K_=/^[a-z][a-z0-9\-+.]+:/i;function Z_(d){if(K_.test(d))return new URL(d);const t=process.getBuiltinModule("url");return new URL(t.pathToFileURL(d))}class Q_{constructor(t){this.source=t,this.url=Z_(t.url),Qt(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var t,e;return(e=(t=this._fullRequestReader)==null?void 0:t._loaded)!=null?e:0}getFullReader(){return Qt(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new J_(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new tw(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){var e;(e=this._fullRequestReader)==null||e.cancel(t);for(const s of this._rangeRequestReaders.slice(0))s.cancel(t)}}class J_{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=Ad(0,this._url.href)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}read(){return H(this,null,function*(){var s;if(yield this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new Jn("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class tw{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}read(){return H(this,null,function*(){var s;if(yield this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,(s=this.onProgress)==null||s.call(this,{loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const ew=1e5,u0=30;var _0,In,Xe,sc,ic,Vr,$i,nc,rc,zr,To,ko,Pn,Io,ac,Po,Ur,oc,lc,Ro,Gr,hc,Rn,Mo,of,Lb,lf,Db,cc,mg,Lo,Gd,dc,bg,hf,Fb,cf,Nb;const $t=class $t{constructor({textContentSource:t,container:e,viewport:s}){p(this,of);p(this,lf);p(this,cc);p(this,In,Promise.withResolvers());p(this,Xe,null);p(this,sc,!1);p(this,ic,!!((_0=globalThis.FontInspector)!=null&&_0.enabled));p(this,Vr,null);p(this,$i,null);p(this,nc,0);p(this,rc,0);p(this,zr,null);p(this,To,null);p(this,ko,0);p(this,Pn,0);p(this,Io,Object.create(null));p(this,ac,[]);p(this,Po,null);p(this,Ur,[]);p(this,oc,new WeakMap);p(this,lc,null);var l;if(t instanceof ReadableStream)m(this,Po,t);else if(typeof t=="object")m(this,Po,new ReadableStream({start(h){h.enqueue(t),h.close()}}));else throw new Error('No "textContentSource" parameter specified.');m(this,Xe,m(this,To,e)),m(this,Pn,s.scale*an.pixelRatio),m(this,ko,s.rotation),m(this,$i,{div:null,properties:null,ctx:null});const{pageWidth:i,pageHeight:n,pageX:a,pageY:o}=s.rawDims;m(this,lc,[1,0,0,-1,-a,o+n]),m(this,rc,i),m(this,nc,n),y(l=$t,hf,Fb).call(l),Ra(e,s),r(this,In).promise.finally(()=>{r($t,Mo).delete(this),m(this,$i,null),m(this,Io,null)}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=Se.platform;return gt(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){const t=()=>{r(this,zr).read().then(({value:e,done:s})=>{var i;if(s){r(this,In).resolve();return}(i=r(this,Vr))!=null||m(this,Vr,e.lang),Object.assign(r(this,Io),e.styles),y(this,of,Lb).call(this,e.items),t()},r(this,In).reject)};return m(this,zr,r(this,Po).getReader()),r($t,Mo).add(this),t(),r(this,In).promise}update({viewport:t,onBefore:e=null}){var n;const s=t.scale*an.pixelRatio,i=t.rotation;if(i!==r(this,ko)&&(e==null||e(),m(this,ko,i),Ra(r(this,To),{rotation:i})),s!==r(this,Pn)){e==null||e(),m(this,Pn,s);const a={div:null,properties:null,ctx:y(n=$t,Lo,Gd).call(n,r(this,Vr))};for(const o of r(this,Ur))a.properties=r(this,oc).get(o),a.div=o,y(this,cc,mg).call(this,a)}}cancel(){var e;const t=new Jn("TextLayer task cancelled.");(e=r(this,zr))==null||e.cancel(t).catch(()=>{}),m(this,zr,null),r(this,In).reject(t)}get textDivs(){return r(this,Ur)}get textContentItemsStr(){return r(this,ac)}static cleanup(){if(!(r(this,Mo).size>0)){r(this,Ro).clear();for(const{canvas:t}of r(this,Gr).values())t.remove();r(this,Gr).clear()}}};In=new WeakMap,Xe=new WeakMap,sc=new WeakMap,ic=new WeakMap,Vr=new WeakMap,$i=new WeakMap,nc=new WeakMap,rc=new WeakMap,zr=new WeakMap,To=new WeakMap,ko=new WeakMap,Pn=new WeakMap,Io=new WeakMap,ac=new WeakMap,Po=new WeakMap,Ur=new WeakMap,oc=new WeakMap,lc=new WeakMap,Ro=new WeakMap,Gr=new WeakMap,hc=new WeakMap,Rn=new WeakMap,Mo=new WeakMap,of=new WeakSet,Lb=function(t){var i,n,a;if(r(this,sc))return;(a=(n=r(this,$i)).ctx)!=null||(n.ctx=y(i=$t,Lo,Gd).call(i,r(this,Vr)));const e=r(this,Ur),s=r(this,ac);for(const o of t){if(e.length>ew){ht("Ignoring additional textDivs for performance reasons."),m(this,sc,!0);return}if(o.str===void 0){if(o.type==="beginMarkedContentProps"||o.type==="beginMarkedContent"){const l=r(this,Xe);m(this,Xe,document.createElement("span")),r(this,Xe).classList.add("markedContent"),o.id!==null&&r(this,Xe).setAttribute("id",`${o.id}`),l.append(r(this,Xe))}else o.type==="endMarkedContent"&&m(this,Xe,r(this,Xe).parentNode);continue}s.push(o.str),y(this,lf,Db).call(this,o)}},lf=new WeakSet,Db=function(t){var v;const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};r(this,Ur).push(e);const i=K.transform(r(this,lc),t.transform);let n=Math.atan2(i[1],i[0]);const a=r(this,Io)[t.fontName];a.vertical&&(n+=Math.PI/2);let o=r(this,ic)&&a.fontSubstitution||a.fontFamily;o=$t.fontFamilyMap.get(o)||o;const l=Math.hypot(i[2],i[3]),h=l*y(v=$t,cf,Nb).call(v,o,a,r(this,Vr));let c,u;n===0?(c=i[4],u=i[5]-h):(c=i[4]+h*Math.sin(n),u=i[5]-h*Math.cos(n));const f="calc(var(--total-scale-factor) *",g=e.style;r(this,Xe)===r(this,To)?(g.left=`${(100*c/r(this,rc)).toFixed(2)}%`,g.top=`${(100*u/r(this,nc)).toFixed(2)}%`):(g.left=`${f}${c.toFixed(2)}px)`,g.top=`${f}${u.toFixed(2)}px)`),g.fontSize=`${f}${(r($t,Rn)*l).toFixed(2)}px)`,g.fontFamily=o,s.fontSize=l,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,r(this,ic)&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),n!==0&&(s.angle=n*(180/Math.PI));let b=!1;if(t.str.length>1)b=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const _=Math.abs(t.transform[0]),w=Math.abs(t.transform[3]);_!==w&&Math.max(_,w)/Math.min(_,w)>1.5&&(b=!0)}if(b&&(s.canvasWidth=a.vertical?t.height:t.width),r(this,oc).set(e,s),r(this,$i).div=e,r(this,$i).properties=s,y(this,cc,mg).call(this,r(this,$i)),s.hasText&&r(this,Xe).append(e),s.hasEOL){const _=document.createElement("br");_.setAttribute("role","presentation"),r(this,Xe).append(_)}},cc=new WeakSet,mg=function(t){var o;const{div:e,properties:s,ctx:i}=t,{style:n}=e;let a="";if(r($t,Rn)>1&&(a=`scale(${1/r($t,Rn)})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:l}=n,{canvasWidth:h,fontSize:c}=s;y(o=$t,dc,bg).call(o,i,c*r(this,Pn),l);const{width:u}=i.measureText(e.textContent);u>0&&(a=`scaleX(${h*r(this,Pn)/u}) ${a}`)}s.angle!==0&&(a=`rotate(${s.angle}deg) ${a}`),a.length>0&&(n.transform=a)},Lo=new WeakSet,Gd=function(t=null){let e=r(this,Gr).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),r(this,Gr).set(t,e),r(this,hc).set(e,{size:0,family:""})}return e},dc=new WeakSet,bg=function(t,e,s){const i=r(this,hc).get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)},hf=new WeakSet,Fb=function(){if(r(this,Rn)!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),m(this,Rn,t.getBoundingClientRect().height),t.remove()},cf=new WeakSet,Nb=function(t,e,s){const i=r(this,Ro).get(t);if(i)return i;const n=y(this,Lo,Gd).call(this,s);n.canvas.width=n.canvas.height=u0,y(this,dc,bg).call(this,n,u0,t);const a=n.measureText(""),o=a.fontBoundingBoxAscent,l=Math.abs(a.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let h=.8;return o?h=o/(o+l):(Se.platform.isFirefox&&ht("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?h=e.ascent:e.descent&&(h=1+e.descent)),r(this,Ro).set(t,h),h},p($t,Lo),p($t,dc),p($t,hf),p($t,cf),p($t,Ro,new Map),p($t,Gr,new Map),p($t,hc,new WeakMap),p($t,Rn,null),p($t,Mo,new Set);let sh=$t;class ih{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(n){var l;if(!n)return;let a=null;const o=n.name;if(o==="#text")a=n.value;else if(ih.shouldBuildText(o))(l=n==null?void 0:n.attributes)!=null&&l.textContent?a=n.attributes.textContent:n.value&&(a=n.value);else return;if(a!==null&&e.push({str:a}),!!n.children)for(const h of n.children)i(h)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const sw=65536,iw=100;function Ob(d={}){var ee,Vt;typeof d=="string"||d instanceof URL?d={url:d}:(d instanceof ArrayBuffer||ArrayBuffer.isView(d))&&(d={data:d});const t=new yg,{docId:e}=t,s=d.url?nw(d.url):null,i=d.data?rw(d.data):null,n=d.httpHeaders||null,a=d.withCredentials===!0,o=(ee=d.password)!=null?ee:null,l=d.range instanceof $b?d.range:null,h=Number.isInteger(d.rangeChunkSize)&&d.rangeChunkSize>0?d.rangeChunkSize:sw;let c=d.worker instanceof Xa?d.worker:null;const u=d.verbosity,f=typeof d.docBaseUrl=="string"&&!mp(d.docBaseUrl)?d.docBaseUrl:null,g=Pd(d.cMapUrl),b=d.cMapPacked!==!1,v=d.CMapReaderFactory||(ze?w_:Jm),_=Pd(d.iccUrl),w=Pd(d.standardFontDataUrl),A=d.StandardFontDataFactory||(ze?A_:t0),C=Pd(d.wasmUrl),S=d.WasmFactory||(ze?S_:e0),T=d.stopAtErrors!==!0,x=Number.isInteger(d.maxImageSize)&&d.maxImageSize>-1?d.maxImageSize:-1,k=d.isEvalSupported!==!1,I=typeof d.isOffscreenCanvasSupported=="boolean"?d.isOffscreenCanvasSupported:!ze,M=typeof d.isImageDecoderSupported=="boolean"?d.isImageDecoderSupported:!ze&&(Se.platform.isFirefox||!globalThis.chrome),L=Number.isInteger(d.canvasMaxAreaInBytes)?d.canvasMaxAreaInBytes:-1,F=typeof d.disableFontFace=="boolean"?d.disableFontFace:ze,it=d.fontExtraProperties===!0,at=d.enableXfa===!0,J=d.ownerDocument||globalThis.document,tt=d.disableRange===!0,B=d.disableStream===!0,G=d.disableAutoFetch===!0,kt=d.pdfBug===!0,Bt=d.CanvasFactory||(ze?__:b_),ge=d.FilterFactory||(ze?y_:v_),xe=d.enableHWA===!0,Et=d.useWasm!==!1,At=l?l.length:(Vt=d.length)!=null?Vt:NaN,U=typeof d.useSystemFonts=="boolean"?d.useSystemFonts:!ze&&!F,j=typeof d.useWorkerFetch=="boolean"?d.useWorkerFetch:!!(v===Jm&&A===t0&&S===e0&&g&&w&&C&&$a(g,document.baseURI)&&$a(w,document.baseURI)&&$a(C,document.baseURI)),Y=null;Ky(u);const N={canvasFactory:new Bt({ownerDocument:J,enableHWA:xe}),filterFactory:new ge({docId:e,ownerDocument:J}),cMapReaderFactory:j?null:new v({baseUrl:g,isCompressed:b}),standardFontDataFactory:j?null:new A({baseUrl:w}),wasmFactory:j?null:new S({baseUrl:C})};if(!c){const Wt={verbosity:u,port:bi.workerPort};c=Wt.port?Xa.fromPort(Wt):new Xa(Wt),t._worker=c}const V={docId:e,apiVersion:"5.2.133",data:i,password:o,disableAutoFetch:G,rangeChunkSize:h,length:At,docBaseUrl:f,enableXfa:at,evaluatorOptions:{maxImageSize:x,disableFontFace:F,ignoreErrors:T,isEvalSupported:k,isOffscreenCanvasSupported:I,isImageDecoderSupported:M,canvasMaxAreaInBytes:L,fontExtraProperties:it,useSystemFonts:U,useWasm:Et,useWorkerFetch:j,cMapUrl:g,iccUrl:_,standardFontDataUrl:w,wasmUrl:C}},Nt={ownerDocument:J,pdfBug:kt,styleElement:Y,loadingParams:{disableAutoFetch:G,enableXfa:at}};return c.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const Wt=c.messageHandler.sendWithPromise("GetDocRequest",V,i?[i.buffer]:null);let gs;if(l)gs=new H_(l,{disableRange:tt,disableStream:B});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");let Ys;if(ze)if($a(s)){if(typeof fetch=="undefined"||typeof Response=="undefined"||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");Ys=d0}else Ys=Q_;else Ys=$a(s)?d0:q_;gs=new Ys({url:s,length:At,httpHeaders:n,withCredentials:a,rangeChunkSize:h,disableRange:tt,disableStream:B})}return Wt.then(Ys=>{if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const yt=new Hl(e,Ys,c.port),z=new dw(yt,t,gs,Nt,N);t._transport=z,yt.send("Ready",null)})}).catch(t._capability.reject),t}function nw(d){if(d instanceof URL)return d.href;if(typeof d=="string"){if(ze)return d;const t=URL.parse(d,window.location);if(t)return t.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function rw(d){if(ze&&typeof Buffer!="undefined"&&d instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(d instanceof Uint8Array&&d.byteLength===d.buffer.byteLength)return d;if(typeof d=="string")return vd(d);if(d instanceof ArrayBuffer||ArrayBuffer.isView(d)||typeof d=="object"&&!isNaN(d==null?void 0:d.length))return new Uint8Array(d);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function Pd(d){if(typeof d!="string")return null;if(d.endsWith("/"))return d;throw new Error(`Invalid factory url: "${d}" must include trailing slash.`)}const vg=d=>typeof d=="object"&&Number.isInteger(d==null?void 0:d.num)&&d.num>=0&&Number.isInteger(d==null?void 0:d.gen)&&d.gen>=0,aw=d=>typeof d=="object"&&typeof(d==null?void 0:d.name)=="string",ow=r_.bind(null,vg,aw);var df;const uf=class uf{constructor(){O(this,"_capability",Promise.withResolvers());O(this,"_transport",null);O(this,"_worker",null);O(this,"docId",`d${Fe(uf,df)._++}`);O(this,"destroyed",!1);O(this,"onPassword",null);O(this,"onProgress",null)}get promise(){return this._capability.promise}destroy(){return H(this,null,function*(){var t,e,s,i;this.destroyed=!0;try{(t=this._worker)!=null&&t.port&&(this._worker._pendingDestroy=!0),yield(e=this._transport)==null?void 0:e.destroy()}catch(n){throw(s=this._worker)!=null&&s.port&&delete this._worker._pendingDestroy,n}this._transport=null,(i=this._worker)==null||i.destroy(),this._worker=null})}getData(){return H(this,null,function*(){return this._transport.getData()})}};df=new WeakMap,p(uf,df,0);let yg=uf;class $b{constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(const s of this._progressListeners)s(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(const e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){xt("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class lw{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return gt(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}var Hi,jr,Gl;class hw{constructor(t,e,s,i=!1){p(this,jr);p(this,Hi,!1);this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new Km:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new Hb,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:n=!1}={}){return new _d({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return gt(this,"isPureXfa",!!this._transport._htmlForXfa)}getXfa(){return H(this,null,function*(){var t;return((t=this._transport._htmlForXfa)==null?void 0:t.children[this._pageIndex])||null})}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=hn.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:c=null,isEditing:u=!1}){var S,T;(S=this._stats)==null||S.time("Overall");const f=this._transport.getRenderingIntent(s,i,c,u),{renderingIntent:g,cacheKey:b}=f;m(this,Hi,!1),o||(o=this._transport.getOptionalContentConfig(g));let v=this._intentStates.get(b);v||(v=Object.create(null),this._intentStates.set(b,v)),v.streamReaderCancelTimeout&&(clearTimeout(v.streamReaderCancelTimeout),v.streamReaderCancelTimeout=null);const _=!!(g&ds.PRINT);v.displayReadyCapability||(v.displayReadyCapability=Promise.withResolvers(),v.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(T=this._stats)==null||T.time("Page Request"),this._pumpOperatorList(f));const w=x=>{var k;v.renderTasks.delete(A),_&&m(this,Hi,!0),y(this,jr,Gl).call(this),x?(A.capability.reject(x),this._abortOperatorList({intentState:v,reason:x instanceof Error?x:new Error(x)})):A.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),(k=globalThis.Stats)!=null&&k.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},A=new wg({callback:w,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:v.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!_,pdfBug:this._pdfBug,pageColors:h});(v.renderTasks||(v.renderTasks=new Set)).add(A);const C=A.task;return Promise.all([v.displayReadyCapability.promise,o]).then(([x,k])=>{var I;if(this.destroyed){w();return}if((I=this._stats)==null||I.time("Rendering"),!(k.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");A.initializeGraphics({transparency:x,optionalContentConfig:k}),A.operatorListChanged()}).catch(w),C}getOperatorList({intent:t="display",annotationMode:e=hn.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){var h;function n(){o.operatorList.lastChunk&&(o.opListReadCapability.resolve(o.operatorList),o.renderTasks.delete(l))}const a=this._transport.getRenderingIntent(t,e,s,i,!0);let o=this._intentStates.get(a.cacheKey);o||(o=Object.create(null),this._intentStates.set(a.cacheKey,o));let l;return o.opListReadCapability||(l=Object.create(null),l.operatorListChanged=n,o.opListReadCapability=Promise.withResolvers(),(o.renderTasks||(o.renderTasks=new Set)).add(l),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(h=this._stats)==null||h.time("Page Request"),this._pumpOperatorList(a)),o.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>ih.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function n(){a.read().then(function({value:l,done:h}){var c;if(h){s(o);return}(c=o.lang)!=null||(o.lang=l.lang),Object.assign(o.styles,l.styles),o.items.push(...l.items),n()},i)}const a=e.getReader(),o={items:[],styles:Object.create(null),lang:null};n()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),m(this,Hi,!1),Promise.all(t)}cleanup(t=!1){m(this,Hi,!0);const e=y(this,jr,Gl).call(this);return t&&e&&this._stats&&(this._stats=new Km),e}_startRenderPage(t,e){var i,n;const s=this._intentStates.get(e);s&&((i=this._stats)==null||i.timeEnd("Page Request"),(n=s.displayReadyCapability)==null||n.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&y(this,jr,Gl).call(this)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:n,transfer:a}=s,l=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:i},a).getReader(),h=this._intentStates.get(e);h.streamReader=l;const c=()=>{l.read().then(({value:u,done:f})=>{if(f){h.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(u,h),c())},u=>{if(h.streamReader=null,!this._transport.destroyed){if(h.operatorList){h.operatorList.lastChunk=!0;for(const f of h.renderTasks)f.operatorListChanged();y(this,jr,Gl).call(this)}if(h.displayReadyCapability)h.displayReadyCapability.reject(u);else if(h.opListReadCapability)h.opListReadCapability.reject(u);else throw u}})};c()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof Am){let i=iw;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new Jn(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,n]of this._intentStates)if(n===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}Hi=new WeakMap,jr=new WeakSet,Gl=function(){if(!r(this,Hi)||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),m(this,Hi,!1),!0};var Bi,ff;class cw{constructor(){p(this,Bi,new Map);p(this,ff,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};r(this,ff).then(()=>{for(const[i]of r(this,Bi))i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if((s==null?void 0:s.signal)instanceof AbortSignal){const{signal:n}=s;if(n.aborted){ht("LoopbackPort - cannot use an `aborted` signal.");return}const a=()=>this.removeEventListener(t,e);i=()=>n.removeEventListener("abort",a),n.addEventListener("abort",a)}r(this,Bi).set(e,i)}removeEventListener(t,e){const s=r(this,Bi).get(e);s==null||s(),r(this,Bi).delete(e)}terminate(){for(const[,t]of r(this,Bi))t==null||t();r(this,Bi).clear()}}Bi=new WeakMap,ff=new WeakMap;var pf,Wr,qr,Do,jd,Fo,Wd;const Rt=class Rt{constructor({name:t=null,port:e=null,verbosity:s=Zy()}={}){p(this,Do);var i;if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if((i=r(Rt,qr))!=null&&i.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(r(Rt,qr)||m(Rt,qr,new WeakMap)).set(e,this),this._initializeFromPort(e);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new Hl("main","worker",t),this._messageHandler.on("ready",function(){}),y(this,Do,jd).call(this)}_initialize(){if(r(Rt,Wr)||r(Rt,Fo,Wd)){this._setupFakeWorker();return}let{workerSrc:t}=Rt;try{Rt._isSameOrigin(window.location,t)||(t=Rt._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Hl("main","worker",e),i=()=>{n.abort(),s.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},n=new AbortController;e.addEventListener("error",()=>{this._webWorker||i()},{signal:n.signal}),s.on("test",o=>{if(n.abort(),this.destroyed||!o){i();return}this._messageHandler=s,this._port=e,this._webWorker=e,y(this,Do,jd).call(this)}),s.on("ready",o=>{if(n.abort(),this.destroyed){i();return}try{a()}catch(l){this._setupFakeWorker()}});const a=()=>{const o=new Uint8Array;s.send("test",o,[o.buffer])};a();return}catch(e){gp("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){r(Rt,Wr)||(ht("Setting up fake worker."),m(Rt,Wr,!0)),Rt._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new cw;this._port=e;const s=`fake${Fe(Rt,pf)._++}`,i=new Hl(s+"_worker",s,e);t.setup(i,e),this._messageHandler=new Hl(s,s+"_worker",e),y(this,Do,jd).call(this)}).catch(t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){var t,e,s;this.destroyed=!0,(t=this._webWorker)==null||t.terminate(),this._webWorker=null,(e=r(Rt,qr))==null||e.delete(this._port),this._port=null,(s=this._messageHandler)==null||s.destroy(),this._messageHandler=null}static fromPort(t){var s;if(!(t!=null&&t.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const e=(s=r(this,qr))==null?void 0:s.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new Rt(t)}static get workerSrc(){if(bi.workerSrc)return bi.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return gt(this,"_setupFakeWorkerGlobal",H(this,null,function*(){return r(this,Fo,Wd)?r(this,Fo,Wd):(yield h1(()=>import(this.workerSrc),__vite__mapDeps([]))).WorkerMessageHandler}))}};pf=new WeakMap,Wr=new WeakMap,qr=new WeakMap,Do=new WeakSet,jd=function(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})},Fo=new WeakSet,Wd=function(){var t;try{return((t=globalThis.pdfjsWorker)==null?void 0:t.WorkerMessageHandler)||null}catch(e){return null}},p(Rt,Fo),p(Rt,pf,0),p(Rt,Wr,!1),p(Rt,qr,void 0),ze&&(m(Rt,Wr,!0),bi.workerSrc||(bi.workerSrc="./pdf.worker.mjs")),Rt._isSameOrigin=(t,e)=>{const s=URL.parse(t);if(!(s!=null&&s.origin)||s.origin==="null")return!1;const i=new URL(e,s);return s.origin===i.origin},Rt._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};let Xa=Rt;var Vi,li,No,Oo,zi,Xr,jl;class dw{constructor(t,e,s,i,n){p(this,Xr);p(this,Vi,new Map);p(this,li,new Map);p(this,No,new Map);p(this,Oo,new Map);p(this,zi,null);this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Hb,this.fontLoader=new g_({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return gt(this,"annotationStorage",new Tm)}getRenderingIntent(t,e=hn.ENABLE,s=null,i=!1,n=!1){let a=ds.DISPLAY,o=og;switch(t){case"any":a=ds.ANY;break;case"display":break;case"print":a=ds.PRINT;break;default:ht(`getRenderingIntent - invalid intent: ${t}`)}const l=a&ds.PRINT&&s instanceof bb?s:this.annotationStorage;switch(e){case hn.DISABLE:a+=ds.ANNOTATIONS_DISABLE;break;case hn.ENABLE:break;case hn.ENABLE_FORMS:a+=ds.ANNOTATIONS_FORMS;break;case hn.ENABLE_STORAGE:a+=ds.ANNOTATIONS_STORAGE,o=l.serializable;break;default:ht(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(a+=ds.IS_EDITING),n&&(a+=ds.OPLIST);const{ids:h,hash:c}=l.modifiedIds,u=[a,o.hash,c];return{renderingIntent:a,cacheKey:u.join("_"),annotationStorageSerializable:o,modifiedIds:h}}destroy(){var s;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),(s=r(this,zi))==null||s.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const i of r(this,li).values())t.push(i._destroy());r(this,li).clear(),r(this,No).clear(),r(this,Oo).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{var i,n;this.commonObjs.clear(),this.fontLoader.clear(),r(this,Vi).clear(),this.filterFactory.destroy(),sh.cleanup(),(i=this._networkStream)==null||i.cancelAllRequests(new Jn("Worker was terminated.")),(n=this.messageHandler)==null||n.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{Qt(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=n=>{this._lastProgress={loaded:n.loaded,total:n.total}},i.onPull=()=>{this._fullReader.read().then(function({value:n,done:a}){if(a){i.close();return}Qt(n instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(n),1,[n])}).catch(n=>{i.error(n)})},i.onCancel=n=>{this._fullReader.cancel(n),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("ReaderHeadersReady",s=>H(this,null,function*(){var o;yield this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:n,contentLength:a}=this._fullReader;return(!i||!n)&&(this._lastProgress&&((o=e.onProgress)==null||o.call(e,this._lastProgress)),this._fullReader.onProgress=l=>{var h;(h=e.onProgress)==null||h.call(e,{loaded:l.loaded,total:l.total})}),{isStreamingSupported:i,isRangeSupported:n,contentLength:a}})),t.on("GetRangeReader",(s,i)=>{Qt(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const n=this._networkStream.getRangeReader(s.begin,s.end);if(!n){i.close();return}i.onPull=()=>{n.read().then(function({value:a,done:o}){if(o){i.close();return}Qt(a instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(a),1,[a])}).catch(a=>{i.error(a)})},i.onCancel=a=>{n.cancel(a),i.ready.catch(o=>{if(!this.destroyed)throw o})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new lw(s,this))}),t.on("DocException",s=>{e._capability.reject(je(s))}),t.on("PasswordRequest",s=>{m(this,zi,Promise.withResolvers());try{if(!e.onPassword)throw je(s);const i=n=>{n instanceof Error?r(this,zi).reject(n):r(this,zi).resolve({password:n})};e.onPassword(i,s.code)}catch(i){r(this,zi).reject(i)}return r(this,zi).promise}),t.on("DataLoaded",s=>{var i;(i=e.onProgress)==null||i.call(e,{loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;r(this,li).get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,n])=>{var a;if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":if("error"in n){const c=n.error;ht(`Error during font loading: ${c}`),this.commonObjs.resolve(s,c);break}const o=this._params.pdfBug&&((a=globalThis.FontInspector)!=null&&a.enabled)?(c,u)=>globalThis.FontInspector.fontAdded(c,u):null,l=new m_(n,o);this.fontLoader.bind(l).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!l.fontExtraProperties&&l.data&&(l.data=null),this.commonObjs.resolve(s,l)});break;case"CopyLocalImage":const{imageRef:h}=n;Qt(h,"The imageRef must be defined.");for(const c of r(this,li).values())for(const[,u]of c.objs)if((u==null?void 0:u.ref)===h)return u.dataLen?(this.commonObjs.resolve(s,structuredClone(u)),u.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,n,a])=>{var l;if(this.destroyed)return;const o=r(this,li).get(i);if(!o.objs.has(s)){if(o._intentStates.size===0){(l=a==null?void 0:a.bitmap)==null||l.close();return}switch(n){case"Image":case"Pattern":o.objs.resolve(s,a);break;default:throw new Error(`Got unknown object type ${n}`)}}}),t.on("DocProgress",s=>{var i;this.destroyed||(i=e.onProgress)==null||i.call(e,{loaded:s.loaded,total:s.total})}),t.on("FetchBinaryData",s=>H(this,null,function*(){if(this.destroyed)throw new Error("Worker was destroyed.");const i=this[s.type];if(!i)throw new Error(`${s.type} not initialized, see the \`useWorkerFetch\` parameter.`);return i.fetch(s)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var s,i;this.annotationStorage.size<=0&&ht("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:(i=(s=this._fullReader)==null?void 0:s.filename)!=null?i:null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=r(this,No).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(n=>{if(this.destroyed)throw new Error("Transport destroyed");n.refStr&&r(this,Oo).set(n.refStr,t);const a=new hw(e,n,this,this._params.pdfBug);return r(this,li).set(e,a),a});return r(this,No).set(e,i),i}getPageIndex(t){return vg(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return y(this,Xr,jl).call(this,"GetFieldObjects")}hasJSActions(){return y(this,Xr,jl).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return y(this,Xr,jl).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return y(this,Xr,jl).call(this,"GetOptionalContentConfig").then(e=>new $_(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=r(this,Vi).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>{var n,a,o,l;return{info:i[0],metadata:i[1]?new N_(i[1]):null,contentDispositionFilename:(a=(n=this._fullReader)==null?void 0:n.filename)!=null?a:null,contentLength:(l=(o=this._fullReader)==null?void 0:o.contentLength)!=null?l:null}});return r(this,Vi).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}startCleanup(t=!1){return H(this,null,function*(){if(!this.destroyed){yield this.messageHandler.sendWithPromise("Cleanup",null);for(const e of r(this,li).values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),r(this,Vi).clear(),this.filterFactory.destroy(!0),sh.cleanup()}})}cachedPageNumber(t){var s;if(!vg(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return(s=r(this,Oo).get(e))!=null?s:null}}Vi=new WeakMap,li=new WeakMap,No=new WeakMap,Oo=new WeakMap,zi=new WeakMap,Xr=new WeakSet,jl=function(t,e=null){const s=r(this,Vi).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return r(this,Vi).set(t,i),i};const Rl=Symbol("INITIAL_DATA");var es,uc,_g;class Hb{constructor(){p(this,uc);p(this,es,Object.create(null))}get(t,e=null){if(e){const i=y(this,uc,_g).call(this,t);return i.promise.then(()=>e(i.data)),null}const s=r(this,es)[t];if(!s||s.data===Rl)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=r(this,es)[t];return!!e&&e.data!==Rl}delete(t){const e=r(this,es)[t];return!e||e.data===Rl?!1:(delete r(this,es)[t],!0)}resolve(t,e=null){const s=y(this,uc,_g).call(this,t);s.data=e,s.resolve()}clear(){var t;for(const e in r(this,es)){const{data:s}=r(this,es)[e];(t=s==null?void 0:s.bitmap)==null||t.close()}m(this,es,Object.create(null))}*[Symbol.iterator](){for(const t in r(this,es)){const{data:e}=r(this,es)[t];e!==Rl&&(yield[t,e])}}}es=new WeakMap,uc=new WeakSet,_g=function(t){var e;return(e=r(this,es))[t]||(e[t]=vi(Ge({},Promise.withResolvers()),{data:Rl}))};var Mn;class uw{constructor(t){p(this,Mn,null);O(this,"onContinue",null);O(this,"onError",null);m(this,Mn,t)}get promise(){return r(this,Mn).capability.promise}cancel(t=0){r(this,Mn).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=r(this,Mn).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=r(this,Mn);return t.form||t.canvas&&(e==null?void 0:e.size)>0}}Mn=new WeakMap;var Ln,Yr;const or=class or{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:n,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:h,useRequestAnimationFrame:c=!1,pdfBug:u=!1,pageColors:f=null}){p(this,Ln,null);this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=l,this.filterFactory=h,this._pdfBug=u,this.pageColors=f,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=c===!0&&typeof window!="undefined",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new uw(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){var o,l;if(this.cancelled)return;if(this._canvas){if(r(or,Yr).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");r(or,Yr).add(this._canvas)}this._pdfBug&&((o=globalThis.StepperManager)!=null&&o.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:n,background:a}=this.params;this.gfx=new qa(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:i,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,(l=this.graphicsReadyCallback)==null||l.call(this)}cancel(t=null,e=0){var s,i,n;this.running=!1,this.cancelled=!0,(s=this.gfx)==null||s.endDrawing(),r(this,Ln)&&(window.cancelAnimationFrame(r(this,Ln)),m(this,Ln,null)),r(or,Yr).delete(this._canvas),t||(t=new Am(`Rendering cancelled, page ${this._pageIndex+1}`,e)),this.callback(t),(n=(i=this.task).onError)==null||n.call(i,t)}operatorListChanged(){var t;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(t=this.stepper)==null||t.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?m(this,Ln,window.requestAnimationFrame(()=>{m(this,Ln,null),this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}_next(){return H(this,null,function*(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),r(or,Yr).delete(this._canvas),this.callback())))})}};Ln=new WeakMap,Yr=new WeakMap,p(or,Yr,new WeakSet);let wg=or;const fw="5.2.133",pw="4f7761353";function f0(d){return Math.floor(Math.max(0,Math.min(1,d))*255).toString(16).padStart(2,"0")}function Ml(d){return Math.max(0,Math.min(255,255*d))}class p0{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=Ml(t),[t,t,t]}static G_HTML([t]){const e=f0(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(Ml)}static RGB_HTML(t){return`#${t.map(f0).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[Ml(1-Math.min(1,t+i)),Ml(1-Math.min(1,s+i)),Ml(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,n=1-e,a=1-s,o=Math.min(i,n,a);return["CMYK",i,n,a,o]}}class gw{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){xt("Abstract method `_createSVG` called.")}}class gu extends gw{_createSVG(t){return document.createElementNS(wi,t)}}class Bb{static setupStorage(t,e,s,i,n){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(a.value!==null&&(t.textContent=a.value),n==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),n==="print")break;t.addEventListener("change",o=>{i.setValue(e,{value:o.target.checked?o.target.getAttribute("xfaOn"):o.target.getAttribute("xfaOff")})})}else{if(a.value!==null&&t.setAttribute("value",a.value),n==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})})}break;case"select":if(a.value!==null){t.setAttribute("value",a.value);for(const o of s.children)o.attributes.value===a.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}t.addEventListener("input",o=>{const l=o.target.options,h=l.selectedIndex===-1?"":l[l.selectedIndex].value;i.setValue(e,{value:h})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:n}){const{attributes:a}=e,o=t instanceof HTMLAnchorElement;a.type==="radio"&&(a.name=`${a.name}-${i}`);for(const[l,h]of Object.entries(a))if(h!=null)switch(l){case"class":h.length&&t.setAttribute(l,h.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",h);break;case"style":Object.assign(t.style,h);break;case"textContent":t.textContent=h;break;default:(!o||l!=="href"&&l!=="newWindow")&&t.setAttribute(l,h)}o&&n.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){var u,f;const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,n=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:n,linkService:s});const o=n!=="richText",l=t.div;if(l.append(a),t.viewport){const g=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=g}o&&l.setAttribute("class","xfaLayer xfaFont");const h=[];if(i.children.length===0){if(i.value){const g=document.createTextNode(i.value);a.append(g),o&&ih.shouldBuildText(i.name)&&h.push(g)}return{textDivs:h}}const c=[[i,-1,a]];for(;c.length>0;){const[g,b,v]=c.at(-1);if(b+1===g.children.length){c.pop();continue}const _=g.children[++c.at(-1)[1]];if(_===null)continue;const{name:w}=_;if(w==="#text"){const C=document.createTextNode(_.value);h.push(C),v.append(C);continue}const A=(u=_==null?void 0:_.attributes)!=null&&u.xmlns?document.createElementNS(_.attributes.xmlns,w):document.createElement(w);if(v.append(A),_.attributes&&this.setAttributes({html:A,element:_,storage:e,intent:n,linkService:s}),((f=_.children)==null?void 0:f.length)>0)c.push([_,-1,A]);else if(_.value){const C=document.createTextNode(_.value);o&&ih.shouldBuildText(w)&&h.push(C),A.append(C)}}for(const g of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))g.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const Sd=1e3,mw=9,La=new WeakSet;class g0{static create(t){switch(t.data.annotationType){case Yt.LINK:return new Vb(t);case Yt.TEXT:return new bw(t);case Yt.WIDGET:switch(t.data.fieldType){case"Tx":return new vw(t);case"Btn":return t.data.radioButton?new Gb(t):t.data.checkBox?new _w(t):new ww(t);case"Ch":return new Aw(t);case"Sig":return new yw(t)}return new Fa(t);case Yt.POPUP:return new Sg(t);case Yt.FREETEXT:return new Yb(t);case Yt.LINE:return new Cw(t);case Yt.SQUARE:return new xw(t);case Yt.CIRCLE:return new Ew(t);case Yt.POLYLINE:return new Kb(t);case Yt.CARET:return new kw(t);case Yt.INK:return new Pm(t);case Yt.POLYGON:return new Tw(t);case Yt.HIGHLIGHT:return new Zb(t);case Yt.UNDERLINE:return new Iw(t);case Yt.SQUIGGLY:return new Pw(t);case Yt.STRIKEOUT:return new Rw(t);case Yt.STAMP:return new Qb(t);case Yt.FILEATTACHMENT:return new Mw(t);default:return new jt(t)}}}var Kr,$o,Ho,fc,Ag;const Om=class Om{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){p(this,fc);p(this,Kr,null);p(this,$o,!1);p(this,Ho,null);this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:s}){return!!(t!=null&&t.str||e!=null&&e.str||s!=null&&s.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return Om._hasPopupData(this.data)}updateEdited(t){var s;if(!this.container)return;r(this,Kr)||m(this,Kr,{rect:this.data.rect.slice(0)});const{rect:e}=t;e&&y(this,fc,Ag).call(this,e),(s=r(this,Ho))==null||s.popup.updateEdited(t)}resetEdited(){var t;r(this,Kr)&&(y(this,fc,Ag).call(this,r(this,Kr).rect),(t=r(this,Ho))==null||t.popup.resetEdited(),m(this,Kr,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof Fa||(n.tabIndex=Sd);const{style:a}=n;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof Sg){const{rotation:v}=e;return!e.hasOwnCanvas&&v!==0&&this.setRotation(v,n),n}const{width:o,height:l}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const v=e.borderStyle.horizontalCornerRadius,_=e.borderStyle.verticalCornerRadius;if(v>0||_>0){const A=`calc(${v}px * var(--total-scale-factor)) / calc(${_}px * var(--total-scale-factor))`;a.borderRadius=A}else if(this instanceof Gb){const A=`calc(${o}px * var(--total-scale-factor)) / calc(${l}px * var(--total-scale-factor))`;a.borderRadius=A}switch(e.borderStyle.style){case Oa.SOLID:a.borderStyle="solid";break;case Oa.DASHED:a.borderStyle="dashed";break;case Oa.BEVELED:ht("Unimplemented border style: beveled");break;case Oa.INSET:ht("Unimplemented border style: inset");break;case Oa.UNDERLINE:a.borderBottomStyle="solid";break}const w=e.borderColor||null;w?(m(this,$o,!0),a.borderColor=K.makeHexColor(w[0]|0,w[1]|0,w[2]|0)):a.borderWidth=0}const h=K.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:c,pageHeight:u,pageX:f,pageY:g}=i.rawDims;a.left=`${100*(h[0]-f)/c}%`,a.top=`${100*(h[1]-g)/u}%`;const{rotation:b}=e;return e.hasOwnCanvas||b===0?(a.width=`${100*o/c}%`,a.height=`${100*l/u}%`):this.setRotation(b,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims;let{width:n,height:a}=this;t%180!==0&&([n,a]=[a,n]),e.style.width=`${100*n/s}%`,e.style.height=`${100*a/i}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const n=i.detail[e],a=n[0],o=n.slice(1);i.target.style[s]=p0[`${a}_HTML`](o),this.annotationStorage.setValue(this.data.id,{[s]:p0[`${a}_rgb`](o)})};return gt(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail)){const n=t[i]||s[i];n==null||n(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,n]of Object.entries(e)){const a=s[i];if(a){const o={detail:{[i]:n},target:t};a(o),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,n]=this.data.rect.map(v=>Math.fround(v));if(t.length===8){const[v,_,w,A]=t.subarray(2,6);if(i===v&&n===_&&e===w&&s===A)return}const{style:a}=this.container;let o;if(r(this,$o)){const{borderColor:v,borderWidth:_}=a;a.borderWidth=0,o=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${v}" stroke-width="${_}">`],this.container.classList.add("hasBorder")}const l=i-e,h=n-s,{svgFactory:c}=this,u=c.createElement("svg");u.classList.add("quadrilateralsContainer"),u.setAttribute("width",0),u.setAttribute("height",0);const f=c.createElement("defs");u.append(f);const g=c.createElement("clipPath"),b=`clippath_${this.data.id}`;g.setAttribute("id",b),g.setAttribute("clipPathUnits","objectBoundingBox"),f.append(g);for(let v=2,_=t.length;v<_;v+=8){const w=t[v],A=t[v+1],C=t[v+2],S=t[v+3],T=c.createElement("rect"),x=(C-e)/l,k=(n-A)/h,I=(w-C)/l,M=(A-S)/h;T.setAttribute("x",x),T.setAttribute("y",k),T.setAttribute("width",I),T.setAttribute("height",M),g.append(T),o==null||o.push(`<rect vector-effect="non-scaling-stroke" x="${x}" y="${k}" width="${I}" height="${M}"/>`)}r(this,$o)&&(o.push("</g></svg>')"),a.backgroundImage=o.join("")),this.container.append(u),this.container.style.clipPath=`url(#${b})`}_createPopup(){const{data:t}=this,e=m(this,Ho,new Sg({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]}));this.parent.div.append(e.render())}render(){xt("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:n,id:a,exportValues:o}of i){if(n===-1||a===e)continue;const l=typeof o=="string"?o:null,h=document.querySelector(`[data-element-id="${a}"]`);if(h&&!La.has(h)){ht(`_getElementsByName - element not allowed: ${a}`);continue}s.push({id:a,exportValue:l,domElement:h})}return s}for(const i of document.getElementsByName(t)){const{exportValue:n}=i,a=i.getAttribute("data-element-id");a!==e&&La.has(i)&&s.push({id:a,exportValue:n,domElement:i})}return s}show(){var t;this.container&&(this.container.hidden=!1),(t=this.popup)==null||t.maybeShow()}hide(){var t;this.container&&(this.container.hidden=!0),(t=this.popup)==null||t.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{var s;(s=this.linkService.eventBus)==null||s.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}};Kr=new WeakMap,$o=new WeakMap,Ho=new WeakMap,fc=new WeakSet,Ag=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:o,pageY:l}}}}=this;s==null||s.splice(0,4,...t),e.left=`${100*(t[0]-o)/n}%`,e.top=`${100*(a-t[3]+l)/a}%`,i===0?(e.width=`${100*(t[2]-t[0])/n}%`,e.height=`${100*(t[3]-t[1])/a}%`):this.setRotation(i)};let jt=Om;var Ui,er,gf,zb,mf,Ub;class Vb extends jt{constructor(e,s=null){super(e,{isRenderable:!0,ignoreBorder:!!(s!=null&&s.ignoreBorder),createQuadrilaterals:!0});p(this,Ui);p(this,gf);p(this,mf);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:s}=this,i=document.createElement("a");i.setAttribute("data-element-id",e.id);let n=!1;return e.url?(s.addLinkAttributes(i,e.url,e.newWindow),n=!0):e.action?(this._bindNamedAction(i,e.action),n=!0):e.attachment?(y(this,gf,zb).call(this,i,e.attachment,e.attachmentDest),n=!0):e.setOCGState?(y(this,mf,Ub).call(this,i,e.setOCGState),n=!0):e.dest?(this._bindLink(i,e.dest),n=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,e),n=!0),e.resetForm?(this._bindResetFormAction(i,e.resetForm),n=!0):this.isTooltipOnly&&!n&&(this._bindLink(i,""),n=!0)),this.container.classList.add("linkAnnotation"),n&&this.container.append(i),this.container}_bindLink(e,s){e.href=this.linkService.getDestinationHash(s),e.onclick=()=>(s&&this.linkService.goToDestination(s),!1),(s||s==="")&&y(this,Ui,er).call(this)}_bindNamedAction(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(s),!1),y(this,Ui,er).call(this)}_bindJSAction(e,s){e.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const n of Object.keys(s.actions)){const a=i.get(n);a&&(e[a]=()=>{var o;return(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:s.id,name:n}}),!1})}e.onclick||(e.onclick=()=>!1),y(this,Ui,er).call(this)}_bindResetFormAction(e,s){const i=e.onclick;if(i||(e.href=this.linkService.getAnchorUrl("")),y(this,Ui,er).call(this),!this._fieldObjects){ht('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(e.onclick=()=>!1);return}e.onclick=()=>{var u;i==null||i();const{fields:n,refs:a,include:o}=s,l=[];if(n.length!==0||a.length!==0){const f=new Set(a);for(const g of n){const b=this._fieldObjects[g]||[];for(const{id:v}of b)f.add(v)}for(const g of Object.values(this._fieldObjects))for(const b of g)f.has(b.id)===o&&l.push(b)}else for(const f of Object.values(this._fieldObjects))l.push(...f);const h=this.annotationStorage,c=[];for(const f of l){const{id:g}=f;switch(c.push(g),f.type){case"text":{const v=f.defaultValue||"";h.setValue(g,{value:v});break}case"checkbox":case"radiobutton":{const v=f.defaultValue===f.exportValues;h.setValue(g,{value:v});break}case"combobox":case"listbox":{const v=f.defaultValue||"";h.setValue(g,{value:v});break}default:continue}const b=document.querySelector(`[data-element-id="${g}"]`);if(b){if(!La.has(b)){ht(`_bindResetFormAction - element not allowed: ${g}`);continue}}else continue;b.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((u=this.linkService.eventBus)==null||u.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:c,name:"ResetForm"}})),!1}}}Ui=new WeakSet,er=function(){this.container.setAttribute("data-internal-link","")},gf=new WeakSet,zb=function(e,s,i=null){e.href=this.linkService.getAnchorUrl(""),s.description&&(e.title=s.description),e.onclick=()=>{var n;return(n=this.downloadManager)==null||n.openOrDownloadData(s.content,s.filename,i),!1},y(this,Ui,er).call(this)},mf=new WeakSet,Ub=function(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(s),!1),y(this,Ui,er).call(this)};class bw extends jt{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class Fa extends jt{render(){return this.container}showElementAndHideCanvas(t){var e;this.data.hasOwnCanvas&&(((e=t.previousSibling)==null?void 0:e.nodeName)==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return Se.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,n){s.includes("mouse")?t.addEventListener(s,a=>{var o;(o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(a),shift:a.shiftKey,modifier:this._getKeyModifier(a)}})}):t.addEventListener(s,a=>{var o;if(s==="blur"){if(!e.focused||!a.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}n&&((o=this.linkService.eventBus)==null||o.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(a)}}))})}_setEventListeners(t,e,s,i){var n,a,o;for(const[l,h]of s)(h==="Action"||(n=this.data.actions)!=null&&n[h])&&((h==="Focus"||h==="Blur")&&(e||(e={focused:!1})),this._setEventListener(t,e,l,h,i),h==="Focus"&&!((a=this.data.actions)!=null&&a.Blur)?this._setEventListener(t,e,"blur","Blur",null):h==="Blur"&&!((o=this.data.actions)!=null&&o.Focus)&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":K.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||mw,n=t.style;let a;const o=2,l=h=>Math.round(10*h)/10;if(this.data.multiLine){const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o),c=Math.round(h/(kp*i))||1,u=h/c;a=Math.min(i,l(u/kp))}else{const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o);a=Math.min(i,l(h/kp))}n.fontSize=`calc(${a}px * var(--total-scale-factor))`,n.color=K.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class vw extends Fa{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),n.setValue(a.id,{[i]:s})}render(){var i,n;const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const a=t.getValue(e,{value:this.data.fieldValue});let o=a.value||"";const l=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;l&&o.length>l&&(o=o.slice(0,l));let h=a.formattedValue||((i=this.data.textContent)==null?void 0:i.join(`
`))||null;h&&this.data.comb&&(h=h.replaceAll(/\s+/g,""));const c={userValue:o,formattedValue:h,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=h!=null?h:o,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type=this.data.password?"password":"text",s.setAttribute("value",h!=null?h:o),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),La.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=Sd,this._setRequired(s,this.data.required),l&&(s.maxLength=l),s.addEventListener("input",f=>{t.setValue(e,{value:f.target.value}),this.setPropertyOnSiblings(s,"value",f.target.value,"value"),c.formattedValue=null}),s.addEventListener("resetform",f=>{var b;const g=(b=this.data.defaultFieldValue)!=null?b:"";s.value=c.userValue=g,c.formattedValue=null});let u=f=>{const{formattedValue:g}=c;g!=null&&(f.target.value=g),f.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",g=>{var v;if(c.focused)return;const{target:b}=g;c.userValue&&(b.value=c.userValue),c.lastCommittedValue=b.value,c.commitKey=1,(v=this.data.actions)!=null&&v.Focus||(c.focused=!0)}),s.addEventListener("updatefromsandbox",g=>{this.showElementAndHideCanvas(g.target);const b={value(v){var _;c.userValue=(_=v.detail.value)!=null?_:"",t.setValue(e,{value:c.userValue.toString()}),v.target.value=c.userValue},formattedValue(v){const{formattedValue:_}=v.detail;c.formattedValue=_,_!=null&&v.target!==document.activeElement&&(v.target.value=_),t.setValue(e,{formattedValue:_})},selRange(v){v.target.setSelectionRange(...v.detail.selRange)},charLimit:v=>{var C;const{charLimit:_}=v.detail,{target:w}=v;if(_===0){w.removeAttribute("maxLength");return}w.setAttribute("maxLength",_);let A=c.userValue;!A||A.length<=_||(A=A.slice(0,_),w.value=c.userValue=A,t.setValue(e,{value:A}),(C=this.linkService.eventBus)==null||C.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:A,willCommit:!0,commitKey:1,selStart:w.selectionStart,selEnd:w.selectionEnd}}))}};this._dispatchEventFromSandbox(b,g)}),s.addEventListener("keydown",g=>{var _;c.commitKey=1;let b=-1;if(g.key==="Escape"?b=0:g.key==="Enter"&&!this.data.multiLine?b=2:g.key==="Tab"&&(c.commitKey=3),b===-1)return;const{value:v}=g.target;c.lastCommittedValue!==v&&(c.lastCommittedValue=v,c.userValue=v,(_=this.linkService.eventBus)==null||_.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:v,willCommit:!0,commitKey:b,selStart:g.target.selectionStart,selEnd:g.target.selectionEnd}}))});const f=u;u=null,s.addEventListener("blur",g=>{var v,_;if(!c.focused||!g.relatedTarget)return;(v=this.data.actions)!=null&&v.Blur||(c.focused=!1);const{value:b}=g.target;c.userValue=b,c.lastCommittedValue!==b&&((_=this.linkService.eventBus)==null||_.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:b,willCommit:!0,commitKey:c.commitKey,selStart:g.target.selectionStart,selEnd:g.target.selectionEnd}})),f(g)}),(n=this.data.actions)!=null&&n.Keystroke&&s.addEventListener("beforeinput",g=>{var T;c.lastCommittedValue=null;const{data:b,target:v}=g,{value:_,selectionStart:w,selectionEnd:A}=v;let C=w,S=A;switch(g.inputType){case"deleteWordBackward":{const x=_.substring(0,w).match(/\w*[^\w]*$/);x&&(C-=x[0].length);break}case"deleteWordForward":{const x=_.substring(w).match(/^[^\w]*\w*/);x&&(S+=x[0].length);break}case"deleteContentBackward":w===A&&(C-=1);break;case"deleteContentForward":w===A&&(S+=1);break}g.preventDefault(),(T=this.linkService.eventBus)==null||T.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:_,change:b||"",willCommit:!1,selStart:C,selEnd:S}})}),this._setEventListeners(s,c,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],g=>g.target.value)}if(u&&s.addEventListener("blur",u),this.data.comb){const g=(this.data.rect[2]-this.data.rect[0])/l;s.classList.add("comb"),s.style.letterSpacing=`calc(${g}px * var(--total-scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class yw extends Fa{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class _w extends Fa{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return La.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=Sd,n.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s)){const c=l&&h.exportValue===e.exportValue;h.domElement&&(h.domElement.checked=c),t.setValue(h.id,{value:c})}t.setValue(s,{value:l})}),n.addEventListener("resetform",a=>{const o=e.defaultFieldValue||"Off";a.target.checked=o===e.exportValue}),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",a=>{const o={value(l){l.target.checked=l.detail.value!=="Off",t.setValue(s,{value:l.target.checked})}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Gb extends Fa{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const n=document.createElement("input");if(La.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=Sd,n.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s))t.setValue(h.id,{value:!1});t.setValue(s,{value:l})}),n.addEventListener("resetform",a=>{const o=e.defaultFieldValue;a.target.checked=o!=null&&o===e.buttonValue}),this.enableScripting&&this.hasJSActions){const a=e.buttonValue;n.addEventListener("updatefromsandbox",o=>{const l={value:h=>{const c=a===h.detail.value;for(const u of this._getElementsByName(h.target.name)){const f=c&&u.id===s;u.domElement&&(u.domElement.checked=f),t.setValue(u.id,{value:f})}}};this._dispatchEventFromSandbox(l,o)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],o=>o.target.checked)}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class ww extends Vb{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class Aw extends Fa{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");La.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=Sd;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",c=>{const u=this.data.defaultFieldValue;for(const f of i.options)f.selected=f.value===u});for(const c of this.data.options){const u=document.createElement("option");u.textContent=c.displayValue,u.value=c.exportValue,s.value.includes(c.exportValue)&&(u.setAttribute("selected",!0),n=!1),i.append(u)}let a=null;if(n){const c=document.createElement("option");c.value=" ",c.setAttribute("hidden",!0),c.setAttribute("selected",!0),i.prepend(c),a=()=>{c.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const o=c=>{const u=c?"value":"textContent",{options:f,multiple:g}=i;return g?Array.prototype.filter.call(f,b=>b.selected).map(b=>b[u]):f.selectedIndex===-1?null:f[f.selectedIndex][u]};let l=o(!1);const h=c=>{const u=c.target.options;return Array.prototype.map.call(u,f=>({displayValue:f.textContent,exportValue:f.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",c=>{const u={value(f){a==null||a();const g=f.detail.value,b=new Set(Array.isArray(g)?g:[g]);for(const v of i.options)v.selected=b.has(v.value);t.setValue(e,{value:o(!0)}),l=o(!1)},multipleSelection(f){i.multiple=!0},remove(f){const g=i.options,b=f.detail.remove;g[b].selected=!1,i.remove(b),g.length>0&&Array.prototype.findIndex.call(g,_=>_.selected)===-1&&(g[0].selected=!0),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},clear(f){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),l=o(!1)},insert(f){const{index:g,displayValue:b,exportValue:v}=f.detail.insert,_=i.children[g],w=document.createElement("option");w.textContent=b,w.value=v,_?_.before(w):i.append(w),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},items(f){const{items:g}=f.detail;for(;i.length!==0;)i.remove(0);for(const b of g){const{displayValue:v,exportValue:_}=b,w=document.createElement("option");w.textContent=v,w.value=_,i.append(w)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:o(!0),items:h(f)}),l=o(!1)},indices(f){const g=new Set(f.detail.indices);for(const b of f.target.options)b.selected=g.has(b.index);t.setValue(e,{value:o(!0)}),l=o(!1)},editable(f){f.target.disabled=!f.detail.editable}};this._dispatchEventFromSandbox(u,c)}),i.addEventListener("input",c=>{var g;const u=o(!0),f=o(!1);t.setValue(e,{value:u}),c.preventDefault(),(g=this.linkService.eventBus)==null||g.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:l,change:f,changeEx:u,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],c=>c.target.value)):i.addEventListener("input",function(c){t.setValue(e,{value:o(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class Sg extends jt{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:jt._hasPopupData(e)}),this.elements=s,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new Sw({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const s of this.elements)s.popup=t,s.container.ariaHasPopup="dialog",e.push(s.data.id),s.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(s=>`${wm}${s}`).join(",")),this.container}}var Bo,bf,vf,Vo,zo,Ht,Gi,Uo,pc,gc,Go,ji,Ns,Wi,mc,qi,bc,Zr,Qr,jo,qd,vc,Cg,yf,jb,_f,Wb,wf,qb,Af,Xb,Wo,Xd,qo,Yd,yc,xg;class Sw{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:n,contentsObj:a,richText:o,parent:l,rect:h,parentRect:c,open:u}){p(this,jo);p(this,vc);p(this,yf);p(this,_f);p(this,wf);p(this,Af);p(this,Wo);p(this,qo);p(this,yc);p(this,Bo,y(this,wf,qb).bind(this));p(this,bf,y(this,yc,xg).bind(this));p(this,vf,y(this,qo,Yd).bind(this));p(this,Vo,y(this,Wo,Xd).bind(this));p(this,zo,null);p(this,Ht,null);p(this,Gi,null);p(this,Uo,null);p(this,pc,null);p(this,gc,null);p(this,Go,null);p(this,ji,!1);p(this,Ns,null);p(this,Wi,null);p(this,mc,null);p(this,qi,null);p(this,bc,null);p(this,Zr,null);p(this,Qr,!1);var f;m(this,Ht,t),m(this,bc,i),m(this,Gi,a),m(this,qi,o),m(this,gc,l),m(this,zo,e),m(this,mc,h),m(this,Go,c),m(this,pc,s),m(this,Uo,Cm.toDateObject(n)),this.trigger=s.flatMap(g=>g.getElementsToTriggerPopup());for(const g of this.trigger)g.addEventListener("click",r(this,Vo)),g.addEventListener("mouseenter",r(this,vf)),g.addEventListener("mouseleave",r(this,bf)),g.classList.add("popupTriggerArea");for(const g of s)(f=g.container)==null||f.addEventListener("keydown",r(this,Bo));r(this,Ht).hidden=!0,u&&y(this,Wo,Xd).call(this)}render(){if(r(this,Ns))return;const t=m(this,Ns,document.createElement("div"));if(t.className="popup",r(this,zo)){const n=t.style.outlineColor=K.makeHexColor(...r(this,zo));t.style.backgroundColor=`color-mix(in srgb, ${n} 30%, white)`}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=r(this,bc),t.append(e),r(this,Uo)){const n=document.createElement("span");n.classList.add("popupDate"),n.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),n.setAttribute("data-l10n-args",JSON.stringify({dateObj:r(this,Uo).valueOf()})),e.append(n)}const i=r(this,jo,qd);if(i)Bb.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const n=this._formatContents(r(this,Gi));t.append(n)}r(this,Ht).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let n=0,a=i.length;n<a;++n){const o=i[n];s.append(document.createTextNode(o)),n<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){var s;r(this,Zr)||m(this,Zr,{contentsObj:r(this,Gi),richText:r(this,qi)}),t&&m(this,Wi,null),e&&(m(this,qi,y(this,_f,Wb).call(this,e)),m(this,Gi,null)),(s=r(this,Ns))==null||s.remove(),m(this,Ns,null)}resetEdited(){var t;r(this,Zr)&&({contentsObj:Fe(this,Gi)._,richText:Fe(this,qi)._}=r(this,Zr),m(this,Zr,null),(t=r(this,Ns))==null||t.remove(),m(this,Ns,null),m(this,Wi,null))}forceHide(){m(this,Qr,this.isVisible),r(this,Qr)&&(r(this,Ht).hidden=!0)}maybeShow(){r(this,Qr)&&(r(this,Ns)||y(this,qo,Yd).call(this),m(this,Qr,!1),r(this,Ht).hidden=!1)}get isVisible(){return r(this,Ht).hidden===!1}}Bo=new WeakMap,bf=new WeakMap,vf=new WeakMap,Vo=new WeakMap,zo=new WeakMap,Ht=new WeakMap,Gi=new WeakMap,Uo=new WeakMap,pc=new WeakMap,gc=new WeakMap,Go=new WeakMap,ji=new WeakMap,Ns=new WeakMap,Wi=new WeakMap,mc=new WeakMap,qi=new WeakMap,bc=new WeakMap,Zr=new WeakMap,Qr=new WeakMap,jo=new WeakSet,qd=function(){const t=r(this,qi),e=r(this,Gi);return t!=null&&t.str&&(!(e!=null&&e.str)||e.str===t.str)&&r(this,qi).html||null},vc=new WeakSet,Cg=function(){var t,e,s;return((s=(e=(t=r(this,jo,qd))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.fontSize)||0},yf=new WeakSet,jb=function(){var t,e,s;return((s=(e=(t=r(this,jo,qd))==null?void 0:t.attributes)==null?void 0:e.style)==null?void 0:s.color)||null},_f=new WeakSet,Wb=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:r(this,yf,jb),fontSize:r(this,vc,Cg)?`calc(${r(this,vc,Cg)}px * var(--total-scale-factor))`:""}};for(const n of t.split(`
`))e.push({name:"span",value:n,attributes:i});return s},wf=new WeakSet,qb=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&r(this,ji))&&y(this,Wo,Xd).call(this)},Af=new WeakSet,Xb=function(){if(r(this,Wi)!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:n}}}=r(this,gc);let a=!!r(this,Go),o=a?r(this,Go):r(this,mc);for(const b of r(this,pc))if(!o||K.intersect(b.data.rect,o)!==null){o=b.data.rect,a=!0;break}const l=K.normalizeRect([o[0],t[3]-o[1]+t[1],o[2],t[3]-o[3]+t[1]]),c=a?o[2]-o[0]+5:0,u=l[0]+c,f=l[1];m(this,Wi,[100*(u-i)/e,100*(f-n)/s]);const{style:g}=r(this,Ht);g.left=`${r(this,Wi)[0]}%`,g.top=`${r(this,Wi)[1]}%`},Wo=new WeakSet,Xd=function(){m(this,ji,!r(this,ji)),r(this,ji)?(y(this,qo,Yd).call(this),r(this,Ht).addEventListener("click",r(this,Vo)),r(this,Ht).addEventListener("keydown",r(this,Bo))):(y(this,yc,xg).call(this),r(this,Ht).removeEventListener("click",r(this,Vo)),r(this,Ht).removeEventListener("keydown",r(this,Bo)))},qo=new WeakSet,Yd=function(){r(this,Ns)||this.render(),this.isVisible?r(this,ji)&&r(this,Ht).classList.add("focused"):(y(this,Af,Xb).call(this),r(this,Ht).hidden=!1,r(this,Ht).style.zIndex=parseInt(r(this,Ht).style.zIndex)+1e3)},yc=new WeakSet,xg=function(){r(this,Ht).classList.remove("focused"),!(r(this,ji)||!this.isVisible)&&(r(this,Ht).hidden=!0,r(this,Ht).style.zIndex=parseInt(r(this,Ht).style.zIndex)-1e3)};class Yb extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=ot.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var _c;class Cw extends jt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});p(this,_c,null)}render(){this.container.classList.add("lineAnnotation");const{data:e,width:s,height:i}=this,n=this.svgFactory.create(s,i,!0),a=m(this,_c,this.svgFactory.createElement("svg:line"));return a.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),a.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),a.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),a.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),a.setAttribute("stroke-width",e.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),n.append(a),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,_c)}addHighlightArea(){this.container.classList.add("highlightArea")}}_c=new WeakMap;var wc;class xw extends jt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});p(this,wc,null)}render(){this.container.classList.add("squareAnnotation");const{data:e,width:s,height:i}=this,n=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=m(this,wc,this.svgFactory.createElement("svg:rect"));return o.setAttribute("x",a/2),o.setAttribute("y",a/2),o.setAttribute("width",s-a),o.setAttribute("height",i-a),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),n.append(o),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,wc)}addHighlightArea(){this.container.classList.add("highlightArea")}}wc=new WeakMap;var Ac;class Ew extends jt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});p(this,Ac,null)}render(){this.container.classList.add("circleAnnotation");const{data:e,width:s,height:i}=this,n=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=m(this,Ac,this.svgFactory.createElement("svg:ellipse"));return o.setAttribute("cx",s/2),o.setAttribute("cy",i/2),o.setAttribute("rx",s/2-a/2),o.setAttribute("ry",i/2-a/2),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),n.append(o),this.container.append(n),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,Ac)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ac=new WeakMap;var Sc;class Kb extends jt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});p(this,Sc,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,vertices:s,borderStyle:i,popupRef:n},width:a,height:o}=this;if(!s)return this.container;const l=this.svgFactory.create(a,o,!0);let h=[];for(let u=0,f=s.length;u<f;u+=2){const g=s[u]-e[0],b=e[3]-s[u+1];h.push(`${g},${b}`)}h=h.join(" ");const c=m(this,Sc,this.svgFactory.createElement(this.svgElementName));return c.setAttribute("points",h),c.setAttribute("stroke-width",i.width||1),c.setAttribute("stroke","transparent"),c.setAttribute("fill","transparent"),l.append(c),this.container.append(l),!n&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return r(this,Sc)}addHighlightArea(){this.container.classList.add("highlightArea")}}Sc=new WeakMap;class Tw extends Kb{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class kw extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var Cc,Jr,xc,Eg;class Pm extends jt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});p(this,xc);p(this,Cc,null);p(this,Jr,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?ot.HIGHLIGHT:ot.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,rotation:s,inkLists:i,borderStyle:n,popupRef:a}}=this,{transform:o,width:l,height:h}=y(this,xc,Eg).call(this,s,e),c=this.svgFactory.create(l,h,!0),u=m(this,Cc,this.svgFactory.createElement("svg:g"));c.append(u),u.setAttribute("stroke-width",n.width||1),u.setAttribute("stroke-linecap","round"),u.setAttribute("stroke-linejoin","round"),u.setAttribute("stroke-miterlimit",10),u.setAttribute("stroke","transparent"),u.setAttribute("fill","transparent"),u.setAttribute("transform",o);for(let f=0,g=i.length;f<g;f++){const b=this.svgFactory.createElement(this.svgElementName);r(this,Jr).push(b),b.setAttribute("points",i[f].join(",")),u.append(b)}return!a&&this.hasPopupData&&this._createPopup(),this.container.append(c),this._editOnDoubleClick(),this.container}updateEdited(e){super.updateEdited(e);const{thickness:s,points:i,rect:n}=e,a=r(this,Cc);if(s>=0&&a.setAttribute("stroke-width",s||1),i)for(let o=0,l=r(this,Jr).length;o<l;o++)r(this,Jr)[o].setAttribute("points",i[o].join(","));if(n){const{transform:o,width:l,height:h}=y(this,xc,Eg).call(this,this.data.rotation,n);a.parentElement.setAttribute("viewBox",`0 0 ${l} ${h}`),a.setAttribute("transform",o)}}getElementsToTriggerPopup(){return r(this,Jr)}addHighlightArea(){this.container.classList.add("highlightArea")}}Cc=new WeakMap,Jr=new WeakMap,xc=new WeakSet,Eg=function(e,s){switch(e){case 90:return{transform:`rotate(90) translate(${-s[0]},${s[1]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};case 180:return{transform:`rotate(180) translate(${-s[2]},${s[1]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]};case 270:return{transform:`rotate(270) translate(${-s[2]},${s[3]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};default:return{transform:`translate(${-s[0]},${s[3]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]}}};class Zb extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=ot.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class Iw extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class Pw extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Rw extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Qb extends jt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=ot.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var Ec,Tc,Tg;class Mw extends jt{constructor(e){var i;super(e,{isRenderable:!0});p(this,Tc);p(this,Ec,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,(i=this.linkService.eventBus)==null||i.dispatch("fileattachmentannotation",Ge({source:this},s))}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:s}=this;let i;s.hasAppearance||s.fillAlpha===0?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(s.name)?"paperclip":"pushpin"}.svg`,s.fillAlpha&&s.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(s.fillAlpha*100)}%);`)),i.addEventListener("dblclick",y(this,Tc,Tg).bind(this)),m(this,Ec,i);const{isMac:n}=Se.platform;return e.addEventListener("keydown",a=>{a.key==="Enter"&&(n?a.metaKey:a.ctrlKey)&&y(this,Tc,Tg).call(this)}),!s.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),e.append(i),e}getElementsToTriggerPopup(){return r(this,Ec)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ec=new WeakMap,Tc=new WeakSet,Tg=function(){var e;(e=this.downloadManager)==null||e.openOrDownloadData(this.content,this.filename)};var kc,ta,Dn,Ic,Pc,Ig,Rc,Pg;const $m=class $m{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:n,viewport:a,structTreeLayer:o}){p(this,Pc);p(this,Rc);p(this,kc,null);p(this,ta,null);p(this,Dn,new Map);p(this,Ic,null);this.div=t,m(this,kc,e),m(this,ta,s),m(this,Ic,o||null),this.page=n,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return r(this,Dn).size>0}render(t){return H(this,null,function*(){var a;const{annotations:e}=t,s=this.div;Ra(s,this.viewport);const i=new Map,n={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new gu,annotationStorage:t.annotationStorage||new Tm,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const o of e){if(o.noHTML)continue;const l=o.annotationType===Yt.POPUP;if(l){const u=i.get(o.id);if(!u)continue;n.elements=u}else if(o.rect[2]===o.rect[0]||o.rect[3]===o.rect[1])continue;n.data=o;const h=g0.create(n);if(!h.isRenderable)continue;if(!l&&o.popupRef){const u=i.get(o.popupRef);u?u.push(h):i.set(o.popupRef,[h])}const c=h.render();o.hidden&&(c.style.visibility="hidden"),yield y(this,Pc,Ig).call(this,c,o.id),h._isEditable&&(r(this,Dn).set(h.data.id,h),(a=this._annotationEditorUIManager)==null||a.renderAnnotationElement(h))}y(this,Rc,Pg).call(this)})}addLinkAnnotations(t,e){return H(this,null,function*(){const s={data:null,layer:this.div,linkService:e,svgFactory:new gu,parent:this};for(const i of t){i.borderStyle||(i.borderStyle=$m._defaultBorderStyle),s.data=i;const n=g0.create(s);if(!n.isRenderable)continue;const a=n.render();yield y(this,Pc,Ig).call(this,a,i.id)}})}update({viewport:t}){const e=this.div;this.viewport=t,Ra(e,{rotation:t.rotation}),y(this,Rc,Pg).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(r(this,Dn).values())}getEditableAnnotation(t){return r(this,Dn).get(t)}static get _defaultBorderStyle(){return gt(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:Oa.SOLID,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}};kc=new WeakMap,ta=new WeakMap,Dn=new WeakMap,Ic=new WeakMap,Pc=new WeakSet,Ig=function(t,e){return H(this,null,function*(){var a,o;const s=t.firstChild||t,i=s.id=`${wm}${e}`,n=yield(a=r(this,Ic))==null?void 0:a.getAriaAttributes(i);if(n)for(const[l,h]of n)s.setAttribute(l,h);this.div.append(t),(o=r(this,kc))==null||o.moveElementInDOM(this.div,t,s,!1)})},Rc=new WeakSet,Pg=function(){var e;if(!r(this,ta))return;const t=this.div;for(const[s,i]of r(this,ta)){const n=t.querySelector(`[data-annotation-id="${s}"]`);if(!n)continue;i.className="annotationContent";const{firstChild:a}=n;a?a.nodeName==="CANVAS"?a.replaceWith(i):a.classList.contains("annotationContent")?a.after(i):a.before(i):n.append(i);const o=r(this,Dn).get(s);o&&(o._hasNoCanvas?((e=this._annotationEditorUIManager)==null||e.setMissingCanvas(s,n.id,i),o._hasNoCanvas=!1):o.canvas=i)}r(this,ta).clear()};let kg=$m;const Rd=/\r\n?|\n/g;var Os,ss,Mc,ea,is,Sf,Jb,Cf,tv,xf,ev,Xo,Kd,Yo,Zd,Ko,Qd,Ef,sv,Lc,Mg,Tf,iv;const St=class St extends It{constructor(e){super(vi(Ge({},e),{name:"freeTextEditor"}));p(this,Sf);p(this,Cf);p(this,xf);p(this,Xo);p(this,Ko);p(this,Ef);p(this,Tf);p(this,Os,void 0);p(this,ss,"");p(this,Mc,`${this.id}-editor`);p(this,ea,null);p(this,is,void 0);m(this,Os,e.color||St._defaultColor||It._defaultLineColor),m(this,is,e.fontSize||St._defaultFontSize)}static get _keyboardManager(){const e=St.prototype,s=a=>a.isEmpty(),i=Ma.TRANSLATE_SMALL,n=Ma.TRANSLATE_BIG;return gt(this,"_keyboardManager",new wd([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-n,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[n,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-n],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,n],checker:s}]]))}static initialize(e,s){It.initialize(e,s);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case vt.FREETEXT_SIZE:St._defaultFontSize=s;break;case vt.FREETEXT_COLOR:St._defaultColor=s;break}}updateParams(e,s){switch(e){case vt.FREETEXT_SIZE:y(this,Sf,Jb).call(this,s);break;case vt.FREETEXT_COLOR:y(this,Cf,tv).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[vt.FREETEXT_SIZE,St._defaultFontSize],[vt.FREETEXT_COLOR,St._defaultColor||It._defaultLineColor]]}get propertiesToUpdate(){return[[vt.FREETEXT_SIZE,r(this,is)],[vt.FREETEXT_COLOR,r(this,Os)]]}_translateEmpty(e,s){this._uiManager.translateSelectedEditors(e,s,!0)}getInitialTranslation(){const e=this.parentScale;return[-St._internalPadding*e,-(St._internalPadding+r(this,is))*e]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(ot.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),m(this,ea,new AbortController);const e=this._uiManager.combinedSignal(r(this,ea));this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:e}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:e}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:e}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:e}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:e})}disableEditMode(){var e;this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",r(this,Mc)),this._isDraggable=!0,(e=r(this,ea))==null||e.abort(),m(this,ea,null),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),e.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(e){var s;this.width||(this.enableEditMode(),e&&this.editorDiv.focus(),(s=this._initialOptions)!=null&&s.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const e=r(this,ss),s=m(this,ss,y(this,xf,ev).call(this).trimEnd());if(e===s)return;const i=n=>{if(m(this,ss,n),!n){this.remove();return}y(this,Ko,Qd).call(this),this._uiManager.rebuild(this),y(this,Xo,Kd).call(this)};this.addCommands({cmd:()=>{i(s)},undo:()=>{i(e)},mustExec:!1}),y(this,Xo,Kd).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(e){this.enterInEditMode()}keydown(e){e.target===this.div&&e.key==="Enter"&&(this.enterInEditMode(),e.preventDefault())}editorDivKeydown(e){St._keyboardManager.exec(this,e)}editorDivFocus(e){this.isEditing=!0}editorDivBlur(e){this.isEditing=!1}editorDivInput(e){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let e,s;(this._isCopy||this.annotationElementId)&&(e=this.x,s=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",r(this,Mc)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${r(this,is)}px * var(--total-scale-factor))`,i.color=r(this,Os),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),Em(this,this.div,["dblclick","keydown"]),this._isCopy||this.annotationElementId){const[n,a]=this.parentDimensions;if(this.annotationElementId){const{position:o}=this._initialData;let[l,h]=this.getInitialTranslation();[l,h]=this.pageTranslationToScreen(l,h);const[c,u]=this.pageDimensions,[f,g]=this.pageTranslation;let b,v;switch(this.rotation){case 0:b=e+(o[0]-f)/c,v=s+this.height-(o[1]-g)/u;break;case 90:b=e+(o[0]-f)/c,v=s-(o[1]-g)/u,[l,h]=[h,-l];break;case 180:b=e-this.width+(o[0]-f)/c,v=s-(o[1]-g)/u,[l,h]=[-l,-h];break;case 270:b=e+(o[0]-f-this.height*u)/c,v=s+(o[1]-g-this.width*c)/u,[l,h]=[-h,l];break}this.setAt(b*n,v*a,l,h)}else this._moveAfterPaste(e,s);y(this,Ko,Qd).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var b,v,_;const s=e.clipboardData||window.clipboardData,{types:i}=s;if(i.length===1&&i[0]==="text/plain")return;e.preventDefault();const n=y(b=St,Lc,Mg).call(b,s.getData("text")||"").replaceAll(Rd,`
`);if(!n)return;const a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();const o=a.getRangeAt(0);if(!n.includes(`
`)){o.insertNode(document.createTextNode(n)),this.editorDiv.normalize(),a.collapseToStart();return}const{startContainer:l,startOffset:h}=o,c=[],u=[];if(l.nodeType===Node.TEXT_NODE){const w=l.parentElement;if(u.push(l.nodeValue.slice(h).replaceAll(Rd,"")),w!==this.editorDiv){let A=c;for(const C of this.editorDiv.childNodes){if(C===w){A=u;continue}A.push(y(v=St,Yo,Zd).call(v,C))}}c.push(l.nodeValue.slice(0,h).replaceAll(Rd,""))}else if(l===this.editorDiv){let w=c,A=0;for(const C of this.editorDiv.childNodes)A++===h&&(w=u),w.push(y(_=St,Yo,Zd).call(_,C))}m(this,ss,`${c.join(`
`)}${n}${u.join(`
`)}`),y(this,Ko,Qd).call(this);const f=new Range;let g=Math.sumPrecise(c.map(w=>w.length));for(const{firstChild:w}of this.editorDiv.childNodes)if(w.nodeType===Node.TEXT_NODE){const A=w.nodeValue.length;if(g<=A){f.setStart(w,g),f.setEnd(w,g);break}g-=A}a.removeAllRanges(),a.addRange(f)}get contentDiv(){return this.editorDiv}static deserialize(e,s,i){return H(this,null,function*(){var o;let n=null;if(e instanceof Yb){const{data:{defaultAppearanceData:{fontSize:l,fontColor:h},rect:c,rotation:u,id:f,popupRef:g},textContent:b,textPosition:v,parent:{page:{pageNumber:_}}}=e;if(!b||b.length===0)return null;n=e={annotationType:ot.FREETEXT,color:Array.from(h),fontSize:l,value:b.join(`
`),position:v,pageIndex:_-1,rect:c.slice(0),rotation:u,id:f,deleted:!1,popupRef:g}}const a=yield yi(St,this,"deserialize").call(this,e,s,i);return m(a,is,e.fontSize),m(a,Os,K.makeHexColor(...e.color)),m(a,ss,y(o=St,Lc,Mg).call(o,e.value)),a.annotationElementId=e.id||null,a._initialData=n,a})}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s=St._internalPadding*this.parentScale,i=this.getRect(s,s),n=It._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:r(this,Os)),a={annotationType:ot.FREETEXT,color:n,fontSize:r(this,is),value:y(this,Ef,sv).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(a.isCopy=!0,a):this.annotationElementId&&!y(this,Tf,iv).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${r(this,is)}px * var(--total-scale-factor))`,i.color=r(this,Os),s.replaceChildren();for(const a of r(this,ss).split(`
`)){const o=document.createElement("div");o.append(a?document.createTextNode(a):document.createElement("br")),s.append(o)}const n=St._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(n,n),popupContent:r(this,ss)}),s}resetAnnotationElement(e){super.resetAnnotationElement(e),e.resetEdited()}};Os=new WeakMap,ss=new WeakMap,Mc=new WeakMap,ea=new WeakMap,is=new WeakMap,Sf=new WeakSet,Jb=function(e){const s=n=>{this.editorDiv.style.fontSize=`calc(${n}px * var(--total-scale-factor))`,this.translate(0,-(n-r(this,is))*this.parentScale),m(this,is,n),y(this,Xo,Kd).call(this)},i=r(this,is);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:vt.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},Cf=new WeakSet,tv=function(e){const s=n=>{m(this,Os,this.editorDiv.style.color=n)},i=r(this,Os);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:vt.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},xf=new WeakSet,ev=function(){var i;const e=[];this.editorDiv.normalize();let s=null;for(const n of this.editorDiv.childNodes)(s==null?void 0:s.nodeType)===Node.TEXT_NODE&&n.nodeName==="BR"||(e.push(y(i=St,Yo,Zd).call(i,n)),s=n);return e.join(`
`)},Xo=new WeakSet,Kd=function(){const[e,s]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:n,div:a}=this,o=a.style.display,l=a.classList.contains("hidden");a.classList.remove("hidden"),a.style.display="hidden",n.div.append(this.div),i=a.getBoundingClientRect(),a.remove(),a.style.display=o,a.classList.toggle("hidden",l)}this.rotation%180===this.parentRotation%180?(this.width=i.width/e,this.height=i.height/s):(this.width=i.height/e,this.height=i.width/s),this.fixAndSetPosition()},Yo=new WeakSet,Zd=function(e){return(e.nodeType===Node.TEXT_NODE?e.nodeValue:e.innerText).replaceAll(Rd,"")},Ko=new WeakSet,Qd=function(){if(this.editorDiv.replaceChildren(),!!r(this,ss))for(const e of r(this,ss).split(`
`)){const s=document.createElement("div");s.append(e?document.createTextNode(e):document.createElement("br")),this.editorDiv.append(s)}},Ef=new WeakSet,sv=function(){return r(this,ss).replaceAll(" "," ")},Lc=new WeakSet,Mg=function(e){return e.replaceAll(" "," ")},Tf=new WeakSet,iv=function(e){const{value:s,fontSize:i,color:n,pageIndex:a}=this._initialData;return this._hasBeenMoved||e.value!==s||e.fontSize!==i||e.color.some((o,l)=>o!==n[l])||e.pageIndex!==a},p(St,Yo),p(St,Lc),O(St,"_freeTextDefaultContent",""),O(St,"_internalPadding",0),O(St,"_defaultColor",null),O(St,"_defaultFontSize",10),O(St,"_type","freetext"),O(St,"_editorType",ot.FREETEXT);let Rg=St;class W{toSVGPath(){xt("Abstract method `toSVGPath` must be implemented.")}get box(){xt("Abstract getter `box` must be implemented.")}serialize(t,e){xt("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,n,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o]*i,a[o+1]=s+t[o+1]*n;return a}static _rescaleAndSwap(t,e,s,i,n,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o+1]*i,a[o+1]=s+t[o]*n;return a}static _translate(t,e,s,i){i||(i=new Float32Array(t.length));for(let n=0,a=t.length;n<a;n+=2)i[n]=e+t[n],i[n+1]=s+t[n+1];return i}static svgRound(t){return Math.round(t*1e4)}static _normalizePoint(t,e,s,i,n){switch(n){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,n,a){return[(t+5*s)/6,(e+5*i)/6,(5*s+n)/6,(5*i+a)/6,(s+n)/2,(i+a)/2]}}O(W,"PRECISION",1e-4);var ns,$s,Zo,Qo,hi,pt,sa,ia,Dc,Fc,Jo,tl,Fn,Nc,kf,If,na,Wl,Pf,nv,Rf,rv,Mf,av,Lf,ov,Df,lv,Ff,hv;const Ei=class Ei{constructor({x:t,y:e},s,i,n,a,o=0){p(this,na);p(this,Pf);p(this,Rf);p(this,Mf);p(this,Lf);p(this,Df);p(this,Ff);p(this,ns,void 0);p(this,$s,[]);p(this,Zo,void 0);p(this,Qo,void 0);p(this,hi,[]);p(this,pt,new Float32Array(18));p(this,sa,void 0);p(this,ia,void 0);p(this,Dc,void 0);p(this,Fc,void 0);p(this,Jo,void 0);p(this,tl,void 0);p(this,Fn,[]);m(this,ns,s),m(this,tl,n*i),m(this,Qo,a),r(this,pt).set([NaN,NaN,NaN,NaN,t,e],6),m(this,Zo,o),m(this,Fc,r(Ei,Nc)*i),m(this,Dc,r(Ei,If)*i),m(this,Jo,i),r(this,Fn).push(t,e)}isEmpty(){return isNaN(r(this,pt)[8])}add({x:t,y:e}){var M;m(this,sa,t),m(this,ia,e);const[s,i,n,a]=r(this,ns);let[o,l,h,c]=r(this,pt).subarray(8,12);const u=t-h,f=e-c,g=Math.hypot(u,f);if(g<r(this,Dc))return!1;const b=g-r(this,Fc),v=b/g,_=v*u,w=v*f;let A=o,C=l;o=h,l=c,h+=_,c+=w,(M=r(this,Fn))==null||M.push(t,e);const S=-w/b,T=_/b,x=S*r(this,tl),k=T*r(this,tl);return r(this,pt).set(r(this,pt).subarray(2,8),0),r(this,pt).set([h+x,c+k],4),r(this,pt).set(r(this,pt).subarray(14,18),12),r(this,pt).set([h-x,c-k],16),isNaN(r(this,pt)[6])?(r(this,hi).length===0&&(r(this,pt).set([o+x,l+k],2),r(this,hi).push(NaN,NaN,NaN,NaN,(o+x-s)/n,(l+k-i)/a),r(this,pt).set([o-x,l-k],14),r(this,$s).push(NaN,NaN,NaN,NaN,(o-x-s)/n,(l-k-i)/a)),r(this,pt).set([A,C,o,l,h,c],6),!this.isEmpty()):(r(this,pt).set([A,C,o,l,h,c],6),Math.abs(Math.atan2(C-l,A-o)-Math.atan2(w,_))<Math.PI/2?([o,l,h,c]=r(this,pt).subarray(2,6),r(this,hi).push(NaN,NaN,NaN,NaN,((o+h)/2-s)/n,((l+c)/2-i)/a),[o,l,A,C]=r(this,pt).subarray(14,18),r(this,$s).push(NaN,NaN,NaN,NaN,((A+o)/2-s)/n,((C+l)/2-i)/a),!0):([A,C,o,l,h,c]=r(this,pt).subarray(0,6),r(this,hi).push(((A+5*o)/6-s)/n,((C+5*l)/6-i)/a,((5*o+h)/6-s)/n,((5*l+c)/6-i)/a,((o+h)/2-s)/n,((l+c)/2-i)/a),[h,c,o,l,A,C]=r(this,pt).subarray(12,18),r(this,$s).push(((A+5*o)/6-s)/n,((C+5*l)/6-i)/a,((5*o+h)/6-s)/n,((5*l+c)/6-i)/a,((o+h)/2-s)/n,((l+c)/2-i)/a),!0))}toSVGPath(){if(this.isEmpty())return"";const t=r(this,hi),e=r(this,$s);if(isNaN(r(this,pt)[6])&&!this.isEmpty())return y(this,Pf,nv).call(this);const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);y(this,Mf,av).call(this,s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return y(this,Rf,rv).call(this,s),s.join(" ")}newFreeDrawOutline(t,e,s,i,n,a){return new cv(t,e,s,i,n,a)}getOutlines(){var u,f;const t=r(this,hi),e=r(this,$s),s=r(this,pt),[i,n,a,o]=r(this,ns),l=new Float32Array(((f=(u=r(this,Fn))==null?void 0:u.length)!=null?f:0)+2);for(let g=0,b=l.length-2;g<b;g+=2)l[g]=(r(this,Fn)[g]-i)/a,l[g+1]=(r(this,Fn)[g+1]-n)/o;if(l[l.length-2]=(r(this,sa)-i)/a,l[l.length-1]=(r(this,ia)-n)/o,isNaN(s[6])&&!this.isEmpty())return y(this,Lf,ov).call(this,l);const h=new Float32Array(r(this,hi).length+24+r(this,$s).length);let c=t.length;for(let g=0;g<c;g+=2){if(isNaN(t[g])){h[g]=h[g+1]=NaN;continue}h[g]=t[g],h[g+1]=t[g+1]}c=y(this,Ff,hv).call(this,h,c);for(let g=e.length-6;g>=6;g-=6)for(let b=0;b<6;b+=2){if(isNaN(e[g+b])){h[c]=h[c+1]=NaN,c+=2;continue}h[c]=e[g+b],h[c+1]=e[g+b+1],c+=2}return y(this,Df,lv).call(this,h,c),this.newFreeDrawOutline(h,l,r(this,ns),r(this,Jo),r(this,Zo),r(this,Qo))}};ns=new WeakMap,$s=new WeakMap,Zo=new WeakMap,Qo=new WeakMap,hi=new WeakMap,pt=new WeakMap,sa=new WeakMap,ia=new WeakMap,Dc=new WeakMap,Fc=new WeakMap,Jo=new WeakMap,tl=new WeakMap,Fn=new WeakMap,Nc=new WeakMap,kf=new WeakMap,If=new WeakMap,na=new WeakSet,Wl=function(){const t=r(this,pt).subarray(4,6),e=r(this,pt).subarray(16,18),[s,i,n,a]=r(this,ns);return[(r(this,sa)+(t[0]-e[0])/2-s)/n,(r(this,ia)+(t[1]-e[1])/2-i)/a,(r(this,sa)+(e[0]-t[0])/2-s)/n,(r(this,ia)+(e[1]-t[1])/2-i)/a]},Pf=new WeakSet,nv=function(){const[t,e,s,i]=r(this,ns),[n,a,o,l]=y(this,na,Wl).call(this);return`M${(r(this,pt)[2]-t)/s} ${(r(this,pt)[3]-e)/i} L${(r(this,pt)[4]-t)/s} ${(r(this,pt)[5]-e)/i} L${n} ${a} L${o} ${l} L${(r(this,pt)[16]-t)/s} ${(r(this,pt)[17]-e)/i} L${(r(this,pt)[14]-t)/s} ${(r(this,pt)[15]-e)/i} Z`},Rf=new WeakSet,rv=function(t){const e=r(this,$s);t.push(`L${e[4]} ${e[5]} Z`)},Mf=new WeakSet,av=function(t){const[e,s,i,n]=r(this,ns),a=r(this,pt).subarray(4,6),o=r(this,pt).subarray(16,18),[l,h,c,u]=y(this,na,Wl).call(this);t.push(`L${(a[0]-e)/i} ${(a[1]-s)/n} L${l} ${h} L${c} ${u} L${(o[0]-e)/i} ${(o[1]-s)/n}`)},Lf=new WeakSet,ov=function(t){const e=r(this,pt),[s,i,n,a]=r(this,ns),[o,l,h,c]=y(this,na,Wl).call(this),u=new Float32Array(36);return u.set([NaN,NaN,NaN,NaN,(e[2]-s)/n,(e[3]-i)/a,NaN,NaN,NaN,NaN,(e[4]-s)/n,(e[5]-i)/a,NaN,NaN,NaN,NaN,o,l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,(e[16]-s)/n,(e[17]-i)/a,NaN,NaN,NaN,NaN,(e[14]-s)/n,(e[15]-i)/a],0),this.newFreeDrawOutline(u,t,r(this,ns),r(this,Jo),r(this,Zo),r(this,Qo))},Df=new WeakSet,lv=function(t,e){const s=r(this,$s);return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+=6},Ff=new WeakSet,hv=function(t,e){const s=r(this,pt).subarray(4,6),i=r(this,pt).subarray(16,18),[n,a,o,l]=r(this,ns),[h,c,u,f]=y(this,na,Wl).call(this);return t.set([NaN,NaN,NaN,NaN,(s[0]-n)/o,(s[1]-a)/l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,u,f,NaN,NaN,NaN,NaN,(i[0]-n)/o,(i[1]-a)/l],e),e+=24},p(Ei,Nc,8),p(Ei,kf,2),p(Ei,If,r(Ei,Nc)+r(Ei,kf));let mu=Ei;var el,ra,Xi,Oc,rs,$c,Ut,Nf,dv;class cv extends W{constructor(e,s,i,n,a,o){super();p(this,Nf);p(this,el,void 0);p(this,ra,new Float32Array(4));p(this,Xi,void 0);p(this,Oc,void 0);p(this,rs,void 0);p(this,$c,void 0);p(this,Ut,void 0);m(this,Ut,e),m(this,rs,s),m(this,el,i),m(this,$c,n),m(this,Xi,a),m(this,Oc,o),this.lastPoint=[NaN,NaN],y(this,Nf,dv).call(this,o);const[l,h,c,u]=r(this,ra);for(let f=0,g=e.length;f<g;f+=2)e[f]=(e[f]-l)/c,e[f+1]=(e[f+1]-h)/u;for(let f=0,g=s.length;f<g;f+=2)s[f]=(s[f]-l)/c,s[f+1]=(s[f+1]-h)/u}toSVGPath(){const e=[`M${r(this,Ut)[4]} ${r(this,Ut)[5]}`];for(let s=6,i=r(this,Ut).length;s<i;s+=6){if(isNaN(r(this,Ut)[s])){e.push(`L${r(this,Ut)[s+4]} ${r(this,Ut)[s+5]}`);continue}e.push(`C${r(this,Ut)[s]} ${r(this,Ut)[s+1]} ${r(this,Ut)[s+2]} ${r(this,Ut)[s+3]} ${r(this,Ut)[s+4]} ${r(this,Ut)[s+5]}`)}return e.push("Z"),e.join(" ")}serialize([e,s,i,n],a){const o=i-e,l=n-s;let h,c;switch(a){case 0:h=W._rescale(r(this,Ut),e,n,o,-l),c=W._rescale(r(this,rs),e,n,o,-l);break;case 90:h=W._rescaleAndSwap(r(this,Ut),e,s,o,l),c=W._rescaleAndSwap(r(this,rs),e,s,o,l);break;case 180:h=W._rescale(r(this,Ut),i,s,-o,l),c=W._rescale(r(this,rs),i,s,-o,l);break;case 270:h=W._rescaleAndSwap(r(this,Ut),i,n,-o,-l),c=W._rescaleAndSwap(r(this,rs),i,n,-o,-l);break}return{outline:Array.from(h),points:[Array.from(c)]}}get box(){return r(this,ra)}newOutliner(e,s,i,n,a,o=0){return new mu(e,s,i,n,a,o)}getNewOutline(e,s){const[i,n,a,o]=r(this,ra),[l,h,c,u]=r(this,el),f=a*c,g=o*u,b=i*c+l,v=n*u+h,_=this.newOutliner({x:r(this,rs)[0]*f+b,y:r(this,rs)[1]*g+v},r(this,el),r(this,$c),e,r(this,Oc),s!=null?s:r(this,Xi));for(let w=2;w<r(this,rs).length;w+=2)_.add({x:r(this,rs)[w]*f+b,y:r(this,rs)[w+1]*g+v});return _.getOutlines()}}el=new WeakMap,ra=new WeakMap,Xi=new WeakMap,Oc=new WeakMap,rs=new WeakMap,$c=new WeakMap,Ut=new WeakMap,Nf=new WeakSet,dv=function(e){const s=r(this,Ut);let i=s[4],n=s[5];const a=[i,n,i,n];let o=i,l=n;const h=e?Math.max:Math.min;for(let u=6,f=s.length;u<f;u+=6){const g=s[u+4],b=s[u+5];if(isNaN(s[u]))K.pointBoundingBox(g,b,a),l<b?(o=g,l=b):l===b&&(o=h(o,g));else{const v=[1/0,1/0,-1/0,-1/0];K.bezierBoundingBox(i,n,...s.slice(u,u+6),v),K.rectBoundingBox(...v,a),l<v[3]?(o=v[2],l=v[3]):l===v[3]&&(o=h(o,v[2]))}i=g,n=b}const c=r(this,ra);c[0]=a[0]-r(this,Xi),c[1]=a[1]-r(this,Xi),c[2]=a[2]-a[0]+2*r(this,Xi),c[3]=a[3]-a[1]+2*r(this,Xi),this.lastPoint=[o,l]};var Hc,Bc,Nn,Hs,Of,uv,sl,Jd,$f,fv,Hf,pv,Vc,Dg;class Lg{constructor(t,e=0,s=0,i=!0){p(this,Of);p(this,sl);p(this,$f);p(this,Hf);p(this,Vc);p(this,Hc,void 0);p(this,Bc,void 0);p(this,Nn,[]);p(this,Hs,[]);const n=[1/0,1/0,-1/0,-1/0],o=oe(10,-4);for(const{x:b,y:v,width:_,height:w}of t){const A=Math.floor((b-e)/o)*o,C=Math.ceil((b+_+e)/o)*o,S=Math.floor((v-e)/o)*o,T=Math.ceil((v+w+e)/o)*o,x=[A,S,T,!0],k=[C,S,T,!1];r(this,Nn).push(x,k),K.rectBoundingBox(A,S,C,T,n)}const l=n[2]-n[0]+2*s,h=n[3]-n[1]+2*s,c=n[0]-s,u=n[1]-s,f=r(this,Nn).at(i?-1:-2),g=[f[0],f[2]];for(const b of r(this,Nn)){const[v,_,w]=b;b[0]=(v-c)/l,b[1]=(_-u)/h,b[2]=(w-u)/h}m(this,Hc,new Float32Array([c,u,l,h])),m(this,Bc,g)}getOutlines(){r(this,Nn).sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of r(this,Nn))e[3]?(t.push(...y(this,Vc,Dg).call(this,e)),y(this,$f,fv).call(this,e)):(y(this,Hf,pv).call(this,e),t.push(...y(this,Vc,Dg).call(this,e)));return y(this,Of,uv).call(this,t)}}Hc=new WeakMap,Bc=new WeakMap,Nn=new WeakMap,Hs=new WeakMap,Of=new WeakSet,uv=function(t){const e=[],s=new Set;for(const a of t){const[o,l,h]=a;e.push([o,l,a],[o,h,a])}e.sort((a,o)=>a[1]-o[1]||a[0]-o[0]);for(let a=0,o=e.length;a<o;a+=2){const l=e[a][2],h=e[a+1][2];l.push(h),h.push(l),s.add(l),s.add(h)}const i=[];let n;for(;s.size>0;){const a=s.values().next().value;let[o,l,h,c,u]=a;s.delete(a);let f=o,g=l;for(n=[o,h],i.push(n);;){let b;if(s.has(c))b=c;else if(s.has(u))b=u;else break;s.delete(b),[o,l,h,c,u]=b,f!==o&&(n.push(f,g,o,g===l?l:h),f=o),g=g===l?h:l}n.push(f,g)}return new Lw(i,r(this,Hc),r(this,Bc))},sl=new WeakSet,Jd=function(t){const e=r(this,Hs);let s=0,i=e.length-1;for(;s<=i;){const n=s+i>>1,a=e[n][0];if(a===t)return n;a<t?s=n+1:i=n-1}return i+1},$f=new WeakSet,fv=function([,t,e]){const s=y(this,sl,Jd).call(this,t);r(this,Hs).splice(s,0,[t,e])},Hf=new WeakSet,pv=function([,t,e]){const s=y(this,sl,Jd).call(this,t);for(let i=s;i<r(this,Hs).length;i++){const[n,a]=r(this,Hs)[i];if(n!==t)break;if(n===t&&a===e){r(this,Hs).splice(i,1);return}}for(let i=s-1;i>=0;i--){const[n,a]=r(this,Hs)[i];if(n!==t)break;if(n===t&&a===e){r(this,Hs).splice(i,1);return}}},Vc=new WeakSet,Dg=function(t){const[e,s,i]=t,n=[[e,s,i]],a=y(this,sl,Jd).call(this,i);for(let o=0;o<a;o++){const[l,h]=r(this,Hs)[o];for(let c=0,u=n.length;c<u;c++){const[,f,g]=n[c];if(!(h<=f||g<=l)){if(f>=l){if(g>h)n[c][1]=h;else{if(u===1)return[];n.splice(c,1),c--,u--}continue}n[c][2]=l,g>h&&n.push([e,h,g])}}}return n};var zc,il;class Lw extends W{constructor(e,s,i){super();p(this,zc,void 0);p(this,il,void 0);m(this,il,e),m(this,zc,s),this.lastPoint=i}toSVGPath(){const e=[];for(const s of r(this,il)){let[i,n]=s;e.push(`M${i} ${n}`);for(let a=2;a<s.length;a+=2){const o=s[a],l=s[a+1];o===i?(e.push(`V${l}`),n=l):l===n&&(e.push(`H${o}`),i=o)}e.push("Z")}return e.join(" ")}serialize([e,s,i,n],a){const o=[],l=i-e,h=n-s;for(const c of r(this,il)){const u=new Array(c.length);for(let f=0;f<c.length;f+=2)u[f]=e+c[f]*l,u[f+1]=n-c[f+1]*h;o.push(u)}return o}get box(){return r(this,zc)}get classNamesForOutlining(){return["highlightOutline"]}}zc=new WeakMap,il=new WeakMap;class Fg extends mu{newFreeDrawOutline(t,e,s,i,n,a){return new Dw(t,e,s,i,n,a)}}class Dw extends cv{newOutliner(t,e,s,i,n,a=0){return new Fg(t,e,s,i,n,a)}}var Bs,aa,nl,ne,Uc,rl,Gc,jc,On,Vs,al,Wc,qc,Ng,Xc,Og,Yc,$g,Yi,sr,Bf,gv,ci,ln;const We=class We{constructor({editor:t=null,uiManager:e=null}){p(this,qc);p(this,Xc);p(this,Yc);p(this,Yi);p(this,Bf);p(this,ci);p(this,Bs,null);p(this,aa,null);p(this,nl,void 0);p(this,ne,null);p(this,Uc,!1);p(this,rl,!1);p(this,Gc,null);p(this,jc,void 0);p(this,On,null);p(this,Vs,null);p(this,al,void 0);var s;t?(m(this,rl,!1),m(this,al,vt.HIGHLIGHT_COLOR),m(this,Gc,t)):(m(this,rl,!0),m(this,al,vt.HIGHLIGHT_DEFAULT_COLOR)),m(this,Vs,(t==null?void 0:t._uiManager)||e),m(this,jc,r(this,Vs)._eventBus),m(this,nl,(t==null?void 0:t.color)||((s=r(this,Vs))==null?void 0:s.highlightColors.values().next().value)||"#FFFF98"),r(We,Wc)||m(We,Wc,Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"}))}static get _keyboardManager(){return gt(this,"_keyboardManager",new wd([[["Escape","mac+Escape"],We.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],We.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],We.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],We.prototype._moveToPrevious],[["Home","mac+Home"],We.prototype._moveToBeginning],[["End","mac+End"],We.prototype._moveToEnd]]))}renderButton(){const t=m(this,Bs,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=r(this,Vs)._signal;t.addEventListener("click",y(this,Yi,sr).bind(this),{signal:e}),t.addEventListener("keydown",y(this,Yc,$g).bind(this),{signal:e});const s=m(this,aa,document.createElement("span"));return s.className="swatch",s.setAttribute("aria-hidden",!0),s.style.backgroundColor=r(this,nl),t.append(s),t}renderMainDropdown(){const t=m(this,ne,y(this,qc,Ng).call(this));return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}_colorSelectFromKeyboard(t){if(t.target===r(this,Bs)){y(this,Yi,sr).call(this,t);return}const e=t.target.getAttribute("data-color");e&&y(this,Xc,Og).call(this,e,t)}_moveToNext(t){var e,s;if(!r(this,ci,ln)){y(this,Yi,sr).call(this,t);return}if(t.target===r(this,Bs)){(e=r(this,ne).firstChild)==null||e.focus();return}(s=t.target.nextSibling)==null||s.focus()}_moveToPrevious(t){var e,s;if(t.target===((e=r(this,ne))==null?void 0:e.firstChild)||t.target===r(this,Bs)){r(this,ci,ln)&&this._hideDropdownFromKeyboard();return}r(this,ci,ln)||y(this,Yi,sr).call(this,t),(s=t.target.previousSibling)==null||s.focus()}_moveToBeginning(t){var e;if(!r(this,ci,ln)){y(this,Yi,sr).call(this,t);return}(e=r(this,ne).firstChild)==null||e.focus()}_moveToEnd(t){var e;if(!r(this,ci,ln)){y(this,Yi,sr).call(this,t);return}(e=r(this,ne).lastChild)==null||e.focus()}hideDropdown(){var t,e;(t=r(this,ne))==null||t.classList.add("hidden"),(e=r(this,On))==null||e.abort(),m(this,On,null)}_hideDropdownFromKeyboard(){var t;if(!r(this,rl)){if(!r(this,ci,ln)){(t=r(this,Gc))==null||t.unselect();return}this.hideDropdown(),r(this,Bs).focus({preventScroll:!0,focusVisible:r(this,Uc)})}}updateColor(t){if(r(this,aa)&&(r(this,aa).style.backgroundColor=t),!r(this,ne))return;const e=r(this,Vs).highlightColors.values();for(const s of r(this,ne).children)s.setAttribute("aria-selected",e.next().value===t)}destroy(){var t,e;(t=r(this,Bs))==null||t.remove(),m(this,Bs,null),m(this,aa,null),(e=r(this,ne))==null||e.remove(),m(this,ne,null)}};Bs=new WeakMap,aa=new WeakMap,nl=new WeakMap,ne=new WeakMap,Uc=new WeakMap,rl=new WeakMap,Gc=new WeakMap,jc=new WeakMap,On=new WeakMap,Vs=new WeakMap,al=new WeakMap,Wc=new WeakMap,qc=new WeakSet,Ng=function(){const t=document.createElement("div"),e=r(this,Vs)._signal;t.addEventListener("contextmenu",qs,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[s,i]of r(this,Vs).highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",i),n.title=s,n.setAttribute("data-l10n-id",r(We,Wc)[s]);const a=document.createElement("span");n.append(a),a.className="swatch",a.style.backgroundColor=i,n.setAttribute("aria-selected",i===r(this,nl)),n.addEventListener("click",y(this,Xc,Og).bind(this,i),{signal:e}),t.append(n)}return t.addEventListener("keydown",y(this,Yc,$g).bind(this),{signal:e}),t},Xc=new WeakSet,Og=function(t,e){e.stopPropagation(),r(this,jc).dispatch("switchannotationeditorparams",{source:this,type:r(this,al),value:t})},Yc=new WeakSet,$g=function(t){We._keyboardManager.exec(this,t)},Yi=new WeakSet,sr=function(t){if(r(this,ci,ln)){this.hideDropdown();return}if(m(this,Uc,t.detail===0),r(this,On)||(m(this,On,new AbortController),window.addEventListener("pointerdown",y(this,Bf,gv).bind(this),{signal:r(this,Vs).combinedSignal(r(this,On))})),r(this,ne)){r(this,ne).classList.remove("hidden");return}const e=m(this,ne,y(this,qc,Ng).call(this));r(this,Bs).append(e)},Bf=new WeakSet,gv=function(t){var e;(e=r(this,ne))!=null&&e.contains(t.target)||this.hideDropdown()},ci=new WeakSet,ln=function(){return r(this,ne)&&!r(this,ne).classList.contains("hidden")},p(We,Wc,null);let bu=We;var ol,Kc,Ki,oa,ll,Ye,Zc,Qc,la,As,as,_e,hl,Zi,Pe,cl,Ss,Jc,td,Hg,dl,tu,Vf,mv,zf,bv,Uf,vv,ed,Bg,Qi,ir,$n,Ua,Gf,yv,ul,eu,ha,ql,jf,_v,Wf,wv,qf,Av,Xf,Sv,Yf,Cv;const bt=class bt extends It{constructor(e){super(vi(Ge({},e),{name:"highlightEditor"}));p(this,td);p(this,dl);p(this,Vf);p(this,zf);p(this,Uf);p(this,ed);p(this,Qi);p(this,Gf);p(this,ul);p(this,ha);p(this,jf);p(this,Wf);p(this,Yf);p(this,ol,null);p(this,Kc,0);p(this,Ki,void 0);p(this,oa,null);p(this,ll,null);p(this,Ye,null);p(this,Zc,null);p(this,Qc,0);p(this,la,null);p(this,As,null);p(this,as,null);p(this,_e,!1);p(this,hl,null);p(this,Zi,void 0);p(this,Pe,null);p(this,cl,"");p(this,Ss,void 0);p(this,Jc,"");this.color=e.color||bt._defaultColor,m(this,Ss,e.thickness||bt._defaultThickness),m(this,Zi,e.opacity||bt._defaultOpacity),m(this,Ki,e.boxes||null),m(this,Jc,e.methodOfCreation||""),m(this,cl,e.text||""),this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",e.highlightId>-1?(m(this,_e,!0),y(this,dl,tu).call(this,e),y(this,Qi,ir).call(this)):r(this,Ki)&&(m(this,ol,e.anchorNode),m(this,Kc,e.anchorOffset),m(this,Zc,e.focusNode),m(this,Qc,e.focusOffset),y(this,td,Hg).call(this),y(this,Qi,ir).call(this),this.rotate(this.rotation))}static get _keyboardManager(){const e=bt.prototype;return gt(this,"_keyboardManager",new wd([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:r(this,_e)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:r(this,Ss),methodOfCreation:r(this,Jc)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(e){return{numberOfColors:e.get("color").size}}static initialize(e,s){var i;It.initialize(e,s),bt._defaultColor||(bt._defaultColor=((i=s.highlightColors)==null?void 0:i.values().next().value)||"#fff066")}static updateDefaultParams(e,s){switch(e){case vt.HIGHLIGHT_DEFAULT_COLOR:bt._defaultColor=s;break;case vt.HIGHLIGHT_THICKNESS:bt._defaultThickness=s;break}}translateInPage(e,s){}get toolbarPosition(){return r(this,hl)}updateParams(e,s){switch(e){case vt.HIGHLIGHT_COLOR:y(this,Vf,mv).call(this,s);break;case vt.HIGHLIGHT_THICKNESS:y(this,zf,bv).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[vt.HIGHLIGHT_DEFAULT_COLOR,bt._defaultColor],[vt.HIGHLIGHT_THICKNESS,bt._defaultThickness]]}get propertiesToUpdate(){return[[vt.HIGHLIGHT_COLOR,this.color||bt._defaultColor],[vt.HIGHLIGHT_THICKNESS,r(this,Ss)||bt._defaultThickness],[vt.HIGHLIGHT_FREE,r(this,_e)]]}addEditToolbar(){return H(this,null,function*(){const e=yield yi(bt.prototype,this,"addEditToolbar").call(this);return e?(this._uiManager.highlightColors&&(m(this,ll,new bu({editor:this})),e.addColorPicker(r(this,ll))),e):null})}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(y(this,ha,ql).call(this))}getBaseTranslation(){return[0,0]}getRect(e,s){return super.getRect(e,s,y(this,ha,ql).call(this))}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),e&&this.div.focus()}remove(){y(this,ed,Bg).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(y(this,Qi,ir).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){var i;let s=!1;this.parent&&!e?y(this,ed,Bg).call(this):e&&(y(this,Qi,ir).call(this,e),s=!this.parent&&((i=this.div)==null?void 0:i.classList.contains("selectedEditor"))),super.setParent(e),this.show(this._isVisible),s&&this.select()}rotate(e){var n,a,o;const{drawLayer:s}=this.parent;let i;r(this,_e)?(e=(e-this.rotation+360)%360,i=y(n=bt,$n,Ua).call(n,r(this,As).box,e)):i=y(a=bt,$n,Ua).call(a,[this.x,this.y,this.width,this.height],e),s.updateProperties(r(this,as),{bbox:i,root:{"data-main-rotation":e}}),s.updateProperties(r(this,Pe),{bbox:y(o=bt,$n,Ua).call(o,r(this,Ye).box,e),root:{"data-main-rotation":e}})}render(){if(this.div)return this.div;const e=super.render();r(this,cl)&&(e.setAttribute("aria-label",r(this,cl)),e.setAttribute("role","mark")),r(this,_e)?e.classList.add("free"):this.div.addEventListener("keydown",y(this,Gf,yv).bind(this),{signal:this._uiManager._signal});const s=m(this,la,document.createElement("div"));e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal",s.style.clipPath=r(this,oa);const[i,n]=this.parentDimensions;return this.setDims(this.width*i,this.height*n),Em(this,r(this,la),["pointerover","pointerleave"]),this.enableEditing(),e}pointerover(){var e;this.isSelected||(e=this.parent)==null||e.drawLayer.updateProperties(r(this,Pe),{rootClass:{hovered:!0}})}pointerleave(){var e;this.isSelected||(e=this.parent)==null||e.drawLayer.updateProperties(r(this,Pe),{rootClass:{hovered:!1}})}_moveCaret(e){switch(this.parent.unselect(this),e){case 0:case 2:y(this,ul,eu).call(this,!0);break;case 1:case 3:y(this,ul,eu).call(this,!1);break}}select(){var e;super.select(),r(this,Pe)&&((e=this.parent)==null||e.drawLayer.updateProperties(r(this,Pe),{rootClass:{hovered:!1,selected:!0}}))}unselect(){var e;super.unselect(),r(this,Pe)&&((e=this.parent)==null||e.drawLayer.updateProperties(r(this,Pe),{rootClass:{selected:!1}}),r(this,_e)||y(this,ul,eu).call(this,!1))}get _mustFixPosition(){return!r(this,_e)}show(e=this._isVisible){super.show(e),this.parent&&(this.parent.drawLayer.updateProperties(r(this,as),{rootClass:{hidden:!e}}),this.parent.drawLayer.updateProperties(r(this,Pe),{rootClass:{hidden:!e}}))}static startHighlighting(e,s,{target:i,x:n,y:a}){const{x:o,y:l,width:h,height:c}=i.getBoundingClientRect(),u=new AbortController,f=e.combinedSignal(u),g=b=>{u.abort(),y(this,Xf,Sv).call(this,e,b)};window.addEventListener("blur",g,{signal:f}),window.addEventListener("pointerup",g,{signal:f}),window.addEventListener("pointerdown",Kt,{capture:!0,passive:!1,signal:f}),window.addEventListener("contextmenu",qs,{signal:f}),i.addEventListener("pointermove",y(this,qf,Av).bind(this,e),{signal:f}),this._freeHighlight=new Fg({x:n,y:a},[o,l,h,c],e.scale,this._defaultThickness/2,s,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static deserialize(e,s,i){return H(this,null,function*(){var v,_,w,A;let n=null;if(e instanceof Zb){const{data:{quadPoints:C,rect:S,rotation:T,id:x,color:k,opacity:I,popupRef:M},parent:{page:{pageNumber:L}}}=e;n=e={annotationType:ot.HIGHLIGHT,color:Array.from(k),opacity:I,quadPoints:C,boxes:null,pageIndex:L-1,rect:S.slice(0),rotation:T,id:x,deleted:!1,popupRef:M}}else if(e instanceof Pm){const{data:{inkLists:C,rect:S,rotation:T,id:x,color:k,borderStyle:{rawWidth:I},popupRef:M},parent:{page:{pageNumber:L}}}=e;n=e={annotationType:ot.HIGHLIGHT,color:Array.from(k),thickness:I,inkLists:C,boxes:null,pageIndex:L-1,rect:S.slice(0),rotation:T,id:x,deleted:!1,popupRef:M}}const{color:a,quadPoints:o,inkLists:l,opacity:h}=e,c=yield yi(bt,this,"deserialize").call(this,e,s,i);c.color=K.makeHexColor(...a),m(c,Zi,h||1),l&&m(c,Ss,e.thickness),c.annotationElementId=e.id||null,c._initialData=n;const[u,f]=c.pageDimensions,[g,b]=c.pageTranslation;if(o){const C=m(c,Ki,[]);for(let S=0;S<o.length;S+=8)C.push({x:(o[S]-g)/u,y:1-(o[S+1]-b)/f,width:(o[S+2]-o[S])/u,height:(o[S+1]-o[S+5])/f});y(v=c,td,Hg).call(v),y(_=c,Qi,ir).call(_),c.rotate(c.rotation)}else if(l){m(c,_e,!0);const C=l[0],S={x:C[0]-g,y:f-(C[1]-b)},T=new Fg(S,[0,0,u,f],1,r(c,Ss)/2,!0,.001);for(let I=0,M=C.length;I<M;I+=2)S.x=C[I]-g,S.y=f-(C[I+1]-b),T.add(S);const{id:x,clipPathId:k}=s.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:c.color,"fill-opacity":c._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:T.toSVGPath()}},!0,!0);y(w=c,dl,tu).call(w,{highlightOutlines:T.getOutlines(),highlightId:x,clipPathId:k}),y(A=c,Qi,ir).call(A),c.rotate(c.parentRotation)}return c})}serialize(e=!1){if(this.isEmpty()||e)return null;if(this.deleted)return this.serializeDeleted();const s=this.getRect(0,0),i=It._colorManager.convert(this.color),n={annotationType:ot.HIGHLIGHT,color:i,opacity:r(this,Zi),thickness:r(this,Ss),quadPoints:y(this,jf,_v).call(this),outlines:y(this,Wf,wv).call(this,s),pageIndex:this.pageIndex,rect:s,rotation:y(this,ha,ql).call(this),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!y(this,Yf,Cv).call(this,n)?null:(n.id=this.annotationElementId,n)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};ol=new WeakMap,Kc=new WeakMap,Ki=new WeakMap,oa=new WeakMap,ll=new WeakMap,Ye=new WeakMap,Zc=new WeakMap,Qc=new WeakMap,la=new WeakMap,As=new WeakMap,as=new WeakMap,_e=new WeakMap,hl=new WeakMap,Zi=new WeakMap,Pe=new WeakMap,cl=new WeakMap,Ss=new WeakMap,Jc=new WeakMap,td=new WeakSet,Hg=function(){const e=new Lg(r(this,Ki),.001);m(this,As,e.getOutlines()),[this.x,this.y,this.width,this.height]=r(this,As).box;const s=new Lg(r(this,Ki),.0025,.001,this._uiManager.direction==="ltr");m(this,Ye,s.getOutlines());const{lastPoint:i}=r(this,Ye);m(this,hl,[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height])},dl=new WeakSet,tu=function({highlightOutlines:e,highlightId:s,clipPathId:i}){var u,f;if(m(this,As,e),m(this,Ye,e.getNewOutline(r(this,Ss)/2+1.5,.0025)),s>=0)m(this,as,s),m(this,oa,i),this.parent.drawLayer.finalizeDraw(s,{bbox:e.box,path:{d:e.toSVGPath()}}),m(this,Pe,this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:r(this,Ye).box,path:{d:r(this,Ye).toSVGPath()}},!0));else if(this.parent){const g=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(r(this,as),{bbox:y(u=bt,$n,Ua).call(u,r(this,As).box,(g-this.rotation+360)%360),path:{d:e.toSVGPath()}}),this.parent.drawLayer.updateProperties(r(this,Pe),{bbox:y(f=bt,$n,Ua).call(f,r(this,Ye).box,g),path:{d:r(this,Ye).toSVGPath()}})}const[a,o,l,h]=e.box;switch(this.rotation){case 0:this.x=a,this.y=o,this.width=l,this.height=h;break;case 90:{const[g,b]=this.parentDimensions;this.x=o,this.y=1-a,this.width=l*b/g,this.height=h*g/b;break}case 180:this.x=1-a,this.y=1-o,this.width=l,this.height=h;break;case 270:{const[g,b]=this.parentDimensions;this.x=1-o,this.y=a,this.width=l*b/g,this.height=h*g/b;break}}const{lastPoint:c}=r(this,Ye);m(this,hl,[(c[0]-a)/l,(c[1]-o)/h])},Vf=new WeakSet,mv=function(e){const s=(a,o)=>{var l,h;this.color=a,m(this,Zi,o),(l=this.parent)==null||l.drawLayer.updateProperties(r(this,as),{root:{fill:a,"fill-opacity":o}}),(h=r(this,ll))==null||h.updateColor(a)},i=this.color,n=r(this,Zi);this.addCommands({cmd:s.bind(this,e,bt._defaultOpacity),undo:s.bind(this,i,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:vt.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(e)},!0)},zf=new WeakSet,bv=function(e){const s=r(this,Ss),i=n=>{m(this,Ss,n),y(this,Uf,vv).call(this,n)};this.addCommands({cmd:i.bind(this,e),undo:i.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:vt.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:e},!0)},Uf=new WeakSet,vv=function(e){if(!r(this,_e))return;y(this,dl,tu).call(this,{highlightOutlines:r(this,As).getNewOutline(e/2)}),this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)},ed=new WeakSet,Bg=function(){r(this,as)===null||!this.parent||(this.parent.drawLayer.remove(r(this,as)),m(this,as,null),this.parent.drawLayer.remove(r(this,Pe)),m(this,Pe,null))},Qi=new WeakSet,ir=function(e=this.parent){r(this,as)===null&&({id:Fe(this,as)._,clipPathId:Fe(this,oa)._}=e.drawLayer.draw({bbox:r(this,As).box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":r(this,Zi)},rootClass:{highlight:!0,free:r(this,_e)},path:{d:r(this,As).toSVGPath()}},!1,!0),m(this,Pe,e.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:r(this,_e)},bbox:r(this,Ye).box,path:{d:r(this,Ye).toSVGPath()}},r(this,_e))),r(this,la)&&(r(this,la).style.clipPath=r(this,oa)))},$n=new WeakSet,Ua=function([e,s,i,n],a){switch(a){case 90:return[1-s-n,e,n,i];case 180:return[1-e-i,1-s-n,i,n];case 270:return[s,1-e-i,n,i]}return[e,s,i,n]},Gf=new WeakSet,yv=function(e){bt._keyboardManager.exec(this,e)},ul=new WeakSet,eu=function(e){if(!r(this,ol))return;const s=window.getSelection();e?s.setPosition(r(this,ol),r(this,Kc)):s.setPosition(r(this,Zc),r(this,Qc))},ha=new WeakSet,ql=function(){return r(this,_e)?this.rotation:0},jf=new WeakSet,_v=function(){if(r(this,_e))return null;const[e,s]=this.pageDimensions,[i,n]=this.pageTranslation,a=r(this,Ki),o=new Float32Array(a.length*8);let l=0;for(const{x:h,y:c,width:u,height:f}of a){const g=h*e+i,b=(1-c)*s+n;o[l]=o[l+4]=g,o[l+1]=o[l+3]=b,o[l+2]=o[l+6]=g+u*e,o[l+5]=o[l+7]=b-f*s,l+=8}return o},Wf=new WeakSet,wv=function(e){return r(this,As).serialize(e,y(this,ha,ql).call(this))},qf=new WeakSet,Av=function(e,s){this._freeHighlight.add(s)&&e.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})},Xf=new WeakSet,Sv=function(e,s){this._freeHighlight.isEmpty()?e.drawLayer.remove(this._freeHighlightId):e.createAndAddNewEditor(s,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},Yf=new WeakSet,Cv=function(e){const{color:s}=this._initialData;return e.color.some((i,n)=>i!==s[n])},p(bt,$n),p(bt,qf),p(bt,Xf),O(bt,"_defaultColor",null),O(bt,"_defaultOpacity",1),O(bt,"_defaultThickness",12),O(bt,"_type","highlight"),O(bt,"_editorType",ot.HIGHLIGHT),O(bt,"_freeHighlightId",-1),O(bt,"_freeHighlight",null),O(bt,"_freeHighlightClipId","");let vu=bt;var ca;class xv{constructor(){p(this,ca,Object.create(null))}updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,s)}updateSVGProperty(t,e){r(this,ca)[t]=e}toSVGProperties(){const t=r(this,ca);return m(this,ca,Object.create(null)),{root:t}}reset(){m(this,ca,Object.create(null))}updateAll(t=this){this.updateProperties(t)}clone(){xt("Not implemented")}}ca=new WeakMap;var os,fl,de,da,ua,Hn,Bn,Vn,fa,sd,Vg,id,zg,nd,Ug,pa,Xl,Kf,Ev,pl,su,ga,Yl,zn,Ga;const X=class X extends It{constructor(e){super(e);p(this,sd);p(this,id);p(this,nd);p(this,pa);p(this,Kf);p(this,pl);p(this,ga);p(this,zn);p(this,os,null);p(this,fl,void 0);O(this,"_drawId",null);m(this,fl,e.mustBeCommitted||!1),this._addOutlines(e)}_addOutlines(e){e.drawOutlines&&(y(this,sd,Vg).call(this,e),y(this,pa,Xl).call(this))}static _mergeSVGProperties(e,s){const i=new Set(Object.keys(e));for(const[n,a]of Object.entries(s))i.has(n)?Object.assign(e[n],a):e[n]=a;return e}static getDefaultDrawingOptions(e){xt("Not implemented")}static get typesMap(){xt("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(e,s){const i=this.typesMap.get(e);i&&this._defaultDrawingOptions.updateProperty(i,s),this._currentParent&&(r(X,de).updateProperty(i,s),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(e,s){const i=this.constructor.typesMap.get(e);i&&this._updateProperty(e,i,s)}static get defaultPropertiesToUpdate(){const e=[],s=this._defaultDrawingOptions;for(const[i,n]of this.typesMap)e.push([i,s[n]]);return e}get propertiesToUpdate(){const e=[],{_drawingOptions:s}=this;for(const[i,n]of this.constructor.typesMap)e.push([i,s[n]]);return e}_updateProperty(e,s,i){const n=this._drawingOptions,a=n[s],o=l=>{var c;n.updateProperty(s,l);const h=r(this,os).updateProperty(s,l);h&&y(this,ga,Yl).call(this,h),(c=this.parent)==null||c.drawLayer.updateProperties(this._drawId,n.toSVGProperties())};this.addCommands({cmd:o.bind(this,i),undo:o.bind(this,a),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:e,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,X._mergeSVGProperties(r(this,os).getPathResizingSVGProperties(y(this,pl,su).call(this)),{bbox:y(this,zn,Ga).call(this)}))}_onResized(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,X._mergeSVGProperties(r(this,os).getPathResizedSVGProperties(y(this,pl,su).call(this)),{bbox:y(this,zn,Ga).call(this)}))}_onTranslating(e,s){var i;(i=this.parent)==null||i.drawLayer.updateProperties(this._drawId,{bbox:y(this,zn,Ga).call(this)})}_onTranslated(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,X._mergeSVGProperties(r(this,os).getPathTranslatedSVGProperties(y(this,pl,su).call(this),this.parentDimensions),{bbox:y(this,zn,Ga).call(this)}))}_onStartDragging(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){var e;(e=this.parent)==null||e.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,r(this,fl)&&(m(this,fl,!1),this.commit(),this.parent.setSelected(this),e&&this.isOnScreen&&this.div.focus())}remove(){y(this,nd,Ug).call(this),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(y(this,pa,Xl).call(this),y(this,ga,Yl).call(this,r(this,os).box),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){var i;let s=!1;this.parent&&!e?(this._uiManager.removeShouldRescale(this),y(this,nd,Ug).call(this)):e&&(this._uiManager.addShouldRescale(this),y(this,pa,Xl).call(this,e),s=!this.parent&&((i=this.div)==null?void 0:i.classList.contains("selectedEditor"))),super.setParent(e),s&&this.select()}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,X._mergeSVGProperties({bbox:y(this,zn,Ga).call(this)},r(this,os).updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&y(this,ga,Yl).call(this,r(this,os).updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let e,s;this._isCopy&&(e=this.x,s=this.y);const i=super.render();i.classList.add("draw");const n=document.createElement("div");i.append(n),n.setAttribute("aria-hidden","true"),n.className="internal";const[a,o]=this.parentDimensions;return this.setDims(this.width*a,this.height*o),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(e,s),i}static createDrawerInstance(e,s,i,n,a){xt("Not implemented")}static startDrawing(e,s,i,n){var _,w;const{target:a,offsetX:o,offsetY:l,pointerId:h,pointerType:c}=n;if(r(X,Bn)&&r(X,Bn)!==c)return;const{viewport:{rotation:u}}=e,{width:f,height:g}=a.getBoundingClientRect(),b=m(X,da,new AbortController),v=e.combinedSignal(b);if(r(X,Hn)||m(X,Hn,h),(_=r(X,Bn))!=null||m(X,Bn,c),window.addEventListener("pointerup",A=>{var C;r(X,Hn)===A.pointerId?this._endDraw(A):(C=r(X,Vn))==null||C.delete(A.pointerId)},{signal:v}),window.addEventListener("pointercancel",A=>{var C;r(X,Hn)===A.pointerId?this._currentParent.endDrawingSession():(C=r(X,Vn))==null||C.delete(A.pointerId)},{signal:v}),window.addEventListener("pointerdown",A=>{r(X,Bn)===A.pointerType&&((r(X,Vn)||m(X,Vn,new Set)).add(A.pointerId),r(X,de).isCancellable()&&(r(X,de).removeLastElement(),r(X,de).isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:v}),window.addEventListener("contextmenu",qs,{signal:v}),a.addEventListener("pointermove",this._drawMove.bind(this),{signal:v}),a.addEventListener("touchmove",A=>{A.timeStamp===r(X,fa)&&Kt(A)},{signal:v}),e.toggleDrawing(),(w=s._editorUndoBar)==null||w.hide(),r(X,de)){e.drawLayer.updateProperties(this._currentDrawId,r(X,de).startNew(o,l,f,g,u));return}s.updateUIForDefaultProperties(this),m(X,de,this.createDrawerInstance(o,l,f,g,u)),m(X,ua,this.getDefaultDrawingOptions()),this._currentParent=e,{id:this._currentDrawId}=e.drawLayer.draw(this._mergeSVGProperties(r(X,ua).toSVGProperties(),r(X,de).defaultSVGProperties),!0,!1)}static _drawMove(e){var a;if(m(X,fa,-1),!r(X,de))return;const{offsetX:s,offsetY:i,pointerId:n}=e;if(r(X,Hn)===n){if(((a=r(X,Vn))==null?void 0:a.size)>=1){this._endDraw(e);return}this._currentParent.drawLayer.updateProperties(this._currentDrawId,r(X,de).add(s,i)),m(X,fa,e.timeStamp),Kt(e)}}static _cleanup(e){e&&(this._currentDrawId=-1,this._currentParent=null,m(X,de,null),m(X,ua,null),m(X,Bn,null),m(X,fa,NaN)),r(X,da)&&(r(X,da).abort(),m(X,da,null),m(X,Hn,NaN),m(X,Vn,null))}static _endDraw(e){const s=this._currentParent;if(s){if(s.toggleDrawing(!0),this._cleanup(!1),(e==null?void 0:e.target)===s.div&&s.drawLayer.updateProperties(this._currentDrawId,r(X,de).end(e.offsetX,e.offsetY)),this.supportMultipleDrawings){const i=r(X,de),n=this._currentDrawId,a=i.getLastElement();s.addCommands({cmd:()=>{s.drawLayer.updateProperties(n,i.setLastElement(a))},undo:()=>{s.drawLayer.updateProperties(n,i.removeLastElement())},mustExec:!1,type:vt.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(e){const s=this._currentParent;if(!s)return null;if(s.toggleDrawing(!0),s.cleanUndoStack(vt.DRAW_STEP),!r(X,de).isEmpty()){const{pageDimensions:[i,n],scale:a}=s,o=s.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:r(X,de).getOutlines(i*a,n*a,a,this._INNER_MARGIN),drawingOptions:r(X,ua),mustBeCommitted:!e});return this._cleanup(!0),o}return s.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(e){}static deserializeDraw(e,s,i,n,a,o){xt("Not implemented")}static deserialize(e,s,i){return H(this,null,function*(){var u,f;const{rawDims:{pageWidth:n,pageHeight:a,pageX:o,pageY:l}}=s.viewport,h=this.deserializeDraw(o,l,n,a,this._INNER_MARGIN,e),c=yield yi(X,this,"deserialize").call(this,e,s,i);return c.createDrawingOptions(e),y(u=c,sd,Vg).call(u,{drawOutlines:h}),y(f=c,pa,Xl).call(f),c.onScaleChanging(),c.rotate(),c})}serializeDraw(e){const[s,i]=this.pageTranslation,[n,a]=this.pageDimensions;return r(this,os).serialize([s,i,n,a],e)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};os=new WeakMap,fl=new WeakMap,de=new WeakMap,da=new WeakMap,ua=new WeakMap,Hn=new WeakMap,Bn=new WeakMap,Vn=new WeakMap,fa=new WeakMap,sd=new WeakSet,Vg=function({drawOutlines:e,drawId:s,drawingOptions:i}){m(this,os,e),this._drawingOptions||(this._drawingOptions=i),s>=0?(this._drawId=s,this.parent.drawLayer.finalizeDraw(s,e.defaultProperties)):this._drawId=y(this,id,zg).call(this,e,this.parent),y(this,ga,Yl).call(this,e.box)},id=new WeakSet,zg=function(e,s){const{id:i}=s.drawLayer.draw(X._mergeSVGProperties(this._drawingOptions.toSVGProperties(),e.defaultSVGProperties),!1,!1);return i},nd=new WeakSet,Ug=function(){this._drawId===null||!this.parent||(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())},pa=new WeakSet,Xl=function(e=this.parent){if(!(this._drawId!==null&&this.parent===e)){if(this._drawId!==null){this.parent.drawLayer.updateParent(this._drawId,e.drawLayer);return}this._drawingOptions.updateAll(),this._drawId=y(this,id,zg).call(this,r(this,os),e)}},Kf=new WeakSet,Ev=function([e,s,i,n]){const{parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[s,1-e,i*(o/a),n*(a/o)];case 180:return[1-e,1-s,i,n];case 270:return[1-s,e,i*(o/a),n*(a/o)];default:return[e,s,i,n]}},pl=new WeakSet,su=function(){const{x:e,y:s,width:i,height:n,parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[1-s,e,i*(a/o),n*(o/a)];case 180:return[1-e,1-s,i,n];case 270:return[s,1-e,i*(a/o),n*(o/a)];default:return[e,s,i,n]}},ga=new WeakSet,Yl=function(e){if([this.x,this.y,this.width,this.height]=y(this,Kf,Ev).call(this,e),this.div){this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)}this._onResized()},zn=new WeakSet,Ga=function(){const{x:e,y:s,width:i,height:n,rotation:a,parentRotation:o,parentDimensions:[l,h]}=this;switch((a*4+o)/90){case 1:return[1-s-n,e,n,i];case 2:return[1-e-i,1-s-n,i,n];case 3:return[s,1-e-i,n,i];case 4:return[e,s-i*(l/h),n*(h/l),i*(l/h)];case 5:return[1-s,e,i*(l/h),n*(h/l)];case 6:return[1-e-n*(h/l),1-s,n*(h/l),i*(l/h)];case 7:return[s-i*(l/h),1-e-n*(h/l),i*(l/h),n*(h/l)];case 8:return[e-i,s-n,i,n];case 9:return[1-s,e-i,n,i];case 10:return[1-e,1-s,i,n];case 11:return[s-n,1-e,n,i];case 12:return[e-n*(h/l),s,n*(h/l),i*(l/h)];case 13:return[1-s-i*(l/h),e-n*(h/l),i*(l/h),n*(h/l)];case 14:return[1-e,1-s-i*(l/h),n*(h/l),i*(l/h)];case 15:return[s,1-e,i*(l/h),n*(h/l)];default:return[e,s,i,n]}},O(X,"_currentDrawId",-1),O(X,"_currentParent",null),p(X,de,null),p(X,da,null),p(X,ua,null),p(X,Hn,NaN),p(X,Bn,null),p(X,Vn,null),p(X,fa,NaN),O(X,"_INNER_MARGIN",3);let yu=X;var di,ue,fe,ma,gl,He,we,Cs,ba,va,ya,ml,iu;class Fw{constructor(t,e,s,i,n,a){p(this,ml);p(this,di,new Float64Array(6));p(this,ue,void 0);p(this,fe,void 0);p(this,ma,void 0);p(this,gl,void 0);p(this,He,void 0);p(this,we,"");p(this,Cs,0);p(this,ba,new Cd);p(this,va,void 0);p(this,ya,void 0);m(this,va,s),m(this,ya,i),m(this,ma,n),m(this,gl,a),[t,e]=y(this,ml,iu).call(this,t,e);const o=m(this,ue,[NaN,NaN,NaN,NaN,t,e]);m(this,He,[t,e]),m(this,fe,[{line:o,points:r(this,He)}]),r(this,di).set(o,0)}updateProperty(t,e){t==="stroke-width"&&m(this,gl,e)}isEmpty(){return!r(this,fe)||r(this,fe).length===0}isCancellable(){return r(this,He).length<=10}add(t,e){[t,e]=y(this,ml,iu).call(this,t,e);const[s,i,n,a]=r(this,di).subarray(2,6),o=t-n,l=e-a;return Math.hypot(r(this,va)*o,r(this,ya)*l)<=2?null:(r(this,He).push(t,e),isNaN(s)?(r(this,di).set([n,a,t,e],2),r(this,ue).push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(r(this,di)[0])&&r(this,ue).splice(6,6),r(this,di).set([s,i,n,a,t,e],0),r(this,ue).push(...W.createBezierPoints(s,i,n,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(r(this,He).length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,n){m(this,va,s),m(this,ya,i),m(this,ma,n),[t,e]=y(this,ml,iu).call(this,t,e);const a=m(this,ue,[NaN,NaN,NaN,NaN,t,e]);m(this,He,[t,e]);const o=r(this,fe).at(-1);return o&&(o.line=new Float32Array(o.line),o.points=new Float32Array(o.points)),r(this,fe).push({line:a,points:r(this,He)}),r(this,di).set(a,0),m(this,Cs,0),this.toSVGPath(),null}getLastElement(){return r(this,fe).at(-1)}setLastElement(t){return r(this,fe)?(r(this,fe).push(t),m(this,ue,t.line),m(this,He,t.points),m(this,Cs,0),{path:{d:this.toSVGPath()}}):r(this,ba).setLastElement(t)}removeLastElement(){if(!r(this,fe))return r(this,ba).removeLastElement();r(this,fe).pop(),m(this,we,"");for(let t=0,e=r(this,fe).length;t<e;t++){const{line:s,points:i}=r(this,fe)[t];m(this,ue,s),m(this,He,i),m(this,Cs,0),this.toSVGPath()}return{path:{d:r(this,we)}}}toSVGPath(){const t=W.svgRound(r(this,ue)[4]),e=W.svgRound(r(this,ue)[5]);if(r(this,He).length===2)return m(this,we,`${r(this,we)} M ${t} ${e} Z`),r(this,we);if(r(this,He).length<=6){const i=r(this,we).lastIndexOf("M");m(this,we,`${r(this,we).slice(0,i)} M ${t} ${e}`),m(this,Cs,6)}if(r(this,He).length===4){const i=W.svgRound(r(this,ue)[10]),n=W.svgRound(r(this,ue)[11]);return m(this,we,`${r(this,we)} L ${i} ${n}`),m(this,Cs,12),r(this,we)}const s=[];r(this,Cs)===0&&(s.push(`M ${t} ${e}`),m(this,Cs,6));for(let i=r(this,Cs),n=r(this,ue).length;i<n;i+=6){const[a,o,l,h,c,u]=r(this,ue).slice(i,i+6).map(W.svgRound);s.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}return m(this,we,r(this,we)+s.join(" ")),m(this,Cs,r(this,ue).length),r(this,we)}getOutlines(t,e,s,i){const n=r(this,fe).at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),r(this,ba).build(r(this,fe),t,e,s,r(this,ma),r(this,gl),i),m(this,di,null),m(this,ue,null),m(this,fe,null),m(this,we,null),r(this,ba)}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}di=new WeakMap,ue=new WeakMap,fe=new WeakMap,ma=new WeakMap,gl=new WeakMap,He=new WeakMap,we=new WeakMap,Cs=new WeakMap,ba=new WeakMap,va=new WeakMap,ya=new WeakMap,ml=new WeakSet,iu=function(t,e){return W._normalizePoint(t,e,r(this,va),r(this,ya),r(this,ma))};var Be,rd,ad,ls,ui,fi,bl,vl,_a,zs,Si,Zf,Tv,Qf,kv,Jf,Iv;class Cd extends W{constructor(){super(...arguments);p(this,zs);p(this,Zf);p(this,Qf);p(this,Jf);p(this,Be,void 0);p(this,rd,0);p(this,ad,void 0);p(this,ls,void 0);p(this,ui,void 0);p(this,fi,void 0);p(this,bl,void 0);p(this,vl,void 0);p(this,_a,void 0)}build(e,s,i,n,a,o,l){m(this,ui,s),m(this,fi,i),m(this,bl,n),m(this,vl,a),m(this,_a,o),m(this,ad,l!=null?l:0),m(this,ls,e),y(this,Qf,kv).call(this)}get thickness(){return r(this,_a)}setLastElement(e){return r(this,ls).push(e),{path:{d:this.toSVGPath()}}}removeLastElement(){return r(this,ls).pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const e=[];for(const{line:s}of r(this,ls)){if(e.push(`M${W.svgRound(s[4])} ${W.svgRound(s[5])}`),s.length===6){e.push("Z");continue}if(s.length===12&&isNaN(s[6])){e.push(`L${W.svgRound(s[10])} ${W.svgRound(s[11])}`);continue}for(let i=6,n=s.length;i<n;i+=6){const[a,o,l,h,c,u]=s.subarray(i,i+6).map(W.svgRound);e.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}}return e.join("")}serialize([e,s,i,n],a){const o=[],l=[],[h,c,u,f]=y(this,Zf,Tv).call(this);let g,b,v,_,w,A,C,S,T;switch(r(this,vl)){case 0:T=W._rescale,g=e,b=s+n,v=i,_=-n,w=e+h*i,A=s+(1-c-f)*n,C=e+(h+u)*i,S=s+(1-c)*n;break;case 90:T=W._rescaleAndSwap,g=e,b=s,v=i,_=n,w=e+c*i,A=s+h*n,C=e+(c+f)*i,S=s+(h+u)*n;break;case 180:T=W._rescale,g=e+i,b=s,v=-i,_=n,w=e+(1-h-u)*i,A=s+c*n,C=e+(1-h)*i,S=s+(c+f)*n;break;case 270:T=W._rescaleAndSwap,g=e+i,b=s+n,v=-i,_=-n,w=e+(1-c-f)*i,A=s+(1-h-u)*n,C=e+(1-c)*i,S=s+(1-h)*n;break}for(const{line:x,points:k}of r(this,ls))o.push(T(x,g,b,v,_,a?new Array(x.length):null)),l.push(T(k,g,b,v,_,a?new Array(k.length):null));return{lines:o,points:l,rect:[w,A,C,S]}}static deserialize(e,s,i,n,a,{paths:{lines:o,points:l},rotation:h,thickness:c}){const u=[];let f,g,b,v,_;switch(h){case 0:_=W._rescale,f=-e/i,g=s/n+1,b=1/i,v=-1/n;break;case 90:_=W._rescaleAndSwap,f=-s/n,g=-e/i,b=1/n,v=1/i;break;case 180:_=W._rescale,f=e/i+1,g=-s/n,b=-1/i,v=1/n;break;case 270:_=W._rescaleAndSwap,f=s/n+1,g=e/i+1,b=-1/n,v=-1/i;break}if(!o){o=[];for(const A of l){const C=A.length;if(C===2){o.push(new Float32Array([NaN,NaN,NaN,NaN,A[0],A[1]]));continue}if(C===4){o.push(new Float32Array([NaN,NaN,NaN,NaN,A[0],A[1],NaN,NaN,NaN,NaN,A[2],A[3]]));continue}const S=new Float32Array(3*(C-2));o.push(S);let[T,x,k,I]=A.subarray(0,4);S.set([NaN,NaN,NaN,NaN,T,x],0);for(let M=4;M<C;M+=2){const L=A[M],F=A[M+1];S.set(W.createBezierPoints(T,x,k,I,L,F),(M-2)*3),[T,x,k,I]=[k,I,L,F]}}}for(let A=0,C=o.length;A<C;A++)u.push({line:_(o[A].map(S=>S!=null?S:NaN),f,g,b,v),points:_(l[A].map(S=>S!=null?S:NaN),f,g,b,v)});const w=new this.prototype.constructor;return w.build(u,i,n,1,h,c,a),w}get box(){return r(this,Be)}updateProperty(e,s){return e==="stroke-width"?y(this,Jf,Iv).call(this,s):null}updateParentDimensions([e,s],i){const[n,a]=y(this,zs,Si).call(this);m(this,ui,e),m(this,fi,s),m(this,bl,i);const[o,l]=y(this,zs,Si).call(this),h=o-n,c=l-a,u=r(this,Be);return u[0]-=h,u[1]-=c,u[2]+=2*h,u[3]+=2*c,u}updateRotation(e){return m(this,rd,e),{path:{transform:this.rotationTransform}}}get viewBox(){return r(this,Be).map(W.svgRound).join(" ")}get defaultProperties(){const[e,s]=r(this,Be);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${W.svgRound(e)} ${W.svgRound(s)}`}}}get rotationTransform(){const[,,e,s]=r(this,Be);let i=0,n=0,a=0,o=0,l=0,h=0;switch(r(this,rd)){case 90:n=s/e,a=-e/s,l=e;break;case 180:i=-1,o=-1,l=e,h=s;break;case 270:n=-s/e,a=e/s,h=s;break;default:return""}return`matrix(${i} ${n} ${a} ${o} ${W.svgRound(l)} ${W.svgRound(h)})`}getPathResizingSVGProperties([e,s,i,n]){const[a,o]=y(this,zs,Si).call(this),[l,h,c,u]=r(this,Be);if(Math.abs(c-a)<=W.PRECISION||Math.abs(u-o)<=W.PRECISION){const _=e+i/2-(l+c/2),w=s+n/2-(h+u/2);return{path:{"transform-origin":`${W.svgRound(e)} ${W.svgRound(s)}`,transform:`${this.rotationTransform} translate(${_} ${w})`}}}const f=(i-2*a)/(c-2*a),g=(n-2*o)/(u-2*o),b=c/i,v=u/n;return{path:{"transform-origin":`${W.svgRound(l)} ${W.svgRound(h)}`,transform:`${this.rotationTransform} scale(${b} ${v}) translate(${W.svgRound(a)} ${W.svgRound(o)}) scale(${f} ${g}) translate(${W.svgRound(-a)} ${W.svgRound(-o)})`}}}getPathResizedSVGProperties([e,s,i,n]){const[a,o]=y(this,zs,Si).call(this),l=r(this,Be),[h,c,u,f]=l;if(l[0]=e,l[1]=s,l[2]=i,l[3]=n,Math.abs(u-a)<=W.PRECISION||Math.abs(f-o)<=W.PRECISION){const w=e+i/2-(h+u/2),A=s+n/2-(c+f/2);for(const{line:C,points:S}of r(this,ls))W._translate(C,w,A,C),W._translate(S,w,A,S);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${W.svgRound(e)} ${W.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const g=(i-2*a)/(u-2*a),b=(n-2*o)/(f-2*o),v=-g*(h+a)+e+a,_=-b*(c+o)+s+o;if(g!==1||b!==1||v!==0||_!==0)for(const{line:w,points:A}of r(this,ls))W._rescale(w,v,_,g,b,w),W._rescale(A,v,_,g,b,A);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${W.svgRound(e)} ${W.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([e,s],i){const[n,a]=i,o=r(this,Be),l=e-o[0],h=s-o[1];if(r(this,ui)===n&&r(this,fi)===a)for(const{line:c,points:u}of r(this,ls))W._translate(c,l,h,c),W._translate(u,l,h,u);else{const c=r(this,ui)/n,u=r(this,fi)/a;m(this,ui,n),m(this,fi,a);for(const{line:f,points:g}of r(this,ls))W._rescale(f,l,h,c,u,f),W._rescale(g,l,h,c,u,g);o[2]*=c,o[3]*=u}return o[0]=e,o[1]=s,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${W.svgRound(e)} ${W.svgRound(s)}`}}}get defaultSVGProperties(){const e=r(this,Be);return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${W.svgRound(e[0])} ${W.svgRound(e[1])}`,transform:this.rotationTransform||null},bbox:e}}}Be=new WeakMap,rd=new WeakMap,ad=new WeakMap,ls=new WeakMap,ui=new WeakMap,fi=new WeakMap,bl=new WeakMap,vl=new WeakMap,_a=new WeakMap,zs=new WeakSet,Si=function(e=r(this,_a)){const s=r(this,ad)+e/2*r(this,bl);return r(this,vl)%180===0?[s/r(this,ui),s/r(this,fi)]:[s/r(this,fi),s/r(this,ui)]},Zf=new WeakSet,Tv=function(){const[e,s,i,n]=r(this,Be),[a,o]=y(this,zs,Si).call(this,0);return[e+a,s+o,i-2*a,n-2*o]},Qf=new WeakSet,kv=function(){const e=m(this,Be,new Float32Array([1/0,1/0,-1/0,-1/0]));for(const{line:n}of r(this,ls)){if(n.length<=12){for(let l=4,h=n.length;l<h;l+=6)K.pointBoundingBox(n[l],n[l+1],e);continue}let a=n[4],o=n[5];for(let l=6,h=n.length;l<h;l+=6){const[c,u,f,g,b,v]=n.subarray(l,l+6);K.bezierBoundingBox(a,o,c,u,f,g,b,v,e),a=b,o=v}}const[s,i]=y(this,zs,Si).call(this);e[0]=Ue(e[0]-s,0,1),e[1]=Ue(e[1]-i,0,1),e[2]=Ue(e[2]+s,0,1),e[3]=Ue(e[3]+i,0,1),e[2]-=e[0],e[3]-=e[1]},Jf=new WeakSet,Iv=function(e){const[s,i]=y(this,zs,Si).call(this);m(this,_a,e);const[n,a]=y(this,zs,Si).call(this),[o,l]=[n-s,a-i],h=r(this,Be);return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h};class vp extends xv{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:It._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){t==="stroke-width"&&(e!=null||(e=this["stroke-width"]),e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new vp(this._viewParameters);return t.updateAll(this),t}}var tp,Pv;const lr=class lr extends yu{constructor(e){super(vi(Ge({},e),{name:"inkEditor"}));p(this,tp);this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(e,s){It.initialize(e,s),this._defaultDrawingOptions=new vp(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!0}static get typesMap(){return gt(this,"typesMap",new Map([[vt.INK_THICKNESS,"stroke-width"],[vt.INK_COLOR,"stroke"],[vt.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(e,s,i,n,a){return new Fw(e,s,i,n,a,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(e,s,i,n,a,o){return Cd.deserialize(e,s,i,n,a,o)}static deserialize(e,s,i){return H(this,null,function*(){let n=null;if(e instanceof Pm){const{data:{inkLists:o,rect:l,rotation:h,id:c,color:u,opacity:f,borderStyle:{rawWidth:g},popupRef:b},parent:{page:{pageNumber:v}}}=e;n=e={annotationType:ot.INK,color:Array.from(u),thickness:g,opacity:f,paths:{points:o},boxes:null,pageIndex:v-1,rect:l.slice(0),rotation:h,id:c,deleted:!1,popupRef:b}}const a=yield yi(lr,this,"deserialize").call(this,e,s,i);return a.annotationElementId=e.id||null,a._initialData=n,a})}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:e,_drawingOptions:s,parent:i}=this;s.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(e,s.toSVGProperties())}static onScaleChangingWhenDrawing(){const e=this._currentParent;e&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),e.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:e,thickness:s,opacity:i}){this._drawingOptions=lr.getDefaultDrawingOptions({stroke:K.makeHexColor(...e),"stroke-width":s,"stroke-opacity":i})}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:s,points:i,rect:n}=this.serializeDraw(e),{_drawingOptions:{stroke:a,"stroke-opacity":o,"stroke-width":l}}=this,h={annotationType:ot.INK,color:It._colorManager.convert(a),opacity:o,thickness:l,paths:{lines:s,points:i},pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(h.isCopy=!0,h):this.annotationElementId&&!y(this,tp,Pv).call(this,h)?null:(h.id=this.annotationElementId,h)}renderAnnotationElement(e){const{points:s,rect:i}=this.serializeDraw(!1);return e.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:s}),null}};tp=new WeakSet,Pv=function(e){const{color:s,thickness:i,opacity:n,pageIndex:a}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||e.color.some((o,l)=>o!==s[l])||e.thickness!==i||e.opacity!==n||e.pageIndex!==a},O(lr,"_type","ink"),O(lr,"_editorType",ot.INK),O(lr,"_defaultDrawingOptions",null);let Gg=lr;class jg extends Cd{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}const Md=8,Ll=3;var wa,od,Wg,Us,ep,Rv,sp,Mv,ld,qg,yl,nu,ip,Lv,np,Dv,rp,Fv,hd,Xg,cd,Yg,ap,Nv;class re{static extractContoursFromText(t,{fontFamily:e,fontStyle:s,fontWeight:i},n,a,o,l){let h=new OffscreenCanvas(1,1),c=h.getContext("2d",{alpha:!1});const u=200,f=c.font=`${s} ${i} ${u}px ${e}`,{actualBoundingBoxLeft:g,actualBoundingBoxRight:b,actualBoundingBoxAscent:v,actualBoundingBoxDescent:_,fontBoundingBoxAscent:w,fontBoundingBoxDescent:A,width:C}=c.measureText(t),S=1.5,T=Math.ceil(Math.max(Math.abs(g)+Math.abs(b)||0,C)*S),x=Math.ceil(Math.max(Math.abs(v)+Math.abs(_)||u,Math.abs(w)+Math.abs(A)||u)*S);h=new OffscreenCanvas(T,x),c=h.getContext("2d",{alpha:!0,willReadFrequently:!0}),c.font=f,c.filter="grayscale(1)",c.fillStyle="white",c.fillRect(0,0,T,x),c.fillStyle="black",c.fillText(t,T*(S-1)/2,x*(3-S)/2);const k=y(this,hd,Xg).call(this,c.getImageData(0,0,T,x).data),I=y(this,rp,Fv).call(this,k),M=y(this,cd,Yg).call(this,I),L=y(this,ld,qg).call(this,k,T,x,M);return this.processDrawnLines({lines:{curves:L,width:T,height:x},pageWidth:n,pageHeight:a,rotation:o,innerMargin:l,mustSmooth:!0,areContours:!0})}static process(t,e,s,i,n){const[a,o,l]=y(this,ap,Nv).call(this,t),[h,c]=y(this,np,Dv).call(this,a,o,l,Math.hypot(o,l)*r(this,wa).sigmaSFactor,r(this,wa).sigmaR,r(this,wa).kernelSize),u=y(this,cd,Yg).call(this,c),f=y(this,ld,qg).call(this,h,o,l,u);return this.processDrawnLines({lines:{curves:f,width:o,height:l},pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:a,areContours:o}){var A;i%180!==0&&([e,s]=[s,e]);const{curves:l,width:h,height:c}=t,u=(A=t.thickness)!=null?A:0,f=[],g=Math.min(e/h,s/c),b=g/e,v=g/s,_=[];for(const{points:C}of l){const S=a?y(this,ip,Lv).call(this,C):C;if(!S)continue;_.push(S);const T=S.length,x=new Float32Array(T),k=new Float32Array(3*(T===2?2:T-2));if(f.push({line:k,points:x}),T===2){x[0]=S[0]*b,x[1]=S[1]*v,k.set([NaN,NaN,NaN,NaN,x[0],x[1]],0);continue}let[I,M,L,F]=S;I*=b,M*=v,L*=b,F*=v,x.set([I,M,L,F],0),k.set([NaN,NaN,NaN,NaN,I,M],0);for(let it=4;it<T;it+=2){const at=x[it]=S[it]*b,J=x[it+1]=S[it+1]*v;k.set(W.createBezierPoints(I,M,L,F,at,J),(it-2)*3),[I,M,L,F]=[L,F,at,J]}}if(f.length===0)return null;const w=o?new jg:new Cd;return w.build(f,e,s,1,i,o?0:u,n),{outline:w,newCurves:_,areContours:o,thickness:u,width:h,height:c}}static compressSignature(a){return H(this,arguments,function*({outlines:t,areContours:e,thickness:s,width:i,height:n}){let o=1/0,l=-1/0,h=0;for(const S of t){h+=S.length;for(let T=2,x=S.length;T<x;T++){const k=S[T]-S[T-2];o=Math.min(o,k),l=Math.max(l,k)}}let c;o>=-128&&l<=127?c=Int8Array:o>=-32768&&l<=32767?c=Int16Array:c=Int32Array;const u=t.length,f=Md+Ll*u,g=new Uint32Array(f);let b=0;g[b++]=f*Uint32Array.BYTES_PER_ELEMENT+(h-2*u)*c.BYTES_PER_ELEMENT,g[b++]=0,g[b++]=i,g[b++]=n,g[b++]=e?0:1,g[b++]=Math.max(0,Math.floor(s!=null?s:0)),g[b++]=u,g[b++]=c.BYTES_PER_ELEMENT;for(const S of t)g[b++]=S.length-2,g[b++]=S[0],g[b++]=S[1];const v=new CompressionStream("deflate-raw"),_=v.writable.getWriter();yield _.ready,_.write(g);const w=c.prototype.constructor;for(const S of t){const T=new w(S.length-2);for(let x=2,k=S.length;x<k;x++)T[x-2]=S[x]-S[x-2];_.write(T)}_.close();const A=yield new Response(v.readable).arrayBuffer(),C=new Uint8Array(A);return $0(C)})}static decompressSignature(t){return H(this,null,function*(){try{const a=a_(t),{readable:o,writable:l}=new DecompressionStream("deflate-raw"),h=l.getWriter();yield h.ready,h.write(a).then(()=>H(this,null,function*(){yield h.ready,yield h.close()})).catch(()=>{});let c=null,u=0;try{for(var e=zm(o),s,i,n;s=!(i=yield e.next()).done;s=!1){const k=i.value;c||(c=new Uint8Array(new Uint32Array(k.buffer,0,4)[0])),c.set(k,u),u+=k.length}}catch(i){n=[i]}finally{try{s&&(i=e.return)&&(yield i.call(e))}finally{if(n)throw n[0]}}const f=new Uint32Array(c.buffer,0,c.length>>2),g=f[1];if(g!==0)throw new Error(`Invalid version: ${g}`);const b=f[2],v=f[3],_=f[4]===0,w=f[5],A=f[6],C=f[7],S=[],T=(Md+Ll*A)*Uint32Array.BYTES_PER_ELEMENT;let x;switch(C){case Int8Array.BYTES_PER_ELEMENT:x=new Int8Array(c.buffer,T);break;case Int16Array.BYTES_PER_ELEMENT:x=new Int16Array(c.buffer,T);break;case Int32Array.BYTES_PER_ELEMENT:x=new Int32Array(c.buffer,T);break}u=0;for(let k=0;k<A;k++){const I=f[Ll*k+Md],M=new Float32Array(I+2);S.push(M);for(let L=0;L<Ll-1;L++)M[L]=f[Ll*k+Md+L+1];for(let L=0;L<I;L++)M[L+2]=M[L]+x[u++]}return{areContours:_,thickness:w,outlines:S,width:b,height:v}}catch(a){return ht(`decompressSignature: ${a}`),null}})}}wa=new WeakMap,od=new WeakSet,Wg=function(t,e,s,i){return s-=t,i-=e,s===0?i>0?0:4:s===1?i+6:2-i},Us=new WeakMap,ep=new WeakSet,Rv=function(t,e,s,i,n,a,o){const l=y(this,od,Wg).call(this,s,i,n,a);for(let h=0;h<8;h++){const c=(-h+l-o+16)%8,u=r(this,Us)[2*c],f=r(this,Us)[2*c+1];if(t[(s+u)*e+(i+f)]!==0)return c}return-1},sp=new WeakSet,Mv=function(t,e,s,i,n,a,o){const l=y(this,od,Wg).call(this,s,i,n,a);for(let h=0;h<8;h++){const c=(h+l+o+16)%8,u=r(this,Us)[2*c],f=r(this,Us)[2*c+1];if(t[(s+u)*e+(i+f)]!==0)return c}return-1},ld=new WeakSet,qg=function(t,e,s,i){const n=t.length,a=new Int32Array(n);for(let c=0;c<n;c++)a[c]=t[c]<=i?1:0;for(let c=1;c<s-1;c++)a[c*e]=a[c*e+e-1]=0;for(let c=0;c<e;c++)a[c]=a[e*s-1-c]=0;let o=1,l;const h=[];for(let c=1;c<s-1;c++){l=1;for(let u=1;u<e-1;u++){const f=c*e+u,g=a[f];if(g===0)continue;let b=c,v=u;if(g===1&&a[f-1]===0)o+=1,v-=1;else if(g>=1&&a[f+1]===0)o+=1,v+=1,g>1&&(l=g);else{g!==1&&(l=Math.abs(g));continue}const _=[u,c],w=v===u+1,A={isHole:w,points:_,id:o,parent:0};h.push(A);let C;for(const F of h)if(F.id===l){C=F;break}C?C.isHole?A.parent=w?C.parent:l:A.parent=w?l:C.parent:A.parent=w?l:0;const S=y(this,ep,Rv).call(this,a,e,c,u,b,v,0);if(S===-1){a[f]=-o,a[f]!==1&&(l=Math.abs(a[f]));continue}let T=r(this,Us)[2*S],x=r(this,Us)[2*S+1];const k=c+T,I=u+x;b=k,v=I;let M=c,L=u;for(;;){const F=y(this,sp,Mv).call(this,a,e,M,L,b,v,1);T=r(this,Us)[2*F],x=r(this,Us)[2*F+1];const it=M+T,at=L+x;_.push(at,it);const J=M*e+L;if(a[J+1]===0?a[J]=-o:a[J]===1&&(a[J]=o),it===c&&at===u&&M===k&&L===I){a[f]!==1&&(l=Math.abs(a[f]));break}else b=M,v=L,M=it,L=at}}}return h},yl=new WeakSet,nu=function(t,e,s,i){if(s-e<=4){for(let k=e;k<s-2;k+=2)i.push(t[k],t[k+1]);return}const n=t[e],a=t[e+1],o=t[s-4]-n,l=t[s-3]-a,h=Math.hypot(o,l),c=o/h,u=l/h,f=c*a-u*n,g=l/o,b=1/h,v=Math.atan(g),_=Math.cos(v),w=Math.sin(v),A=b*(Math.abs(_)+Math.abs(w)),C=b*(1-A+oe(A,2)),S=Math.max(Math.atan(Math.abs(w+_)*C),Math.atan(Math.abs(w-_)*C));let T=0,x=e;for(let k=e+2;k<s-2;k+=2){const I=Math.abs(f-c*t[k+1]+u*t[k]);I>T&&(x=k,T=I)}T>oe(h*S,2)?(y(this,yl,nu).call(this,t,e,x+2,i),y(this,yl,nu).call(this,t,x,s,i)):i.push(n,a)},ip=new WeakSet,Lv=function(t){const e=[],s=t.length;return y(this,yl,nu).call(this,t,0,s,e),e.push(t[s-2],t[s-1]),e.length<=4?null:e},np=new WeakSet,Dv=function(t,e,s,i,n,a){const o=new Float32Array(oe(a,2)),l=-2*oe(i,2),h=a>>1;for(let v=0;v<a;v++){const _=oe(v-h,2);for(let w=0;w<a;w++)o[v*a+w]=Math.exp((_+oe(w-h,2))/l)}const c=new Float32Array(256),u=-2*oe(n,2);for(let v=0;v<256;v++)c[v]=Math.exp(oe(v,2)/u);const f=t.length,g=new Uint8Array(f),b=new Uint32Array(256);for(let v=0;v<s;v++)for(let _=0;_<e;_++){const w=v*e+_,A=t[w];let C=0,S=0;for(let x=0;x<a;x++){const k=v+x-h;if(!(k<0||k>=s))for(let I=0;I<a;I++){const M=_+I-h;if(M<0||M>=e)continue;const L=t[k*e+M],F=o[x*a+I]*c[Math.abs(L-A)];C+=L*F,S+=F}}const T=g[w]=Math.round(C/S);b[T]++}return[g,b]},rp=new WeakSet,Fv=function(t){const e=new Uint32Array(256);for(const s of t)e[s]++;return e},hd=new WeakSet,Xg=function(t){const e=t.length,s=new Uint8ClampedArray(e>>2);let i=-1/0,n=1/0;for(let o=0,l=s.length;o<l;o++){if(t[(o<<2)+3]===0){i=s[o]=255;continue}const c=s[o]=t[o<<2];c>i&&(i=c),c<n&&(n=c)}const a=255/(i-n);for(let o=0;o<e;o++)s[o]=(s[o]-n)*a;return s},cd=new WeakSet,Yg=function(t){let e,s=-1/0,i=-1/0;const n=t.findIndex(l=>l!==0);let a=n,o=n;for(e=n;e<256;e++){const l=t[e];l>s&&(e-a>i&&(i=e-a,o=e-1),s=l,a=e)}for(e=o-1;e>=0&&!(t[e]>t[e+1]);e--);return e},ap=new WeakSet,Nv=function(t){const e=t,{width:s,height:i}=t,{maxDim:n}=r(this,wa);let a=s,o=i;if(s>n||i>n){let f=s,g=i,b=Math.log2(Math.max(s,i)/n);const v=Math.floor(b);b=b===v?v-1:v;for(let w=0;w<b;w++){a=f,o=g,a>n&&(a=Math.ceil(a/2)),o>n&&(o=Math.ceil(o/2));const A=new OffscreenCanvas(a,o);A.getContext("2d").drawImage(t,0,0,f,g,0,0,a,o),f=a,g=o,t!==e&&t.close(),t=A.transferToImageBitmap()}const _=Math.min(n/a,n/o);a=Math.round(a*_),o=Math.round(o*_)}const h=new OffscreenCanvas(a,o).getContext("2d",{willReadFrequently:!0});h.filter="grayscale(1)",h.drawImage(t,0,0,t.width,t.height,0,0,a,o);const c=h.getImageData(0,0,a,o).data;return[y(this,hd,Xg).call(this,c),a,o]},p(re,od),p(re,ep),p(re,sp),p(re,ld),p(re,yl),p(re,ip),p(re,np),p(re,rp),p(re,hd),p(re,cd),p(re,ap),p(re,wa,{maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16}),p(re,Us,new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]));class Rm extends xv{constructor(){super(),super.updateProperties({fill:It._defaultLineColor,"stroke-width":0})}clone(){const t=new Rm;return t.updateAll(this),t}}class Mm extends vp{constructor(t){super(t),super.updateProperties({stroke:It._defaultLineColor,"stroke-width":1})}clone(){const t=new Mm(this._viewParameters);return t.updateAll(this),t}}var Un,hs,Gn,jn;const $e=class $e extends yu{constructor(e){super(vi(Ge({},e),{mustBeCommitted:!0,name:"signatureEditor"}));p(this,Un,!1);p(this,hs,null);p(this,Gn,null);p(this,jn,null);this._willKeepAspectRatio=!0,m(this,Gn,e.signatureData||null),m(this,hs,null),this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(e,s){It.initialize(e,s),this._defaultDrawingOptions=new Rm,this._defaultDrawnSignatureOptions=new Mm(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!1}static get typesMap(){return gt(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!r(this,hs)}}static computeTelemetryFinalData(e){var i,n;const s=e.get("hasDescription");return{hasAltText:(i=s.get(!0))!=null?i:0,hasNoAltText:(n=s.get(!1))!=null?n:0}}get isResizable(){return!0}onScaleChanging(){this._drawId!==null&&super.onScaleChanging()}render(){if(this.div)return this.div;let e,s;const{_isCopy:i}=this;if(i&&(this._isCopy=!1,e=this.x,s=this.y),super.render(),this._drawId===null)if(r(this,Gn)){const{lines:n,mustSmooth:a,areContours:o,description:l,uuid:h,heightInPage:c}=r(this,Gn),{rawDims:{pageWidth:u,pageHeight:f},rotation:g}=this.parent.viewport,b=re.processDrawnLines({lines:n,pageWidth:u,pageHeight:f,rotation:g,innerMargin:$e._INNER_MARGIN,mustSmooth:a,areContours:o});this.addSignature(b,c,l,h)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(e,s)),this.div}setUuid(e){m(this,jn,e),this.addEditToolbar()}getUuid(){return r(this,jn)}get description(){return r(this,hs)}set description(e){m(this,hs,e),super.addEditToolbar().then(s=>{s==null||s.updateEditSignatureButton(e)})}getSignaturePreview(){const{newCurves:e,areContours:s,thickness:i,width:n,height:a}=r(this,Gn),o=Math.max(n,a),l=re.processDrawnLines({lines:{curves:e.map(h=>({points:h})),thickness:i,width:n,height:a},pageWidth:o,pageHeight:o,rotation:0,innerMargin:0,mustSmooth:!1,areContours:s});return{areContours:s,outline:l.outline}}addEditToolbar(){return H(this,null,function*(){const e=yield yi($e.prototype,this,"addEditToolbar").call(this);return e?(this._uiManager.signatureManager&&r(this,hs)!==null&&(yield e.addEditSignatureButton(this._uiManager.signatureManager,r(this,jn),r(this,hs)),e.show()),e):null})}addSignature(e,s,i,n){const{x:a,y:o}=this,{outline:l}=m(this,Gn,e);m(this,Un,l instanceof jg),m(this,hs,i),this.div.setAttribute("data-l10n-args",JSON.stringify({description:i}));let h;r(this,Un)?h=$e.getDefaultDrawingOptions():(h=$e._defaultDrawnSignatureOptions.clone(),h.updateProperties({"stroke-width":l.thickness})),this._addOutlines({drawOutlines:l,drawingOptions:h});const[c,u]=this.parentDimensions,[,f]=this.pageDimensions;let g=s/f;g=g>=1?.5:g,this.width*=g/this.height,this.width>=1&&(g*=.9/this.width,this.width=.9),this.height=g,this.setDims(c*this.width,u*this.height),this.x=a,this.y=o,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(n),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!n,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return re.process(e,s,i,n,$e._INNER_MARGIN)}getFromText(e,s){const{rawDims:{pageWidth:i,pageHeight:n},rotation:a}=this.parent.viewport;return re.extractContoursFromText(e,s,i,n,a,$e._INNER_MARGIN)}getDrawnSignature(e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return re.processDrawnLines({lines:e,pageWidth:s,pageHeight:i,rotation:n,innerMargin:$e._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:e,thickness:s}){e?this._drawingOptions=$e.getDefaultDrawingOptions():(this._drawingOptions=$e._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":s}))}serialize(e=!1){if(this.isEmpty())return null;const{lines:s,points:i,rect:n}=this.serializeDraw(e),{_drawingOptions:{"stroke-width":a}}=this,o={annotationType:ot.SIGNATURE,isSignature:!0,areContours:r(this,Un),color:[0,0,0],thickness:r(this,Un)?0:a,pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?(o.paths={lines:s,points:i},o.uuid=r(this,jn),o.isCopy=!0):o.lines=s,r(this,hs)&&(o.accessibilityData={type:"Figure",alt:r(this,hs)}),o}static deserializeDraw(e,s,i,n,a,o){return o.areContours?jg.deserialize(e,s,i,n,a,o):Cd.deserialize(e,s,i,n,a,o)}static deserialize(e,s,i){return H(this,null,function*(){var a;const n=yield yi($e,this,"deserialize").call(this,e,s,i);return m(n,Un,e.areContours),m(n,hs,((a=e.accessibilityData)==null?void 0:a.alt)||""),m(n,jn,e.uuid),n})}};Un=new WeakMap,hs=new WeakMap,Gn=new WeakMap,jn=new WeakMap,O($e,"_type","signature"),O($e,"_editorType",ot.SIGNATURE),O($e,"_defaultDrawingOptions",null);let Kg=$e;var Mt,pe,Wn,Ji,qn,_l,tn,Aa,pi,cs,wl,Sa,Kl,Ca,Zl,Al,ru,Sl,au,Cl,ou,dd,Qg,xl,lu,op,Ov;const Jl=class Jl extends It{constructor(e){super(vi(Ge({},e),{name:"stampEditor"}));p(this,Sa);p(this,Ca);p(this,Al);p(this,Sl);p(this,Cl);p(this,dd);p(this,xl);p(this,op);p(this,Mt,null);p(this,pe,null);p(this,Wn,null);p(this,Ji,null);p(this,qn,null);p(this,_l,"");p(this,tn,null);p(this,Aa,!1);p(this,pi,null);p(this,cs,!1);p(this,wl,!1);m(this,Ji,e.bitmapUrl),m(this,qn,e.bitmapFile),this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(e,s){It.initialize(e,s)}static isHandlingMimeForPasting(e){return Gp.includes(e)}static paste(e,s){s.pasteEditor(ot.STAMP,{bitmapFile:e.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){var e;return{type:"stamp",hasAltText:!!((e=this.altTextData)!=null&&e.altText)}}static computeTelemetryFinalData(e){var i,n;const s=e.get("hasAltText");return{hasAltText:(i=s.get(!0))!=null?i:0,hasNoAltText:(n=s.get(!1))!=null?n:0}}mlGuessAltText(e=null,s=!0){return H(this,null,function*(){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!(yield i.isEnabledFor("altText")))throw new Error("ML isn't enabled for alt text.");const{data:n,width:a,height:o}=e||this.copyCanvas(null,null,!0).imageData,l=yield i.guess({name:"altText",request:{data:n,width:a,height:o,channels:n.length/(a*o)}});if(!l)throw new Error("No response from the AI service.");if(l.error)throw new Error("Error from the AI service.");if(l.cancel)return null;if(!l.output)throw new Error("No valid response from the AI service.");const h=l.output;return yield this.setGuessedAltText(h),s&&!this.hasAltTextData()&&(this.altTextData={alt:h,decorative:!1}),h})}remove(){var e;r(this,pe)&&(m(this,Mt,null),this._uiManager.imageManager.deleteId(r(this,pe)),(e=r(this,tn))==null||e.remove(),m(this,tn,null),r(this,pi)&&(clearTimeout(r(this,pi)),m(this,pi,null))),super.remove()}rebuild(){if(!this.parent){r(this,pe)&&y(this,Al,ru).call(this);return}super.rebuild(),this.div!==null&&(r(this,pe)&&r(this,tn)===null&&y(this,Al,ru).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(e){this._isDraggable=!0,e&&this.div.focus()}isEmpty(){return!(r(this,Wn)||r(this,Mt)||r(this,Ji)||r(this,qn)||r(this,pe)||r(this,Aa))}get isResizable(){return!0}render(){if(this.div)return this.div;let e,s;return this._isCopy&&(e=this.x,s=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),r(this,Aa)||(r(this,Mt)?y(this,Sl,au).call(this):y(this,Al,ru).call(this)),this._isCopy&&this._moveAfterPaste(e,s),this._uiManager.addShouldRescale(this),this.div}setCanvas(e,s){const{id:i,bitmap:n}=this._uiManager.imageManager.getFromCanvas(e,s);s.remove(),i&&this._uiManager.imageManager.isValidId(i)&&(m(this,pe,i),n&&m(this,Mt,n),m(this,Aa,!1),y(this,Sl,au).call(this))}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;r(this,pi)!==null&&clearTimeout(r(this,pi)),m(this,pi,setTimeout(()=>{m(this,pi,null),y(this,dd,Qg).call(this)},200))}copyCanvas(e,s,i=!1){var g;e||(e=224);const{width:n,height:a}=r(this,Mt),o=new an;let l=r(this,Mt),h=n,c=a,u=null;if(s){if(n>s||a>s){const I=Math.min(s/n,s/a);h=Math.floor(n*I),c=Math.floor(a*I)}u=document.createElement("canvas");const b=u.width=Math.ceil(h*o.sx),v=u.height=Math.ceil(c*o.sy);r(this,cs)||(l=y(this,Cl,ou).call(this,b,v));const _=u.getContext("2d");_.filter=this._uiManager.hcmFilter;let w="white",A="#cfcfd8";this._uiManager.hcmFilter!=="none"?A="black":(g=window.matchMedia)!=null&&g.call(window,"(prefers-color-scheme: dark)").matches&&(w="#8f8f9d",A="#42414d");const C=15,S=C*o.sx,T=C*o.sy,x=new OffscreenCanvas(S*2,T*2),k=x.getContext("2d");k.fillStyle=w,k.fillRect(0,0,S*2,T*2),k.fillStyle=A,k.fillRect(0,0,S,T),k.fillRect(S,T,S,T),_.fillStyle=_.createPattern(x,"repeat"),_.fillRect(0,0,b,v),_.drawImage(l,0,0,l.width,l.height,0,0,b,v)}let f=null;if(i){let b,v;if(o.symmetric&&l.width<e&&l.height<e)b=l.width,v=l.height;else if(l=r(this,Mt),n>e||a>e){const A=Math.min(e/n,e/a);b=Math.floor(n*A),v=Math.floor(a*A),r(this,cs)||(l=y(this,Cl,ou).call(this,b,v))}const w=new OffscreenCanvas(b,v).getContext("2d",{willReadFrequently:!0});w.drawImage(l,0,0,l.width,l.height,0,0,b,v),f={width:b,height:v,data:w.getImageData(0,0,b,v).data}}return{canvas:u,width:h,height:c,imageData:f}}static deserialize(e,s,i){return H(this,null,function*(){var _;let n=null,a=!1;if(e instanceof Qb){const{data:{rect:w,rotation:A,id:C,structParent:S,popupRef:T},container:x,parent:{page:{pageNumber:k}},canvas:I}=e;let M,L;I?(delete e.canvas,{id:M,bitmap:L}=i.imageManager.getFromCanvas(x.id,I),I.remove()):(a=!0,e._hasNoCanvas=!0);const F=((_=yield s._structTree.getAriaAttributes(`${wm}${C}`))==null?void 0:_.get("aria-label"))||"";n=e={annotationType:ot.STAMP,bitmapId:M,bitmap:L,pageIndex:k-1,rect:w.slice(0),rotation:A,id:C,deleted:!1,accessibilityData:{decorative:!1,altText:F},isSvg:!1,structParent:S,popupRef:T}}const o=yield yi(Jl,this,"deserialize").call(this,e,s,i),{rect:l,bitmap:h,bitmapUrl:c,bitmapId:u,isSvg:f,accessibilityData:g}=e;a?(i.addMissingCanvas(e.id,o),m(o,Aa,!0)):u&&i.imageManager.isValidId(u)?(m(o,pe,u),h&&m(o,Mt,h)):m(o,Ji,c),m(o,cs,f);const[b,v]=o.pageDimensions;return o.width=(l[2]-l[0])/b,o.height=(l[3]-l[1])/v,o.annotationElementId=e.id||null,g&&(o.altTextData=g),o._initialData=n,m(o,wl,!!n),o})}serialize(e=!1,s=null){var l;if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:ot.STAMP,bitmapId:r(this,pe),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:r(this,cs),structTreeParentId:this._structTreeParentId};if(e)return i.bitmapUrl=y(this,xl,lu).call(this,!0),i.accessibilityData=this.serializeAltText(!0),i.isCopy=!0,i;const{decorative:n,altText:a}=this.serializeAltText(!1);if(!n&&a&&(i.accessibilityData={type:"Figure",alt:a}),this.annotationElementId){const h=y(this,op,Ov).call(this,i);if(h.isSame)return null;h.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=(l=this._initialData.structParent)!=null?l:-1}if(i.id=this.annotationElementId,s===null)return i;s.stamps||(s.stamps=new Map);const o=r(this,cs)?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(!s.stamps.has(r(this,pe)))s.stamps.set(r(this,pe),{area:o,serialized:i}),i.bitmap=y(this,xl,lu).call(this,!1);else if(r(this,cs)){const h=s.stamps.get(r(this,pe));o>h.area&&(h.area=o,h.serialized.bitmap.close(),h.serialized.bitmap=y(this,xl,lu).call(this,!1))}return i}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}};Mt=new WeakMap,pe=new WeakMap,Wn=new WeakMap,Ji=new WeakMap,qn=new WeakMap,_l=new WeakMap,tn=new WeakMap,Aa=new WeakMap,pi=new WeakMap,cs=new WeakMap,wl=new WeakMap,Sa=new WeakSet,Kl=function(e,s=!1){if(!e){this.remove();return}m(this,Mt,e.bitmap),s||(m(this,pe,e.id),m(this,cs,e.isSvg)),e.file&&m(this,_l,e.file.name),y(this,Sl,au).call(this)},Ca=new WeakSet,Zl=function(){if(m(this,Wn,null),this._uiManager.enableWaiting(!1),!!r(this,tn)){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&r(this,Mt)){this._editToolbar.hide(),this._uiManager.editAltText(this,!0);return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&r(this,Mt)){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch(e){}}this.div.focus()}},Al=new WeakSet,ru=function(){if(r(this,pe)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(r(this,pe)).then(i=>y(this,Sa,Kl).call(this,i,!0)).finally(()=>y(this,Ca,Zl).call(this));return}if(r(this,Ji)){const i=r(this,Ji);m(this,Ji,null),this._uiManager.enableWaiting(!0),m(this,Wn,this._uiManager.imageManager.getFromUrl(i).then(n=>y(this,Sa,Kl).call(this,n)).finally(()=>y(this,Ca,Zl).call(this)));return}if(r(this,qn)){const i=r(this,qn);m(this,qn,null),this._uiManager.enableWaiting(!0),m(this,Wn,this._uiManager.imageManager.getFromFile(i).then(n=>y(this,Sa,Kl).call(this,n)).finally(()=>y(this,Ca,Zl).call(this)));return}const e=document.createElement("input");e.type="file",e.accept=Gp.join(",");const s=this._uiManager._signal;m(this,Wn,new Promise(i=>{e.addEventListener("change",()=>H(this,null,function*(){if(!e.files||e.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const n=yield this._uiManager.imageManager.getFromFile(e.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),y(this,Sa,Kl).call(this,n)}i()}),{signal:s}),e.addEventListener("cancel",()=>{this.remove(),i()},{signal:s})}).finally(()=>y(this,Ca,Zl).call(this))),e.click()},Sl=new WeakSet,au=function(){var u;const{div:e}=this;let{width:s,height:i}=r(this,Mt);const[n,a]=this.pageDimensions,o=.75;if(this.width)s=this.width*n,i=this.height*a;else if(s>o*n||i>o*a){const f=Math.min(o*n/s,o*a/i);s*=f,i*=f}const[l,h]=this.parentDimensions;this.setDims(s*l/n,i*h/a),this._uiManager.enableWaiting(!1);const c=m(this,tn,document.createElement("canvas"));c.setAttribute("role","img"),this.addContainer(c),this.width=s/n,this.height=i/a,(u=this._initialOptions)!=null&&u.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,(!this._uiManager.useNewAltTextWhenAddingImage||!this._uiManager.useNewAltTextFlow||this.annotationElementId)&&(e.hidden=!1),y(this,dd,Qg).call(this),r(this,wl)||(this.parent.addUndoableEditor(this),m(this,wl,!0)),this._reportTelemetry({action:"inserted_image"}),r(this,_l)&&this.div.setAttribute("aria-description",r(this,_l))},Cl=new WeakSet,ou=function(e,s){const{width:i,height:n}=r(this,Mt);let a=i,o=n,l=r(this,Mt);for(;a>2*e||o>2*s;){const h=a,c=o;a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),o>2*s&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2));const u=new OffscreenCanvas(a,o);u.getContext("2d").drawImage(l,0,0,h,c,0,0,a,o),l=u.transferToImageBitmap()}return l},dd=new WeakSet,Qg=function(){const[e,s]=this.parentDimensions,{width:i,height:n}=this,a=new an,o=Math.ceil(i*e*a.sx),l=Math.ceil(n*s*a.sy),h=r(this,tn);if(!h||h.width===o&&h.height===l)return;h.width=o,h.height=l;const c=r(this,cs)?r(this,Mt):y(this,Cl,ou).call(this,o,l),u=h.getContext("2d");u.filter=this._uiManager.hcmFilter,u.drawImage(c,0,0,c.width,c.height,0,0,o,l)},xl=new WeakSet,lu=function(e){if(e){if(r(this,cs)){const n=this._uiManager.imageManager.getSvgUrl(r(this,pe));if(n)return n}const s=document.createElement("canvas");return{width:s.width,height:s.height}=r(this,Mt),s.getContext("2d").drawImage(r(this,Mt),0,0),s.toDataURL()}if(r(this,cs)){const[s,i]=this.pageDimensions,n=Math.round(this.width*s*tr.PDF_TO_CSS_UNITS),a=Math.round(this.height*i*tr.PDF_TO_CSS_UNITS),o=new OffscreenCanvas(n,a);return o.getContext("2d").drawImage(r(this,Mt),0,0,r(this,Mt).width,r(this,Mt).height,0,0,n,a),o.transferToImageBitmap()}return structuredClone(r(this,Mt))},op=new WeakSet,Ov=function(e){var o;const{pageIndex:s,accessibilityData:{altText:i}}=this._initialData,n=e.pageIndex===s,a=(((o=e.accessibilityData)==null?void 0:o.alt)||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&n&&a,isSameAltText:a}},O(Jl,"_type","stamp"),O(Jl,"_editorType",ot.STAMP);let Zg=Jl;var xa,El,gi,Xn,en,xs,Yn,Tl,Ea,Gs,sn,Re,nn,Z,Kn,lp,$v,Es,Qs,ud,tm,fd,em,kl,hu;const Rs=class Rs{constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:n,annotationLayer:a,drawLayer:o,textLayer:l,viewport:h,l10n:c}){p(this,lp);p(this,Es);p(this,ud);p(this,fd);p(this,kl);p(this,xa,void 0);p(this,El,!1);p(this,gi,null);p(this,Xn,null);p(this,en,null);p(this,xs,new Map);p(this,Yn,!1);p(this,Tl,!1);p(this,Ea,!1);p(this,Gs,null);p(this,sn,null);p(this,Re,null);p(this,nn,null);p(this,Z,void 0);const u=[...r(Rs,Kn).values()];if(!Rs._initialized){Rs._initialized=!0;for(const f of u)f.initialize(c,t)}t.registerEditorTypes(u),m(this,Z,t),this.pageIndex=e,this.div=s,m(this,xa,n),m(this,gi,a),this.viewport=h,m(this,Re,l),this.drawLayer=o,this._structTree=i,r(this,Z).addLayer(this)}get isEmpty(){return r(this,xs).size===0}get isInvisible(){return this.isEmpty&&r(this,Z).getMode()===ot.NONE}updateToolbar(t){r(this,Z).updateToolbar(t)}updateMode(t=r(this,Z).getMode()){switch(y(this,kl,hu).call(this),t){case ot.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case ot.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case ot.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of r(Rs,Kn).values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){var e;return t===((e=r(this,Re))==null?void 0:e.div)}setEditingState(t){r(this,Z).setEditingState(t)}addCommands(t){r(this,Z).addCommands(t)}cleanUndoStack(t){r(this,Z).cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){var e;(e=r(this,gi))==null||e.div.classList.toggle("disabled",!t)}enable(){return H(this,null,function*(){m(this,Ea,!0),this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const s of r(this,xs).values())s.enableEditing(),s.show(!0),s.annotationElementId&&(r(this,Z).removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!r(this,gi)){m(this,Ea,!1);return}const e=r(this,gi).getEditableAnnotations();for(const s of e){if(s.hide(),r(this,Z).isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=yield this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}m(this,Ea,!1)})}disable(){var i;m(this,Tl,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const n of r(this,xs).values())if(n.disableEditing(),!!n.annotationElementId){if(n.serialize()!==null){t.set(n.annotationElementId,n);continue}else e.set(n.annotationElementId,n);(i=this.getEditableAnnotation(n.annotationElementId))==null||i.show(),n.remove()}if(r(this,gi)){const n=r(this,gi).getEditableAnnotations();for(const a of n){const{id:o}=a.data;if(r(this,Z).isDeletedAnnotationElement(o))continue;let l=e.get(o);if(l){l.resetAnnotationElement(a),l.show(!1),a.show();continue}l=t.get(o),l&&(r(this,Z).addChangedExistingAnnotation(l),l.renderAnnotationElement(a)&&l.show(!1)),a.show()}}y(this,kl,hu).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const n of r(Rs,Kn).values())s.remove(`${n._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),m(this,Tl,!1)}getEditableAnnotation(t){var e;return((e=r(this,gi))==null?void 0:e.getEditableAnnotation(t))||null}setActiveEditor(t){r(this,Z).getActive()!==t&&r(this,Z).setActiveEditor(t)}enableTextSelection(){var t;if(this.div.tabIndex=-1,(t=r(this,Re))!=null&&t.div&&!r(this,nn)){m(this,nn,new AbortController);const e=r(this,Z).combinedSignal(r(this,nn));r(this,Re).div.addEventListener("pointerdown",y(this,lp,$v).bind(this),{signal:e}),r(this,Re).div.classList.add("highlighting")}}disableTextSelection(){var t;this.div.tabIndex=0,(t=r(this,Re))!=null&&t.div&&r(this,nn)&&(r(this,nn).abort(),m(this,nn,null),r(this,Re).div.classList.remove("highlighting"))}enableClick(){if(r(this,Xn))return;m(this,Xn,new AbortController);const t=r(this,Z).combinedSignal(r(this,Xn));this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){var t;(t=r(this,Xn))==null||t.abort(),m(this,Xn,null)}attach(t){r(this,xs).set(t.id,t);const{annotationElementId:e}=t;e&&r(this,Z).isDeletedAnnotationElement(e)&&r(this,Z).removeDeletedAnnotationElement(t)}detach(t){var e;r(this,xs).delete(t.id),(e=r(this,xa))==null||e.removePointerInTextLayer(t.contentDiv),!r(this,Tl)&&t.annotationElementId&&r(this,Z).addDeletedAnnotationElement(t)}remove(t){this.detach(t),r(this,Z).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){var e;t.parent!==this&&(t.parent&&t.annotationElementId&&(r(this,Z).addDeletedAnnotationElement(t.annotationElementId),It.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),(e=t.parent)==null||e.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),r(this,Z).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!r(this,Ea)),r(this,Z).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){var s;if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!r(this,en)&&(t._focusEventsAllowed=!1,m(this,en,setTimeout(()=>{m(this,en,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:r(this,Z)._signal}),e.focus())},0))),t._structTreeParentId=(s=r(this,xa))==null?void 0:s.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return r(this,Z).getId()}combinedSignal(t){return r(this,Z).combinedSignal(t)}canCreateNewEmptyEditor(){var t;return(t=r(this,Es,Qs))==null?void 0:t.canCreateNewEmptyEditor()}pasteEditor(t,e){return H(this,null,function*(){r(this,Z).updateToolbar(t),yield r(this,Z).updateMode(t);const{offsetX:s,offsetY:i}=y(this,fd,em).call(this),n=this.getNextId(),a=y(this,ud,tm).call(this,Ge({parent:this,id:n,x:s,y:i,uiManager:r(this,Z),isCentered:!0},e));a&&this.add(a)})}deserialize(t){return H(this,null,function*(){var e,s;return(yield(s=r(Rs,Kn).get((e=t.annotationType)!=null?e:t.annotationEditorType))==null?void 0:s.deserialize(t,this,r(this,Z)))||null})}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),n=y(this,ud,tm).call(this,Ge({parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:r(this,Z),isCentered:e},s));return n&&this.add(n),n}addNewEditor(t={}){this.createAndAddNewEditor(y(this,fd,em).call(this),!0,t)}setSelected(t){r(this,Z).setSelected(t)}toggleSelected(t){r(this,Z).toggleSelected(t)}unselect(t){r(this,Z).unselect(t)}pointerup(t){var i;const{isMac:e}=Se.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div||!r(this,Yn)||(m(this,Yn,!1),(i=r(this,Es,Qs))!=null&&i.isDrawer&&r(this,Es,Qs).supportMultipleDrawings))return;if(!r(this,El)){m(this,El,!0);return}const s=r(this,Z).getMode();if(s===ot.STAMP||s===ot.SIGNATURE){r(this,Z).unselectAll();return}this.createAndAddNewEditor(t,!1)}pointerdown(t){var i;if(r(this,Z).getMode()===ot.HIGHLIGHT&&this.enableTextSelection(),r(this,Yn)){m(this,Yn,!1);return}const{isMac:e}=Se.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(m(this,Yn,!0),(i=r(this,Es,Qs))!=null&&i.isDrawer){this.startDrawingSession(t);return}const s=r(this,Z).getActive();m(this,El,!s||s.isEmpty())}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),r(this,Gs)){r(this,Es,Qs).startDrawing(this,r(this,Z),!1,t);return}r(this,Z).setCurrentDrawingSession(this),m(this,Gs,new AbortController);const e=r(this,Z).combinedSignal(r(this,Gs));this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(m(this,sn,null),this.commitOrRemove())},{signal:e}),r(this,Es,Qs).startDrawing(this,r(this,Z),!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&m(this,sn,e);return}r(this,sn)&&setTimeout(()=>{var e;(e=r(this,sn))==null||e.focus(),m(this,sn,null)},0)}endDrawingSession(t=!1){return r(this,Gs)?(r(this,Z).setCurrentDrawingSession(null),r(this,Gs).abort(),m(this,Gs,null),m(this,sn,null),r(this,Es,Qs).endDrawing(t)):null}findNewParent(t,e,s){const i=r(this,Z).findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return r(this,Gs)?(this.endDrawingSession(),!0):!1}onScaleChanging(){r(this,Gs)&&r(this,Es,Qs).onScaleChangingWhenDrawing(this)}destroy(){var t,e;this.commitOrRemove(),((t=r(this,Z).getActive())==null?void 0:t.parent)===this&&(r(this,Z).commitOrRemove(),r(this,Z).setActiveEditor(null)),r(this,en)&&(clearTimeout(r(this,en)),m(this,en,null));for(const s of r(this,xs).values())(e=r(this,xa))==null||e.removePointerInTextLayer(s.contentDiv),s.setParent(null),s.isAttachedToDOM=!1,s.div.remove();this.div=null,r(this,xs).clear(),r(this,Z).removeLayer(this)}render({viewport:t}){this.viewport=t,Ra(this.div,t);for(const e of r(this,Z).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){r(this,Z).commitOrRemove(),y(this,kl,hu).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,Ra(this.div,{rotation:s}),e!==s)for(const i of r(this,xs).values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return r(this,Z).viewParameters.realScale}};xa=new WeakMap,El=new WeakMap,gi=new WeakMap,Xn=new WeakMap,en=new WeakMap,xs=new WeakMap,Yn=new WeakMap,Tl=new WeakMap,Ea=new WeakMap,Gs=new WeakMap,sn=new WeakMap,Re=new WeakMap,nn=new WeakMap,Z=new WeakMap,Kn=new WeakMap,lp=new WeakSet,$v=function(t){r(this,Z).unselectAll();const{target:e}=t;if(e===r(this,Re).div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&r(this,Re).div.contains(e)){const{isMac:s}=Se.platform;if(t.button!==0||t.ctrlKey&&s)return;r(this,Z).showAllEditors("highlight",!0,!0),r(this,Re).div.classList.add("free"),this.toggleDrawing(),vu.startHighlighting(this,r(this,Z).direction==="ltr",{target:r(this,Re).div,x:t.x,y:t.y}),r(this,Re).div.addEventListener("pointerup",()=>{r(this,Re).div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:r(this,Z)._signal}),t.preventDefault()}},Es=new WeakSet,Qs=function(){return r(Rs,Kn).get(r(this,Z).getMode())},ud=new WeakSet,tm=function(t){const e=r(this,Es,Qs);return e?new e.prototype.constructor(t):null},fd=new WeakSet,em=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),o=Math.min(window.innerWidth,t+s),l=Math.min(window.innerHeight,e+i),h=(n+o)/2-t,c=(a+l)/2-e,[u,f]=this.viewport.rotation%180===0?[h,c]:[c,h];return{offsetX:u,offsetY:f}},kl=new WeakSet,hu=function(){for(const t of r(this,xs).values())t.isEmpty()&&t.remove()},O(Rs,"_initialized",!1),p(Rs,Kn,new Map([Rg,Gg,Zg,vu,Kg].map(t=>[t._editorType,t])));let Jg=Rs;var js,Ve,Ta,pd,hp,Hv,gd,im,cp,Bv,md,nm;const se=class se{constructor({pageIndex:t}){p(this,gd);p(this,cp);p(this,md);p(this,js,null);p(this,Ve,new Map);p(this,Ta,new Map);this.pageIndex=t}setParent(t){if(!r(this,js)){m(this,js,t);return}if(r(this,js)!==t){if(r(this,Ve).size>0)for(const e of r(this,Ve).values())e.remove(),t.append(e);m(this,js,t)}}static get _svgFactory(){return gt(this,"_svgFactory",new gu)}draw(t,e=!1,s=!1){const i=Fe(se,pd)._++,n=y(this,gd,im).call(this),a=se._svgFactory.createElement("defs");n.append(a);const o=se._svgFactory.createElement("path");a.append(o);const l=`path_p${this.pageIndex}_${i}`;o.setAttribute("id",l),o.setAttribute("vector-effect","non-scaling-stroke"),e&&r(this,Ta).set(i,o);const h=s?y(this,cp,Bv).call(this,a,l):null,c=se._svgFactory.createElement("use");return n.append(c),c.setAttribute("href",`#${l}`),this.updateProperties(n,t),r(this,Ve).set(i,n),{id:i,clipPathId:`url(#${h})`}}drawOutline(t,e){const s=Fe(se,pd)._++,i=y(this,gd,im).call(this),n=se._svgFactory.createElement("defs");i.append(n);const a=se._svgFactory.createElement("path");n.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke");let l;if(e){const u=se._svgFactory.createElement("mask");n.append(u),l=`mask_p${this.pageIndex}_${s}`,u.setAttribute("id",l),u.setAttribute("maskUnits","objectBoundingBox");const f=se._svgFactory.createElement("rect");u.append(f),f.setAttribute("width","1"),f.setAttribute("height","1"),f.setAttribute("fill","white");const g=se._svgFactory.createElement("use");u.append(g),g.setAttribute("href",`#${o}`),g.setAttribute("stroke","none"),g.setAttribute("fill","black"),g.setAttribute("fill-rule","nonzero"),g.classList.add("mask")}const h=se._svgFactory.createElement("use");i.append(h),h.setAttribute("href",`#${o}`),l&&h.setAttribute("mask",`url(#${l})`);const c=h.cloneNode();return i.append(c),h.classList.add("mainOutline"),c.classList.add("secondaryOutline"),this.updateProperties(i,t),r(this,Ve).set(s,i),s}finalizeDraw(t,e){r(this,Ta).delete(t),this.updateProperties(t,e)}updateProperties(t,e){var l;if(!e)return;const{root:s,bbox:i,rootClass:n,path:a}=e,o=typeof t=="number"?r(this,Ve).get(t):t;if(o){if(s&&y(this,md,nm).call(this,o,s),i&&y(l=se,hp,Hv).call(l,o,i),n){const{classList:h}=o;for(const[c,u]of Object.entries(n))h.toggle(c,u)}if(a){const c=o.firstChild.firstChild;y(this,md,nm).call(this,c,a)}}}updateParent(t,e){if(e===this)return;const s=r(this,Ve).get(t);s&&(r(e,js).append(s),r(this,Ve).delete(t),r(e,Ve).set(t,s))}remove(t){r(this,Ta).delete(t),r(this,js)!==null&&(r(this,Ve).get(t).remove(),r(this,Ve).delete(t))}destroy(){m(this,js,null);for(const t of r(this,Ve).values())t.remove();r(this,Ve).clear(),r(this,Ta).clear()}};js=new WeakMap,Ve=new WeakMap,Ta=new WeakMap,pd=new WeakMap,hp=new WeakSet,Hv=function(t,[e,s,i,n]){const{style:a}=t;a.top=`${100*s}%`,a.left=`${100*e}%`,a.width=`${100*i}%`,a.height=`${100*n}%`},gd=new WeakSet,im=function(){const t=se._svgFactory.create(1,1,!0);return r(this,js).append(t),t.setAttribute("aria-hidden",!0),t},cp=new WeakSet,Bv=function(t,e){const s=se._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const n=se._svgFactory.createElement("use");return s.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),i},md=new WeakSet,nm=function(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)},p(se,hp),p(se,pd,0);let sm=se;globalThis.pdfjsTestingUtils={HighlightOutliner:Lg};globalThis.pdfjsLib={AbortException:Jn,AnnotationEditorLayer:Jg,AnnotationEditorParamsType:vt,AnnotationEditorType:ot,AnnotationEditorUIManager:Ma,AnnotationLayer:kg,AnnotationMode:hn,AnnotationType:Yt,build:pw,ColorPicker:bu,createValidAbsoluteUrl:D0,DOMSVGFactory:gu,DrawLayer:sm,FeatureTest:Se,fetchData:yd,getDocument:Ob,getFilenameFromUrl:o_,getPdfFilenameFromUrl:l_,getUuid:O0,getXfaPageViewport:h_,GlobalWorkerOptions:bi,ImageKind:Ld,InvalidPDFException:zp,isDataScheme:mp,isPdfFile:Sm,isValidExplicitDest:ow,MathClamp:Ue,noContextMenu:qs,normalizeUnicode:n_,OPS:du,OutputScale:an,PasswordResponses:Yy,PDFDataRangeTransport:$b,PDFDateString:Cm,PDFWorker:Xa,PermissionFlag:Xy,PixelsPerInch:tr,RenderingCancelledException:Am,ResponseException:uu,setLayerDimensions:Ra,shadow:gt,SignatureExtractor:re,stopEvent:Kt,SupportedImageMimeTypes:Gp,TextLayer:sh,TouchManager:pu,updateUrlHash:F0,Util:K,VerbosityLevel:fp,version:fw,XfaLayer:Bb};const Nw={key:0,class:"error-tips"},Ow={key:1,id:"printJS-area"},$w=_t({__name:"index",props:{height:{type:String,default:void 0},getPdfData:{type:Function,default:()=>{}},autoGetData:{type:Boolean,default:!0}},setup(d,{expose:t}){const e=d,s=nt(""),i=nt(!1),n=nt(null);Ws(()=>H(this,null,function*(){Promise.withResolvers||(Promise.withResolvers=function(){let u,f;return{promise:new Promise((b,v)=>{u=b,f=v}),resolve:u,reject:f}}),URL.parse||(URL.parse=function(u,f){try{return new URL(u,f)}catch(g){return null}}),bi.workerSrc="/pdf.worker.min.mjs",e.autoGetData&&o()}));function a(u){return H(this,null,function*(){try{const g=yield(yield u.clone()).text();return JSON.parse(g),!1}catch(f){return!0}})}function o(){e.getPdfData().then(u=>H(this,null,function*(){if(u)if(yield a(u)){const g=yield u.blob();if(g.type.indexOf("application/pdf")===-1){l({data:{customField:"tinymceDialogError",err:"pdf文件不存在！"}});return}const b=new Blob([g],{type:"application/pdf;charset=utf-8"}),v=new FileReader;v.readAsArrayBuffer(b),v.onloadend=_=>{var w;l({data:{customField:"tinymceDialogGetPdf",pdfData:new Uint8Array((w=_.target)==null?void 0:w.result)}})},n.value=b}else{const g=yield u.text(),b=JSON.parse(g);l({data:{customField:"tinymceDialogError",err:b.msg}})}})).catch(u=>{l({data:{customField:"tinymceDialogError",err:u}})})}function l(u){var f=u.data;f.customField==="tinymceDialogGetPdf"?h(f.pdfData):f.customField==="tinymceDialogError"&&(i.value=!0,s.value="pdf 加载失败 "+f.err)}function h(u){Ob({data:u,cMapUrl:"/cmaps/",cMapPacked:!0}).promise.then(b=>{const v=document.getElementById("printJS-area"),_=v.childNodes;for(let w=_.length-1;w>=0;w--)v.removeChild(_[w]);c(b,1,b.numPages)})}function c(u,f,g){return new Promise(b=>{u.getPage(f).then(v=>{const _=document.createElement("span");_.style.position="relative",_.style.display="block";const w=document.createElement("canvas");_.appendChild(w),document.getElementById("printJS-area").appendChild(_);let A=v.getViewport({scale:1});A=v.getViewport({scale:794/A.width}),w.height=A.height,w.width=A.width;const S={canvasContext:w.getContext("2d"),viewport:A};v.render(S).promise.then(()=>H(this,null,function*(){b(void 0),f+=1,f<=g?yield c(u,f,g):u.destroy()}))})})}return t({initPreview:o,pdfBlod:n}),(u,f)=>(P(),$("div",{class:"previewPdf-main",style:De({height:d.height})},[i.value?(P(),$("div",Nw,Jt(s.value),1)):(P(),$("div",Ow))],4))}}),Hw=ae($w,[["__scopeId","data-v-fc5b94f4"]]),Bw=Hw,Vw={class:"viewDialog"},zw=_t({__name:"help-center",props:{modelValue:{type:Boolean,default:()=>!1}},emits:["update:modelValue"],setup(d,{emit:t}){var h,c,u;const s=(u=((c=(h=Ts().getItem(Pa))==null?void 0:h.role_name)!=null?c:[])[0])!=null?u:"",i=d,n=nt(i.modelValue),a=t;Is(()=>i.modelValue,()=>{n.value=i.modelValue}),Is(n,f=>{a("update:modelValue",f)});const o=nt(`/helpDocument/${s||"通用"}.pdf`);function l(){return fetch(o.value)}return(f,g)=>{const b=Q("el-dialog");return P(),$("div",Vw,[R(b,{modelValue:n.value,"onUpdate:modelValue":g[0]||(g[0]=v=>n.value=v),draggable:"","append-to-body":"",width:"900",title:"帮助中心","destroy-on-close":"","close-on-click-modal":!1,class:"pdfPreview","modal-class":"help-center-modal"},{default:q(()=>[n.value?(P(),lt(E(Bw),{key:0,ref:"pdfPreviewRef","auto-get-data":!0,"get-pdf-data":l,style:{width:"100%",overflow:"auto"}},null,512)):ct("",!0)]),_:1},8,["modelValue"])])}}}),Vv={width:24,height:24,body:'<path fill="currentColor" d="M21 18v2H3v-2h18ZM6.95 3.55v9.9L2 8.5l4.95-4.95ZM21 11v2h-9v-2h9Zm0-7v2h-9V4h9Z"/>'},Uw={width:24,height:24,body:'<path fill="currentColor" d="M21 18v2H3v-2h18ZM17.05 3.55L22 8.5l-4.95 4.95v-9.9ZM12 11v2H3v-2h9Zm0-7v2H3V4h9Z"/>'},Gw=["title"],jw=_t({__name:"topCollapse",props:{isActive:{type:Boolean,default:!1}},emits:["toggleClick"],setup(d,{emit:t}){const e=d,s=t,i=()=>{s("toggleClick")};return(n,a)=>{const o=Q("IconifyIconOffline");return P(),$("div",{class:"px-3 mr-1 navbar-bg-hover",title:e.isActive?"点击折叠":"点击展开",onClick:i},[R(o,{icon:e.isActive?E(Vv):E(Uw),class:"inline-block align-middle hover:text-primary dark:hover:!text-white"},null,8,["icon"])],8,Gw)}}}),Ww={width:24,height:24,body:'<path fill="currentColor" d="m12 13.171l4.95-4.95l1.414 1.415L12 16L5.636 9.636L7.05 8.222l4.95 4.95Z"/>'},rm={width:24,height:24,body:'<path fill="currentColor" d="m13.171 12l-4.95-4.95l1.415-1.413L16 12l-6.364 6.364l-1.414-1.415l4.95-4.95Z"/>'},am={width:24,height:24,body:'<path fill="currentColor" d="m10.828 12l4.95 4.95l-1.414 1.415L8 12l6.364-6.364l1.414 1.414l-4.95 4.95Z"/>'};var qw={VITE_PORT:"8848",VITE_HIDE_HOME:"true",VITE_PUBLIC_PATH:"/",VITE_ROUTER_HISTORY:"hash",VITE_CDN:"false",VITE_COMPRESSION:"none",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const Xw={class:"arrow-left"},Yw=["onContextmenu","onMouseenter","onMouseleave","onClick"],Kw={class:"tag-title dark:!text-text_color_primary dark:hover:!text-primary"},Zw=["onClick"],Qw={class:"arrow-right"},Jw=["onClick"],tA={class:"arrow-down"},eA=_t({__name:"index",setup(d){var yt;const{Close:t,route:e,router:s,visible:i,showTags:n,instance:a,multiTags:o,tagsViews:l,buttonTop:h,buttonLeft:c,showModel:u,translateX:f,pureSetting:g,activeIndex:b,getTabStyle:v,isScrolling:_,iconIsActive:w,linkIsActive:A,currentSelect:C,scheduleIsActive:S,getContextMenuStyle:T,closeMenu:x,onMounted:k,onMouseenter:I,onMouseleave:M,onContentFullScreen:L}=P0(),F=nt(),it=nt(),at=nt(),J=nt(),tt=nt(!1),B=(yt=cu())==null?void 0:yt.path,{VITE_HIDE_HOME:G}=qw,kt=()=>H(this,null,function*(){yield ka();const z=o.value.findIndex(et=>Ia(e.query)?Ia(e.params)?e.path===et.path:xp(e.params,et.params):xp(e.query,et.query));Bt(z)}),Bt=z=>H(this,null,function*(){var qt,ms;yield ka();const et=10;if(!a.refs["dynamic"+z])return;const st=a.refs["dynamic"+z][0],mt=st==null?void 0:st.offsetLeft,Ot=st==null?void 0:st.offsetWidth,dt=at.value?(qt=at.value)==null?void 0:qt.offsetWidth:0,ut=F.value?(ms=F.value)==null?void 0:ms.offsetWidth:0;dt<=ut?tt.value=!0:tt.value=!1,ut<dt||mt===0?f.value=0:mt<-f.value?f.value=-mt+et:mt>-f.value&&mt+Ot<-f.value+dt?f.value=Math.min(0,dt-Ot-mt-et):f.value=-(mt-(dt-et-Ot))}),ge=z=>{var mt;const et=at.value?(mt=at.value)==null?void 0:mt.offsetWidth:0,st=F.value?F.value.offsetWidth:0;z>0?f.value=Math.min(0,f.value+z):et<st?f.value>=-(st-et)&&(f.value=Math.max(f.value+z,et-st)):f.value=0,_.value=!1},xe=z=>{_.value=!0;const et=Math.abs(z.deltaX)+Math.abs(z.deltaY);let st=0;z.deltaX<0?st=et>0?et:100:st=et>0?-et:-100,Et(st)},Et=z=>{let st=Math.abs(z);const mt=()=>{const Ot=Math.sign(z)*Math.min(20,st);ge(Ot),st-=Math.abs(Ot),st>0&&requestAnimationFrame(mt)};requestAnimationFrame(mt)};function At(z){const et=o.value.some(mt=>mt.path===z);function st(mt,Ot){et||mt.forEach(dt=>{dt.path===Ot?nr().handleTags("push",{path:Ot,meta:dt.meta,name:dt.name}):dt.children&&dt.children.length>0&&st(dt.children,Ot)})}st(s.options.routes,z)}function U(){const{fullPath:z,query:et}=E(e);s.replace({path:"/redirect"+z,query:et}),Ep(e,"refresh")}function j(z,et,st){var ut,qt,ms,Hm;const mt=o.value.findIndex(Ps=>{if(Ps.query){if(Ps.path===z.path)return Ps.query===z.query}else if(Ps.params){if(Ps.path===z.path)return Ps.params===z.params}else return Ps.path===z.path}),Ot=(Ps,Xv,Yv)=>{Yv?nr().handleTags("equal",[G==="false"?u1[0]:Zn(cu()),z]):nr().handleTags("splice","",{startIndex:Ps,length:Xv}),kt()};st==="other"?Ot(1,1,!0):st==="left"?Ot(1,mt-1):st==="right"?Ot(mt+1,o.value.length):Ot(mt,1);const dt=nr().handleTags("slice");if(et===e.path){if(st==="left")return;(ut=dt[0])!=null&&ut.query?s.push({name:dt[0].name,query:dt[0].query}):(qt=dt[0])!=null&&qt.params?s.push({name:dt[0].name,params:dt[0].params}):s.push({path:dt[0].path})}else{if(!o.value.length||o.value.some(Ps=>Ps.path===e.path))return;(ms=dt[0])!=null&&ms.query?s.push({name:dt[0].name,query:dt[0].query}):(Hm=dt[0])!=null&&Hm.params?s.push({name:dt[0].name,params:dt[0].params}):s.push({path:dt[0].path})}}function Y(z,et){j(z,z.path,et),Ep(e)}function N(z,et,st){if(et&&et.disabled)return;let mt;switch(st?mt={path:st.path,meta:st.meta,name:st.name,query:st==null?void 0:st.query,params:st==null?void 0:st.params}:mt={path:e.path,meta:e.meta},z){case 0:U();break;case 1:Y(mt);break;case 2:Y(mt,"left");break;case 3:Y(mt,"right");break;case 4:Y(mt,"other");break;case 5:nr().handleTags("splice","",{startIndex:1,length:o.value.length}),s.push(B),Ep(e);break;case 6:L(),setTimeout(()=>{g.hiddenSideBar?(l[6].icon=c1,l[6].text="内容区退出全屏"):(l[6].icon=d1,l[6].text="内容区全屏")},100);break}setTimeout(()=>{Wt(e.fullPath,e.query)})}function V(z){const{key:et,item:st}=z;N(et,st)}function Nt(z,et){x(),N(z,et,C.value)}function ee(z){Array.of(1,2,3,4,5).forEach(et=>{l[et].show=z})}function Vt(z){Array.of(1,2,3,4,5).forEach(et=>{l[et].disabled=z})}function Wt(z,et={},st=!1){const mt=o.value,Ot=o.value.length;let dt=-1;Ia(et)?dt=mt.findIndex(ut=>ut.path===z):dt=mt.findIndex(ut=>xp(ut.query,et)),ee(!0),st&&(l[0].show=!0),dt===1&&Ot!==2?(l[2].show=!1,Array.of(1,3,4,5).forEach(ut=>{l[ut].disabled=!1}),l[2].disabled=!0):dt===1&&Ot===2?(Vt(!1),Array.of(2,3,4).forEach(ut=>{l[ut].show=!1,l[ut].disabled=!0})):Ot-1===dt&&dt!==0?(l[3].show=!1,Array.of(1,2,4,5).forEach(ut=>{l[ut].disabled=!1}),l[3].disabled=!0):dt===0||z===`/redirect${B}`?Vt(!0):Vt(!1)}function gs(z,et){x(),z.path===B?(ee(!1),l[0].show=!0):e.path!==z.path&&e.name!==z.name?(l[0].show=!1,Wt(z.path,z.query)):o.value.length===2&&e.path!==z.path?(ee(!0),l[4].show=!1):e.path===z.path&&Wt(z.path,z.query,!0),C.value=z;const st=140,mt=E(it).getBoundingClientRect().left,dt=E(it).offsetWidth-st,ut=et.clientX-mt+5;ut>dt?c.value=dt:c.value=ut,R0().hiddenSideBar,h.value=et.clientY,ka(()=>{i.value=!0})}function Ys(z){const{name:et,path:st}=z;et?z.query?s.push({name:et,query:z.query}):z.params?s.push({name:et,params:z.params}):s.push({path:st}):s.push({path:st})}return x0(J,x,{detectIframe:!0}),Is(e,()=>{b.value=-1,kt()}),k(()=>{a&&(Wt(e.fullPath),Me.on("tagViewsChange",z=>{E(n)!==z&&(n.value=z)}),Me.on("tagViewsShowModel",z=>{u.value=z}),Me.on("changLayoutRoute",z=>{At(z),setTimeout(()=>{Wt(z)})}),dp(at,kt),A0().then(()=>kt()))}),lm(()=>{Me.off("tagViewsChange"),Me.off("tagViewsShowModel"),Me.off("changLayoutRoute")}),(z,et)=>{const st=Q("IconifyIconOffline"),mt=Q("el-dropdown-item"),Ot=Q("el-dropdown-menu"),dt=Q("el-dropdown");return E(n)?ct("",!0):(P(),$("div",{key:0,ref_key:"containerDom",ref:it,class:"tags-view"},[Zt(D("span",Xw,[R(st,{icon:E(am),onClick:et[0]||(et[0]=ut=>ge(200))},null,8,["icon"])],512),[[rn,tt.value]]),D("div",{ref_key:"scrollbarDom",ref:at,class:"scroll-container",onWheel:mi(xe,["prevent"])},[D("div",{ref_key:"tabDom",ref:F,class:"tab select-none",style:De(E(v))},[(P(!0),$(Le,null,ks(E(o),(ut,qt)=>(P(),$("div",{ref_for:!0,ref:"dynamic"+qt,key:qt,class:Ft(["scroll-item is-closable",E(A)(ut)]),onContextmenu:mi(ms=>gs(ut,ms),["prevent"]),onMouseenter:mi(ms=>E(I)(qt),["prevent"]),onMouseleave:mi(ms=>E(M)(qt),["prevent"]),onClick:ms=>Ys(ut)},[D("span",Kw,Jt(ut.meta.title),1),E(w)(ut,qt)||qt===E(b)&&qt!==0?(P(),$("span",{key:0,class:"el-icon-close",onClick:mi(ms=>Y(ut),["stop"])},[R(st,{icon:E(t)},null,8,["icon"])],8,Zw)):ct("",!0),E(u)!=="card"?(P(),$("span",{key:1,ref_for:!0,ref:"schedule"+qt,class:Ft([E(S)(ut)])},null,2)):ct("",!0)],42,Yw))),128))],4)],544),Zt(D("span",Qw,[R(st,{icon:E(rm),onClick:et[1]||(et[1]=ut=>ge(-200))},null,8,["icon"])],512),[[rn,tt.value]]),R(hm,{name:"el-zoom-in-top"},{default:q(()=>[Zt((P(),$("ul",{ref_key:"contextmenuRef",ref:J,key:Math.random(),style:De(E(T)),class:"contextmenu"},[(P(!0),$(Le,null,ks(E(l).slice(0,6),(ut,qt)=>(P(),$("div",{key:qt,style:{display:"flex","align-items":"center"}},[ut.show?(P(),$("li",{key:0,onClick:ms=>Nt(qt,ut)},[R(st,{icon:ut.icon},null,8,["icon"]),Tt(" "+Jt(ut.text),1)],8,Jw)):ct("",!0)]))),128))],4)),[[rn,E(i)]])]),_:1}),R(dt,{trigger:"click",placement:"bottom-end",onCommand:V},{dropdown:q(()=>[R(Ot,null,{default:q(()=>[(P(!0),$(Le,null,ks(E(l),(ut,qt)=>(P(),lt(mt,{key:qt,command:{key:qt,item:ut},divided:ut.divided,disabled:ut.disabled},{default:q(()=>[R(st,{icon:ut.icon},null,8,["icon"]),Tt(" "+Jt(ut.text),1)]),_:2},1032,["command","divided","disabled"]))),128))]),_:1})]),default:q(()=>[D("span",tA,[R(st,{icon:E(Ww),class:"dark:text-white"},null,8,["icon"])])]),_:1})],512))}}}),zv=ae(eA,[["__scopeId","data-v-056ac4d1"]]),sA=d=>(cm("data-v-9e18fb91"),d=d(),dm(),d),iA={class:"navbar bg-[#fff] shadow-sm shadow-[rgba(0,21,41,0.08)]"},nA={key:2,class:"vertical-header-right"},rA={class:"nav-but-wrap"},aA=sA(()=>D("div",{class:"ques-icon"},"?",-1)),oA={class:"el-dropdown-link navbar-bg-hover select-none"},lA={class:"user-text-box"},hA={key:0,class:"dark:text-white user-name-text"},cA={key:0},dA={key:1,class:"dark:text-white"},uA=_t({__name:"navbar",setup(d){const{layout:t,device:e,logout:s,changRolesFn:i,onPanel:n,pureApp:a,username:o,name:l,userAvatar:h,avatarsStyle:c,toggleSideBar:u}=fs(),{bool:f,toggle:g,setFalse:b}=up(),v=nt(!1),_=rt(()=>{var L;return(L=f1())==null?void 0:L.roles});Ws(()=>{sessionStorage.getItem("roleInfo")?v.value=!0:v.value=!1,_.value[0]==1||_.value[0]==2});const w=nt(!1);function A(){w.value=!0}function C(){g()}function S(){w.value=!1}function T(){b()}nt(0);const x=nt(),k=()=>{p1().lock()},I=nt(!1);function M(){I.value=!0}return(L,F)=>{const it=Q("el-check-tag"),at=Q("Lock"),J=Q("el-icon"),tt=Q("IconifyIconOffline"),B=Q("el-dropdown-item"),G=Q("el-dropdown-menu"),kt=Q("el-dropdown");return P(),$("div",iA,[E(e)==="mobile"?(P(),lt(jw,{key:0,class:"hamburger-container","is-active":E(a).sidebar.opened,onToggleClick:E(u)},null,8,["is-active","onToggleClick"])):ct("",!0),E(t)==="mix"?(P(),lt(Wy,{key:1})):ct("",!0),E(t)==="vertical"?(P(),$("div",nA,[R(zv,{style:{"flex-grow":"1","min-width":"0","margin-right":"10px"}}),D("div",rA,[D("div",{class:"navbar-bg-hover help-center",onClick:M},[R(it,{checked:""},{default:q(()=>[aA,Tt("帮助中心 ")]),_:1})]),D("div",{class:"fullscreen-icon navbar-bg-hover",onClick:k},[R(J,null,{default:q(()=>[R(at)]),_:1})]),R(pm,{id:"header-search"}),R(mm,{id:"full-screen"}),R(kt,{trigger:"click"},{dropdown:q(()=>[R(G,{class:"logout"},{default:q(()=>[v.value?(P(),lt(B,{key:0,onClick:A},{default:q(()=>[R(tt,{icon:E(bm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 切换角色 ")]),_:1})):ct("",!0),R(B,{onClick:C},{default:q(()=>[R(tt,{icon:E(ym),style:{margin:"5px"}},null,8,["icon"]),Tt(" 修改密码 ")]),_:1}),R(B,{onClick:E(s)},{default:q(()=>[R(tt,{icon:E(vm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 退出系统 ")]),_:1},8,["onClick"])]),_:1})]),default:q(()=>[D("div",oA,[D("div",lA,[E(l)?(P(),$("p",hA,[Tt(Jt(E(l)),1),E(Ts)().getItem(E(Pa)).user_type===2?(P(),$("span",cA,"（AI）")):ct("",!0)])):ct("",!0),E(o)?(P(),$("p",dA)):ct("",!0)])])]),_:1}),D("span",{class:"set-icon navbar-bg-hover",title:"打开项目配置",onClick:F[0]||(F[0]=(...Bt)=>E(n)&&E(n)(...Bt))},[R(tt,{icon:E(_m)},null,8,["icon"])])])])):ct("",!0),R(um,{show:w.value,onCloseChangeRole:S},null,8,["show"]),R(gm,{show:E(f),onCloseChangePassword:T},null,8,["show"]),R(R1,{ref_key:"serviceRef",ref:x},null,512),R(zw,{modelValue:I.value,"onUpdate:modelValue":F[1]||(F[1]=Bt=>I.value=Bt)},null,8,["modelValue"])])}}}),m0=ae(uA,[["__scopeId","data-v-9e18fb91"]]),Dl=new Map,fA=()=>{function d(s,i){Dl.set(s,i)}function t(s){return s?Dl.get(s):[...Dl.entries()]}function e(s){Dl.delete(s)}return{setMap:d,getMap:t,delMap:e,MAP:Dl}},pA={class:"w-full h-full"},gA=_t({__name:"index",props:{currRoute:{},currComp:{}},setup(d){const t=d,e=Np([]),{setMap:s,getMap:i,MAP:n,delMap:a}=fA(),o=rt(()=>{var h,c;return th().KeepAlive&&((h=t.currRoute.meta)==null?void 0:h.keepAlive)&&!!((c=t.currRoute.meta)!=null&&c.frameSrc)}),l=rt(()=>!o.value&&t.currComp);return Is(nr().multiTags,h=>{var f;const c=jm().getPageInfo;if(c&&((f=Object.keys(c))!=null&&f.length)&&Array.isArray(h)){const g=h.map(b=>b.path);for(let b in c)g.includes(b)||jm().handlePageInfo("splice",b)}if(!Array.isArray(h)||!o.value)return;if(h.filter(g=>{var b;return(b=g.meta)==null?void 0:b.frameSrc}).length<n.size)for(const g of n.keys())h.some(b=>b.path===g)||(a(g),e.value=i())}),Is(()=>t.currRoute.fullPath,h=>{const u=nr().multiTags.filter(f=>{var g;return(g=f.meta)==null?void 0:g.frameSrc});o.value&&u.length!==n.size&&([...n.keys()].find(g=>h===g)||s(h,t.currComp)),n.size>0&&(e.value=i())},{immediate:!0}),(h,c)=>(P(),$(Le,null,[(P(!0),$(Le,null,ks(e.value,([u,f])=>{var g;return Zt((P(),$("div",{key:u,class:"w-full h-full"},[eh(h.$slots,"default",{fullPath:u,Comp:f,frameInfo:{frameSrc:(g=h.currRoute.meta)==null?void 0:g.frameSrc,fullPath:u}})],512)),[[rn,u===h.currRoute.fullPath]])}),128)),Zt(D("div",pA,[eh(h.$slots,"default",{Comp:l.value,fullPath:h.currRoute.fullPath})],512),[[rn,!o.value]])],64))}}),mA={class:"layout-footer text-[rgba(0,0,0,0.6)] dark:text-[rgba(220,220,242,0.8)]"},bA=_t({__name:"index",setup(d){const t=th("Title"),e=th("Version");return(s,i)=>(P(),$("footer",mA," Copyright © 2024  "+Jt(E(t))+Jt(E(e)),1))}}),b0=ae(bA,[["__scopeId","data-v-78ffe3bb"]]),vA={xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24"},yA=D("path",{fill:"none",d:"M0 0h24v24H0z"},null,-1),_A=D("path",{d:"M2.88 18.054a35.9 35.9 0 0 1 8.531-16.32.8.8 0 0 1 1.178 0q.25.27.413.455a35.9 35.9 0 0 1 8.118 15.865c-2.141.451-4.34.747-6.584.874l-2.089 4.178a.5.5 0 0 1-.894 0l-2.089-4.178a44 44 0 0 1-6.584-.874m6.698-1.123 1.157.066L12 19.527l1.265-2.53 1.157-.066a42 42 0 0 0 4.227-.454A33.9 33.9 0 0 0 12 4.09a33.9 33.9 0 0 0-6.649 12.387q2.093.334 4.227.454M12 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6m0-2a1 1 0 1 0 0-2 1 1 0 0 0 0 2"},null,-1),wA=[yA,_A];function AA(d,t){return P(),$("svg",vA,[...wA])}const Uv={render:AA},SA={class:"grow"},CA={key:1,class:"grow"},xA=_t({__name:"appMain",props:{fixedHeader:Boolean},setup(d){const t=d,{showModel:e}=P0(),{$storage:s,$config:i}=bd();rt(()=>i==null?void 0:i.KeepAlive);const n=rt(()=>g=>g.meta.transition),a=rt(()=>s==null?void 0:s.configure.hideTabs),o=rt(()=>s==null?void 0:s.configure.hideFooter),l=rt(()=>s==null?void 0:s.configure.stretch),h=rt(()=>(s==null?void 0:s.layout.layout)==="vertical"),c=rt(()=>Op(l.value)?l.value+"px":l.value?"1440px":"100%"),u=rt(()=>[a.value&&h?"padding-top: 34px;":"",!a.value&&h?e.value=="chrome"?"padding-top: 38px;":"padding-top: 34px;":"",a.value&&!h.value?"padding-top: 34px;":"",!a.value&&!h.value?e.value=="chrome"?"padding-top: 71px;":"padding-top: 67px;":"",t.fixedHeader?"":`padding-top: 0;${a.value?"min-height: calc(100vh - 34px);":"min-height: calc(100vh - 39px);"}`]),f=_t({props:{route:{type:void 0,required:!0}},render(){var _,w,A;const g=((_=n.value(this.route))==null?void 0:_.name)||"fade-transform",b=(w=n.value(this.route))==null?void 0:w.enterTransition,v=(A=n.value(this.route))==null?void 0:A.leaveTransition;return rr(hm,{name:b?"pure-classes-transition":g,enterActiveClass:b?`animate__animated ${b}`:void 0,leaveActiveClass:v?`animate__animated ${v}`:void 0,mode:"out-in",appear:!0},{default:()=>[this.$slots.default()]})}});return rt(()=>Ze().cachePageList),(g,b)=>{const v=Q("el-backtop"),_=Q("el-scrollbar"),w=Q("router-view");return P(),$("section",{class:Ft([d.fixedHeader?"app-main":"app-main-nofixed-header"]),style:De(u.value)},[R(w,null,{default:q(({Component:A,route:C})=>[R(gA,{currComp:A,currRoute:C},{default:q(({Comp:S,fullPath:T,frameInfo:x})=>[d.fixedHeader?(P(),lt(_,{key:0,"wrap-style":{display:"flex","flex-wrap":"wrap","max-width":c.value,margin:"0 auto",transition:"all 300ms cubic-bezier(0.4, 0, 0.2, 1)"},"view-style":{display:"flex",flex:"auto",overflow:"hidden","flex-direction":"column"}},{default:q(()=>[R(v,{title:"回到顶部",target:".app-main .el-scrollbar__wrap"},{default:q(()=>[R(E(Uv))]),_:1}),D("div",SA,[R(E(f),{route:C},{default:q(()=>[(P(),lt(Gm,{include:E(Ze)().cachePageList,max:10},[(P(),lt(Il(S),{key:C.name,frameInfo:x,class:"main-content"},null,8,["frameInfo"]))],1032,["include"]))]),_:2},1032,["route"])]),o.value?ct("",!0):(P(),lt(b0,{key:0}))]),_:2},1032,["wrap-style"])):(P(),$("div",CA,[R(E(f),{route:C},{default:q(()=>[(P(),lt(Gm,{include:E(Ze)().cachePageList,max:10},[(P(),lt(Il(S),{key:C.name,frameInfo:x,class:"main-content"},null,8,["frameInfo"]))],1032,["include"]))]),_:2},1032,["route"])]))]),_:2},1032,["currComp","currRoute"])]),_:1}),!o.value&&!d.fixedHeader?(P(),lt(b0,{key:0})):ct("",!0)],6)}}}),v0=ae(xA,[["__scopeId","data-v-78f76c41"]]),Gv=d=>(cm("data-v-8850bc96"),d=d(),dm(),d),EA=Gv(()=>D("div",{class:"right-panel-background"},null,-1)),TA={class:"project-configuration border-b-[1px] border-solid border-[var(--pure-border-color)]"},kA=Gv(()=>D("h4",{class:"dark:text-white"},"项目配置",-1)),IA={class:"flex justify-end p-3 border-t-[1px] border-solid border-[var(--pure-border-color)]"},PA=_t({__name:"index",setup(d){const t=nt(null),e=nt(!1),s=rt(()=>["w-[22px]","h-[22px]","flex","justify-center","items-center","outline-none","rounded-[4px]","cursor-pointer","transition-colors","hover:bg-[#0000000f]","dark:hover:bg-[#ffffff1f]","dark:hover:text-[#ffffffd9]"]),{onReset:i}=fm();return x0(t,n=>{n.clientX>t.value.offsetLeft||(e.value=!1)}),Ws(()=>{Me.on("openPanel",()=>{e.value=!0})}),lm(()=>{Me.off("openPanel")}),(n,a)=>{const o=Q("IconifyIconOffline"),l=Q("el-scrollbar"),h=Q("el-button"),c=Qn("tippy");return P(),$("div",{class:Ft({show:e.value})},[EA,D("div",{ref_key:"target",ref:t,class:"right-panel bg-bg_color"},[D("div",TA,[kA,Zt((P(),$("span",{class:Ft(s.value)},[R(o,{class:"dark:text-white",width:"18px",height:"18px",icon:E(I0),onClick:a[0]||(a[0]=u=>e.value=!e.value)},null,8,["icon"])],2)),[[c,{content:"关闭配置",placement:"bottom-start",zIndex:41e3}]])]),R(l,null,{default:q(()=>[eh(n.$slots,"default",{},void 0,!0)]),_:3}),D("div",IA,[Zt((P(),lt(h,{type:"danger",text:"",bg:"",onClick:E(i)},{default:q(()=>[Tt(" 清空缓存 ")]),_:1},8,["onClick"])),[[c,{content:"清空缓存并返回登录页",placement:"left-start",zIndex:41e3}]])])],512)],2)}}}),RA=ae(PA,[["__scopeId","data-v-8850bc96"]]),MA=g1({id:"zf-common",state:()=>({menuChangeFlag:!1}),getters:{getMenuChangeFlag(d){return d.menuChangeFlag}},actions:{setMenuChangeFlag(d){this.menuChangeFlag=d}}});function LA(){return MA(m1)}const DA={width:1024,height:1024,body:'<path fill="currentColor" d="M406.656 706.944L195.84 496.256a32 32 0 1 0-45.248 45.248l256 256l512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"/>'},FA={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24"},NA=D("path",{fill:"none",d:"M0 0h24v24H0z"},null,-1),OA=D("path",{d:"M12 18a6 6 0 1 1 0-12 6 6 0 0 1 0 12M11 1h2v3h-2zm0 19h2v3h-2zM3.515 4.929l1.414-1.414L7.05 5.636 5.636 7.05zM16.95 18.364l1.414-1.414 2.121 2.121-1.414 1.414zm2.121-14.85 1.414 1.415-2.121 2.121-1.414-1.414 2.121-2.121zM5.636 16.95l1.414 1.414-2.121 2.121-1.414-1.414zM23 11v2h-3v-2zM4 11v2H1v-2z"},null,-1),$A=[NA,OA];function HA(d,t){return P(),$("svg",FA,[...$A])}const BA={render:HA},VA={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24"},zA=D("path",{fill:"none",d:"M0 0h24v24H0z"},null,-1),UA=D("path",{d:"M11.38 2.019a7.5 7.5 0 1 0 10.6 10.6C21.662 17.854 17.316 22 12.001 22 6.477 22 2 17.523 2 12c0-5.315 4.146-9.661 9.38-9.981"},null,-1),GA=[zA,UA];function jA(d,t){return P(),$("svg",VA,[...GA])}const WA={render:jA},qA={xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 24 24"},XA=D("path",{d:"m10.13 15.842-.787 2.94-1.932-.517.787-2.94a11 11 0 0 1-3.237-1.871l-2.153 2.153-1.414-1.414 2.153-2.154a10.96 10.96 0 0 1-2.371-5.07l.9-.164A16.92 16.92 0 0 0 12 10c3.704 0 7.132-1.184 9.924-3.195l.9.163a10.96 10.96 0 0 1-2.37 5.071l2.153 2.154-1.414 1.414-2.154-2.153a11 11 0 0 1-3.237 1.872l.788 2.939-1.932.517-.788-2.94a11.1 11.1 0 0 1-3.74 0"},null,-1),YA=[XA];function KA(d,t){return P(),$("svg",qA,[...YA])}const ZA={render:KA},QA={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",class:"icon",viewBox:"0 0 1024 1024"},JA=D("path",{d:"M554 849.574c0 23.365-18.635 42.307-42 42.307s-42-18.941-42-42.307V662.719c0-23.365 18.635-42.307 42-42.307v-7.051c23.365 0 42 25.993 42 49.358z"},null,-1),tS=D("path",{d:"M893 888.5c0 17.397-14.103 31.5-31.5 31.5h-700c-17.397 0-31.5-14.103-31.5-31.5s14.103-31.5 31.5-31.5h700c17.397 0 31.5 14.103 31.5 31.5m33-714.074C926 135.484 894.686 105 855.744 105H168.256C129.314 105 98 135.484 98 174.426V533h828zM98 630.988C98 669.931 129.314 702 168.256 702h687.488C894.686 702 926 669.931 926 630.988V596H98z"},null,-1),eS=[JA,tS];function sS(d,t){return P(),$("svg",QA,[...eS])}const iS={render:sS},ps=d=>(cm("data-v-6ba2cc85"),d=d(),dm(),d),nS={class:"p-5"},rS={class:"theme-color"},aS=["onClick"],oS={class:"pure-theme"},lS=ps(()=>D("div",null,null,-1)),hS=ps(()=>D("div",null,null,-1)),cS=[lS,hS],dS=ps(()=>D("div",null,null,-1)),uS=ps(()=>D("div",null,null,-1)),fS=[dS,uS],pS=ps(()=>D("div",null,null,-1)),gS=ps(()=>D("div",null,null,-1)),mS=[pS,gS],bS={key:0},vS=ps(()=>D("div",{class:"flex-grow border-b border-dashed",style:{"border-color":"var(--el-color-primary)"}},null,-1)),yS=ps(()=>D("p",{class:"mt-5 font-medium text-sm dark:text-white"},"界面显示",-1)),_S={class:"setting"},wS=ps(()=>D("span",{class:"dark:text-white"},"灰色模式",-1)),AS=ps(()=>D("span",{class:"dark:text-white"},"色弱模式",-1)),SS=ps(()=>D("span",{class:"dark:text-white"},"隐藏标签页",-1)),CS=ps(()=>D("span",{class:"dark:text-white"},"隐藏页脚",-1)),xS=ps(()=>D("span",{class:"dark:text-white"},"Logo",-1)),ES=_t({__name:"index",setup(d){var At,U,j,Y;const{device:t}=fs(),{isDark:e}=E0(),{$storage:s}=bd(),i=nt(),n=nt(),a=nt(),{dataTheme:o,overallStyle:l,layoutTheme:h,themeColors:c,toggleClass:u,dataThemeChange:f,setLayoutThemeColor:g}=fm();if(E(h)){const N=E(h).layout,V=E(h).theme;I1({scopeName:`layout-theme-${V}`}),kt(N)}const b=nt((U=(At=s.configure)==null?void 0:At.showModel)!=null?U:"smart"),v=nt((Y=(j=s.configure)==null?void 0:j.showLogo)!=null?Y:!0),_=om({greyVal:s.configure.grey,weakVal:s.configure.weak,eyeVal:s.configure.eye,tabsVal:s.configure.hideTabs,showLogo:s.configure.showLogo,showModel:s.configure.showModel,hideFooter:s.configure.hideFooter,multiTagsCache:s.configure.multiTagsCache,stretch:s.configure.stretch}),w=rt(()=>N=>({background:N})),A=rt(()=>N=>!(N==="light"&&e.value));function C(N,V){const Nt=s.configure;Nt[N]=V,s.configure=Nt}const S=N=>{const V=document.querySelector("html");u(_.greyVal,"html-grey",V),C("grey",N)},T=N=>{const V=document.querySelector("html");u(_.weakVal,"html-weakness",V),C("weak",N)},x=()=>{const N=_.tabsVal;C("hideTabs",N),Me.emit("tagViewsChange",N),Me.emit("hideTagChange",N)},k=()=>{const N=_.hideFooter;C("hideFooter",N),Me.emit("hideFooterChange",N)};function I({option:N}){const{value:V}=N;b.value=V,C("showModel",V),Me.emit("tagViewsShowModel",V)}function M(){E(v)?C("showLogo",!0):C("showLogo",!1),Me.emit("logoChange",E(v))}function L(N){N.forEach(V=>{u(!1,"is-select",E(V))})}const F=[{label:"固定",tip:"紧凑页面，轻松找到所需信息",value:"fixed"},{label:"自定义",tip:"最小1280、最大1600",value:"custom"}],it=N=>{_.stretch=N,C("stretch",N)},at=({option:N})=>{const{value:V}=N;it(V==="custom"?1440:!1)},J=rt(()=>N=>N===h.value.theme&&h.value.theme!=="light"?"#fff":N===h.value.theme&&h.value.theme==="light"?"#1d2b45":"transparent"),tt=rt(()=>["mb-[12px]","font-medium","text-sm","dark:text-white"]),B=rt(()=>[{label:"浅色",icon:BA,theme:"light",tip:"清新启航，点亮舒适的工作界面",iconAttrs:{fill:e.value?"#fff":"#000"}},{label:"深色",icon:WA,theme:"dark",tip:"月光序曲，沉醉于夜的静谧雅致",iconAttrs:{fill:e.value?"#fff":"#000"}},{label:"护眼",icon:ZA,theme:"eye",tip:"柔和色彩，专注于舒缓细腻时光",iconAttrs:{fill:e.value?"#fff":"#000"}},{label:"自动",icon:iS,theme:"system",tip:"同步时光，界面随晨昏自然呼应",iconAttrs:{fill:e.value?"#fff":"#000"}}]),G=[{label:"灵动",tip:"灵动标签，添趣生辉",value:"smart"},{label:"卡片",tip:"卡片标签，高效浏览",value:"card"}];function kt(N){var Nt,ee,Vt,Wt,gs;LA().setMenuChangeFlag(!0);let V={type:"layoutChange"};window.postMessage(V,origin),h.value.layout=N,window.document.body.setAttribute("layout",N),s.layout={layout:N,theme:h.value.theme,darkMode:(Nt=s.layout)==null?void 0:Nt.darkMode,sidebarStatus:(ee=s.layout)==null?void 0:ee.sidebarStatus,epThemeColor:(Vt=s.layout)==null?void 0:Vt.epThemeColor,themeColor:(Wt=s.layout)==null?void 0:Wt.themeColor,overallStyle:(gs=s.layout)==null?void 0:gs.overallStyle},Ci().setLayout(N),Me.emit("setLayoutModel",N)}Is(s,({layout:N})=>{switch(N.layout){case"vertical":u(!0,"is-select",E(n)),Na(L([a]),50),Na(L([i]),50);break;case"horizontal":u(!0,"is-select",E(a)),Na(L([n]),50),Na(L([i]),50);break;case"mix":u(!0,"is-select",E(i)),Na(L([n]),50),Na(L([a]),50);break}});const Bt=window.matchMedia("(prefers-color-scheme: dark)");function ge(){l.value==="system"&&(Bt.matches?o.value=!0:o.value=!1,f(l.value))}function xe(){Bt.removeEventListener("change",ge)}function Et(){ge(),xe(),Bt.addEventListener("change",ge)}return T0(()=>{ka(()=>{var N,V;Et(),_.greyVal&&((N=document.querySelector("html"))==null||N.classList.add("html-grey")),_.weakVal&&((V=document.querySelector("html"))==null||V.classList.add("html-weakness")),_.tabsVal&&x(),_.hideFooter&&k()})}),b1(()=>xe),(N,V)=>{const Nt=Q("IconifyIconOffline"),ee=Q("el-icon"),Vt=Q("el-input-number"),Wt=Q("el-switch"),gs=Qn("tippy"),Ys=Qn("ripple");return P(),lt(RA,null,{default:q(()=>[D("div",nS,[D("p",{class:Ft(tt.value)},"整体风格",2),R(E(Tp),{class:"select-none",modelValue:E(l)==="system"?3:E(l)==="eye"?2:E(o)?1:0,options:B.value,onChange:V[0]||(V[0]=yt=>{yt.index===1&&yt.index!==3?o.value=!0:o.value=!1,l.value=yt.option.theme,E(f)(yt.option.theme),yt.index===2&&Et()})},null,8,["modelValue","options"]),D("p",{class:Ft(["mt-5",tt.value])},"主题色",2),D("ul",rS,[(P(!0),$(Le,null,ks(E(c),(yt,z)=>Zt((P(),$("li",{key:z,style:De(w.value(yt.color)),onClick:et=>E(g)(yt.themeColor)},[R(ee,{style:{margin:"0.1em 0.1em 0 0"},size:17,color:J.value(yt.themeColor)},{default:q(()=>[R(Nt,{icon:E(DA)},null,8,["icon"])]),_:2},1032,["color"])],12,aS)),[[rn,A.value(yt.themeColor)]])),128))]),D("p",{class:Ft(["mt-5",tt.value])},"导航模式",2),D("ul",oS,[Zt((P(),$("li",{ref_key:"verticalRef",ref:n,class:Ft(E(h).layout==="vertical"?"is-select":""),onClick:V[1]||(V[1]=yt=>kt("vertical"))},cS,2)),[[gs,{content:"左侧菜单，亲切熟悉",zIndex:41e3}]]),E(t)!=="mobile"?Zt((P(),$("li",{key:0,ref_key:"horizontalRef",ref:a,class:Ft(E(h).layout==="horizontal"?"is-select":""),onClick:V[2]||(V[2]=yt=>kt("horizontal"))},fS,2)),[[gs,{content:"顶部菜单，简洁概览",zIndex:41e3}]]):ct("",!0),E(t)!=="mobile"?Zt((P(),$("li",{key:1,ref_key:"mixRef",ref:i,class:Ft(E(h).layout==="mix"?"is-select":""),onClick:V[3]||(V[3]=yt=>kt("mix"))},mS,2)),[[gs,{content:"混合菜单，灵活多变",zIndex:41e3}]]):ct("",!0)]),E(Ci)().getViewportWidth>1280?(P(),$("span",bS,[D("p",{class:Ft(["mt-5",tt.value])},"页宽",2),R(E(Tp),{class:"mb-2 select-none",modelValue:E(Op)(_.stretch)?1:0,options:F,onChange:at},null,8,["modelValue"]),E(Op)(_.stretch)?(P(),lt(Vt,{key:0,modelValue:_.stretch,"onUpdate:modelValue":V[4]||(V[4]=yt=>_.stretch=yt),min:1280,max:1600,"controls-position":"right",onChange:V[5]||(V[5]=yt=>it(yt))},null,8,["modelValue"])):Zt((P(),$("button",{key:1,class:"bg-transparent flex-c w-full h-20 rounded-md border border-[var(--pure-border-color)]",onClick:V[6]||(V[6]=yt=>it(!_.stretch))},[D("div",{class:Ft(["flex-bc transition-all duration-300",[_.stretch?"w-[24%]":"w-[50%]"]]),style:{color:"var(--el-color-primary)"}},[R(Nt,{icon:_.stretch?E(rm):E(am),height:"20"},null,8,["icon"]),vS,R(Nt,{icon:_.stretch?E(am):E(rm),height:"20"},null,8,["icon"])],2)])),[[Ys,{class:"text-gray-300"}]])])):ct("",!0),D("p",{class:Ft(["mt-4",tt.value])},"页签风格",2),R(E(Tp),{class:"select-none",modelValue:b.value==="smart"?0:1,options:G,onChange:I},null,8,["modelValue"]),yS,D("ul",_S,[D("li",null,[wS,R(Wt,{modelValue:_.greyVal,"onUpdate:modelValue":V[7]||(V[7]=yt=>_.greyVal=yt),"inline-prompt":"","active-text":"开","inactive-text":"关",onChange:S},null,8,["modelValue"])]),D("li",null,[AS,R(Wt,{modelValue:_.weakVal,"onUpdate:modelValue":V[8]||(V[8]=yt=>_.weakVal=yt),"inline-prompt":"","active-text":"开","inactive-text":"关",onChange:T},null,8,["modelValue"])]),D("li",null,[SS,R(Wt,{modelValue:_.tabsVal,"onUpdate:modelValue":V[9]||(V[9]=yt=>_.tabsVal=yt),"inline-prompt":"","active-text":"开","inactive-text":"关",onChange:x},null,8,["modelValue"])]),D("li",null,[CS,R(Wt,{modelValue:_.hideFooter,"onUpdate:modelValue":V[10]||(V[10]=yt=>_.hideFooter=yt),"inline-prompt":"","active-text":"开","inactive-text":"关",onChange:k},null,8,["modelValue"])]),D("li",null,[xS,R(Wt,{modelValue:v.value,"onUpdate:modelValue":V[11]||(V[11]=yt=>v.value=yt),"inline-prompt":"","active-value":!0,"inactive-value":!1,"active-text":"开","inactive-text":"关",onChange:M},null,8,["modelValue"])])])])]),_:1})}}}),TS=ae(ES,[["__scopeId","data-v-6ba2cc85"]]),kS=["src"],IS={class:"sidebar-title"},PS=["src"],RS={class:"sidebar-title"},MS=_t({__name:"logo",props:{collapse:Boolean},setup(d){const t=k0,e=d,{title:s}=fs();return(i,n)=>{const a=Q("router-link");return P(),$("div",{class:Ft(["sidebar-logo-container",{collapses:e.collapse}])},[R(hm,{name:"sidebarLogoFade"},{default:q(()=>{var o,l,h,c;return[e.collapse?(P(),lt(a,{key:"props.collapse",title:E(s),class:"sidebar-logo-link",to:(l=(o=E(cu)())==null?void 0:o.path)!=null?l:"/"},{default:q(()=>[D("img",{src:E(t),alt:"logo"},null,8,kS),D("span",IS,Jt(E(s)),1)]),_:1},8,["title","to"])):(P(),lt(a,{key:"expand",title:E(s),class:"sidebar-logo-link",to:(c=(h=E(cu)())==null?void 0:h.path)!=null?c:"/"},{default:q(()=>[D("img",{src:E(t),alt:"logo"},null,8,PS),D("span",RS,Jt(E(s)),1)]),_:1},8,["title","to"]))]}),_:1})],2)}}}),LS=ae(MS,[["__scopeId","data-v-1f765f60"]]);var nh={exports:{}},DS={},FS=process.platform==="win32",Xs=D1;function yp(d,t){for(var e=[],s=0;s<d.length;s++){var i=d[s];!i||i==="."||(i===".."?e.length&&e[e.length-1]!==".."?e.pop():t&&e.push(".."):e.push(i))}return e}function Ql(d){for(var t=d.length-1,e=0;e<=t&&!d[e];e++);for(var s=t;s>=0&&!d[s];s--);return e===0&&s===t?d:e>s?[]:d.slice(e,s+1)}var jv=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,NS=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,Gt={};function _p(d){var t=jv.exec(d),e=(t[1]||"")+(t[2]||""),s=t[3]||"",i=NS.exec(s),n=i[1],a=i[2],o=i[3];return[e,n,a,o]}function Lm(d){var t=jv.exec(d),e=t[1]||"",s=!!e&&e[1]!==":";return{device:e,isUnc:s,isAbsolute:s||!!t[2],tail:t[3]}}function Wv(d){return"\\\\"+d.replace(/^[\\\/]+/,"").replace(/[\\\/]+/g,"\\")}Gt.resolve=function(){for(var d="",t="",e=!1,s=arguments.length-1;s>=-1;s--){var i;if(s>=0?i=arguments[s]:d?(i=DS["="+d],(!i||i.substr(0,3).toLowerCase()!==d.toLowerCase()+"\\")&&(i=d+"\\")):i=process.cwd(),Xs.isString(i)){if(!i)continue}else throw new TypeError("Arguments to path.resolve must be strings");var n=Lm(i),a=n.device,o=n.isUnc,l=n.isAbsolute,h=n.tail;if(!(a&&d&&a.toLowerCase()!==d.toLowerCase())&&(d||(d=a),e||(t=h+"\\"+t,e=l),d&&e))break}return o&&(d=Wv(d)),t=yp(t.split(/[\\\/]+/),!e).join("\\"),d+(e?"\\":"")+t||"."};Gt.normalize=function(d){var t=Lm(d),e=t.device,s=t.isUnc,i=t.isAbsolute,n=t.tail,a=/[\\\/]$/.test(n);return n=yp(n.split(/[\\\/]+/),!i).join("\\"),!n&&!i&&(n="."),n&&a&&(n+="\\"),s&&(e=Wv(e)),e+(i?"\\":"")+n};Gt.isAbsolute=function(d){return Lm(d).isAbsolute};Gt.join=function(){for(var d=[],t=0;t<arguments.length;t++){var e=arguments[t];if(!Xs.isString(e))throw new TypeError("Arguments to path.join must be strings");e&&d.push(e)}var s=d.join("\\");return/^[\\\/]{2}[^\\\/]/.test(d[0])||(s=s.replace(/^[\\\/]{2,}/,"\\")),Gt.normalize(s)};Gt.relative=function(d,t){d=Gt.resolve(d),t=Gt.resolve(t);for(var e=d.toLowerCase(),s=t.toLowerCase(),i=Ql(t.split("\\")),n=Ql(e.split("\\")),a=Ql(s.split("\\")),o=Math.min(n.length,a.length),l=o,h=0;h<o;h++)if(n[h]!==a[h]){l=h;break}if(l==0)return t;for(var c=[],h=l;h<n.length;h++)c.push("..");return c=c.concat(i.slice(l)),c.join("\\")};Gt._makeLong=function(d){if(!Xs.isString(d))return d;if(!d)return"";var t=Gt.resolve(d);return/^[a-zA-Z]\:\\/.test(t)?"\\\\?\\"+t:/^\\\\[^?.]/.test(t)?"\\\\?\\UNC\\"+t.substring(2):d};Gt.dirname=function(d){var t=_p(d),e=t[0],s=t[1];return!e&&!s?".":(s&&(s=s.substr(0,s.length-1)),e+s)};Gt.basename=function(d,t){var e=_p(d)[2];return t&&e.substr(-1*t.length)===t&&(e=e.substr(0,e.length-t.length)),e};Gt.extname=function(d){return _p(d)[3]};Gt.format=function(d){if(!Xs.isObject(d))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof d);var t=d.root||"";if(!Xs.isString(t))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof d.root);var e=d.dir,s=d.base||"";return e?e[e.length-1]===Gt.sep?e+s:e+Gt.sep+s:s};Gt.parse=function(d){if(!Xs.isString(d))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof d);var t=_p(d);if(!t||t.length!==4)throw new TypeError("Invalid path '"+d+"'");return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};Gt.sep="\\";Gt.delimiter=";";var OS=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,te={};function wp(d){return OS.exec(d).slice(1)}te.resolve=function(){for(var d="",t=!1,e=arguments.length-1;e>=-1&&!t;e--){var s=e>=0?arguments[e]:process.cwd();if(Xs.isString(s)){if(!s)continue}else throw new TypeError("Arguments to path.resolve must be strings");d=s+"/"+d,t=s[0]==="/"}return d=yp(d.split("/"),!t).join("/"),(t?"/":"")+d||"."};te.normalize=function(d){var t=te.isAbsolute(d),e=d&&d[d.length-1]==="/";return d=yp(d.split("/"),!t).join("/"),!d&&!t&&(d="."),d&&e&&(d+="/"),(t?"/":"")+d};te.isAbsolute=function(d){return d.charAt(0)==="/"};te.join=function(){for(var d="",t=0;t<arguments.length;t++){var e=arguments[t];if(!Xs.isString(e))throw new TypeError("Arguments to path.join must be strings");e&&(d?d+="/"+e:d+=e)}return te.normalize(d)};te.relative=function(d,t){d=te.resolve(d).substr(1),t=te.resolve(t).substr(1);for(var e=Ql(d.split("/")),s=Ql(t.split("/")),i=Math.min(e.length,s.length),n=i,a=0;a<i;a++)if(e[a]!==s[a]){n=a;break}for(var o=[],a=n;a<e.length;a++)o.push("..");return o=o.concat(s.slice(n)),o.join("/")};te._makeLong=function(d){return d};te.dirname=function(d){var t=wp(d),e=t[0],s=t[1];return!e&&!s?".":(s&&(s=s.substr(0,s.length-1)),e+s)};te.basename=function(d,t){var e=wp(d)[2];return t&&e.substr(-1*t.length)===t&&(e=e.substr(0,e.length-t.length)),e};te.extname=function(d){return wp(d)[3]};te.format=function(d){if(!Xs.isObject(d))throw new TypeError("Parameter 'pathObject' must be an object, not "+typeof d);var t=d.root||"";if(!Xs.isString(t))throw new TypeError("'pathObject.root' must be a string or undefined, not "+typeof d.root);var e=d.dir?d.dir+te.sep:"",s=d.base||"";return e+s};te.parse=function(d){if(!Xs.isString(d))throw new TypeError("Parameter 'pathString' must be a string, not "+typeof d);var t=wp(d);if(!t||t.length!==4)throw new TypeError("Invalid path '"+d+"'");return t[1]=t[1]||"",t[2]=t[2]||"",t[3]=t[3]||"",{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};te.sep="/";te.delimiter=":";FS?nh.exports=Gt:nh.exports=te;nh.exports.posix=te;nh.exports.win32=Gt;var $S=nh.exports;const HS=v1($S),BS=_t({name:"LinkItem",__name:"linkItem",props:{to:{}},setup(d){const t=d,e=rt(()=>w1(t.to.name)),s=i=>e.value?{href:i.name,target:"_blank",rel:"noopener"}:{to:i};return(i,n)=>(P(),lt(Il(e.value?"a":"router-link"),y1(_1(s(i.to))),{default:q(()=>[eh(i.$slots,"default")]),_:3},16))}}),VS=_t({__name:"index",props:{lineClamp:{type:[String,Number]},tippyProps:{type:Object,default:()=>({})}},setup(d){const t=d,e=A1(),s=nt(),i=nt(),n=l=>t.lineClamp?l.scrollHeight>l.clientHeight:l.scrollWidth>l.clientWidth,a=()=>Ge({content:rr(e.content||e.default)},t.tippyProps);function o(l){n(l.target)?(i.value.setProps(a()),i.value.enable()):i.value.disable()}return Ws(()=>{var l;i.value=S1((l=s.value)==null?void 0:l.$el,a())}),(l,h)=>{const c=Q("el-text");return P(),lt(c,$p(Ge({truncated:!d.lineClamp,lineClamp:d.lineClamp},l.$attrs),{ref_key:"textRef",ref:s,onMouseover:mi(o,["self"])}),{default:q(()=>[eh(l.$slots,"default")]),_:3},16)}}}),y0=C1(VS),zS={width:1024,height:1024,body:'<path fill="currentColor" d="M104.704 685.248a64 64 0 0 0 90.496 0l316.8-316.8l316.8 316.8a64 64 0 0 0 90.496-90.496L557.248 232.704a64 64 0 0 0-90.496 0L104.704 594.752a64 64 0 0 0 0 90.496z"/>'},US={width:1024,height:1024,body:'<path fill="currentColor" d="M104.704 338.752a64 64 0 0 1 90.496 0l316.8 316.8l316.8-316.8a64 64 0 0 1 90.496 90.496L557.248 791.296a64 64 0 0 1-90.496 0L104.704 429.248a64 64 0 0 1 0-90.496z"/>'},GS={width:1024,height:1024,body:'<path fill="currentColor" d="M685.248 104.704a64 64 0 0 1 0 90.496L368.448 512l316.8 316.8a64 64 0 0 1-90.496 90.496L232.704 557.248a64 64 0 0 1 0-90.496l362.048-362.048a64 64 0 0 1 90.496 0z"/>'},jS={width:1024,height:1024,body:'<path fill="currentColor" d="M338.752 104.704a64 64 0 0 0 0 90.496l316.8 316.8l-316.8 316.8a64 64 0 0 0 90.496 90.496l362.048-362.048a64 64 0 0 0 0-90.496L429.248 104.704a64 64 0 0 0-90.496 0z"/>'},qv=_t({__name:"sidebarItem",props:{item:{type:Object},isNest:{type:Boolean,default:!1},basePath:{type:String,default:""}},setup(d){const t=x1(),{layout:e,isCollapse:s,tooltipEffect:i,getDivStyle:n}=fs(),a=d,o=rt(()=>({width:"100%",display:"flex",alignItems:"center"})),l=rt(()=>({display:"flex",justifyContent:"center",alignItems:"center",margin:e.value==="horizontal"?"0 5px 0 0":s.value?"0 auto":"0 5px 0 0"})),h=rt(()=>{var g;return(g=th())!=null&&g.MenuArrowIconNoTransition?{"expand-close-icon":hr(US),"expand-open-icon":hr(zS),"collapse-close-icon":hr(jS),"collapse-open-icon":hr(GS)}:""}),c=nt(null);function u(g=[],b){var _,w;const v=g.filter(A=>(c.value=A,!0));return(w=(_=v[0])==null?void 0:_.meta)!=null&&w.showParent?!1:v.length===1?!0:v.length===0?(c.value=vi(Ge({},b),{path:"",noShowingChildren:!0}),!0):!1}function f(g){const b=/^http(s?):\/\//;return b.test(g)||b.test(a.basePath)?g||a.basePath:HS.posix.resolve(a.basePath,g)}return(g,b)=>{const v=Q("IconifyIconOffline"),_=Q("el-text"),w=Q("el-menu-item"),A=Q("sidebar-item",!0),C=Q("el-sub-menu");return u(a.item.children,a.item)&&(!c.value.children||c.value.noShowingChildren)?(P(),lt(BS,{key:0,to:d.item},{default:q(()=>{var S;return[(S=d.item.meta)!=null&&S.onlyHiddenMenu?ct("",!0):(P(),lt(w,$p({key:0,index:f(c.value.path)||d.item.redirect,class:{"submenu-title-noDropdown":!d.isNest},style:o.value},E(t)),{title:q(()=>[D("div",{style:De(E(n))},[R(E(y0),{tippyProps:{offset:[0,-10],theme:E(i)},class:"!text-inherit"},{default:q(()=>[Tt(Jt(c.value.meta.title),1)]),_:1},8,["tippyProps"]),R(Bp,{extraIcon:c.value.meta.extraIcon},null,8,["extraIcon"])],4)]),default:q(()=>{var T,x,k,I,M;return[Zn(a.item.meta.icon)?(P(),$("div",{key:0,class:"sub-menu-icon",style:De(l.value)},[R(v,{icon:a.item.meta.icon},null,8,["icon"])],4)):ct("",!0),!((T=a.item)!=null&&T.meta.icon)&&E(s)&&E(e)==="vertical"&&((k=(x=a.item)==null?void 0:x.pathList)==null?void 0:k.length)===1||!c.value.meta.icon&&E(s)&&E(e)==="mix"&&((M=(I=a.item)==null?void 0:I.pathList)==null?void 0:M.length)===2?(P(),lt(_,{key:1,truncated:"",class:"!px-4 !text-inherit"},{default:q(()=>[Tt(Jt(c.value.meta.title),1)]),_:1})):ct("",!0)]}),_:1},16,["index","class","style"]))]}),_:1},8,["to"])):(P(),lt(C,$p({key:1,ref:"subMenu",teleported:"",index:f(a.item.path)},h.value),{title:q(()=>[Zn(a.item.meta.icon)?(P(),$("div",{key:0,style:De(l.value),class:"sub-menu-icon"},[R(v,{icon:a.item.meta.icon},null,8,["icon"])],4)):ct("",!0),E(e)==="vertical"&&E(s)&&Zn(a.item.meta.icon)&&a.item.parentId===null?ct("",!0):(P(),lt(E(y0),{key:1,tippyProps:{offset:[0,-10],theme:E(i)},class:Ft({"!text-inherit":!0,"!px-4":E(e)!=="horizontal"&&E(s)&&!Zn(a.item.meta.icon)&&a.item.parentId===null})},{default:q(()=>[Tt(Jt(a.item.meta.title),1)]),_:1},8,["tippyProps","class"])),E(s)?ct("",!0):(P(),lt(Bp,{key:2,extraIcon:a.item.meta.extraIcon},null,8,["extraIcon"]))]),default:q(()=>[(P(!0),$(Le,null,ks(a.item.children,S=>(P(),lt(A,{key:S.path,"is-nest":!0,item:S,"base-path":f(S.path),class:"nest-menu"},null,8,["item","base-path"]))),128))]),_:1},16,["index"]))}}}),WS={class:"left-collapse"},qS=_t({__name:"leftCollapse",props:{isActive:{type:Boolean,default:!1}},emits:["toggleClick"],setup(d,{emit:t}){const e=d,{tooltipEffect:s}=fs(),i=rt(()=>["ml-4","mb-1","w-[16px]","h-[16px]","inline-block","align-middle","cursor-pointer","duration-[100ms]"]),{$storage:n}=bd(),a=rt(()=>{var h;return(h=n.layout)==null?void 0:h.themeColor}),o=t,l=()=>{o("toggleClick")};return(h,c)=>{const u=Q("IconifyIconOffline"),f=Qn("tippy");return P(),$("div",WS,[Zt(R(u,{icon:E(Vv),class:Ft([i.value,a.value==="light"?"":"text-primary"]),style:De({transform:e.isActive?"none":"rotateY(180deg)"}),onClick:l},null,8,["icon","class","style"]),[[f,{content:e.isActive?"点击折叠":"点击展开",theme:E(s),hideOnClick:"toggle",placement:"right"}]])])}}}),XS=ae(qS,[["__scopeId","data-v-9c7792f7"]]),YS={width:24,height:24,body:'<path fill="currentColor" d="m4.836 12l6.207 6.207l1.414-1.414L7.664 12l4.793-4.793l-1.414-1.414L4.836 12Zm5.65 0l6.207 6.207l1.414-1.414L13.314 12l4.793-4.793l-1.414-1.414L10.486 12Z"/>'},KS=_t({__name:"centerCollapse",props:{isActive:{type:Boolean,default:!1}},emits:["toggleClick"],setup(d,{emit:t}){const e=d,{tooltipEffect:s}=fs(),i=rt(()=>["w-[16px]","h-[16px]"]),{$storage:n}=bd(),a=rt(()=>{var h;return(h=n.layout)==null?void 0:h.themeColor}),o=t,l=()=>{o("toggleClick")};return(h,c)=>{const u=Q("IconifyIconOffline"),f=Qn("tippy");return Zt((P(),$("div",{class:"center-collapse",onClick:l},[R(u,{icon:E(YS),class:Ft([i.value,a.value==="light"?"":"text-primary"]),style:De({transform:e.isActive?"none":"rotateY(180deg)"})},null,8,["icon","class","style"])])),[[f,{content:e.isActive?"点击折叠":"点击展开",theme:E(s),hideOnClick:"toggle",placement:"right"}]])}}}),ZS=ae(KS,[["__scopeId","data-v-f8d2db65"]]),QS=_t({__name:"vertical",setup(d){var v,_;const t=E1(),e=nt(!1),s=nt((_=(v=Ts().getItem(`${T1()}configure`))==null?void 0:v.showLogo)!=null?_:!0),{device:i,pureApp:n,isCollapse:a,tooltipEffect:o,menuSelect:l,toggleSideBar:h}=fs(),c=nt([]),u=rt(()=>n.layout==="mix"&&i.value!=="mobile"?c.value:Ze().wholeMenus),f=rt(()=>n.layout==="mix"?!1:u.value.length===0),g=rt(()=>{var w;return Ia((w=t.meta)==null?void 0:w.activePath)?t.path:t.meta.activePath});function b(){let w="";w=g.value,c.value=[];const A=S0(w,Ze().wholeMenus),C=C0(A[0]||w,Ze().wholeMenus);C!=null&&C.children&&(c.value=C==null?void 0:C.children)}return Is(()=>[t.path,Ze().wholeMenus],()=>{t.path.includes("/redirect")||(b(),l(t.path))}),Ws(()=>{b(),Me.on("logoChange",w=>{s.value=w})}),lm(()=>{Me.off("logoChange")}),(w,A)=>{const C=Q("el-menu"),S=Q("el-scrollbar"),T=Qn("loading");return Zt((P(),$("div",{class:Ft(["sidebar-container",s.value?"has-logo":"no-logo"]),onMouseenter:A[0]||(A[0]=mi(x=>e.value=!0,["prevent"])),onMouseleave:A[1]||(A[1]=mi(x=>e.value=!1,["prevent"]))},[s.value?(P(),lt(LS,{key:0,collapse:E(a)},null,8,["collapse"])):ct("",!0),R(S,{"wrap-class":"scrollbar-wrapper",class:Ft([E(i)==="mobile"?"mobile":"pc"])},{default:q(()=>[R(C,{router:"","unique-opened":"",mode:"vertical","popper-class":"pure-scrollbar",class:"outer-most select-none",collapse:E(a),"collapse-transition":!1,"popper-effect":E(o),"default-active":g.value},{default:q(()=>[(P(!0),$(Le,null,ks(u.value,x=>(P(),lt(qv,{key:x.path,item:x,"base-path":x.path,class:"outer-most select-none"},null,8,["item","base-path"]))),128))]),_:1},8,["collapse","popper-effect","default-active"])]),_:1},8,["class"]),E(i)!=="mobile"&&(e.value||E(a))?(P(),lt(ZS,{key:1,"is-active":E(n).sidebar.opened,onToggleClick:E(h)},null,8,["is-active","onToggleClick"])):ct("",!0),E(i)!=="mobile"?(P(),lt(XS,{key:2,"is-active":E(n).sidebar.opened,onToggleClick:E(h)},null,8,["is-active","onToggleClick"])):ct("",!0)],34)),[[T,f.value]])}}}),JS=ae(QS,[["__scopeId","data-v-d50ad1a6"]]),t2={class:"horizontal-header"},e2=["src"],s2={class:"horizontal-header-right"},i2={class:"el-dropdown-link navbar-bg-hover"},n2={class:"user-text-box"},r2={key:0,class:"dark:text-white user-name-text"},a2={key:0},o2={key:1,class:"dark:text-white"},l2=_t({__name:"horizontal",setup(d){const t=k0,e=nt(),{route:s,title:i,logout:n,changRolesFn:a,backTopMenu:o,onPanel:l,getLogo:h,name:c,username:u,userAvatar:f,avatarsStyle:g}=fs(),b=rt(()=>{var k;return Ia((k=s.meta)==null?void 0:k.activePath)?s.path:s.meta.activePath});ka(()=>{var k;(k=e.value)==null||k.handleResize()});const{bool:v,toggle:_,setFalse:w}=up(),A=nt(!1);Ws(()=>{sessionStorage.getItem("roleInfo")?A.value=!0:A.value=!1});function C(){_()}function S(){_()}function T(){w()}function x(){w()}return(k,I)=>{const M=Q("el-menu"),L=Q("IconifyIconOffline"),F=Q("el-dropdown-item"),it=Q("el-dropdown-menu"),at=Q("el-dropdown"),J=Qn("loading");return Zt((P(),$("div",t2,[D("div",{class:"horizontal-header-left",onClick:I[0]||(I[0]=(...tt)=>E(o)&&E(o)(...tt))},[D("img",{src:E(t),alt:"logo"},null,8,e2),D("span",null,Jt(E(i)),1)]),R(M,{ref_key:"menuRef",ref:e,router:"",mode:"horizontal","popper-class":"pure-scrollbar",class:"horizontal-header-menu","default-active":b.value},{default:q(()=>[(P(!0),$(Le,null,ks(E(Ze)().wholeMenus,tt=>(P(),lt(qv,{key:tt.path,item:tt,"base-path":tt.path},null,8,["item","base-path"]))),128))]),_:1},8,["default-active"]),D("div",s2,[R(pm,{id:"header-search"}),R(mm,{id:"full-screen"}),R(at,{trigger:"click"},{dropdown:q(()=>[R(it,{class:"logout"},{default:q(()=>[A.value?(P(),lt(F,{key:0,onClick:C},{default:q(()=>[R(L,{icon:E(bm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 切换角色 ")]),_:1})):ct("",!0),R(F,{onClick:S},{default:q(()=>[R(L,{icon:E(ym),style:{margin:"5px"}},null,8,["icon"]),Tt(" 修改密码 ")]),_:1}),R(F,{onClick:E(n)},{default:q(()=>[R(L,{icon:E(vm),style:{margin:"5px"}},null,8,["icon"]),Tt(" 退出系统 ")]),_:1},8,["onClick"])]),_:1})]),default:q(()=>[D("div",i2,[D("div",n2,[E(c)?(P(),$("p",r2,[Tt(Jt(E(c)),1),E(Ts)().getItem(E(Pa)).ai_type===2?(P(),$("span",a2,"（AI）")):ct("",!0)])):ct("",!0),E(u)?(P(),$("p",o2)):ct("",!0)])])]),_:1}),D("span",{class:"set-icon navbar-bg-hover",title:"打开项目配置",onClick:I[1]||(I[1]=(...tt)=>E(l)&&E(l)(...tt))},[R(L,{icon:E(_m)},null,8,["icon"])])]),R(um,{show:E(v),onCloseChangeRole:T},null,8,["show"]),R(gm,{show:E(v),onCloseChangePassword:x},null,8,["show"])])),[[J,E(Ze)().wholeMenus.length===0]])}}}),h2=ae(l2,[["__scopeId","data-v-6fe38480"]]),c2={key:0},d2=_t({__name:"index",setup(d){const t=nt(),{isDark:e}=E0(),{layout:s}=P1(),i=k1(),n=R0(),{$storage:a}=bd(),o=om({sidebar:rt(()=>Ci().sidebar),device:rt(()=>Ci().device),fixedHeader:rt(()=>n.fixedHeader),classes:rt(()=>({hideSidebar:!o.sidebar.opened,openSidebar:o.sidebar.opened,withoutAnimation:o.sidebar.withoutAnimation,mobile:o.device==="mobile"})),hideTabs:rt(()=>a==null?void 0:a.configure.hideTabs)});function l(f){var g,b,v,_,w,A;window.document.body.setAttribute("layout",f),a.layout={layout:`${f}`,theme:(g=a.layout)==null?void 0:g.theme,darkMode:(b=a.layout)==null?void 0:b.darkMode,sidebarStatus:(v=a.layout)==null?void 0:v.sidebarStatus,epThemeColor:(_=a.layout)==null?void 0:_.epThemeColor,themeColor:(w=a.layout)==null?void 0:w.themeColor,overallStyle:(A=a.layout)==null?void 0:A.overallStyle}}function h(f,g){Ci().toggleDevice(f),Ci().toggleSideBar(g,"resize")}let c=!0;dp(t,f=>{if(i)return;const g=f[0],[{inlineSize:b,blockSize:v}]=g.borderBoxSize;Ci().setViewportSize({width:b,height:v}),b<=760?l("vertical"):l(Ci().layout),b>0&&b<=760?(h("mobile",!1),c=!0):b>760&&b<=990?c&&(h("desktop",!1),c=!1):b>990&&!o.sidebar.isClickCollapse?(h("desktop",!0),c=!0):(h("desktop",!1),c=!1)}),Ws(()=>{i&&h("mobile",!1)}),T0(()=>{var f;fm().dataThemeChange((f=a.layout)==null?void 0:f.overallStyle)});const u=_t({render(){return rr("div",{class:{"fixed-header":o.fixedHeader},style:[o.hideTabs&&s.value.includes("horizontal")?e.value?"box-shadow: 0 1px 4px #0d0d0d":"box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08)":""]},{default:()=>[!n.hiddenSideBar&&(s.value.includes("vertical")||s.value.includes("mix"))?rr(m0):s.value.includes("vertical")?rr(m0):null,!n.hiddenSideBar&&s.value.includes("horizontal")?rr(h2):null,s.value.includes("horizontal")||s.value.includes("mix")?rr(zv):null]})}});return(f,g)=>{const b=Q("el-backtop"),v=Q("el-scrollbar");return P(),$("div",{ref_key:"appWrapperRef",ref:t,class:Ft(["app-wrapper",o.classes])},[Zt(D("div",{class:"app-mask",onClick:g[0]||(g[0]=_=>E(Ci)().toggleSideBar())},null,512),[[rn,o.device==="mobile"&&o.sidebar.opened&&E(s).includes("vertical")]]),Zt(R(JS,null,null,512),[[rn,!E(n).hiddenSideBar&&(E(s).includes("vertical")||E(s).includes("mix"))]]),D("div",{class:Ft(["main-container",E(n).hiddenSideBar?"main-hidden":""])},[o.fixedHeader?(P(),$("div",c2,[R(E(u)),R(v0,{"fixed-header":o.fixedHeader},null,8,["fixed-header"])])):(P(),lt(v,{key:1},{default:q(()=>[R(b,{title:"回到顶部",target:".main-container .el-scrollbar__wrap"},{default:q(()=>[R(E(Uv))]),_:1}),R(E(u)),R(v0,{"fixed-header":o.fixedHeader},null,8,["fixed-header"])]),_:1}))],2),R(TS)],2)}}}),A2=ae(d2,[["__scopeId","data-v-6a7503d7"]]);export{A2 as default};
