var h=(y,u,e)=>new Promise((i,t)=>{var c=l=>{try{a(e.next(l))}catch(n){t(n)}},d=l=>{try{a(e.throw(l))}catch(n){t(n)}},a=l=>l.done?i(l.value):Promise.resolve(l.value).then(c,d);a((e=e.apply(y,u)).next())});import{M as _,l as k}from"./rule-BOjT6TPp.js";import{l as f,i8 as R,d as O,P as S,r as g,o as I,g as M,h as r,b as o,u as s,f as C,Q as T,aa as U}from"./index-B63pSD2p.js";import{u as b}from"./hooks-Cf_Naqnw.js";const w=f(!1),p=f(null),m=f(""),x=()=>({isDisabled:w,timer:p,text:m,start:(e,i,t=60)=>h(void 0,null,function*(){if(!e)return;const c=R(t,!0);yield e.validateField(i,d=>{d&&(clearInterval(p.value),w.value=!0,m.value=`${t}`,p.value=setInterval(()=>{t>0?(t-=1,m.value=`${t}`):(m.value="",w.value=!1,clearInterval(p.value),t=c)},1e3))})}),end:()=>{m.value="",w.value=!1,clearInterval(p.value)}}),Z={width:24,height:24,body:'<path fill="currentColor" d="M19 10h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h1V9a7 7 0 0 1 14 0v1Zm-2 0V9A5 5 0 0 0 7 9v1h10Zm-6 4v4h2v-4h-2Z"/>'},F=Z,J={width:24,height:24,body:'<path fill="currentColor" d="M20 22H4v-2a5 5 0 0 1 5-5h6a5 5 0 0 1 5 5v2Zm-8-9a6 6 0 1 1 0-12a6 6 0 0 1 0 12Z"/>'},$=J,q=O({__name:"regist",setup(y){f(!1);const u=f(!1),e=S({username:"",password:""}),i=f();x();const t=d=>h(this,null,function*(){u.value=!0,d&&(yield d.validate((a,l)=>{if(a){let n=JSON.parse(JSON.stringify(e));n.password=T(JSON.parse(JSON.stringify(e.password)))}else return u.value=!1,l}))});function c(){x().end(),U().SET_CURRENT_COMPONENT(0)}return(d,a)=>{const l=g("el-input"),n=g("el-form-item"),V=g("el-button"),N=g("el-form");return I(),M(N,{ref_key:"ruleFormRef",ref:i,model:e,rules:s(k),size:"large"},{default:r(()=>[o(s(_),{delay:100},{default:r(()=>[o(n,{rules:[{required:!0,message:"请输入账号",trigger:"blur"}],prop:"username"},{default:r(()=>[o(l,{modelValue:e.username,"onUpdate:modelValue":a[0]||(a[0]=v=>e.username=v),clearable:"",placeholder:"账号","prefix-icon":s(b)(s($))},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1}),o(s(_),{delay:150},{default:r(()=>[o(n,{prop:"password"},{default:r(()=>[o(l,{modelValue:e.password,"onUpdate:modelValue":a[1]||(a[1]=v=>e.password=v),clearable:"","show-password":"",placeholder:"密码","prefix-icon":s(b)(s(F))},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1}),o(s(_),{delay:250},{default:r(()=>[o(V,{class:"w-full mt-4",size:"default",type:"primary",loading:u.value,onClick:a[2]||(a[2]=v=>t(i.value))},{default:r(()=>[C(" 注册 ")]),_:1},8,["loading"])]),_:1}),o(s(_),{delay:250},{default:r(()=>[o(V,{class:"w-full mt-4",size:"default",loading:u.value,onClick:c},{default:r(()=>[C(" 返回 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])}}});export{q as default};
