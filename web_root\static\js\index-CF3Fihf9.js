import e from"./index-DLGxCJxs.js";import{d as o,az as t,n as a,o as n,c as r,b as m}from"./index-B63pSD2p.js";import"./handleMethod-BIjqYEft.js";import"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";const k=o({name:"my-quality-task",__name:"index",setup(s){return t(),a(()=>{}),(i,p)=>(n(),r("div",null,[m(e)]))}});export{k as default};
