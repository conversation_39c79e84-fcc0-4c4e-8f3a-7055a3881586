import{aQ as s,aR as t}from"./index-B63pSD2p.js";const a=e=>s.request("post",t("/v1/ques_manage/get_ques_list"),{data:e}),_=e=>s.request("post",t("/v1/ques_manage/get_ques_detail"),{data:e}),n=e=>s.request("post",t("/v1/ques_manage/delete_question"),{data:e}),q=e=>s.request("post",t("/v1/ques_manage/edit_ques"),{data:e}),r=e=>s.request("post",t("/v1/ques_manage/all_business_ques_type"),{data:e}),o=e=>s.request("post",t("/v1/ques_manage/create_question"),{data:e});export{r as a,a as b,o as c,n as d,q as e,_ as g};
