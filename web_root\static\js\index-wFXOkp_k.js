var we=Object.defineProperty,Re=Object.defineProperties;var Oe=Object.getOwnPropertyDescriptors;var N=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var oe=(t,n,r)=>n in t?we(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r,W=(t,n)=>{for(var r in n||(n={}))re.call(n,r)&&oe(t,r,n[r]);if(N)for(var r of N(n))le.call(n,r)&&oe(t,r,n[r]);return t},J=(t,n)=>Re(t,Oe(n));var se=(t,n)=>{var r={};for(var c in t)re.call(t,c)&&n.indexOf(c)<0&&(r[c]=t[c]);if(t!=null&&N)for(var c of N(t))n.indexOf(c)<0&&le.call(t,c)&&(r[c]=t[c]);return r};var A=(t,n,r)=>new Promise((c,M)=>{var L=f=>{try{m(r.next(f))}catch(T){M(T)}},H=f=>{try{m(r.throw(f))}catch(T){M(T)}},m=f=>f.done?c(f.value):Promise.resolve(f.value).then(L,H);m((r=r.apply(t,n)).next())});import{f as Q}from"./handleMethod-BIjqYEft.js";import{aC as Me,aQ as R,aR as O,d as Te,l as p,P as qe,n as Ve,ao as ze,r as _,o as B,c as We,e as k,b as u,h as l,f as v,q as Be,t as Le,u as G,aN as ue,g as E,y as ie,aV as K,T as y,C as pe,ac as He,ad as Ie,_ as Fe}from"./index-B63pSD2p.js";import{c as Ne,a as Ae}from"./calculateTableHeight-BjE6OFD1.js";import{S as Ee}from"./scoreRelateLineChart-DOiNxsyg.js";import{d as ce}from"./validate-Dc6ka3px.js";import"./index-C9GYnvBh.js";const Ue=Me(),$e=t=>R.request("post",O("/v1/official_mark/get_task_list"),{data:J(W({},t),{user_id:Ue.userId})}),Pe=t=>R.request("post",O("/v1/user/get_user"),{data:W({},t)}),je=t=>R.request("post",O("/v1/survey_monitor/try_mark_monitor"),{data:t}).then(n=>n),de=t=>R.request("post",O("/v1/survey_monitor/update_try_mark_result"),{data:t}).then(n=>n),Je=t=>R.request("post",O("/v1/survey_monitor/update_is_office_mark"),{data:t}).then(n=>n),Qe=t=>R.request("post",O("/v1/survey_monitor/try_mark_result"),{data:t}).then(n=>n),Ge=t=>(He("data-v-f9a84acd"),t=t(),Ie(),t),Ke={class:"zf-first-box trial-monitoring"},Xe={class:"zf-second-box"},Ye={class:"zf-flex-end"},Ze={class:"task-btn-box"},et={class:"task-btn-box"},tt={class:"echart-wrap"},at=Ge(()=>k("template",null,null,-1)),nt=Te({name:"trial-monitoring",__name:"index",setup(t){const n=[{value:"0",label:"未判定"},{value:"1",label:"合格"},{value:"2",label:"不合格"}],r=[{value:!1,label:"否"},{value:!0,label:"是"}],c=[{value:1,label:"第1轮"},{value:2,label:"第2轮"},{value:3,label:"第3轮"},{value:4,label:"第4轮"},{value:5,label:"第5轮"},{value:6,label:"第6轮"}],M=[{value:1,label:"未发起"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}],L=p(null),H=p(null),m=p({task_type:2}),f=qe({labelWidth:"68px",itemWidth:"160px",rules:{diff_count:[{trigger:["blur"],validator:(a,e,o)=>{if((!e[0]||ce(e[0]))&&(!e[1]||ce(e[1])))o();else return o(new Error("请输入数字！"))}},{trigger:["blur"],validator:(a,e,o)=>{if(!e[0]&&!e[1]||e[0]&&e[1])o();else return o(new Error("请输入完整的起止区间！"))}}]},fields:[{label:"任务",prop:"task_name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择任务",optionData:()=>X.value},{label:"试评结果",prop:"try_mark_result",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择试评结果",optionData:()=>n},{label:"转为正评",prop:"is_office_mark",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择转为正评",optionData:()=>r},{label:"轮次",prop:"round_count",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择轮次",optionData:()=>c},{label:"试评状态",prop:"round_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择试评状态",optionData:()=>M},{label:"评阅员",prop:"name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评阅员",optionData:()=>Y.value},{label:"试评偏差量",prop:"diff_count",type:"doubleInput",defaultValue:[null,null],placeholder:"请输入",clearable:!0,labelWidth:82,width:"80px"}]}),T=()=>A(this,null,function*(){var d,s;const e=(s=(d=(yield $e({page_size:-1,task_type:2})).data)==null?void 0:d.data)!=null?s:[],o=new Map(e.map(i=>[i.task_name,i]));X.value=[...o.values()].map(i=>({label:i.task_name,value:i.task_name}))}),_e=()=>A(this,null,function*(){var d;const e=((d=(yield Pe({page_size:"99999",user_type:1,system_user_type:2,role_id:"3"})).data)==null?void 0:d.data)||[],o=new Map(e.map(s=>[s.name,s]));Y.value=[...o.values()].map(s=>({label:s.name,value:s.name}))}),X=p([]),Y=p([]),x=p({field:[{prop:"project_name",label:"资格",minWidth:"180px"},{prop:"subject_name",label:"科目",minWidth:"220px"},{prop:"task_name",label:"任务名称",minWidth:"220px"},{prop:"round_count",label:"轮次",minWidth:"90px",formatter:a=>a.round_count?`第${a.round_count}轮`:"-"},{prop:"name",label:"评阅员",minWidth:"90px"},{prop:"ques_group_name",label:"所属小组",minWidth:"140px"},{prop:"reviewed_count",label:"已试评量",minWidth:"90px"},{prop:"exceed_count",label:"试评偏差量",minWidth:"100px"},{prop:"round_state",label:"试评状态",minWidth:"90px",formatter:a=>{var e;return(e=Q(a.round_state,M))!=null?e:"-"}},{prop:"average_speed",label:"平均速度（份/时）",minWidth:"150px",formatter:a=>{var e;return(e=a.average_speed)!=null?e:"-"}},{prop:"try_mark_result",label:"试评结果",type:"slot",minWidth:"90px"},{prop:"is_office_mark",label:"转为正评",minWidth:"90px",formatter:a=>{var e;return(e=Q(a.is_office_mark,r))!=null?e:"-"}},{prop:"operation",label:"操作",type:"slot",minWidth:"210px",fixed:"right"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),Z=p([]),q=p([]);let ee=null;Ve(()=>{T(),_e(),Ne(ee,L.value,x.value),D()}),ze(()=>{Ae(ee)});const me=(a,e)=>{},D=()=>{H.value.formValidate().then(()=>{let{currentPage:a,pageSize:e}=x.value.pageOptions;const i=m.value,{diff_count:o}=i,d=se(i,["diff_count"]);let s=J(W({current_page:a,page_size:e},d),{round_state_list:m.value.round_state?[m.value.round_state]:[]});(o[0]||o[1])&&(s.diff_count=["",""],s.diff_count[0]=o[0]?Number(o[0]):"",s.diff_count[1]=o[1]?Number(o[1]):""),je(s).then(g=>{var h,w,F;const S=((h=g.data)==null?void 0:h.data)||[];Z.value=S,x.value.pageOptions.total=(F=(w=g.data)==null?void 0:w.total)!=null?F:0})})},fe=a=>{x.value.pageOptions.pageSize=a,D()},ge=a=>{x.value.pageOptions.currentPage=a,D()},he=a=>{q.value=a},be=(a,e)=>A(this,null,function*(){K.confirm(`确定判定${a==="2"?'试评<span style="color: red;">不合格</span>':'试评<span style="color: #52c41a;">合格</span>'}吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then(()=>{de({try_mark_result_list:[{task_id:e.task_id,round_id:e.round_id,user_id:e.user_id,group_id:e.group_id,ques_group_id:e.ques_group_id,try_mark_result:a}]}).then(()=>{D(),y({type:"success",message:"操作成功"})}).catch(()=>{y({type:"error",message:"操作失败"})})}).catch(()=>{})}),U=p();function ve(){if(!q.value.length)y.warning("至少选择一条数据！");else{if(!U.value)return;U.value.handleOpen()}}function ye(a){K.confirm(`确定判定${a==="2"?'试评<span style="color: red;">不合格</span>':'试评<span style="color: #52c41a;">合格</span>'}吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"warning"}).then(()=>{const e=q.value.map(o=>({task_id:o.task_id,round_id:o.round_id,user_id:o.user_id,group_id:o.group_id,ques_group_id:o.ques_group_id,try_mark_result:a}));de({try_mark_result_list:e}).then(()=>{D(),y({type:"success",message:"操作成功"})}).catch(()=>{y({type:"error",message:"操作失败"})})}).catch(()=>{})}function ke(){q.value.length?K.confirm("确定进入正评吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{const a=q.value.map(e=>({task_id:e.task_id,round_id:e.round_id,user_id:e.user_id,group_id:e.group_id,ques_group_id:e.ques_group_id,is_office_mark:!0}));Je({is_office_mark_list:a}).then(()=>{D(),y({type:"success",message:"操作成功"})}).catch(()=>{y({type:"error",message:"操作失败"})})}).catch(()=>{}):y.warning("至少选择一条数据！")}const $=p(!1),P=p(!1),C={xData:["1","2","3"],options:{xAxis:{data:[]}},series:[{name:"评阅员评分",data:[0,0,0]},{name:"小组平均分",data:[0,0,0]},{name:"差异分",data:[0,0,0]}]};C.options.xAxis.data=C.xData;const V=p(C.xData),j=p(C.options),I=p(C.series);function te(){$.value=!1,P.value=!1}const ae=p();function xe(a){ae.value=a,$.value=!0,j.value.xAxis={data:[]},V.value=C.xData,I.value=C.series,De(),pe(()=>{P.value=!0})}function De(){Qe(W({},ae.value)).then(a=>{var e,o,d,s,i;if(a.code===200){const g=(o=(e=a.data)==null?void 0:e.data)!=null?o:{};V.value=(s=(d=g.x_data)==null?void 0:d.map((S,h)=>`考生${h+1}`))!=null?s:[],j.value.xAxis={data:V.value},I.value=[],(i=g.legend)==null||i.forEach((S,h)=>{var w;I.value.push({name:S,data:(w=g[`y${h+1}_data`])!=null?w:[]})})}}).catch(()=>{V.value=[]})}const ne=p();function Ce(){pe(()=>{ne.value.resizeChart()})}return(a,e)=>{const o=_("form-component"),d=_("el-card"),s=_("el-button"),i=_("el-dropdown-item"),g=_("el-dropdown-menu"),S=_("el-dropdown"),h=_("Auth"),w=_("table-component"),F=_("el-empty"),Se=_("DialogComponent");return B(),We("div",Ke,[k("div",Xe,[u(d,null,{default:l(()=>[k("div",{ref_key:"formDivRef",ref:L},[u(o,{ref_key:"formRef",ref:H,modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=b=>m.value=b),"form-options":f,"is-query-btn":!0,onOnchangeFn:me,onQueryDataFn:D},null,8,["modelValue","form-options"])],512)]),_:1}),u(d,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:l(()=>[k("div",Ye,[k("div",Ze,[u(h,{value:"trial-monitoring/batch-judge"},{default:l(()=>[u(S,{class:"task-btn",ref_key:"tableTopDropdownRef",ref:U,trigger:"contextmenu",onCommand:ye},{dropdown:l(()=>[u(g,null,{default:l(()=>[u(i,{command:"1"},{default:l(()=>[v("试评合格")]),_:1}),u(i,{command:"2"},{default:l(()=>[v("试评不合格")]),_:1})]),_:1})]),default:l(()=>[u(s,{type:"primary",onClick:ve},{default:l(()=>[v(" 批量判定 ")]),_:1})]),_:1},512)]),_:1}),u(h,{value:"trial-monitoring/to-formal"},{default:l(()=>[u(s,{type:"primary",onClick:ke},{default:l(()=>[v("转为正评")]),_:1})]),_:1})])]),u(w,{minHeight:x.value.styleOptions.minHeight,"table-options":x.value,"table-data":Z.value,onOnHandleSizeChange:fe,onOnHandleCurrentChange:ge,onOnHandleSelectionChange:he},{try_mark_result:l(({row:b})=>{var z;return[k("span",{style:Be({color:b.try_mark_result===2?"red":""})},Le((z=G(Q)(typeof b.try_mark_result=="number"?String(b.try_mark_result):b.try_mark_result,n))!=null?z:"-"),5)]}),operation:l(b=>[k("div",et,[G(ue)("trial-monitoring/result-judge")?(B(),E(S,{key:0,class:"task-btn",trigger:"click",onCommand:z=>be(z,b.row)},{dropdown:l(()=>[u(g,null,{default:l(()=>[u(i,{command:"1"},{default:l(()=>[v("试评合格")]),_:1}),u(i,{command:"2"},{default:l(()=>[v("试评不合格")]),_:1})]),_:1})]),default:l(()=>[u(s,{link:"",type:"primary"},{default:l(()=>[v(" 结果判定 ")]),_:1})]),_:2},1032,["onCommand"])):ie("",!0),G(ue)("trial-monitoring/result")?(B(),E(s,{key:1,link:"",type:"primary",class:"task-btn",onClick:z=>xe(b.row)},{default:l(()=>[v(" 试评结果 ")]),_:2},1032,["onClick"])):ie("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1}),u(Se,{isShowDialog:$.value,onCloseDialog:te,beforeClose:te,fullscreenIcon:!0,title:"试评结果",width:"800",class:"rootDialogClass",onFullScreen:Ce},{content:l(()=>[k("div",tt,[P.value&&V.value.length!==0?(B(),E(Ee,{key:0,ref_key:"lineChartRef",ref:ne,options:j.value,series:I.value},null,8,["options","series"])):(B(),E(F,{key:1,description:"暂无数据","image-size":80}))])]),footer:l(()=>[at]),_:1},8,["isShowDialog"])])])}}}),ct=Fe(nt,[["__scopeId","data-v-f9a84acd"]]);export{ct as default};
