var B=Object.defineProperty,E=Object.defineProperties;var G=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var w=(p,o,a)=>o in p?B(p,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):p[o]=a,j=(p,o)=>{for(var a in o||(o={}))N.call(o,a)&&w(p,a,o[a]);if(O)for(var a of O(o))Q.call(o,a)&&w(p,a,o[a]);return p},S=(p,o)=>E(p,G(o));var x=(p,o,a)=>new Promise((k,e)=>{var W=n=>{try{d(a.next(n))}catch(u){e(u)}},m=n=>{try{d(a.throw(n))}catch(u){e(u)}},d=n=>n.done?k(n.value):Promise.resolve(n.value).then(W,m);d((a=a.apply(p,o)).next())});import{g as A,e as U,c as $}from"./formal-monitor-BXl8VaF0.js";import{c as I,a as J}from"./calculateTableHeight-BjE6OFD1.js";import{p as K,g as X,a as Y}from"./common-methods-BWkba4Bo.js";import{d as Z}from"./downloadRequest-CdE2PBjt.js";import{d as ee,l as _,P as te,n as ae,ao as oe,r as v,o as le,c as ne,b,h as y,ae as re,e as V,f as pe,aV as se,T as C,C as ue,_ as ie}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const ce={class:"zf-flex-end"},de=ee({__name:"question-monitor",setup(p){const o=[{value:1,label:"未发起"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}],a=_(null),k=_(null),e=_({}),W=te({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>K.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>m.value},{label:"任务",prop:"task_name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择任务",optionData:()=>d.value},{label:"执行状态",prop:"round_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择执行状态",optionData:()=>o},{label:"所属题组",prop:"ques_group_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属题组",optionData:()=>n.value}]}),m=_([]),d=_([]),n=_([]),u=_({field:[{prop:"project_name",label:"所属资格",minWidth:"220px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"ques_group_name",label:"题组",minWidth:"140px"},{prop:"expert_total",label:"参评人数",minWidth:"120px"},{prop:"total_count",label:"阅卷总量",minWidth:"120px"},{prop:"reviewed_count",label:"已阅量",minWidth:"120px"},{prop:"unreviewed_count",label:"未阅量",minWidth:"120px"},{prop:"rate",label:"阅卷进度",minWidth:"120px",formatter:t=>`${t.rate?t.rate:0}%`},{prop:"average_speed1",label:"平均速度（份/时）",minWidth:"100px"},{prop:"average_speed2",label:"平均评分时间（秒/份）",minWidth:"120px"},{prop:"max_speed",label:"最长评分时间（秒）",minWidth:"120px"},{prop:"min_speed",label:"最短评分时间（秒）",minWidth:"120px"},{prop:"time",label:"预估剩余时间（时）",minWidth:"120px"},{prop:"average_score",label:"平均分",minWidth:"120px"},{prop:"max_score",label:"最高分",minWidth:"120px"},{prop:"min_score",label:"最低分",minWidth:"120px"},{prop:"score_standard_deviation",label:"分数标准差",minWidth:"120px"},{prop:"full_score_rate",label:"满分率",minWidth:"120px"},{prop:"full_score_count",label:"满分人数",minWidth:"120px"},{prop:"zero_score_rate",label:"零分率",minWidth:"120px"},{prop:"zero_score_count",label:"零分人数",minWidth:"120px"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),q=_([]);let D=null;ae(()=>{X(),f(),I(D,a.value,u.value,!0,58),g()}),oe(()=>{J(D)});const F=(t,r)=>x(this,null,function*(){if(t.prop==="project_id"&&(m.value=[],d.value=[],n.value=[],e.value.subject_id&&(e.value.subject_id=null),e.value.task_name&&(e.value.task_name=null),e.value.ques_group_id&&(e.value.ques_group_id=null),r)){const s=yield Y(r);m.value=s||[],f()}t.prop==="subject_id"&&(d.value=[],n.value=[],e.value.task_name&&(e.value.task_name=null),e.value.ques_group_id&&(e.value.ques_group_id=null),r&&(f(),R()))}),f=()=>x(this,null,function*(){var i,c;const r=(c=(i=(yield A({page_size:-1,project_id:e.value.project_id,subject_id:e.value.subject_id,task_type:1})).data)==null?void 0:i.data)!=null?c:[],s=new Map(r.map(l=>[l.task_name,l]));d.value=[...s.values()].map(l=>({label:l.task_name,value:l.task_name}))}),R=()=>x(this,null,function*(){var c;const s=(((c=(yield $({page_size:-1,project_id:e.value.project_id,subject_id:e.value.subject_id})).data)==null?void 0:c.data)||[]).filter(l=>l.group_level===2),i=new Map(s.map(l=>[l.ques_group_id,l]));n.value=[...i.values()].map(l=>({label:l.name,value:l.ques_group_id}))});function z(){let{currentPage:t,pageSize:r}=u.value.pageOptions;return j({current_page:t,page_size:r,task_type:1,round_state_list:e.value.round_state?[e.value.round_state]:[],round_count:"1"},e.value)}const g=()=>{U(z()).then(t=>{var s,i,c;const r=((s=t.data)==null?void 0:s.data)||[];q.value=r,u.value.pageOptions.total=(c=(i=t.data)==null?void 0:i.total)!=null?c:0})},M=()=>{se.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z("post","/v1/survey_monitor/ques_group_survey_monitor_export",void 0,{},S(j({},z()),{page_size:-1}),"xlsx").then(t=>{t&&t.data&&t.data.type=="application/json"&&C({message:"暂无导出信息！",type:"warning"})}).catch(()=>{C({type:"error",message:"导出失败"})})}).catch(()=>{})},L=t=>{u.value.pageOptions.pageSize=t,g()},T=t=>{u.value.pageOptions.currentPage=t,g()};function H(){m.value=[],d.value=[],n.value=[],ue(()=>{f()})}return(t,r)=>{const s=v("form-component"),i=v("el-card"),c=v("el-button"),l=v("Auth"),P=v("table-component");return le(),ne("div",null,[b(i,null,{default:y(()=>[re(t.$slots,"tabs",{},void 0,!0),V("div",{ref_key:"formDivRef",ref:a},[b(s,{ref_key:"formRef",ref:k,modelValue:e.value,"onUpdate:modelValue":r[0]||(r[0]=h=>e.value=h),"form-options":W,"is-query-btn":!0,onOnchangeFn:F,onQueryDataFn:g,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:3}),b(i,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:y(()=>{var h;return[V("div",ce,[b(l,{value:"formal-monitor/question-export"},{default:y(()=>[b(c,{type:"primary",onClick:M},{default:y(()=>[pe("导出")]),_:1})]),_:1})]),b(P,{minHeight:(h=u.value.styleOptions)==null?void 0:h.minHeight,"table-options":u.value,"table-data":q.value,onOnHandleSizeChange:L,onOnHandleCurrentChange:T},null,8,["minHeight","table-options","table-data"])]}),_:1})])}}}),xe=ie(de,[["__scopeId","data-v-61b23e55"]]);export{xe as default};
