#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卓帆电子化考试阅卷管理系统 - 一键启动器
功能：
1. 按顺序启动：Redis → 主程序 → 数据服务程序
2. 一键关闭：关闭数据服务程序 → 关闭主程序 → 关闭Redis
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import os
import time
import threading
import psutil
import signal
from datetime import datetime


class SystemLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("卓帆电子化考试阅卷管理系统 - 一键启动器")
        self.root.geometry("600x500")
        self.root.resizable(False, False)

        # 进程管理
        self.redis_process = None
        self.main_process = None
        self.service_process = None

        # 程序路径
        self.redis_path = r"Redis-x64-3.0.504\redis-server.exe"
        self.main_program_path = r"卓帆电子化考试阅卷管理系统V1.0.exe"
        self.service_program_path = r"定时任务V1.0.exe"

        self.setup_ui()
        self.check_programs_exist()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="卓帆电子化考试阅卷管理系统",
                                font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 状态显示框架
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2,
                          sticky=(tk.W, tk.E), pady=(0, 10))

        # 状态标签
        self.redis_status = ttk.Label(
            status_frame, text="Redis: 未启动", foreground="red")
        self.redis_status.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.main_status = ttk.Label(
            status_frame, text="主程序: 未启动", foreground="red")
        self.main_status.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.service_status = ttk.Label(
            status_frame, text="数据服务: 未启动", foreground="red")
        self.service_status.grid(row=2, column=0, sticky=tk.W, pady=2)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)

        # 启动按钮
        self.start_button = ttk.Button(button_frame, text="一键启动",
                                       command=self.start_all_threaded, width=15)
        self.start_button.grid(row=0, column=0, padx=5)

        # 关闭按钮
        self.stop_button = ttk.Button(button_frame, text="一键关闭",
                                      command=self.stop_all_threaded, width=15)
        self.stop_button.grid(row=0, column=1, padx=5)

        # 刷新状态按钮
        self.refresh_button = ttk.Button(button_frame, text="刷新状态",
                                         command=self.update_status, width=15)
        self.refresh_button.grid(row=0, column=2, padx=5)

        # 日志显示框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(
            tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=15, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 初始状态更新
        self.update_status()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()

    def check_programs_exist(self):
        """检查程序文件是否存在"""
        missing_files = []

        if not os.path.exists(self.redis_path):
            missing_files.append(f"Redis服务器: {self.redis_path}")

        if not os.path.exists(self.main_program_path):
            missing_files.append(f"主程序: {self.main_program_path}")

        if not os.path.exists(self.service_program_path):
            missing_files.append(f"数据服务程序: {self.service_program_path}")

        if missing_files:
            error_msg = "以下程序文件未找到：\n" + "\n".join(missing_files)
            messagebox.showerror("文件缺失", error_msg)
            self.log_message(f"错误: {error_msg}")

    def is_process_running(self, process_name):
        """检查进程是否正在运行"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False

    def update_status(self):
        """更新状态显示"""
        # 检查Redis状态
        if self.is_process_running("redis-server"):
            self.redis_status.config(text="Redis: 运行中", foreground="green")
        else:
            self.redis_status.config(text="Redis: 未启动", foreground="red")

        # 检查主程序状态
        if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
            self.main_status.config(text="主程序: 运行中", foreground="green")
        else:
            self.main_status.config(text="主程序: 未启动", foreground="red")

        # 检查数据服务状态
        if self.is_process_running("定时任务V1.0"):
            self.service_status.config(text="数据服务: 运行中", foreground="green")
        else:
            self.service_status.config(text="数据服务: 未启动", foreground="red")

    def start_redis(self):
        """启动Redis服务"""
        try:
            if self.is_process_running("redis-server"):
                self.log_message("Redis已经在运行中")
                return True

            self.log_message("正在启动Redis服务...")
            self.redis_process = subprocess.Popen([self.redis_path],
                                                  cwd=os.path.dirname(self.redis_path))
            time.sleep(3)  # 等待Redis启动

            if self.is_process_running("redis-server"):
                self.log_message("Redis服务启动成功")
                return True
            else:
                self.log_message("Redis服务启动失败")
                return False

        except Exception as e:
            self.log_message(f"启动Redis时出错: {str(e)}")
            return False

    def start_main_program(self):
        """启动主程序"""
        try:
            if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
                self.log_message("主程序已经在运行中")
                return True

            self.log_message("正在启动主程序...")
            self.main_process = subprocess.Popen([self.main_program_path])
            time.sleep(2)  # 等待主程序启动

            if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
                self.log_message("主程序启动成功")
                return True
            else:
                self.log_message("主程序启动失败")
                return False

        except Exception as e:
            self.log_message(f"启动主程序时出错: {str(e)}")
            return False

    def start_service_program(self):
        """启动数据服务程序"""
        try:
            if self.is_process_running("定时任务V1.0"):
                self.log_message("数据服务程序已经在运行中")
                return True

            self.log_message("正在启动数据服务程序...")
            self.service_process = subprocess.Popen(
                [self.service_program_path])
            time.sleep(2)  # 等待服务程序启动

            if self.is_process_running("定时任务V1.0"):
                self.log_message("数据服务程序启动成功")
                return True
            else:
                self.log_message("数据服务程序启动失败")
                return False

        except Exception as e:
            self.log_message(f"启动数据服务程序时出错: {str(e)}")
            return False

    def kill_process_by_name(self, process_name):
        """根据进程名终止进程"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    proc.terminate()
                    killed = True
                    self.log_message(
                        f"已终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.log_message(f"无法终止进程 {process_name}: {str(e)}")
        return killed

    def stop_service_program(self):
        """关闭数据服务程序"""
        try:
            self.log_message("正在关闭数据服务程序...")
            if self.kill_process_by_name("定时任务V1.0"):
                time.sleep(1)
                self.log_message("数据服务程序已关闭")
                return True
            else:
                self.log_message("数据服务程序未在运行或关闭失败")
                return True  # 如果没有运行，也算成功
        except Exception as e:
            self.log_message(f"关闭数据服务程序时出错: {str(e)}")
            return False

    def stop_main_program(self):
        """关闭主程序"""
        try:
            self.log_message("正在关闭主程序...")
            if self.kill_process_by_name("卓帆电子化考试阅卷管理系统V1.0"):
                time.sleep(1)
                self.log_message("主程序已关闭")
                return True
            else:
                self.log_message("主程序未在运行或关闭失败")
                return True  # 如果没有运行，也算成功
        except Exception as e:
            self.log_message(f"关闭主程序时出错: {str(e)}")
            return False

    def stop_redis(self):
        """关闭Redis服务"""
        try:
            self.log_message("正在关闭Redis服务...")
            if self.kill_process_by_name("redis-server"):
                time.sleep(1)
                self.log_message("Redis服务已关闭")
                return True
            else:
                self.log_message("Redis服务未在运行或关闭失败")
                return True  # 如果没有运行，也算成功
        except Exception as e:
            self.log_message(f"关闭Redis服务时出错: {str(e)}")
            return False

    def start_all(self):
        """按顺序启动所有程序"""
        self.log_message("开始一键启动...")
        self.start_button.config(state="disabled")
        self.stop_button.config(state="disabled")

        try:
            # 1. 启动Redis
            if not self.start_redis():
                self.log_message("启动失败：Redis服务启动失败")
                return False

            self.update_status()

            # 2. 启动主程序
            if not self.start_main_program():
                self.log_message("启动失败：主程序启动失败")
                return False

            self.update_status()

            # 3. 启动数据服务程序
            if not self.start_service_program():
                self.log_message("启动失败：数据服务程序启动失败")
                return False

            self.update_status()
            self.log_message("所有程序启动完成！")
            return True

        finally:
            self.start_button.config(state="normal")
            self.stop_button.config(state="normal")

    def stop_all(self):
        """按顺序关闭所有程序"""
        self.log_message("开始一键关闭...")
        self.start_button.config(state="disabled")
        self.stop_button.config(state="disabled")

        try:
            # 1. 关闭数据服务程序
            self.stop_service_program()
            self.update_status()

            # 2. 关闭主程序
            self.stop_main_program()
            self.update_status()

            # 3. 关闭Redis
            self.stop_redis()
            self.update_status()

            self.log_message("所有程序关闭完成！")

        finally:
            self.start_button.config(state="normal")
            self.stop_button.config(state="normal")

    def start_all_threaded(self):
        """在新线程中启动所有程序"""
        thread = threading.Thread(target=self.start_all)
        thread.daemon = True
        thread.start()

    def stop_all_threaded(self):
        """在新线程中关闭所有程序"""
        thread = threading.Thread(target=self.stop_all)
        thread.daemon = True
        thread.start()

    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出启动器吗？\n注意：这不会关闭已启动的程序。"):
            self.root.destroy()

    def run(self):
        """运行主程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("启动器已就绪")
        self.root.mainloop()


if __name__ == "__main__":
    try:
        launcher = SystemLauncher()
        launcher.run()
    except Exception as e:
        print(f"启动器运行出错: {str(e)}")
        input("按回车键退出...")
