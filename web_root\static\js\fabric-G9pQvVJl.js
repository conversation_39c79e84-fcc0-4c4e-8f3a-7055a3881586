import{ia as gt}from"./index-B63pSD2p.js";var Ct={};(function(et){/*! Fabric.js Copyright 2008-2015, Printio (<PERSON><PERSON><PERSON>, Maxim Chernyak) */var f=f||{version:"4.6.0"};if(et.fabric=f,typeof document!="undefined"&&typeof window!="undefined")document instanceof(typeof HTMLDocument!="undefined"?HTMLDocument:Document)?f.document=document:f.document=document.implementation.createHTMLDocument(""),f.window=window;else{var vt=gt,mt=new vt.JSDOM(decodeURIComponent("%3C!DOCTYPE%20html%3E%3Chtml%3E%3Chead%3E%3C%2Fhead%3E%3Cbody%3E%3C%2Fbody%3E%3C%2Fhtml%3E"),{features:{FetchExternalResources:["img"]},resources:"usable"}).window;f.document=mt.document,f.jsdomImplForWrapper=gt.implForWrapper,f.nodeCanvas=gt.Canvas,f.window=mt,DOMParser=f.window.DOMParser}f.isTouchSupported="ontouchstart"in f.window||"ontouchstart"in f.document||f.window&&f.window.navigator&&f.window.navigator.maxTouchPoints>0,f.isLikelyNode=typeof Buffer!="undefined"&&typeof window=="undefined",f.SHARED_ATTRIBUTES=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"],f.DPI=96,f.reNum="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:[eE][-+]?\\d+)?)",f.commaWsp="(?:\\s+,?\\s*|,\\s*)",f.rePathCommand=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:[eE][-+]?\d+)?)/ig,f.reNonWord=/[ \n\.,;!\?\-]/,f.fontPaths={},f.iMatrix=[1,0,0,1,0,0],f.svgNS="http://www.w3.org/2000/svg",f.perfLimitSizeTotal=2097152,f.maxCacheSideLimit=4096,f.minCacheSideLimit=256,f.charWidthsCache={},f.textureSize=2048,f.disableStyleCopyPaste=!1,f.enableGLFiltering=!0,f.devicePixelRatio=f.window.devicePixelRatio||f.window.webkitDevicePixelRatio||f.window.mozDevicePixelRatio||1,f.browserShadowBlurConstant=1,f.arcToSegmentsCache={},f.boundsOfCurveCache={},f.cachesBoundsOfCurve=!0,f.forceGLPutImageData=!1,f.initFilterBackend=function(){if(f.enableGLFiltering&&f.isWebglSupported&&f.isWebglSupported(f.textureSize))return console.log("max texture size: "+f.maxTextureSize),new f.WebglFilterBackend({tileSize:f.textureSize});if(f.Canvas2dFilterBackend)return new f.Canvas2dFilterBackend},typeof document!="undefined"&&typeof window!="undefined"&&(window.fabric=f),function(){function c(t,n){if(this.__eventListeners[t]){var h=this.__eventListeners[t];n?h[h.indexOf(n)]=!1:f.util.array.fill(h,!1)}}function s(t,n){if(this.__eventListeners||(this.__eventListeners={}),arguments.length===1)for(var h in t)this.on(h,t[h]);else this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(n);return this}function o(t,n){var h=function(){n.apply(this,arguments),this.off(t,h)}.bind(this);this.on(t,h)}function a(t,n){if(arguments.length===1)for(var h in t)o.call(this,h,t[h]);else o.call(this,t,n);return this}function e(t,n){if(!this.__eventListeners)return this;if(arguments.length===0)for(t in this.__eventListeners)c.call(this,t);else if(arguments.length===1&&typeof arguments[0]=="object")for(var h in t)c.call(this,h,t[h]);else c.call(this,t,n);return this}function i(t,n){if(!this.__eventListeners)return this;var h=this.__eventListeners[t];if(!h)return this;for(var r=0,l=h.length;r<l;r++)h[r]&&h[r].call(this,n||{});return this.__eventListeners[t]=h.filter(function(u){return u!==!1}),this}f.Observable={fire:i,on:s,once:a,off:e}}(),f.Collection={_objects:[],add:function(){if(this._objects.push.apply(this._objects,arguments),this._onObjectAdded)for(var c=0,s=arguments.length;c<s;c++)this._onObjectAdded(arguments[c]);return this.renderOnAddRemove&&this.requestRenderAll(),this},insertAt:function(c,s,o){var a=this._objects;return o?a[s]=c:a.splice(s,0,c),this._onObjectAdded&&this._onObjectAdded(c),this.renderOnAddRemove&&this.requestRenderAll(),this},remove:function(){for(var c=this._objects,s,o=!1,a=0,e=arguments.length;a<e;a++)s=c.indexOf(arguments[a]),s!==-1&&(o=!0,c.splice(s,1),this._onObjectRemoved&&this._onObjectRemoved(arguments[a]));return this.renderOnAddRemove&&o&&this.requestRenderAll(),this},forEachObject:function(c,s){for(var o=this.getObjects(),a=0,e=o.length;a<e;a++)c.call(s,o[a],a,o);return this},getObjects:function(c){return typeof c=="undefined"?this._objects.concat():this._objects.filter(function(s){return s.type===c})},item:function(c){return this._objects[c]},isEmpty:function(){return this._objects.length===0},size:function(){return this._objects.length},contains:function(c,s){return this._objects.indexOf(c)>-1?!0:s?this._objects.some(function(o){return typeof o.contains=="function"&&o.contains(c,!0)}):!1},complexity:function(){return this._objects.reduce(function(c,s){return c+=s.complexity?s.complexity():0,c},0)}},f.CommonMethods={_setOptions:function(c){for(var s in c)this.set(s,c[s])},_initGradient:function(c,s){c&&c.colorStops&&!(c instanceof f.Gradient)&&this.set(s,new f.Gradient(c))},_initPattern:function(c,s,o){c&&c.source&&!(c instanceof f.Pattern)?this.set(s,new f.Pattern(c,o)):o&&o()},_setObject:function(c){for(var s in c)this._set(s,c[s])},set:function(c,s){return typeof c=="object"?this._setObject(c):this._set(c,s),this},_set:function(c,s){this[c]=s},toggle:function(c){var s=this.get(c);return typeof s=="boolean"&&this.set(c,!s),this},get:function(c){return this[c]}},function(c){var s=Math.sqrt,o=Math.atan2,a=Math.pow,e=Math.PI/180,i=Math.PI/2;f.util={cos:function(t){if(t===0)return 1;t<0&&(t=-t);var n=t/i;switch(n){case 1:case 3:return 0;case 2:return-1}return Math.cos(t)},sin:function(t){if(t===0)return 0;var n=t/i,h=1;switch(t<0&&(h=-1),n){case 1:return h;case 2:return 0;case 3:return-h}return Math.sin(t)},removeFromArray:function(t,n){var h=t.indexOf(n);return h!==-1&&t.splice(h,1),t},getRandomInt:function(t,n){return Math.floor(Math.random()*(n-t+1))+t},degreesToRadians:function(t){return t*e},radiansToDegrees:function(t){return t/e},rotatePoint:function(t,n,h){var r=new f.Point(t.x-n.x,t.y-n.y),l=f.util.rotateVector(r,h);return new f.Point(l.x,l.y).addEquals(n)},rotateVector:function(t,n){var h=f.util.sin(n),r=f.util.cos(n),l=t.x*r-t.y*h,u=t.x*h+t.y*r;return{x:l,y:u}},transformPoint:function(t,n,h){return h?new f.Point(n[0]*t.x+n[2]*t.y,n[1]*t.x+n[3]*t.y):new f.Point(n[0]*t.x+n[2]*t.y+n[4],n[1]*t.x+n[3]*t.y+n[5])},makeBoundingBoxFromPoints:function(t,n){if(n)for(var h=0;h<t.length;h++)t[h]=f.util.transformPoint(t[h],n);var r=[t[0].x,t[1].x,t[2].x,t[3].x],l=f.util.array.min(r),u=f.util.array.max(r),d=u-l,g=[t[0].y,t[1].y,t[2].y,t[3].y],m=f.util.array.min(g),v=f.util.array.max(g),y=v-m;return{left:l,top:m,width:d,height:y}},invertTransform:function(t){var n=1/(t[0]*t[3]-t[1]*t[2]),h=[n*t[3],-n*t[1],-n*t[2],n*t[0]],r=f.util.transformPoint({x:t[4],y:t[5]},h,!0);return h[4]=-r.x,h[5]=-r.y,h},toFixed:function(t,n){return parseFloat(Number(t).toFixed(n))},parseUnit:function(t,n){var h=/\D{0,2}$/.exec(t),r=parseFloat(t);switch(n||(n=f.Text.DEFAULT_SVG_FONT_SIZE),h[0]){case"mm":return r*f.DPI/25.4;case"cm":return r*f.DPI/2.54;case"in":return r*f.DPI;case"pt":return r*f.DPI/72;case"pc":return r*f.DPI/72*12;case"em":return r*n;default:return r}},falseFunction:function(){return!1},getKlass:function(t,n){return t=f.util.string.camelize(t.charAt(0).toUpperCase()+t.slice(1)),f.util.resolveNamespace(n)[t]},getSvgAttributes:function(t){var n=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":n=n.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);break;case"radialGradient":n=n.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);break;case"stop":n=n.concat(["offset","stop-color","stop-opacity"]);break}return n},resolveNamespace:function(t){if(!t)return f;var n=t.split("."),h=n.length,r,l=c||f.window;for(r=0;r<h;++r)l=l[n[r]];return l},loadImage:function(t,n,h,r){if(!t){n&&n.call(h,t);return}var l=f.util.createImage(),u=function(){n&&n.call(h,l,!1),l=l.onload=l.onerror=null};l.onload=u,l.onerror=function(){f.log("Error loading "+l.src),n&&n.call(h,null,!0),l=l.onload=l.onerror=null},t.indexOf("data")!==0&&r!==void 0&&r!==null&&(l.crossOrigin=r),t.substring(0,14)==="data:image/svg"&&(l.onload=null,f.util.loadImageInDom(l,u)),l.src=t},loadImageInDom:function(t,n){var h=f.document.createElement("div");h.style.width=h.style.height="1px",h.style.left=h.style.top="-100%",h.style.position="absolute",h.appendChild(t),f.document.querySelector("body").appendChild(h),t.onload=function(){n(),h.parentNode.removeChild(h),h=null}},enlivenObjects:function(t,n,h,r){t=t||[];var l=[],u=0,d=t.length;function g(){++u===d&&n&&n(l.filter(function(m){return m}))}if(!d){n&&n(l);return}t.forEach(function(m,v){if(!m||!m.type){g();return}var y=f.util.getKlass(m.type,h);y.fromObject(m,function(T,M){M||(l[v]=T),r&&r(m,T,M),g()})})},enlivenPatterns:function(t,n){t=t||[];function h(){++l===u&&n&&n(r)}var r=[],l=0,u=t.length;if(!u){n&&n(r);return}t.forEach(function(d,g){d&&d.source?new f.Pattern(d,function(m){r[g]=m,h()}):(r[g]=d,h())})},groupSVGElements:function(t,n,h){var r;return t&&t.length===1?t[0]:(n&&(n.width&&n.height?n.centerPoint={x:n.width/2,y:n.height/2}:(delete n.width,delete n.height)),r=new f.Group(t,n),typeof h!="undefined"&&(r.sourcePath=h),r)},populateWithProperties:function(t,n,h){if(h&&Object.prototype.toString.call(h)==="[object Array]")for(var r=0,l=h.length;r<l;r++)h[r]in t&&(n[h[r]]=t[h[r]])},drawDashedLine:function(t,n,h,r,l,u){var d=r-n,g=l-h,m=s(d*d+g*g),v=o(g,d),y=u.length,T=0,M=!0;for(t.save(),t.translate(n,h),t.moveTo(0,0),t.rotate(v),n=0;m>n;)n+=u[T++%y],n>m&&(n=m),t[M?"lineTo":"moveTo"](n,0),M=!M;t.restore()},createCanvasElement:function(){return f.document.createElement("canvas")},copyCanvasElement:function(t){var n=f.util.createCanvasElement();return n.width=t.width,n.height=t.height,n.getContext("2d").drawImage(t,0,0),n},toDataURL:function(t,n,h){return t.toDataURL("image/"+n,h)},createImage:function(){return f.document.createElement("img")},multiplyTransformMatrices:function(t,n,h){return[t[0]*n[0]+t[2]*n[1],t[1]*n[0]+t[3]*n[1],t[0]*n[2]+t[2]*n[3],t[1]*n[2]+t[3]*n[3],h?0:t[0]*n[4]+t[2]*n[5]+t[4],h?0:t[1]*n[4]+t[3]*n[5]+t[5]]},qrDecompose:function(t){var n=o(t[1],t[0]),h=a(t[0],2)+a(t[1],2),r=s(h),l=(t[0]*t[3]-t[2]*t[1])/r,u=o(t[0]*t[2]+t[1]*t[3],h);return{angle:n/e,scaleX:r,scaleY:l,skewX:u/e,skewY:0,translateX:t[4],translateY:t[5]}},calcRotateMatrix:function(t){if(!t.angle)return f.iMatrix.concat();var n=f.util.degreesToRadians(t.angle),h=f.util.cos(n),r=f.util.sin(n);return[h,r,-r,h,0,0]},calcDimensionsMatrix:function(t){var n=typeof t.scaleX=="undefined"?1:t.scaleX,h=typeof t.scaleY=="undefined"?1:t.scaleY,r=[t.flipX?-n:n,0,0,t.flipY?-h:h,0,0],l=f.util.multiplyTransformMatrices,u=f.util.degreesToRadians;return t.skewX&&(r=l(r,[1,0,Math.tan(u(t.skewX)),1],!0)),t.skewY&&(r=l(r,[1,Math.tan(u(t.skewY)),0,1],!0)),r},composeMatrix:function(t){var n=[1,0,0,1,t.translateX||0,t.translateY||0],h=f.util.multiplyTransformMatrices;return t.angle&&(n=h(n,f.util.calcRotateMatrix(t))),(t.scaleX!==1||t.scaleY!==1||t.skewX||t.skewY||t.flipX||t.flipY)&&(n=h(n,f.util.calcDimensionsMatrix(t))),n},resetObjectTransform:function(t){t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},saveObjectTransform:function(t){return{scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}},isTransparent:function(t,n,h,r){r>0&&(n>r?n-=r:n=0,h>r?h-=r:h=0);var l=!0,u,d,g=t.getImageData(n,h,r*2||1,r*2||1),m=g.data.length;for(u=3;u<m&&(d=g.data[u],l=d<=0,l!==!1);u+=4);return g=null,l},parsePreserveAspectRatioAttribute:function(t){var n="meet",h="Mid",r="Mid",l=t.split(" "),u;return l&&l.length&&(n=l.pop(),n!=="meet"&&n!=="slice"?(u=n,n="meet"):l.length&&(u=l.pop())),h=u!=="none"?u.slice(1,4):"none",r=u!=="none"?u.slice(5,8):"none",{meetOrSlice:n,alignX:h,alignY:r}},clearFabricFontCache:function(t){t=(t||"").toLowerCase(),t?f.charWidthsCache[t]&&delete f.charWidthsCache[t]:f.charWidthsCache={}},limitDimsByArea:function(t,n){var h=Math.sqrt(n*t),r=Math.floor(n/h);return{x:Math.floor(h),y:r}},capValue:function(t,n,h){return Math.max(t,Math.min(n,h))},findScaleToFit:function(t,n){return Math.min(n.width/t.width,n.height/t.height)},findScaleToCover:function(t,n){return Math.max(n.width/t.width,n.height/t.height)},matrixToSVG:function(t){return"matrix("+t.map(function(n){return f.util.toFixed(n,f.Object.NUM_FRACTION_DIGITS)}).join(" ")+")"},removeTransformFromObject:function(t,n){var h=f.util.invertTransform(n),r=f.util.multiplyTransformMatrices(h,t.calcOwnMatrix());f.util.applyTransformToObject(t,r)},addTransformToObject:function(t,n){f.util.applyTransformToObject(t,f.util.multiplyTransformMatrices(n,t.calcOwnMatrix()))},applyTransformToObject:function(t,n){var h=f.util.qrDecompose(n),r=new f.Point(h.translateX,h.translateY);t.flipX=!1,t.flipY=!1,t.set("scaleX",h.scaleX),t.set("scaleY",h.scaleY),t.skewX=h.skewX,t.skewY=h.skewY,t.angle=h.angle,t.setPositionByOrigin(r,"center","center")},sizeAfterTransform:function(t,n,h){var r=t/2,l=n/2,u=[{x:-r,y:-l},{x:r,y:-l},{x:-r,y:l},{x:r,y:l}],d=f.util.calcDimensionsMatrix(h),g=f.util.makeBoundingBoxFromPoints(u,d);return{x:g.width,y:g.height}}}}(et),function(){var c=Array.prototype.join,s={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},o={m:"l",M:"L"};function a(b,w,P,p,_,x,C,A,E,R,j){var Y=f.util.cos(b),D=f.util.sin(b),O=f.util.cos(w),k=f.util.sin(w),I=P*_*O-p*x*k+C,L=p*_*O+P*x*k+A,X=R+E*(-P*_*D-p*x*Y),z=j+E*(-p*_*D+P*x*Y),Z=I+E*(P*_*k+p*x*O),J=L+E*(p*_*k-P*x*O);return["C",X,z,Z,J,I,L]}function e(b,w,P,p,_,x,C){var A=Math.PI,E=C*A/180,R=f.util.sin(E),j=f.util.cos(E),Y=0,D=0;P=Math.abs(P),p=Math.abs(p);var O=-j*b*.5-R*w*.5,k=-j*w*.5+R*b*.5,I=P*P,L=p*p,X=k*k,z=O*O,Z=I*L-I*X-L*z,J=0;if(Z<0){var B=Math.sqrt(1-Z/(I*L));P*=B,p*=B}else J=(_===x?-1:1)*Math.sqrt(Z/(I*X+L*z));var q=J*P*k/p,tt=-J*p*O/P,it=j*q-R*tt+b*.5,at=R*q+j*tt+w*.5,nt=i(1,0,(O-q)/P,(k-tt)/p),st=i((O-q)/P,(k-tt)/p,(-O-q)/P,(-k-tt)/p);x===0&&st>0?st-=2*A:x===1&&st<0&&(st+=2*A);for(var rt=Math.ceil(Math.abs(st/A*2)),ot=[],lt=st/rt,ct=8/3*Math.sin(lt/4)*Math.sin(lt/4)/Math.sin(lt/2),ut=nt+lt,ht=0;ht<rt;ht++)ot[ht]=a(nt,ut,j,R,P,p,it,at,ct,Y,D),Y=ot[ht][5],D=ot[ht][6],nt=ut,ut+=lt;return ot}function i(b,w,P,p){var _=Math.atan2(w,b),x=Math.atan2(p,P);return x>=_?x-_:2*Math.PI-(_-x)}function t(b,w,P,p,_,x,C,A){var E;if(f.cachesBoundsOfCurve&&(E=c.call(arguments),f.boundsOfCurveCache[E]))return f.boundsOfCurveCache[E];var R=Math.sqrt,j=Math.min,Y=Math.max,D=Math.abs,O=[],k=[[],[]],I,L,X,z,Z,J,B,q;L=6*b-12*P+6*_,I=-3*b+9*P-9*_+3*C,X=3*P-3*b;for(var tt=0;tt<2;++tt){if(tt>0&&(L=6*w-12*p+6*x,I=-3*w+9*p-9*x+3*A,X=3*p-3*w),D(I)<1e-12){if(D(L)<1e-12)continue;z=-X/L,0<z&&z<1&&O.push(z);continue}B=L*L-4*X*I,!(B<0)&&(q=R(B),Z=(-L+q)/(2*I),0<Z&&Z<1&&O.push(Z),J=(-L-q)/(2*I),0<J&&J<1&&O.push(J))}for(var it,at,nt=O.length,st=nt,rt;nt--;)z=O[nt],rt=1-z,it=rt*rt*rt*b+3*rt*rt*z*P+3*rt*z*z*_+z*z*z*C,k[0][nt]=it,at=rt*rt*rt*w+3*rt*rt*z*p+3*rt*z*z*x+z*z*z*A,k[1][nt]=at;k[0][st]=b,k[1][st]=w,k[0][st+1]=C,k[1][st+1]=A;var ot=[{x:j.apply(null,k[0]),y:j.apply(null,k[1])},{x:Y.apply(null,k[0]),y:Y.apply(null,k[1])}];return f.cachesBoundsOfCurve&&(f.boundsOfCurveCache[E]=ot),ot}function n(b,w,P){for(var p=P[1],_=P[2],x=P[3],C=P[4],A=P[5],E=P[6],R=P[7],j=e(E-b,R-w,p,_,C,A,x),Y=0,D=j.length;Y<D;Y++)j[Y][1]+=b,j[Y][2]+=w,j[Y][3]+=b,j[Y][4]+=w,j[Y][5]+=b,j[Y][6]+=w;return j}function h(b){var w=0,P=0,p=b.length,_=0,x=0,C,A,E,R=[],j,Y,D;for(A=0;A<p;++A){switch(E=!1,C=b[A].slice(0),C[0]){case"l":C[0]="L",C[1]+=w,C[2]+=P;case"L":w=C[1],P=C[2];break;case"h":C[1]+=w;case"H":C[0]="L",C[2]=P,w=C[1];break;case"v":C[1]+=P;case"V":C[0]="L",P=C[1],C[1]=w,C[2]=P;break;case"m":C[0]="M",C[1]+=w,C[2]+=P;case"M":w=C[1],P=C[2],_=C[1],x=C[2];break;case"c":C[0]="C",C[1]+=w,C[2]+=P,C[3]+=w,C[4]+=P,C[5]+=w,C[6]+=P;case"C":Y=C[3],D=C[4],w=C[5],P=C[6];break;case"s":C[0]="S",C[1]+=w,C[2]+=P,C[3]+=w,C[4]+=P;case"S":j==="C"?(Y=2*w-Y,D=2*P-D):(Y=w,D=P),w=C[3],P=C[4],C[0]="C",C[5]=C[3],C[6]=C[4],C[3]=C[1],C[4]=C[2],C[1]=Y,C[2]=D,Y=C[3],D=C[4];break;case"q":C[0]="Q",C[1]+=w,C[2]+=P,C[3]+=w,C[4]+=P;case"Q":Y=C[1],D=C[2],w=C[3],P=C[4];break;case"t":C[0]="T",C[1]+=w,C[2]+=P;case"T":j==="Q"?(Y=2*w-Y,D=2*P-D):(Y=w,D=P),C[0]="Q",w=C[1],P=C[2],C[1]=Y,C[2]=D,C[3]=w,C[4]=P;break;case"a":C[0]="A",C[6]+=w,C[7]+=P;case"A":E=!0,R=R.concat(n(w,P,C)),w=C[6],P=C[7];break;case"z":case"Z":w=_,P=x;break}E||R.push(C),j=C[0]}return R}function r(b,w,P,p){return Math.sqrt((P-b)*(P-b)+(p-w)*(p-w))}function l(b){return b*b*b}function u(b){return 3*b*b*(1-b)}function d(b){return 3*b*(1-b)*(1-b)}function g(b){return(1-b)*(1-b)*(1-b)}function m(b,w,P,p,_,x,C,A){return function(E){var R=l(E),j=u(E),Y=d(E),D=g(E);return{x:C*R+_*j+P*Y+b*D,y:A*R+x*j+p*Y+w*D}}}function v(b,w,P,p,_,x,C,A){return function(E){var R=1-E,j=3*R*R*(P-b)+6*R*E*(_-P)+3*E*E*(C-_),Y=3*R*R*(p-w)+6*R*E*(x-p)+3*E*E*(A-x);return Math.atan2(Y,j)}}function y(b){return b*b}function T(b){return 2*b*(1-b)}function M(b){return(1-b)*(1-b)}function W(b,w,P,p,_,x){return function(C){var A=y(C),E=T(C),R=M(C);return{x:_*A+P*E+b*R,y:x*A+p*E+w*R}}}function H(b,w,P,p,_,x){return function(C){var A=1-C,E=2*A*(P-b)+2*C*(_-P),R=2*A*(p-w)+2*C*(x-p);return Math.atan2(R,E)}}function G(b,w,P){var p={x:w,y:P},_,x=0,C;for(C=1;C<=100;C+=1)_=b(C/100),x+=r(p.x,p.y,_.x,_.y),p=_;return x}function V(b,w){for(var P=0,p=0,_=b.iterator,x={x:b.x,y:b.y},C,A,E=.01,R=b.angleFinder,j;p<w&&P<=1&&E>1e-4;)C=_(P),j=P,A=r(x.x,x.y,C.x,C.y),A+p>w?(E/=2,P-=E):(x=C,P+=E,p+=A);return C.angle=R(j),C}function U(b){for(var w=0,P=b.length,p,_=0,x=0,C=0,A=0,E=[],R,j,Y,D=0;D<P;D++){switch(p=b[D],j={x:_,y:x,command:p[0]},p[0]){case"M":j.length=0,C=_=p[1],A=x=p[2];break;case"L":j.length=r(_,x,p[1],p[2]),_=p[1],x=p[2];break;case"C":R=m(_,x,p[1],p[2],p[3],p[4],p[5],p[6]),Y=v(_,x,p[1],p[2],p[3],p[4],p[5],p[6]),j.iterator=R,j.angleFinder=Y,j.length=G(R,_,x),_=p[5],x=p[6];break;case"Q":R=W(_,x,p[1],p[2],p[3],p[4]),Y=H(_,x,p[1],p[2],p[3],p[4]),j.iterator=R,j.angleFinder=Y,j.length=G(R,_,x),_=p[3],x=p[4];break;case"Z":case"z":j.destX=C,j.destY=A,j.length=r(_,x,C,A),_=C,x=A;break}w+=j.length,E.push(j)}return E.push({length:w,x:_,y:x}),E}function N(b,w,P){P||(P=U(b));for(var p=0;w-P[p].length>0&&p<P.length-2;)w-=P[p].length,p++;var _=P[p],x=w/_.length,C=_.command,A=b[p],E;switch(C){case"M":return{x:_.x,y:_.y,angle:0};case"Z":case"z":return E=new f.Point(_.x,_.y).lerp(new f.Point(_.destX,_.destY),x),E.angle=Math.atan2(_.destY-_.y,_.destX-_.x),E;case"L":return E=new f.Point(_.x,_.y).lerp(new f.Point(A[1],A[2]),x),E.angle=Math.atan2(A[2]-_.y,A[1]-_.x),E;case"C":return V(_,w);case"Q":return V(_,w)}}function Q(b){var w=[],P=[],p,_,x=f.rePathCommand,C="[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?\\s*",A="("+C+")"+f.commaWsp,E="([01])"+f.commaWsp+"?",R=A+"?"+A+"?"+A+E+E+A+"?("+C+")",j=new RegExp(R,"g"),Y,D,O;if(!b||!b.match)return w;O=b.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi);for(var k=0,I,L=O.length;k<L;k++){p=O[k],D=p.slice(1).trim(),P.length=0;var X=p.charAt(0);if(I=[X],X.toLowerCase()==="a")for(var z;z=j.exec(D);)for(var Z=1;Z<z.length;Z++)P.push(z[Z]);else for(;Y=x.exec(D);)P.push(Y[0]);for(var Z=0,J=P.length;Z<J;Z++)_=parseFloat(P[Z]),isNaN(_)||I.push(_);var B=s[X.toLowerCase()],q=o[X]||X;if(I.length-1>B)for(var tt=1,it=I.length;tt<it;tt+=B)w.push([X].concat(I.slice(tt,tt+B))),X=q;else w.push(I)}return w}function $(b,w){var P=[],p,_=new f.Point(b[0].x,b[0].y),x=new f.Point(b[1].x,b[1].y),C=b.length,A=1,E=0,R=C>2;for(w=w||0,R&&(A=b[2].x<x.x?-1:b[2].x===x.x?0:1,E=b[2].y<x.y?-1:b[2].y===x.y?0:1),P.push(["M",_.x-A*w,_.y-E*w]),p=1;p<C;p++){if(!_.eq(x)){var j=_.midPointFrom(x);P.push(["Q",_.x,_.y,j.x,j.y])}_=b[p],p+1<b.length&&(x=b[p+1])}return R&&(A=_.x>b[p-2].x?1:_.x===b[p-2].x?0:-1,E=_.y>b[p-2].y?1:_.y===b[p-2].y?0:-1),P.push(["L",_.x+A*w,_.y+E*w]),P}function K(b,w,P){return P&&(w=f.util.multiplyTransformMatrices(w,[1,0,0,1,-P.x,-P.y])),b.map(function(p){for(var _=p.slice(0),x={},C=1;C<p.length-1;C+=2)x.x=p[C],x.y=p[C+1],x=f.util.transformPoint(x,w),_[C]=x.x,_[C+1]=x.y;return _})}function S(b,w,P,p,_,x,C,A,E){for(var R=0,j=0,Y,D=[],O=e(A-b,E-w,P,p,x,C,_),k=0,I=O.length;k<I;k++)Y=t(R,j,O[k][1],O[k][2],O[k][3],O[k][4],O[k][5],O[k][6]),D.push({x:Y[0].x+b,y:Y[0].y+w}),D.push({x:Y[1].x+b,y:Y[1].y+w}),R=O[k][5],j=O[k][6];return D}function F(b,w,P,p){p=p.slice(0).unshift("X");var _=n(w,P,p);_.forEach(function(x){b.bezierCurveTo.apply(b,x.slice(1))})}f.util.joinPath=function(b){return b.map(function(w){return w.join(" ")}).join(" ")},f.util.parsePath=Q,f.util.makePathSimpler=h,f.util.getSmoothPathFromPoints=$,f.util.getPathSegmentsInfo=U,f.util.getBoundsOfCurve=t,f.util.getPointOnPath=N,f.util.transformPath=K,f.util.fromArcToBeizers=n,f.util.getBoundsOfArc=S,f.util.drawArc=F}(),function(){var c=Array.prototype.slice;function s(t,n){for(var h=c.call(arguments,2),r=[],l=0,u=t.length;l<u;l++)r[l]=h.length?t[l][n].apply(t[l],h):t[l][n].call(t[l]);return r}function o(t,n){return i(t,n,function(h,r){return h>=r})}function a(t,n){return i(t,n,function(h,r){return h<r})}function e(t,n){for(var h=t.length;h--;)t[h]=n;return t}function i(t,n,h){if(!(!t||t.length===0)){var r=t.length-1,l=n?t[r][n]:t[r];if(n)for(;r--;)h(t[r][n],l)&&(l=t[r][n]);else for(;r--;)h(t[r],l)&&(l=t[r]);return l}}f.util.array={fill:e,invoke:s,min:a,max:o}}(),function(){function c(o,a,e){if(e)if(!f.isLikelyNode&&a instanceof Element)o=a;else if(a instanceof Array){o=[];for(var i=0,t=a.length;i<t;i++)o[i]=c({},a[i],e)}else if(a&&typeof a=="object")for(var n in a)n==="canvas"||n==="group"?o[n]=null:a.hasOwnProperty(n)&&(o[n]=c({},a[n],e));else o=a;else for(var n in a)o[n]=a[n];return o}function s(o,a){return c({},o,a)}f.util.object={extend:c,clone:s},f.util.object.extend(f.util,f.Observable)}(),function(){function c(i){return i.replace(/-+(.)?/g,function(t,n){return n?n.toUpperCase():""})}function s(i,t){return i.charAt(0).toUpperCase()+(t?i.slice(1):i.slice(1).toLowerCase())}function o(i){return i.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function a(i){var t=0,n,h=[];for(t=0,n;t<i.length;t++)(n=e(i,t))!==!1&&h.push(n);return h}function e(i,t){var n=i.charCodeAt(t);if(isNaN(n))return"";if(n<55296||n>57343)return i.charAt(t);if(55296<=n&&n<=56319){if(i.length<=t+1)throw"High surrogate without following low surrogate";var h=i.charCodeAt(t+1);if(56320>h||h>57343)throw"High surrogate without following low surrogate";return i.charAt(t)+i.charAt(t+1)}if(t===0)throw"Low surrogate without preceding high surrogate";var r=i.charCodeAt(t-1);if(55296>r||r>56319)throw"Low surrogate without preceding high surrogate";return!1}f.util.string={camelize:c,capitalize:s,escapeXml:o,graphemeSplit:a}}(),function(){var c=Array.prototype.slice,s=function(){},o=function(){for(var n in{toString:1})if(n==="toString")return!1;return!0}(),a=function(n,h,r){for(var l in h)l in n.prototype&&typeof n.prototype[l]=="function"&&(h[l]+"").indexOf("callSuper")>-1?n.prototype[l]=function(u){return function(){var d=this.constructor.superclass;this.constructor.superclass=r;var g=h[u].apply(this,arguments);if(this.constructor.superclass=d,u!=="initialize")return g}}(l):n.prototype[l]=h[l],o&&(h.toString!==Object.prototype.toString&&(n.prototype.toString=h.toString),h.valueOf!==Object.prototype.valueOf&&(n.prototype.valueOf=h.valueOf))};function e(){}function i(n){for(var h=null,r=this;r.constructor.superclass;){var l=r.constructor.superclass.prototype[n];if(r[n]!==l){h=l;break}r=r.constructor.superclass.prototype}return h?arguments.length>1?h.apply(this,c.call(arguments,1)):h.call(this):console.log("tried to callSuper "+n+", method not found in prototype chain",this)}function t(){var n=null,h=c.call(arguments,0);typeof h[0]=="function"&&(n=h.shift());function r(){this.initialize.apply(this,arguments)}r.superclass=n,r.subclasses=[],n&&(e.prototype=n.prototype,r.prototype=new e,n.subclasses.push(r));for(var l=0,u=h.length;l<u;l++)a(r,h[l],n);return r.prototype.initialize||(r.prototype.initialize=s),r.prototype.constructor=r,r.prototype.callSuper=i,r}f.util.createClass=t}(),function(){var c=!!f.document.createElement("div").attachEvent,s=["touchstart","touchmove","touchend"];f.util.addListener=function(a,e,i,t){a&&a.addEventListener(e,i,c?!1:t)},f.util.removeListener=function(a,e,i,t){a&&a.removeEventListener(e,i,c?!1:t)};function o(a){var e=a.changedTouches;return e&&e[0]?e[0]:a}f.util.getPointer=function(a){var e=a.target,i=f.util.getScrollLeftTop(e),t=o(a);return{x:t.clientX+i.left,y:t.clientY+i.top}},f.util.isTouchEvent=function(a){return s.indexOf(a.type)>-1||a.pointerType==="touch"}}(),function(){function c(t,n){var h=t.style;if(!h)return t;if(typeof n=="string")return t.style.cssText+=";"+n,n.indexOf("opacity")>-1?i(t,n.match(/opacity:\s*(\d?\.?\d*)/)[1]):t;for(var r in n)if(r==="opacity")i(t,n[r]);else{var l=r==="float"||r==="cssFloat"?typeof h.styleFloat=="undefined"?"cssFloat":"styleFloat":r;h[l]=n[r]}return t}var s=f.document.createElement("div"),o=typeof s.style.opacity=="string",a=typeof s.style.filter=="string",e=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,i=function(t){return t};o?i=function(t,n){return t.style.opacity=n,t}:a&&(i=function(t,n){var h=t.style;return t.currentStyle&&!t.currentStyle.hasLayout&&(h.zoom=1),e.test(h.filter)?(n=n>=.9999?"":"alpha(opacity="+n*100+")",h.filter=h.filter.replace(e,n)):h.filter+=" alpha(opacity="+n*100+")",t}),f.util.setStyle=c}(),function(){var c=Array.prototype.slice;function s(g){return typeof g=="string"?f.document.getElementById(g):g}var o,a=function(g){return c.call(g,0)};try{o=a(f.document.childNodes)instanceof Array}catch(g){}o||(a=function(g){for(var m=new Array(g.length),v=g.length;v--;)m[v]=g[v];return m});function e(g,m){var v=f.document.createElement(g);for(var y in m)y==="class"?v.className=m[y]:y==="for"?v.htmlFor=m[y]:v.setAttribute(y,m[y]);return v}function i(g,m){g&&(" "+g.className+" ").indexOf(" "+m+" ")===-1&&(g.className+=(g.className?" ":"")+m)}function t(g,m,v){return typeof m=="string"&&(m=e(m,v)),g.parentNode&&g.parentNode.replaceChild(m,g),m.appendChild(g),m}function n(g){for(var m=0,v=0,y=f.document.documentElement,T=f.document.body||{scrollLeft:0,scrollTop:0};g&&(g.parentNode||g.host)&&(g=g.parentNode||g.host,g===f.document?(m=T.scrollLeft||y.scrollLeft||0,v=T.scrollTop||y.scrollTop||0):(m+=g.scrollLeft||0,v+=g.scrollTop||0),!(g.nodeType===1&&g.style.position==="fixed")););return{left:m,top:v}}function h(g){var m,v=g&&g.ownerDocument,y={left:0,top:0},T={left:0,top:0},M,W={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!v)return T;for(var H in W)T[W[H]]+=parseInt(r(g,H),10)||0;return m=v.documentElement,typeof g.getBoundingClientRect!="undefined"&&(y=g.getBoundingClientRect()),M=n(g),{left:y.left+M.left-(m.clientLeft||0)+T.left,top:y.top+M.top-(m.clientTop||0)+T.top}}var r;f.document.defaultView&&f.document.defaultView.getComputedStyle?r=function(g,m){var v=f.document.defaultView.getComputedStyle(g,null);return v?v[m]:void 0}:r=function(g,m){var v=g.style[m];return!v&&g.currentStyle&&(v=g.currentStyle[m]),v},function(){var g=f.document.documentElement.style,m="userSelect"in g?"userSelect":"MozUserSelect"in g?"MozUserSelect":"WebkitUserSelect"in g?"WebkitUserSelect":"KhtmlUserSelect"in g?"KhtmlUserSelect":"";function v(T){return typeof T.onselectstart!="undefined"&&(T.onselectstart=f.util.falseFunction),m?T.style[m]="none":typeof T.unselectable=="string"&&(T.unselectable="on"),T}function y(T){return typeof T.onselectstart!="undefined"&&(T.onselectstart=null),m?T.style[m]="":typeof T.unselectable=="string"&&(T.unselectable=""),T}f.util.makeElementUnselectable=v,f.util.makeElementSelectable=y}();function l(g){var m=f.jsdomImplForWrapper(g);return m._canvas||m._image}function u(g){if(f.isLikelyNode){var m=f.jsdomImplForWrapper(g);m&&(m._image=null,m._canvas=null,m._currentSrc=null,m._attributes=null,m._classList=null)}}function d(g,m){g.imageSmoothingEnabled=g.imageSmoothingEnabled||g.webkitImageSmoothingEnabled||g.mozImageSmoothingEnabled||g.msImageSmoothingEnabled||g.oImageSmoothingEnabled,g.imageSmoothingEnabled=m}f.util.setImageSmoothing=d,f.util.getById=s,f.util.toArray=a,f.util.addClass=i,f.util.makeElement=e,f.util.wrapElement=t,f.util.getScrollLeftTop=n,f.util.getElementOffset=h,f.util.getNodeCanvas=l,f.util.cleanUpJsdomNode=u}(),function(){function c(a,e){return a+(/\?/.test(a)?"&":"?")+e}function s(){}function o(a,e){e||(e={});var i=e.method?e.method.toUpperCase():"GET",t=e.onComplete||function(){},n=new f.window.XMLHttpRequest,h=e.body||e.parameters;return n.onreadystatechange=function(){n.readyState===4&&(t(n),n.onreadystatechange=s)},i==="GET"&&(h=null,typeof e.parameters=="string"&&(a=c(a,e.parameters))),n.open(i,a,!0),(i==="POST"||i==="PUT")&&n.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.send(h),n}f.util.request=o}(),f.log=console.log,f.warn=console.warn,function(){function c(){return!1}function s(n,h,r,l){return-r*Math.cos(n/l*(Math.PI/2))+r+h}function o(n){var h=!1;return i(function(r){n||(n={});var l=r||+new Date,u=n.duration||500,d=l+u,g,m=n.onChange||c,v=n.abort||c,y=n.onComplete||c,T=n.easing||s,M="startValue"in n?n.startValue:0,W="endValue"in n?n.endValue:100,H=n.byValue||W-M;n.onStart&&n.onStart(),function G(V){g=V||+new Date;var U=g>d?u:g-l,N=U/u,Q=T(U,M,H,u),$=Math.abs((Q-M)/H);if(!h){if(v(Q,$,N)){y(W,1,1);return}if(g>d){m(W,1,1),y(W,1,1);return}else m(Q,$,N),i(G)}}(l)}),function(){h=!0}}var a=f.window.requestAnimationFrame||f.window.webkitRequestAnimationFrame||f.window.mozRequestAnimationFrame||f.window.oRequestAnimationFrame||f.window.msRequestAnimationFrame||function(n){return f.window.setTimeout(n,1e3/60)},e=f.window.cancelAnimationFrame||f.window.clearTimeout;function i(){return a.apply(f.window,arguments)}function t(){return e.apply(f.window,arguments)}f.util.animate=o,f.util.requestAnimFrame=i,f.util.cancelAnimFrame=t}(),function(){function c(o,a,e){var i="rgba("+parseInt(o[0]+e*(a[0]-o[0]),10)+","+parseInt(o[1]+e*(a[1]-o[1]),10)+","+parseInt(o[2]+e*(a[2]-o[2]),10);return i+=","+(o&&a?parseFloat(o[3]+e*(a[3]-o[3])):1),i+=")",i}function s(o,a,e,i){var t=new f.Color(o).getSource(),n=new f.Color(a).getSource(),h=i.onComplete,r=i.onChange;return i=i||{},f.util.animate(f.util.object.extend(i,{duration:e||500,startValue:t,endValue:n,byValue:n,easing:function(l,u,d,g){var m=i.colorEasing?i.colorEasing(l,g):1-Math.cos(l/g*(Math.PI/2));return c(u,d,m)},onComplete:function(l,u,d){if(h)return h(c(n,n,0),u,d)},onChange:function(l,u,d){if(r){if(Array.isArray(l))return r(c(l,l,0),u,d);r(l,u,d)}}}))}f.util.animateColor=s}(),function(){function c(S,F,b,w){return S<Math.abs(F)?(S=F,w=b/4):F===0&&S===0?w=b/(2*Math.PI)*Math.asin(1):w=b/(2*Math.PI)*Math.asin(F/S),{a:S,c:F,p:b,s:w}}function s(S,F,b){return S.a*Math.pow(2,10*(F-=1))*Math.sin((F*b-S.s)*(2*Math.PI)/S.p)}function o(S,F,b,w){return b*((S=S/w-1)*S*S+1)+F}function a(S,F,b,w){return S/=w/2,S<1?b/2*S*S*S+F:b/2*((S-=2)*S*S+2)+F}function e(S,F,b,w){return b*(S/=w)*S*S*S+F}function i(S,F,b,w){return-b*((S=S/w-1)*S*S*S-1)+F}function t(S,F,b,w){return S/=w/2,S<1?b/2*S*S*S*S+F:-b/2*((S-=2)*S*S*S-2)+F}function n(S,F,b,w){return b*(S/=w)*S*S*S*S+F}function h(S,F,b,w){return b*((S=S/w-1)*S*S*S*S+1)+F}function r(S,F,b,w){return S/=w/2,S<1?b/2*S*S*S*S*S+F:b/2*((S-=2)*S*S*S*S+2)+F}function l(S,F,b,w){return-b*Math.cos(S/w*(Math.PI/2))+b+F}function u(S,F,b,w){return b*Math.sin(S/w*(Math.PI/2))+F}function d(S,F,b,w){return-b/2*(Math.cos(Math.PI*S/w)-1)+F}function g(S,F,b,w){return S===0?F:b*Math.pow(2,10*(S/w-1))+F}function m(S,F,b,w){return S===w?F+b:b*(-Math.pow(2,-10*S/w)+1)+F}function v(S,F,b,w){return S===0?F:S===w?F+b:(S/=w/2,S<1?b/2*Math.pow(2,10*(S-1))+F:b/2*(-Math.pow(2,-10*--S)+2)+F)}function y(S,F,b,w){return-b*(Math.sqrt(1-(S/=w)*S)-1)+F}function T(S,F,b,w){return b*Math.sqrt(1-(S=S/w-1)*S)+F}function M(S,F,b,w){return S/=w/2,S<1?-b/2*(Math.sqrt(1-S*S)-1)+F:b/2*(Math.sqrt(1-(S-=2)*S)+1)+F}function W(S,F,b,w){var P=1.70158,p=0,_=b;if(S===0)return F;if(S/=w,S===1)return F+b;p||(p=w*.3);var x=c(_,b,p,P);return-s(x,S,w)+F}function H(S,F,b,w){var P=1.70158,p=0,_=b;if(S===0)return F;if(S/=w,S===1)return F+b;p||(p=w*.3);var x=c(_,b,p,P);return x.a*Math.pow(2,-10*S)*Math.sin((S*w-x.s)*(2*Math.PI)/x.p)+x.c+F}function G(S,F,b,w){var P=1.70158,p=0,_=b;if(S===0)return F;if(S/=w/2,S===2)return F+b;p||(p=w*(.3*1.5));var x=c(_,b,p,P);return S<1?-.5*s(x,S,w)+F:x.a*Math.pow(2,-10*(S-=1))*Math.sin((S*w-x.s)*(2*Math.PI)/x.p)*.5+x.c+F}function V(S,F,b,w,P){return P===void 0&&(P=1.70158),b*(S/=w)*S*((P+1)*S-P)+F}function U(S,F,b,w,P){return P===void 0&&(P=1.70158),b*((S=S/w-1)*S*((P+1)*S+P)+1)+F}function N(S,F,b,w,P){return P===void 0&&(P=1.70158),S/=w/2,S<1?b/2*(S*S*(((P*=1.525)+1)*S-P))+F:b/2*((S-=2)*S*(((P*=1.525)+1)*S+P)+2)+F}function Q(S,F,b,w){return b-$(w-S,0,b,w)+F}function $(S,F,b,w){return(S/=w)<1/2.75?b*(7.5625*S*S)+F:S<2/2.75?b*(7.5625*(S-=1.5/2.75)*S+.75)+F:S<2.5/2.75?b*(7.5625*(S-=2.25/2.75)*S+.9375)+F:b*(7.5625*(S-=2.625/2.75)*S+.984375)+F}function K(S,F,b,w){return S<w/2?Q(S*2,0,b,w)*.5+F:$(S*2-w,0,b,w)*.5+b*.5+F}f.util.ease={easeInQuad:function(S,F,b,w){return b*(S/=w)*S+F},easeOutQuad:function(S,F,b,w){return-b*(S/=w)*(S-2)+F},easeInOutQuad:function(S,F,b,w){return S/=w/2,S<1?b/2*S*S+F:-b/2*(--S*(S-2)-1)+F},easeInCubic:function(S,F,b,w){return b*(S/=w)*S*S+F},easeOutCubic:o,easeInOutCubic:a,easeInQuart:e,easeOutQuart:i,easeInOutQuart:t,easeInQuint:n,easeOutQuint:h,easeInOutQuint:r,easeInSine:l,easeOutSine:u,easeInOutSine:d,easeInExpo:g,easeOutExpo:m,easeInOutExpo:v,easeInCirc:y,easeOutCirc:T,easeInOutCirc:M,easeInElastic:W,easeOutElastic:H,easeInOutElastic:G,easeInBack:V,easeOutBack:U,easeInOutBack:N,easeInBounce:Q,easeOutBounce:$,easeInOutBounce:K}}(),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.util.object.clone,e=s.util.toFixed,i=s.util.parseUnit,t=s.util.multiplyTransformMatrices,n=["path","circle","polygon","polyline","ellipse","rect","line","image","text"],h=["symbol","image","marker","pattern","view","svg"],r=["pattern","defs","symbol","metadata","clipPath","mask","desc"],l=["symbol","g","a","svg","clipPath","defs"],u={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},d={stroke:"strokeOpacity",fill:"fillOpacity"},g="font-size",m="clip-path";s.svgValidTagNamesRegEx=T(n),s.svgViewBoxElementsRegEx=T(h),s.svgInvalidAncestorsRegEx=T(r),s.svgValidParentsRegEx=T(l),s.cssRules={},s.gradientDefs={},s.clipPaths={};function v(p){return p in u?u[p]:p}function y(p,_,x,C){var A=Object.prototype.toString.call(_)==="[object Array]",E;if((p==="fill"||p==="stroke")&&_==="none")_="";else{if(p==="strokeUniform")return _==="non-scaling-stroke";if(p==="strokeDashArray")_==="none"?_=null:_=_.replace(/,/g," ").split(/\s+/).map(parseFloat);else if(p==="transformMatrix")x&&x.transformMatrix?_=t(x.transformMatrix,s.parseTransformAttribute(_)):_=s.parseTransformAttribute(_);else if(p==="visible")_=_!=="none"&&_!=="hidden",x&&x.visible===!1&&(_=!1);else if(p==="opacity")_=parseFloat(_),x&&typeof x.opacity!="undefined"&&(_*=x.opacity);else if(p==="textAnchor")_=_==="start"?"left":_==="end"?"right":"center";else if(p==="charSpacing")E=i(_,C)/C*1e3;else if(p==="paintFirst"){var R=_.indexOf("fill"),j=_.indexOf("stroke"),_="fill";(R>-1&&j>-1&&j<R||R===-1&&j>-1)&&(_="stroke")}else{if(p==="href"||p==="xlink:href"||p==="font")return _;if(p==="imageSmoothing")return _==="optimizeQuality";E=A?_.map(i):i(_,C)}}return!A&&isNaN(E)?_:E}function T(p){return new RegExp("^("+p.join("|")+")\\b","i")}function M(p){for(var _ in d)if(!(typeof p[d[_]]=="undefined"||p[_]==="")){if(typeof p[_]=="undefined"){if(!s.Object.prototype[_])continue;p[_]=s.Object.prototype[_]}if(p[_].indexOf("url(")!==0){var x=new s.Color(p[_]);p[_]=x.setAlpha(e(x.getAlpha()*p[d[_]],2)).toRgba()}}return p}function W(p,_){var x,C=[],A,E,R;for(E=0,R=_.length;E<R;E++)x=_[E],A=p.getElementsByTagName(x),C=C.concat(Array.prototype.slice.call(A));return C}s.parseTransformAttribute=function(){function p(B,q){var tt=s.util.cos(q[0]),it=s.util.sin(q[0]),at=0,nt=0;q.length===3&&(at=q[1],nt=q[2]),B[0]=tt,B[1]=it,B[2]=-it,B[3]=tt,B[4]=at-(tt*at-it*nt),B[5]=nt-(it*at+tt*nt)}function _(B,q){var tt=q[0],it=q.length===2?q[1]:q[0];B[0]=tt,B[3]=it}function x(B,q,tt){B[tt]=Math.tan(s.util.degreesToRadians(q[0]))}function C(B,q){B[4]=q[0],q.length===2&&(B[5]=q[1])}var A=s.iMatrix,E=s.reNum,R=s.commaWsp,j="(?:(skewX)\\s*\\(\\s*("+E+")\\s*\\))",Y="(?:(skewY)\\s*\\(\\s*("+E+")\\s*\\))",D="(?:(rotate)\\s*\\(\\s*("+E+")(?:"+R+"("+E+")"+R+"("+E+"))?\\s*\\))",O="(?:(scale)\\s*\\(\\s*("+E+")(?:"+R+"("+E+"))?\\s*\\))",k="(?:(translate)\\s*\\(\\s*("+E+")(?:"+R+"("+E+"))?\\s*\\))",I="(?:(matrix)\\s*\\(\\s*("+E+")"+R+"("+E+")"+R+"("+E+")"+R+"("+E+")"+R+"("+E+")"+R+"("+E+")\\s*\\))",L="(?:"+I+"|"+k+"|"+O+"|"+D+"|"+j+"|"+Y+")",X="(?:"+L+"(?:"+R+"*"+L+")*)",z="^\\s*(?:"+X+"?)\\s*$",Z=new RegExp(z),J=new RegExp(L,"g");return function(B){var q=A.concat(),tt=[];if(!B||B&&!Z.test(B))return q;B.replace(J,function(at){var nt=new RegExp(L).exec(at).filter(function(ot){return!!ot}),st=nt[1],rt=nt.slice(2).map(parseFloat);switch(st){case"translate":C(q,rt);break;case"rotate":rt[0]=s.util.degreesToRadians(rt[0]),p(q,rt);break;case"scale":_(q,rt);break;case"skewX":x(q,rt,2);break;case"skewY":x(q,rt,1);break;case"matrix":q=rt;break}tt.push(q.concat()),q=A.concat()});for(var it=tt[0];tt.length>1;)tt.shift(),it=s.util.multiplyTransformMatrices(it,tt[0]);return it}}();function H(p,_){var x,C;p.replace(/;\s*$/,"").split(";").forEach(function(A){var E=A.split(":");x=E[0].trim().toLowerCase(),C=E[1].trim(),_[x]=C})}function G(p,_){var x,C;for(var A in p)typeof p[A]!="undefined"&&(x=A.toLowerCase(),C=p[A],_[x]=C)}function V(p,_){var x={};for(var C in s.cssRules[_])if(U(p,C.split(" ")))for(var A in s.cssRules[_][C])x[A]=s.cssRules[_][C][A];return x}function U(p,_){var x,C=!0;return x=Q(p,_.pop()),x&&_.length&&(C=N(p,_)),x&&C&&_.length===0}function N(p,_){for(var x,C=!0;p.parentNode&&p.parentNode.nodeType===1&&_.length;)C&&(x=_.pop()),p=p.parentNode,C=Q(p,x);return _.length===0}function Q(p,_){var x=p.nodeName,C=p.getAttribute("class"),A=p.getAttribute("id"),E,R;if(E=new RegExp("^"+x,"i"),_=_.replace(E,""),A&&_.length&&(E=new RegExp("#"+A+"(?![a-zA-Z\\-]+)","i"),_=_.replace(E,"")),C&&_.length)for(C=C.split(" "),R=C.length;R--;)E=new RegExp("\\."+C[R]+"(?![a-zA-Z\\-]+)","i"),_=_.replace(E,"");return _.length===0}function $(p,_){var x;if(p.getElementById&&(x=p.getElementById(_)),x)return x;var C,A,E,R=p.getElementsByTagName("*");for(A=0,E=R.length;A<E;A++)if(C=R[A],_===C.getAttribute("id"))return C}function K(p){for(var _=W(p,["use","svg:use"]),x=0;_.length&&x<_.length;){var C=_[x],A=C.getAttribute("xlink:href")||C.getAttribute("href");if(A===null)return;var E=A.substr(1),R=C.getAttribute("x")||0,j=C.getAttribute("y")||0,Y=$(p,E).cloneNode(!0),D=(Y.getAttribute("transform")||"")+" translate("+R+", "+j+")",O,k=_.length,I,L,X,z,Z=s.svgNS;if(F(Y),/^svg$/i.test(Y.nodeName)){var J=Y.ownerDocument.createElementNS(Z,"g");for(L=0,X=Y.attributes,z=X.length;L<z;L++)I=X.item(L),J.setAttributeNS(Z,I.nodeName,I.nodeValue);for(;Y.firstChild;)J.appendChild(Y.firstChild);Y=J}for(L=0,X=C.attributes,z=X.length;L<z;L++)I=X.item(L),!(I.nodeName==="x"||I.nodeName==="y"||I.nodeName==="xlink:href"||I.nodeName==="href")&&(I.nodeName==="transform"?D=I.nodeValue+" "+D:Y.setAttribute(I.nodeName,I.nodeValue));Y.setAttribute("transform",D),Y.setAttribute("instantiated_by_use","1"),Y.removeAttribute("id"),O=C.parentNode,O.replaceChild(Y,C),_.length===k&&x++}}var S=new RegExp("^\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*,?\\s*("+s.reNum+"+)\\s*$");function F(p){if(!s.svgViewBoxElementsRegEx.test(p.nodeName))return{};var _=p.getAttribute("viewBox"),x=1,C=1,A=0,E=0,R,j,Y,D,O=p.getAttribute("width"),k=p.getAttribute("height"),I=p.getAttribute("x")||0,L=p.getAttribute("y")||0,X=p.getAttribute("preserveAspectRatio")||"",z=!_||!(_=_.match(S)),Z=!O||!k||O==="100%"||k==="100%",J=z&&Z,B={},q="",tt=0,it=0;if(B.width=0,B.height=0,B.toBeParsed=J,z&&(I||L)&&p.parentNode&&p.parentNode.nodeName!=="#document"&&(q=" translate("+i(I)+" "+i(L)+") ",Y=(p.getAttribute("transform")||"")+q,p.setAttribute("transform",Y),p.removeAttribute("x"),p.removeAttribute("y")),J)return B;if(z)return B.width=i(O),B.height=i(k),B;if(A=-parseFloat(_[1]),E=-parseFloat(_[2]),R=parseFloat(_[3]),j=parseFloat(_[4]),B.minX=A,B.minY=E,B.viewBoxWidth=R,B.viewBoxHeight=j,Z?(B.width=R,B.height=j):(B.width=i(O),B.height=i(k),x=B.width/R,C=B.height/j),X=s.util.parsePreserveAspectRatioAttribute(X),X.alignX!=="none"&&(X.meetOrSlice==="meet"&&(C=x=x>C?C:x),X.meetOrSlice==="slice"&&(C=x=x>C?x:C),tt=B.width-R*x,it=B.height-j*x,X.alignX==="Mid"&&(tt/=2),X.alignY==="Mid"&&(it/=2),X.alignX==="Min"&&(tt=0),X.alignY==="Min"&&(it=0)),x===1&&C===1&&A===0&&E===0&&I===0&&L===0)return B;if((I||L)&&p.parentNode.nodeName!=="#document"&&(q=" translate("+i(I)+" "+i(L)+") "),Y=q+" matrix("+x+" 0 0 "+C+" "+(A*x+tt)+" "+(E*C+it)+") ",p.nodeName==="svg"){for(D=p.ownerDocument.createElementNS(s.svgNS,"g");p.firstChild;)D.appendChild(p.firstChild);p.appendChild(D)}else D=p,D.removeAttribute("x"),D.removeAttribute("y"),Y=D.getAttribute("transform")+Y;return D.setAttribute("transform",Y),B}function b(p,_){for(;p&&(p=p.parentNode);)if(p.nodeName&&_.test(p.nodeName.replace("svg:",""))&&!p.getAttribute("instantiated_by_use"))return!0;return!1}s.parseSVGDocument=function(p,_,x,C){if(p){K(p);var A=s.Object.__uid++,E,R,j=F(p),Y=s.util.toArray(p.getElementsByTagName("*"));if(j.crossOrigin=C&&C.crossOrigin,j.svgUid=A,Y.length===0&&s.isLikelyNode){Y=p.selectNodes('//*[name(.)!="svg"]');var D=[];for(E=0,R=Y.length;E<R;E++)D[E]=Y[E];Y=D}var O=Y.filter(function(I){return F(I),s.svgValidTagNamesRegEx.test(I.nodeName.replace("svg:",""))&&!b(I,s.svgInvalidAncestorsRegEx)});if(!O||O&&!O.length){_&&_([],{});return}var k={};Y.filter(function(I){return I.nodeName.replace("svg:","")==="clipPath"}).forEach(function(I){var L=I.getAttribute("id");k[L]=s.util.toArray(I.getElementsByTagName("*")).filter(function(X){return s.svgValidTagNamesRegEx.test(X.nodeName.replace("svg:",""))})}),s.gradientDefs[A]=s.getGradientDefs(p),s.cssRules[A]=s.getCSSRules(p),s.clipPaths[A]=k,s.parseElements(O,function(I,L){_&&(_(I,j,L,Y),delete s.gradientDefs[A],delete s.cssRules[A],delete s.clipPaths[A])},a(j),x,C)}};function w(p,_){var x=["gradientTransform","x1","x2","y1","y2","gradientUnits","cx","cy","r","fx","fy"],C="xlink:href",A=_.getAttribute(C).substr(1),E=$(p,A);if(E&&E.getAttribute(C)&&w(p,E),x.forEach(function(j){E&&!_.hasAttribute(j)&&E.hasAttribute(j)&&_.setAttribute(j,E.getAttribute(j))}),!_.children.length)for(var R=E.cloneNode(!0);R.firstChild;)_.appendChild(R.firstChild);_.removeAttribute(C)}var P=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+s.reNum+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+s.reNum+"))?\\s+(.*)");o(s,{parseFontDeclaration:function(p,_){var x=p.match(P);if(x){var C=x[1],A=x[3],E=x[4],R=x[5],j=x[6];C&&(_.fontStyle=C),A&&(_.fontWeight=isNaN(parseFloat(A))?A:parseFloat(A)),E&&(_.fontSize=i(E)),j&&(_.fontFamily=j),R&&(_.lineHeight=R==="normal"?1:R)}},getGradientDefs:function(p){var _=["linearGradient","radialGradient","svg:linearGradient","svg:radialGradient"],x=W(p,_),C,A=0,E={};for(A=x.length;A--;)C=x[A],C.getAttribute("xlink:href")&&w(p,C),E[C.getAttribute("id")]=C;return E},parseAttributes:function(p,_,x){if(p){var C,A={},E,R;typeof x=="undefined"&&(x=p.getAttribute("svgUid")),p.parentNode&&s.svgValidParentsRegEx.test(p.parentNode.nodeName)&&(A=s.parseAttributes(p.parentNode,_,x));var j=_.reduce(function(X,z){return C=p.getAttribute(z),C&&(X[z]=C),X},{}),Y=o(V(p,x),s.parseStyleAttribute(p));j=o(j,Y),Y[m]&&p.setAttribute(m,Y[m]),E=R=A.fontSize||s.Text.DEFAULT_SVG_FONT_SIZE,j[g]&&(j[g]=E=i(j[g],R));var D,O,k={};for(var I in j)D=v(I),O=y(D,j[I],A,E),k[D]=O;k&&k.font&&s.parseFontDeclaration(k.font,k);var L=o(A,k);return s.svgValidParentsRegEx.test(p.nodeName)?L:M(L)}},parseElements:function(p,_,x,C,A){new s.ElementsParser(p,_,x,C,A).parse()},parseStyleAttribute:function(p){var _={},x=p.getAttribute("style");return x&&(typeof x=="string"?H(x,_):G(x,_)),_},parsePointsAttribute:function(p){if(!p)return null;p=p.replace(/,/g," ").trim(),p=p.split(/\s+/);var _=[],x,C;for(x=0,C=p.length;x<C;x+=2)_.push({x:parseFloat(p[x]),y:parseFloat(p[x+1])});return _},getCSSRules:function(p){var _=p.getElementsByTagName("style"),x,C,A={},E;for(x=0,C=_.length;x<C;x++){var R=_[x].textContent;R=R.replace(/\/\*[\s\S]*?\*\//g,""),R.trim()!==""&&(E=R.match(/[^{]*\{[\s\S]*?\}/g),E=E.map(function(j){return j.trim()}),E.forEach(function(j){var Y=j.match(/([\s\S]*?)\s*\{([^}]*)\}/),D={},O=Y[2].trim(),k=O.replace(/;$/,"").split(/\s*;\s*/);for(x=0,C=k.length;x<C;x++){var I=k[x].split(/\s*:\s*/),L=I[0],X=I[1];D[L]=X}j=Y[1],j.split(",").forEach(function(z){z=z.replace(/^svg/i,"").trim(),z!==""&&(A[z]?s.util.object.extend(A[z],D):A[z]=s.util.object.clone(D))})}))}return A},loadSVGFromURL:function(p,_,x,C){p=p.replace(/^\n\s*/,"").trim(),new s.util.request(p,{method:"get",onComplete:A});function A(E){var R=E.responseXML;if(!R||!R.documentElement)return _&&_(null),!1;s.parseSVGDocument(R.documentElement,function(j,Y,D,O){_&&_(j,Y,D,O)},x,C)}},loadSVGFromString:function(p,_,x,C){var A=new s.window.DOMParser,E=A.parseFromString(p.trim(),"text/xml");s.parseSVGDocument(E.documentElement,function(R,j,Y,D){_(R,j,Y,D)},x,C)}})}(et),f.ElementsParser=function(c,s,o,a,e,i){this.elements=c,this.callback=s,this.options=o,this.reviver=a,this.svgUid=o&&o.svgUid||0,this.parsingOptions=e,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=i},function(c){c.parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},c.createObjects=function(){var s=this;this.elements.forEach(function(o,a){o.setAttribute("svgUid",s.svgUid),s.createObject(o,a)})},c.findTag=function(s){return f[f.util.string.capitalize(s.tagName.replace("svg:",""))]},c.createObject=function(s,o){var a=this.findTag(s);if(a&&a.fromElement)try{a.fromElement(s,this.createCallback(o,s),this.options)}catch(e){f.log(e)}else this.checkIfDone()},c.createCallback=function(s,o){var a=this;return function(e){var i;a.resolveGradient(e,o,"fill"),a.resolveGradient(e,o,"stroke"),e instanceof f.Image&&e._originalElement&&(i=e.parsePreserveAspectRatioAttribute(o)),e._removeTransformMatrix(i),a.resolveClipPath(e,o),a.reviver&&a.reviver(o,e),a.instances[s]=e,a.checkIfDone()}},c.extractPropertyDefinition=function(s,o,a){var e=s[o],i=this.regexUrl;if(i.test(e)){i.lastIndex=0;var t=i.exec(e)[1];return i.lastIndex=0,f[a][this.svgUid][t]}},c.resolveGradient=function(s,o,a){var e=this.extractPropertyDefinition(s,a,"gradientDefs");if(e){var i=o.getAttribute(a+"-opacity"),t=f.Gradient.fromElement(e,s,i,this.options);s.set(a,t)}},c.createClipPathCallback=function(s,o){return function(a){a._removeTransformMatrix(),a.fillRule=a.clipRule,o.push(a)}},c.resolveClipPath=function(s,o){var a=this.extractPropertyDefinition(s,"clipPath","clipPaths"),e,i,t,n,h,r;if(a){n=[],t=f.util.invertTransform(s.calcTransformMatrix());for(var l=a[0].parentNode,u=o;u.parentNode&&u.getAttribute("clip-path")!==s.clipPath;)u=u.parentNode;u.parentNode.appendChild(l);for(var d=0;d<a.length;d++)e=a[d],i=this.findTag(e),i.fromElement(e,this.createClipPathCallback(s,n),this.options);n.length===1?a=n[0]:a=new f.Group(n),h=f.util.multiplyTransformMatrices(t,a.calcTransformMatrix()),a.clipPath&&this.resolveClipPath(a,u);var r=f.util.qrDecompose(h);a.flipX=!1,a.flipY=!1,a.set("scaleX",r.scaleX),a.set("scaleY",r.scaleY),a.angle=r.angle,a.skewX=r.skewX,a.skewY=0,a.setPositionByOrigin({x:r.translateX,y:r.translateY},"center","center"),s.clipPath=a}else delete s.clipPath},c.checkIfDone=function(){--this.numElements===0&&(this.instances=this.instances.filter(function(s){return s!=null}),this.callback(this.instances,this.elements))}}(f.ElementsParser.prototype),function(c){var s=c.fabric||(c.fabric={});if(s.Point){s.warn("fabric.Point is already defined");return}s.Point=o;function o(a,e){this.x=a,this.y=e}o.prototype={type:"point",constructor:o,add:function(a){return new o(this.x+a.x,this.y+a.y)},addEquals:function(a){return this.x+=a.x,this.y+=a.y,this},scalarAdd:function(a){return new o(this.x+a,this.y+a)},scalarAddEquals:function(a){return this.x+=a,this.y+=a,this},subtract:function(a){return new o(this.x-a.x,this.y-a.y)},subtractEquals:function(a){return this.x-=a.x,this.y-=a.y,this},scalarSubtract:function(a){return new o(this.x-a,this.y-a)},scalarSubtractEquals:function(a){return this.x-=a,this.y-=a,this},multiply:function(a){return new o(this.x*a,this.y*a)},multiplyEquals:function(a){return this.x*=a,this.y*=a,this},divide:function(a){return new o(this.x/a,this.y/a)},divideEquals:function(a){return this.x/=a,this.y/=a,this},eq:function(a){return this.x===a.x&&this.y===a.y},lt:function(a){return this.x<a.x&&this.y<a.y},lte:function(a){return this.x<=a.x&&this.y<=a.y},gt:function(a){return this.x>a.x&&this.y>a.y},gte:function(a){return this.x>=a.x&&this.y>=a.y},lerp:function(a,e){return typeof e=="undefined"&&(e=.5),e=Math.max(Math.min(1,e),0),new o(this.x+(a.x-this.x)*e,this.y+(a.y-this.y)*e)},distanceFrom:function(a){var e=this.x-a.x,i=this.y-a.y;return Math.sqrt(e*e+i*i)},midPointFrom:function(a){return this.lerp(a)},min:function(a){return new o(Math.min(this.x,a.x),Math.min(this.y,a.y))},max:function(a){return new o(Math.max(this.x,a.x),Math.max(this.y,a.y))},toString:function(){return this.x+","+this.y},setXY:function(a,e){return this.x=a,this.y=e,this},setX:function(a){return this.x=a,this},setY:function(a){return this.y=a,this},setFromPoint:function(a){return this.x=a.x,this.y=a.y,this},swap:function(a){var e=this.x,i=this.y;this.x=a.x,this.y=a.y,a.x=e,a.y=i},clone:function(){return new o(this.x,this.y)}}}(et),function(c){var s=c.fabric||(c.fabric={});if(s.Intersection){s.warn("fabric.Intersection is already defined");return}function o(a){this.status=a,this.points=[]}s.Intersection=o,s.Intersection.prototype={constructor:o,appendPoint:function(a){return this.points.push(a),this},appendPoints:function(a){return this.points=this.points.concat(a),this}},s.Intersection.intersectLineLine=function(a,e,i,t){var n,h=(t.x-i.x)*(a.y-i.y)-(t.y-i.y)*(a.x-i.x),r=(e.x-a.x)*(a.y-i.y)-(e.y-a.y)*(a.x-i.x),l=(t.y-i.y)*(e.x-a.x)-(t.x-i.x)*(e.y-a.y);if(l!==0){var u=h/l,d=r/l;0<=u&&u<=1&&0<=d&&d<=1?(n=new o("Intersection"),n.appendPoint(new s.Point(a.x+u*(e.x-a.x),a.y+u*(e.y-a.y)))):n=new o}else h===0||r===0?n=new o("Coincident"):n=new o("Parallel");return n},s.Intersection.intersectLinePolygon=function(a,e,i){var t=new o,n=i.length,h,r,l,u;for(u=0;u<n;u++)h=i[u],r=i[(u+1)%n],l=o.intersectLineLine(a,e,h,r),t.appendPoints(l.points);return t.points.length>0&&(t.status="Intersection"),t},s.Intersection.intersectPolygonPolygon=function(a,e){var i=new o,t=a.length,n;for(n=0;n<t;n++){var h=a[n],r=a[(n+1)%t],l=o.intersectLinePolygon(h,r,e);i.appendPoints(l.points)}return i.points.length>0&&(i.status="Intersection"),i},s.Intersection.intersectPolygonRectangle=function(a,e,i){var t=e.min(i),n=e.max(i),h=new s.Point(n.x,t.y),r=new s.Point(t.x,n.y),l=o.intersectLinePolygon(t,h,a),u=o.intersectLinePolygon(h,n,a),d=o.intersectLinePolygon(n,r,a),g=o.intersectLinePolygon(r,t,a),m=new o;return m.appendPoints(l.points),m.appendPoints(u.points),m.appendPoints(d.points),m.appendPoints(g.points),m.points.length>0&&(m.status="Intersection"),m}}(et),function(c){var s=c.fabric||(c.fabric={});if(s.Color){s.warn("fabric.Color is already defined.");return}function o(e){e?this._tryParsingColor(e):this.setSource([0,0,0,1])}s.Color=o,s.Color.prototype={_tryParsingColor:function(e){var i;e in o.colorNameMap&&(e=o.colorNameMap[e]),e==="transparent"&&(i=[255,255,255,0]),i||(i=o.sourceFromHex(e)),i||(i=o.sourceFromRgb(e)),i||(i=o.sourceFromHsl(e)),i||(i=[0,0,0,1]),i&&this.setSource(i)},_rgbToHsl:function(e,i,t){e/=255,i/=255,t/=255;var n,h,r,l=s.util.array.max([e,i,t]),u=s.util.array.min([e,i,t]);if(r=(l+u)/2,l===u)n=h=0;else{var d=l-u;switch(h=r>.5?d/(2-l-u):d/(l+u),l){case e:n=(i-t)/d+(i<t?6:0);break;case i:n=(t-e)/d+2;break;case t:n=(e-i)/d+4;break}n/=6}return[Math.round(n*360),Math.round(h*100),Math.round(r*100)]},getSource:function(){return this._source},setSource:function(e){this._source=e},toRgb:function(){var e=this.getSource();return"rgb("+e[0]+","+e[1]+","+e[2]+")"},toRgba:function(){var e=this.getSource();return"rgba("+e[0]+","+e[1]+","+e[2]+","+e[3]+")"},toHsl:function(){var e=this.getSource(),i=this._rgbToHsl(e[0],e[1],e[2]);return"hsl("+i[0]+","+i[1]+"%,"+i[2]+"%)"},toHsla:function(){var e=this.getSource(),i=this._rgbToHsl(e[0],e[1],e[2]);return"hsla("+i[0]+","+i[1]+"%,"+i[2]+"%,"+e[3]+")"},toHex:function(){var e=this.getSource(),i,t,n;return i=e[0].toString(16),i=i.length===1?"0"+i:i,t=e[1].toString(16),t=t.length===1?"0"+t:t,n=e[2].toString(16),n=n.length===1?"0"+n:n,i.toUpperCase()+t.toUpperCase()+n.toUpperCase()},toHexa:function(){var e=this.getSource(),i;return i=Math.round(e[3]*255),i=i.toString(16),i=i.length===1?"0"+i:i,this.toHex()+i.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(e){var i=this.getSource();return i[3]=e,this.setSource(i),this},toGrayscale:function(){var e=this.getSource(),i=parseInt((e[0]*.3+e[1]*.59+e[2]*.11).toFixed(0),10),t=e[3];return this.setSource([i,i,i,t]),this},toBlackWhite:function(e){var i=this.getSource(),t=(i[0]*.3+i[1]*.59+i[2]*.11).toFixed(0),n=i[3];return e=e||127,t=Number(t)<Number(e)?0:255,this.setSource([t,t,t,n]),this},overlayWith:function(e){e instanceof o||(e=new o(e));var i=[],t=this.getAlpha(),n=.5,h=this.getSource(),r=e.getSource(),l;for(l=0;l<3;l++)i.push(Math.round(h[l]*(1-n)+r[l]*n));return i[3]=t,this.setSource(i),this}},s.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*((?:\d*\.?\d+)?)\s*)?\)$/i,s.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/i,s.Color.reHex=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,s.Color.colorNameMap={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"};function a(e,i,t){return t<0&&(t+=1),t>1&&(t-=1),t<1/6?e+(i-e)*6*t:t<1/2?i:t<2/3?e+(i-e)*(2/3-t)*6:e}s.Color.fromRgb=function(e){return o.fromSource(o.sourceFromRgb(e))},s.Color.sourceFromRgb=function(e){var i=e.match(o.reRGBa);if(i){var t=parseInt(i[1],10)/(/%$/.test(i[1])?100:1)*(/%$/.test(i[1])?255:1),n=parseInt(i[2],10)/(/%$/.test(i[2])?100:1)*(/%$/.test(i[2])?255:1),h=parseInt(i[3],10)/(/%$/.test(i[3])?100:1)*(/%$/.test(i[3])?255:1);return[parseInt(t,10),parseInt(n,10),parseInt(h,10),i[4]?parseFloat(i[4]):1]}},s.Color.fromRgba=o.fromRgb,s.Color.fromHsl=function(e){return o.fromSource(o.sourceFromHsl(e))},s.Color.sourceFromHsl=function(e){var i=e.match(o.reHSLa);if(i){var t=(parseFloat(i[1])%360+360)%360/360,n=parseFloat(i[2])/(/%$/.test(i[2])?100:1),h=parseFloat(i[3])/(/%$/.test(i[3])?100:1),r,l,u;if(n===0)r=l=u=h;else{var d=h<=.5?h*(n+1):h+n-h*n,g=h*2-d;r=a(g,d,t+1/3),l=a(g,d,t),u=a(g,d,t-1/3)}return[Math.round(r*255),Math.round(l*255),Math.round(u*255),i[4]?parseFloat(i[4]):1]}},s.Color.fromHsla=o.fromHsl,s.Color.fromHex=function(e){return o.fromSource(o.sourceFromHex(e))},s.Color.sourceFromHex=function(e){if(e.match(o.reHex)){var i=e.slice(e.indexOf("#")+1),t=i.length===3||i.length===4,n=i.length===8||i.length===4,h=t?i.charAt(0)+i.charAt(0):i.substring(0,2),r=t?i.charAt(1)+i.charAt(1):i.substring(2,4),l=t?i.charAt(2)+i.charAt(2):i.substring(4,6),u=n?t?i.charAt(3)+i.charAt(3):i.substring(6,8):"FF";return[parseInt(h,16),parseInt(r,16),parseInt(l,16),parseFloat((parseInt(u,16)/255).toFixed(2))]}},s.Color.fromSource=function(e){var i=new o;return i.setSource(e),i}}(et),function(c){var s=c.fabric||(c.fabric={}),o=["e","se","s","sw","w","nw","n","ne","e"],a=["ns","nesw","ew","nwse"],e={},i="left",t="top",n="right",h="bottom",r="center",l={top:h,bottom:t,left:n,right:i,center:r},u=s.util.radiansToDegrees,d=Math.sign||function(D){return(D>0)-(D<0)||+D};function g(D,O){var k=D.angle+u(Math.atan2(O.y,O.x))+360;return Math.round(k%360/45)}function m(D,O){var k=O.transform.target,I=k.canvas,L=s.util.object.clone(O);L.target=k,I&&I.fire("object:"+D,L),k.fire(D,O)}function v(D,O){var k=O.canvas,I=k.uniScaleKey,L=D[I];return k.uniformScaling&&!L||!k.uniformScaling&&L}function y(D){return D.originX===r&&D.originY===r}function T(D,O,k){var I=D.lockScalingX,L=D.lockScalingY;return!!(I&&L||!O&&(I||L)&&k||I&&O==="x"||L&&O==="y")}function M(D,O,k){var I="not-allowed",L=v(D,k),X="";if(O.x!==0&&O.y===0?X="x":O.x===0&&O.y!==0&&(X="y"),T(k,X,L))return I;var z=g(k,O);return o[z]+"-resize"}function W(D,O,k){var I="not-allowed";if(O.x!==0&&k.lockSkewingY||O.y!==0&&k.lockSkewingX)return I;var L=g(k,O)%4;return a[L]+"-resize"}function H(D,O,k){return D[k.canvas.altActionKey]?e.skewCursorStyleHandler(D,O,k):e.scaleCursorStyleHandler(D,O,k)}function G(D,O,k){var I=D[k.canvas.altActionKey];if(O.x===0)return I?"skewX":"scaleY";if(O.y===0)return I?"skewY":"scaleX"}function V(D,O,k){return k.lockRotation?"not-allowed":O.cursorStyle}function U(D,O,k,I){return{e:D,transform:O,pointer:{x:k,y:I}}}function N(D){return function(O,k,I,L){var X=k.target,z=X.getCenterPoint(),Z=X.translateToOriginPoint(z,k.originX,k.originY),J=D(O,k,I,L);return X.setPositionByOrigin(Z,k.originX,k.originY),J}}function Q(D,O){return function(k,I,L,X){var z=O(k,I,L,X);return z&&m(D,U(k,I,L,X)),z}}function $(D,O,k,I,L){var X=D.target,z=X.controls[D.corner],Z=X.canvas.getZoom(),J=X.padding/Z,B=X.toLocalPoint(new s.Point(I,L),O,k);return B.x>=J&&(B.x-=J),B.x<=-J&&(B.x+=J),B.y>=J&&(B.y-=J),B.y<=J&&(B.y+=J),B.x-=z.offsetX,B.y-=z.offsetY,B}function K(D){return D.flipX!==D.flipY}function S(D,O,k,I,L){if(D[O]!==0){var X=D._getTransformedDimensions()[I],z=L/X*D[k];D.set(k,z)}}function F(D,O,k,I){var L=O.target,X=L._getTransformedDimensions(0,L.skewY),z=$(O,O.originX,O.originY,k,I),Z=Math.abs(z.x*2)-X.x,J=L.skewX,B;Z<2?B=0:(B=u(Math.atan2(Z/L.scaleX,X.y/L.scaleY)),O.originX===i&&O.originY===h&&(B=-B),O.originX===n&&O.originY===t&&(B=-B),K(L)&&(B=-B));var q=J!==B;if(q){var tt=L._getTransformedDimensions().y;L.set("skewX",B),S(L,"skewY","scaleY","y",tt)}return q}function b(D,O,k,I){var L=O.target,X=L._getTransformedDimensions(L.skewX,0),z=$(O,O.originX,O.originY,k,I),Z=Math.abs(z.y*2)-X.y,J=L.skewY,B;Z<2?B=0:(B=u(Math.atan2(Z/L.scaleY,X.x/L.scaleX)),O.originX===i&&O.originY===h&&(B=-B),O.originX===n&&O.originY===t&&(B=-B),K(L)&&(B=-B));var q=J!==B;if(q){var tt=L._getTransformedDimensions().x;L.set("skewY",B),S(L,"skewX","scaleX","x",tt)}return q}function w(D,O,k,I){var L=O.target,X=L.skewX,z,Z=O.originY;if(L.lockSkewingX)return!1;if(X===0){var J=$(O,r,r,k,I);J.x>0?z=i:z=n}else X>0&&(z=Z===t?i:n),X<0&&(z=Z===t?n:i),K(L)&&(z=z===i?n:i);O.originX=z;var B=Q("skewing",N(F));return B(D,O,k,I)}function P(D,O,k,I){var L=O.target,X=L.skewY,z,Z=O.originX;if(L.lockSkewingY)return!1;if(X===0){var J=$(O,r,r,k,I);J.y>0?z=t:z=h}else X>0&&(z=Z===i?t:h),X<0&&(z=Z===i?h:t),K(L)&&(z=z===t?h:t);O.originY=z;var B=Q("skewing",N(b));return B(D,O,k,I)}function p(D,O,k,I){var L=O,X=L.target,z=X.translateToOriginPoint(X.getCenterPoint(),L.originX,L.originY);if(X.lockRotation)return!1;var Z=Math.atan2(L.ey-z.y,L.ex-z.x),J=Math.atan2(I-z.y,k-z.x),B=u(J-Z+L.theta),q=!0;if(X.snapAngle>0){var tt=X.snapAngle,it=X.snapThreshold||tt,at=Math.ceil(B/tt)*tt,nt=Math.floor(B/tt)*tt;Math.abs(B-nt)<it?B=nt:Math.abs(B-at)<it&&(B=at)}return B<0&&(B=360+B),B%=360,q=X.angle!==B,X.angle=B,q}function _(D,O,k,I,L){L=L||{};var X=O.target,z=X.lockScalingX,Z=X.lockScalingY,J=L.by,B,q,tt,it,at=v(D,X),nt=T(X,J,at),st,rt,ot=O.gestureScale;if(nt)return!1;if(ot)q=O.scaleX*ot,tt=O.scaleY*ot;else{if(B=$(O,O.originX,O.originY,k,I),st=J!=="y"?d(B.x):1,rt=J!=="x"?d(B.y):1,O.signX||(O.signX=st),O.signY||(O.signY=rt),X.lockScalingFlip&&(O.signX!==st||O.signY!==rt))return!1;if(it=X._getTransformedDimensions(),at&&!J){var lt=Math.abs(B.x)+Math.abs(B.y),ct=O.original,ut=Math.abs(it.x*ct.scaleX/X.scaleX)+Math.abs(it.y*ct.scaleY/X.scaleY),ht=lt/ut;q=ct.scaleX*ht,tt=ct.scaleY*ht}else q=Math.abs(B.x*X.scaleX/it.x),tt=Math.abs(B.y*X.scaleY/it.y);y(O)&&(q*=2,tt*=2),O.signX!==st&&J!=="y"&&(O.originX=l[O.originX],q*=-1,O.signX=st),O.signY!==rt&&J!=="x"&&(O.originY=l[O.originY],tt*=-1,O.signY=rt)}var _t=X.scaleX,yt=X.scaleY;return J?(J==="x"&&X.set("scaleX",q),J==="y"&&X.set("scaleY",tt)):(!z&&X.set("scaleX",q),!Z&&X.set("scaleY",tt)),_t!==X.scaleX||yt!==X.scaleY}function x(D,O,k,I){return _(D,O,k,I)}function C(D,O,k,I){return _(D,O,k,I,{by:"x"})}function A(D,O,k,I){return _(D,O,k,I,{by:"y"})}function E(D,O,k,I){return D[O.target.canvas.altActionKey]?e.skewHandlerX(D,O,k,I):e.scalingY(D,O,k,I)}function R(D,O,k,I){return D[O.target.canvas.altActionKey]?e.skewHandlerY(D,O,k,I):e.scalingX(D,O,k,I)}function j(D,O,k,I){var L=O.target,X=$(O,O.originX,O.originY,k,I),z=L.strokeWidth/(L.strokeUniform?L.scaleX:1),Z=y(O)?2:1,J=L.width,B=Math.abs(X.x*Z/L.scaleX)-z;return L.set("width",Math.max(B,0)),J!==B}function Y(D,O,k,I){var L=O.target,X=k-O.offsetX,z=I-O.offsetY,Z=!L.get("lockMovementX")&&L.left!==X,J=!L.get("lockMovementY")&&L.top!==z;return Z&&L.set("left",X),J&&L.set("top",z),(Z||J)&&m("moving",U(D,O,k,I)),Z||J}e.scaleCursorStyleHandler=M,e.skewCursorStyleHandler=W,e.scaleSkewCursorStyleHandler=H,e.rotationWithSnapping=Q("rotating",N(p)),e.scalingEqually=Q("scaling",N(x)),e.scalingX=Q("scaling",N(C)),e.scalingY=Q("scaling",N(A)),e.scalingYOrSkewingX=E,e.scalingXOrSkewingY=R,e.changeWidth=Q("resizing",N(j)),e.skewHandlerX=w,e.skewHandlerY=P,e.dragHandler=Y,e.scaleOrSkewActionName=G,e.rotationStyleHandler=V,e.fireEvent=m,e.wrapWithFixedAnchor=N,e.wrapWithFireEvent=Q,e.getLocalPoint=$,s.controlsUtils=e}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.degreesToRadians,a=s.controlsUtils;function e(t,n,h,r,l){r=r||{};var u=this.sizeX||r.cornerSize||l.cornerSize,d=this.sizeY||r.cornerSize||l.cornerSize,g=typeof r.transparentCorners!="undefined"?r.transparentCorners:l.transparentCorners,m=g?"stroke":"fill",v=!g&&(r.cornerStrokeColor||l.cornerStrokeColor),y=n,T=h,M;t.save(),t.fillStyle=r.cornerColor||l.cornerColor,t.strokeStyle=r.cornerStrokeColor||l.cornerStrokeColor,u>d?(M=u,t.scale(1,d/u),T=h*u/d):d>u?(M=d,t.scale(u/d,1),y=n*d/u):M=u,t.lineWidth=1,t.beginPath(),t.arc(y,T,M/2,0,2*Math.PI,!1),t[m](),v&&t.stroke(),t.restore()}function i(t,n,h,r,l){r=r||{};var u=this.sizeX||r.cornerSize||l.cornerSize,d=this.sizeY||r.cornerSize||l.cornerSize,g=typeof r.transparentCorners!="undefined"?r.transparentCorners:l.transparentCorners,m=g?"stroke":"fill",v=!g&&(r.cornerStrokeColor||l.cornerStrokeColor),y=u/2,T=d/2;t.save(),t.fillStyle=r.cornerColor||l.cornerColor,t.strokeStyle=r.cornerStrokeColor||l.cornerStrokeColor,t.lineWidth=1,t.translate(n,h),t.rotate(o(l.angle)),t[m+"Rect"](-y,-T,u,d),v&&t.strokeRect(-y,-T,u,d),t.restore()}a.renderCircleControl=e,a.renderSquareControl=i}(et),function(c){var s=c.fabric||(c.fabric={});function o(a){for(var e in a)this[e]=a[e]}s.Control=o,s.Control.prototype={visible:!0,actionName:"scale",angle:0,x:0,y:0,offsetX:0,offsetY:0,sizeX:null,sizeY:null,touchSizeX:null,touchSizeY:null,cursorStyle:"crosshair",withConnection:!1,actionHandler:function(){},mouseDownHandler:function(){},mouseUpHandler:function(){},getActionHandler:function(){return this.actionHandler},getMouseDownHandler:function(){return this.mouseDownHandler},getMouseUpHandler:function(){return this.mouseUpHandler},cursorStyleHandler:function(a,e){return e.cursorStyle},getActionName:function(a,e){return e.actionName},getVisibility:function(a,e){var i=a._controlsVisibility;return i&&typeof i[e]!="undefined"?i[e]:this.visible},setVisibility:function(a){this.visible=a},positionHandler:function(a,e){var i=s.util.transformPoint({x:this.x*a.x+this.offsetX,y:this.y*a.y+this.offsetY},e);return i},calcCornerCoords:function(a,e,i,t,n){var h,r,l,u,d=n?this.touchSizeX:this.sizeX,g=n?this.touchSizeY:this.sizeY;if(d&&g&&d!==g){var m=Math.atan2(g,d),v=Math.sqrt(d*d+g*g)/2,y=m-s.util.degreesToRadians(a),T=Math.PI/2-m-s.util.degreesToRadians(a);h=v*s.util.cos(y),r=v*s.util.sin(y),l=v*s.util.cos(T),u=v*s.util.sin(T)}else{var M=d&&g?d:e;v=M*.7071067812;var y=s.util.degreesToRadians(45-a);h=l=v*s.util.cos(y),r=u=v*s.util.sin(y)}return{tl:{x:i-u,y:t-l},tr:{x:i+h,y:t-r},bl:{x:i-h,y:t+r},br:{x:i+u,y:t+l}}},render:function(a,e,i,t,n){switch(t=t||{},t.cornerStyle||n.cornerStyle){case"circle":s.controlsUtils.renderCircleControl.call(this,a,e,i,t,n);break;default:s.controlsUtils.renderSquareControl.call(this,a,e,i,t,n)}}}}(et),function(){function c(i,t){var n=i.getAttribute("style"),h=i.getAttribute("offset")||0,r,l,u,d;if(h=parseFloat(h)/(/%$/.test(h)?100:1),h=h<0?0:h>1?1:h,n){var g=n.split(/\s*;\s*/);for(g[g.length-1]===""&&g.pop(),d=g.length;d--;){var m=g[d].split(/\s*:\s*/),v=m[0].trim(),y=m[1].trim();v==="stop-color"?r=y:v==="stop-opacity"&&(u=y)}}return r||(r=i.getAttribute("stop-color")||"rgb(0,0,0)"),u||(u=i.getAttribute("stop-opacity")),r=new f.Color(r),l=r.getAlpha(),u=isNaN(parseFloat(u))?1:parseFloat(u),u*=l*t,{offset:h,color:r.toRgb(),opacity:u}}function s(i){return{x1:i.getAttribute("x1")||0,y1:i.getAttribute("y1")||0,x2:i.getAttribute("x2")||"100%",y2:i.getAttribute("y2")||0}}function o(i){return{x1:i.getAttribute("fx")||i.getAttribute("cx")||"50%",y1:i.getAttribute("fy")||i.getAttribute("cy")||"50%",r1:0,x2:i.getAttribute("cx")||"50%",y2:i.getAttribute("cy")||"50%",r2:i.getAttribute("r")||"50%"}}var a=f.util.object.clone;f.Gradient=f.util.createClass({offsetX:0,offsetY:0,gradientTransform:null,gradientUnits:"pixels",type:"linear",initialize:function(i){i||(i={}),i.coords||(i.coords={});var t,n=this;Object.keys(i).forEach(function(h){n[h]=i[h]}),this.id?this.id+="_"+f.Object.__uid++:this.id=f.Object.__uid++,t={x1:i.coords.x1||0,y1:i.coords.y1||0,x2:i.coords.x2||0,y2:i.coords.y2||0},this.type==="radial"&&(t.r1=i.coords.r1||0,t.r2=i.coords.r2||0),this.coords=t,this.colorStops=i.colorStops.slice()},addColorStop:function(i){for(var t in i){var n=new f.Color(i[t]);this.colorStops.push({offset:parseFloat(t),color:n.toRgb(),opacity:n.getAlpha()})}return this},toObject:function(i){var t={type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?this.gradientTransform.concat():this.gradientTransform};return f.util.populateWithProperties(this,t,i),t},toSVG:function(i,l){var n=a(this.coords,!0),h,r,l=l||{},u,d,g=a(this.colorStops,!0),m=n.r1>n.r2,v=this.gradientTransform?this.gradientTransform.concat():f.iMatrix.concat(),y=-this.offsetX,T=-this.offsetY,M=!!l.additionalTransform,W=this.gradientUnits==="pixels"?"userSpaceOnUse":"objectBoundingBox";if(g.sort(function(N,Q){return N.offset-Q.offset}),W==="objectBoundingBox"?(y/=i.width,T/=i.height):(y+=i.width/2,T+=i.height/2),i.type==="path"&&this.gradientUnits!=="percentage"&&(y-=i.pathOffset.x,T-=i.pathOffset.y),v[4]-=y,v[5]-=T,d='id="SVGID_'+this.id+'" gradientUnits="'+W+'"',d+=' gradientTransform="'+(M?l.additionalTransform+" ":"")+f.util.matrixToSVG(v)+'" ',this.type==="linear"?u=["<linearGradient ",d,' x1="',n.x1,'" y1="',n.y1,'" x2="',n.x2,'" y2="',n.y2,`">
`]:this.type==="radial"&&(u=["<radialGradient ",d,' cx="',m?n.x1:n.x2,'" cy="',m?n.y1:n.y2,'" r="',m?n.r1:n.r2,'" fx="',m?n.x2:n.x1,'" fy="',m?n.y2:n.y1,`">
`]),this.type==="radial"){if(m)for(g=g.concat(),g.reverse(),h=0,r=g.length;h<r;h++)g[h].offset=1-g[h].offset;var H=Math.min(n.r1,n.r2);if(H>0){var G=Math.max(n.r1,n.r2),V=H/G;for(h=0,r=g.length;h<r;h++)g[h].offset+=V*(1-g[h].offset)}}for(h=0,r=g.length;h<r;h++){var U=g[h];u.push("<stop ",'offset="',U.offset*100+"%",'" style="stop-color:',U.color,typeof U.opacity!="undefined"?";stop-opacity: "+U.opacity:";",`"/>
`)}return u.push(this.type==="linear"?`</linearGradient>
`:`</radialGradient>
`),u.join("")},toLive:function(i){var t,n=f.util.object.clone(this.coords),h,r;if(this.type){for(this.type==="linear"?t=i.createLinearGradient(n.x1,n.y1,n.x2,n.y2):this.type==="radial"&&(t=i.createRadialGradient(n.x1,n.y1,n.r1,n.x2,n.y2,n.r2)),h=0,r=this.colorStops.length;h<r;h++){var l=this.colorStops[h].color,u=this.colorStops[h].opacity,d=this.colorStops[h].offset;typeof u!="undefined"&&(l=new f.Color(l).setAlpha(u).toRgba()),t.addColorStop(d,l)}return t}}}),f.util.object.extend(f.Gradient,{fromElement:function(i,t,n,h){var r=parseFloat(n)/(/%$/.test(n)?100:1);r=r<0?0:r>1?1:r,isNaN(r)&&(r=1);var l=i.getElementsByTagName("stop"),u,d=i.getAttribute("gradientUnits")==="userSpaceOnUse"?"pixels":"percentage",g=i.getAttribute("gradientTransform")||"",m=[],v,y,T=0,M=0,W;for(i.nodeName==="linearGradient"||i.nodeName==="LINEARGRADIENT"?(u="linear",v=s(i)):(u="radial",v=o(i)),y=l.length;y--;)m.push(c(l[y],r));W=f.parseTransformAttribute(g),e(t,v,h,d),d==="pixels"&&(T=-t.left,M=-t.top);var H=new f.Gradient({id:i.getAttribute("id"),type:u,coords:v,colorStops:m,gradientUnits:d,gradientTransform:W,offsetX:T,offsetY:M});return H}});function e(i,t,n,h){var r,l;Object.keys(t).forEach(function(u){r=t[u],r==="Infinity"?l=1:r==="-Infinity"?l=0:(l=parseFloat(t[u],10),typeof r=="string"&&/^(\d+\.\d+)%|(\d+)%$/.test(r)&&(l*=.01,h==="pixels"&&((u==="x1"||u==="x2"||u==="r2")&&(l*=n.viewBoxWidth||n.width),(u==="y1"||u==="y2")&&(l*=n.viewBoxHeight||n.height)))),t[u]=l})}}(),function(){var c=f.util.toFixed;f.Pattern=f.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,crossOrigin:"",patternTransform:null,initialize:function(s,o){if(s||(s={}),this.id=f.Object.__uid++,this.setOptions(s),!s.source||s.source&&typeof s.source!="string"){o&&o(this);return}else{var a=this;this.source=f.util.createImage(),f.util.loadImage(s.source,function(e,i){a.source=e,o&&o(a,i)},null,this.crossOrigin)}},toObject:function(s){var o=f.Object.NUM_FRACTION_DIGITS,a,e;return typeof this.source.src=="string"?a=this.source.src:typeof this.source=="object"&&this.source.toDataURL&&(a=this.source.toDataURL()),e={type:"pattern",source:a,repeat:this.repeat,crossOrigin:this.crossOrigin,offsetX:c(this.offsetX,o),offsetY:c(this.offsetY,o),patternTransform:this.patternTransform?this.patternTransform.concat():null},f.util.populateWithProperties(this,e,s),e},toSVG:function(s){var o=typeof this.source=="function"?this.source():this.source,a=o.width/s.width,e=o.height/s.height,i=this.offsetX/s.width,t=this.offsetY/s.height,n="";return(this.repeat==="repeat-x"||this.repeat==="no-repeat")&&(e=1,t&&(e+=Math.abs(t))),(this.repeat==="repeat-y"||this.repeat==="no-repeat")&&(a=1,i&&(a+=Math.abs(i))),o.src?n=o.src:o.toDataURL&&(n=o.toDataURL()),'<pattern id="SVGID_'+this.id+'" x="'+i+'" y="'+t+'" width="'+a+'" height="'+e+`">
<image x="0" y="0" width="`+o.width+'" height="'+o.height+'" xlink:href="'+n+`"></image>
</pattern>
`},setOptions:function(s){for(var o in s)this[o]=s[o]},toLive:function(s){var o=this.source;return!o||typeof o.src!="undefined"&&(!o.complete||o.naturalWidth===0||o.naturalHeight===0)?"":s.createPattern(o,this.repeat)}})}(),function(c){var s=c.fabric||(c.fabric={}),o=s.util.toFixed;if(s.Shadow){s.warn("fabric.Shadow is already defined.");return}s.Shadow=s.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1,initialize:function(a){typeof a=="string"&&(a=this._parseShadow(a));for(var e in a)this[e]=a[e];this.id=s.Object.__uid++},_parseShadow:function(a){var e=a.trim(),i=s.Shadow.reOffsetsAndBlur.exec(e)||[],t=e.replace(s.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)";return{color:t.trim(),offsetX:parseFloat(i[1],10)||0,offsetY:parseFloat(i[2],10)||0,blur:parseFloat(i[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toSVG:function(a){var e=40,i=40,t=s.Object.NUM_FRACTION_DIGITS,n=s.util.rotateVector({x:this.offsetX,y:this.offsetY},s.util.degreesToRadians(-a.angle)),h=20,r=new s.Color(this.color);return a.width&&a.height&&(e=o((Math.abs(n.x)+this.blur)/a.width,t)*100+h,i=o((Math.abs(n.y)+this.blur)/a.height,t)*100+h),a.flipX&&(n.x*=-1),a.flipY&&(n.y*=-1),'<filter id="SVGID_'+this.id+'" y="-'+i+'%" height="'+(100+2*i)+'%" x="-'+e+'%" width="'+(100+2*e)+`%" >
	<feGaussianBlur in="SourceAlpha" stdDeviation="`+o(this.blur?this.blur/2:0,t)+`"></feGaussianBlur>
	<feOffset dx="`+o(n.x,t)+'" dy="'+o(n.y,t)+`" result="oBlur" ></feOffset>
	<feFlood flood-color="`+r.toRgb()+'" flood-opacity="'+r.getAlpha()+`"/>
	<feComposite in2="oBlur" operator="in" />
	<feMerge>
		<feMergeNode></feMergeNode>
		<feMergeNode in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
`},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling};var a={},e=s.Shadow.prototype;return["color","blur","offsetX","offsetY","affectStroke","nonScaling"].forEach(function(i){this[i]!==e[i]&&(a[i]=this[i])},this),a}}),s.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(\d+(?:\.\d*)?(?:px)?)?(?:\s?|$)(?:$|\s)/}(et),function(){if(f.StaticCanvas){f.warn("fabric.StaticCanvas is already defined.");return}var c=f.util.object.extend,s=f.util.getElementOffset,o=f.util.removeFromArray,a=f.util.toFixed,e=f.util.transformPoint,i=f.util.invertTransform,t=f.util.getNodeCanvas,n=f.util.createCanvasElement,h=new Error("Could not initialize `canvas` element");f.StaticCanvas=f.util.createClass(f.CommonMethods,{initialize:function(r,l){l||(l={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(r,l)},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!1,renderOnAddRemove:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:f.iMatrix.concat(),backgroundVpt:!0,overlayVpt:!0,enableRetinaScaling:!0,vptCoords:{},skipOffscreen:!0,clipPath:void 0,_initStatic:function(r,l){var u=this.requestRenderAllBound;this._objects=[],this._createLowerCanvas(r),this._initOptions(l),this.interactive||this._initRetinaScaling(),l.overlayImage&&this.setOverlayImage(l.overlayImage,u),l.backgroundImage&&this.setBackgroundImage(l.backgroundImage,u),l.backgroundColor&&this.setBackgroundColor(l.backgroundColor,u),l.overlayColor&&this.setOverlayColor(l.overlayColor,u),this.calcOffset()},_isRetinaScaling:function(){return f.devicePixelRatio!==1&&this.enableRetinaScaling},getRetinaScaling:function(){return this._isRetinaScaling()?f.devicePixelRatio:1},_initRetinaScaling:function(){if(this._isRetinaScaling()){var r=f.devicePixelRatio;this.__initRetinaScaling(r,this.lowerCanvasEl,this.contextContainer),this.upperCanvasEl&&this.__initRetinaScaling(r,this.upperCanvasEl,this.contextTop)}},__initRetinaScaling:function(r,l,u){l.setAttribute("width",this.width*r),l.setAttribute("height",this.height*r),u.scale(r,r)},calcOffset:function(){return this._offset=s(this.lowerCanvasEl),this},setOverlayImage:function(r,l,u){return this.__setBgOverlayImage("overlayImage",r,l,u)},setBackgroundImage:function(r,l,u){return this.__setBgOverlayImage("backgroundImage",r,l,u)},setOverlayColor:function(r,l){return this.__setBgOverlayColor("overlayColor",r,l)},setBackgroundColor:function(r,l){return this.__setBgOverlayColor("backgroundColor",r,l)},__setBgOverlayImage:function(r,l,u,d){return typeof l=="string"?f.util.loadImage(l,function(g,m){if(g){var v=new f.Image(g,d);this[r]=v,v.canvas=this}u&&u(g,m)},this,d&&d.crossOrigin):(d&&l.setOptions(d),this[r]=l,l&&(l.canvas=this),u&&u(l,!1)),this},__setBgOverlayColor:function(r,l,u){return this[r]=l,this._initGradient(l,r),this._initPattern(l,r,u),this},_createCanvasElement:function(){var r=n();if(!r||(r.style||(r.style={}),typeof r.getContext=="undefined"))throw h;return r},_initOptions:function(r){var l=this.lowerCanvasEl;this._setOptions(r),this.width=this.width||parseInt(l.width,10)||0,this.height=this.height||parseInt(l.height,10)||0,this.lowerCanvasEl.style&&(l.width=this.width,l.height=this.height,l.style.width=this.width+"px",l.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(r){r&&r.getContext?this.lowerCanvasEl=r:this.lowerCanvasEl=f.util.getById(r)||this._createCanvasElement(),f.util.addClass(this.lowerCanvasEl,"lower-canvas"),this._originalCanvasStyle=this.lowerCanvasEl.style,this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(r,l){return this.setDimensions({width:r},l)},setHeight:function(r,l){return this.setDimensions({height:r},l)},setDimensions:function(r,l){var u;l=l||{};for(var d in r)u=r[d],l.cssOnly||(this._setBackstoreDimension(d,r[d]),u+="px",this.hasLostContext=!0),l.backstoreOnly||this._setCssDimension(d,u);return this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(),this._initRetinaScaling(),this.calcOffset(),l.cssOnly||this.requestRenderAll(),this},_setBackstoreDimension:function(r,l){return this.lowerCanvasEl[r]=l,this.upperCanvasEl&&(this.upperCanvasEl[r]=l),this.cacheCanvasEl&&(this.cacheCanvasEl[r]=l),this[r]=l,this},_setCssDimension:function(r,l){return this.lowerCanvasEl.style[r]=l,this.upperCanvasEl&&(this.upperCanvasEl.style[r]=l),this.wrapperEl&&(this.wrapperEl.style[r]=l),this},getZoom:function(){return this.viewportTransform[0]},setViewportTransform:function(r){var l=this._activeObject,u=this.backgroundImage,d=this.overlayImage,g,m,v;for(this.viewportTransform=r,m=0,v=this._objects.length;m<v;m++)g=this._objects[m],g.group||g.setCoords(!0);return l&&l.setCoords(),u&&u.setCoords(!0),d&&d.setCoords(!0),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll(),this},zoomToPoint:function(r,l){var u=r,d=this.viewportTransform.slice(0);r=e(r,i(this.viewportTransform)),d[0]=l,d[3]=l;var g=e(r,d);return d[4]+=u.x-g.x,d[5]+=u.y-g.y,this.setViewportTransform(d)},setZoom:function(r){return this.zoomToPoint(new f.Point(0,0),r),this},absolutePan:function(r){var l=this.viewportTransform.slice(0);return l[4]=-r.x,l[5]=-r.y,this.setViewportTransform(l)},relativePan:function(r){return this.absolutePan(new f.Point(-r.x-this.viewportTransform[4],-r.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},_onObjectAdded:function(r){this.stateful&&r.setupState(),r._set("canvas",this),r.setCoords(),this.fire("object:added",{target:r}),r.fire("added")},_onObjectRemoved:function(r){this.fire("object:removed",{target:r}),r.fire("removed"),delete r.canvas},clearContext:function(r){return r.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this.remove.apply(this,this.getObjects()),this.backgroundImage=null,this.overlayImage=null,this.backgroundColor="",this.overlayColor="",this._hasITextHandlers&&(this.off("mouse:up",this._mouseUpITextHandler),this._iTextInstances=null,this._hasITextHandlers=!1),this.clearContext(this.contextContainer),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll(),this},renderAll:function(){var r=this.contextContainer;return this.renderCanvas(r,this._objects),this},renderAndReset:function(){this.isRendering=0,this.renderAll()},requestRenderAll:function(){return this.isRendering||(this.isRendering=f.util.requestAnimFrame(this.renderAndResetBound)),this},calcViewportBoundaries:function(){var r={},l=this.width,u=this.height,d=i(this.viewportTransform);return r.tl=e({x:0,y:0},d),r.br=e({x:l,y:u},d),r.tr=new f.Point(r.br.x,r.tl.y),r.bl=new f.Point(r.tl.x,r.br.y),this.vptCoords=r,r},cancelRequestedRender:function(){this.isRendering&&(f.util.cancelAnimFrame(this.isRendering),this.isRendering=0)},renderCanvas:function(r,l){var u=this.viewportTransform,d=this.clipPath;this.cancelRequestedRender(),this.calcViewportBoundaries(),this.clearContext(r),f.util.setImageSmoothing(r,this.imageSmoothingEnabled),this.fire("before:render",{ctx:r}),this._renderBackground(r),r.save(),r.transform(u[0],u[1],u[2],u[3],u[4],u[5]),this._renderObjects(r,l),r.restore(),!this.controlsAboveOverlay&&this.interactive&&this.drawControls(r),d&&(d.canvas=this,d.shouldCache(),d._transformDone=!0,d.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(r)),this._renderOverlay(r),this.controlsAboveOverlay&&this.interactive&&this.drawControls(r),this.fire("after:render",{ctx:r})},drawClipPathOnCanvas:function(r){var l=this.viewportTransform,u=this.clipPath;r.save(),r.transform(l[0],l[1],l[2],l[3],l[4],l[5]),r.globalCompositeOperation="destination-in",u.transform(r),r.scale(1/u.zoomX,1/u.zoomY),r.drawImage(u._cacheCanvas,-u.cacheTranslationX,-u.cacheTranslationY),r.restore()},_renderObjects:function(r,l){var u,d;for(u=0,d=l.length;u<d;++u)l[u]&&l[u].render(r)},_renderBackgroundOrOverlay:function(r,l){var u=this[l+"Color"],d=this[l+"Image"],g=this.viewportTransform,m=this[l+"Vpt"];if(!(!u&&!d)){if(u){r.save(),r.beginPath(),r.moveTo(0,0),r.lineTo(this.width,0),r.lineTo(this.width,this.height),r.lineTo(0,this.height),r.closePath(),r.fillStyle=u.toLive?u.toLive(r,this):u,m&&r.transform(g[0],g[1],g[2],g[3],g[4],g[5]),r.transform(1,0,0,1,u.offsetX||0,u.offsetY||0);var v=u.gradientTransform||u.patternTransform;v&&r.transform(v[0],v[1],v[2],v[3],v[4],v[5]),r.fill(),r.restore()}d&&(r.save(),m&&r.transform(g[0],g[1],g[2],g[3],g[4],g[5]),d.render(r),r.restore())}},_renderBackground:function(r){this._renderBackgroundOrOverlay(r,"background")},_renderOverlay:function(r){this._renderBackgroundOrOverlay(r,"overlay")},getCenter:function(){return{top:this.height/2,left:this.width/2}},centerObjectH:function(r){return this._centerObject(r,new f.Point(this.getCenter().left,r.getCenterPoint().y))},centerObjectV:function(r){return this._centerObject(r,new f.Point(r.getCenterPoint().x,this.getCenter().top))},centerObject:function(r){var l=this.getCenter();return this._centerObject(r,new f.Point(l.left,l.top))},viewportCenterObject:function(r){var l=this.getVpCenter();return this._centerObject(r,l)},viewportCenterObjectH:function(r){var l=this.getVpCenter();return this._centerObject(r,new f.Point(l.x,r.getCenterPoint().y)),this},viewportCenterObjectV:function(r){var l=this.getVpCenter();return this._centerObject(r,new f.Point(r.getCenterPoint().x,l.y))},getVpCenter:function(){var r=this.getCenter(),l=i(this.viewportTransform);return e({x:r.left,y:r.top},l)},_centerObject:function(r,l){return r.setPositionByOrigin(l,"center","center"),r.setCoords(),this.renderOnAddRemove&&this.requestRenderAll(),this},toDatalessJSON:function(r){return this.toDatalessObject(r)},toObject:function(r){return this._toObjectMethod("toObject",r)},toDatalessObject:function(r){return this._toObjectMethod("toDatalessObject",r)},_toObjectMethod:function(r,l){var u=this.clipPath,d={version:f.version,objects:this._toObjects(r,l)};return u&&!u.excludeFromExport&&(d.clipPath=this._toObject(this.clipPath,r,l)),c(d,this.__serializeBgOverlay(r,l)),f.util.populateWithProperties(this,d,l),d},_toObjects:function(r,l){return this._objects.filter(function(u){return!u.excludeFromExport}).map(function(u){return this._toObject(u,r,l)},this)},_toObject:function(r,l,u){var d;this.includeDefaultValues||(d=r.includeDefaultValues,r.includeDefaultValues=!1);var g=r[l](u);return this.includeDefaultValues||(r.includeDefaultValues=d),g},__serializeBgOverlay:function(r,l){var u={},d=this.backgroundImage,g=this.overlayImage,m=this.backgroundColor,v=this.overlayColor;return m&&m.toObject?m.excludeFromExport||(u.background=m.toObject(l)):m&&(u.background=m),v&&v.toObject?v.excludeFromExport||(u.overlay=v.toObject(l)):v&&(u.overlay=v),d&&!d.excludeFromExport&&(u.backgroundImage=this._toObject(d,r,l)),g&&!g.excludeFromExport&&(u.overlayImage=this._toObject(g,r,l)),u},svgViewportTransformation:!0,toSVG:function(r,l){r||(r={}),r.reviver=l;var u=[];return this._setSVGPreamble(u,r),this._setSVGHeader(u,r),this.clipPath&&u.push('<g clip-path="url(#'+this.clipPath.clipPathId+`)" >
`),this._setSVGBgOverlayColor(u,"background"),this._setSVGBgOverlayImage(u,"backgroundImage",l),this._setSVGObjects(u,l),this.clipPath&&u.push(`</g>
`),this._setSVGBgOverlayColor(u,"overlay"),this._setSVGBgOverlayImage(u,"overlayImage",l),u.push("</svg>"),u.join("")},_setSVGPreamble:function(r,l){l.suppressPreamble||r.push('<?xml version="1.0" encoding="',l.encoding||"UTF-8",`" standalone="no" ?>
`,'<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ',`"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
`)},_setSVGHeader:function(r,l){var u=l.width||this.width,d=l.height||this.height,g,m='viewBox="0 0 '+this.width+" "+this.height+'" ',v=f.Object.NUM_FRACTION_DIGITS;l.viewBox?m='viewBox="'+l.viewBox.x+" "+l.viewBox.y+" "+l.viewBox.width+" "+l.viewBox.height+'" ':this.svgViewportTransformation&&(g=this.viewportTransform,m='viewBox="'+a(-g[4]/g[0],v)+" "+a(-g[5]/g[3],v)+" "+a(this.width/g[0],v)+" "+a(this.height/g[3],v)+'" '),r.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',u,'" ','height="',d,'" ',m,`xml:space="preserve">
`,"<desc>Created with Fabric.js ",f.version,`</desc>
`,`<defs>
`,this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(l),`</defs>
`)},createSVGClipPathMarkup:function(r){var l=this.clipPath;return l?(l.clipPathId="CLIPPATH_"+f.Object.__uid++,'<clipPath id="'+l.clipPathId+`" >
`+this.clipPath.toClipPathSVG(r.reviver)+`</clipPath>
`):""},createSVGRefElementsMarkup:function(){var r=this,l=["background","overlay"].map(function(u){var d=r[u+"Color"];if(d&&d.toLive){var g=r[u+"Vpt"],m=r.viewportTransform,v={width:r.width/(g?m[0]:1),height:r.height/(g?m[3]:1)};return d.toSVG(v,{additionalTransform:g?f.util.matrixToSVG(m):""})}});return l.join("")},createSVGFontFacesMarkup:function(){var r="",l={},u,d,g,m,v,y,T,M,W,H=f.fontPaths,G=[];for(this._objects.forEach(function U(N){G.push(N),N._objects&&N._objects.forEach(U)}),M=0,W=G.length;M<W;M++)if(u=G[M],d=u.fontFamily,!(u.type.indexOf("text")===-1||l[d]||!H[d])&&(l[d]=!0,!!u.styles)){g=u.styles;for(v in g){m=g[v];for(T in m)y=m[T],d=y.fontFamily,!l[d]&&H[d]&&(l[d]=!0)}}for(var V in l)r+=[`		@font-face {
`,"			font-family: '",V,`';
`,"			src: url('",H[V],`');
`,`		}
`].join("");return r&&(r=['	<style type="text/css">',`<![CDATA[
`,r,"]]>",`</style>
`].join("")),r},_setSVGObjects:function(r,l){var u,d,g,m=this._objects;for(d=0,g=m.length;d<g;d++)u=m[d],!u.excludeFromExport&&this._setSVGObject(r,u,l)},_setSVGObject:function(r,l,u){r.push(l.toSVG(u))},_setSVGBgOverlayImage:function(r,l,u){this[l]&&!this[l].excludeFromExport&&this[l].toSVG&&r.push(this[l].toSVG(u))},_setSVGBgOverlayColor:function(r,l){var u=this[l+"Color"],d=this.viewportTransform,g=this.width,m=this.height;if(u)if(u.toLive){var v=u.repeat,y=f.util.invertTransform(d),T=this[l+"Vpt"],M=T?f.util.matrixToSVG(y):"";r.push('<rect transform="'+M+" translate(",g/2,",",m/2,')"',' x="',u.offsetX-g/2,'" y="',u.offsetY-m/2,'" ','width="',v==="repeat-y"||v==="no-repeat"?u.source.width:g,'" height="',v==="repeat-x"||v==="no-repeat"?u.source.height:m,'" fill="url(#SVGID_'+u.id+')"',`></rect>
`)}else r.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',u,'"',`></rect>
`)},sendToBack:function(r){if(!r)return this;var l=this._activeObject,u,d,g;if(r===l&&r.type==="activeSelection")for(g=l._objects,u=g.length;u--;)d=g[u],o(this._objects,d),this._objects.unshift(d);else o(this._objects,r),this._objects.unshift(r);return this.renderOnAddRemove&&this.requestRenderAll(),this},bringToFront:function(r){if(!r)return this;var l=this._activeObject,u,d,g;if(r===l&&r.type==="activeSelection")for(g=l._objects,u=0;u<g.length;u++)d=g[u],o(this._objects,d),this._objects.push(d);else o(this._objects,r),this._objects.push(r);return this.renderOnAddRemove&&this.requestRenderAll(),this},sendBackwards:function(r,l){if(!r)return this;var u=this._activeObject,d,g,m,v,y,T=0;if(r===u&&r.type==="activeSelection")for(y=u._objects,d=0;d<y.length;d++)g=y[d],m=this._objects.indexOf(g),m>0+T&&(v=m-1,o(this._objects,g),this._objects.splice(v,0,g)),T++;else m=this._objects.indexOf(r),m!==0&&(v=this._findNewLowerIndex(r,m,l),o(this._objects,r),this._objects.splice(v,0,r));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewLowerIndex:function(r,l,u){var d,g;if(u)for(d=l,g=l-1;g>=0;--g){var m=r.intersectsWithObject(this._objects[g])||r.isContainedWithinObject(this._objects[g])||this._objects[g].isContainedWithinObject(r);if(m){d=g;break}}else d=l-1;return d},bringForward:function(r,l){if(!r)return this;var u=this._activeObject,d,g,m,v,y,T=0;if(r===u&&r.type==="activeSelection")for(y=u._objects,d=y.length;d--;)g=y[d],m=this._objects.indexOf(g),m<this._objects.length-1-T&&(v=m+1,o(this._objects,g),this._objects.splice(v,0,g)),T++;else m=this._objects.indexOf(r),m!==this._objects.length-1&&(v=this._findNewUpperIndex(r,m,l),o(this._objects,r),this._objects.splice(v,0,r));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewUpperIndex:function(r,l,u){var d,g,m;if(u)for(d=l,g=l+1,m=this._objects.length;g<m;++g){var v=r.intersectsWithObject(this._objects[g])||r.isContainedWithinObject(this._objects[g])||this._objects[g].isContainedWithinObject(r);if(v){d=g;break}}else d=l+1;return d},moveTo:function(r,l){return o(this._objects,r),this._objects.splice(l,0,r),this.renderOnAddRemove&&this.requestRenderAll()},dispose:function(){return this.isRendering&&(f.util.cancelAnimFrame(this.isRendering),this.isRendering=0),this.forEachObject(function(r){r.dispose&&r.dispose()}),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose&&this.backgroundImage.dispose(),this.backgroundImage=null,this.overlayImage&&this.overlayImage.dispose&&this.overlayImage.dispose(),this.overlayImage=null,this._iTextInstances=null,this.contextContainer=null,this.lowerCanvasEl.classList.remove("lower-canvas"),this.lowerCanvasEl.style=this._originalCanvasStyle,delete this._originalCanvasStyle,this.lowerCanvasEl.setAttribute("width",this.width),this.lowerCanvasEl.setAttribute("height",this.height),f.util.cleanUpJsdomNode(this.lowerCanvasEl),this.lowerCanvasEl=void 0,this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this._objects.length+" }>"}}),c(f.StaticCanvas.prototype,f.Observable),c(f.StaticCanvas.prototype,f.Collection),c(f.StaticCanvas.prototype,f.DataURLExporter),c(f.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(r){var l=n();if(!l||!l.getContext)return null;var u=l.getContext("2d");if(!u)return null;switch(r){case"setLineDash":return typeof u.setLineDash!="undefined";default:return null}}}),f.StaticCanvas.prototype.toJSON=f.StaticCanvas.prototype.toObject,f.isLikelyNode&&(f.StaticCanvas.prototype.createPNGStream=function(){var r=t(this.lowerCanvasEl);return r&&r.createPNGStream()},f.StaticCanvas.prototype.createJPEGStream=function(r){var l=t(this.lowerCanvasEl);return l&&l.createJPEGStream(r)})}(),f.BaseBrush=f.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",strokeMiterLimit:10,strokeDashArray:null,limitedToCanvasSize:!1,_setBrushStyles:function(){var c=this.canvas.contextTop;c.strokeStyle=this.color,c.lineWidth=this.width,c.lineCap=this.strokeLineCap,c.miterLimit=this.strokeMiterLimit,c.lineJoin=this.strokeLineJoin,c.setLineDash(this.strokeDashArray||[])},_saveAndTransform:function(c){var s=this.canvas.viewportTransform;c.save(),c.transform(s[0],s[1],s[2],s[3],s[4],s[5])},_setShadow:function(){if(this.shadow){var c=this.canvas,s=this.shadow,o=c.contextTop,a=c.getZoom();c&&c._isRetinaScaling()&&(a*=f.devicePixelRatio),o.shadowColor=s.color,o.shadowBlur=s.blur*a,o.shadowOffsetX=s.offsetX*a,o.shadowOffsetY=s.offsetY*a}},needsFullRender:function(){var c=new f.Color(this.color);return c.getAlpha()<1||!!this.shadow},_resetShadow:function(){var c=this.canvas.contextTop;c.shadowColor="",c.shadowBlur=c.shadowOffsetX=c.shadowOffsetY=0},_isOutSideCanvas:function(c){return c.x<0||c.x>this.canvas.getWidth()||c.y<0||c.y>this.canvas.getHeight()}}),function(){f.PencilBrush=f.util.createClass(f.BaseBrush,{decimate:.4,initialize:function(c){this.canvas=c,this._points=[]},_drawSegment:function(c,s,o){var a=s.midPointFrom(o);return c.quadraticCurveTo(s.x,s.y,a.x,a.y),a},onMouseDown:function(c,s){this.canvas._isMainEvent(s.e)&&(this._prepareForDrawing(c),this._captureDrawingPath(c),this._render())},onMouseMove:function(c,s){if(this.canvas._isMainEvent(s.e)&&!(this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c))&&this._captureDrawingPath(c)&&this._points.length>1)if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{var o=this._points,a=o.length,e=this.canvas.contextTop;this._saveAndTransform(e),this.oldEnd&&(e.beginPath(),e.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=this._drawSegment(e,o[a-2],o[a-1],!0),e.stroke(),e.restore()}},onMouseUp:function(c){return this.canvas._isMainEvent(c.e)?(this.oldEnd=void 0,this._finalizeAndAddPath(),!1):!0},_prepareForDrawing:function(c){var s=new f.Point(c.x,c.y);this._reset(),this._addPoint(s),this.canvas.contextTop.moveTo(s.x,s.y)},_addPoint:function(c){return this._points.length>1&&c.eq(this._points[this._points.length-1])?!1:(this._points.push(c),!0)},_reset:function(){this._points=[],this._setBrushStyles(),this._setShadow()},_captureDrawingPath:function(c){var s=new f.Point(c.x,c.y);return this._addPoint(s)},_render:function(){var c=this.canvas.contextTop,s,o,a=this._points[0],e=this._points[1];if(this._saveAndTransform(c),c.beginPath(),this._points.length===2&&a.x===e.x&&a.y===e.y){var i=this.width/1e3;a=new f.Point(a.x,a.y),e=new f.Point(e.x,e.y),a.x-=i,e.x+=i}for(c.moveTo(a.x,a.y),s=1,o=this._points.length;s<o;s++)this._drawSegment(c,a,e),a=this._points[s],e=this._points[s+1];c.lineTo(a.x,a.y),c.stroke(),c.restore()},convertPointsToSVGPath:function(c){var s=this.width/1e3;return f.util.getSmoothPathFromPoints(c,s)},_isEmptySVGPath:function(c){var s=f.util.joinPath(c);return s==="M 0 0 Q 0 0 0 0 L 0 0"},createPath:function(c){var s=new f.Path(c,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,s.shadow=new f.Shadow(this.shadow)),s},decimatePoints:function(c,s){if(c.length<=2)return c;var o=this.canvas.getZoom(),a=Math.pow(s/o,2),e,i=c.length-1,t=c[0],n=[t],h;for(e=1;e<i-1;e++)h=Math.pow(t.x-c[e].x,2)+Math.pow(t.y-c[e].y,2),h>=a&&(t=c[e],n.push(t));return n.push(c[i]),n},_finalizeAndAddPath:function(){var c=this.canvas.contextTop;c.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));var s=this.convertPointsToSVGPath(this._points);if(this._isEmptySVGPath(s)){this.canvas.requestRenderAll();return}var o=this.createPath(s);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:o}),this.canvas.add(o),this.canvas.requestRenderAll(),o.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:o})}})}(),f.CircleBrush=f.util.createClass(f.BaseBrush,{width:10,initialize:function(c){this.canvas=c,this.points=[]},drawDot:function(c){var s=this.addPoint(c),o=this.canvas.contextTop;this._saveAndTransform(o),this.dot(o,s),o.restore()},dot:function(c,s){c.fillStyle=s.fill,c.beginPath(),c.arc(s.x,s.y,s.radius,0,Math.PI*2,!1),c.closePath(),c.fill()},onMouseDown:function(c){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(c)},_render:function(){var c=this.canvas.contextTop,s,o,a=this.points;for(this._saveAndTransform(c),s=0,o=a.length;s<o;s++)this.dot(c,a[s]);c.restore()},onMouseMove:function(c){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c)||(this.needsFullRender()?(this.canvas.clearContext(this.canvas.contextTop),this.addPoint(c),this._render()):this.drawDot(c))},onMouseUp:function(){var c=this.canvas.renderOnAddRemove,s,o;this.canvas.renderOnAddRemove=!1;var a=[];for(s=0,o=this.points.length;s<o;s++){var e=this.points[s],i=new f.Circle({radius:e.radius,left:e.x,top:e.y,originX:"center",originY:"center",fill:e.fill});this.shadow&&(i.shadow=new f.Shadow(this.shadow)),a.push(i)}var t=new f.Group(a);t.canvas=this.canvas,this.canvas.fire("before:path:created",{path:t}),this.canvas.add(t),this.canvas.fire("path:created",{path:t}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=c,this.canvas.requestRenderAll()},addPoint:function(c){var s=new f.Point(c.x,c.y),o=f.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,a=new f.Color(this.color).setAlpha(f.util.getRandomInt(0,100)/100).toRgba();return s.radius=o,s.fill=a,this.points.push(s),s}}),f.SprayBrush=f.util.createClass(f.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(c){this.canvas=c,this.sprayChunks=[]},onMouseDown:function(c){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(c),this.render(this.sprayChunkPoints)},onMouseMove:function(c){this.limitedToCanvasSize===!0&&this._isOutSideCanvas(c)||(this.addSprayChunk(c),this.render(this.sprayChunkPoints))},onMouseUp:function(){var c=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var s=[],o=0,a=this.sprayChunks.length;o<a;o++)for(var e=this.sprayChunks[o],i=0,t=e.length;i<t;i++){var n=new f.Rect({width:e[i].width,height:e[i].width,left:e[i].x+1,top:e[i].y+1,originX:"center",originY:"center",fill:this.color});s.push(n)}this.optimizeOverlapping&&(s=this._getOptimizedRects(s));var h=new f.Group(s);this.shadow&&h.set("shadow",new f.Shadow(this.shadow)),this.canvas.fire("before:path:created",{path:h}),this.canvas.add(h),this.canvas.fire("path:created",{path:h}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=c,this.canvas.requestRenderAll()},_getOptimizedRects:function(c){var s={},o,a,e;for(a=0,e=c.length;a<e;a++)o=c[a].left+""+c[a].top,s[o]||(s[o]=c[a]);var i=[];for(o in s)i.push(s[o]);return i},render:function(c){var s=this.canvas.contextTop,o,a;for(s.fillStyle=this.color,this._saveAndTransform(s),o=0,a=c.length;o<a;o++){var e=c[o];typeof e.opacity!="undefined"&&(s.globalAlpha=e.opacity),s.fillRect(e.x,e.y,e.width,e.width)}s.restore()},_render:function(){var c=this.canvas.contextTop,s,o;for(c.fillStyle=this.color,this._saveAndTransform(c),s=0,o=this.sprayChunks.length;s<o;s++)this.render(this.sprayChunks[s]);c.restore()},addSprayChunk:function(c){this.sprayChunkPoints=[];var s,o,a,e=this.width/2,i;for(i=0;i<this.density;i++){s=f.util.getRandomInt(c.x-e,c.x+e),o=f.util.getRandomInt(c.y-e,c.y+e),this.dotWidthVariance?a=f.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):a=this.dotWidth;var t=new f.Point(s,o);t.width=a,this.randomOpacity&&(t.opacity=f.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(t)}this.sprayChunks.push(this.sprayChunkPoints)}}),f.PatternBrush=f.util.createClass(f.PencilBrush,{getPatternSrc:function(){var c=20,s=5,o=f.util.createCanvasElement(),a=o.getContext("2d");return o.width=o.height=c+s,a.fillStyle=this.color,a.beginPath(),a.arc(c/2,c/2,c/2,0,Math.PI*2,!1),a.closePath(),a.fill(),o},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(){return this.canvas.contextTop.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(){this.callSuper("_setBrushStyles"),this.canvas.contextTop.strokeStyle=this.getPattern()},createPath:function(c){var s=this.callSuper("createPath",c),o=s._getLeftTopCoords().scalarAdd(s.strokeWidth/2);return s.stroke=new f.Pattern({source:this.source||this.getPatternSrcFunction(),offsetX:-o.x,offsetY:-o.y}),s}}),function(){var c=f.util.getPointer,s=f.util.degreesToRadians,o=f.util.isTouchEvent;f.Canvas=f.util.createClass(f.StaticCanvas,{initialize:function(e,i){i||(i={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(e,i),this._initInteractive(),this._createCacheCanvas()},uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",interactive:!0,selection:!0,selectionKey:"shiftKey",altSelectionKey:null,selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",rotationCursor:"crosshair",notAllowedCursor:"not-allowed",containerClass:"canvas-container",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,isDrawingMode:!1,preserveObjectStacking:!1,snapAngle:0,snapThreshold:null,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,targets:[],_hoveredTarget:null,_hoveredTargets:[],_initInteractive:function(){this._currentTransform=null,this._groupSelector=null,this._initWrapperElement(),this._createUpperCanvas(),this._initEventListeners(),this._initRetinaScaling(),this.freeDrawingBrush=f.PencilBrush&&new f.PencilBrush(this),this.calcOffset()},_chooseObjectsToRender:function(){var e=this.getActiveObjects(),i,t,n;if(e.length>0&&!this.preserveObjectStacking){t=[],n=[];for(var h=0,r=this._objects.length;h<r;h++)i=this._objects[h],e.indexOf(i)===-1?t.push(i):n.push(i);e.length>1&&(this._activeObject._objects=n),t.push.apply(t,n)}else t=this._objects;return t},renderAll:function(){this.contextTopDirty&&!this._groupSelector&&!this.isDrawingMode&&(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&this.renderTopLayer(this.contextTop);var e=this.contextContainer;return this.renderCanvas(e,this._chooseObjectsToRender()),this},renderTopLayer:function(e){e.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(e),this.contextTopDirty=!0),e.restore()},renderTop:function(){var e=this.contextTop;return this.clearContext(e),this.renderTopLayer(e),this.fire("after:render"),this},_normalizePointer:function(e,i){var t=e.calcTransformMatrix(),n=f.util.invertTransform(t),h=this.restorePointerVpt(i);return f.util.transformPoint(h,n)},isTargetTransparent:function(e,i,t){if(e.shouldCache()&&e._cacheCanvas&&e!==this._activeObject){var n=this._normalizePointer(e,{x:i,y:t}),h=Math.max(e.cacheTranslationX+n.x*e.zoomX,0),r=Math.max(e.cacheTranslationY+n.y*e.zoomY,0),g=f.util.isTransparent(e._cacheContext,Math.round(h),Math.round(r),this.targetFindTolerance);return g}var l=this.contextCache,u=e.selectionBackgroundColor,d=this.viewportTransform;e.selectionBackgroundColor="",this.clearContext(l),l.save(),l.transform(d[0],d[1],d[2],d[3],d[4],d[5]),e.render(l),l.restore(),e.selectionBackgroundColor=u;var g=f.util.isTransparent(l,i,t,this.targetFindTolerance);return g},_isSelectionKeyPressed:function(e){var i=!1;return Object.prototype.toString.call(this.selectionKey)==="[object Array]"?i=!!this.selectionKey.find(function(t){return e[t]===!0}):i=e[this.selectionKey],i},_shouldClearSelection:function(e,i){var t=this.getActiveObjects(),n=this._activeObject;return!i||i&&n&&t.length>1&&t.indexOf(i)===-1&&n!==i&&!this._isSelectionKeyPressed(e)||i&&!i.evented||i&&!i.selectable&&n&&n!==i},_shouldCenterTransform:function(e,i,t){if(e){var n;return i==="scale"||i==="scaleX"||i==="scaleY"||i==="resizing"?n=this.centeredScaling||e.centeredScaling:i==="rotate"&&(n=this.centeredRotation||e.centeredRotation),n?!t:t}},_getOriginFromCorner:function(e,i){var t={x:e.originX,y:e.originY};return i==="ml"||i==="tl"||i==="bl"?t.x="right":(i==="mr"||i==="tr"||i==="br")&&(t.x="left"),i==="tl"||i==="mt"||i==="tr"?t.y="bottom":(i==="bl"||i==="mb"||i==="br")&&(t.y="top"),t},_getActionFromCorner:function(e,i,t,n){if(!i||!e)return"drag";var h=n.controls[i];return h.getActionName(t,h,n)},_setupCurrentTransform:function(e,i,t){if(i){var n=this.getPointer(e),h=i.__corner,r=i.controls[h],l=t&&h?r.getActionHandler(e,i,r):f.controlsUtils.dragHandler,u=this._getActionFromCorner(t,h,e,i),d=this._getOriginFromCorner(i,h),g=e[this.centeredKey],m={target:i,action:u,actionHandler:l,corner:h,scaleX:i.scaleX,scaleY:i.scaleY,skewX:i.skewX,skewY:i.skewY,offsetX:n.x-i.left,offsetY:n.y-i.top,originX:d.x,originY:d.y,ex:n.x,ey:n.y,lastX:n.x,lastY:n.y,theta:s(i.angle),width:i.width*i.scaleX,shiftKey:e.shiftKey,altKey:g,original:f.util.saveObjectTransform(i)};this._shouldCenterTransform(i,u,g)&&(m.originX="center",m.originY="center"),m.original.originX=d.x,m.original.originY=d.y,this._currentTransform=m,this._beforeTransform(e)}},setCursor:function(e){this.upperCanvasEl.style.cursor=e},_drawSelection:function(e){var i=this._groupSelector,t=new f.Point(i.ex,i.ey),n=f.util.transformPoint(t,this.viewportTransform),h=new f.Point(i.ex+i.left,i.ey+i.top),r=f.util.transformPoint(h,this.viewportTransform),l=Math.min(n.x,r.x),u=Math.min(n.y,r.y),d=Math.max(n.x,r.x),g=Math.max(n.y,r.y),m=this.selectionLineWidth/2;this.selectionColor&&(e.fillStyle=this.selectionColor,e.fillRect(l,u,d-l,g-u)),!(!this.selectionLineWidth||!this.selectionBorderColor)&&(e.lineWidth=this.selectionLineWidth,e.strokeStyle=this.selectionBorderColor,l+=m,u+=m,d-=m,g-=m,f.Object.prototype._setLineDash.call(this,e,this.selectionDashArray),e.strokeRect(l,u,d-l,g-u))},findTarget:function(e,i){if(!this.skipTargetFind){var t=!0,n=this.getPointer(e,t),h=this._activeObject,r=this.getActiveObjects(),l,u,d=o(e),g=r.length>1&&!i||r.length===1;if(this.targets=[],g&&h._findTargetCorner(n,d)||r.length>1&&!i&&h===this._searchPossibleTargets([h],n))return h;if(r.length===1&&h===this._searchPossibleTargets([h],n))if(this.preserveObjectStacking)l=h,u=this.targets,this.targets=[];else return h;var m=this._searchPossibleTargets(this._objects,n);return e[this.altSelectionKey]&&m&&l&&m!==l&&(m=l,this.targets=u),m}},_checkTarget:function(e,i,t){if(i&&i.visible&&i.evented&&i.containsPoint(e))if((this.perPixelTargetFind||i.perPixelTargetFind)&&!i.isEditing){var n=this.isTargetTransparent(i,t.x,t.y);if(!n)return!0}else return!0},_searchPossibleTargets:function(e,i){for(var t,n=e.length,h;n--;){var r=e[n],l=r.group?this._normalizePointer(r.group,i):i;if(this._checkTarget(l,r,i)){t=e[n],t.subTargetCheck&&t instanceof f.Group&&(h=this._searchPossibleTargets(t._objects,i),h&&this.targets.push(h));break}}return t},restorePointerVpt:function(e){return f.util.transformPoint(e,f.util.invertTransform(this.viewportTransform))},getPointer:function(e,i){if(this._absolutePointer&&!i)return this._absolutePointer;if(this._pointer&&i)return this._pointer;var t=c(e),n=this.upperCanvasEl,h=n.getBoundingClientRect(),r=h.width||0,l=h.height||0,u;(!r||!l)&&("top"in h&&"bottom"in h&&(l=Math.abs(h.top-h.bottom)),"right"in h&&"left"in h&&(r=Math.abs(h.right-h.left))),this.calcOffset(),t.x=t.x-this._offset.left,t.y=t.y-this._offset.top,i||(t=this.restorePointerVpt(t));var d=this.getRetinaScaling();return d!==1&&(t.x/=d,t.y/=d),r===0||l===0?u={width:1,height:1}:u={width:n.width/r,height:n.height/l},{x:t.x*u.width,y:t.y*u.height}},_createUpperCanvas:function(){var e=this.lowerCanvasEl.className.replace(/\s*lower-canvas\s*/,""),i=this.lowerCanvasEl,t=this.upperCanvasEl;t?t.className="":(t=this._createCanvasElement(),this.upperCanvasEl=t),f.util.addClass(t,"upper-canvas "+e),this.wrapperEl.appendChild(t),this._copyCanvasStyle(i,t),this._applyCanvasStyle(t),this.contextTop=t.getContext("2d")},_createCacheCanvas:function(){this.cacheCanvasEl=this._createCanvasElement(),this.cacheCanvasEl.setAttribute("width",this.width),this.cacheCanvasEl.setAttribute("height",this.height),this.contextCache=this.cacheCanvasEl.getContext("2d")},_initWrapperElement:function(){this.wrapperEl=f.util.wrapElement(this.lowerCanvasEl,"div",{class:this.containerClass}),f.util.setStyle(this.wrapperEl,{width:this.width+"px",height:this.height+"px",position:"relative"}),f.util.makeElementUnselectable(this.wrapperEl)},_applyCanvasStyle:function(e){var i=this.width||e.width,t=this.height||e.height;f.util.setStyle(e,{position:"absolute",width:i+"px",height:t+"px",left:0,top:0,"touch-action":this.allowTouchScrolling?"manipulation":"none","-ms-touch-action":this.allowTouchScrolling?"manipulation":"none"}),e.width=i,e.height=t,f.util.makeElementUnselectable(e)},_copyCanvasStyle:function(e,i){i.style.cssText=e.style.cssText},getSelectionContext:function(){return this.contextTop},getSelectionElement:function(){return this.upperCanvasEl},getActiveObject:function(){return this._activeObject},getActiveObjects:function(){var e=this._activeObject;return e?e.type==="activeSelection"&&e._objects?e._objects.slice(0):[e]:[]},_onObjectRemoved:function(e){e===this._activeObject&&(this.fire("before:selection:cleared",{target:e}),this._discardActiveObject(),this.fire("selection:cleared",{target:e}),e.fire("deselected")),e===this._hoveredTarget&&(this._hoveredTarget=null,this._hoveredTargets=[]),this.callSuper("_onObjectRemoved",e)},_fireSelectionEvents:function(e,i){var t=!1,n=this.getActiveObjects(),h=[],r=[];e.forEach(function(l){n.indexOf(l)===-1&&(t=!0,l.fire("deselected",{e:i,target:l}),r.push(l))}),n.forEach(function(l){e.indexOf(l)===-1&&(t=!0,l.fire("selected",{e:i,target:l}),h.push(l))}),e.length>0&&n.length>0?t&&this.fire("selection:updated",{e:i,selected:h,deselected:r,updated:h[0]||r[0],target:this._activeObject}):n.length>0?this.fire("selection:created",{e:i,selected:h,target:this._activeObject}):e.length>0&&this.fire("selection:cleared",{e:i,deselected:r})},setActiveObject:function(e,i){var t=this.getActiveObjects();return this._setActiveObject(e,i),this._fireSelectionEvents(t,i),this},_setActiveObject:function(e,i){return this._activeObject===e||!this._discardActiveObject(i,e)||e.onSelect({e:i})?!1:(this._activeObject=e,!0)},_discardActiveObject:function(e,i){var t=this._activeObject;if(t){if(t.onDeselect({e,object:i}))return!1;this._activeObject=null}return!0},discardActiveObject:function(e){var i=this.getActiveObjects(),t=this.getActiveObject();return i.length&&this.fire("before:selection:cleared",{target:t,e}),this._discardActiveObject(e),this._fireSelectionEvents(i,e),this},dispose:function(){var e=this.wrapperEl;return this.removeListeners(),e.removeChild(this.upperCanvasEl),e.removeChild(this.lowerCanvasEl),this.contextCache=null,this.contextTop=null,["upperCanvasEl","cacheCanvasEl"].forEach(function(i){f.util.cleanUpJsdomNode(this[i]),this[i]=void 0}.bind(this)),e.parentNode&&e.parentNode.replaceChild(this.lowerCanvasEl,this.wrapperEl),delete this.wrapperEl,f.StaticCanvas.prototype.dispose.call(this),this},clear:function(){return this.discardActiveObject(),this.clearContext(this.contextTop),this.callSuper("clear")},drawControls:function(e){var i=this._activeObject;i&&i._renderControls(e)},_toObject:function(e,i,t){var n=this._realizeGroupTransformOnObject(e),h=this.callSuper("_toObject",e,i,t);return this._unwindGroupTransformOnObject(e,n),h},_realizeGroupTransformOnObject:function(e){if(e.group&&e.group.type==="activeSelection"&&this._activeObject===e.group){var i=["angle","flipX","flipY","left","scaleX","scaleY","skewX","skewY","top"],t={};return i.forEach(function(n){t[n]=e[n]}),f.util.addTransformToObject(e,this._activeObject.calcOwnMatrix()),t}else return null},_unwindGroupTransformOnObject:function(e,i){i&&e.set(i)},_setSVGObject:function(e,i,t){var n=this._realizeGroupTransformOnObject(i);this.callSuper("_setSVGObject",e,i,t),this._unwindGroupTransformOnObject(i,n)},setViewportTransform:function(e){this.renderOnAddRemove&&this._activeObject&&this._activeObject.isEditing&&this._activeObject.clearContextTop(),f.StaticCanvas.prototype.setViewportTransform.call(this,e)}});for(var a in f.StaticCanvas)a!=="prototype"&&(f.Canvas[a]=f.StaticCanvas[a])}(),function(){var c=f.util.addListener,s=f.util.removeListener,o=3,a=2,e=1,i={passive:!1};function t(n,h){return n.button&&n.button===h-1}f.util.object.extend(f.Canvas.prototype,{mainTouchId:null,_initEventListeners:function(){this.removeListeners(),this._bindEvents(),this.addOrRemove(c,"add")},_getEventPrefix:function(){return this.enablePointerEvents?"pointer":"mouse"},addOrRemove:function(n,h){var r=this.upperCanvasEl,l=this._getEventPrefix();n(f.window,"resize",this._onResize),n(r,l+"down",this._onMouseDown),n(r,l+"move",this._onMouseMove,i),n(r,l+"out",this._onMouseOut),n(r,l+"enter",this._onMouseEnter),n(r,"wheel",this._onMouseWheel),n(r,"contextmenu",this._onContextMenu),n(r,"dblclick",this._onDoubleClick),n(r,"dragover",this._onDragOver),n(r,"dragenter",this._onDragEnter),n(r,"dragleave",this._onDragLeave),n(r,"drop",this._onDrop),this.enablePointerEvents||n(r,"touchstart",this._onTouchStart,i),typeof eventjs!="undefined"&&h in eventjs&&(eventjs[h](r,"gesture",this._onGesture),eventjs[h](r,"drag",this._onDrag),eventjs[h](r,"orientation",this._onOrientationChange),eventjs[h](r,"shake",this._onShake),eventjs[h](r,"longpress",this._onLongPress))},removeListeners:function(){this.addOrRemove(s,"remove");var n=this._getEventPrefix();s(f.document,n+"up",this._onMouseUp),s(f.document,"touchend",this._onTouchEnd,i),s(f.document,n+"move",this._onMouseMove,i),s(f.document,"touchmove",this._onMouseMove,i)},_bindEvents:function(){this.eventsBound||(this._onMouseDown=this._onMouseDown.bind(this),this._onTouchStart=this._onTouchStart.bind(this),this._onMouseMove=this._onMouseMove.bind(this),this._onMouseUp=this._onMouseUp.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onResize=this._onResize.bind(this),this._onGesture=this._onGesture.bind(this),this._onDrag=this._onDrag.bind(this),this._onShake=this._onShake.bind(this),this._onLongPress=this._onLongPress.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._onMouseWheel=this._onMouseWheel.bind(this),this._onMouseOut=this._onMouseOut.bind(this),this._onMouseEnter=this._onMouseEnter.bind(this),this._onContextMenu=this._onContextMenu.bind(this),this._onDoubleClick=this._onDoubleClick.bind(this),this._onDragOver=this._onDragOver.bind(this),this._onDragEnter=this._simpleEventHandler.bind(this,"dragenter"),this._onDragLeave=this._simpleEventHandler.bind(this,"dragleave"),this._onDrop=this._simpleEventHandler.bind(this,"drop"),this.eventsBound=!0)},_onGesture:function(n,h){this.__onTransformGesture&&this.__onTransformGesture(n,h)},_onDrag:function(n,h){this.__onDrag&&this.__onDrag(n,h)},_onMouseWheel:function(n){this.__onMouseWheel(n)},_onMouseOut:function(n){var h=this._hoveredTarget;this.fire("mouse:out",{target:h,e:n}),this._hoveredTarget=null,h&&h.fire("mouseout",{e:n});var r=this;this._hoveredTargets.forEach(function(l){r.fire("mouse:out",{target:h,e:n}),l&&h.fire("mouseout",{e:n})}),this._hoveredTargets=[],this._iTextInstances&&this._iTextInstances.forEach(function(l){l.isEditing&&l.hiddenTextarea.focus()})},_onMouseEnter:function(n){!this._currentTransform&&!this.findTarget(n)&&(this.fire("mouse:over",{target:null,e:n}),this._hoveredTarget=null,this._hoveredTargets=[])},_onOrientationChange:function(n,h){this.__onOrientationChange&&this.__onOrientationChange(n,h)},_onShake:function(n,h){this.__onShake&&this.__onShake(n,h)},_onLongPress:function(n,h){this.__onLongPress&&this.__onLongPress(n,h)},_onDragOver:function(n){n.preventDefault();var h=this._simpleEventHandler("dragover",n);this._fireEnterLeaveEvents(h,n)},_onContextMenu:function(n){return this.stopContextMenu&&(n.stopPropagation(),n.preventDefault()),!1},_onDoubleClick:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"dblclick"),this._resetTransformEventData(n)},getPointerId:function(n){var h=n.changedTouches;return h?h[0]&&h[0].identifier:this.enablePointerEvents?n.pointerId:-1},_isMainEvent:function(n){return n.isPrimary===!0?!0:n.isPrimary===!1?!1:n.type==="touchend"&&n.touches.length===0?!0:n.changedTouches?n.changedTouches[0].identifier===this.mainTouchId:!0},_onTouchStart:function(n){n.preventDefault(),this.mainTouchId===null&&(this.mainTouchId=this.getPointerId(n)),this.__onMouseDown(n),this._resetTransformEventData();var h=this.upperCanvasEl,r=this._getEventPrefix();c(f.document,"touchend",this._onTouchEnd,i),c(f.document,"touchmove",this._onMouseMove,i),s(h,r+"down",this._onMouseDown)},_onMouseDown:function(n){this.__onMouseDown(n),this._resetTransformEventData();var h=this.upperCanvasEl,r=this._getEventPrefix();s(h,r+"move",this._onMouseMove,i),c(f.document,r+"up",this._onMouseUp),c(f.document,r+"move",this._onMouseMove,i)},_onTouchEnd:function(n){if(!(n.touches.length>0)){this.__onMouseUp(n),this._resetTransformEventData(),this.mainTouchId=null;var h=this._getEventPrefix();s(f.document,"touchend",this._onTouchEnd,i),s(f.document,"touchmove",this._onMouseMove,i);var r=this;this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(function(){c(r.upperCanvasEl,h+"down",r._onMouseDown),r._willAddMouseDown=0},400)}},_onMouseUp:function(n){this.__onMouseUp(n),this._resetTransformEventData();var h=this.upperCanvasEl,r=this._getEventPrefix();this._isMainEvent(n)&&(s(f.document,r+"up",this._onMouseUp),s(f.document,r+"move",this._onMouseMove,i),c(h,r+"move",this._onMouseMove,i))},_onMouseMove:function(n){!this.allowTouchScrolling&&n.preventDefault&&n.preventDefault(),this.__onMouseMove(n)},_onResize:function(){this.calcOffset()},_shouldRender:function(n){var h=this._activeObject;return!!h!=!!n||h&&n&&h!==n?!0:(h&&h.isEditing,!1)},__onMouseUp:function(n){var h,r=this._currentTransform,l=this._groupSelector,u=!1,d=!l||l.left===0&&l.top===0;if(this._cacheTransformEventData(n),h=this._target,this._handleEvent(n,"up:before"),t(n,o)){this.fireRightClick&&this._handleEvent(n,"up",o,d);return}if(t(n,a)){this.fireMiddleClick&&this._handleEvent(n,"up",a,d),this._resetTransformEventData();return}if(this.isDrawingMode&&this._isCurrentlyDrawing){this._onMouseUpInDrawingMode(n);return}if(this._isMainEvent(n)){if(r&&(this._finalizeCurrentTransform(n),u=r.actionPerformed),!d){var g=h===this._activeObject;this._maybeGroupObjects(n),u||(u=this._shouldRender(h)||!g&&h===this._activeObject)}if(h){if(h.selectable&&h!==this._activeObject&&h.activeOn==="up")this.setActiveObject(h,n),u=!0;else{var m=h._findTargetCorner(this.getPointer(n,!0),f.util.isTouchEvent(n)),v=h.controls[m],y=v&&v.getMouseUpHandler(n,h,v);if(y){var T=this.getPointer(n);y(n,r,T.x,T.y)}}h.isMoving=!1}this._setCursorFromEvent(n,h),this._handleEvent(n,"up",e,d),this._groupSelector=null,this._currentTransform=null,h&&(h.__corner=0),u?this.requestRenderAll():d||this.renderTop()}},_simpleEventHandler:function(n,h){var r=this.findTarget(h),l=this.targets,u={e:h,target:r,subTargets:l};if(this.fire(n,u),r&&r.fire(n,u),!l)return r;for(var d=0;d<l.length;d++)l[d].fire(n,u);return r},_handleEvent:function(n,h,r,l){var u=this._target,d=this.targets||[],g={e:n,target:u,subTargets:d,button:r||e,isClick:l||!1,pointer:this._pointer,absolutePointer:this._absolutePointer,transform:this._currentTransform};h==="up"&&(g.currentTarget=this.findTarget(n),g.currentSubTargets=this.targets),this.fire("mouse:"+h,g),u&&u.fire("mouse"+h,g);for(var m=0;m<d.length;m++)d[m].fire("mouse"+h,g)},_finalizeCurrentTransform:function(n){var h=this._currentTransform,r=h.target,l,u={e:n,target:r,transform:h,action:h.action};r._scaling&&(r._scaling=!1),r.setCoords(),(h.actionPerformed||this.stateful&&r.hasStateChanged())&&(h.actionPerformed&&(l=this._addEventOptions(u,h),this._fire(l,u)),this._fire("modified",u))},_addEventOptions:function(n,h){var r,l;switch(h.action){case"scaleX":r="scaled",l="x";break;case"scaleY":r="scaled",l="y";break;case"skewX":r="skewed",l="x";break;case"skewY":r="skewed",l="y";break;case"scale":r="scaled",l="equally";break;case"rotate":r="rotated";break;case"drag":r="moved";break}return n.by=l,r},_onMouseDownInDrawingMode:function(n){this._isCurrentlyDrawing=!0,this.getActiveObject()&&this.discardActiveObject(n).requestRenderAll();var h=this.getPointer(n);this.freeDrawingBrush.onMouseDown(h,{e:n,pointer:h}),this._handleEvent(n,"down")},_onMouseMoveInDrawingMode:function(n){if(this._isCurrentlyDrawing){var h=this.getPointer(n);this.freeDrawingBrush.onMouseMove(h,{e:n,pointer:h})}this.setCursor(this.freeDrawingCursor),this._handleEvent(n,"move")},_onMouseUpInDrawingMode:function(n){var h=this.getPointer(n);this._isCurrentlyDrawing=this.freeDrawingBrush.onMouseUp({e:n,pointer:h}),this._handleEvent(n,"up")},__onMouseDown:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"down:before");var h=this._target;if(t(n,o)){this.fireRightClick&&this._handleEvent(n,"down",o);return}if(t(n,a)){this.fireMiddleClick&&this._handleEvent(n,"down",a);return}if(this.isDrawingMode){this._onMouseDownInDrawingMode(n);return}if(this._isMainEvent(n)&&!this._currentTransform){var r=this._pointer;this._previousPointer=r;var l=this._shouldRender(h),u=this._shouldGroup(n,h);if(this._shouldClearSelection(n,h)?this.discardActiveObject(n):u&&(this._handleGrouping(n,h),h=this._activeObject),this.selection&&(!h||!h.selectable&&!h.isEditing&&h!==this._activeObject)&&(this._groupSelector={ex:this._absolutePointer.x,ey:this._absolutePointer.y,top:0,left:0}),h){var d=h===this._activeObject;h.selectable&&h.activeOn==="down"&&this.setActiveObject(h,n);var g=h._findTargetCorner(this.getPointer(n,!0),f.util.isTouchEvent(n));if(h.__corner=g,h===this._activeObject&&(g||!u)){this._setupCurrentTransform(n,h,d);var m=h.controls[g],r=this.getPointer(n),v=m&&m.getMouseDownHandler(n,h,m);v&&v(n,this._currentTransform,r.x,r.y)}}this._handleEvent(n,"down"),(l||u)&&this.requestRenderAll()}},_resetTransformEventData:function(){this._target=null,this._pointer=null,this._absolutePointer=null},_cacheTransformEventData:function(n){this._resetTransformEventData(),this._pointer=this.getPointer(n,!0),this._absolutePointer=this.restorePointerVpt(this._pointer),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(n)||null},_beforeTransform:function(n){var h=this._currentTransform;this.stateful&&h.target.saveState(),this.fire("before:transform",{e:n,transform:h})},__onMouseMove:function(n){this._handleEvent(n,"move:before"),this._cacheTransformEventData(n);var h,r;if(this.isDrawingMode){this._onMouseMoveInDrawingMode(n);return}if(this._isMainEvent(n)){var l=this._groupSelector;l?(r=this._absolutePointer,l.left=r.x-l.ex,l.top=r.y-l.ey,this.renderTop()):this._currentTransform?this._transformObject(n):(h=this.findTarget(n)||null,this._setCursorFromEvent(n,h),this._fireOverOutEvents(h,n)),this._handleEvent(n,"move"),this._resetTransformEventData()}},_fireOverOutEvents:function(n,h){var r=this._hoveredTarget,l=this._hoveredTargets,u=this.targets,d=Math.max(l.length,u.length);this.fireSyntheticInOutEvents(n,h,{oldTarget:r,evtOut:"mouseout",canvasEvtOut:"mouse:out",evtIn:"mouseover",canvasEvtIn:"mouse:over"});for(var g=0;g<d;g++)this.fireSyntheticInOutEvents(u[g],h,{oldTarget:l[g],evtOut:"mouseout",evtIn:"mouseover"});this._hoveredTarget=n,this._hoveredTargets=this.targets.concat()},_fireEnterLeaveEvents:function(n,h){var r=this._draggedoverTarget,l=this._hoveredTargets,u=this.targets,d=Math.max(l.length,u.length);this.fireSyntheticInOutEvents(n,h,{oldTarget:r,evtOut:"dragleave",evtIn:"dragenter"});for(var g=0;g<d;g++)this.fireSyntheticInOutEvents(u[g],h,{oldTarget:l[g],evtOut:"dragleave",evtIn:"dragenter"});this._draggedoverTarget=n},fireSyntheticInOutEvents:function(n,h,r){var l,u,d=r.oldTarget,g,m,v=d!==n,y=r.canvasEvtIn,T=r.canvasEvtOut;v&&(l={e:h,target:n,previousTarget:d},u={e:h,target:d,nextTarget:n}),m=n&&v,g=d&&v,g&&(T&&this.fire(T,u),d.fire(r.evtOut,u)),m&&(y&&this.fire(y,l),n.fire(r.evtIn,l))},__onMouseWheel:function(n){this._cacheTransformEventData(n),this._handleEvent(n,"wheel"),this._resetTransformEventData()},_transformObject:function(n){var h=this.getPointer(n),r=this._currentTransform;r.reset=!1,r.shiftKey=n.shiftKey,r.altKey=n[this.centeredKey],this._performTransformAction(n,r,h),r.actionPerformed&&this.requestRenderAll()},_performTransformAction:function(n,h,r){var l=r.x,u=r.y,d=h.action,g=!1,m=h.actionHandler;m&&(g=m(n,h,l,u)),d==="drag"&&g&&(h.target.isMoving=!0,this.setCursor(h.target.moveCursor||this.moveCursor)),h.actionPerformed=h.actionPerformed||g},_fire:f.controlsUtils.fireEvent,_setCursorFromEvent:function(n,h){if(!h)return this.setCursor(this.defaultCursor),!1;var r=h.hoverCursor||this.hoverCursor,l=this._activeObject&&this._activeObject.type==="activeSelection"?this._activeObject:null,u=(!l||!l.contains(h))&&h._findTargetCorner(this.getPointer(n,!0));u?this.setCursor(this.getCornerCursor(u,h,n)):(h.subTargetCheck&&this.targets.concat().reverse().map(function(d){r=d.hoverCursor||r}),this.setCursor(r))},getCornerCursor:function(n,h,r){var l=h.controls[n];return l.cursorStyleHandler(r,l,h)}})}(),function(){var c=Math.min,s=Math.max;f.util.object.extend(f.Canvas.prototype,{_shouldGroup:function(o,a){var e=this._activeObject;return e&&this._isSelectionKeyPressed(o)&&a&&a.selectable&&this.selection&&(e!==a||e.type==="activeSelection")&&!a.onSelect({e:o})},_handleGrouping:function(o,a){var e=this._activeObject;e.__corner||a===e&&(a=this.findTarget(o,!0),!a||!a.selectable)||(e&&e.type==="activeSelection"?this._updateActiveSelection(a,o):this._createActiveSelection(a,o))},_updateActiveSelection:function(o,a){var e=this._activeObject,i=e._objects.slice(0);e.contains(o)?(e.removeWithUpdate(o),this._hoveredTarget=o,this._hoveredTargets=this.targets.concat(),e.size()===1&&this._setActiveObject(e.item(0),a)):(e.addWithUpdate(o),this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()),this._fireSelectionEvents(i,a)},_createActiveSelection:function(o,a){var e=this.getActiveObjects(),i=this._createGroup(o);this._hoveredTarget=i,this._setActiveObject(i,a),this._fireSelectionEvents(e,a)},_createGroup:function(o){var a=this._objects,e=a.indexOf(this._activeObject)<a.indexOf(o),i=e?[this._activeObject,o]:[o,this._activeObject];return this._activeObject.isEditing&&this._activeObject.exitEditing(),new f.ActiveSelection(i,{canvas:this})},_groupSelectedObjects:function(o){var a=this._collectObjects(o),e;a.length===1?this.setActiveObject(a[0],o):a.length>1&&(e=new f.ActiveSelection(a.reverse(),{canvas:this}),this.setActiveObject(e,o))},_collectObjects:function(o){for(var a=[],e,i=this._groupSelector.ex,t=this._groupSelector.ey,n=i+this._groupSelector.left,h=t+this._groupSelector.top,r=new f.Point(c(i,n),c(t,h)),l=new f.Point(s(i,n),s(t,h)),u=!this.selectionFullyContained,d=i===n&&t===h,g=this._objects.length;g--&&(e=this._objects[g],!(!(!e||!e.selectable||!e.visible)&&(u&&e.intersectsWithRect(r,l,!0)||e.isContainedWithinRect(r,l,!0)||u&&e.containsPoint(r,null,!0)||u&&e.containsPoint(l,null,!0))&&(a.push(e),d))););return a.length>1&&(a=a.filter(function(m){return!m.onSelect({e:o})})),a},_maybeGroupObjects:function(o){this.selection&&this._groupSelector&&this._groupSelectedObjects(o),this.setCursor(this.defaultCursor),this._groupSelector=null}})}(),function(){f.util.object.extend(f.StaticCanvas.prototype,{toDataURL:function(c){c||(c={});var s=c.format||"png",o=c.quality||1,a=(c.multiplier||1)*(c.enableRetinaScaling?this.getRetinaScaling():1),e=this.toCanvasElement(a,c);return f.util.toDataURL(e,s,o)},toCanvasElement:function(c,s){c=c||1,s=s||{};var o=(s.width||this.width)*c,a=(s.height||this.height)*c,e=this.getZoom(),i=this.width,t=this.height,n=e*c,h=this.viewportTransform,r=(h[4]-(s.left||0))*c,l=(h[5]-(s.top||0))*c,u=this.interactive,d=[n,0,0,n,r,l],g=this.enableRetinaScaling,m=f.util.createCanvasElement(),v=this.contextTop;return m.width=o,m.height=a,this.contextTop=null,this.enableRetinaScaling=!1,this.interactive=!1,this.viewportTransform=d,this.width=o,this.height=a,this.calcViewportBoundaries(),this.renderCanvas(m.getContext("2d"),this._objects),this.viewportTransform=h,this.width=i,this.height=t,this.calcViewportBoundaries(),this.interactive=u,this.enableRetinaScaling=g,this.contextTop=v,m}})}(),f.util.object.extend(f.StaticCanvas.prototype,{loadFromJSON:function(c,s,o){if(c){var a=typeof c=="string"?JSON.parse(c):f.util.object.clone(c),e=this,i=a.clipPath,t=this.renderOnAddRemove;return this.renderOnAddRemove=!1,delete a.clipPath,this._enlivenObjects(a.objects,function(n){e.clear(),e._setBgOverlay(a,function(){i?e._enlivenObjects([i],function(h){e.clipPath=h[0],e.__setupCanvas.call(e,a,n,t,s)}):e.__setupCanvas.call(e,a,n,t,s)})},o),this}},__setupCanvas:function(c,s,o,a){var e=this;s.forEach(function(i,t){e.insertAt(i,t)}),this.renderOnAddRemove=o,delete c.objects,delete c.backgroundImage,delete c.overlayImage,delete c.background,delete c.overlay,this._setOptions(c),this.renderAll(),a&&a()},_setBgOverlay:function(c,s){var o={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(!c.backgroundImage&&!c.overlayImage&&!c.background&&!c.overlay){s&&s();return}var a=function(){o.backgroundImage&&o.overlayImage&&o.backgroundColor&&o.overlayColor&&s&&s()};this.__setBgOverlay("backgroundImage",c.backgroundImage,o,a),this.__setBgOverlay("overlayImage",c.overlayImage,o,a),this.__setBgOverlay("backgroundColor",c.background,o,a),this.__setBgOverlay("overlayColor",c.overlay,o,a)},__setBgOverlay:function(c,s,o,a){var e=this;if(!s){o[c]=!0,a&&a();return}c==="backgroundImage"||c==="overlayImage"?f.util.enlivenObjects([s],function(i){e[c]=i[0],o[c]=!0,a&&a()}):this["set"+f.util.string.capitalize(c,!0)](s,function(){o[c]=!0,a&&a()})},_enlivenObjects:function(c,s,o){if(!c||c.length===0){s&&s([]);return}f.util.enlivenObjects(c,function(a){s&&s(a)},null,o)},_toDataURL:function(c,s){this.clone(function(o){s(o.toDataURL(c))})},_toDataURLWithMultiplier:function(c,s,o){this.clone(function(a){o(a.toDataURLWithMultiplier(c,s))})},clone:function(c,s){var o=JSON.stringify(this.toJSON(s));this.cloneWithoutData(function(a){a.loadFromJSON(o,function(){c&&c(a)})})},cloneWithoutData:function(c){var s=f.util.createCanvasElement();s.width=this.width,s.height=this.height;var o=new f.Canvas(s);this.backgroundImage?(o.setBackgroundImage(this.backgroundImage.src,function(){o.renderAll(),c&&c(o)}),o.backgroundImageOpacity=this.backgroundImageOpacity,o.backgroundImageStretch=this.backgroundImageStretch):c&&c(o)}}),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.util.object.clone,e=s.util.toFixed,i=s.util.string.capitalize,t=s.util.degreesToRadians,n=!s.isLikelyNode,h=2;s.Object||(s.Object=s.util.createClass(s.CommonMethods,{type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,skewX:0,skewY:0,cornerSize:13,touchCornerSize:24,transparentCorners:!0,hoverCursor:null,moveCursor:null,padding:0,borderColor:"rgb(178,204,255)",borderDashArray:null,cornerColor:"rgb(178,204,255)",cornerStrokeColor:null,cornerStyle:"rect",cornerDashArray:null,centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"nonzero",globalCompositeOperation:"source-over",backgroundColor:"",selectionBackgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,minScaleLimit:0,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,perPixelTargetFind:!1,includeDefaultValues:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,excludeFromExport:!1,objectCaching:n,statefullCache:!1,noScaleCache:!0,strokeUniform:!1,dirty:!0,__corner:0,paintFirst:"fill",activeOn:"down",stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit angle opacity fill globalCompositeOperation shadow visible backgroundColor skewX skewY fillRule paintFirst clipPath strokeUniform".split(" "),cacheProperties:"fill stroke strokeWidth strokeDashArray width height paintFirst strokeUniform strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit backgroundColor clipPath".split(" "),colorProperties:"fill stroke backgroundColor".split(" "),clipPath:void 0,inverted:!1,absolutePositioned:!1,initialize:function(r){r&&this.setOptions(r)},_createCacheCanvas:function(){this._cacheProperties={},this._cacheCanvas=s.util.createCanvasElement(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0},_limitCacheSize:function(r){var l=s.perfLimitSizeTotal,u=r.width,d=r.height,g=s.maxCacheSideLimit,m=s.minCacheSideLimit;if(u<=g&&d<=g&&u*d<=l)return u<m&&(r.width=m),d<m&&(r.height=m),r;var v=u/d,y=s.util.limitDimsByArea(v,l),T=s.util.capValue,M=T(m,y.x,g),W=T(m,y.y,g);return u>M&&(r.zoomX/=u/M,r.width=M,r.capped=!0),d>W&&(r.zoomY/=d/W,r.height=W,r.capped=!0),r},_getCacheCanvasDimensions:function(){var r=this.getTotalObjectScaling(),l=this._getTransformedDimensions(0,0),u=l.x*r.scaleX/this.scaleX,d=l.y*r.scaleY/this.scaleY;return{width:u+h,height:d+h,zoomX:r.scaleX,zoomY:r.scaleY,x:u,y:d}},_updateCacheCanvas:function(){var r=this.canvas;if(this.noScaleCache&&r&&r._currentTransform){var l=r._currentTransform.target,u=r._currentTransform.action;if(this===l&&u.slice&&u.slice(0,5)==="scale")return!1}var d=this._cacheCanvas,g=this._limitCacheSize(this._getCacheCanvasDimensions()),m=s.minCacheSideLimit,v=g.width,y=g.height,T,M,W=g.zoomX,H=g.zoomY,G=v!==this.cacheWidth||y!==this.cacheHeight,V=this.zoomX!==W||this.zoomY!==H,U=G||V,N=0,Q=0,$=!1;if(G){var K=this._cacheCanvas.width,S=this._cacheCanvas.height,F=v>K||y>S,b=(v<K*.9||y<S*.9)&&K>m&&S>m;$=F||b,F&&!g.capped&&(v>m||y>m)&&(N=v*.1,Q=y*.1)}return this instanceof s.Text&&this.path&&(U=!0,$=!0,N+=this.getHeightOfLine(0)*this.zoomX,Q+=this.getHeightOfLine(0)*this.zoomY),U?($?(d.width=Math.ceil(v+N),d.height=Math.ceil(y+Q)):(this._cacheContext.setTransform(1,0,0,1,0,0),this._cacheContext.clearRect(0,0,d.width,d.height)),T=g.x/2,M=g.y/2,this.cacheTranslationX=Math.round(d.width/2-T)+T,this.cacheTranslationY=Math.round(d.height/2-M)+M,this.cacheWidth=v,this.cacheHeight=y,this._cacheContext.translate(this.cacheTranslationX,this.cacheTranslationY),this._cacheContext.scale(W,H),this.zoomX=W,this.zoomY=H,!0):!1},setOptions:function(r){this._setOptions(r),this._initGradient(r.fill,"fill"),this._initGradient(r.stroke,"stroke"),this._initPattern(r.fill,"fill"),this._initPattern(r.stroke,"stroke")},transform:function(r){var l=this.group&&!this.group._transformDone||this.group&&this.canvas&&r===this.canvas.contextTop,u=this.calcTransformMatrix(!l);r.transform(u[0],u[1],u[2],u[3],u[4],u[5])},toObject:function(r){var l=s.Object.NUM_FRACTION_DIGITS,u={type:this.type,version:s.version,originX:this.originX,originY:this.originY,left:e(this.left,l),top:e(this.top,l),width:e(this.width,l),height:e(this.height,l),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:e(this.strokeWidth,l),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:e(this.strokeMiterLimit,l),scaleX:e(this.scaleX,l),scaleY:e(this.scaleY,l),angle:e(this.angle,l),flipX:this.flipX,flipY:this.flipY,opacity:e(this.opacity,l),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:e(this.skewX,l),skewY:e(this.skewY,l)};return this.clipPath&&!this.clipPath.excludeFromExport&&(u.clipPath=this.clipPath.toObject(r),u.clipPath.inverted=this.clipPath.inverted,u.clipPath.absolutePositioned=this.clipPath.absolutePositioned),s.util.populateWithProperties(this,u,r),this.includeDefaultValues||(u=this._removeDefaultValues(u)),u},toDatalessObject:function(r){return this.toObject(r)},_removeDefaultValues:function(r){var l=s.util.getKlass(r.type).prototype,u=l.stateProperties;return u.forEach(function(d){if(!(d==="left"||d==="top")){r[d]===l[d]&&delete r[d];var g=Object.prototype.toString.call(r[d])==="[object Array]"&&Object.prototype.toString.call(l[d])==="[object Array]";g&&r[d].length===0&&l[d].length===0&&delete r[d]}}),r},toString:function(){return"#<fabric."+i(this.type)+">"},getObjectScaling:function(){if(!this.group)return{scaleX:this.scaleX,scaleY:this.scaleY};var r=s.util.qrDecompose(this.calcTransformMatrix());return{scaleX:Math.abs(r.scaleX),scaleY:Math.abs(r.scaleY)}},getTotalObjectScaling:function(){var r=this.getObjectScaling(),l=r.scaleX,u=r.scaleY;if(this.canvas){var d=this.canvas.getZoom(),g=this.canvas.getRetinaScaling();l*=d*g,u*=d*g}return{scaleX:l,scaleY:u}},getObjectOpacity:function(){var r=this.opacity;return this.group&&(r*=this.group.getObjectOpacity()),r},_set:function(r,l){var u=r==="scaleX"||r==="scaleY",d=this[r]!==l,g=!1;return u&&(l=this._constrainScale(l)),r==="scaleX"&&l<0?(this.flipX=!this.flipX,l*=-1):r==="scaleY"&&l<0?(this.flipY=!this.flipY,l*=-1):r==="shadow"&&l&&!(l instanceof s.Shadow)?l=new s.Shadow(l):r==="dirty"&&this.group&&this.group.set("dirty",l),this[r]=l,d&&(g=this.group&&this.group.isOnACache(),this.cacheProperties.indexOf(r)>-1?(this.dirty=!0,g&&this.group.set("dirty",!0)):g&&this.stateProperties.indexOf(r)>-1&&this.group.set("dirty",!0)),this},setOnGroup:function(){},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:s.iMatrix.concat()},isNotVisible:function(){return this.opacity===0||!this.width&&!this.height&&this.strokeWidth===0||!this.visible},render:function(r){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(r.save(),this._setupCompositeOperation(r),this.drawSelectionBackground(r),this.transform(r),this._setOpacity(r),this._setShadow(r,this),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(r)):(this._removeCacheCanvas(),this.dirty=!1,this.drawObject(r),this.objectCaching&&this.statefullCache&&this.saveState({propertySet:"cacheProperties"})),r.restore())},renderCache:function(r){r=r||{},this._cacheCanvas||this._createCacheCanvas(),this.isCacheDirty()&&(this.statefullCache&&this.saveState({propertySet:"cacheProperties"}),this.drawObject(this._cacheContext,r.forClipping),this.dirty=!1)},_removeCacheCanvas:function(){this._cacheCanvas=null,this.cacheWidth=0,this.cacheHeight=0},hasStroke:function(){return this.stroke&&this.stroke!=="transparent"&&this.strokeWidth!==0},hasFill:function(){return this.fill&&this.fill!=="transparent"},needsItsOwnCache:function(){return!!(this.paintFirst==="stroke"&&this.hasFill()&&this.hasStroke()&&typeof this.shadow=="object"||this.clipPath)},shouldCache:function(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.group||!this.group.isOnACache()),this.ownCaching},willDrawShadow:function(){return!!this.shadow&&(this.shadow.offsetX!==0||this.shadow.offsetY!==0)},drawClipPathOnCache:function(r){var l=this.clipPath;if(r.save(),l.inverted?r.globalCompositeOperation="destination-out":r.globalCompositeOperation="destination-in",l.absolutePositioned){var u=s.util.invertTransform(this.calcTransformMatrix());r.transform(u[0],u[1],u[2],u[3],u[4],u[5])}l.transform(r),r.scale(1/l.zoomX,1/l.zoomY),r.drawImage(l._cacheCanvas,-l.cacheTranslationX,-l.cacheTranslationY),r.restore()},drawObject:function(r,l){var u=this.fill,d=this.stroke;l?(this.fill="black",this.stroke="",this._setClippingProperties(r)):this._renderBackground(r),this._render(r),this._drawClipPath(r),this.fill=u,this.stroke=d},_drawClipPath:function(r){var l=this.clipPath;l&&(l.canvas=this.canvas,l.shouldCache(),l._transformDone=!0,l.renderCache({forClipping:!0}),this.drawClipPathOnCache(r))},drawCacheOnCanvas:function(r){r.scale(1/this.zoomX,1/this.zoomY),r.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)},isCacheDirty:function(r){if(this.isNotVisible())return!1;if(this._cacheCanvas&&!r&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned||this.statefullCache&&this.hasStateChanged("cacheProperties")){if(this._cacheCanvas&&!r){var l=this.cacheWidth/this.zoomX,u=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-l/2,-u/2,l,u)}return!0}return!1},_renderBackground:function(r){if(this.backgroundColor){var l=this._getNonTransformedDimensions();r.fillStyle=this.backgroundColor,r.fillRect(-l.x/2,-l.y/2,l.x,l.y),this._removeShadow(r)}},_setOpacity:function(r){this.group&&!this.group._transformDone?r.globalAlpha=this.getObjectOpacity():r.globalAlpha*=this.opacity},_setStrokeStyles:function(r,l){var u=l.stroke;u&&(r.lineWidth=l.strokeWidth,r.lineCap=l.strokeLineCap,r.lineDashOffset=l.strokeDashOffset,r.lineJoin=l.strokeLineJoin,r.miterLimit=l.strokeMiterLimit,u.toLive?u.gradientUnits==="percentage"||u.gradientTransform||u.patternTransform?this._applyPatternForTransformedGradient(r,u):(r.strokeStyle=u.toLive(r,this),this._applyPatternGradientTransform(r,u)):r.strokeStyle=l.stroke)},_setFillStyles:function(r,l){var u=l.fill;u&&(u.toLive?(r.fillStyle=u.toLive(r,this),this._applyPatternGradientTransform(r,l.fill)):r.fillStyle=u)},_setClippingProperties:function(r){r.globalAlpha=1,r.strokeStyle="transparent",r.fillStyle="#000000"},_setLineDash:function(r,l){!l||l.length===0||(1&l.length&&l.push.apply(l,l),r.setLineDash(l))},_renderControls:function(r,l){var u=this.getViewportTransform(),d=this.calcTransformMatrix(),g,m,v;l=l||{},m=typeof l.hasBorders!="undefined"?l.hasBorders:this.hasBorders,v=typeof l.hasControls!="undefined"?l.hasControls:this.hasControls,d=s.util.multiplyTransformMatrices(u,d),g=s.util.qrDecompose(d),r.save(),r.translate(g.translateX,g.translateY),r.lineWidth=1*this.borderScaleFactor,this.group||(r.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),r.rotate(t(g.angle)),l.forActiveSelection||this.group?m&&this.drawBordersInGroup(r,g,l):m&&this.drawBorders(r,l),v&&this.drawControls(r,l),r.restore()},_setShadow:function(r){if(this.shadow){var l=this.shadow,u=this.canvas,d,g=u&&u.viewportTransform[0]||1,m=u&&u.viewportTransform[3]||1;l.nonScaling?d={scaleX:1,scaleY:1}:d=this.getObjectScaling(),u&&u._isRetinaScaling()&&(g*=s.devicePixelRatio,m*=s.devicePixelRatio),r.shadowColor=l.color,r.shadowBlur=l.blur*s.browserShadowBlurConstant*(g+m)*(d.scaleX+d.scaleY)/4,r.shadowOffsetX=l.offsetX*g*d.scaleX,r.shadowOffsetY=l.offsetY*m*d.scaleY}},_removeShadow:function(r){this.shadow&&(r.shadowColor="",r.shadowBlur=r.shadowOffsetX=r.shadowOffsetY=0)},_applyPatternGradientTransform:function(r,l){if(!l||!l.toLive)return{offsetX:0,offsetY:0};var u=l.gradientTransform||l.patternTransform,d=-this.width/2+l.offsetX||0,g=-this.height/2+l.offsetY||0;return l.gradientUnits==="percentage"?r.transform(this.width,0,0,this.height,d,g):r.transform(1,0,0,1,d,g),u&&r.transform(u[0],u[1],u[2],u[3],u[4],u[5]),{offsetX:d,offsetY:g}},_renderPaintInOrder:function(r){this.paintFirst==="stroke"?(this._renderStroke(r),this._renderFill(r)):(this._renderFill(r),this._renderStroke(r))},_render:function(){},_renderFill:function(r){this.fill&&(r.save(),this._setFillStyles(r,this),this.fillRule==="evenodd"?r.fill("evenodd"):r.fill(),r.restore())},_renderStroke:function(r){if(!(!this.stroke||this.strokeWidth===0)){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(r),r.save(),this.strokeUniform&&this.group){var l=this.getObjectScaling();r.scale(1/l.scaleX,1/l.scaleY)}else this.strokeUniform&&r.scale(1/this.scaleX,1/this.scaleY);this._setLineDash(r,this.strokeDashArray),this._setStrokeStyles(r,this),r.stroke(),r.restore()}},_applyPatternForTransformedGradient:function(r,l){var u=this._limitCacheSize(this._getCacheCanvasDimensions()),d=s.util.createCanvasElement(),g,m=this.canvas.getRetinaScaling(),v=u.x/this.scaleX/m,y=u.y/this.scaleY/m;d.width=v,d.height=y,g=d.getContext("2d"),g.beginPath(),g.moveTo(0,0),g.lineTo(v,0),g.lineTo(v,y),g.lineTo(0,y),g.closePath(),g.translate(v/2,y/2),g.scale(u.zoomX/this.scaleX/m,u.zoomY/this.scaleY/m),this._applyPatternGradientTransform(g,l),g.fillStyle=l.toLive(r),g.fill(),r.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),r.scale(m*this.scaleX/u.zoomX,m*this.scaleY/u.zoomY),r.strokeStyle=g.createPattern(d,"no-repeat")},_findCenterFromElement:function(){return{x:this.left+this.width/2,y:this.top+this.height/2}},_assignTransformMatrixProps:function(){if(this.transformMatrix){var r=s.util.qrDecompose(this.transformMatrix);this.flipX=!1,this.flipY=!1,this.set("scaleX",r.scaleX),this.set("scaleY",r.scaleY),this.angle=r.angle,this.skewX=r.skewX,this.skewY=0}},_removeTransformMatrix:function(r){var l=this._findCenterFromElement();this.transformMatrix&&(this._assignTransformMatrixProps(),l=s.util.transformPoint(l,this.transformMatrix)),this.transformMatrix=null,r&&(this.scaleX*=r.scaleX,this.scaleY*=r.scaleY,this.cropX=r.cropX,this.cropY=r.cropY,l.x+=r.offsetLeft,l.y+=r.offsetTop,this.width=r.width,this.height=r.height),this.setPositionByOrigin(l,"center","center")},clone:function(r,l){var u=this.toObject(l);this.constructor.fromObject?this.constructor.fromObject(u,r):s.Object._fromObject("Object",u,r)},cloneAsImage:function(r,l){var u=this.toCanvasElement(l);return r&&r(new s.Image(u)),this},toCanvasElement:function(r){r||(r={});var l=s.util,u=l.saveObjectTransform(this),d=this.group,g=this.shadow,m=Math.abs,v=(r.multiplier||1)*(r.enableRetinaScaling?s.devicePixelRatio:1);delete this.group,r.withoutTransform&&l.resetObjectTransform(this),r.withoutShadow&&(this.shadow=null);var y=s.util.createCanvasElement(),T=this.getBoundingRect(!0,!0),M=this.shadow,W,H={x:0,y:0},G,V,U;M&&(G=M.blur,M.nonScaling?W={scaleX:1,scaleY:1}:W=this.getObjectScaling(),H.x=2*Math.round(m(M.offsetX)+G)*m(W.scaleX),H.y=2*Math.round(m(M.offsetY)+G)*m(W.scaleY)),V=T.width+H.x,U=T.height+H.y,y.width=Math.ceil(V),y.height=Math.ceil(U);var N=new s.StaticCanvas(y,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1});r.format==="jpeg"&&(N.backgroundColor="#fff"),this.setPositionByOrigin(new s.Point(N.width/2,N.height/2),"center","center");var Q=this.canvas;N.add(this);var $=N.toCanvasElement(v||1,r);return this.shadow=g,this.set("canvas",Q),d&&(this.group=d),this.set(u).setCoords(),N._objects=[],N.dispose(),N=null,$},toDataURL:function(r){return r||(r={}),s.util.toDataURL(this.toCanvasElement(r),r.format||"png",r.quality||1)},isType:function(r){return this.type===r},complexity:function(){return 1},toJSON:function(r){return this.toObject(r)},rotate:function(r){var l=(this.originX!=="center"||this.originY!=="center")&&this.centeredRotation;return l&&this._setOriginToCenter(),this.set("angle",r),l&&this._resetOrigin(),this},centerH:function(){return this.canvas&&this.canvas.centerObjectH(this),this},viewportCenterH:function(){return this.canvas&&this.canvas.viewportCenterObjectH(this),this},centerV:function(){return this.canvas&&this.canvas.centerObjectV(this),this},viewportCenterV:function(){return this.canvas&&this.canvas.viewportCenterObjectV(this),this},center:function(){return this.canvas&&this.canvas.centerObject(this),this},viewportCenter:function(){return this.canvas&&this.canvas.viewportCenterObject(this),this},getLocalPointer:function(r,l){l=l||this.canvas.getPointer(r);var u=new s.Point(l.x,l.y),d=this._getLeftTopCoords();return this.angle&&(u=s.util.rotatePoint(u,d,t(-this.angle))),{x:u.x-d.x,y:u.y-d.y}},_setupCompositeOperation:function(r){this.globalCompositeOperation&&(r.globalCompositeOperation=this.globalCompositeOperation)}}),s.util.createAccessors&&s.util.createAccessors(s.Object),o(s.Object.prototype,s.Observable),s.Object.NUM_FRACTION_DIGITS=2,s.Object._fromObject=function(r,l,u,d){var g=s[r];l=a(l,!0),s.util.enlivenPatterns([l.fill,l.stroke],function(m){typeof m[0]!="undefined"&&(l.fill=m[0]),typeof m[1]!="undefined"&&(l.stroke=m[1]),s.util.enlivenObjects([l.clipPath],function(v){l.clipPath=v[0];var y=d?new g(l[d],l):new g(l);u&&u(y)})})},s.Object.__uid=0)}(et),function(){var c=f.util.degreesToRadians,s={left:-.5,center:0,right:.5},o={top:-.5,center:0,bottom:.5};f.util.object.extend(f.Object.prototype,{translateToGivenOrigin:function(a,e,i,t,n){var h=a.x,r=a.y,l,u,d;return typeof e=="string"?e=s[e]:e-=.5,typeof t=="string"?t=s[t]:t-=.5,l=t-e,typeof i=="string"?i=o[i]:i-=.5,typeof n=="string"?n=o[n]:n-=.5,u=n-i,(l||u)&&(d=this._getTransformedDimensions(),h=a.x+l*d.x,r=a.y+u*d.y),new f.Point(h,r)},translateToCenterPoint:function(a,e,i){var t=this.translateToGivenOrigin(a,e,i,"center","center");return this.angle?f.util.rotatePoint(t,a,c(this.angle)):t},translateToOriginPoint:function(a,e,i){var t=this.translateToGivenOrigin(a,"center","center",e,i);return this.angle?f.util.rotatePoint(t,a,c(this.angle)):t},getCenterPoint:function(){var a=new f.Point(this.left,this.top);return this.translateToCenterPoint(a,this.originX,this.originY)},getPointByOrigin:function(a,e){var i=this.getCenterPoint();return this.translateToOriginPoint(i,a,e)},toLocalPoint:function(a,e,i){var t=this.getCenterPoint(),n,h;return typeof e!="undefined"&&typeof i!="undefined"?n=this.translateToGivenOrigin(t,"center","center",e,i):n=new f.Point(this.left,this.top),h=new f.Point(a.x,a.y),this.angle&&(h=f.util.rotatePoint(h,t,-c(this.angle))),h.subtractEquals(n)},setPositionByOrigin:function(a,e,i){var t=this.translateToCenterPoint(a,e,i),n=this.translateToOriginPoint(t,this.originX,this.originY);this.set("left",n.x),this.set("top",n.y)},adjustPosition:function(a){var e=c(this.angle),i=this.getScaledWidth(),t=f.util.cos(e)*i,n=f.util.sin(e)*i,h,r;typeof this.originX=="string"?h=s[this.originX]:h=this.originX-.5,typeof a=="string"?r=s[a]:r=a-.5,this.left+=t*(r-h),this.top+=n*(r-h),this.setCoords(),this.originX=a},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var a=this.getCenterPoint();this.originX="center",this.originY="center",this.left=a.x,this.top=a.y},_resetOrigin:function(){var a=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=a.x,this.top=a.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","top")}})}(),function(){function c(i){return[new f.Point(i.tl.x,i.tl.y),new f.Point(i.tr.x,i.tr.y),new f.Point(i.br.x,i.br.y),new f.Point(i.bl.x,i.bl.y)]}var s=f.util,o=s.degreesToRadians,a=s.multiplyTransformMatrices,e=s.transformPoint;s.object.extend(f.Object.prototype,{oCoords:null,aCoords:null,lineCoords:null,ownMatrixCache:null,matrixCache:null,controls:{},_getCoords:function(i,t){return t?i?this.calcACoords():this.calcLineCoords():((!this.aCoords||!this.lineCoords)&&this.setCoords(!0),i?this.aCoords:this.lineCoords)},getCoords:function(i,t){return c(this._getCoords(i,t))},intersectsWithRect:function(i,t,n,h){var r=this.getCoords(n,h),l=f.Intersection.intersectPolygonRectangle(r,i,t);return l.status==="Intersection"},intersectsWithObject:function(i,t,n){var h=f.Intersection.intersectPolygonPolygon(this.getCoords(t,n),i.getCoords(t,n));return h.status==="Intersection"||i.isContainedWithinObject(this,t,n)||this.isContainedWithinObject(i,t,n)},isContainedWithinObject:function(i,t,n){for(var h=this.getCoords(t,n),r=t?i.aCoords:i.lineCoords,l=0,u=i._getImageLines(r);l<4;l++)if(!i.containsPoint(h[l],u))return!1;return!0},isContainedWithinRect:function(i,t,n,h){var r=this.getBoundingRect(n,h);return r.left>=i.x&&r.left+r.width<=t.x&&r.top>=i.y&&r.top+r.height<=t.y},containsPoint:function(i,l,n,h){var r=this._getCoords(n,h),l=l||this._getImageLines(r),u=this._findCrossPoints(i,l);return u!==0&&u%2===1},isOnScreen:function(i){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,n=this.canvas.vptCoords.br,h=this.getCoords(!0,i);return h.some(function(r){return r.x<=n.x&&r.x>=t.x&&r.y<=n.y&&r.y>=t.y})||this.intersectsWithRect(t,n,!0,i)?!0:this._containsCenterOfCanvas(t,n,i)},_containsCenterOfCanvas:function(i,t,n){var h={x:(i.x+t.x)/2,y:(i.y+t.y)/2};return!!this.containsPoint(h,null,!0,n)},isPartiallyOnScreen:function(i){if(!this.canvas)return!1;var t=this.canvas.vptCoords.tl,n=this.canvas.vptCoords.br;if(this.intersectsWithRect(t,n,!0,i))return!0;var h=this.getCoords(!0,i).every(function(r){return(r.x>=n.x||r.x<=t.x)&&(r.y>=n.y||r.y<=t.y)});return h&&this._containsCenterOfCanvas(t,n,i)},_getImageLines:function(i){var t={topline:{o:i.tl,d:i.tr},rightline:{o:i.tr,d:i.br},bottomline:{o:i.br,d:i.bl},leftline:{o:i.bl,d:i.tl}};return t},_findCrossPoints:function(i,t){var n,h,r,l,u,d=0,g;for(var m in t)if(g=t[m],!(g.o.y<i.y&&g.d.y<i.y)&&!(g.o.y>=i.y&&g.d.y>=i.y)&&(g.o.x===g.d.x&&g.o.x>=i.x?u=g.o.x:(n=0,h=(g.d.y-g.o.y)/(g.d.x-g.o.x),r=i.y-n*i.x,l=g.o.y-h*g.o.x,u=-(r-l)/(n-h)),u>=i.x&&(d+=1),d===2))break;return d},getBoundingRect:function(i,t){var n=this.getCoords(i,t);return s.makeBoundingBoxFromPoints(n)},getScaledWidth:function(){return this._getTransformedDimensions().x},getScaledHeight:function(){return this._getTransformedDimensions().y},_constrainScale:function(i){return Math.abs(i)<this.minScaleLimit?i<0?-this.minScaleLimit:this.minScaleLimit:i===0?1e-4:i},scale:function(i){return this._set("scaleX",i),this._set("scaleY",i),this.setCoords()},scaleToWidth:function(i,t){var n=this.getBoundingRect(t).width/this.getScaledWidth();return this.scale(i/this.width/n)},scaleToHeight:function(i,t){var n=this.getBoundingRect(t).height/this.getScaledHeight();return this.scale(i/this.height/n)},calcCoords:function(i){return i?this.calcACoords():this.calcOCoords()},calcLineCoords:function(){var i=this.getViewportTransform(),t=this.padding,n=o(this.angle),h=s.cos(n),r=s.sin(n),l=h*t,u=r*t,d=l+u,g=l-u,m=this.calcACoords(),v={tl:e(m.tl,i),tr:e(m.tr,i),bl:e(m.bl,i),br:e(m.br,i)};return t&&(v.tl.x-=g,v.tl.y-=d,v.tr.x+=d,v.tr.y-=g,v.bl.x-=d,v.bl.y+=g,v.br.x+=g,v.br.y+=d),v},calcOCoords:function(){var i=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),n=this.getViewportTransform(),h=a(n,t),r=a(h,i),r=a(r,[1/n[0],0,0,1/n[3],0,0]),l=this._calculateCurrentDimensions(),u={};return this.forEachControl(function(d,g,m){u[g]=d.positionHandler(l,r,m)}),u},calcACoords:function(){var i=this._calcRotateMatrix(),t=this._calcTranslateMatrix(),n=a(t,i),h=this._getTransformedDimensions(),r=h.x/2,l=h.y/2;return{tl:e({x:-r,y:-l},n),tr:e({x:r,y:-l},n),bl:e({x:-r,y:l},n),br:e({x:r,y:l},n)}},setCoords:function(i){return this.aCoords=this.calcACoords(),this.lineCoords=this.group?this.aCoords:this.calcLineCoords(),i?this:(this.oCoords=this.calcOCoords(),this._setCornerCoords&&this._setCornerCoords(),this)},_calcRotateMatrix:function(){return s.calcRotateMatrix(this)},_calcTranslateMatrix:function(){var i=this.getCenterPoint();return[1,0,0,1,i.x,i.y]},transformMatrixKey:function(i){var t="_",n="";return!i&&this.group&&(n=this.group.transformMatrixKey(i)+t),n+this.top+t+this.left+t+this.scaleX+t+this.scaleY+t+this.skewX+t+this.skewY+t+this.angle+t+this.originX+t+this.originY+t+this.width+t+this.height+t+this.strokeWidth+this.flipX+this.flipY},calcTransformMatrix:function(i){var t=this.calcOwnMatrix();if(i||!this.group)return t;var n=this.transformMatrixKey(i),h=this.matrixCache||(this.matrixCache={});return h.key===n?h.value:(this.group&&(t=a(this.group.calcTransformMatrix(!1),t)),h.key=n,h.value=t,t)},calcOwnMatrix:function(){var i=this.transformMatrixKey(!0),t=this.ownMatrixCache||(this.ownMatrixCache={});if(t.key===i)return t.value;var n=this._calcTranslateMatrix(),h={angle:this.angle,translateX:n[4],translateY:n[5],scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY};return t.key=i,t.value=s.composeMatrix(h),t.value},_calcDimensionsTransformMatrix:function(i,t,n){return s.calcDimensionsMatrix({skewX:i,skewY:t,scaleX:this.scaleX*(n&&this.flipX?-1:1),scaleY:this.scaleY*(n&&this.flipY?-1:1)})},_getNonTransformedDimensions:function(){var i=this.strokeWidth,t=this.width+i,n=this.height+i;return{x:t,y:n}},_getTransformedDimensions:function(i,t){typeof i=="undefined"&&(i=this.skewX),typeof t=="undefined"&&(t=this.skewY);var n,h,r,l=i===0&&t===0;if(this.strokeUniform?(h=this.width,r=this.height):(n=this._getNonTransformedDimensions(),h=n.x,r=n.y),l)return this._finalizeDimensions(h*this.scaleX,r*this.scaleY);var u=s.sizeAfterTransform(h,r,{scaleX:this.scaleX,scaleY:this.scaleY,skewX:i,skewY:t});return this._finalizeDimensions(u.x,u.y)},_finalizeDimensions:function(i,t){return this.strokeUniform?{x:i+this.strokeWidth,y:t+this.strokeWidth}:{x:i,y:t}},_calculateCurrentDimensions:function(){var i=this.getViewportTransform(),t=this._getTransformedDimensions(),n=e(t,i,!0);return n.scalarAdd(2*this.padding)}})}(),f.util.object.extend(f.Object.prototype,{sendToBack:function(){return this.group?f.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas&&this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?f.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas&&this.canvas.bringToFront(this),this},sendBackwards:function(c){return this.group?f.StaticCanvas.prototype.sendBackwards.call(this.group,this,c):this.canvas&&this.canvas.sendBackwards(this,c),this},bringForward:function(c){return this.group?f.StaticCanvas.prototype.bringForward.call(this.group,this,c):this.canvas&&this.canvas.bringForward(this,c),this},moveTo:function(c){return this.group&&this.group.type!=="activeSelection"?f.StaticCanvas.prototype.moveTo.call(this.group,this,c):this.canvas&&this.canvas.moveTo(this,c),this}}),function(){function c(o,a){if(a){if(a.toLive)return o+": url(#SVGID_"+a.id+"); ";var e=new f.Color(a),i=o+": "+e.toRgb()+"; ",t=e.getAlpha();return t!==1&&(i+=o+"-opacity: "+t.toString()+"; "),i}else return o+": none; "}var s=f.util.toFixed;f.util.object.extend(f.Object.prototype,{getSvgStyles:function(o){var a=this.fillRule?this.fillRule:"nonzero",e=this.strokeWidth?this.strokeWidth:"0",i=this.strokeDashArray?this.strokeDashArray.join(" "):"none",t=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",h=this.strokeLineJoin?this.strokeLineJoin:"miter",r=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=typeof this.opacity!="undefined"?this.opacity:"1",u=this.visible?"":" visibility: hidden;",d=o?"":this.getSvgFilter(),g=c("fill",this.fill),m=c("stroke",this.stroke);return[m,"stroke-width: ",e,"; ","stroke-dasharray: ",i,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",t,"; ","stroke-linejoin: ",h,"; ","stroke-miterlimit: ",r,"; ",g,"fill-rule: ",a,"; ","opacity: ",l,";",d,u].join("")},getSvgSpanStyles:function(o,a){var e="; ",t=o.fontFamily?"font-family: "+(o.fontFamily.indexOf("'")===-1&&o.fontFamily.indexOf('"')===-1?"'"+o.fontFamily+"'":o.fontFamily)+e:"",i=o.strokeWidth?"stroke-width: "+o.strokeWidth+e:"",t=t,n=o.fontSize?"font-size: "+o.fontSize+"px"+e:"",h=o.fontStyle?"font-style: "+o.fontStyle+e:"",r=o.fontWeight?"font-weight: "+o.fontWeight+e:"",l=o.fill?c("fill",o.fill):"",u=o.stroke?c("stroke",o.stroke):"",d=this.getSvgTextDecoration(o),g=o.deltaY?"baseline-shift: "+-o.deltaY+"; ":"";return d&&(d="text-decoration: "+d+e),[u,i,t,n,h,r,d,l,g,a?"white-space: pre; ":""].join("")},getSvgTextDecoration:function(o){return["overline","underline","line-through"].filter(function(a){return o[a.replace("-","")]}).join(" ")},getSvgFilter:function(){return this.shadow?"filter: url(#SVGID_"+this.shadow.id+");":""},getSvgCommons:function(){return[this.id?'id="'+this.id+'" ':"",this.clipPath?'clip-path="url(#'+this.clipPath.clipPathId+')" ':""].join("")},getSvgTransform:function(o,a){var e=o?this.calcTransformMatrix():this.calcOwnMatrix(),i='transform="'+f.util.matrixToSVG(e);return i+(a||"")+'" '},_setSVGBg:function(o){if(this.backgroundColor){var a=f.Object.NUM_FRACTION_DIGITS;o.push("		<rect ",this._getFillAttributes(this.backgroundColor),' x="',s(-this.width/2,a),'" y="',s(-this.height/2,a),'" width="',s(this.width,a),'" height="',s(this.height,a),`"></rect>
`)}},toSVG:function(o){return this._createBaseSVGMarkup(this._toSVG(o),{reviver:o})},toClipPathSVG:function(o){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(o),{reviver:o})},_createBaseClipPathSVGMarkup:function(o,a){a=a||{};var e=a.reviver,i=a.additionalTransform||"",t=[this.getSvgTransform(!0,i),this.getSvgCommons()].join(""),n=o.indexOf("COMMON_PARTS");return o[n]=t,e?e(o.join("")):o.join("")},_createBaseSVGMarkup:function(o,a){a=a||{};var e=a.noStyle,i=a.reviver,t=e?"":'style="'+this.getSvgStyles()+'" ',n=a.withShadow?'style="'+this.getSvgFilter()+'" ':"",h=this.clipPath,r=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",l=h&&h.absolutePositioned,u=this.stroke,d=this.fill,g=this.shadow,m,v=[],y,T=o.indexOf("COMMON_PARTS"),M=a.additionalTransform;return h&&(h.clipPathId="CLIPPATH_"+f.Object.__uid++,y='<clipPath id="'+h.clipPathId+`" >
`+h.toClipPathSVG(i)+`</clipPath>
`),l&&v.push("<g ",n,this.getSvgCommons(),` >
`),v.push("<g ",this.getSvgTransform(!1),l?"":n+this.getSvgCommons(),` >
`),m=[t,r,e?"":this.addPaintOrder()," ",M?'transform="'+M+'" ':""].join(""),o[T]=m,d&&d.toLive&&v.push(d.toSVG(this)),u&&u.toLive&&v.push(u.toSVG(this)),g&&v.push(g.toSVG(this)),h&&v.push(y),v.push(o.join("")),v.push(`</g>
`),l&&v.push(`</g>
`),i?i(v.join("")):v.join("")},addPaintOrder:function(){return this.paintFirst!=="fill"?' paint-order="'+this.paintFirst+'" ':""}})}(),function(){var c=f.util.object.extend,s="stateProperties";function o(e,i,t){var n={},h=!0;t.forEach(function(r){n[r]=e[r]}),c(e[i],n,h)}function a(e,i,t){if(e===i)return!0;if(Array.isArray(e)){if(!Array.isArray(i)||e.length!==i.length)return!1;for(var n=0,h=e.length;n<h;n++)if(!a(e[n],i[n]))return!1;return!0}else if(e&&typeof e=="object"){var r=Object.keys(e),l;if(!i||typeof i!="object"||!t&&r.length!==Object.keys(i).length)return!1;for(var n=0,h=r.length;n<h;n++)if(l=r[n],!(l==="canvas"||l==="group")&&!a(e[l],i[l]))return!1;return!0}}f.util.object.extend(f.Object.prototype,{hasStateChanged:function(e){e=e||s;var i="_"+e;return Object.keys(this[i]).length<this[e].length?!0:!a(this[i],this,!0)},saveState:function(e){var i=e&&e.propertySet||s,t="_"+i;return this[t]?(o(this,t,this[i]),e&&e.stateProperties&&o(this,t,e.stateProperties),this):this.setupState(e)},setupState:function(e){e=e||{};var i=e.propertySet||s;return e.propertySet=i,this["_"+i]={},this.saveState(e),this}})}(),function(){var c=f.util.degreesToRadians;f.util.object.extend(f.Object.prototype,{_findTargetCorner:function(s,o){if(!this.hasControls||this.group||!this.canvas||this.canvas._activeObject!==this)return!1;var a=s.x,e=s.y,i,t,n=Object.keys(this.oCoords),h=n.length-1,r;for(this.__corner=0;h>=0;h--)if(r=n[h],!!this.isControlVisible(r)&&(t=this._getImageLines(o?this.oCoords[r].touchCorner:this.oCoords[r].corner),i=this._findCrossPoints({x:a,y:e},t),i!==0&&i%2===1))return this.__corner=r,r;return!1},forEachControl:function(s){for(var o in this.controls)s(this.controls[o],o,this)},_setCornerCoords:function(){var s=this.oCoords;for(var o in s){var a=this.controls[o];s[o].corner=a.calcCornerCoords(this.angle,this.cornerSize,s[o].x,s[o].y,!1),s[o].touchCorner=a.calcCornerCoords(this.angle,this.touchCornerSize,s[o].x,s[o].y,!0)}},drawSelectionBackground:function(s){if(!this.selectionBackgroundColor||this.canvas&&!this.canvas.interactive||this.canvas&&this.canvas._activeObject!==this)return this;s.save();var o=this.getCenterPoint(),a=this._calculateCurrentDimensions(),e=this.canvas.viewportTransform;return s.translate(o.x,o.y),s.scale(1/e[0],1/e[3]),s.rotate(c(this.angle)),s.fillStyle=this.selectionBackgroundColor,s.fillRect(-a.x/2,-a.y/2,a.x,a.y),s.restore(),this},drawBorders:function(s,o){o=o||{};var a=this._calculateCurrentDimensions(),e=this.borderScaleFactor,i=a.x+e,t=a.y+e,n=typeof o.hasControls!="undefined"?o.hasControls:this.hasControls,h=!1;return s.save(),s.strokeStyle=o.borderColor||this.borderColor,this._setLineDash(s,o.borderDashArray||this.borderDashArray),s.strokeRect(-i/2,-t/2,i,t),n&&(s.beginPath(),this.forEachControl(function(r,l,u){r.withConnection&&r.getVisibility(u,l)&&(h=!0,s.moveTo(r.x*i,r.y*t),s.lineTo(r.x*i+r.offsetX,r.y*t+r.offsetY))}),h&&s.stroke()),s.restore(),this},drawBordersInGroup:function(s,o,a){a=a||{};var e=f.util.sizeAfterTransform(this.width,this.height,o),i=this.strokeWidth,t=this.strokeUniform,n=this.borderScaleFactor,h=e.x+i*(t?this.canvas.getZoom():o.scaleX)+n,r=e.y+i*(t?this.canvas.getZoom():o.scaleY)+n;return s.save(),this._setLineDash(s,a.borderDashArray||this.borderDashArray),s.strokeStyle=a.borderColor||this.borderColor,s.strokeRect(-h/2,-r/2,h,r),s.restore(),this},drawControls:function(s,o){o=o||{},s.save();var a=this.canvas.getRetinaScaling(),e,i;return s.setTransform(a,0,0,a,0,0),s.strokeStyle=s.fillStyle=o.cornerColor||this.cornerColor,this.transparentCorners||(s.strokeStyle=o.cornerStrokeColor||this.cornerStrokeColor),this._setLineDash(s,o.cornerDashArray||this.cornerDashArray),this.setCoords(),this.group&&(e=this.group.calcTransformMatrix()),this.forEachControl(function(t,n,h){i=h.oCoords[n],t.getVisibility(h,n)&&(e&&(i=f.util.transformPoint(i,e)),t.render(s,i.x,i.y,o,h))}),s.restore(),this},isControlVisible:function(s){return this.controls[s]&&this.controls[s].getVisibility(this,s)},setControlVisible:function(s,o){return this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[s]=o,this},setControlsVisibility:function(s){s||(s={});for(var o in s)this.setControlVisible(o,s[o]);return this},onDeselect:function(){},onSelect:function(){}})}(),f.util.object.extend(f.StaticCanvas.prototype,{FX_DURATION:500,fxCenterObjectH:function(c,s){s=s||{};var o=function(){},a=s.onComplete||o,e=s.onChange||o,i=this;return f.util.animate({startValue:c.left,endValue:this.getCenter().left,duration:this.FX_DURATION,onChange:function(t){c.set("left",t),i.requestRenderAll(),e()},onComplete:function(){c.setCoords(),a()}}),this},fxCenterObjectV:function(c,s){s=s||{};var o=function(){},a=s.onComplete||o,e=s.onChange||o,i=this;return f.util.animate({startValue:c.top,endValue:this.getCenter().top,duration:this.FX_DURATION,onChange:function(t){c.set("top",t),i.requestRenderAll(),e()},onComplete:function(){c.setCoords(),a()}}),this},fxRemove:function(c,s){s=s||{};var o=function(){},a=s.onComplete||o,e=s.onChange||o,i=this;return f.util.animate({startValue:c.opacity,endValue:0,duration:this.FX_DURATION,onChange:function(t){c.set("opacity",t),i.requestRenderAll(),e()},onComplete:function(){i.remove(c),a()}}),this}}),f.util.object.extend(f.Object.prototype,{animate:function(){if(arguments[0]&&typeof arguments[0]=="object"){var c=[],s,o;for(s in arguments[0])c.push(s);for(var a=0,e=c.length;a<e;a++)s=c[a],o=a!==e-1,this._animate(s,arguments[0][s],arguments[1],o)}else this._animate.apply(this,arguments);return this},_animate:function(c,s,o,a){var e=this,i;s=s.toString(),o?o=f.util.object.clone(o):o={},~c.indexOf(".")&&(i=c.split("."));var t=e.colorProperties.indexOf(c)>-1||i&&e.colorProperties.indexOf(i[1])>-1,n=i?this.get(i[0])[i[1]]:this.get(c);"from"in o||(o.from=n),t||(~s.indexOf("=")?s=n+parseFloat(s.replace("=","")):s=parseFloat(s));var h={startValue:o.from,endValue:s,byValue:o.by,easing:o.easing,duration:o.duration,abort:o.abort&&function(r,l,u){return o.abort.call(e,r,l,u)},onChange:function(r,l,u){i?e[i[0]][i[1]]=r:e.set(c,r),!a&&o.onChange&&o.onChange(r,l,u)},onComplete:function(r,l,u){a||(e.setCoords(),o.onComplete&&o.onComplete(r,l,u))}};return t?f.util.animateColor(h.startValue,h.endValue,h.duration,h):f.util.animate(h)}}),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.util.object.clone,e={x1:1,x2:1,y1:1,y2:1};if(s.Line){s.warn("fabric.Line is already defined");return}s.Line=s.util.createClass(s.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,cacheProperties:s.Object.prototype.cacheProperties.concat("x1","x2","y1","y2"),initialize:function(t,n){t||(t=[0,0,0,0]),this.callSuper("initialize",n),this.set("x1",t[0]),this.set("y1",t[1]),this.set("x2",t[2]),this.set("y2",t[3]),this._setWidthHeight(n)},_setWidthHeight:function(t){t||(t={}),this.width=Math.abs(this.x2-this.x1),this.height=Math.abs(this.y2-this.y1),this.left="left"in t?t.left:this._getLeftToOriginX(),this.top="top"in t?t.top:this._getTopToOriginY()},_set:function(t,n){return this.callSuper("_set",t,n),typeof e[t]!="undefined"&&this._setWidthHeight(),this},_getLeftToOriginX:i({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:i({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(t){t.beginPath();var n=this.calcLinePoints();t.moveTo(n.x1,n.y1),t.lineTo(n.x2,n.y2),t.lineWidth=this.strokeWidth;var h=t.strokeStyle;t.strokeStyle=this.stroke||t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=h},_findCenterFromElement:function(){return{x:(this.x1+this.x2)/2,y:(this.y1+this.y2)/2}},toObject:function(t){return o(this.callSuper("toObject",t),this.calcLinePoints())},_getNonTransformedDimensions:function(){var t=this.callSuper("_getNonTransformedDimensions");return this.strokeLineCap==="butt"&&(this.width===0&&(t.y-=this.strokeWidth),this.height===0&&(t.x-=this.strokeWidth)),t},calcLinePoints:function(){var t=this.x1<=this.x2?-1:1,n=this.y1<=this.y2?-1:1,h=t*this.width*.5,r=n*this.height*.5,l=t*this.width*-.5,u=n*this.height*-.5;return{x1:h,x2:l,y1:r,y2:u}},_toSVG:function(){var t=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="',t.x1,'" y1="',t.y1,'" x2="',t.x2,'" y2="',t.y2,`" />
`]}}),s.Line.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x1 y1 x2 y2".split(" ")),s.Line.fromElement=function(t,n,h){h=h||{};var r=s.parseAttributes(t,s.Line.ATTRIBUTE_NAMES),l=[r.x1||0,r.y1||0,r.x2||0,r.y2||0];n(new s.Line(l,o(r,h)))},s.Line.fromObject=function(t,n){function h(l){delete l.points,n&&n(l)}var r=a(t,!0);r.points=[t.x1,t.y1,t.x2,t.y2],s.Object._fromObject("Line",r,h,"points")};function i(t,n){var h=t.origin,r=t.axis1,l=t.axis2,u=t.dimension,d=n.nearest,g=n.center,m=n.farthest;return function(){switch(this.get(h)){case d:return Math.min(this.get(r),this.get(l));case g:return Math.min(this.get(r),this.get(l))+.5*this.get(u);case m:return Math.max(this.get(r),this.get(l))}}}}(et),function(c){var s=c.fabric||(c.fabric={}),o=Math.PI;if(s.Circle){s.warn("fabric.Circle is already defined.");return}s.Circle=s.util.createClass(s.Object,{type:"circle",radius:0,startAngle:0,endAngle:o*2,cacheProperties:s.Object.prototype.cacheProperties.concat("radius","startAngle","endAngle"),_set:function(e,i){return this.callSuper("_set",e,i),e==="radius"&&this.setRadius(i),this},toObject:function(e){return this.callSuper("toObject",["radius","startAngle","endAngle"].concat(e))},_toSVG:function(){var e,i=0,t=0,n=(this.endAngle-this.startAngle)%(2*o);if(n===0)e=["<circle ","COMMON_PARTS",'cx="'+i+'" cy="'+t+'" ','r="',this.radius,`" />
`];else{var h=s.util.cos(this.startAngle)*this.radius,r=s.util.sin(this.startAngle)*this.radius,l=s.util.cos(this.endAngle)*this.radius,u=s.util.sin(this.endAngle)*this.radius,d=n>o?"1":"0";e=['<path d="M '+h+" "+r," A "+this.radius+" "+this.radius," 0 ",+d+" 1"," "+l+" "+u,'" ',"COMMON_PARTS",` />
`]}return e},_render:function(e){e.beginPath(),e.arc(0,0,this.radius,this.startAngle,this.endAngle,!1),this._renderPaintInOrder(e)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(e){return this.radius=e,this.set("width",e*2).set("height",e*2)}}),s.Circle.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("cx cy r".split(" ")),s.Circle.fromElement=function(e,i){var t=s.parseAttributes(e,s.Circle.ATTRIBUTE_NAMES);if(!a(t))throw new Error("value of `r` attribute is required and can not be negative");t.left=(t.left||0)-t.radius,t.top=(t.top||0)-t.radius,i(new s.Circle(t))};function a(e){return"radius"in e&&e.radius>=0}s.Circle.fromObject=function(e,i){s.Object._fromObject("Circle",e,i)}}(et),function(c){var s=c.fabric||(c.fabric={});if(s.Triangle){s.warn("fabric.Triangle is already defined");return}s.Triangle=s.util.createClass(s.Object,{type:"triangle",width:100,height:100,_render:function(o){var a=this.width/2,e=this.height/2;o.beginPath(),o.moveTo(-a,e),o.lineTo(0,-e),o.lineTo(a,e),o.closePath(),this._renderPaintInOrder(o)},_toSVG:function(){var o=this.width/2,a=this.height/2,e=[-o+" "+a,"0 "+-a,o+" "+a].join(",");return["<polygon ","COMMON_PARTS",'points="',e,'" />']}}),s.Triangle.fromObject=function(o,a){return s.Object._fromObject("Triangle",o,a)}}(et),function(c){var s=c.fabric||(c.fabric={}),o=Math.PI*2;if(s.Ellipse){s.warn("fabric.Ellipse is already defined.");return}s.Ellipse=s.util.createClass(s.Object,{type:"ellipse",rx:0,ry:0,cacheProperties:s.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(a){this.callSuper("initialize",a),this.set("rx",a&&a.rx||0),this.set("ry",a&&a.ry||0)},_set:function(a,e){switch(this.callSuper("_set",a,e),a){case"rx":this.rx=e,this.set("width",e*2);break;case"ry":this.ry=e,this.set("height",e*2);break}return this},getRx:function(){return this.get("rx")*this.get("scaleX")},getRy:function(){return this.get("ry")*this.get("scaleY")},toObject:function(a){return this.callSuper("toObject",["rx","ry"].concat(a))},_toSVG:function(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" ','rx="',this.rx,'" ry="',this.ry,`" />
`]},_render:function(a){a.beginPath(),a.save(),a.transform(1,0,0,this.ry/this.rx,0,0),a.arc(0,0,this.rx,0,o,!1),a.restore(),this._renderPaintInOrder(a)}}),s.Ellipse.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("cx cy rx ry".split(" ")),s.Ellipse.fromElement=function(a,e){var i=s.parseAttributes(a,s.Ellipse.ATTRIBUTE_NAMES);i.left=(i.left||0)-i.rx,i.top=(i.top||0)-i.ry,e(new s.Ellipse(i))},s.Ellipse.fromObject=function(a,e){s.Object._fromObject("Ellipse",a,e)}}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend;if(s.Rect){s.warn("fabric.Rect is already defined");return}s.Rect=s.util.createClass(s.Object,{stateProperties:s.Object.prototype.stateProperties.concat("rx","ry"),type:"rect",rx:0,ry:0,cacheProperties:s.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(a){this.callSuper("initialize",a),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(a){var e=this.rx?Math.min(this.rx,this.width/2):0,i=this.ry?Math.min(this.ry,this.height/2):0,t=this.width,n=this.height,h=-this.width/2,r=-this.height/2,l=e!==0||i!==0,u=1-.5522847498;a.beginPath(),a.moveTo(h+e,r),a.lineTo(h+t-e,r),l&&a.bezierCurveTo(h+t-u*e,r,h+t,r+u*i,h+t,r+i),a.lineTo(h+t,r+n-i),l&&a.bezierCurveTo(h+t,r+n-u*i,h+t-u*e,r+n,h+t-e,r+n),a.lineTo(h+e,r+n),l&&a.bezierCurveTo(h+u*e,r+n,h,r+n-u*i,h,r+n-i),a.lineTo(h,r+i),l&&a.bezierCurveTo(h,r+u*i,h+u*e,r,h+e,r),a.closePath(),this._renderPaintInOrder(a)},toObject:function(a){return this.callSuper("toObject",["rx","ry"].concat(a))},_toSVG:function(){var a=-this.width/2,e=-this.height/2;return["<rect ","COMMON_PARTS",'x="',a,'" y="',e,'" rx="',this.rx,'" ry="',this.ry,'" width="',this.width,'" height="',this.height,`" />
`]}}),s.Rect.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x y rx ry width height".split(" ")),s.Rect.fromElement=function(a,e,i){if(!a)return e(null);i=i||{};var t=s.parseAttributes(a,s.Rect.ATTRIBUTE_NAMES);t.left=t.left||0,t.top=t.top||0,t.height=t.height||0,t.width=t.width||0;var n=new s.Rect(o(i?s.util.object.clone(i):{},t));n.visible=n.visible&&n.width>0&&n.height>0,e(n)},s.Rect.fromObject=function(a,e){return s.Object._fromObject("Rect",a,e)}}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.util.array.min,e=s.util.array.max,i=s.util.toFixed;if(s.Polyline){s.warn("fabric.Polyline is already defined");return}s.Polyline=s.util.createClass(s.Object,{type:"polyline",points:null,cacheProperties:s.Object.prototype.cacheProperties.concat("points"),initialize:function(t,n){n=n||{},this.points=t||[],this.callSuper("initialize",n),this._setPositionDimensions(n)},_setPositionDimensions:function(t){var n=this._calcDimensions(t),h;this.width=n.width,this.height=n.height,t.fromSVG||(h=this.translateToGivenOrigin({x:n.left-this.strokeWidth/2,y:n.top-this.strokeWidth/2},"left","top",this.originX,this.originY)),typeof t.left=="undefined"&&(this.left=t.fromSVG?n.left:h.x),typeof t.top=="undefined"&&(this.top=t.fromSVG?n.top:h.y),this.pathOffset={x:n.left+this.width/2,y:n.top+this.height/2}},_calcDimensions:function(){var t=this.points,n=a(t,"x")||0,h=a(t,"y")||0,r=e(t,"x")||0,l=e(t,"y")||0,u=r-n,d=l-h;return{left:n,top:h,width:u,height:d}},toObject:function(t){return o(this.callSuper("toObject",t),{points:this.points.concat()})},_toSVG:function(){for(var t=[],n=this.pathOffset.x,h=this.pathOffset.y,r=s.Object.NUM_FRACTION_DIGITS,l=0,u=this.points.length;l<u;l++)t.push(i(this.points[l].x-n,r),",",i(this.points[l].y-h,r)," ");return["<"+this.type+" ","COMMON_PARTS",'points="',t.join(""),`" />
`]},commonRender:function(t){var n,h=this.points.length,r=this.pathOffset.x,l=this.pathOffset.y;if(!h||isNaN(this.points[h-1].y))return!1;t.beginPath(),t.moveTo(this.points[0].x-r,this.points[0].y-l);for(var u=0;u<h;u++)n=this.points[u],t.lineTo(n.x-r,n.y-l);return!0},_render:function(t){this.commonRender(t)&&this._renderPaintInOrder(t)},complexity:function(){return this.get("points").length}}),s.Polyline.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(),s.Polyline.fromElementGenerator=function(t){return function(n,h,r){if(!n)return h(null);r||(r={});var l=s.parsePointsAttribute(n.getAttribute("points")),u=s.parseAttributes(n,s[t].ATTRIBUTE_NAMES);u.fromSVG=!0,h(new s[t](l,o(u,r)))}},s.Polyline.fromElement=s.Polyline.fromElementGenerator("Polyline"),s.Polyline.fromObject=function(t,n){return s.Object._fromObject("Polyline",t,n,"points")}}(et),function(c){var s=c.fabric||(c.fabric={});if(s.Polygon){s.warn("fabric.Polygon is already defined");return}s.Polygon=s.util.createClass(s.Polyline,{type:"polygon",_render:function(o){this.commonRender(o)&&(o.closePath(),this._renderPaintInOrder(o))}}),s.Polygon.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(),s.Polygon.fromElement=s.Polyline.fromElementGenerator("Polygon"),s.Polygon.fromObject=function(o,a){s.Object._fromObject("Polygon",o,a,"points")}}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.array.min,a=s.util.array.max,e=s.util.object.extend,i=Object.prototype.toString,t=s.util.toFixed;if(s.Path){s.warn("fabric.Path is already defined");return}s.Path=s.util.createClass(s.Object,{type:"path",path:null,cacheProperties:s.Object.prototype.cacheProperties.concat("path","fillRule"),stateProperties:s.Object.prototype.stateProperties.concat("path"),initialize:function(n,h){h=h||{},this.callSuper("initialize",h),n||(n=[]);var r=i.call(n)==="[object Array]";this.path=s.util.makePathSimpler(r?n:s.util.parsePath(n)),this.path&&s.Polyline.prototype._setPositionDimensions.call(this,h)},_renderPathCommands:function(n){var h,r=0,l=0,u=0,d=0,g=0,m=0,v=-this.pathOffset.x,y=-this.pathOffset.y;n.beginPath();for(var T=0,M=this.path.length;T<M;++T)switch(h=this.path[T],h[0]){case"L":u=h[1],d=h[2],n.lineTo(u+v,d+y);break;case"M":u=h[1],d=h[2],r=u,l=d,n.moveTo(u+v,d+y);break;case"C":u=h[5],d=h[6],g=h[3],m=h[4],n.bezierCurveTo(h[1]+v,h[2]+y,g+v,m+y,u+v,d+y);break;case"Q":n.quadraticCurveTo(h[1]+v,h[2]+y,h[3]+v,h[4]+y),u=h[3],d=h[4],g=h[1],m=h[2];break;case"z":case"Z":u=r,d=l,n.closePath();break}},_render:function(n){this._renderPathCommands(n),this._renderPaintInOrder(n)},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(n){return e(this.callSuper("toObject",n),{path:this.path.map(function(h){return h.slice()})})},toDatalessObject:function(n){var h=this.toObject(["sourcePath"].concat(n));return h.sourcePath&&delete h.path,h},_toSVG:function(){var n=s.util.joinPath(this.path);return["<path ","COMMON_PARTS",'d="',n,'" stroke-linecap="round" ',`/>
`]},_getOffsetTransform:function(){var n=s.Object.NUM_FRACTION_DIGITS;return" translate("+t(-this.pathOffset.x,n)+", "+t(-this.pathOffset.y,n)+")"},toClipPathSVG:function(n){var h=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:n,additionalTransform:h})},toSVG:function(n){var h=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:n,additionalTransform:h})},complexity:function(){return this.path.length},_calcDimensions:function(){for(var n=[],h=[],r,l=0,u=0,d=0,g=0,m,v=0,y=this.path.length;v<y;++v){switch(r=this.path[v],r[0]){case"L":d=r[1],g=r[2],m=[];break;case"M":d=r[1],g=r[2],l=d,u=g,m=[];break;case"C":m=s.util.getBoundsOfCurve(d,g,r[1],r[2],r[3],r[4],r[5],r[6]),d=r[5],g=r[6];break;case"Q":m=s.util.getBoundsOfCurve(d,g,r[1],r[2],r[1],r[2],r[3],r[4]),d=r[3],g=r[4];break;case"z":case"Z":d=l,g=u;break}m.forEach(function(U){n.push(U.x),h.push(U.y)}),n.push(d),h.push(g)}var T=o(n)||0,M=o(h)||0,W=a(n)||0,H=a(h)||0,G=W-T,V=H-M;return{left:T,top:M,width:G,height:V}}}),s.Path.fromObject=function(n,h){if(typeof n.sourcePath=="string"){var r=n.sourcePath;s.loadSVGFromURL(r,function(l){var u=l[0];u.setOptions(n),h&&h(u)})}else s.Object._fromObject("Path",n,h,"path")},s.Path.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat(["d"]),s.Path.fromElement=function(n,h,r){var l=s.parseAttributes(n,s.Path.ATTRIBUTE_NAMES);l.fromSVG=!0,h(new s.Path(l.d,e(l,r)))}}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.array.min,a=s.util.array.max;s.Group||(s.Group=s.util.createClass(s.Object,s.Collection,{type:"group",strokeWidth:0,subTargetCheck:!1,cacheProperties:[],useSetOnGroup:!1,initialize:function(e,i,t){i=i||{},this._objects=[],t&&this.callSuper("initialize",i),this._objects=e||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;if(t)this._updateObjectsACoords();else{var h=i&&i.centerPoint;i.originX!==void 0&&(this.originX=i.originX),i.originY!==void 0&&(this.originY=i.originY),h||this._calcBounds(),this._updateObjectsCoords(h),delete i.centerPoint,this.callSuper("initialize",i)}this.setCoords()},_updateObjectsACoords:function(){for(var e=!0,i=this._objects.length;i--;)this._objects[i].setCoords(e)},_updateObjectsCoords:function(i){for(var i=i||this.getCenterPoint(),t=this._objects.length;t--;)this._updateObjectCoords(this._objects[t],i)},_updateObjectCoords:function(e,i){var t=e.left,n=e.top,h=!0;e.set({left:t-i.x,top:n-i.y}),e.group=this,e.setCoords(h)},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(e){var i=!!this.group;return this._restoreObjectsState(),s.util.resetObjectTransform(this),e&&(i&&s.util.removeTransformFromObject(e,this.group.calcTransformMatrix()),this._objects.push(e),e.group=this,e._set("canvas",this.canvas)),this._calcBounds(),this._updateObjectsCoords(),this.dirty=!0,i?this.group.addWithUpdate():this.setCoords(),this},removeWithUpdate:function(e){return this._restoreObjectsState(),s.util.resetObjectTransform(this),this.remove(e),this._calcBounds(),this._updateObjectsCoords(),this.setCoords(),this.dirty=!0,this},_onObjectAdded:function(e){this.dirty=!0,e.group=this,e._set("canvas",this.canvas)},_onObjectRemoved:function(e){this.dirty=!0,delete e.group},_set:function(e,i){var t=this._objects.length;if(this.useSetOnGroup)for(;t--;)this._objects[t].setOnGroup(e,i);if(e==="canvas")for(;t--;)this._objects[t]._set(e,i);s.Object.prototype._set.call(this,e,i)},toObject:function(e){var i=this.includeDefaultValues,t=this._objects.filter(function(h){return!h.excludeFromExport}).map(function(h){var r=h.includeDefaultValues;h.includeDefaultValues=i;var l=h.toObject(e);return h.includeDefaultValues=r,l}),n=s.Object.prototype.toObject.call(this,e);return n.objects=t,n},toDatalessObject:function(e){var i,t=this.sourcePath;if(t)i=t;else{var n=this.includeDefaultValues;i=this._objects.map(function(r){var l=r.includeDefaultValues;r.includeDefaultValues=n;var u=r.toDatalessObject(e);return r.includeDefaultValues=l,u})}var h=s.Object.prototype.toDatalessObject.call(this,e);return h.objects=i,h},render:function(e){this._transformDone=!0,this.callSuper("render",e),this._transformDone=!1},shouldCache:function(){var e=s.Object.prototype.shouldCache.call(this);if(e){for(var i=0,t=this._objects.length;i<t;i++)if(this._objects[i].willDrawShadow())return this.ownCaching=!1,!1}return e},willDrawShadow:function(){if(s.Object.prototype.willDrawShadow.call(this))return!0;for(var e=0,i=this._objects.length;e<i;e++)if(this._objects[e].willDrawShadow())return!0;return!1},isOnACache:function(){return this.ownCaching||this.group&&this.group.isOnACache()},drawObject:function(e){for(var i=0,t=this._objects.length;i<t;i++)this._objects[i].render(e);this._drawClipPath(e)},isCacheDirty:function(e){if(this.callSuper("isCacheDirty",e))return!0;if(!this.statefullCache)return!1;for(var i=0,t=this._objects.length;i<t;i++)if(this._objects[i].isCacheDirty(!0)){if(this._cacheCanvas){var n=this.cacheWidth/this.zoomX,h=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-n/2,-h/2,n,h)}return!0}return!1},_restoreObjectsState:function(){var e=this.calcOwnMatrix();return this._objects.forEach(function(i){s.util.addTransformToObject(i,e),delete i.group,i.setCoords()}),this},realizeTransform:function(e,i){return s.util.addTransformToObject(e,i),e},destroy:function(){return this._objects.forEach(function(e){e.set("dirty",!0)}),this._restoreObjectsState()},toActiveSelection:function(){if(this.canvas){var e=this._objects,i=this.canvas;this._objects=[];var t=this.toObject();delete t.objects;var n=new s.ActiveSelection([]);return n.set(t),n.type="activeSelection",i.remove(this),e.forEach(function(h){h.group=n,h.dirty=!0,i.add(h)}),n.canvas=i,n._objects=e,i._activeObject=n,n.setCoords(),n}},ungroupOnCanvas:function(){return this._restoreObjectsState()},setObjectsCoords:function(){var e=!0;return this.forEachObject(function(i){i.setCoords(e)}),this},_calcBounds:function(e){for(var i=[],t=[],n,h,r,l=["tr","br","bl","tl"],u=0,d=this._objects.length,g,m=l.length;u<d;++u){for(n=this._objects[u],r=n.calcACoords(),g=0;g<m;g++)h=l[g],i.push(r[h].x),t.push(r[h].y);n.aCoords=r}this._getBounds(i,t,e)},_getBounds:function(e,i,t){var n=new s.Point(o(e),o(i)),h=new s.Point(a(e),a(i)),r=n.y||0,l=n.x||0,u=h.x-n.x||0,d=h.y-n.y||0;this.width=u,this.height=d,t||this.setPositionByOrigin({x:l,y:r},"left","top")},_toSVG:function(e){for(var i=["<g ","COMMON_PARTS",` >
`],t=0,n=this._objects.length;t<n;t++)i.push("		",this._objects[t].toSVG(e));return i.push(`</g>
`),i},getSvgStyles:function(){var e=typeof this.opacity!="undefined"&&this.opacity!==1?"opacity: "+this.opacity+";":"",i=this.visible?"":" visibility: hidden;";return[e,this.getSvgFilter(),i].join("")},toClipPathSVG:function(e){for(var i=[],t=0,n=this._objects.length;t<n;t++)i.push("	",this._objects[t].toClipPathSVG(e));return this._createBaseClipPathSVGMarkup(i,{reviver:e})}}),s.Group.fromObject=function(e,i){var t=e.objects,n=s.util.object.clone(e,!0);if(delete n.objects,typeof t=="string"){s.loadSVGFromURL(t,function(h){var r=s.util.groupSVGElements(h,e,t);r.set(n),i&&i(r)});return}s.util.enlivenObjects(t,function(h){s.util.enlivenObjects([e.clipPath],function(r){var l=s.util.object.clone(e,!0);l.clipPath=r[0],delete l.objects,i&&i(new s.Group(h,l,!0))})})})}(et),function(c){var s=c.fabric||(c.fabric={});s.ActiveSelection||(s.ActiveSelection=s.util.createClass(s.Group,{type:"activeSelection",initialize:function(o,a){a=a||{},this._objects=o||[];for(var e=this._objects.length;e--;)this._objects[e].group=this;a.originX&&(this.originX=a.originX),a.originY&&(this.originY=a.originY),this._calcBounds(),this._updateObjectsCoords(),s.Object.prototype.initialize.call(this,a),this.setCoords()},toGroup:function(){var o=this._objects.concat();this._objects=[];var a=s.Object.prototype.toObject.call(this),e=new s.Group([]);if(delete a.type,e.set(a),o.forEach(function(t){t.canvas.remove(t),t.group=e}),e._objects=o,!this.canvas)return e;var i=this.canvas;return i.add(e),i._activeObject=e,e.setCoords(),e},onDeselect:function(){return this.destroy(),!1},toString:function(){return"#<fabric.ActiveSelection: ("+this.complexity()+")>"},shouldCache:function(){return!1},isOnACache:function(){return!1},_renderControls:function(o,a,e){o.save(),o.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,this.callSuper("_renderControls",o,a),e=e||{},typeof e.hasControls=="undefined"&&(e.hasControls=!1),e.forActiveSelection=!0;for(var i=0,t=this._objects.length;i<t;i++)this._objects[i]._renderControls(o,e);o.restore()}}),s.ActiveSelection.fromObject=function(o,a){s.util.enlivenObjects(o.objects,function(e){delete o.objects,a&&a(new s.ActiveSelection(e,o,!0))})})}(et),function(c){var s=f.util.object.extend;if(c.fabric||(c.fabric={}),c.fabric.Image){f.warn("fabric.Image is already defined.");return}f.Image=f.util.createClass(f.Object,{type:"image",strokeWidth:0,srcFromAttribute:!1,_lastScaleX:1,_lastScaleY:1,_filterScalingX:1,_filterScalingY:1,minimumScaleTrigger:.5,stateProperties:f.Object.prototype.stateProperties.concat("cropX","cropY"),cacheProperties:f.Object.prototype.cacheProperties.concat("cropX","cropY"),cacheKey:"",cropX:0,cropY:0,imageSmoothing:!0,initialize:function(o,a){a||(a={}),this.filters=[],this.cacheKey="texture"+f.Object.__uid++,this.callSuper("initialize",a),this._initElement(o,a)},getElement:function(){return this._element||{}},setElement:function(o,a){return this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._element=o,this._originalElement=o,this._initConfig(a),this.filters.length!==0&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters(),this},removeTexture:function(o){var a=f.filterBackend;a&&a.evictCachesForKey&&a.evictCachesForKey(o)},dispose:function(){this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._cacheContext=void 0,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(function(o){f.util.cleanUpJsdomNode(this[o]),this[o]=void 0}.bind(this))},getCrossOrigin:function(){return this._originalElement&&(this._originalElement.crossOrigin||null)},getOriginalSize:function(){var o=this.getElement();return{width:o.naturalWidth||o.width,height:o.naturalHeight||o.height}},_stroke:function(o){if(!(!this.stroke||this.strokeWidth===0)){var a=this.width/2,e=this.height/2;o.beginPath(),o.moveTo(-a,-e),o.lineTo(a,-e),o.lineTo(a,e),o.lineTo(-a,e),o.lineTo(-a,-e),o.closePath()}},toObject:function(o){var a=[];this.filters.forEach(function(i){i&&a.push(i.toObject())});var e=s(this.callSuper("toObject",["cropX","cropY"].concat(o)),{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:a});return this.resizeFilter&&(e.resizeFilter=this.resizeFilter.toObject()),e},hasCrop:function(){return this.cropX||this.cropY||this.width<this._element.width||this.height<this._element.height},_toSVG:function(){var o=[],a=[],e,i=this._element,t=-this.width/2,n=-this.height/2,h="",r="";if(!i)return[];if(this.hasCrop()){var l=f.Object.__uid++;o.push('<clipPath id="imageCrop_'+l+`">
`,'	<rect x="'+t+'" y="'+n+'" width="'+this.width+'" height="'+this.height+`" />
`,`</clipPath>
`),h=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(r='" image-rendering="optimizeSpeed'),a.push("	<image ","COMMON_PARTS",'xlink:href="',this.getSvgSrc(!0),'" x="',t-this.cropX,'" y="',n-this.cropY,'" width="',i.width||i.naturalWidth,'" height="',i.height||i.height,r,'"',h,`></image>
`),this.stroke||this.strokeDashArray){var u=this.fill;this.fill=null,e=["	<rect ",'x="',t,'" y="',n,'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),`"/>
`],this.fill=u}return this.paintFirst!=="fill"?o=o.concat(e,a):o=o.concat(a,e),o},getSrc:function(o){var a=o?this._element:this._originalElement;return a?a.toDataURL?a.toDataURL():this.srcFromAttribute?a.getAttribute("src"):a.src:this.src||""},setSrc:function(o,a,e){return f.util.loadImage(o,function(i,t){this.setElement(i,e),this._setWidthHeight(),a&&a(this,t)},this,e&&e.crossOrigin),this},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},applyResizeFilters:function(){var o=this.resizeFilter,a=this.minimumScaleTrigger,e=this.getTotalObjectScaling(),i=e.scaleX,t=e.scaleY,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!o||i>a&&t>a){this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=i,this._lastScaleY=t;return}f.filterBackend||(f.filterBackend=f.initFilterBackend());var h=f.util.createCanvasElement(),r=this._filteredEl?this.cacheKey+"_filtered":this.cacheKey,l=n.width,u=n.height;h.width=l,h.height=u,this._element=h,this._lastScaleX=o.scaleX=i,this._lastScaleY=o.scaleY=t,f.filterBackend.applyFilters([o],n,l,u,this._element,r),this._filterScalingX=h.width/this._originalElement.width,this._filterScalingY=h.height/this._originalElement.height},applyFilters:function(o){if(o=o||this.filters||[],o=o.filter(function(n){return n&&!n.isNeutralState()}),this.set("dirty",!0),this.removeTexture(this.cacheKey+"_filtered"),o.length===0)return this._element=this._originalElement,this._filteredEl=null,this._filterScalingX=1,this._filterScalingY=1,this;var a=this._originalElement,e=a.naturalWidth||a.width,i=a.naturalHeight||a.height;if(this._element===this._originalElement){var t=f.util.createCanvasElement();t.width=e,t.height=i,this._element=t,this._filteredEl=t}else this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,e,i),this._lastScaleX=1,this._lastScaleY=1;return f.filterBackend||(f.filterBackend=f.initFilterBackend()),f.filterBackend.applyFilters(o,this._originalElement,e,i,this._element,this.cacheKey),(this._originalElement.width!==this._element.width||this._originalElement.height!==this._element.height)&&(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height),this},_render:function(o){f.util.setImageSmoothing(o,this.imageSmoothing),this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(o),this._renderPaintInOrder(o)},drawCacheOnCanvas:function(o){f.util.setImageSmoothing(o,this.imageSmoothing),f.Object.prototype.drawCacheOnCanvas.call(this,o)},shouldCache:function(){return this.needsItsOwnCache()},_renderFill:function(o){var a=this._element;if(a){var e=this._filterScalingX,i=this._filterScalingY,t=this.width,n=this.height,h=Math.min,r=Math.max,l=r(this.cropX,0),u=r(this.cropY,0),d=a.naturalWidth||a.width,g=a.naturalHeight||a.height,m=l*e,v=u*i,y=h(t*e,d-m),T=h(n*i,g-v),M=-t/2,W=-n/2,H=h(t,d/e-l),G=h(n,g/i-u);a&&o.drawImage(a,m,v,y,T,M,W,H,G)}},_needsResize:function(){var o=this.getTotalObjectScaling();return o.scaleX!==this._lastScaleX||o.scaleY!==this._lastScaleY},_resetWidthHeight:function(){this.set(this.getOriginalSize())},_initElement:function(o,a){this.setElement(f.util.getById(o),a),f.util.addClass(this.getElement(),f.Image.CSS_CANVAS)},_initConfig:function(o){o||(o={}),this.setOptions(o),this._setWidthHeight(o)},_initFilters:function(o,a){o&&o.length?f.util.enlivenObjects(o,function(e){a&&a(e)},"fabric.Image.filters"):a&&a()},_setWidthHeight:function(o){o||(o={});var a=this.getElement();this.width=o.width||a.naturalWidth||a.width||0,this.height=o.height||a.naturalHeight||a.height||0},parsePreserveAspectRatioAttribute:function(){var o=f.util.parsePreserveAspectRatioAttribute(this.preserveAspectRatio||""),a=this._element.width,e=this._element.height,i=1,t=1,n=0,h=0,r=0,l=0,u,d=this.width,g=this.height,m={width:d,height:g};return o&&(o.alignX!=="none"||o.alignY!=="none")?(o.meetOrSlice==="meet"&&(i=t=f.util.findScaleToFit(this._element,m),u=(d-a*i)/2,o.alignX==="Min"&&(n=-u),o.alignX==="Max"&&(n=u),u=(g-e*t)/2,o.alignY==="Min"&&(h=-u),o.alignY==="Max"&&(h=u)),o.meetOrSlice==="slice"&&(i=t=f.util.findScaleToCover(this._element,m),u=a-d/i,o.alignX==="Mid"&&(r=u/2),o.alignX==="Max"&&(r=u),u=e-g/t,o.alignY==="Mid"&&(l=u/2),o.alignY==="Max"&&(l=u),a=d/i,e=g/t)):(i=d/a,t=g/e),{width:a,height:e,scaleX:i,scaleY:t,offsetLeft:n,offsetTop:h,cropX:r,cropY:l}}}),f.Image.CSS_CANVAS="canvas-img",f.Image.prototype.getSvgSrc=f.Image.prototype.getSrc,f.Image.fromObject=function(o,a){var e=f.util.object.clone(o);f.util.loadImage(e.src,function(i,t){if(t){a&&a(null,!0);return}f.Image.prototype._initFilters.call(e,e.filters,function(n){e.filters=n||[],f.Image.prototype._initFilters.call(e,[e.resizeFilter],function(h){e.resizeFilter=h[0],f.util.enlivenObjects([e.clipPath],function(r){e.clipPath=r[0];var l=new f.Image(i,e);a(l,!1)})})})},null,e.crossOrigin)},f.Image.fromURL=function(o,a,e){f.util.loadImage(o,function(i,t){a&&a(new f.Image(i,e),t)},null,e&&e.crossOrigin)},f.Image.ATTRIBUTE_NAMES=f.SHARED_ATTRIBUTES.concat("x y width height preserveAspectRatio xlink:href crossOrigin image-rendering".split(" ")),f.Image.fromElement=function(o,a,e){var i=f.parseAttributes(o,f.Image.ATTRIBUTE_NAMES);f.Image.fromURL(i["xlink:href"],a,s(e?f.util.object.clone(e):{},i))}}(et),f.util.object.extend(f.Object.prototype,{_getAngleValueForStraighten:function(){var c=this.angle%360;return c>0?Math.round((c-1)/90)*90:Math.round(c/90)*90},straighten:function(){return this.rotate(this._getAngleValueForStraighten()),this},fxStraighten:function(c){c=c||{};var s=function(){},o=c.onComplete||s,a=c.onChange||s,e=this;return f.util.animate({startValue:this.get("angle"),endValue:this._getAngleValueForStraighten(),duration:this.FX_DURATION,onChange:function(i){e.rotate(i),a()},onComplete:function(){e.setCoords(),o()}}),this}}),f.util.object.extend(f.StaticCanvas.prototype,{straightenObject:function(c){return c.straighten(),this.requestRenderAll(),this},fxStraightenObject:function(c){return c.fxStraighten({onChange:this.requestRenderAllBound}),this}}),function(){function c(o,a){var e="precision "+a+` float;
void main(){}`,i=o.createShader(o.FRAGMENT_SHADER);return o.shaderSource(i,e),o.compileShader(i),!!o.getShaderParameter(i,o.COMPILE_STATUS)}f.isWebglSupported=function(o){if(f.isLikelyNode)return!1;o=o||f.WebglFilterBackend.prototype.tileSize;var a=document.createElement("canvas"),e=a.getContext("webgl")||a.getContext("experimental-webgl"),i=!1;if(e){f.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),i=f.maxTextureSize>=o;for(var t=["highp","mediump","lowp"],n=0;n<3;n++)if(c(e,t[n])){f.webGlPrecision=t[n];break}}return this.isSupported=i,i},f.WebglFilterBackend=s;function s(o){o&&o.tileSize&&(this.tileSize=o.tileSize),this.setupGLContext(this.tileSize,this.tileSize),this.captureGPUInfo()}s.prototype={tileSize:2048,resources:{},setupGLContext:function(o,a){this.dispose(),this.createWebGLCanvas(o,a),this.aPosition=new Float32Array([0,0,0,1,1,0,1,1]),this.chooseFastestCopyGLTo2DMethod(o,a)},chooseFastestCopyGLTo2DMethod:function(o,a){var e=typeof window.performance!="undefined",i;try{new ImageData(1,1),i=!0}catch(m){i=!1}var t=typeof ArrayBuffer!="undefined",n=typeof Uint8ClampedArray!="undefined";if(e&&i&&t&&n){var h=f.util.createCanvasElement(),r=new ArrayBuffer(o*a*4);if(f.forceGLPutImageData){this.imageBuffer=r,this.copyGLTo2D=dt;return}var l={imageBuffer:r,destinationWidth:o,destinationHeight:a,targetCanvas:h},u,d,g;h.width=o,h.height=a,u=window.performance.now(),ft.call(l,this.gl,l),d=window.performance.now()-u,u=window.performance.now(),dt.call(l,this.gl,l),g=window.performance.now()-u,d>g?(this.imageBuffer=r,this.copyGLTo2D=dt):this.copyGLTo2D=ft}},createWebGLCanvas:function(o,a){var e=f.util.createCanvasElement();e.width=o,e.height=a;var i={alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1},t=e.getContext("webgl",i);t||(t=e.getContext("experimental-webgl",i)),t&&(t.clearColor(0,0,0,0),this.canvas=e,this.gl=t)},applyFilters:function(o,a,e,i,t,n){var h=this.gl,r;n&&(r=this.getCachedTexture(n,a));var l={originalWidth:a.width||a.originalWidth,originalHeight:a.height||a.originalHeight,sourceWidth:e,sourceHeight:i,destinationWidth:e,destinationHeight:i,context:h,sourceTexture:this.createTexture(h,e,i,!r&&a),targetTexture:this.createTexture(h,e,i),originalTexture:r||this.createTexture(h,e,i,!r&&a),passes:o.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:t},u=h.createFramebuffer();return h.bindFramebuffer(h.FRAMEBUFFER,u),o.forEach(function(d){d&&d.applyTo(l)}),pt(l),this.copyGLTo2D(h,l),h.bindTexture(h.TEXTURE_2D,null),h.deleteTexture(l.sourceTexture),h.deleteTexture(l.targetTexture),h.deleteFramebuffer(u),t.getContext("2d").setTransform(1,0,0,1,0,0),l},dispose:function(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()},clearWebGLCaches:function(){this.programCache={},this.textureCache={}},createTexture:function(o,a,e,i){var t=o.createTexture();return o.bindTexture(o.TEXTURE_2D,t),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_MAG_FILTER,o.NEAREST),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_MIN_FILTER,o.NEAREST),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_WRAP_S,o.CLAMP_TO_EDGE),o.texParameteri(o.TEXTURE_2D,o.TEXTURE_WRAP_T,o.CLAMP_TO_EDGE),i?o.texImage2D(o.TEXTURE_2D,0,o.RGBA,o.RGBA,o.UNSIGNED_BYTE,i):o.texImage2D(o.TEXTURE_2D,0,o.RGBA,a,e,0,o.RGBA,o.UNSIGNED_BYTE,null),t},getCachedTexture:function(o,a){if(this.textureCache[o])return this.textureCache[o];var e=this.createTexture(this.gl,a.width,a.height,a);return this.textureCache[o]=e,e},evictCachesForKey:function(o){this.textureCache[o]&&(this.gl.deleteTexture(this.textureCache[o]),delete this.textureCache[o])},copyGLTo2D:ft,captureGPUInfo:function(){if(this.gpuInfo)return this.gpuInfo;var o=this.gl,a={renderer:"",vendor:""};if(!o)return a;var e=o.getExtension("WEBGL_debug_renderer_info");if(e){var i=o.getParameter(e.UNMASKED_RENDERER_WEBGL),t=o.getParameter(e.UNMASKED_VENDOR_WEBGL);i&&(a.renderer=i.toLowerCase()),t&&(a.vendor=t.toLowerCase())}return this.gpuInfo=a,a}}}();function pt(c){var s=c.targetCanvas,o=s.width,a=s.height,e=c.destinationWidth,i=c.destinationHeight;(o!==e||a!==i)&&(s.width=e,s.height=i)}function ft(c,s){var o=c.canvas,a=s.targetCanvas,e=a.getContext("2d");e.translate(0,a.height),e.scale(1,-1);var i=o.height-a.height;e.drawImage(o,0,i,a.width,a.height,0,0,a.width,a.height)}function dt(c,s){var o=s.targetCanvas,a=o.getContext("2d"),e=s.destinationWidth,i=s.destinationHeight,t=e*i*4,n=new Uint8Array(this.imageBuffer,0,t),h=new Uint8ClampedArray(this.imageBuffer,0,t);c.readPixels(0,0,e,i,c.RGBA,c.UNSIGNED_BYTE,n);var r=new ImageData(h,e,i);a.putImageData(r,0,0)}(function(){var c=function(){};f.Canvas2dFilterBackend=s;function s(){}s.prototype={evictCachesForKey:c,dispose:c,clearWebGLCaches:c,resources:{},applyFilters:function(o,a,e,i,t){var n=t.getContext("2d");n.drawImage(a,0,0,e,i);var h=n.getImageData(0,0,e,i),r=n.getImageData(0,0,e,i),l={sourceWidth:e,sourceHeight:i,imageData:h,originalEl:a,originalImageData:r,canvasEl:t,ctx:n,filterBackend:this};return o.forEach(function(u){u.applyTo(l)}),(l.imageData.width!==e||l.imageData.height!==i)&&(t.width=l.imageData.width,t.height=l.imageData.height),n.putImageData(l.imageData,0,0),l}}})(),f.Image=f.Image||{},f.Image.filters=f.Image.filters||{},f.Image.filters.BaseFilter=f.util.createClass({type:"BaseFilter",vertexSource:`attribute vec2 aPosition;
varying vec2 vTexCoord;
void main() {
vTexCoord = aPosition;
gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
}`,fragmentSource:`precision highp float;
varying vec2 vTexCoord;
uniform sampler2D uTexture;
void main() {
gl_FragColor = texture2D(uTexture, vTexCoord);
}`,initialize:function(c){c&&this.setOptions(c)},setOptions:function(c){for(var s in c)this[s]=c[s]},createProgram:function(c,s,o){s=s||this.fragmentSource,o=o||this.vertexSource,f.webGlPrecision!=="highp"&&(s=s.replace(/precision highp float/g,"precision "+f.webGlPrecision+" float"));var a=c.createShader(c.VERTEX_SHADER);if(c.shaderSource(a,o),c.compileShader(a),!c.getShaderParameter(a,c.COMPILE_STATUS))throw new Error("Vertex shader compile error for "+this.type+": "+c.getShaderInfoLog(a));var e=c.createShader(c.FRAGMENT_SHADER);if(c.shaderSource(e,s),c.compileShader(e),!c.getShaderParameter(e,c.COMPILE_STATUS))throw new Error("Fragment shader compile error for "+this.type+": "+c.getShaderInfoLog(e));var i=c.createProgram();if(c.attachShader(i,a),c.attachShader(i,e),c.linkProgram(i),!c.getProgramParameter(i,c.LINK_STATUS))throw new Error('Shader link error for "${this.type}" '+c.getProgramInfoLog(i));var t=this.getAttributeLocations(c,i),n=this.getUniformLocations(c,i)||{};return n.uStepW=c.getUniformLocation(i,"uStepW"),n.uStepH=c.getUniformLocation(i,"uStepH"),{program:i,attributeLocations:t,uniformLocations:n}},getAttributeLocations:function(c,s){return{aPosition:c.getAttribLocation(s,"aPosition")}},getUniformLocations:function(){return{}},sendAttributeData:function(c,s,o){var a=s.aPosition,e=c.createBuffer();c.bindBuffer(c.ARRAY_BUFFER,e),c.enableVertexAttribArray(a),c.vertexAttribPointer(a,2,c.FLOAT,!1,0,0),c.bufferData(c.ARRAY_BUFFER,o,c.STATIC_DRAW)},_setupFrameBuffer:function(c){var s=c.context,o,a;c.passes>1?(o=c.destinationWidth,a=c.destinationHeight,(c.sourceWidth!==o||c.sourceHeight!==a)&&(s.deleteTexture(c.targetTexture),c.targetTexture=c.filterBackend.createTexture(s,o,a)),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,c.targetTexture,0)):(s.bindFramebuffer(s.FRAMEBUFFER,null),s.finish())},_swapTextures:function(c){c.passes--,c.pass++;var s=c.targetTexture;c.targetTexture=c.sourceTexture,c.sourceTexture=s},isNeutralState:function(){var c=this.mainParameter,s=f.Image.filters[this.type].prototype;if(c)if(Array.isArray(s[c])){for(var o=s[c].length;o--;)if(this[c][o]!==s[c][o])return!1;return!0}else return s[c]===this[c];else return!1},applyTo:function(c){c.webgl?(this._setupFrameBuffer(c),this.applyToWebGL(c),this._swapTextures(c)):this.applyTo2d(c)},retrieveShader:function(c){return c.programCache.hasOwnProperty(this.type)||(c.programCache[this.type]=this.createProgram(c.context)),c.programCache[this.type]},applyToWebGL:function(c){var s=c.context,o=this.retrieveShader(c);c.pass===0&&c.originalTexture?s.bindTexture(s.TEXTURE_2D,c.originalTexture):s.bindTexture(s.TEXTURE_2D,c.sourceTexture),s.useProgram(o.program),this.sendAttributeData(s,o.attributeLocations,c.aPosition),s.uniform1f(o.uniformLocations.uStepW,1/c.sourceWidth),s.uniform1f(o.uniformLocations.uStepH,1/c.sourceHeight),this.sendUniformData(s,o.uniformLocations),s.viewport(0,0,c.destinationWidth,c.destinationHeight),s.drawArrays(s.TRIANGLE_STRIP,0,4)},bindAdditionalTexture:function(c,s,o){c.activeTexture(o),c.bindTexture(c.TEXTURE_2D,s),c.activeTexture(c.TEXTURE0)},unbindAdditionalTexture:function(c,s){c.activeTexture(s),c.bindTexture(c.TEXTURE_2D,null),c.activeTexture(c.TEXTURE0)},getMainParameter:function(){return this[this.mainParameter]},setMainParameter:function(c){this[this.mainParameter]=c},sendUniformData:function(){},createHelpLayer:function(c){if(!c.helpLayer){var s=document.createElement("canvas");s.width=c.sourceWidth,s.height=c.sourceHeight,c.helpLayer=s}},toObject:function(){var c={type:this.type},s=this.mainParameter;return s&&(c[s]=this[s]),c},toJSON:function(){return this.toObject()}}),f.Image.filters.BaseFilter.fromObject=function(c,s){var o=new f.Image.filters[c.type](c);return s&&s(o),o},function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.ColorMatrix=a(o.BaseFilter,{type:"ColorMatrix",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
varying vec2 vTexCoord;
uniform mat4 uColorMatrix;
uniform vec4 uConstants;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color *= uColorMatrix;
color += uConstants;
gl_FragColor = color;
}`,matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0,initialize:function(e){this.callSuper("initialize",e),this.matrix=this.matrix.slice(0)},applyTo2d:function(e){var i=e.imageData,t=i.data,n=t.length,h=this.matrix,r,l,u,d,g,m=this.colorsOnly;for(g=0;g<n;g+=4)r=t[g],l=t[g+1],u=t[g+2],m?(t[g]=r*h[0]+l*h[1]+u*h[2]+h[4]*255,t[g+1]=r*h[5]+l*h[6]+u*h[7]+h[9]*255,t[g+2]=r*h[10]+l*h[11]+u*h[12]+h[14]*255):(d=t[g+3],t[g]=r*h[0]+l*h[1]+u*h[2]+d*h[3]+h[4]*255,t[g+1]=r*h[5]+l*h[6]+u*h[7]+d*h[8]+h[9]*255,t[g+2]=r*h[10]+l*h[11]+u*h[12]+d*h[13]+h[14]*255,t[g+3]=r*h[15]+l*h[16]+u*h[17]+d*h[18]+h[19]*255)},getUniformLocations:function(e,i){return{uColorMatrix:e.getUniformLocation(i,"uColorMatrix"),uConstants:e.getUniformLocation(i,"uConstants")}},sendUniformData:function(e,i){var t=this.matrix,n=[t[0],t[1],t[2],t[3],t[5],t[6],t[7],t[8],t[10],t[11],t[12],t[13],t[15],t[16],t[17],t[18]],h=[t[4],t[9],t[14],t[19]];e.uniformMatrix4fv(i.uColorMatrix,!1,n),e.uniform4fv(i.uConstants,h)}}),s.Image.filters.ColorMatrix.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Brightness=a(o.BaseFilter,{type:"Brightness",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uBrightness;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color.rgb += uBrightness;
gl_FragColor = color;
}`,brightness:0,mainParameter:"brightness",applyTo2d:function(e){if(this.brightness!==0){var i=e.imageData,t=i.data,n,h=t.length,r=Math.round(this.brightness*255);for(n=0;n<h;n+=4)t[n]=t[n]+r,t[n+1]=t[n+1]+r,t[n+2]=t[n+2]+r}},getUniformLocations:function(e,i){return{uBrightness:e.getUniformLocation(i,"uBrightness")}},sendUniformData:function(e,i){e.uniform1f(i.uBrightness,this.brightness)}}),s.Image.filters.Brightness.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.Convolute=e(a.BaseFilter,{type:"Convolute",opaque:!1,matrix:[0,0,0,0,1,0,0,0,0],fragmentSource:{Convolute_3_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[9];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 3.0; h+=1.0) {
for (float w = 0.0; w < 3.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_3_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[9];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 3.0; h+=1.0) {
for (float w = 0.0; w < 3.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_5_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[25];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 5.0; h+=1.0) {
for (float w = 0.0; w < 5.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_5_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[25];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 5.0; h+=1.0) {
for (float w = 0.0; w < 5.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_7_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[49];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 7.0; h+=1.0) {
for (float w = 0.0; w < 7.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_7_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[49];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 7.0; h+=1.0) {
for (float w = 0.0; w < 7.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`,Convolute_9_1:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[81];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 0);
for (float h = 0.0; h < 9.0; h+=1.0) {
for (float w = 0.0; w < 9.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];
}
}
gl_FragColor = color;
}`,Convolute_9_0:`precision highp float;
uniform sampler2D uTexture;
uniform float uMatrix[81];
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
vec4 color = vec4(0, 0, 0, 1);
for (float h = 0.0; h < 9.0; h+=1.0) {
for (float w = 0.0; w < 9.0; w+=1.0) {
vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];
}
}
float alpha = texture2D(uTexture, vTexCoord).a;
gl_FragColor = color;
gl_FragColor.a = alpha;
}`},retrieveShader:function(i){var t=Math.sqrt(this.matrix.length),n=this.type+"_"+t+"_"+(this.opaque?1:0),h=this.fragmentSource[n];return i.programCache.hasOwnProperty(n)||(i.programCache[n]=this.createProgram(i.context,h)),i.programCache[n]},applyTo2d:function(i){var t=i.imageData,n=t.data,h=this.matrix,r=Math.round(Math.sqrt(h.length)),l=Math.floor(r/2),u=t.width,d=t.height,g=i.ctx.createImageData(u,d),m=g.data,v=this.opaque?1:0,y,T,M,W,H,G,V,U,N,Q,$,K,S;for($=0;$<d;$++)for(Q=0;Q<u;Q++){for(H=($*u+Q)*4,y=0,T=0,M=0,W=0,S=0;S<r;S++)for(K=0;K<r;K++)V=$+S-l,G=Q+K-l,!(V<0||V>=d||G<0||G>=u)&&(U=(V*u+G)*4,N=h[S*r+K],y+=n[U]*N,T+=n[U+1]*N,M+=n[U+2]*N,v||(W+=n[U+3]*N));m[H]=y,m[H+1]=T,m[H+2]=M,v?m[H+3]=n[H+3]:m[H+3]=W}i.imageData=g},getUniformLocations:function(i,t){return{uMatrix:i.getUniformLocation(t,"uMatrix"),uOpaque:i.getUniformLocation(t,"uOpaque"),uHalfSize:i.getUniformLocation(t,"uHalfSize"),uSize:i.getUniformLocation(t,"uSize")}},sendUniformData:function(i,t){i.uniform1fv(t.uMatrix,this.matrix)},toObject:function(){return o(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),s.Image.filters.Convolute.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Grayscale=a(o.BaseFilter,{type:"Grayscale",fragmentSource:{average:`precision highp float;
uniform sampler2D uTexture;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float average = (color.r + color.b + color.g) / 3.0;
gl_FragColor = vec4(average, average, average, color.a);
}`,lightness:`precision highp float;
uniform sampler2D uTexture;
uniform int uMode;
varying vec2 vTexCoord;
void main() {
vec4 col = texture2D(uTexture, vTexCoord);
float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;
gl_FragColor = vec4(average, average, average, col.a);
}`,luminosity:`precision highp float;
uniform sampler2D uTexture;
uniform int uMode;
varying vec2 vTexCoord;
void main() {
vec4 col = texture2D(uTexture, vTexCoord);
float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;
gl_FragColor = vec4(average, average, average, col.a);
}`},mode:"average",mainParameter:"mode",applyTo2d:function(e){var i=e.imageData,t=i.data,n,h=t.length,r,l=this.mode;for(n=0;n<h;n+=4)l==="average"?r=(t[n]+t[n+1]+t[n+2])/3:l==="lightness"?r=(Math.min(t[n],t[n+1],t[n+2])+Math.max(t[n],t[n+1],t[n+2]))/2:l==="luminosity"&&(r=.21*t[n]+.72*t[n+1]+.07*t[n+2]),t[n]=r,t[n+1]=r,t[n+2]=r},retrieveShader:function(e){var i=this.type+"_"+this.mode;if(!e.programCache.hasOwnProperty(i)){var t=this.fragmentSource[this.mode];e.programCache[i]=this.createProgram(e.context,t)}return e.programCache[i]},getUniformLocations:function(e,i){return{uMode:e.getUniformLocation(i,"uMode")}},sendUniformData:function(e,i){var t=1;e.uniform1i(i.uMode,t)},isNeutralState:function(){return!1}}),s.Image.filters.Grayscale.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Invert=a(o.BaseFilter,{type:"Invert",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform int uInvert;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
if (uInvert == 1) {
gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);
} else {
gl_FragColor = color;
}
}`,invert:!0,mainParameter:"invert",applyTo2d:function(e){var i=e.imageData,t=i.data,n,h=t.length;for(n=0;n<h;n+=4)t[n]=255-t[n],t[n+1]=255-t[n+1],t[n+2]=255-t[n+2]},isNeutralState:function(){return!this.invert},getUniformLocations:function(e,i){return{uInvert:e.getUniformLocation(i,"uInvert")}},sendUniformData:function(e,i){e.uniform1i(i.uInvert,this.invert)}}),s.Image.filters.Invert.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.Noise=e(a.BaseFilter,{type:"Noise",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uStepH;
uniform float uNoise;
uniform float uSeed;
varying vec2 vTexCoord;
float rand(vec2 co, float seed, float vScale) {
return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);
}
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;
gl_FragColor = color;
}`,mainParameter:"noise",noise:0,applyTo2d:function(i){if(this.noise!==0){var t=i.imageData,n=t.data,h,r=n.length,l=this.noise,u;for(h=0,r=n.length;h<r;h+=4)u=(.5-Math.random())*l,n[h]+=u,n[h+1]+=u,n[h+2]+=u}},getUniformLocations:function(i,t){return{uNoise:i.getUniformLocation(t,"uNoise"),uSeed:i.getUniformLocation(t,"uSeed")}},sendUniformData:function(i,t){i.uniform1f(t.uNoise,this.noise/255),i.uniform1f(t.uSeed,Math.random())},toObject:function(){return o(this.callSuper("toObject"),{noise:this.noise})}}),s.Image.filters.Noise.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Pixelate=a(o.BaseFilter,{type:"Pixelate",blocksize:4,mainParameter:"blocksize",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uBlocksize;
uniform float uStepW;
uniform float uStepH;
varying vec2 vTexCoord;
void main() {
float blockW = uBlocksize * uStepW;
float blockH = uBlocksize * uStepW;
int posX = int(vTexCoord.x / blockW);
int posY = int(vTexCoord.y / blockH);
float fposX = float(posX);
float fposY = float(posY);
vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);
vec4 color = texture2D(uTexture, squareCoords);
gl_FragColor = color;
}`,applyTo2d:function(e){var i=e.imageData,t=i.data,n=i.height,h=i.width,r,l,u,d,g,m,v,y,T,M,W;for(l=0;l<n;l+=this.blocksize)for(u=0;u<h;u+=this.blocksize)for(r=l*4*h+u*4,d=t[r],g=t[r+1],m=t[r+2],v=t[r+3],M=Math.min(l+this.blocksize,n),W=Math.min(u+this.blocksize,h),y=l;y<M;y++)for(T=u;T<W;T++)r=y*4*h+T*4,t[r]=d,t[r+1]=g,t[r+2]=m,t[r+3]=v},isNeutralState:function(){return this.blocksize===1},getUniformLocations:function(e,i){return{uBlocksize:e.getUniformLocation(i,"uBlocksize"),uStepW:e.getUniformLocation(i,"uStepW"),uStepH:e.getUniformLocation(i,"uStepH")}},sendUniformData:function(e,i){e.uniform1f(i.uBlocksize,this.blocksize)}}),s.Image.filters.Pixelate.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.extend,a=s.Image.filters,e=s.util.createClass;a.RemoveColor=e(a.BaseFilter,{type:"RemoveColor",color:"#FFFFFF",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec4 uLow;
uniform vec4 uHigh;
varying vec2 vTexCoord;
void main() {
gl_FragColor = texture2D(uTexture, vTexCoord);
if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {
gl_FragColor.a = 0.0;
}
}`,distance:.02,useAlpha:!1,applyTo2d:function(i){var t=i.imageData,n=t.data,h,r=this.distance*255,l,u,d,g=new s.Color(this.color).getSource(),m=[g[0]-r,g[1]-r,g[2]-r],v=[g[0]+r,g[1]+r,g[2]+r];for(h=0;h<n.length;h+=4)l=n[h],u=n[h+1],d=n[h+2],l>m[0]&&u>m[1]&&d>m[2]&&l<v[0]&&u<v[1]&&d<v[2]&&(n[h+3]=0)},getUniformLocations:function(i,t){return{uLow:i.getUniformLocation(t,"uLow"),uHigh:i.getUniformLocation(t,"uHigh")}},sendUniformData:function(i,t){var n=new s.Color(this.color).getSource(),h=parseFloat(this.distance),r=[0+n[0]/255-h,0+n[1]/255-h,0+n[2]/255-h,1],l=[n[0]/255+h,n[1]/255+h,n[2]/255+h,1];i.uniform4fv(t.uLow,r),i.uniform4fv(t.uHigh,l)},toObject:function(){return o(this.callSuper("toObject"),{color:this.color,distance:this.distance})}}),s.Image.filters.RemoveColor.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass,e={Brownie:[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0],Vintage:[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0],Kodachrome:[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0],Technicolor:[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0],Polaroid:[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],Sepia:[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0],BlackWhite:[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]};for(var i in e)o[i]=a(o.ColorMatrix,{type:i,matrix:e[i],mainParameter:!1,colorsOnly:!0}),s.Image.filters[i].fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric,o=s.Image.filters,a=s.util.createClass;o.BlendColor=a(o.BaseFilter,{type:"BlendColor",color:"#F95C63",mode:"multiply",alpha:1,fragmentSource:{multiply:`gl_FragColor.rgb *= uColor.rgb;
`,screen:`gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);
`,add:`gl_FragColor.rgb += uColor.rgb;
`,diff:`gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);
`,subtract:`gl_FragColor.rgb -= uColor.rgb;
`,lighten:`gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);
`,darken:`gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);
`,exclusion:`gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);
`,overlay:`if (uColor.r < 0.5) {
gl_FragColor.r *= 2.0 * uColor.r;
} else {
gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);
}
if (uColor.g < 0.5) {
gl_FragColor.g *= 2.0 * uColor.g;
} else {
gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);
}
if (uColor.b < 0.5) {
gl_FragColor.b *= 2.0 * uColor.b;
} else {
gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);
}
`,tint:`gl_FragColor.rgb *= (1.0 - uColor.a);
gl_FragColor.rgb += uColor.rgb;
`},buildSource:function(e){return`precision highp float;
uniform sampler2D uTexture;
uniform vec4 uColor;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
gl_FragColor = color;
if (color.a > 0.0) {
`+this.fragmentSource[e]+`}
}`},retrieveShader:function(e){var i=this.type+"_"+this.mode,t;return e.programCache.hasOwnProperty(i)||(t=this.buildSource(this.mode),e.programCache[i]=this.createProgram(e.context,t)),e.programCache[i]},applyTo2d:function(e){var i=e.imageData,t=i.data,n=t.length,h,r,l,u,d,g,m,v=1-this.alpha;m=new s.Color(this.color).getSource(),h=m[0]*this.alpha,r=m[1]*this.alpha,l=m[2]*this.alpha;for(var y=0;y<n;y+=4)switch(u=t[y],d=t[y+1],g=t[y+2],this.mode){case"multiply":t[y]=u*h/255,t[y+1]=d*r/255,t[y+2]=g*l/255;break;case"screen":t[y]=255-(255-u)*(255-h)/255,t[y+1]=255-(255-d)*(255-r)/255,t[y+2]=255-(255-g)*(255-l)/255;break;case"add":t[y]=u+h,t[y+1]=d+r,t[y+2]=g+l;break;case"diff":case"difference":t[y]=Math.abs(u-h),t[y+1]=Math.abs(d-r),t[y+2]=Math.abs(g-l);break;case"subtract":t[y]=u-h,t[y+1]=d-r,t[y+2]=g-l;break;case"darken":t[y]=Math.min(u,h),t[y+1]=Math.min(d,r),t[y+2]=Math.min(g,l);break;case"lighten":t[y]=Math.max(u,h),t[y+1]=Math.max(d,r),t[y+2]=Math.max(g,l);break;case"overlay":t[y]=h<128?2*u*h/255:255-2*(255-u)*(255-h)/255,t[y+1]=r<128?2*d*r/255:255-2*(255-d)*(255-r)/255,t[y+2]=l<128?2*g*l/255:255-2*(255-g)*(255-l)/255;break;case"exclusion":t[y]=h+u-2*h*u/255,t[y+1]=r+d-2*r*d/255,t[y+2]=l+g-2*l*g/255;break;case"tint":t[y]=h+u*v,t[y+1]=r+d*v,t[y+2]=l+g*v}},getUniformLocations:function(e,i){return{uColor:e.getUniformLocation(i,"uColor")}},sendUniformData:function(e,i){var t=new s.Color(this.color).getSource();t[0]=this.alpha*t[0]/255,t[1]=this.alpha*t[1]/255,t[2]=this.alpha*t[2]/255,t[3]=this.alpha,e.uniform4fv(i.uColor,t)},toObject:function(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}),s.Image.filters.BlendColor.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric,o=s.Image.filters,a=s.util.createClass;o.BlendImage=a(o.BaseFilter,{type:"BlendImage",image:null,mode:"multiply",alpha:1,vertexSource:`attribute vec2 aPosition;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
uniform mat3 uTransformMatrix;
void main() {
vTexCoord = aPosition;
vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;
gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
}`,fragmentSource:{multiply:`precision highp float;
uniform sampler2D uTexture;
uniform sampler2D uImage;
uniform vec4 uColor;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec4 color2 = texture2D(uImage, vTexCoord2);
color.rgba *= color2.rgba;
gl_FragColor = color;
}`,mask:`precision highp float;
uniform sampler2D uTexture;
uniform sampler2D uImage;
uniform vec4 uColor;
varying vec2 vTexCoord;
varying vec2 vTexCoord2;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec4 color2 = texture2D(uImage, vTexCoord2);
color.a = color2.a;
gl_FragColor = color;
}`},retrieveShader:function(e){var i=this.type+"_"+this.mode,t=this.fragmentSource[this.mode];return e.programCache.hasOwnProperty(i)||(e.programCache[i]=this.createProgram(e.context,t)),e.programCache[i]},applyToWebGL:function(e){var i=e.context,t=this.createTexture(e.filterBackend,this.image);this.bindAdditionalTexture(i,t,i.TEXTURE1),this.callSuper("applyToWebGL",e),this.unbindAdditionalTexture(i,i.TEXTURE1)},createTexture:function(e,i){return e.getCachedTexture(i.cacheKey,i._element)},calculateMatrix:function(){var e=this.image,i=e._element.width,t=e._element.height;return[1/e.scaleX,0,0,0,1/e.scaleY,0,-e.left/i,-e.top/t,1]},applyTo2d:function(e){var i=e.imageData,t=e.filterBackend.resources,n=i.data,h=n.length,r=i.width,l=i.height,u,d,g,m,v,y,T,M,W,H,G=this.image,V;t.blendImage||(t.blendImage=s.util.createCanvasElement()),W=t.blendImage,H=W.getContext("2d"),W.width!==r||W.height!==l?(W.width=r,W.height=l):H.clearRect(0,0,r,l),H.setTransform(G.scaleX,0,0,G.scaleY,G.left,G.top),H.drawImage(G._element,0,0,r,l),V=H.getImageData(0,0,r,l).data;for(var U=0;U<h;U+=4)switch(v=n[U],y=n[U+1],T=n[U+2],M=n[U+3],u=V[U],d=V[U+1],g=V[U+2],m=V[U+3],this.mode){case"multiply":n[U]=v*u/255,n[U+1]=y*d/255,n[U+2]=T*g/255,n[U+3]=M*m/255;break;case"mask":n[U+3]=m;break}},getUniformLocations:function(e,i){return{uTransformMatrix:e.getUniformLocation(i,"uTransformMatrix"),uImage:e.getUniformLocation(i,"uImage")}},sendUniformData:function(e,i){var t=this.calculateMatrix();e.uniform1i(i.uImage,1),e.uniformMatrix3fv(i.uTransformMatrix,!1,t)},toObject:function(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}}),s.Image.filters.BlendImage.fromObject=function(e,i){s.Image.fromObject(e.image,function(t){var n=s.util.object.clone(e);n.image=t,i(new s.Image.filters.BlendImage(n))})}}(et),function(c){var s=c.fabric||(c.fabric={}),o=Math.pow,a=Math.floor,e=Math.sqrt,i=Math.abs,t=Math.round,n=Math.sin,h=Math.ceil,r=s.Image.filters,l=s.util.createClass;r.Resize=l(r.BaseFilter,{type:"Resize",resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,getUniformLocations:function(u,d){return{uDelta:u.getUniformLocation(d,"uDelta"),uTaps:u.getUniformLocation(d,"uTaps")}},sendUniformData:function(u,d){u.uniform2fv(d.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),u.uniform1fv(d.uTaps,this.taps)},retrieveShader:function(u){var d=this.getFilterWindow(),g=this.type+"_"+d;if(!u.programCache.hasOwnProperty(g)){var m=this.generateShader(d);u.programCache[g]=this.createProgram(u.context,m)}return u.programCache[g]},getFilterWindow:function(){var u=this.tempScale;return Math.ceil(this.lanczosLobes/u)},getTaps:function(){for(var u=this.lanczosCreate(this.lanczosLobes),d=this.tempScale,g=this.getFilterWindow(),m=new Array(g),v=1;v<=g;v++)m[v-1]=u(v*d);return m},generateShader:function(m){for(var d=new Array(m),g=this.fragmentSourceTOP,m,v=1;v<=m;v++)d[v-1]=v+".0 * uDelta";return g+="uniform float uTaps["+m+`];
`,g+=`void main() {
`,g+=`  vec4 color = texture2D(uTexture, vTexCoord);
`,g+=`  float sum = 1.0;
`,d.forEach(function(y,T){g+="  color += texture2D(uTexture, vTexCoord + "+y+") * uTaps["+T+`];
`,g+="  color += texture2D(uTexture, vTexCoord - "+y+") * uTaps["+T+`];
`,g+="  sum += 2.0 * uTaps["+T+`];
`}),g+=`  gl_FragColor = color / sum;
`,g+="}",g},fragmentSourceTOP:`precision highp float;
uniform sampler2D uTexture;
uniform vec2 uDelta;
varying vec2 vTexCoord;
`,applyTo:function(u){u.webgl?(u.passes++,this.width=u.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=u.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),u.destinationWidth=this.dW,this._setupFrameBuffer(u),this.applyToWebGL(u),this._swapTextures(u),u.sourceWidth=u.destinationWidth,this.height=u.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),u.destinationHeight=this.dH,this._setupFrameBuffer(u),this.applyToWebGL(u),this._swapTextures(u),u.sourceHeight=u.destinationHeight):this.applyTo2d(u)},isNeutralState:function(){return this.scaleX===1&&this.scaleY===1},lanczosCreate:function(u){return function(d){if(d>=u||d<=-u)return 0;if(d<11920929e-14&&d>-11920929e-14)return 1;d*=Math.PI;var g=d/u;return n(d)/d*n(g)/g}},applyTo2d:function(u){var d=u.imageData,g=this.scaleX,m=this.scaleY;this.rcpScaleX=1/g,this.rcpScaleY=1/m;var v=d.width,y=d.height,T=t(v*g),M=t(y*m),W;this.resizeType==="sliceHack"?W=this.sliceByTwo(u,v,y,T,M):this.resizeType==="hermite"?W=this.hermiteFastResize(u,v,y,T,M):this.resizeType==="bilinear"?W=this.bilinearFiltering(u,v,y,T,M):this.resizeType==="lanczos"&&(W=this.lanczosResize(u,v,y,T,M)),u.imageData=W},sliceByTwo:function(u,d,g,m,v){var y=u.imageData,T=.5,M=!1,W=!1,H=d*T,G=g*T,V=s.filterBackend.resources,U,N,Q=0,$=0,K=d,S=0;for(V.sliceByTwo||(V.sliceByTwo=document.createElement("canvas")),U=V.sliceByTwo,(U.width<d*1.5||U.height<g)&&(U.width=d*1.5,U.height=g),N=U.getContext("2d"),N.clearRect(0,0,d*1.5,g),N.putImageData(y,0,0),m=a(m),v=a(v);!M||!W;)d=H,g=G,m<a(H*T)?H=a(H*T):(H=m,M=!0),v<a(G*T)?G=a(G*T):(G=v,W=!0),N.drawImage(U,Q,$,d,g,K,S,H,G),Q=K,$=S,S+=G;return N.getImageData(Q,$,m,v)},lanczosResize:function(u,d,g,m,v){function y(b){var w,P,p,_,x,C,A,E,R,j,Y;for(S.x=(b+.5)*G,F.x=a(S.x),w=0;w<v;w++){for(S.y=(w+.5)*V,F.y=a(S.y),x=0,C=0,A=0,E=0,R=0,P=F.x-Q;P<=F.x+Q;P++)if(!(P<0||P>=d)){j=a(1e3*i(P-S.x)),K[j]||(K[j]={});for(var D=F.y-$;D<=F.y+$;D++)D<0||D>=g||(Y=a(1e3*i(D-S.y)),K[j][Y]||(K[j][Y]=H(e(o(j*U,2)+o(Y*N,2))/1e3)),p=K[j][Y],p>0&&(_=(D*d+P)*4,x+=p,C+=p*T[_],A+=p*T[_+1],E+=p*T[_+2],R+=p*T[_+3]))}_=(w*m+b)*4,W[_]=C/x,W[_+1]=A/x,W[_+2]=E/x,W[_+3]=R/x}return++b<m?y(b):M}var T=u.imageData.data,M=u.ctx.createImageData(m,v),W=M.data,H=this.lanczosCreate(this.lanczosLobes),G=this.rcpScaleX,V=this.rcpScaleY,U=2/this.rcpScaleX,N=2/this.rcpScaleY,Q=h(G*this.lanczosLobes/2),$=h(V*this.lanczosLobes/2),K={},S={},F={};return y(0)},bilinearFiltering:function(u,d,g,m,v){var y,T,M,W,H,G,V,U,N,Q,$,K,S=0,F,b=this.rcpScaleX,w=this.rcpScaleY,P=4*(d-1),p=u.imageData,_=p.data,x=u.ctx.createImageData(m,v),C=x.data;for(V=0;V<v;V++)for(U=0;U<m;U++)for(H=a(b*U),G=a(w*V),N=b*U-H,Q=w*V-G,F=4*(G*d+H),$=0;$<4;$++)y=_[F+$],T=_[F+4+$],M=_[F+P+$],W=_[F+P+4+$],K=y*(1-N)*(1-Q)+T*N*(1-Q)+M*Q*(1-N)+W*N*Q,C[S++]=K;return x},hermiteFastResize:function(u,d,g,m,v){for(var y=this.rcpScaleX,T=this.rcpScaleY,M=h(y/2),W=h(T/2),H=u.imageData,G=H.data,V=u.ctx.createImageData(m,v),U=V.data,N=0;N<v;N++)for(var Q=0;Q<m;Q++){for(var $=(Q+N*m)*4,K=0,S=0,F=0,b=0,w=0,P=0,p=0,_=(N+.5)*T,x=a(N*T);x<(N+1)*T;x++)for(var C=i(_-(x+.5))/W,A=(Q+.5)*y,E=C*C,R=a(Q*y);R<(Q+1)*y;R++){var j=i(A-(R+.5))/M,Y=e(E+j*j);Y>1&&Y<-1||(K=2*Y*Y*Y-3*Y*Y+1,K>0&&(j=4*(R+x*d),p+=K*G[j+3],F+=K,G[j+3]<255&&(K=K*G[j+3]/250),b+=K*G[j],w+=K*G[j+1],P+=K*G[j+2],S+=K))}U[$]=b/S,U[$+1]=w/S,U[$+2]=P/S,U[$+3]=p/F}return V},toObject:function(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}),s.Image.filters.Resize.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Contrast=a(o.BaseFilter,{type:"Contrast",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uContrast;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));
color.rgb = contrastF * (color.rgb - 0.5) + 0.5;
gl_FragColor = color;
}`,contrast:0,mainParameter:"contrast",applyTo2d:function(e){if(this.contrast!==0){var i=e.imageData,t,h,n=i.data,h=n.length,r=Math.floor(this.contrast*255),l=259*(r+255)/(255*(259-r));for(t=0;t<h;t+=4)n[t]=l*(n[t]-128)+128,n[t+1]=l*(n[t+1]-128)+128,n[t+2]=l*(n[t+2]-128)+128}},getUniformLocations:function(e,i){return{uContrast:e.getUniformLocation(i,"uContrast")}},sendUniformData:function(e,i){e.uniform1f(i.uContrast,this.contrast)}}),s.Image.filters.Contrast.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Saturation=a(o.BaseFilter,{type:"Saturation",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uSaturation;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float rgMax = max(color.r, color.g);
float rgbMax = max(rgMax, color.b);
color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;
color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;
color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;
gl_FragColor = color;
}`,saturation:0,mainParameter:"saturation",applyTo2d:function(e){if(this.saturation!==0){var i=e.imageData,t=i.data,n=t.length,h=-this.saturation,r,l;for(r=0;r<n;r+=4)l=Math.max(t[r],t[r+1],t[r+2]),t[r]+=l!==t[r]?(l-t[r])*h:0,t[r+1]+=l!==t[r+1]?(l-t[r+1])*h:0,t[r+2]+=l!==t[r+2]?(l-t[r+2])*h:0}},getUniformLocations:function(e,i){return{uSaturation:e.getUniformLocation(i,"uSaturation")}},sendUniformData:function(e,i){e.uniform1f(i.uSaturation,-this.saturation)}}),s.Image.filters.Saturation.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Vibrance=a(o.BaseFilter,{type:"Vibrance",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform float uVibrance;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
float max = max(color.r, max(color.g, color.b));
float avg = (color.r + color.g + color.b) / 3.0;
float amt = (abs(max - avg) * 2.0) * uVibrance;
color.r += max != color.r ? (max - color.r) * amt : 0.00;
color.g += max != color.g ? (max - color.g) * amt : 0.00;
color.b += max != color.b ? (max - color.b) * amt : 0.00;
gl_FragColor = color;
}`,vibrance:0,mainParameter:"vibrance",applyTo2d:function(e){if(this.vibrance!==0){var i=e.imageData,t=i.data,n=t.length,h=-this.vibrance,r,l,u,d;for(r=0;r<n;r+=4)l=Math.max(t[r],t[r+1],t[r+2]),u=(t[r]+t[r+1]+t[r+2])/3,d=Math.abs(l-u)*2/255*h,t[r]+=l!==t[r]?(l-t[r])*d:0,t[r+1]+=l!==t[r+1]?(l-t[r+1])*d:0,t[r+2]+=l!==t[r+2]?(l-t[r+2])*d:0}},getUniformLocations:function(e,i){return{uVibrance:e.getUniformLocation(i,"uVibrance")}},sendUniformData:function(e,i){e.uniform1f(i.uVibrance,-this.vibrance)}}),s.Image.filters.Vibrance.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Blur=a(o.BaseFilter,{type:"Blur",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec2 uDelta;
varying vec2 vTexCoord;
const float nSamples = 15.0;
vec3 v3offset = vec3(12.9898, 78.233, 151.7182);
float random(vec3 scale) {
return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);
}
void main() {
vec4 color = vec4(0.0);
float total = 0.0;
float offset = random(v3offset);
for (float t = -nSamples; t <= nSamples; t++) {
float percent = (t + offset - 0.5) / nSamples;
float weight = 1.0 - abs(percent);
color += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;
total += weight;
}
gl_FragColor = color / total;
}`,blur:0,mainParameter:"blur",applyTo:function(e){e.webgl?(this.aspectRatio=e.sourceWidth/e.sourceHeight,e.passes++,this._setupFrameBuffer(e),this.horizontal=!0,this.applyToWebGL(e),this._swapTextures(e),this._setupFrameBuffer(e),this.horizontal=!1,this.applyToWebGL(e),this._swapTextures(e)):this.applyTo2d(e)},applyTo2d:function(e){e.imageData=this.simpleBlur(e)},simpleBlur:function(e){var i=e.filterBackend.resources,t,n,h=e.imageData.width,r=e.imageData.height;i.blurLayer1||(i.blurLayer1=s.util.createCanvasElement(),i.blurLayer2=s.util.createCanvasElement()),t=i.blurLayer1,n=i.blurLayer2,(t.width!==h||t.height!==r)&&(n.width=t.width=h,n.height=t.height=r);var l=t.getContext("2d"),u=n.getContext("2d"),d=15,g,m,v,y,T=this.blur*.06*.5;for(l.putImageData(e.imageData,0,0),u.clearRect(0,0,h,r),y=-d;y<=d;y++)g=(Math.random()-.5)/4,m=y/d,v=T*m*h+g,u.globalAlpha=1-Math.abs(m),u.drawImage(t,v,g),l.drawImage(n,0,0),u.globalAlpha=1,u.clearRect(0,0,n.width,n.height);for(y=-d;y<=d;y++)g=(Math.random()-.5)/4,m=y/d,v=T*m*r+g,u.globalAlpha=1-Math.abs(m),u.drawImage(t,g,v),l.drawImage(n,0,0),u.globalAlpha=1,u.clearRect(0,0,n.width,n.height);e.ctx.drawImage(t,0,0);var M=e.ctx.getImageData(0,0,t.width,t.height);return l.globalAlpha=1,l.clearRect(0,0,t.width,t.height),M},getUniformLocations:function(e,i){return{delta:e.getUniformLocation(i,"uDelta")}},sendUniformData:function(e,i){var t=this.chooseRightDelta();e.uniform2fv(i.delta,t)},chooseRightDelta:function(){var e=1,i=[0,0],t;return this.horizontal?this.aspectRatio>1&&(e=1/this.aspectRatio):this.aspectRatio<1&&(e=this.aspectRatio),t=e*this.blur*.12,this.horizontal?i[0]=t:i[1]=t,i}}),o.Blur.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Gamma=a(o.BaseFilter,{type:"Gamma",fragmentSource:`precision highp float;
uniform sampler2D uTexture;
uniform vec3 uGamma;
varying vec2 vTexCoord;
void main() {
vec4 color = texture2D(uTexture, vTexCoord);
vec3 correction = (1.0 / uGamma);
color.r = pow(color.r, correction.r);
color.g = pow(color.g, correction.g);
color.b = pow(color.b, correction.b);
gl_FragColor = color;
gl_FragColor.rgb *= color.a;
}`,gamma:[1,1,1],mainParameter:"gamma",initialize:function(e){this.gamma=[1,1,1],o.BaseFilter.prototype.initialize.call(this,e)},applyTo2d:function(e){var i=e.imageData,t=i.data,n=this.gamma,h=t.length,r=1/n[0],l=1/n[1],u=1/n[2],d;for(this.rVals||(this.rVals=new Uint8Array(256),this.gVals=new Uint8Array(256),this.bVals=new Uint8Array(256)),d=0,h=256;d<h;d++)this.rVals[d]=Math.pow(d/255,r)*255,this.gVals[d]=Math.pow(d/255,l)*255,this.bVals[d]=Math.pow(d/255,u)*255;for(d=0,h=t.length;d<h;d+=4)t[d]=this.rVals[t[d]],t[d+1]=this.gVals[t[d+1]],t[d+2]=this.bVals[t[d+2]]},getUniformLocations:function(e,i){return{uGamma:e.getUniformLocation(i,"uGamma")}},sendUniformData:function(e,i){e.uniform3fv(i.uGamma,this.gamma)}}),s.Image.filters.Gamma.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.Composed=a(o.BaseFilter,{type:"Composed",subFilters:[],initialize:function(e){this.callSuper("initialize",e),this.subFilters=this.subFilters.slice(0)},applyTo:function(e){e.passes+=this.subFilters.length-1,this.subFilters.forEach(function(i){i.applyTo(e)})},toObject:function(){return s.util.object.extend(this.callSuper("toObject"),{subFilters:this.subFilters.map(function(e){return e.toObject()})})},isNeutralState:function(){return!this.subFilters.some(function(e){return!e.isNeutralState()})}}),s.Image.filters.Composed.fromObject=function(e,i){var t=e.subFilters||[],n=t.map(function(r){return new s.Image.filters[r.type](r)}),h=new s.Image.filters.Composed({subFilters:n});return i&&i(h),h}}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.Image.filters,a=s.util.createClass;o.HueRotation=a(o.ColorMatrix,{type:"HueRotation",rotation:0,mainParameter:"rotation",calculateMatrix:function(){var e=this.rotation*Math.PI,i=s.util.cos(e),t=s.util.sin(e),n=1/3,h=Math.sqrt(n)*t,r=1-i;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=i+r/3,this.matrix[1]=n*r-h,this.matrix[2]=n*r+h,this.matrix[5]=n*r+h,this.matrix[6]=i+n*r,this.matrix[7]=n*r-h,this.matrix[10]=n*r-h,this.matrix[11]=n*r+h,this.matrix[12]=i+n*r},isNeutralState:function(e){return this.calculateMatrix(),o.BaseFilter.prototype.isNeutralState.call(this,e)},applyTo:function(e){this.calculateMatrix(),o.BaseFilter.prototype.applyTo.call(this,e)}}),s.Image.filters.HueRotation.fromObject=s.Image.filters.BaseFilter.fromObject}(et),function(c){var s=c.fabric||(c.fabric={}),o=s.util.object.clone;if(s.Text){s.warn("fabric.Text is already defined");return}var a="fontFamily fontWeight fontSize text underline overline linethrough textAlign fontStyle lineHeight textBackgroundColor charSpacing styles direction path pathStartOffset pathSide".split(" ");s.Text=s.util.createClass(s.Object,{_dimensionAffectingProps:["fontSize","fontWeight","fontFamily","fontStyle","lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide"],_reNewline:/\r?\n/,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:"left",fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stateProperties:s.Object.prototype.stateProperties.concat(a),cacheProperties:s.Object.prototype.cacheProperties.concat(a),stroke:null,shadow:null,path:null,pathStartOffset:0,pathSide:"left",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,styles:null,_measuringContext:null,deltaY:0,direction:"ltr",_styleProperties:["stroke","strokeWidth","fill","fontFamily","fontSize","fontWeight","fontStyle","underline","overline","linethrough","deltaY","textBackgroundColor"],__charBounds:[],CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,initialize:function(e,i){this.styles=i?i.styles||{}:{},this.text=e,this.__skipDimension=!0,this.callSuper("initialize",i),this.path&&this.setPathInfo(),this.__skipDimension=!1,this.initDimensions(),this.setCoords(),this.setupState({propertySet:"_dimensionAffectingProps"})},setPathInfo:function(){var e=this.path;e&&(e.segmentsInfo=s.util.getPathSegmentsInfo(e.path))},getMeasuringContext:function(){return s._measuringContext||(s._measuringContext=this.canvas&&this.canvas.contextCache||s.util.createCanvasElement().getContext("2d")),s._measuringContext},_splitText:function(){var e=this._splitTextIntoLines(this.text);return this.textLines=e.lines,this._textLines=e.graphemeLines,this._unwrappedTextLines=e._unwrappedLines,this._text=e.graphemeText,e},initDimensions:function(){this.__skipDimension||(this._splitText(),this._clearCache(),this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.indexOf("justify")!==-1&&this.enlargeSpaces(),this.saveState({propertySet:"_dimensionAffectingProps"}))},enlargeSpaces:function(){for(var e,i,t,n,h,r,l,u=0,d=this._textLines.length;u<d;u++)if(!(this.textAlign!=="justify"&&(u===d-1||this.isEndOfWrapping(u)))&&(n=0,h=this._textLines[u],i=this.getLineWidth(u),i<this.width&&(l=this.textLines[u].match(this._reSpacesAndTabs)))){t=l.length,e=(this.width-i)/t;for(var g=0,m=h.length;g<=m;g++)r=this.__charBounds[u][g],this._reSpaceAndTab.test(h[g])?(r.width+=e,r.kernedWidth+=e,r.left+=n,n+=e):r.left+=n}},isEndOfWrapping:function(e){return e===this._textLines.length-1},missingNewlineOffset:function(){return 1},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_getCacheCanvasDimensions:function(){var e=this.callSuper("_getCacheCanvasDimensions"),i=this.fontSize;return e.width+=i*e.zoomX,e.height+=i*e.zoomY,e},_render:function(e){var i=this.path;i&&!i.isNotVisible()&&i._render(e),this._setTextStyles(e),this._renderTextLinesBackground(e),this._renderTextDecoration(e,"underline"),this._renderText(e),this._renderTextDecoration(e,"overline"),this._renderTextDecoration(e,"linethrough")},_renderText:function(e){this.paintFirst==="stroke"?(this._renderTextStroke(e),this._renderTextFill(e)):(this._renderTextFill(e),this._renderTextStroke(e))},_setTextStyles:function(e,i,t){e.textBaseline="alphabetic",e.font=this._getFontDeclaration(i,t)},calcTextWidth:function(){for(var e=this.getLineWidth(0),i=1,t=this._textLines.length;i<t;i++){var n=this.getLineWidth(i);n>e&&(e=n)}return e},_renderTextLine:function(e,i,t,n,h,r){this._renderChars(e,i,t,n,h,r)},_renderTextLinesBackground:function(e){if(!(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))){for(var i,t,n=e.fillStyle,h,r,l=this._getLeftOffset(),u=this._getTopOffset(),d=0,g=0,m,v,y=this.path,T,M=0,W=this._textLines.length;M<W;M++){if(i=this.getHeightOfLine(M),!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",M)){u+=i;continue}h=this._textLines[M],t=this._getLineLeftOffset(M),g=0,d=0,r=this.getValueOfPropertyAt(M,0,"textBackgroundColor");for(var H=0,G=h.length;H<G;H++)m=this.__charBounds[M][H],v=this.getValueOfPropertyAt(M,H,"textBackgroundColor"),y?(e.save(),e.translate(m.renderLeft,m.renderTop),e.rotate(m.angle),e.fillStyle=v,v&&e.fillRect(-m.width/2,-i/this.lineHeight*(1-this._fontSizeFraction),m.width,i/this.lineHeight),e.restore()):v!==r?(T=l+t+d,this.direction==="rtl"&&(T=this.width-T-g),e.fillStyle=r,r&&e.fillRect(T,u,g,i/this.lineHeight),d=m.left,g=m.width,r=v):g+=m.kernedWidth;v&&!y&&(T=l+t+d,this.direction==="rtl"&&(T=this.width-T-g),e.fillStyle=v,e.fillRect(T,u,g,i/this.lineHeight)),u+=i}e.fillStyle=n,this._removeShadow(e)}},getFontCache:function(e){var i=e.fontFamily.toLowerCase();s.charWidthsCache[i]||(s.charWidthsCache[i]={});var t=s.charWidthsCache[i],n=e.fontStyle.toLowerCase()+"_"+(e.fontWeight+"").toLowerCase();return t[n]||(t[n]={}),t[n]},_measureChar:function(e,i,t,n){var h=this.getFontCache(i),r=this._getFontDeclaration(i),l=this._getFontDeclaration(n),u=t+e,d=r===l,g,m,v,y=i.fontSize/this.CACHE_FONT_SIZE,T;if(t&&h[t]!==void 0&&(v=h[t]),h[e]!==void 0&&(T=g=h[e]),d&&h[u]!==void 0&&(m=h[u],T=m-v),g===void 0||v===void 0||m===void 0){var M=this.getMeasuringContext();this._setTextStyles(M,i,!0)}return g===void 0&&(T=g=M.measureText(e).width,h[e]=g),v===void 0&&d&&t&&(v=M.measureText(t).width,h[t]=v),d&&m===void 0&&(m=M.measureText(u).width,h[u]=m,T=m-v),{width:g*y,kernedWidth:T*y}},getHeightOfChar:function(e,i){return this.getValueOfPropertyAt(e,i,"fontSize")},measureLine:function(e){var i=this._measureLine(e);return this.charSpacing!==0&&(i.width-=this._getWidthOfCharSpacing()),i.width<0&&(i.width=0),i},_measureLine:function(e){var i=0,t,n,h=this._textLines[e],r,l,u=0,d=new Array(h.length),g=0,m,v,y=this.path,T=this.pathSide==="right";for(this.__charBounds[e]=d,t=0;t<h.length;t++)n=h[t],l=this._getGraphemeBox(n,e,t,r),d[t]=l,i+=l.kernedWidth,r=n;if(d[t]={left:l?l.left+l.width:0,width:0,kernedWidth:0,height:this.fontSize},y){switch(v=y.segmentsInfo[y.segmentsInfo.length-1].length,m=s.util.getPointOnPath(y.path,0,y.segmentsInfo),m.x+=y.pathOffset.x,m.y+=y.pathOffset.y,this.textAlign){case"left":g=T?v-i:0;break;case"center":g=(v-i)/2;break;case"right":g=T?0:v-i;break}for(g+=this.pathStartOffset*(T?-1:1),t=T?h.length-1:0;T?t>=0:t<h.length;T?t--:t++)l=d[t],g>v?g%=v:g<0&&(g+=v),this._setGraphemeOnPath(g,l,m),g+=l.kernedWidth}return{width:i,numOfSpaces:u}},_setGraphemeOnPath:function(e,i,t){var n=e+i.kernedWidth/2,h=this.path,r=s.util.getPointOnPath(h.path,n,h.segmentsInfo);i.renderLeft=r.x-t.x,i.renderTop=r.y-t.y,i.angle=r.angle+(this.pathSide==="right"?Math.PI:0)},_getGraphemeBox:function(e,i,t,n,h){var r=this.getCompleteStyleDeclaration(i,t),l=n?this.getCompleteStyleDeclaration(i,t-1):{},u=this._measureChar(e,r,n,l),d=u.kernedWidth,g=u.width,m;this.charSpacing!==0&&(m=this._getWidthOfCharSpacing(),g+=m,d+=m);var v={width:g,left:0,height:r.fontSize,kernedWidth:d,deltaY:r.deltaY};if(t>0&&!h){var y=this.__charBounds[i][t-1];v.left=y.left+y.width+u.kernedWidth-u.width}return v},getHeightOfLine:function(e){if(this.__lineHeights[e])return this.__lineHeights[e];for(var i=this._textLines[e],t=this.getHeightOfChar(e,0),n=1,h=i.length;n<h;n++)t=Math.max(this.getHeightOfChar(e,n),t);return this.__lineHeights[e]=t*this.lineHeight*this._fontSizeMult},calcTextHeight:function(){for(var e,i=0,t=0,n=this._textLines.length;t<n;t++)e=this.getHeightOfLine(t),i+=t===n-1?e/this.lineHeight:e;return i},_getLeftOffset:function(){return this.direction==="ltr"?-this.width/2:this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextCommon:function(e,i){e.save();for(var t=0,n=this._getLeftOffset(),h=this._getTopOffset(),r=0,l=this._textLines.length;r<l;r++){var u=this.getHeightOfLine(r),d=u/this.lineHeight,g=this._getLineLeftOffset(r);this._renderTextLine(i,e,this._textLines[r],n+g,h+t+d,r),t+=u}e.restore()},_renderTextFill:function(e){!this.fill&&!this.styleHas("fill")||this._renderTextCommon(e,"fillText")},_renderTextStroke:function(e){(!this.stroke||this.strokeWidth===0)&&this.isEmptyStyles()||(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(e),e.save(),this._setLineDash(e,this.strokeDashArray),e.beginPath(),this._renderTextCommon(e,"strokeText"),e.closePath(),e.restore())},_renderChars:function(e,i,t,n,h,r){var l=this.getHeightOfLine(r),u=this.textAlign.indexOf("justify")!==-1,d,g,m="",v,y=0,T,M=this.path,W=!u&&this.charSpacing===0&&this.isEmptyStyles(r)&&!M,H=this.direction==="ltr",G=this.direction==="ltr"?1:-1,V;if(i.save(),h-=l*this._fontSizeFraction/this.lineHeight,W){i.canvas.setAttribute("dir",H?"ltr":"rtl"),i.direction=H?"ltr":"rtl",i.textAlign=H?"left":"right",this._renderChar(e,i,r,0,t.join(""),n,h,l),i.restore();return}for(var U=0,N=t.length-1;U<=N;U++)T=U===N||this.charSpacing||M,m+=t[U],v=this.__charBounds[r][U],y===0?(n+=G*(v.kernedWidth-v.width),y+=v.width):y+=v.kernedWidth,u&&!T&&this._reSpaceAndTab.test(t[U])&&(T=!0),T||(d=d||this.getCompleteStyleDeclaration(r,U),g=this.getCompleteStyleDeclaration(r,U+1),T=this._hasStyleChanged(d,g)),T&&(M?(i.save(),i.translate(v.renderLeft,v.renderTop),i.rotate(v.angle),this._renderChar(e,i,r,U,m,-y/2,0,l),i.restore()):(V=n,i.canvas.setAttribute("dir",H?"ltr":"rtl"),i.direction=H?"ltr":"rtl",i.textAlign=H?"left":"right",this._renderChar(e,i,r,U,m,V,h,l)),m="",d=g,n+=G*y,y=0);i.restore()},_applyPatternGradientTransformText:function(e){var i=s.util.createCanvasElement(),t,n=this.width+this.strokeWidth,h=this.height+this.strokeWidth;return i.width=n,i.height=h,t=i.getContext("2d"),t.beginPath(),t.moveTo(0,0),t.lineTo(n,0),t.lineTo(n,h),t.lineTo(0,h),t.closePath(),t.translate(n/2,h/2),t.fillStyle=e.toLive(t),this._applyPatternGradientTransform(t,e),t.fill(),t.createPattern(i,"no-repeat")},handleFiller:function(e,i,t){var n,h;return t.toLive?t.gradientUnits==="percentage"||t.gradientTransform||t.patternTransform?(n=-this.width/2,h=-this.height/2,e.translate(n,h),e[i]=this._applyPatternGradientTransformText(t),{offsetX:n,offsetY:h}):(e[i]=t.toLive(e,this),this._applyPatternGradientTransform(e,t)):(e[i]=t,{offsetX:0,offsetY:0})},_setStrokeStyles:function(e,i){return e.lineWidth=i.strokeWidth,e.lineCap=this.strokeLineCap,e.lineDashOffset=this.strokeDashOffset,e.lineJoin=this.strokeLineJoin,e.miterLimit=this.strokeMiterLimit,this.handleFiller(e,"strokeStyle",i.stroke)},_setFillStyles:function(e,i){return this.handleFiller(e,"fillStyle",i.fill)},_renderChar:function(e,i,t,n,h,r,l){var u=this._getStyleDeclaration(t,n),d=this.getCompleteStyleDeclaration(t,n),g=e==="fillText"&&d.fill,m=e==="strokeText"&&d.stroke&&d.strokeWidth,v,y;!m&&!g||(i.save(),g&&(v=this._setFillStyles(i,d)),m&&(y=this._setStrokeStyles(i,d)),i.font=this._getFontDeclaration(d),u&&u.textBackgroundColor&&this._removeShadow(i),u&&u.deltaY&&(l+=u.deltaY),g&&i.fillText(h,r-v.offsetX,l-v.offsetY),m&&i.strokeText(h,r-y.offsetX,l-y.offsetY),i.restore())},setSuperscript:function(e,i){return this._setScript(e,i,this.superscript)},setSubscript:function(e,i){return this._setScript(e,i,this.subscript)},_setScript:function(e,i,t){var n=this.get2DCursorLocation(e,!0),h=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"fontSize"),r=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"deltaY"),l={fontSize:h*t.size,deltaY:r+h*t.baseline};return this.setSelectionStyles(l,e,i),this},_hasStyleChanged:function(e,i){return e.fill!==i.fill||e.stroke!==i.stroke||e.strokeWidth!==i.strokeWidth||e.fontSize!==i.fontSize||e.fontFamily!==i.fontFamily||e.fontWeight!==i.fontWeight||e.fontStyle!==i.fontStyle||e.deltaY!==i.deltaY},_hasStyleChangedForSvg:function(e,i){return this._hasStyleChanged(e,i)||e.overline!==i.overline||e.underline!==i.underline||e.linethrough!==i.linethrough},_getLineLeftOffset:function(e){var i=this.getLineWidth(e),t=this.width-i,n=this.textAlign,h=this.direction,l,r=0,l=this.isEndOfWrapping(e);return n==="justify"||n==="justify-center"&&!l||n==="justify-right"&&!l||n==="justify-left"&&!l?0:(n==="center"&&(r=t/2),n==="right"&&(r=t),n==="justify-center"&&(r=t/2),n==="justify-right"&&(r=t),h==="rtl"&&(r-=t),r)},_clearCache:function(){this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]},_shouldClearDimensionCache:function(){var e=this._forceClearCache;return e||(e=this.hasStateChanged("_dimensionAffectingProps")),e&&(this.dirty=!0,this._forceClearCache=!1),e},getLineWidth:function(e){if(this.__lineWidths[e])return this.__lineWidths[e];var i,t=this._textLines[e],n;return t===""?i=0:(n=this.measureLine(e),i=n.width),this.__lineWidths[e]=i,i},_getWidthOfCharSpacing:function(){return this.charSpacing!==0?this.fontSize*this.charSpacing/1e3:0},getValueOfPropertyAt:function(e,i,t){var n=this._getStyleDeclaration(e,i);return n&&typeof n[t]!="undefined"?n[t]:this[t]},_renderTextDecoration:function(e,i){if(!(!this[i]&&!this.styleHas(i))){for(var t,n,h,r,l,u,d,g,m=this._getLeftOffset(),v=this._getTopOffset(),y,T,M,W,H,G,V,U,N=this.path,Q=this._getWidthOfCharSpacing(),$=this.offsets[i],K=0,S=this._textLines.length;K<S;K++){if(t=this.getHeightOfLine(K),!this[i]&&!this.styleHas(i,K)){v+=t;continue}d=this._textLines[K],G=t/this.lineHeight,r=this._getLineLeftOffset(K),T=0,M=0,g=this.getValueOfPropertyAt(K,0,i),U=this.getValueOfPropertyAt(K,0,"fill"),y=v+G*(1-this._fontSizeFraction),n=this.getHeightOfChar(K,0),l=this.getValueOfPropertyAt(K,0,"deltaY");for(var F=0,b=d.length;F<b;F++)if(W=this.__charBounds[K][F],H=this.getValueOfPropertyAt(K,F,i),V=this.getValueOfPropertyAt(K,F,"fill"),h=this.getHeightOfChar(K,F),u=this.getValueOfPropertyAt(K,F,"deltaY"),N&&H&&V)e.save(),e.fillStyle=U,e.translate(W.renderLeft,W.renderTop),e.rotate(W.angle),e.fillRect(-W.kernedWidth/2,$*h+u,W.kernedWidth,this.fontSize/15),e.restore();else if((H!==g||V!==U||h!==n||u!==l)&&M>0){var w=m+r+T;this.direction==="rtl"&&(w=this.width-w-M),g&&U&&(e.fillStyle=U,e.fillRect(w,y+$*n+l,M,this.fontSize/15)),T=W.left,M=W.width,g=H,U=V,n=h,l=u}else M+=W.kernedWidth;var w=m+r+T;this.direction==="rtl"&&(w=this.width-w-M),e.fillStyle=V,H&&V&&e.fillRect(w,y+$*n+l,M-Q,this.fontSize/15),v+=t}this._removeShadow(e)}},_getFontDeclaration:function(e,i){var t=e||this,n=this.fontFamily,h=s.Text.genericFonts.indexOf(n.toLowerCase())>-1,r=n===void 0||n.indexOf("'")>-1||n.indexOf(",")>-1||n.indexOf('"')>-1||h?t.fontFamily:'"'+t.fontFamily+'"';return[s.isLikelyNode?t.fontWeight:t.fontStyle,s.isLikelyNode?t.fontStyle:t.fontWeight,i?this.CACHE_FONT_SIZE+"px":t.fontSize+"px",r].join(" ")},render:function(e){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._shouldClearDimensionCache()&&this.initDimensions(),this.callSuper("render",e)))},_splitTextIntoLines:function(e){for(var i=e.split(this._reNewline),t=new Array(i.length),n=[`
`],h=[],r=0;r<i.length;r++)t[r]=s.util.string.graphemeSplit(i[r]),h=h.concat(t[r],n);return h.pop(),{_unwrappedLines:t,lines:i,graphemeText:h,graphemeLines:t}},toObject:function(e){var i=a.concat(e),t=this.callSuper("toObject",i);return t.styles=o(this.styles,!0),t.path&&(t.path=this.path.toObject()),t},set:function(e,i){this.callSuper("set",e,i);var t=!1,n=!1;if(typeof e=="object")for(var h in e)h==="path"&&this.setPathInfo(),t=t||this._dimensionAffectingProps.indexOf(h)!==-1,n=n||h==="path";else t=this._dimensionAffectingProps.indexOf(e)!==-1,n=e==="path";return n&&this.setPathInfo(),t&&(this.initDimensions(),this.setCoords()),this},complexity:function(){return 1}}),s.Text.ATTRIBUTE_NAMES=s.SHARED_ATTRIBUTES.concat("x y dx dy font-family font-style font-weight font-size letter-spacing text-decoration text-anchor".split(" ")),s.Text.DEFAULT_SVG_FONT_SIZE=16,s.Text.fromElement=function(e,i,t){if(!e)return i(null);var n=s.parseAttributes(e,s.Text.ATTRIBUTE_NAMES),h=n.textAnchor||"left";if(t=s.util.object.extend(t?o(t):{},n),t.top=t.top||0,t.left=t.left||0,n.textDecoration){var r=n.textDecoration;r.indexOf("underline")!==-1&&(t.underline=!0),r.indexOf("overline")!==-1&&(t.overline=!0),r.indexOf("line-through")!==-1&&(t.linethrough=!0),delete t.textDecoration}"dx"in n&&(t.left+=n.dx),"dy"in n&&(t.top+=n.dy),"fontSize"in t||(t.fontSize=s.Text.DEFAULT_SVG_FONT_SIZE);var l="";"textContent"in e?l=e.textContent:"firstChild"in e&&e.firstChild!==null&&"data"in e.firstChild&&e.firstChild.data!==null&&(l=e.firstChild.data),l=l.replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," ");var u=t.strokeWidth;t.strokeWidth=0;var d=new s.Text(l,t),g=d.getScaledHeight()/d.height,m=(d.height+d.strokeWidth)*d.lineHeight-d.height,v=m*g,y=d.getScaledHeight()+v,T=0;h==="center"&&(T=d.getScaledWidth()/2),h==="right"&&(T=d.getScaledWidth()),d.set({left:d.left-T,top:d.top-(y-d.fontSize*(.07+d._fontSizeFraction))/d.lineHeight,strokeWidth:typeof u!="undefined"?u:1}),i(d)},s.Text.fromObject=function(e,i){var t=o(e),n=e.path;return delete t.path,s.Object._fromObject("Text",t,function(h){n?s.Object._fromObject("Path",n,function(r){h.set("path",r),i(h)},"path"):i(h)},"text")},s.Text.genericFonts=["sans-serif","serif","cursive","fantasy","monospace"],s.util.createAccessors&&s.util.createAccessors(s.Text)}(et),function(){f.util.object.extend(f.Text.prototype,{isEmptyStyles:function(c){if(!this.styles||typeof c!="undefined"&&!this.styles[c])return!0;var s=typeof c=="undefined"?this.styles:{line:this.styles[c]};for(var o in s)for(var a in s[o])for(var e in s[o][a])return!1;return!0},styleHas:function(c,s){if(!this.styles||!c||c===""||typeof s!="undefined"&&!this.styles[s])return!1;var o=typeof s=="undefined"?this.styles:{0:this.styles[s]};for(var a in o)for(var e in o[a])if(typeof o[a][e][c]!="undefined")return!0;return!1},cleanStyle:function(c){if(!this.styles||!c||c==="")return!1;var s=this.styles,o=0,a,e,i=!0,t=0,n;for(var h in s){a=0;for(var r in s[h]){var n=s[h][r],l=n.hasOwnProperty(c);o++,l?(e?n[c]!==e&&(i=!1):e=n[c],n[c]===this[c]&&delete n[c]):i=!1,Object.keys(n).length!==0?a++:delete s[h][r]}a===0&&delete s[h]}for(var u=0;u<this._textLines.length;u++)t+=this._textLines[u].length;i&&o===t&&(this[c]=e,this.removeStyle(c))},removeStyle:function(c){if(!(!this.styles||!c||c==="")){var s=this.styles,o,a,e;for(a in s){o=s[a];for(e in o)delete o[e][c],Object.keys(o[e]).length===0&&delete o[e];Object.keys(o).length===0&&delete s[a]}}},_extendStyles:function(c,s){var o=this.get2DCursorLocation(c);this._getLineStyle(o.lineIndex)||this._setLineStyle(o.lineIndex),this._getStyleDeclaration(o.lineIndex,o.charIndex)||this._setStyleDeclaration(o.lineIndex,o.charIndex,{}),f.util.object.extend(this._getStyleDeclaration(o.lineIndex,o.charIndex),s)},get2DCursorLocation:function(c,s){typeof c=="undefined"&&(c=this.selectionStart);for(var o=s?this._unwrappedTextLines:this._textLines,a=o.length,e=0;e<a;e++){if(c<=o[e].length)return{lineIndex:e,charIndex:c};c-=o[e].length+this.missingNewlineOffset(e)}return{lineIndex:e-1,charIndex:o[e-1].length<c?o[e-1].length:c}},getSelectionStyles:function(c,s,o){typeof c=="undefined"&&(c=this.selectionStart||0),typeof s=="undefined"&&(s=this.selectionEnd||c);for(var a=[],e=c;e<s;e++)a.push(this.getStyleAtPosition(e,o));return a},getStyleAtPosition:function(c,s){var o=this.get2DCursorLocation(c),a=s?this.getCompleteStyleDeclaration(o.lineIndex,o.charIndex):this._getStyleDeclaration(o.lineIndex,o.charIndex);return a||{}},setSelectionStyles:function(c,s,o){typeof s=="undefined"&&(s=this.selectionStart||0),typeof o=="undefined"&&(o=this.selectionEnd||s);for(var a=s;a<o;a++)this._extendStyles(a,c);return this._forceClearCache=!0,this},_getStyleDeclaration:function(c,s){var o=this.styles&&this.styles[c];return o?o[s]:null},getCompleteStyleDeclaration:function(c,s){for(var o=this._getStyleDeclaration(c,s)||{},a={},e,i=0;i<this._styleProperties.length;i++)e=this._styleProperties[i],a[e]=typeof o[e]=="undefined"?this[e]:o[e];return a},_setStyleDeclaration:function(c,s,o){this.styles[c][s]=o},_deleteStyleDeclaration:function(c,s){delete this.styles[c][s]},_getLineStyle:function(c){return!!this.styles[c]},_setLineStyle:function(c){this.styles[c]={}},_deleteLineStyle:function(c){delete this.styles[c]}})}(),function(){function c(s){s.textDecoration&&(s.textDecoration.indexOf("underline")>-1&&(s.underline=!0),s.textDecoration.indexOf("line-through")>-1&&(s.linethrough=!0),s.textDecoration.indexOf("overline")>-1&&(s.overline=!0),delete s.textDecoration)}f.IText=f.util.createClass(f.Text,f.Observable,{type:"i-text",selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,_reSpace:/\s|\n/,_currentCursorOpacity:0,_selectionDirection:null,_abortCursorAnimation:!1,__widthOfSpace:[],inCompositionMode:!1,initialize:function(s,o){this.callSuper("initialize",s,o),this.initBehavior()},setSelectionStart:function(s){s=Math.max(s,0),this._updateAndFire("selectionStart",s)},setSelectionEnd:function(s){s=Math.min(s,this.text.length),this._updateAndFire("selectionEnd",s)},_updateAndFire:function(s,o){this[s]!==o&&(this._fireSelectionChanged(),this[s]=o),this._updateTextarea()},_fireSelectionChanged:function(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})},initDimensions:function(){this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this.callSuper("initDimensions")},render:function(s){this.clearContextTop(),this.callSuper("render",s),this.cursorOffsetCache={},this.renderCursorOrSelection()},_render:function(s){this.callSuper("_render",s)},clearContextTop:function(s){if(!(!this.isEditing||!this.canvas||!this.canvas.contextTop)){var o=this.canvas.contextTop,a=this.canvas.viewportTransform;o.save(),o.transform(a[0],a[1],a[2],a[3],a[4],a[5]),this.transform(o),this._clearTextArea(o),s||o.restore()}},renderCursorOrSelection:function(){if(!(!this.isEditing||!this.canvas||!this.canvas.contextTop)){var s=this._getCursorBoundaries(),o=this.canvas.contextTop;this.clearContextTop(!0),this.selectionStart===this.selectionEnd?this.renderCursor(s,o):this.renderSelection(s,o),o.restore()}},_clearTextArea:function(s){var o=this.width+4,a=this.height+4;s.clearRect(-o/2,-a/2,o,a)},_getCursorBoundaries:function(s){typeof s=="undefined"&&(s=this.selectionStart);var o=this._getLeftOffset(),a=this._getTopOffset(),e=this._getCursorBoundariesOffsets(s);return{left:o,top:a,leftOffset:e.left,topOffset:e.top}},_getCursorBoundariesOffsets:function(s){if(this.cursorOffsetCache&&"top"in this.cursorOffsetCache)return this.cursorOffsetCache;var o,a,e,i=0,t=0,n,h=this.get2DCursorLocation(s);e=h.charIndex,a=h.lineIndex;for(var r=0;r<a;r++)i+=this.getHeightOfLine(r);o=this._getLineLeftOffset(a);var l=this.__charBounds[a][e];return l&&(t=l.left),this.charSpacing!==0&&e===this._textLines[a].length&&(t-=this._getWidthOfCharSpacing()),n={top:i,left:o+(t>0?t:0)},this.direction==="rtl"&&(n.left*=-1),this.cursorOffsetCache=n,this.cursorOffsetCache},renderCursor:function(s,o){var a=this.get2DCursorLocation(),e=a.lineIndex,i=a.charIndex>0?a.charIndex-1:0,t=this.getValueOfPropertyAt(e,i,"fontSize"),n=this.scaleX*this.canvas.getZoom(),h=this.cursorWidth/n,r=s.topOffset,l=this.getValueOfPropertyAt(e,i,"deltaY");r+=(1-this._fontSizeFraction)*this.getHeightOfLine(e)/this.lineHeight-t*(1-this._fontSizeFraction),this.inCompositionMode&&this.renderSelection(s,o),o.fillStyle=this.cursorColor||this.getValueOfPropertyAt(e,i,"fill"),o.globalAlpha=this.__isMousedown?1:this._currentCursorOpacity,o.fillRect(s.left+s.leftOffset-h/2,r+s.top+l,h,t)},renderSelection:function(s,o){for(var a=this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,e=this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd,i=this.textAlign.indexOf("justify")!==-1,t=this.get2DCursorLocation(a),n=this.get2DCursorLocation(e),h=t.lineIndex,r=n.lineIndex,l=t.charIndex<0?0:t.charIndex,u=n.charIndex<0?0:n.charIndex,d=h;d<=r;d++){var g=this._getLineLeftOffset(d)||0,m=this.getHeightOfLine(d),v=0,y=0,T=0;if(d===h&&(y=this.__charBounds[h][l].left),d>=h&&d<r)T=i&&!this.isEndOfWrapping(d)?this.width:this.getLineWidth(d)||5;else if(d===r)if(u===0)T=this.__charBounds[r][u].left;else{var M=this._getWidthOfCharSpacing();T=this.__charBounds[r][u-1].left+this.__charBounds[r][u-1].width-M}v=m,(this.lineHeight<1||d===r&&this.lineHeight>1)&&(m/=this.lineHeight);var W=s.left+g+y,H=T-y,G=m,V=0;this.inCompositionMode?(o.fillStyle=this.compositionColor||"black",G=1,V=m):o.fillStyle=this.selectionColor,this.direction==="rtl"&&(W=this.width-W-H),o.fillRect(W,s.top+s.topOffset+V,H,G),s.topOffset+=v}},getCurrentCharFontSize:function(){var s=this._getCurrentCharIndex();return this.getValueOfPropertyAt(s.l,s.c,"fontSize")},getCurrentCharColor:function(){var s=this._getCurrentCharIndex();return this.getValueOfPropertyAt(s.l,s.c,"fill")},_getCurrentCharIndex:function(){var s=this.get2DCursorLocation(this.selectionStart,!0),o=s.charIndex>0?s.charIndex-1:0;return{l:s.lineIndex,c:o}}}),f.IText.fromObject=function(s,o){if(c(s),s.styles)for(var a in s.styles)for(var e in s.styles[a])c(s.styles[a][e]);f.Object._fromObject("IText",s,o,"text")}}(),function(){var c=f.util.object.clone;f.util.object.extend(f.IText.prototype,{initBehavior:function(){this.initAddedHandler(),this.initRemovedHandler(),this.initCursorSelectionHandlers(),this.initDoubleClickSimulation(),this.mouseMoveHandler=this.mouseMoveHandler.bind(this)},onDeselect:function(){this.isEditing&&this.exitEditing(),this.selected=!1},initAddedHandler:function(){var s=this;this.on("added",function(){var o=s.canvas;o&&(o._hasITextHandlers||(o._hasITextHandlers=!0,s._initCanvasHandlers(o)),o._iTextInstances=o._iTextInstances||[],o._iTextInstances.push(s))})},initRemovedHandler:function(){var s=this;this.on("removed",function(){var o=s.canvas;o&&(o._iTextInstances=o._iTextInstances||[],f.util.removeFromArray(o._iTextInstances,s),o._iTextInstances.length===0&&(o._hasITextHandlers=!1,s._removeCanvasHandlers(o)))})},_initCanvasHandlers:function(s){s._mouseUpITextHandler=function(){s._iTextInstances&&s._iTextInstances.forEach(function(o){o.__isMousedown=!1})},s.on("mouse:up",s._mouseUpITextHandler)},_removeCanvasHandlers:function(s){s.off("mouse:up",s._mouseUpITextHandler)},_tick:function(){this._currentTickState=this._animateCursor(this,1,this.cursorDuration,"_onTickComplete")},_animateCursor:function(s,o,a,e){var i;return i={isAborted:!1,abort:function(){this.isAborted=!0}},s.animate("_currentCursorOpacity",o,{duration:a,onComplete:function(){i.isAborted||s[e]()},onChange:function(){s.canvas&&s.selectionStart===s.selectionEnd&&s.renderCursorOrSelection()},abort:function(){return i.isAborted}}),i},_onTickComplete:function(){var s=this;this._cursorTimeout1&&clearTimeout(this._cursorTimeout1),this._cursorTimeout1=setTimeout(function(){s._currentTickCompleteState=s._animateCursor(s,0,this.cursorDuration/2,"_tick")},100)},initDelayedCursor:function(s){var o=this,a=s?0:this.cursorDelay;this.abortCursorAnimation(),this._currentCursorOpacity=1,this._cursorTimeout2=setTimeout(function(){o._tick()},a)},abortCursorAnimation:function(){var s=this._currentTickState||this._currentTickCompleteState,o=this.canvas;this._currentTickState&&this._currentTickState.abort(),this._currentTickCompleteState&&this._currentTickCompleteState.abort(),clearTimeout(this._cursorTimeout1),clearTimeout(this._cursorTimeout2),this._currentCursorOpacity=0,s&&o&&o.clearContext(o.contextTop||o.contextContainer)},selectAll:function(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this},getSelectedText:function(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")},findWordBoundaryLeft:function(s){var o=0,a=s-1;if(this._reSpace.test(this._text[a]))for(;this._reSpace.test(this._text[a]);)o++,a--;for(;/\S/.test(this._text[a])&&a>-1;)o++,a--;return s-o},findWordBoundaryRight:function(s){var o=0,a=s;if(this._reSpace.test(this._text[a]))for(;this._reSpace.test(this._text[a]);)o++,a++;for(;/\S/.test(this._text[a])&&a<this._text.length;)o++,a++;return s+o},findLineBoundaryLeft:function(s){for(var o=0,a=s-1;!/\n/.test(this._text[a])&&a>-1;)o++,a--;return s-o},findLineBoundaryRight:function(s){for(var o=0,a=s;!/\n/.test(this._text[a])&&a<this._text.length;)o++,a++;return s+o},searchWordBoundary:function(s,o){for(var a=this._text,e=this._reSpace.test(a[s])?s-1:s,i=a[e],t=f.reNonWord;!t.test(i)&&e>0&&e<a.length;)e+=o,i=a[e];return t.test(i)&&(e+=o===1?0:1),e},selectWord:function(s){s=s||this.selectionStart;var o=this.searchWordBoundary(s,-1),a=this.searchWordBoundary(s,1);this.selectionStart=o,this.selectionEnd=a,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()},selectLine:function(s){s=s||this.selectionStart;var o=this.findLineBoundaryLeft(s),a=this.findLineBoundaryRight(s);return this.selectionStart=o,this.selectionEnd=a,this._fireSelectionChanged(),this._updateTextarea(),this},enterEditing:function(s){if(!(this.isEditing||!this.editable))return this.canvas&&(this.canvas.calcOffset(),this.exitEditingOnOthers(this.canvas)),this.isEditing=!0,this.initHiddenTextarea(s),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered"),this._fireSelectionChanged(),this.canvas?(this.canvas.fire("text:editing:entered",{target:this}),this.initMouseMoveHandler(),this.canvas.requestRenderAll(),this):this},exitEditingOnOthers:function(s){s._iTextInstances&&s._iTextInstances.forEach(function(o){o.selected=!1,o.isEditing&&o.exitEditing()})},initMouseMoveHandler:function(){this.canvas.on("mouse:move",this.mouseMoveHandler)},mouseMoveHandler:function(s){if(!(!this.__isMousedown||!this.isEditing)){var o=this.getSelectionStartFromPointer(s.e),a=this.selectionStart,e=this.selectionEnd;(o!==this.__selectionStartOnMouseDown||a===e)&&(a===o||e===o)||(o>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=o):(this.selectionStart=o,this.selectionEnd=this.__selectionStartOnMouseDown),(this.selectionStart!==a||this.selectionEnd!==e)&&(this.restartCursorIfNeeded(),this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}},_setEditingProps:function(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0},fromStringToGraphemeSelection:function(s,o,a){var e=a.slice(0,s),i=f.util.string.graphemeSplit(e).length;if(s===o)return{selectionStart:i,selectionEnd:i};var t=a.slice(s,o),n=f.util.string.graphemeSplit(t).length;return{selectionStart:i,selectionEnd:i+n}},fromGraphemeToStringSelection:function(s,o,a){var e=a.slice(0,s),i=e.join("").length;if(s===o)return{selectionStart:i,selectionEnd:i};var t=a.slice(s,o),n=t.join("").length;return{selectionStart:i,selectionEnd:i+n}},_updateTextarea:function(){if(this.cursorOffsetCache={},!!this.hiddenTextarea){if(!this.inCompositionMode){var s=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=s.selectionStart,this.hiddenTextarea.selectionEnd=s.selectionEnd}this.updateTextareaPosition()}},updateFromTextArea:function(){if(this.hiddenTextarea){this.cursorOffsetCache={},this.text=this.hiddenTextarea.value,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords());var s=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value);this.selectionEnd=this.selectionStart=s.selectionEnd,this.inCompositionMode||(this.selectionStart=s.selectionStart),this.updateTextareaPosition()}},updateTextareaPosition:function(){if(this.selectionStart===this.selectionEnd){var s=this._calcTextareaPosition();this.hiddenTextarea.style.left=s.left,this.hiddenTextarea.style.top=s.top}},_calcTextareaPosition:function(){if(!this.canvas)return{x:1,y:1};var s=this.inCompositionMode?this.compositionStart:this.selectionStart,o=this._getCursorBoundaries(s),a=this.get2DCursorLocation(s),e=a.lineIndex,i=a.charIndex,t=this.getValueOfPropertyAt(e,i,"fontSize")*this.lineHeight,n=o.leftOffset,h=this.calcTransformMatrix(),r={x:o.left+n,y:o.top+o.topOffset+t},l=this.canvas.getRetinaScaling(),u=this.canvas.upperCanvasEl,d=u.width/l,g=u.height/l,m=d-t,v=g-t,y=u.clientWidth/d,T=u.clientHeight/g;return r=f.util.transformPoint(r,h),r=f.util.transformPoint(r,this.canvas.viewportTransform),r.x*=y,r.y*=T,r.x<0&&(r.x=0),r.x>m&&(r.x=m),r.y<0&&(r.y=0),r.y>v&&(r.y=v),r.x+=this.canvas._offset.left,r.y+=this.canvas._offset.top,{left:r.x+"px",top:r.y+"px",fontSize:t+"px",charHeight:t}},_saveEditingProps:function(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}},_restoreEditingProps:function(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor))},exitEditing:function(){var s=this._textBeforeEdit!==this.text,o=this.hiddenTextarea;return this.selected=!1,this.isEditing=!1,this.selectionEnd=this.selectionStart,o&&(o.blur&&o.blur(),o.parentNode&&o.parentNode.removeChild(o)),this.hiddenTextarea=null,this.abortCursorAnimation(),this._restoreEditingProps(),this._currentCursorOpacity=0,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),s&&this.fire("modified"),this.canvas&&(this.canvas.off("mouse:move",this.mouseMoveHandler),this.canvas.fire("text:editing:exited",{target:this}),s&&this.canvas.fire("object:modified",{target:this})),this},_removeExtraneousStyles:function(){for(var s in this.styles)this._textLines[s]||delete this.styles[s]},removeStyleFromTo:function(s,o){var a=this.get2DCursorLocation(s,!0),e=this.get2DCursorLocation(o,!0),i=a.lineIndex,t=a.charIndex,n=e.lineIndex,h=e.charIndex,r,l;if(i!==n){if(this.styles[i])for(r=t;r<this._unwrappedTextLines[i].length;r++)delete this.styles[i][r];if(this.styles[n])for(r=h;r<this._unwrappedTextLines[n].length;r++)l=this.styles[n][r],l&&(this.styles[i]||(this.styles[i]={}),this.styles[i][t+r-h]=l);for(r=i+1;r<=n;r++)delete this.styles[r];this.shiftLineStyles(n,i-n)}else if(this.styles[i]){l=this.styles[i];var u=h-t,d,g;for(r=t;r<h;r++)delete l[r];for(g in this.styles[i])d=parseInt(g,10),d>=h&&(l[d-u]=l[g],delete l[g])}},shiftLineStyles:function(s,o){var a=c(this.styles);for(var e in this.styles){var i=parseInt(e,10);i>s&&(this.styles[i+o]=a[i],a[i-o]||delete this.styles[i])}},restartCursorIfNeeded:function(){(!this._currentTickState||this._currentTickState.isAborted||!this._currentTickCompleteState||this._currentTickCompleteState.isAborted)&&this.initDelayedCursor()},insertNewlineStyleObject:function(s,o,a,e){var i,t={},n=!1,h=this._unwrappedTextLines[s].length===o;a||(a=1),this.shiftLineStyles(s,a),this.styles[s]&&(i=this.styles[s][o===0?o:o-1]);for(var r in this.styles[s]){var l=parseInt(r,10);l>=o&&(n=!0,t[l-o]=this.styles[s][r],h&&o===0||delete this.styles[s][r])}var u=!1;for(n&&!h&&(this.styles[s+a]=t,u=!0),u&&a--;a>0;)e&&e[a-1]?this.styles[s+a]={0:c(e[a-1])}:i?this.styles[s+a]={0:c(i)}:delete this.styles[s+a],a--;this._forceClearCache=!0},insertCharStyleObject:function(s,o,a,e){this.styles||(this.styles={});var i=this.styles[s],t=i?c(i):{};a||(a=1);for(var n in t){var h=parseInt(n,10);h>=o&&(i[h+a]=t[h],t[h-a]||delete i[h])}if(this._forceClearCache=!0,e){for(;a--;)Object.keys(e[a]).length&&(this.styles[s]||(this.styles[s]={}),this.styles[s][o+a]=c(e[a]));return}if(i)for(var r=i[o?o-1:1];r&&a--;)this.styles[s][o+a]=c(r)},insertNewStyleBlock:function(s,o,a){for(var e=this.get2DCursorLocation(o,!0),i=[0],t=0,n=0;n<s.length;n++)s[n]===`
`?(t++,i[t]=0):i[t]++;i[0]>0&&(this.insertCharStyleObject(e.lineIndex,e.charIndex,i[0],a),a=a&&a.slice(i[0]+1)),t&&this.insertNewlineStyleObject(e.lineIndex,e.charIndex+i[0],t);for(var n=1;n<t;n++)i[n]>0?this.insertCharStyleObject(e.lineIndex+n,0,i[n],a):a&&(this.styles[e.lineIndex+n][0]=a[0]),a=a&&a.slice(i[n]+1);i[n]>0&&this.insertCharStyleObject(e.lineIndex+n,0,i[n],a)},setSelectionStartEndWithShift:function(s,o,a){a<=s?(o===s?this._selectionDirection="left":this._selectionDirection==="right"&&(this._selectionDirection="left",this.selectionEnd=s),this.selectionStart=a):a>s&&a<o?this._selectionDirection==="right"?this.selectionEnd=a:this.selectionStart=a:(o===s?this._selectionDirection="right":this._selectionDirection==="left"&&(this._selectionDirection="right",this.selectionStart=o),this.selectionEnd=a)},setSelectionInBoundaries:function(){var s=this.text.length;this.selectionStart>s?this.selectionStart=s:this.selectionStart<0&&(this.selectionStart=0),this.selectionEnd>s?this.selectionEnd=s:this.selectionEnd<0&&(this.selectionEnd=0)}})}(),f.util.object.extend(f.IText.prototype,{initDoubleClickSimulation:function(){this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown)},onMouseDown:function(c){if(this.canvas){this.__newClickTime=+new Date;var s=c.pointer;this.isTripleClick(s)&&(this.fire("tripleclick",c),this._stopEvent(c.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=s,this.__lastIsEditing=this.isEditing,this.__lastSelected=this.selected}},isTripleClick:function(c){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===c.x&&this.__lastPointer.y===c.y},_stopEvent:function(c){c.preventDefault&&c.preventDefault(),c.stopPropagation&&c.stopPropagation()},initCursorSelectionHandlers:function(){this.initMousedownHandler(),this.initMouseupHandler(),this.initClicks()},doubleClickHandler:function(c){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(c.e))},tripleClickHandler:function(c){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(c.e))},initClicks:function(){this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler)},_mouseDownHandler:function(c){!this.canvas||!this.editable||c.e.button&&c.e.button!==1||(this.__isMousedown=!0,this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(c.e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()))},_mouseDownHandlerBefore:function(c){!this.canvas||!this.editable||c.e.button&&c.e.button!==1||(this.selected=this===this.canvas._activeObject)},initMousedownHandler:function(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore)},initMouseupHandler:function(){this.on("mouseup",this.mouseUpHandler)},mouseUpHandler:function(c){if(this.__isMousedown=!1,!(!this.editable||this.group||c.transform&&c.transform.actionPerformed||c.e.button&&c.e.button!==1)){if(this.canvas){var s=this.canvas._activeObject;if(s&&s!==this)return}this.__lastSelected&&!this.__corner?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(c.e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0}},setCursorByClick:function(c){var s=this.getSelectionStartFromPointer(c),o=this.selectionStart,a=this.selectionEnd;c.shiftKey?this.setSelectionStartEndWithShift(o,a,s):(this.selectionStart=s,this.selectionEnd=s),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())},getSelectionStartFromPointer:function(c){for(var s=this.getLocalPointer(c),o=0,a=0,e=0,i=0,t=0,n,h,r=0,l=this._textLines.length;r<l&&e<=s.y;r++)e+=this.getHeightOfLine(r)*this.scaleY,t=r,r>0&&(i+=this._textLines[r-1].length+this.missingNewlineOffset(r-1));n=this._getLineLeftOffset(t),a=n*this.scaleX,h=this._textLines[t],this.direction==="rtl"&&(s.x=this.width*this.scaleX-s.x+a);for(var u=0,d=h.length;u<d&&(o=a,a+=this.__charBounds[t][u].kernedWidth*this.scaleX,a<=s.x);u++)i++;return this._getNewSelectionStartFromOffset(s,o,a,i,d)},_getNewSelectionStartFromOffset:function(c,s,o,a,e){var i=c.x-s,t=o-c.x,n=t>i||t<0?0:1,h=a+n;return this.flipX&&(h=e-h),h>this._text.length&&(h=this._text.length),h}}),f.util.object.extend(f.IText.prototype,{initHiddenTextarea:function(){this.hiddenTextarea=f.document.createElement("textarea"),this.hiddenTextarea.setAttribute("autocapitalize","off"),this.hiddenTextarea.setAttribute("autocorrect","off"),this.hiddenTextarea.setAttribute("autocomplete","off"),this.hiddenTextarea.setAttribute("spellcheck","false"),this.hiddenTextarea.setAttribute("data-fabric-hiddentextarea",""),this.hiddenTextarea.setAttribute("wrap","off");var c=this._calcTextareaPosition();this.hiddenTextarea.style.cssText="position: absolute; top: "+c.top+"; left: "+c.left+"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; paddingｰtop: "+c.fontSize+";",this.hiddenTextareaContainer?this.hiddenTextareaContainer.appendChild(this.hiddenTextarea):f.document.body.appendChild(this.hiddenTextarea),f.util.addListener(this.hiddenTextarea,"keydown",this.onKeyDown.bind(this)),f.util.addListener(this.hiddenTextarea,"keyup",this.onKeyUp.bind(this)),f.util.addListener(this.hiddenTextarea,"input",this.onInput.bind(this)),f.util.addListener(this.hiddenTextarea,"copy",this.copy.bind(this)),f.util.addListener(this.hiddenTextarea,"cut",this.copy.bind(this)),f.util.addListener(this.hiddenTextarea,"paste",this.paste.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionstart",this.onCompositionStart.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionupdate",this.onCompositionUpdate.bind(this)),f.util.addListener(this.hiddenTextarea,"compositionend",this.onCompositionEnd.bind(this)),!this._clickHandlerInitialized&&this.canvas&&(f.util.addListener(this.canvas.upperCanvasEl,"click",this.onClick.bind(this)),this._clickHandlerInitialized=!0)},keysMap:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorRight",36:"moveCursorLeft",37:"moveCursorLeft",38:"moveCursorUp",39:"moveCursorRight",40:"moveCursorDown"},keysMapRtl:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorLeft",36:"moveCursorRight",37:"moveCursorRight",38:"moveCursorUp",39:"moveCursorLeft",40:"moveCursorDown"},ctrlKeysMapUp:{67:"copy",88:"cut"},ctrlKeysMapDown:{65:"selectAll"},onClick:function(){this.hiddenTextarea&&this.hiddenTextarea.focus()},onKeyDown:function(c){if(this.isEditing){var s=this.direction==="rtl"?this.keysMapRtl:this.keysMap;if(c.keyCode in s)this[s[c.keyCode]](c);else if(c.keyCode in this.ctrlKeysMapDown&&(c.ctrlKey||c.metaKey))this[this.ctrlKeysMapDown[c.keyCode]](c);else return;c.stopImmediatePropagation(),c.preventDefault(),c.keyCode>=33&&c.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}},onKeyUp:function(c){if(!this.isEditing||this._copyDone||this.inCompositionMode){this._copyDone=!1;return}if(c.keyCode in this.ctrlKeysMapUp&&(c.ctrlKey||c.metaKey))this[this.ctrlKeysMapUp[c.keyCode]](c);else return;c.stopImmediatePropagation(),c.preventDefault(),this.canvas&&this.canvas.requestRenderAll()},onInput:function(c){var s=this.fromPaste;if(this.fromPaste=!1,c&&c.stopPropagation(),!!this.isEditing){var o=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,a=this._text.length,e=o.length,i,t,n=e-a,h=this.selectionStart,r=this.selectionEnd,l=h!==r,u,d,g;if(this.hiddenTextarea.value===""){this.styles={},this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll());return}var m=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),v=h>m.selectionStart;l?(i=this._text.slice(h,r),n+=r-h):e<a&&(v?i=this._text.slice(r+n,r):i=this._text.slice(h,h-n)),t=o.slice(m.selectionEnd-n,m.selectionEnd),i&&i.length&&(t.length&&(u=this.getSelectionStyles(h,h+1,!1),u=t.map(function(){return u[0]})),l?(d=h,g=r):v?(d=r-i.length,g=r):(d=r,g=r+i.length),this.removeStyleFromTo(d,g)),t.length&&(s&&t.join("")===f.copiedText&&!f.disableStyleCopyPaste&&(u=f.copiedTextStyle),this.insertNewStyleBlock(t,h,u)),this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())}},onCompositionStart:function(){this.inCompositionMode=!0},onCompositionEnd:function(){this.inCompositionMode=!1},onCompositionUpdate:function(c){this.compositionStart=c.target.selectionStart,this.compositionEnd=c.target.selectionEnd,this.updateTextareaPosition()},copy:function(){this.selectionStart!==this.selectionEnd&&(f.copiedText=this.getSelectedText(),f.disableStyleCopyPaste?f.copiedTextStyle=null:f.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0)},paste:function(){this.fromPaste=!0},_getClipboardData:function(c){return c&&c.clipboardData||f.window.clipboardData},_getWidthBeforeCursor:function(c,s){var o=this._getLineLeftOffset(c),a;return s>0&&(a=this.__charBounds[c][s-1],o+=a.left+a.width),o},getDownCursorOffset:function(c,s){var o=this._getSelectionForOffset(c,s),a=this.get2DCursorLocation(o),e=a.lineIndex;if(e===this._textLines.length-1||c.metaKey||c.keyCode===34)return this._text.length-o;var i=a.charIndex,t=this._getWidthBeforeCursor(e,i),n=this._getIndexOnLine(e+1,t),h=this._textLines[e].slice(i);return h.length+n+1+this.missingNewlineOffset(e)},_getSelectionForOffset:function(c,s){return c.shiftKey&&this.selectionStart!==this.selectionEnd&&s?this.selectionEnd:this.selectionStart},getUpCursorOffset:function(c,s){var o=this._getSelectionForOffset(c,s),a=this.get2DCursorLocation(o),e=a.lineIndex;if(e===0||c.metaKey||c.keyCode===33)return-o;var i=a.charIndex,t=this._getWidthBeforeCursor(e,i),n=this._getIndexOnLine(e-1,t),h=this._textLines[e].slice(0,i),r=this.missingNewlineOffset(e-1);return-this._textLines[e-1].length+n-h.length+(1-r)},_getIndexOnLine:function(c,s){for(var o=this._textLines[c],a=this._getLineLeftOffset(c),e=a,i=0,t,n,h=0,r=o.length;h<r;h++)if(t=this.__charBounds[c][h].width,e+=t,e>s){n=!0;var l=e-t,u=e,d=Math.abs(l-s),g=Math.abs(u-s);i=g<d?h:h-1;break}return n||(i=o.length-1),i},moveCursorDown:function(c){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",c)},moveCursorUp:function(c){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorUpOrDown("Up",c)},_moveCursorUpOrDown:function(c,s){var o="get"+c+"CursorOffset",a=this[o](s,this._selectionDirection==="right");s.shiftKey?this.moveCursorWithShift(a):this.moveCursorWithoutShift(a),a!==0&&(this.setSelectionInBoundaries(),this.abortCursorAnimation(),this._currentCursorOpacity=1,this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorWithShift:function(c){var s=this._selectionDirection==="left"?this.selectionStart+c:this.selectionEnd+c;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,s),c!==0},moveCursorWithoutShift:function(c){return c<0?(this.selectionStart+=c,this.selectionEnd=this.selectionStart):(this.selectionEnd+=c,this.selectionStart=this.selectionEnd),c!==0},moveCursorLeft:function(c){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorLeftOrRight("Left",c)},_move:function(c,s,o){var a;if(c.altKey)a=this["findWordBoundary"+o](this[s]);else if(c.metaKey||c.keyCode===35||c.keyCode===36)a=this["findLineBoundary"+o](this[s]);else return this[s]+=o==="Left"?-1:1,!0;if(typeof a!==void 0&&this[s]!==a)return this[s]=a,!0},_moveLeft:function(c,s){return this._move(c,s,"Left")},_moveRight:function(c,s){return this._move(c,s,"Right")},moveCursorLeftWithoutShift:function(c){var s=!0;return this._selectionDirection="left",this.selectionEnd===this.selectionStart&&this.selectionStart!==0&&(s=this._moveLeft(c,"selectionStart")),this.selectionEnd=this.selectionStart,s},moveCursorLeftWithShift:function(c){if(this._selectionDirection==="right"&&this.selectionStart!==this.selectionEnd)return this._moveLeft(c,"selectionEnd");if(this.selectionStart!==0)return this._selectionDirection="left",this._moveLeft(c,"selectionStart")},moveCursorRight:function(c){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",c)},_moveCursorLeftOrRight:function(c,s){var o="moveCursor"+c+"With";this._currentCursorOpacity=1,s.shiftKey?o+="Shift":o+="outShift",this[o](s)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorRightWithShift:function(c){if(this._selectionDirection==="left"&&this.selectionStart!==this.selectionEnd)return this._moveRight(c,"selectionStart");if(this.selectionEnd!==this._text.length)return this._selectionDirection="right",this._moveRight(c,"selectionEnd")},moveCursorRightWithoutShift:function(c){var s=!0;return this._selectionDirection="right",this.selectionStart===this.selectionEnd?(s=this._moveRight(c,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,s},removeChars:function(c,s){typeof s=="undefined"&&(s=c+1),this.removeStyleFromTo(c,s),this._text.splice(c,s-c),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()},insertChars:function(c,s,o,a){typeof a=="undefined"&&(a=o),a>o&&this.removeStyleFromTo(o,a);var e=f.util.string.graphemeSplit(c);this.insertNewStyleBlock(e,o,s),this._text=[].concat(this._text.slice(0,o),e,this._text.slice(a)),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()}}),function(){var c=f.util.toFixed,s=/  +/g;f.util.object.extend(f.Text.prototype,{_toSVG:function(){var o=this._getSVGLeftTopOffsets(),a=this._getSVGTextAndBg(o.textTop,o.textLeft);return this._wrapSVGTextAndBg(a)},toSVG:function(o){return this._createBaseSVGMarkup(this._toSVG(),{reviver:o,noStyle:!0,withShadow:!0})},_getSVGLeftTopOffsets:function(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}},_wrapSVGTextAndBg:function(o){var a=!0,e=this.getSvgTextDecoration(this);return[o.textBgRects.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'+this.fontFamily.replace(/"/g,"'")+'" ':"",this.fontSize?'font-size="'+this.fontSize+'" ':"",this.fontStyle?'font-style="'+this.fontStyle+'" ':"",this.fontWeight?'font-weight="'+this.fontWeight+'" ':"",e?'text-decoration="'+e+'" ':"",'style="',this.getSvgStyles(a),'"',this.addPaintOrder()," >",o.textSpans.join(""),`</text>
`]},_getSVGTextAndBg:function(o,a){var e=[],i=[],t=o,n;this._setSVGBg(i);for(var h=0,r=this._textLines.length;h<r;h++)n=this._getLineLeftOffset(h),(this.textBackgroundColor||this.styleHas("textBackgroundColor",h))&&this._setSVGTextLineBg(i,h,a+n,t),this._setSVGTextLineText(e,h,a+n,t),t+=this.getHeightOfLine(h);return{textSpans:e,textBgRects:i}},_createTextCharSpan:function(o,a,e,i){var t=o!==o.trim()||o.match(s),n=this.getSvgSpanStyles(a,t),h=n?'style="'+n+'"':"",r=a.deltaY,l="",u=f.Object.NUM_FRACTION_DIGITS;return r&&(l=' dy="'+c(r,u)+'" '),['<tspan x="',c(e,u),'" y="',c(i,u),'" ',l,h,">",f.util.string.escapeXml(o),"</tspan>"].join("")},_setSVGTextLineText:function(o,a,e,i){var t=this.getHeightOfLine(a),n=this.textAlign.indexOf("justify")!==-1,h,r,l="",u,d,g=0,m=this._textLines[a],v;i+=t*(1-this._fontSizeFraction)/this.lineHeight;for(var y=0,T=m.length-1;y<=T;y++)v=y===T||this.charSpacing,l+=m[y],u=this.__charBounds[a][y],g===0?(e+=u.kernedWidth-u.width,g+=u.width):g+=u.kernedWidth,n&&!v&&this._reSpaceAndTab.test(m[y])&&(v=!0),v||(h=h||this.getCompleteStyleDeclaration(a,y),r=this.getCompleteStyleDeclaration(a,y+1),v=this._hasStyleChangedForSvg(h,r)),v&&(d=this._getStyleDeclaration(a,y)||{},o.push(this._createTextCharSpan(l,d,e,i)),l="",h=r,e+=g,g=0)},_pushTextBgRect:function(o,a,e,i,t,n){var h=f.Object.NUM_FRACTION_DIGITS;o.push("		<rect ",this._getFillAttributes(a),' x="',c(e,h),'" y="',c(i,h),'" width="',c(t,h),'" height="',c(n,h),`"></rect>
`)},_setSVGTextLineBg:function(o,a,e,i){for(var t=this._textLines[a],n=this.getHeightOfLine(a)/this.lineHeight,h=0,r=0,l,u,d=this.getValueOfPropertyAt(a,0,"textBackgroundColor"),g=0,m=t.length;g<m;g++)l=this.__charBounds[a][g],u=this.getValueOfPropertyAt(a,g,"textBackgroundColor"),u!==d?(d&&this._pushTextBgRect(o,d,e+r,i,h,n),r=l.left,h=l.width,d=u):h+=l.kernedWidth;u&&this._pushTextBgRect(o,u,e+r,i,h,n)},_getFillAttributes:function(o){var a=o&&typeof o=="string"?new f.Color(o):"";return!a||!a.getSource()||a.getAlpha()===1?'fill="'+o+'"':'opacity="'+a.getAlpha()+'" fill="'+a.setAlpha(1).toRgb()+'"'},_getSVGLineTopOffset:function(o){for(var a=0,e=0,i=0;i<o;i++)a+=this.getHeightOfLine(i);return e=this.getHeightOfLine(i),{lineTop:a,offset:(this._fontSizeMult-this._fontSizeFraction)*e/(this.lineHeight*this._fontSizeMult)}},getSvgStyles:function(o){var a=f.Object.prototype.getSvgStyles.call(this,o);return a+" white-space: pre;"}})}(),function(c){var s=c.fabric||(c.fabric={});s.Textbox=s.util.createClass(s.IText,s.Observable,{type:"textbox",minWidth:20,dynamicMinWidth:2,__cachedLines:null,lockScalingFlip:!0,noScaleCache:!1,_dimensionAffectingProps:s.Text.prototype._dimensionAffectingProps.concat("width"),_wordJoiners:/[ \t\r]/,splitByGrapheme:!1,initDimensions:function(){this.__skipDimension||(this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.indexOf("justify")!==-1&&this.enlargeSpaces(),this.height=this.calcTextHeight(),this.saveState({propertySet:"_dimensionAffectingProps"}))},_generateStyleMap:function(o){for(var a=0,e=0,i=0,t={},n=0;n<o.graphemeLines.length;n++)o.graphemeText[i]===`
`&&n>0?(e=0,i++,a++):!this.splitByGrapheme&&this._reSpaceAndTab.test(o.graphemeText[i])&&n>0&&(e++,i++),t[n]={line:a,offset:e},i+=o.graphemeLines[n].length,e+=o.graphemeLines[n].length;return t},styleHas:function(o,a){if(this._styleMap&&!this.isWrapping){var e=this._styleMap[a];e&&(a=e.line)}return s.Text.prototype.styleHas.call(this,o,a)},isEmptyStyles:function(o){if(!this.styles)return!0;var a=0,e=o+1,i,t,n=!1,h=this._styleMap[o],r=this._styleMap[o+1];h&&(o=h.line,a=h.offset),r&&(e=r.line,n=e===o,i=r.offset),t=typeof o=="undefined"?this.styles:{line:this.styles[o]};for(var l in t)for(var u in t[l])if(u>=a&&(!n||u<i))for(var d in t[l][u])return!1;return!0},_getStyleDeclaration:function(o,a){if(this._styleMap&&!this.isWrapping){var e=this._styleMap[o];if(!e)return null;o=e.line,a=e.offset+a}return this.callSuper("_getStyleDeclaration",o,a)},_setStyleDeclaration:function(o,a,e){var i=this._styleMap[o];o=i.line,a=i.offset+a,this.styles[o][a]=e},_deleteStyleDeclaration:function(o,a){var e=this._styleMap[o];o=e.line,a=e.offset+a,delete this.styles[o][a]},_getLineStyle:function(o){var a=this._styleMap[o];return!!this.styles[a.line]},_setLineStyle:function(o){var a=this._styleMap[o];this.styles[a.line]={}},_wrapText:function(o,a){var e=[],i;for(this.isWrapping=!0,i=0;i<o.length;i++)e=e.concat(this._wrapLine(o[i],i,a));return this.isWrapping=!1,e},_measureWord:function(o,a,e){var i=0,t,n=!0;e=e||0;for(var h=0,r=o.length;h<r;h++){var l=this._getGraphemeBox(o[h],a,h+e,t,n);i+=l.kernedWidth,t=o[h]}return i},_wrapLine:function(o,a,e,W){var t=0,n=this.splitByGrapheme,h=[],r=[],l=n?s.util.string.graphemeSplit(o):o.split(this._wordJoiners),u="",d=0,g=n?"":" ",m=0,v=0,y=0,T=!0,M=this._getWidthOfCharSpacing(),W=W||0;l.length===0&&l.push([]),e-=W;for(var H=0;H<l.length;H++)u=n?l[H]:s.util.string.graphemeSplit(l[H]),m=this._measureWord(u,a,d),d+=u.length,t+=v+m-M,t>e&&!T?(h.push(r),r=[],t=m,T=!0):t+=M,!T&&!n&&r.push(g),r=r.concat(u),v=n?0:this._measureWord([g],a,d),d++,T=!1,m>y&&(y=m);return H&&h.push(r),y+W>this.dynamicMinWidth&&(this.dynamicMinWidth=y-M+W),h},isEndOfWrapping:function(o){return!this._styleMap[o+1]||this._styleMap[o+1].line!==this._styleMap[o].line},missingNewlineOffset:function(o){return this.splitByGrapheme?this.isEndOfWrapping(o)?1:0:1},_splitTextIntoLines:function(o){for(var a=s.Text.prototype._splitTextIntoLines.call(this,o),e=this._wrapText(a.lines,this.width),i=new Array(e.length),t=0;t<e.length;t++)i[t]=e[t].join("");return a.lines=i,a.graphemeLines=e,a},getMinWidth:function(){return Math.max(this.minWidth,this.dynamicMinWidth)},_removeExtraneousStyles:function(){var o={};for(var a in this._styleMap)this._textLines[a]&&(o[this._styleMap[a].line]=1);for(var a in this.styles)o[a]||delete this.styles[a]},toObject:function(o){return this.callSuper("toObject",["minWidth","splitByGrapheme"].concat(o))}}),s.Textbox.fromObject=function(o,a){return s.Object._fromObject("Textbox",o,a,"text")}}(et),function(){var c=f.controlsUtils,s=c.scaleSkewCursorStyleHandler,o=c.scaleCursorStyleHandler,a=c.scalingEqually,e=c.scalingYOrSkewingX,i=c.scalingXOrSkewingY,t=c.scaleOrSkewActionName,n=f.Object.prototype.controls;if(n.ml=new f.Control({x:-.5,y:0,cursorStyleHandler:s,actionHandler:i,getActionName:t}),n.mr=new f.Control({x:.5,y:0,cursorStyleHandler:s,actionHandler:i,getActionName:t}),n.mb=new f.Control({x:0,y:.5,cursorStyleHandler:s,actionHandler:e,getActionName:t}),n.mt=new f.Control({x:0,y:-.5,cursorStyleHandler:s,actionHandler:e,getActionName:t}),n.tl=new f.Control({x:-.5,y:-.5,cursorStyleHandler:o,actionHandler:a}),n.tr=new f.Control({x:.5,y:-.5,cursorStyleHandler:o,actionHandler:a}),n.bl=new f.Control({x:-.5,y:.5,cursorStyleHandler:o,actionHandler:a}),n.br=new f.Control({x:.5,y:.5,cursorStyleHandler:o,actionHandler:a}),n.mtr=new f.Control({x:0,y:-.5,actionHandler:c.rotationWithSnapping,cursorStyleHandler:c.rotationStyleHandler,offsetY:-40,withConnection:!0,actionName:"rotate"}),f.Textbox){var h=f.Textbox.prototype.controls={};h.mtr=n.mtr,h.tr=n.tr,h.br=n.br,h.tl=n.tl,h.bl=n.bl,h.mt=n.mt,h.mb=n.mb,h.mr=new f.Control({x:.5,y:0,actionHandler:c.changeWidth,cursorStyleHandler:s,actionName:"resizing"}),h.ml=new f.Control({x:-.5,y:0,actionHandler:c.changeWidth,cursorStyleHandler:s,actionName:"resizing"})}}()})(Ct);export{Ct as f};
