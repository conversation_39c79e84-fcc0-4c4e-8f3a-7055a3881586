import{f as n}from"./handleMethod-BIjqYEft.js";const _=[{value:1,label:"未抽取"},{value:2,label:"待验收"},{value:3,label:"验收中"},{value:4,label:"已完成"}],u=[{value:0,label:"通过"},{value:1,label:"未通过"}],c=[{prop:"project_name",label:"所属资格",minWidth:"90px"},{prop:"subject_name",label:"所属科目",minWidth:"90px"},{prop:"task_state",label:"验收状态",minWidth:"90px",formatter:t=>n(t.task_state,_)},{prop:"check_progress",label:"验收进度",type:"slot",minWidth:"120px"},{prop:"exam_count",label:"参考人数",minWidth:"90px"},{prop:"example_count",label:"抽取数量",minWidth:"90px"},{prop:"pass_count",label:"通过量",minWidth:"70px",formatter:t=>t.task_state==1||t.task_state==2?"-":t.pass_count},{prop:"no_pass_count",label:"未通过量",minWidth:"90px",formatter:t=>t.task_state==1||t.task_state==2?"-":t.no_pass_count},{prop:"pass_rate",label:"通过率",minWidth:"90px",formatter:t=>t.task_state==1||t.task_state==2?"-":t.pass_rate},{prop:"score_threshold",label:"分差阈值",type:"slot",minWidth:"90px"},{prop:"verify_result",label:"验收结果",minWidth:"90px",formatter:t=>t.task_state==1||t.task_state==2||t.task_state==3?"-":n(t.verify_result,u)},{prop:"verify_time",label:"验收完成时间",minWidth:"160px",formatter:t=>t.task_state==1||t.task_state==2||t.task_state==3?"-":t.verify_time},{prop:"operation",label:"操作",type:"slot",minWidth:"120px",fixed:"right"}],f=t=>{t.forEach(e=>{let s=e.example_count?e.example_count:0,a=e.repeat_task_count?e.repeat_task_count:0;s==0?e.percent=0:e.percent=o(a,s);let l=e.pass_count?e.pass_count:0,p=e.no_pass_count?e.no_pass_count:0,r=l+p;r==0?e.pass_rate="0%":e.pass_rate=o(l,r)+"%"})},o=(t,e)=>{let s=t/e*100,a;if(s===0)a=0;else if(s===100)a=100;else{const l=s.toString();if(l.includes(".")){const p=l.split("."),r=p[1].substring(0,2);a=`${p[0]}.${r}`}else a=`${l}.00`}return a};export{f as a,o as b,u as c,_ as r,c as t};
