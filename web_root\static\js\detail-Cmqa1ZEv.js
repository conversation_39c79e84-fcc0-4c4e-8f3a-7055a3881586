var j=(s,l,m)=>new Promise((y,e)=>{var h=c=>{try{x(m.next(c))}catch(u){e(u)}},D=c=>{try{x(m.throw(c))}catch(u){e(u)}},x=c=>c.done?y(c.value):Promise.resolve(c.value).then(h,D);x((m=m.apply(s,l)).next())});import{aQ as N,aR as L,d as z,aO as Q,r as b,o as n,c as i,e as o,b as a,h as d,f as S,t as k,y as C,u as _,F as $,p as I,_ as H,aS as B,G as A,l as O,B as F,g as J,q as R,T,ac as Z,ad as E}from"./index-B63pSD2p.js";import{i as G,g as P}from"./quesNum-CouueI57.js";import{z as K,Z as W,a as X,b as Y,r as ee,T as te,c as se,A as le,i as ae}from"./anticlockwise-2-line-Iit2_C-u.js";import{h as V}from"./handleImages-D-nd439N.js";const ne="/static/png/error-D0QbUSdm.png",Be=s=>N.request("post",L("/v1/exception/get_ques_answer_similarity"),{data:s}),oe=s=>N.request("post",L("/v1/exception/update_judgement_result"),{data:s}),ie=s=>N.request("post",L("/v1/ques_manage/get_ques_detail"),{data:s}),ce={key:0,class:"readOnly-box"},re={class:"flex mt-[12px] mb-[10px] items-center justify-between"},ue={key:0},de={key:0,class:"text-inline-box"},_e=["innerHTML"],me={key:1,class:"text-inline-box"},pe=["innerHTML"],fe={key:2},ve={class:"text-inline-box"},ge={key:0,class:"flex-c"},he=["innerHTML"],be={key:1,class:"flex-c",style:{"min-height":"32px"}},ye=["innerHTML"],xe={key:0,class:"border-b-[1px] border-dashed border-[#dcdfe6] m-[5px]"},qe=z({name:"ques-content-stam",__name:"index",props:{questionDesc:{},questionDescModifiers:{}},emits:["update:questionDesc"],setup(s){const l=Q(s,"questionDesc"),m=e=>{if(e==null)return"";const h=e.indexOf("（");return h!==-1?e.slice(h,e.length):e+"．"},y=e=>e.slice(2);return(e,h)=>{var c,u,t,r,v,g,w;const D=b("el-tag"),x=b("ques-content-stam");return l.value.ques_desc?(n(),i("div",ce,[o("div",re,[a(D,{type:"primary"},{default:d(()=>[S(k(l.value.ques_type_name),1)]),_:1}),l.value.ques_score!=null?(n(),i("div",ue,"分值："+k(l.value.ques_score),1)):C("",!0)]),(u=(c=l.value)==null?void 0:c.ques_desc)!=null&&u.html?(n(),i("div",de,[S(k(m((t=l.value)==null?void 0:t.ques_order_new))+" ",1),o("span",{class:"flex-1",innerHTML:_(V)(l.value.ques_desc.html)},null,8,_e)])):(n(),i("div",me,[S(k(m((r=l.value)==null?void 0:r.ques_order_new))+" ",1),o("span",{class:"flex-1",innerHTML:(g=(v=l.value)==null?void 0:v.ques_desc)==null?void 0:g.text},null,8,pe)])),l.value.ques_type_code==="A"||l.value.ques_type_code==="C"?(n(),i("div",fe,[(n(!0),i($,null,I(l.value.ques_choices,p=>(n(),i("div",ve,[p.optionShow?(n(),i("div",ge,[o("span",null,k(p.options.substring(0,2)),1),o("span",{innerHTML:_(V)(p.optionShow)},null,8,he)])):(n(),i("div",be,[o("span",null,k(p.options.substring(0,2)),1),o("span",{innerHTML:y(_(V)(p.html))},null,8,ye)]))]))),256))])):C("",!0),(n(!0),i($,null,I((w=l.value)==null?void 0:w.children,(p,f)=>(n(),i("div",null,[p.ques_desc?(n(),i("div",xe)):C("",!0),a(x,{questionDesc:l.value.children[f],"onUpdate:questionDesc":M=>l.value.children[f]=M},null,8,["questionDesc","onUpdate:questionDesc"])]))),256))])):C("",!0)}}}),ke=H(qe,[["__scopeId","data-v-45f4bb1f"]]),U=s=>(Z("data-v-ac9fd388"),s=s(),E(),s),we={class:"flex border-t border-b border-1 border-gray-300",style:{height:"100%"}},Se={class:"flex-1 p-5"},De=U(()=>o("h4",null,"试题描述",-1)),Ce={class:"flex-1 p-5 border-l border-1 border-gray-300"},Me=U(()=>o("h4",null,"考生答案",-1)),Oe={class:"flex flex-1 justify-end items-center"},Te={class:"text-[16px]"},Ve={class:"text-red-500 text-[18px] pl-2"},$e={key:0,class:"errorImage",src:ne},Ie={style:{flex:"auto"}},Ne=z({__name:"detail",props:B({title:{type:String,default:"抄袭题干详情"},currentSimilarityId:{type:String,default:""},detailData:{type:Object,default:()=>({})}},{drawerVisible:{},drawerVisibleModifiers:{}}),emits:["update:drawerVisible"],setup(s){let l=A().smallQuesNumStyle;const m=Q(s,"drawerVisible"),y=s,e=O({}),h=O({}),D=O(""),x=()=>{e.value={},h.value={};let u={ques_code:y.detailData.ques_code};ie(u).then(t=>{var r,v,g;if(t.code&&t.code===200){let w=((r=t.data)==null?void 0:r.ques_detail)||{};D.value=w.ques_code;const p=P(l);if(G((v=t.data)==null?void 0:v.ques_detail.children),t.data.ques_detail.level=1,e.value=((g=t.data)==null?void 0:g.ques_detail)||{},e.value.children&&e.value.children.length>0){if(l!=0)for(let f in e.value.children)e.value.children[f].ques_order_new=p[f];else for(let f in e.value.children)e.value.children[f].ques_order_new=e.value.children[f].ques_order;h.value=JSON.parse(JSON.stringify(e.value))}else h.value=JSON.parse(JSON.stringify(e.value))}else T.warning(t.msg)})},c=u=>j(this,null,function*(){const t={answer_quest_similarity_id:y.currentSimilarityId,judgement_result:u},r=yield oe(t);r.code&&r.code===200?(m.value=!1,T.success(r.msg)):T.error(r.msg)});return F(()=>y.detailData,u=>{x()},{deep:!0}),(u,t)=>{const r=b("el-scrollbar"),v=b("iconify-icon-offline"),g=b("el-text"),w=b("hander"),p=b("el-image"),f=b("el-button"),M=b("el-drawer");return n(),J(M,{modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=q=>m.value=q),title:s.title,size:"70%",class:"subjective-check-ai-detail-drawer-container"},{footer:d(()=>[o("div",Ie,[a(f,{onClick:t[1]||(t[1]=q=>c(2))},{default:d(()=>[S("未抄袭")]),_:1}),a(f,{type:"primary",onClick:t[2]||(t[2]=q=>c(1))},{default:d(()=>[S("判定抄袭")]),_:1})])]),default:d(()=>[o("div",we,[o("div",Se,[De,a(r,{height:"calc(100% - 10px)"},{default:d(()=>[a(ke,{questionDesc:e.value,"onUpdate:questionDesc":t[0]||(t[0]=q=>e.value=q)},null,8,["questionDesc"])]),_:1})]),o("div",Ce,[a(w,{class:"flex"},{default:d(()=>[Me,o("div",Oe,[o("p",Te,[S("题干相似度"),o("span",Ve,k((s.detailData.similarity*100).toFixed(2))+"%",1)]),a(g,{class:"icon dark:!bg-black eye-box tool-bg",title:"放大",size:"large",onClick:_(K)},{default:d(()=>[a(v,{icon:_(W)},null,8,["icon"])]),_:1},8,["onClick"]),a(g,{class:"icon pr-2 dark:!bg-black eye-box tool-bg",title:"缩小",size:"large",onClick:_(X)},{default:d(()=>[a(v,{icon:_(Y)},null,8,["icon"])]),_:1},8,["onClick"]),a(g,{class:"icon dark:!bg-black eye-box tool-bg",title:"自适应",size:"large",onClick:_(ee)},{default:d(()=>[a(v,{icon:_(te)},null,8,["icon"])]),_:1},8,["onClick"]),a(g,{class:"icon dark:!bg-black eye-box tool-bg",title:"旋转",size:"large",onClick:_(se)},{default:d(()=>[a(v,{icon:_(le)},null,8,["icon"])]),_:1},8,["onClick"])])]),_:1}),a(r,{height:"calc(100% - 10px)",class:"relative"},{default:d(()=>[(n(!0),i($,null,I(s.detailData.answer_image_paths,q=>(n(),i("div",null,[a(p,{class:"relative",style:R(_(ae)),src:q},null,8,["style","src"])]))),256)),s.detailData.judgement_result?(n(),i("img",$e)):C("",!0)]),_:1})])])]),_:1},8,["modelValue","title"])}}}),Le=H(Ne,[["__scopeId","data-v-ac9fd388"]]),Ae=Object.freeze(Object.defineProperty({__proto__:null,default:Le},Symbol.toStringTag,{value:"Module"}));export{Le as D,Ae as d,Be as g};
