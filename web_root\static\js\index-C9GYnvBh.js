import{dl as Le,dm as xt,dn as vt,dp as $e,dq as ot,dr as ir,ds as at,dt as z,du as Nu,dv as Nt,dw as Vu,dx as ku,dy as Gu,dz as L,dA as Us,dB as Ys,dC as Si,dD as zu,dE as Ou,dF as Ut,dG as wt,dH as Bt,dI as lt,dJ as Xs,dK as V,dL as Ve,dM as pt,dN as _t,dO as U,dP as Yt,dQ as Xr,dR as Zr,dS as ht,dT as Y,dU as Bu,dV as Fu,dW as F,dX as re,dY as fe,dZ as ue,d_ as nt,d$ as Ct,e0 as ke,e1 as $,e2 as Ft,e3 as kt,e4 as Tt,e5 as ct,e6 as qr,e7 as O,e8 as or,e9 as Kr,ea as tt,eb as sr,ec as Wu,ed as It,ee as Xt,ef as rt,eg as Wt,eh as qt,ei as Mn,ej as Kt,ek as $u,el as En,em as jr,en as Hu,eo as nn,ep as jt,eq as Jr,er as on,es as Uu,et as Zt,eu as Qr,ev as Dt,ew as gt,ex as Yu,ey as xe,ez as Xu,eA as Zu,eB as Zs,eC as qu,eD as Ku,eE as xi,eF as Rn,eG as qs,eH as Ee,eI as lr,eJ as q,eK as Ks,eL as ju,eM as Ju,eN as Qu,eO as tv,eP as ta,eQ as Ke,eR as Re,eS as Nn,eT as Vn,eU as ev,eV as rv,eW as av,eX as ur,eY as js,eZ as st,e_ as Js,e$ as sn,f0 as nv,f1 as iv,f2 as mt,f3 as ea,f4 as kn,f5 as Et,f6 as Jt,f7 as ov,f8 as sv,f9 as ra,fa as lv,fb as uv,fc as vv,fd as aa,fe as Qs,ff as ln,fg as cv,fh as tl,fi as Mr,fj as vr,fk as Gn,fl as Lt,fm as je,fn as un,fo as Ot,fp as hv,fq as fv,fr as Rt,fs as St,ft as el,fu as Ne,fv as rl,fw as _i,fx as dr,fy as bi,fz as pv,fA as al,fB as vn,fC as nl,fD as wi,fE as cr,fF as se,fG as hr,fH as gv,fI as dv,fJ as Ce,fK as Ti,fL as yv,fM as cn,fN as Ht,fO as hn,fP as zn,fQ as On,fR as Bn,fS as fr,fT as na,fU as mv,fV as il,fW as Sv,fX as ol,fY as xv,fZ as _v,f_ as Ii,f$ as bv,g0 as Di,g1 as Je,g2 as ie,g3 as wv,g4 as Tv,g5 as Iv,g6 as pr,g7 as sl,g8 as Dv,g9 as ll,ga as ul,gb as vl,gc as Av,gd as Qe,ge as cl,gf as hl,gg as Lv,gh as Cv,gi as Pv,gj as fl,gk as fn,gl as pn,gm as Mv,gn as va,go as pl,gp as ut,gq as ia,gr as Fn,gs as ca,gt as Ev,gu as Rv,gv as He,gw as Nv,gx as Ai,gy as Vv,gz as Li,gA as oa,gB as kv,gC as gn,gD as gl,gE as Ci,gF as Gv,gG as zv,gH as Ov,gI as Pi,gJ as Bv,gK as Fv,gL as Wv,gM as Wn,gN as Mi,gO as Ei,gP as $v,gQ as Hv,gR as Uv,gS as Yv,gT as Xv,gU as Zv,gV as qv,gW as Kv,gX as jv,gY as Jv,gZ as dl,g_ as Qv,g$ as tc,h0 as ec,h1 as rc,h2 as ac,h3 as nc,h4 as ic,h5 as oc,h6 as yl,h7 as sc,h8 as lc,h9 as uc,ha as vc,hb as Ri,hc as cc,hd as Ge,he as yr,hf as hc,hg as fc,hh as pc,hi as gc,hj as dc,hk as yc,hl as mc,hm as ml,hn as ha,ho as Sc,hp as xc,hq as _c,hr as bc,hs as wc,ht as Tc,hu as Ic,hv as Er,hw as Dc,hx as Ac,hy as $n,hz as Lc,hA as Sl,hB as Cc,hC as Pc,hD as Mc,hE as Ec,hF as Rc,hG as Nc,hH as Ni,hI as Vc,hJ as kc,hK as Rr,hL as Hn,hM as Gc,hN as Un,hO as fa,hP as zc,hQ as Oc,hR as Bc,hS as Fc,hT as Wc,hU as $c,hV as Hc,hW as Uc,hX as Yc,hY as Xc,hZ as Zc,h_ as qc,h$ as Kc,i0 as jc,i1 as Jc,i2 as Qc,i3 as th,i4 as eh,i5 as rh}from"./index-B63pSD2p.js";function ah(a){if(a){for(var e=[],t=0;t<a.length;t++)e.push(a[t].slice());return e}}function nh(a,e){var t=a.label,r=e&&e.getTextGuideLine();return{dataIndex:a.dataIndex,dataType:a.dataType,seriesIndex:a.seriesModel.seriesIndex,text:a.label.style.text,rect:a.hostRect,labelRect:a.rect,align:t.style.align,verticalAlign:t.style.verticalAlign,labelLinePoints:ah(r&&r.shape.points)}}var Vi=["align","verticalAlign","width","height","fontSize"],yt=new Le,pa=xt(),ih=xt();function mr(a,e,t){for(var r=0;r<t.length;r++){var n=t[r];e[n]!=null&&(a[n]=e[n])}}var Sr=["x","y","rotation"],oh=function(){function a(){this._labelList=[],this._chartViewList=[]}return a.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},a.prototype._addLabel=function(e,t,r,n,i){var o=n.style,s=n.__hostTarget,l=s.textConfig||{},u=n.getComputedTransform(),v=n.getBoundingRect().plain();vt.applyTransform(v,v,u),u?yt.setLocalTransform(u):(yt.x=yt.y=yt.rotation=yt.originX=yt.originY=0,yt.scaleX=yt.scaleY=1),yt.rotation=$e(yt.rotation);var c=n.__hostTarget,h;if(c){h=c.getBoundingRect().plain();var f=c.getComputedTransform();vt.applyTransform(h,h,f)}var p=h&&c.getTextGuideLine();this._labelList.push({label:n,labelLine:p,seriesModel:r,dataIndex:e,dataType:t,layoutOption:i,computedLayoutOption:null,rect:v,hostRect:h,priority:h?h.width*h.height:0,defaultAttr:{ignore:n.ignore,labelGuideIgnore:p&&p.ignore,x:yt.x,y:yt.y,scaleX:yt.scaleX,scaleY:yt.scaleY,rotation:yt.rotation,style:{x:o.x,y:o.y,align:o.align,verticalAlign:o.verticalAlign,width:o.width,height:o.height,fontSize:o.fontSize},cursor:n.cursor,attachedPos:l.position,attachedRot:l.rotation}})},a.prototype.addLabelsOfSeries=function(e){var t=this;this._chartViewList.push(e);var r=e.__model,n=r.get("labelLayout");(ot(n)||ir(n).length)&&e.group.traverse(function(i){if(i.ignore)return!0;var o=i.getTextContent(),s=at(i);o&&!o.disableLabelLayout&&t._addLabel(s.dataIndex,s.dataType,r,o,n)})},a.prototype.updateLayoutConfig=function(e){var t=e.getWidth(),r=e.getHeight();function n(m,x){return function(){Si(m,x)}}for(var i=0;i<this._labelList.length;i++){var o=this._labelList[i],s=o.label,l=s.__hostTarget,u=o.defaultAttr,v=void 0;ot(o.layoutOption)?v=o.layoutOption(nh(o,l)):v=o.layoutOption,v=v||{},o.computedLayoutOption=v;var c=Math.PI/180;l&&l.setTextConfig({local:!1,position:v.x!=null||v.y!=null?null:u.attachedPos,rotation:v.rotate!=null?v.rotate*c:u.attachedRot,offset:[v.dx||0,v.dy||0]});var h=!1;if(v.x!=null?(s.x=z(v.x,t),s.setStyle("x",0),h=!0):(s.x=u.x,s.setStyle("x",u.style.x)),v.y!=null?(s.y=z(v.y,r),s.setStyle("y",0),h=!0):(s.y=u.y,s.setStyle("y",u.style.y)),v.labelLinePoints){var f=l.getTextGuideLine();f&&(f.setShape({points:v.labelLinePoints}),h=!1)}var p=pa(s);p.needsUpdateLabelLine=h,s.rotation=v.rotate!=null?v.rotate*c:u.rotation,s.scaleX=u.scaleX,s.scaleY=u.scaleY;for(var g=0;g<Vi.length;g++){var d=Vi[g];s.setStyle(d,v[d]!=null?v[d]:u.style[d])}if(v.draggable){if(s.draggable=!0,s.cursor="move",l){var y=o.seriesModel;if(o.dataIndex!=null){var S=o.seriesModel.getData(o.dataType);y=S.getItemModel(o.dataIndex)}s.on("drag",n(l,y.getModel("labelLine")))}}else s.off("drag"),s.cursor=u.cursor}},a.prototype.layout=function(e){var t=e.getWidth(),r=e.getHeight(),n=Nu(this._labelList),i=Nt(n,function(l){return l.layoutOption.moveOverlap==="shiftX"}),o=Nt(n,function(l){return l.layoutOption.moveOverlap==="shiftY"});Vu(i,0,t),ku(o,0,r);var s=Nt(n,function(l){return l.layoutOption.hideOverlap});Gu(s)},a.prototype.processLabelsOverall=function(){var e=this;L(this._chartViewList,function(t){var r=t.__model,n=t.ignoreLabelLineUpdate,i=r.isAnimationEnabled();t.group.traverse(function(o){if(o.ignore&&!o.forceLabelAnimation)return!0;var s=!n,l=o.getTextContent();!s&&l&&(s=pa(l).needsUpdateLabelLine),s&&e._updateLabelLine(o,r),i&&e._animateLabels(o,r)})})},a.prototype._updateLabelLine=function(e,t){var r=e.getTextContent(),n=at(e),i=n.dataIndex;if(r&&i!=null){var o=t.getData(n.dataType),s=o.getItemModel(i),l={},u=o.getItemVisual(i,"style");if(u){var v=o.getVisual("drawType");l.stroke=u[v]}var c=s.getModel("labelLine");Us(e,Ys(s),l),Si(e,c)}},a.prototype._animateLabels=function(e,t){var r=e.getTextContent(),n=e.getTextGuideLine();if(r&&(e.forceLabelAnimation||!r.ignore&&!r.invisible&&!e.disableLabelAnimation&&!zu(e))){var i=pa(r),o=i.oldLayout,s=at(e),l=s.dataIndex,u={x:r.x,y:r.y,rotation:r.rotation},v=t.getData(s.dataType);if(o){r.attr(o);var h=e.prevStates;h&&(Bt(h,"select")>=0&&r.attr(i.oldLayoutSelect),Bt(h,"emphasis")>=0&&r.attr(i.oldLayoutEmphasis)),lt(r,u,t,l)}else if(r.attr(u),!Ou(r).valueAnimation){var c=Ut(r.style.opacity,1);r.style.opacity=0,wt(r,{style:{opacity:c}},t,l)}if(i.oldLayout=u,r.states.select){var f=i.oldLayoutSelect={};mr(f,u,Sr),mr(f,r.states.select,Sr)}if(r.states.emphasis){var p=i.oldLayoutEmphasis={};mr(p,u,Sr),mr(p,r.states.emphasis,Sr)}Xs(r,l,v,t,t)}if(n&&!n.ignore&&!n.invisible){var i=ih(n),o=i.oldLayout,g={points:n.shape.points};o?(n.attr({shape:o}),lt(n,{shape:g},t)):(n.setShape(g),n.style.strokePercent=0,wt(n,{style:{strokePercent:1}},t)),i.oldLayout=g}},a}(),ga=xt();function sh(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){var n=ga(t).labelManager;n||(n=ga(t).labelManager=new oh),n.clearLabels()}),a.registerUpdateLifecycle("series:layoutlabels",function(e,t,r){var n=ga(t).labelManager;r.updatedSeries.forEach(function(i){n.addLabelsOfSeries(t.getViewOfSeriesModel(i))}),n.updateLayoutConfig(t),n.layout(t),n.processLabelsOverall()})}var lh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Ve(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t==null?this.option.large?5e3:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t==null?this.option.large?1e4:this.get("progressiveThreshold"):t},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(pt);const uh=lh;var xl=4,vh=function(){function a(){}return a}(),ch=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new vh},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var n=r.points,i=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&i[0]<xl,v=this.softClipShape,c;if(u){this._ctx=l;return}for(this._ctx=null,c=this._off;c<n.length;){var h=n[c++],f=n[c++];isNaN(h)||isNaN(f)||v&&!v.contain(h,f)||(s.x=h-i[0]/2,s.y=f-i[1]/2,s.width=i[0],s.height=i[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=c,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,n=t.size,i=this._ctx,o=this.softClipShape,s;if(i){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||i.fillRect(l-n[0]/2,u-n[1]/2,n[0],n[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var n=this.shape,i=n.points,o=n.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=i.length/2-1;u>=0;u--){var v=u*2,c=i[v]-s/2,h=i[v+1]-l/2;if(t>=c&&r>=h&&t<=c+s&&r<=h+l)return u}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.points,i=r.size,o=i[0],s=i[1],l=1/0,u=1/0,v=-1/0,c=-1/0,h=0;h<n.length;){var f=n[h++],p=n[h++];l=Math.min(f,l),v=Math.max(f,v),u=Math.min(p,u),c=Math.max(p,c)}t=this._rect=new vt(l-o/2,u-s/2,v-l+o,c-u+s)}return t},e}(_t),hh=function(){function a(){this.group=new U}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var n=(r.endIndex-r.startIndex)*2,i=r.startIndex*4*2;t=new Float32Array(t.buffer,i,n)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var n=this._newAdded[0],i=t.getLayout("points"),o=n&&n.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+i.length);l.set(o),l.set(i,s),n.endIndex=e.end,n.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:i}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new ch({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;r=r||{};var i=t.getVisual("symbolSize");e.setShape("size",i instanceof Array?i:[i,i]),e.softClipShape=r.clipShape||null,e.symbolProxy=Yt(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<xl;e.useStyle(n.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=at(e);u.seriesIndex=n.seriesIndex,e.on("mousemove",function(v){u.dataIndex=null;var c=e.hoverDataIdx;c>=0&&(u.dataIndex=c+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),fh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateSymbolDraw(i,t);o.incrementalPrepareUpdate(i),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4)return{update:!0};var o=Xr("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout(i)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var n=this._symbolDraw,i=r.pipelineContext,o=i.large;return(!n||o!==this._isLargeDraw)&&(n&&n.remove(),n=this._symbolDraw=o?new hh:new Zr,this._isLargeDraw=o,this.group.removeAll()),this.group.add(n.group),n},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(ht);const ph=fh;function gh(a){Y(Bu),a.registerSeriesModel(uh),a.registerChartView(ph),a.registerLayout(Xr("scatter"))}function dh(a){a.eachSeriesByType("radar",function(e){var t=e.getData(),r=[],n=e.coordinateSystem;if(n){var i=n.getIndicatorAxes();L(i,function(o,s){t.each(t.mapDimension(i[s].dim),function(l,u){r[u]=r[u]||[];var v=n.dataToPoint(l,s);r[u][s]=ki(v)?v:Gi(n)})}),t.each(function(o){var s=Fu(r[o],function(l){return ki(l)})||Gi(n);r[o].push(s.slice()),t.setItemLayout(o,r[o])})}})}function ki(a){return!isNaN(a[0])&&!isNaN(a[1])}function Gi(a){return[a.cx,a.cy]}function yh(a){var e=a.polar;if(e){F(e)||(e=[e]);var t=[];L(e,function(r,n){r.indicator?(r.type&&!r.shape&&(r.shape=r.type),a.radar=a.radar||[],F(a.radar)||(a.radar=[a.radar]),a.radar.push(r)):t.push(r)}),a.polar=t}L(a.series,function(r){r&&r.type==="radar"&&r.polarIndex&&(r.radarIndex=r.polarIndex)})}var mh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(h,f){var p=h.getItemVisual(f,"symbol")||"circle";if(p!=="none"){var g=or(h.getItemVisual(f,"symbolSize")),d=Yt(p,-1,-1,2,2),y=h.getItemVisual(f,"symbolRotate")||0;return d.attr({style:{strokeNoScale:!0},z2:100,scaleX:g[0]/2,scaleY:g[1]/2,rotation:y*Math.PI/180||0}),d}}function v(h,f,p,g,d,y){p.removeAll();for(var S=0;S<f.length-1;S++){var m=u(g,d);m&&(m.__dimIdx=S,h[S]?(m.setPosition(h[S]),qr[y?"initProps":"updateProps"](m,{x:f[S][0],y:f[S][1]},t,d)):m.setPosition(f[S]),p.add(m))}}function c(h){return O(h,function(f){return[i.cx,i.cy]})}s.diff(l).add(function(h){var f=s.getItemLayout(h);if(f){var p=new re,g=new fe,d={shape:{points:f}};p.shape.points=c(f),g.shape.points=c(f),wt(p,d,t,h),wt(g,d,t,h);var y=new U,S=new U;y.add(g),y.add(p),y.add(S),v(g.shape.points,f,S,s,h,!0),s.setItemGraphicEl(h,y)}}).update(function(h,f){var p=l.getItemGraphicEl(f),g=p.childAt(0),d=p.childAt(1),y=p.childAt(2),S={shape:{points:s.getItemLayout(h)}};S.shape.points&&(v(g.shape.points,S.shape.points,y,s,h,!1),ue(d),ue(g),lt(g,S,t),lt(d,S,t),s.setItemGraphicEl(h,p))}).remove(function(h){o.remove(l.getItemGraphicEl(h))}).execute(),s.eachItemGraphicEl(function(h,f){var p=s.getItemModel(f),g=h.childAt(0),d=h.childAt(1),y=h.childAt(2),S=s.getItemVisual(f,"style"),m=S.fill;o.add(h),g.useStyle(nt(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:m})),Ct(g,p,"lineStyle"),Ct(d,p,"areaStyle");var x=p.getModel("areaStyle"),_=x.isEmpty()&&x.parentModel.isEmpty();d.ignore=_,L(["emphasis","select","blur"],function(T){var I=p.getModel([T,"areaStyle"]),D=I.isEmpty()&&I.parentModel.isEmpty();d.ensureState(T).ignore=D&&_}),d.useStyle(nt(x.getAreaStyle(),{fill:m,opacity:.7,decal:S.decal}));var b=p.getModel("emphasis"),w=b.getModel("itemStyle").getItemStyle();y.eachChild(function(T){if(T instanceof ke){var I=T.style;T.useStyle($({image:I.image,x:I.x,y:I.y,width:I.width,height:I.height},S))}else T.useStyle(S),T.setColor(m),T.style.strokeNoScale=!0;var D=T.ensureState("emphasis");D.style=Ft(w);var A=s.getStore().get(s.getDimensionIndex(T.__dimIdx),f);(A==null||isNaN(A))&&(A=""),kt(T,Tt(p),{labelFetcher:s.hostModel,labelDataIndex:f,labelDimIndex:T.__dimIdx,defaultText:A,inheritColor:m,defaultOpacity:S.opacity})}),ct(h,b.get("focus"),b.get("blurScope"),b.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(ht);const Sh=mh;var xh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new Kr(tt(this.getData,this),tt(this.getRawData,this))},e.prototype.getInitialData=function(t,r){return sr(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,v=Wu(this,t);return It("section",{header:u,sortBlocks:!0,blocks:O(s,function(c){var h=i.get(i.mapDimension(c.dim),t);return It("nameValue",{markerType:"subItem",markerColor:v,name:c.name,value:h,sortParam:h})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var r=this.getData(),n=this.coordinateSystem,i=r.getValues(O(n.dimensions,function(u){return r.mapDimension(u)}),t),o=0,s=i.length;o<s;o++)if(!isNaN(i[o])){var l=n.getIndicatorAxes();return n.coordToPoint(l[o].dataToCoord(i[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(pt);const _h=xh;var ze=$u.value;function xr(a,e){return nt({show:e},a)}var bh=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),r=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),v=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),h=this.get("triggerEvent"),f=O(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var g=l;p.color!=null&&(g=nt({color:p.color},l));var d=Xt(Ft(p),{boundaryGap:t,splitNumber:r,scale:n,axisLine:i,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:g,triggerEvent:h},!1);if(rt(v)){var y=d.name;d.name=v.replace("{value}",y!=null?y:"")}else ot(v)&&(d.name=v(d.name,d));var S=new Wt(d,null,this.ecModel);return qt(S,Mn.prototype),S.mainType="radar",S.componentIndex=this.componentIndex,S},this);this._indicatorModels=f},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:Xt({lineStyle:{color:"#bbb"}},ze.axisLine),axisLabel:xr(ze.axisLabel,!1),axisTick:xr(ze.axisTick,!1),splitLine:xr(ze.splitLine,!0),splitArea:xr(ze.splitArea,!0),indicator:[]},e}(Kt);const wh=bh;var Th=["axisLine","axisTickLabel","axisName"],Ih=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes(),i=O(n,function(o){var s=o.model.get("showName")?o.name:"",l=new En(o.model,{axisName:s,position:[r.cx,r.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});L(i,function(o){L(Th,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var r=t.coordinateSystem,n=r.getIndicatorAxes();if(!n.length)return;var i=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),v=o.get("show"),c=s.get("show"),h=l.get("color"),f=u.get("color"),p=F(h)?h:[h],g=F(f)?f:[f],d=[],y=[];function S(P,R,N){var k=N%R.length;return P[k]=P[k]||[],k}if(i==="circle")for(var m=n[0].getTicksCoords(),x=r.cx,_=r.cy,b=0;b<m.length;b++){if(v){var w=S(d,p,b);d[w].push(new jr({shape:{cx:x,cy:_,r:m[b].coord}}))}if(c&&b<m.length-1){var w=S(y,g,b);y[w].push(new Hu({shape:{cx:x,cy:_,r0:m[b].coord,r:m[b+1].coord}}))}}else for(var T,I=O(n,function(P,R){var N=P.getTicksCoords();return T=T==null?N.length-1:Math.min(N.length-1,T),O(N,function(k){return r.coordToPoint(k.coord,R)})}),D=[],b=0;b<=T;b++){for(var A=[],M=0;M<n.length;M++)A.push(I[M][b]);if(A[0]&&A.push(A[0].slice()),v){var w=S(d,p,b);d[w].push(new fe({shape:{points:A}}))}if(c&&D){var w=S(y,g,b-1);y[w].push(new re({shape:{points:A.concat(D)}}))}D=A.slice().reverse()}var E=l.getLineStyle(),C=u.getAreaStyle();L(y,function(P,R){this.group.add(nn(P,{style:nt({stroke:"none",fill:g[R%g.length]},C),silent:!0}))},this),L(d,function(P,R){this.group.add(nn(P,{style:nt({fill:"none",stroke:p[R%p.length]},E),silent:!0}))},this)},e.type="radar",e}(jt);const Dh=Ih;var Ah=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t,r,n)||this;return i.type="value",i.angle=0,i.name="",i}return e}(Jr);const Lh=Ah;var Ch=function(){function a(e,t,r){this.dimensions=[],this._model=e,this._indicatorAxes=O(e.getIndicatorModels(),function(n,i){var o="indicator_"+i,s=new Lh(o,new on);return s.name=n.get("name"),s.model=n,n.axis=s,this.dimensions.push(o),s},this),this.resize(e,r)}return a.prototype.getIndicatorAxes=function(){return this._indicatorAxes},a.prototype.dataToPoint=function(e,t){var r=this._indicatorAxes[t];return this.coordToPoint(r.dataToCoord(e),t)},a.prototype.coordToPoint=function(e,t){var r=this._indicatorAxes[t],n=r.angle,i=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[i,o]},a.prototype.pointToData=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,n=Math.sqrt(t*t+r*r);t/=n,r/=n;for(var i=Math.atan2(-r,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var v=this._indicatorAxes[u],c=Math.abs(i-v.angle);c<o&&(s=v,l=u,o=c)}return[l,+(s&&s.coordToData(n))]},a.prototype.resize=function(e,t){var r=e.get("center"),n=t.getWidth(),i=t.getHeight(),o=Math.min(n,i)/2;this.cx=z(r[0],n),this.cy=z(r[1],i),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(rt(s)||Zt(s))&&(s=[0,s]),this.r0=z(s[0],o),this.r=z(s[1],o),L(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var v=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;v=Math.atan2(Math.sin(v),Math.cos(v)),l.angle=v},this)},a.prototype.update=function(e,t){var r=this._indicatorAxes,n=this._model;L(r,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==n)){var u=s.getData();L(r,function(v){v.scale.unionExtentFromData(u,u.mapDimension(v.dim))})}},this);var i=n.get("splitNumber"),o=new on;o.setExtent(0,i),o.setInterval(1),L(r,function(s,l){Uu(s.scale,s.model,o)})},a.prototype.convertToPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.convertFromPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.create=function(e,t){var r=[];return e.eachComponent("radar",function(n){var i=new a(n,e,t);r.push(i),n.coordinateSystem=i}),e.eachSeriesByType("radar",function(n){n.get("coordinateSystem")==="radar"&&(n.coordinateSystem=r[n.get("radarIndex")||0])}),r},a.dimensions=[],a}();const Ph=Ch;function Mh(a){a.registerCoordinateSystem("radar",Ph),a.registerComponentModel(wh),a.registerComponentView(Dh),a.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(r){t.setItemVisual(r,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function Eh(a){Y(Mh),a.registerChartView(Sh),a.registerSeriesModel(_h),a.registerLayout(dh),a.registerProcessor(Qr("radar")),a.registerPreprocessor(yh)}function Yn(a,e,t){var r=a.target;r.x+=e,r.y+=t,r.dirty()}function Xn(a,e,t,r){var n=a.target,i=a.zoomLimit,o=a.zoom=a.zoom||1;if(o*=e,i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/a.zoom;a.zoom=o,n.x-=(t-n.x)*(u-1),n.y-=(r-n.y)*(u-1),n.scaleX*=u,n.scaleY*=u,n.dirty()}function _l(a){if(rt(a)){var e=new DOMParser;a=e.parseFromString(a,"text/xml")}var t=a;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var da,Nr={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},zi=ir(Nr),Vr={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Oi=ir(Vr),Rh=function(){function a(){this._defs={},this._root=null}return a.prototype.parse=function(e,t){t=t||{};var r=_l(e);this._defsUsePending=[];var n=new U;this._root=n;var i=[],o=r.getAttribute("viewBox")||"",s=parseFloat(r.getAttribute("width")||t.width),l=parseFloat(r.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),At(r,n,null,!0,!1);for(var u=r.firstChild;u;)this._parseNode(u,n,i,null,!1,!1),u=u.nextSibling;kh(this._defs,this._defsUsePending),this._defsUsePending=[];var v,c;if(o){var h=sa(o);h.length>=4&&(v={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(v&&s!=null&&l!=null&&(c=wl(v,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=n;n=new U,n.add(f),f.scaleX=f.scaleY=c.scale,f.x=c.x,f.y=c.y}return!t.ignoreRootClip&&s!=null&&l!=null&&n.setClipPath(new Dt({shape:{x:0,y:0,width:s,height:l}})),{root:n,width:s,height:l,viewBoxRect:v,viewBoxTransform:c,named:i}},a.prototype._parseNode=function(e,t,r,n,i,o){var s=e.nodeName.toLowerCase(),l,u=n;if(s==="defs"&&(i=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!i){var v=da[s];if(v&&gt(da,s)){l=v.call(this,e,t);var c=e.getAttribute("name");if(c){var h={name:c,namedFrom:null,svgNodeTagLower:s,el:l};r.push(h),s==="g"&&(u=h)}else n&&r.push({name:n.name,namedFrom:n,svgNodeTagLower:s,el:l});t.add(l)}}var f=Bi[s];if(f&&gt(Bi,s)){var p=f.call(this,e),g=e.getAttribute("id");g&&(this._defs[g]=p)}}if(l&&l.isGroup)for(var d=e.firstChild;d;)d.nodeType===1?this._parseNode(d,l,r,u,i,o):d.nodeType===3&&o&&this._parseText(d,l),d=d.nextSibling},a.prototype._parseText=function(e,t){var r=new Yu({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Pt(t,r),At(e,r,this._defsUsePending,!1,!1),Nh(r,t);var n=r.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,r.scaleX*=i/9,r.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=o;var s=r.getBoundingRect();return this._textX+=s.width,t.add(r),r},a.internalField=function(){da={g:function(e,t){var r=new U;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r},rect:function(e,t){var r=new Dt;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(e,t){var r=new jr;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),r.silent=!0,r},line:function(e,t){var r=new xe;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(e,t){var r=new Xu;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(e,t){var r=e.getAttribute("points"),n;r&&(n=$i(r));var i=new re({shape:{points:n||[]},silent:!0});return Pt(t,i),At(e,i,this._defsUsePending,!1,!1),i},polyline:function(e,t){var r=e.getAttribute("points"),n;r&&(n=$i(r));var i=new fe({shape:{points:n||[]},silent:!0});return Pt(t,i),At(e,i,this._defsUsePending,!1,!1),i},image:function(e,t){var r=new ke;return Pt(t,r),At(e,r,this._defsUsePending,!1,!1),r.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),r.silent=!0,r},text:function(e,t){var r=e.getAttribute("x")||"0",n=e.getAttribute("y")||"0",i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(i),this._textY=parseFloat(n)+parseFloat(o);var s=new U;return Pt(t,s),At(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var r=e.getAttribute("x"),n=e.getAttribute("y");r!=null&&(this._textX=parseFloat(r)),n!=null&&(this._textY=parseFloat(n));var i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new U;return Pt(t,s),At(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(i),this._textY+=parseFloat(o),s},path:function(e,t){var r=e.getAttribute("d")||"",n=Zu(r);return Pt(t,n),At(e,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),a}(),Bi={lineargradient:function(a){var e=parseInt(a.getAttribute("x1")||"0",10),t=parseInt(a.getAttribute("y1")||"0",10),r=parseInt(a.getAttribute("x2")||"10",10),n=parseInt(a.getAttribute("y2")||"0",10),i=new Zs(e,t,r,n);return Fi(a,i),Wi(a,i),i},radialgradient:function(a){var e=parseInt(a.getAttribute("cx")||"0",10),t=parseInt(a.getAttribute("cy")||"0",10),r=parseInt(a.getAttribute("r")||"0",10),n=new qu(e,t,r);return Fi(a,n),Wi(a,n),n}};function Fi(a,e){var t=a.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function Wi(a,e){for(var t=a.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var r=t.getAttribute("offset"),n=void 0;r&&r.indexOf("%")>0?n=parseInt(r,10)/100:r?n=parseFloat(r):n=0;var i={};bl(t,i,i);var o=i.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}t=t.nextSibling}}function Pt(a,e){a&&a.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),nt(e.__inheritedStyle,a.__inheritedStyle))}function $i(a){for(var e=sa(a),t=[],r=0;r<e.length;r+=2){var n=parseFloat(e[r]),i=parseFloat(e[r+1]);t.push([n,i])}return t}function At(a,e,t,r,n){var i=e,o=i.__inheritedStyle=i.__inheritedStyle||{},s={};a.nodeType===1&&(Oh(a,e),bl(a,o,s),r||Bh(a,o,s)),i.style=i.style||{},o.fill!=null&&(i.style.fill=Hi(i,"fill",o.fill,t)),o.stroke!=null&&(i.style.stroke=Hi(i,"stroke",o.stroke,t)),L(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(i.style[l]=parseFloat(o[l]))}),L(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(i.style[l]=o[l])}),n&&(i.__selfStyle=s),o.lineDash&&(i.style.lineDash=O(sa(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(i.invisible=!0),o.display==="none"&&(i.ignore=!0)}function Nh(a,e){var t=e.__selfStyle;if(t){var r=t.textBaseline,n=r;!r||r==="auto"||r==="baseline"?n="alphabetic":r==="before-edge"||r==="text-before-edge"?n="top":r==="after-edge"||r==="text-after-edge"?n="bottom":(r==="central"||r==="mathematical")&&(n="middle"),a.style.textBaseline=n}var i=e.__inheritedStyle;if(i){var o=i.textAlign,s=o;o&&(o==="middle"&&(s="center"),a.style.textAlign=s)}}var Vh=/^url\(\s*#(.*?)\)/;function Hi(a,e,t,r){var n=t&&t.match(Vh);if(n){var i=Ku(n[1]);r.push([a,e,i]);return}return t==="none"&&(t=null),t}function kh(a,e){for(var t=0;t<e.length;t++){var r=e[t];r[0].style[r[1]]=a[r[2]]}}var Gh=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function sa(a){return a.match(Gh)||[]}var zh=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,ya=Math.PI/180;function Oh(a,e){var t=a.getAttribute("transform");if(t){t=t.replace(/,/g," ");var r=[],n=null;t.replace(zh,function(c,h,f){return r.push(h,f),""});for(var i=r.length-1;i>0;i-=2){var o=r[i],s=r[i-1],l=sa(o);switch(n=n||lr(),s){case"translate":Ee(n,n,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":qs(n,n,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":Rn(n,n,-parseFloat(l[0])*ya,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*ya);xi(n,[1,0,u,1,0,0],n);break;case"skewY":var v=Math.tan(parseFloat(l[0])*ya);xi(n,[1,v,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(l[0]),n[1]=parseFloat(l[1]),n[2]=parseFloat(l[2]),n[3]=parseFloat(l[3]),n[4]=parseFloat(l[4]),n[5]=parseFloat(l[5]);break}}e.setLocalTransform(n)}}var Ui=/([^\s:;]+)\s*:\s*([^:;]+)/g;function bl(a,e,t){var r=a.getAttribute("style");if(r){Ui.lastIndex=0;for(var n;(n=Ui.exec(r))!=null;){var i=n[1],o=gt(Nr,i)?Nr[i]:null;o&&(e[o]=n[2]);var s=gt(Vr,i)?Vr[i]:null;s&&(t[s]=n[2])}}}function Bh(a,e,t){for(var r=0;r<zi.length;r++){var n=zi[r],i=a.getAttribute(n);i!=null&&(e[Nr[n]]=i)}for(var r=0;r<Oi.length;r++){var n=Oi[r],i=a.getAttribute(n);i!=null&&(t[Vr[n]]=i)}}function wl(a,e){var t=e.width/a.width,r=e.height/a.height,n=Math.min(t,r);return{scale:n,x:-(a.x+a.width/2)*n+(e.x+e.width/2),y:-(a.y+a.height/2)*n+(e.y+e.height/2)}}function Fh(a,e){var t=new Rh;return t.parse(a,e)}var Wh=q(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),$h=function(){function a(e,t){this.type="geoSVG",this._usedGraphicMap=q(),this._freedGraphics=[],this._mapName=e,this._parsedXML=_l(t)}return a.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=Uh(e.named),r=t.regions,n=t.regionsMap;this._regions=r,this._regionsMap=n}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},a.prototype._buildGraphic=function(e){var t,r;try{t=e&&Fh(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},r=t.root,Ks(r!=null)}catch(d){throw new Error(`Invalid svg format
`+d.message)}var n=new U;n.add(r),n.isGeoSVGGraphicRoot=!0;var i=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,v=void 0,c=void 0,h=void 0;if(i!=null?(u=0,c=i):s&&(u=s.x,c=s.width),o!=null?(v=0,h=o):s&&(v=s.y,h=s.height),u==null||v==null){var f=r.getBoundingRect();u==null&&(u=f.x,c=f.width),v==null&&(v=f.y,h=f.height)}l=this._boundingRect=new vt(u,v,c,h)}if(s){var p=wl(s,l);r.scaleX=r.scaleY=p.scale,r.x=p.x,r.y=p.y}n.setClipPath(new Dt({shape:l.plain()}));var g=[];return L(t.named,function(d){Wh.get(d.svgNodeTagLower)!=null&&(g.push(d),Hh(d.el))}),{root:n,boundingRect:l,named:g}},a.prototype.useGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);return r||(r=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,r),r)},a.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);r&&(t.removeKey(e),this._freedGraphics.push(r))},a}();function Hh(a){a.silent=!1,a.isGroup&&a.traverse(function(e){e.silent=!1})}function Uh(a){var e=[],t=q();return L(a,function(r){if(r.namedFrom==null){var n=new ju(r.name,r.el);e.push(n),t.set(r.name,n)}}),{regions:e,regionsMap:t}}var dn=[126,25],Yi="南海诸岛",ge=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var pe=0;pe<ge.length;pe++)for(var be=0;be<ge[pe].length;be++)ge[pe][be][0]/=10.5,ge[pe][be][1]/=-10.5/.75,ge[pe][be][0]+=dn[0],ge[pe][be][1]+=dn[1];function Yh(a,e){if(a==="china"){for(var t=0;t<e.length;t++)if(e[t].name===Yi)return;e.push(new Ju(Yi,O(ge,function(r){return{type:"polygon",exterior:r}}),dn))}}var Xh={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Zh(a,e){if(a==="china"){var t=Xh[e.name];if(t){var r=e.getCenter();r[0]+=t[0]/10.5,r[1]+=-t[1]/(10.5/.75),e.setCenter(r)}}}var qh=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function Kh(a,e){a==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:qh[0]})}var jh="name",Jh=function(){function a(e,t,r){this.type="geoJSON",this._parsedMap=q(),this._mapName=e,this._specialAreas=r,this._geoJSON=tf(t)}return a.prototype.load=function(e,t){t=t||jh;var r=this._parsedMap.get(t);if(!r){var n=this._parseToRegions(t);r=this._parsedMap.set(t,{regions:n,boundingRect:Qh(n)})}var i=q(),o=[];return L(r.regions,function(s){var l=s.name;e&&gt(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),i.set(l,s)}),{regions:o,boundingRect:r.boundingRect||new vt(0,0,0,0),regionsMap:i}},a.prototype._parseToRegions=function(e){var t=this._mapName,r=this._geoJSON,n;try{n=r?Qu(r,e):[]}catch(i){throw new Error(`Invalid geoJson format
`+i.message)}return Yh(t,n),L(n,function(i){var o=i.name;Zh(t,i),Kh(t,i);var s=this._specialAreas&&this._specialAreas[o];s&&i.transformTo(s.left,s.top,s.width,s.height)},this),n},a.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},a}();function Qh(a){for(var e,t=0;t<a.length;t++){var r=a[t].getBoundingRect();e=e||r.clone(),e.union(r)}return e}function tf(a){return rt(a)?typeof JSON!="undefined"&&JSON.parse?JSON.parse(a):new Function("return ("+a+");")():a}var Oe=q();const ae={registerMap:function(a,e,t){if(e.svg){var r=new $h(a,e.svg);Oe.set(a,r)}else{var n=e.geoJson||e.geoJSON;n&&!e.features?t=e.specialAreas:n=e;var r=new Jh(a,n,t);Oe.set(a,r)}},getGeoResource:function(a){return Oe.get(a)},getMapForUser:function(a){var e=Oe.get(a);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(a,e,t){var r=Oe.get(a);if(r)return r.load(e,t)}};var Zn=["rect","circle","line","ellipse","polygon","polyline","path"],ef=q(Zn),rf=q(Zn.concat(["g"])),af=q(Zn.concat(["g"])),Tl=xt();function _r(a){var e=a.getItemStyle(),t=a.get("areaColor");return t!=null&&(e.fill=t),e}function Xi(a){var e=a.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var nf=function(){function a(e){var t=new U;this.uid=tv("ec_map_draw"),this._controller=new ta(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new U),t.add(this._svgGroup=new U)}return a.prototype.draw=function(e,t,r,n,i){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(y){!s&&y.getHostGeoModel()===e&&(s=y.getData())});var l=e.coordinateSystem,u=this._regionsGroup,v=this.group,c=l.getTransformInfo(),h=c.raw,f=c.roam,p=!u.childAt(0)||i;p?(v.x=f.x,v.y=f.y,v.scaleX=f.scaleX,v.scaleY=f.scaleY,v.dirty()):lt(v,f,e);var g=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,d={api:r,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:g,isGeo:o,transformInfoRaw:h};l.resourceType==="geoJSON"?this._buildGeoJSON(d):l.resourceType==="geoSVG"&&this._buildSVG(d),this._updateController(e,t,r),this._updateMapSelectHandler(e,u,r,n)},a.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=q(),r=q(),n=this._regionsGroup,i=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function v(f,p){return p&&(f=p(f)),f&&[f[0]*i.scaleX+i.x,f[1]*i.scaleY+i.y]}function c(f){for(var p=[],g=!u&&l&&l.project,d=0;d<f.length;++d){var y=v(f[d],g);y&&p.push(y)}return p}function h(f){return{shape:{points:c(f)}}}n.removeAll(),L(e.geo.regions,function(f){var p=f.name,g=t.get(p),d=r.get(p)||{},y=d.dataIdx,S=d.regionModel;g||(g=t.set(p,new U),n.add(g),y=s?s.indexOfName(p):null,S=e.isGeo?o.getRegionModel(p):s?s.getItemModel(y):null,r.set(p,{dataIdx:y,regionModel:S}));var m=[],x=[];L(f.geometries,function(w){if(w.type==="polygon"){var T=[w.exterior].concat(w.interiors||[]);u&&(T=Qi(T,u)),L(T,function(D){m.push(new re(h(D)))})}else{var I=w.points;u&&(I=Qi(I,u,!0)),L(I,function(D){x.push(new fe(h(D)))})}});var _=v(f.getCenter(),l&&l.project);function b(w,T){if(w.length){var I=new av({culling:!0,segmentIgnoreThreshold:1,shape:{paths:w}});g.add(I),Zi(e,I,y,S),qi(e,I,p,S,o,y,_),T&&(Xi(I),L(I.states,Xi))}}b(m),b(x,!0)}),t.each(function(f,p){var g=r.get(p),d=g.dataIdx,y=g.regionModel;Ki(e,f,p,y,o,d),ji(e,f,p,y,o),Ji(e,f,p,y,o)},this)},a.prototype._buildSVG=function(e){var t=e.geo.map,r=e.transformInfoRaw;this._svgGroup.x=r.x,this._svgGroup.y=r.y,this._svgGroup.scaleX=r.scaleX,this._svgGroup.scaleY=r.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var n=this._svgDispatcherMap=q(),i=!1;L(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,v=o.svgNodeTagLower,c=o.el,h=u?u.indexOfName(s):null,f=l.getRegionModel(s);if(ef.get(v)!=null&&c instanceof Ke&&Zi(e,c,h,f),c instanceof Ke&&(c.culling=!0),c.z2EmphasisLift=0,!o.namedFrom&&(af.get(v)!=null&&qi(e,c,s,f,l,h,null),Ki(e,c,s,f,l,h),ji(e,c,s,f,l),rf.get(v)!=null)){var p=Ji(e,c,s,f,l);p==="self"&&(i=!0);var g=n.get(s)||n.set(s,[]);g.push(c)}},this),this._enableBlurEntireSVG(i,e)},a.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var r=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),n=r.opacity;this._svgGraphicRecord.root.traverse(function(i){if(!i.isGroup){Re(i);var o=i.ensureState("blur").style||{};o.opacity==null&&n!=null&&(o.opacity=n),i.ensureState("emphasis")}})}},a.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},a.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var r=t.coordinateSystem;if(r.resourceType==="geoJSON"){var n=this._regionsGroupByName;if(n){var i=n.get(e);return i?[i]:[]}}else if(r.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},a.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},a.prototype._useSVG=function(e){var t=ae.getGeoResource(e);if(t&&t.type==="geoSVG"){var r=t.useGraphic(this.uid);this._svgGroup.add(r.root),this._svgGraphicRecord=r,this._svgMapName=e}},a.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=ae.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},a.prototype._updateController=function(e,t,r){var n=e.coordinateSystem,i=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=n.getZoom(),i.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}i.off("pan").on("pan",function(u){this._mouseDownFlag=!1,Yn(o,u.dx,u.dy),r.dispatchAction($(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),i.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,Xn(o,u.scale,u.originX,u.originY),r.dispatchAction($(l(),{zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),i.setPointerChecker(function(u,v,c){return n.containPoint([v,c])&&!Nn(u,r,e)})},a.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=Tl(t).ignore)})},a.prototype._updateMapSelectHandler=function(e,t,r,n){var i=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){i._mouseDownFlag=!0}),t.on("click",function(o){i._mouseDownFlag&&(i._mouseDownFlag=!1)}))},a}();function Zi(a,e,t,r){var n=r.getModel("itemStyle"),i=r.getModel(["emphasis","itemStyle"]),o=r.getModel(["blur","itemStyle"]),s=r.getModel(["select","itemStyle"]),l=_r(n),u=_r(i),v=_r(s),c=_r(o),h=a.data;if(h){var f=h.getItemVisual(t,"style"),p=h.getItemVisual(t,"decal");a.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Vn(p,a.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=v,e.ensureState("blur").style=c,Re(e)}function qi(a,e,t,r,n,i,o){var s=a.data,l=a.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),i)),v=s&&s.getItemLayout(i);if(l||u||v&&v.showLabel){var c=l?t:i,h=void 0;(!s||i>=0)&&(h=n);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;kt(e,Tt(r),{labelFetcher:h,labelDataIndex:c,defaultText:t},f);var p=e.getTextContent();if(p&&(Tl(p).ignore=p.ignore,e.textConfig&&o)){var g=e.getBoundingRect().clone();e.textConfig.layoutRect=g,e.textConfig.position=[(o[0]-g.x)/g.width*100+"%",(o[1]-g.y)/g.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function Ki(a,e,t,r,n,i){a.data?a.data.setItemGraphicEl(i,e):at(e).eventData={componentType:"geo",componentIndex:n.componentIndex,geoIndex:n.componentIndex,name:t,region:r&&r.option||{}}}function ji(a,e,t,r,n){a.data||ev({el:e,componentModel:n,itemName:t,itemTooltipOption:r.get("tooltip")})}function Ji(a,e,t,r,n){e.highDownSilentOnTouch=!!n.get("selectedMode");var i=r.getModel("emphasis"),o=i.get("focus");return ct(e,o,i.get("blurScope"),i.get("disabled")),a.isGeo&&rv(e,n,t),o}function Qi(a,e,t){var r=[],n;function i(){n=[]}function o(){n.length&&(r.push(n),n=[])}var s=e({polygonStart:i,polygonEnd:o,lineStart:i,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&n.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),L(a,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),r}const Il=nf;var of=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){if(!(i&&i.type==="mapToggleSelect"&&i.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&i.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),i&&i.type==="geoRoam"&&i.componentType==="series"&&i.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new Il(n);o.add(s.group),s.draw(t,r,n,this,i),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&r.getComponent("legend")&&this._renderSymbols(t,r,n)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,r,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=i.getItemLayout(l);if(!(!u||!u.point)){var v=u.point,c=u.offset,h=new jr({style:{fill:t.getData().getVisual("style").fill},shape:{cx:v[0]+c*9,cy:v[1],r:3},silent:!0,z2:8+(c?0:ur+1)});if(!c){var f=t.mainSeries.getData(),p=i.getName(l),g=f.indexOfName(p),d=i.getItemModel(l),y=d.getModel("label"),S=f.getItemGraphicEl(g);kt(h,Tt(d),{labelFetcher:{getFormattedLabel:function(m,x){return t.getFormattedLabel(g,x)}},defaultText:p}),h.disableLabelAnimation=!0,y.get("position")||h.setTextConfig({position:"bottom"}),S.onHoverStateChange=function(m){js(h,m)}}o.add(h)}}})},e.type="map",e}(ht);const sf=of;var lf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(r){if(r!=null){var n=this.getData().getName(r),i=this.coordinateSystem,o=i.getRegion(n);return o&&i.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var r=sr(this,{coordDimensions:["value"],encodeDefaulter:st(Js,this)}),n=q(),i=[],o=0,s=r.count();o<s;o++){var l=r.getName(o);n.set(l,!0)}var u=ae.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return L(u.regions,function(v){var c=v.name;n.get(c)||i.push(c)}),r.appendValues([],i),r},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var r=this.getData();return r.get(r.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var r=this.getData();return r.getItemModel(r.indexOfName(t))},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData(),o=this.getRawValue(t),s=i.getName(t),l=this.seriesGroup,u=[],v=0;v<l.length;v++){var c=l[v].originalData.indexOfName(s),h=i.mapDimension("value");isNaN(l[v].originalData.get(h,c))||u.push(l[v].name)}return It("section",{header:u.join(", "),noHeader:!u.length,blocks:[It("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var r=t.icon||"roundRect",n=Yt(r,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",r.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(pt);const uf=lf;function vf(a,e){var t={};return L(a,function(r){r.each(r.mapDimension("value"),function(n,i){var o="ec-"+r.getName(i);t[o]=t[o]||[],isNaN(n)||t[o].push(n)})}),a[0].map(a[0].mapDimension("value"),function(r,n){for(var i="ec-"+a[0].getName(n),o=0,s=1/0,l=-1/0,u=t[i].length,v=0;v<u;v++)s=Math.min(s,t[i][v]),l=Math.max(l,t[i][v]),o+=t[i][v];var c;return e==="min"?c=s:e==="max"?c=l:e==="average"?c=o/u:c=o,u===0?NaN:c})}function cf(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getHostGeoModel(),n=r?"o"+r.id:"i"+t.getMapType();(e[n]=e[n]||[]).push(t)}),L(e,function(t,r){for(var n=vf(O(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),i=0;i<t.length;i++)t[i].originalData=t[i].getData();for(var i=0;i<t.length;i++)t[i].seriesGroup=t,t[i].needsDrawMap=i===0&&!t[i].getHostGeoModel(),t[i].setData(n.cloneShallow()),t[i].mainSeries=t[0]})}function hf(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getMapType();if(!(t.getHostGeoModel()||e[r])){var n={};L(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&a.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,v){var c=l.getName(v),h=s.getRegion(c);if(!(!h||isNaN(u))){var f=n[c]||0,p=s.dataToPoint(h.getCenter());n[c]=f+1,l.setItemLayout(v,{point:p,offset:f})}})});var i=t.getData();i.each(function(o){var s=i.getName(o),l=i.getItemLayout(o)||{};l.showLabel=!n[s],i.setItemLayout(o,l)}),e[r]=!0}})}var to=sn,gr=function(a){V(e,a);function e(t){var r=a.call(this)||this;return r.type="view",r.dimensions=["x","y"],r._roamTransformable=new Le,r._rawTransformable=new Le,r.name=t,r}return e.prototype.setBoundingRect=function(t,r,n,i){return this._rect=new vt(t,r,n,i),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,r,n,i){this._transformTo(t,r,n,i),this._viewRect=new vt(t,r,n,i)},e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new vt(t,r,n,i));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,r){t&&(this._center=[z(t[0],r.getWidth()),z(t[1],r.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var r=this.zoomLimit;r&&(r.max!=null&&(t=Math.min(r.max,t)),r.min!=null&&(t=Math.max(r.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),r=t.x+t.width/2,n=t.y+t.height/2;return[r,n]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),r=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=sn([],i,t),n=sn([],n,t),r.originX=i[0],r.originY=i[1],r.x=n[0]-i[0],r.y=n[1]-i[1],r.scaleX=r.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,r=this._rawTransformable;r.parent=t,t.updateTransform(),r.updateTransform(),nv(this.transform||(this.transform=[]),r.transform||lr()),this._rawTransform=r.getLocalTransform(),this.invTransform=this.invTransform||[],iv(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,r=this._roamTransformable,n=new Le;return n.transform=r.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,r,n){var i=r?this._rawTransform:this.transform;return n=n||[],i?to(n,t,i):mt(n,t)},e.prototype.pointToData=function(t){var r=this.invTransform;return r?to([],t,r):[t[0],t[1]]},e.prototype.convertToPixel=function(t,r,n){var i=eo(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=eo(r);return i===this?i.pointToData(n):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(Le);function eo(a){var e=a.seriesModel;return e?e.coordinateSystem:null}var ff={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},Dl=["lng","lat"],Al=function(a){V(e,a);function e(t,r,n){var i=a.call(this,t)||this;i.dimensions=Dl,i.type="geo",i._nameCoordMap=q(),i.map=r;var o=n.projection,s=ae.load(r,n.nameMap,n.nameProperty),l=ae.getGeoResource(r);i.resourceType=l?l.type:null;var u=i.regions=s.regions,v=ff[l.type];i._regionsMap=s.regionsMap,i.regions=s.regions,i.projection=o;var c;if(o)for(var h=0;h<u.length;h++){var f=u[h].getBoundingRect(o);c=c||f.clone(),c.union(f)}else c=s.boundingRect;return i.setBoundingRect(c.x,c.y,c.width,c.height),i.aspectScale=o?1:Ut(n.aspectScale,v.aspectScale),i._invertLongitute=o?!1:v.invertLongitute,i}return e.prototype._transformTo=function(t,r,n,i){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new vt(t,r,n,i));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var r=this.regions,n=0;n<r.length;n++){var i=r[n];if(i.type==="geoJSON"&&i.contain(t))return r[n]}},e.prototype.addGeoCoord=function(t,r){this._nameCoordMap.set(t,r)},e.prototype.getGeoCoord=function(t){var r=this._regionsMap.get(t);return this._nameCoordMap.get(t)||r&&r.getCenter()},e.prototype.dataToPoint=function(t,r,n){if(rt(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,r,n)}},e.prototype.pointToData=function(t){var r=this.projection;return r&&(t=r.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return a.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,r,n){return a.prototype.dataToPoint.call(this,t,r,n)},e.prototype.convertToPixel=function(t,r,n){var i=ro(r);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,r,n){var i=ro(r);return i===this?i.pointToData(n):null},e}(gr);qt(Al,gr);function ro(a){var e=a.geoModel,t=a.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",ea).models[0]||{}).coordinateSystem:null}const ao=Al;function no(a,e){var t=a.get("boundingCoords");if(t!=null){var r=t[0],n=t[1];if(isFinite(r[0])&&isFinite(r[1])&&isFinite(n[0])&&isFinite(n[1])){var i=this.projection;if(i){var o=r[0],s=r[1],l=n[0],u=n[1];r=[1/0,1/0],n=[-1/0,-1/0];var v=function(b,w,T,I){for(var D=T-b,A=I-w,M=0;M<=100;M++){var E=M/100,C=i.project([b+D*E,w+A*E]);ov(r,r,C),sv(n,n,C)}};v(o,s,l,s),v(l,s,l,u),v(l,u,o,u),v(o,u,l,s)}this.setBoundingRect(r[0],r[1],n[0]-r[0],n[1]-r[1])}}var c=this.getBoundingRect(),h=a.get("layoutCenter"),f=a.get("layoutSize"),p=e.getWidth(),g=e.getHeight(),d=c.width/c.height*this.aspectScale,y=!1,S,m;h&&f&&(S=[z(h[0],p),z(h[1],g)],m=z(f,Math.min(p,g)),!isNaN(S[0])&&!isNaN(S[1])&&!isNaN(m)&&(y=!0));var x;if(y)x={},d>1?(x.width=m,x.height=m/d):(x.height=m,x.width=m*d),x.y=S[1]-x.height/2,x.x=S[0]-x.width/2;else{var _=a.getBoxLayoutParams();_.aspect=d,x=Jt(_,{width:p,height:g})}this.setViewRect(x.x,x.y,x.width,x.height),this.setCenter(a.get("center"),e),this.setZoom(a.get("zoom"))}function pf(a,e){L(e.get("geoCoord"),function(t,r){a.addGeoCoord(r,t)})}var gf=function(){function a(){this.dimensions=Dl}return a.prototype.create=function(e,t){var r=[];function n(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new ao(l+s,l,$({nameMap:o.get("nameMap")},n(o)));u.zoomLimit=o.get("scaleLimit"),r.push(u),o.coordinateSystem=u,u.model=o,u.resize=no,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=r[l]}});var i={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();i[s]=i[s]||[],i[s].push(o)}}),L(i,function(o,s){var l=O(o,function(v){return v.get("nameMap")}),u=new ao(s,s,$({nameMap:kn(l)},n(o[0])));u.zoomLimit=Et.apply(null,O(o,function(v){return v.get("scaleLimit")})),r.push(u),u.resize=no,u.resize(o[0],t),L(o,function(v){v.coordinateSystem=u,pf(u,v)})}),r},a.prototype.getFilledRegions=function(e,t,r,n){for(var i=(e||[]).slice(),o=q(),s=0;s<i.length;s++)o.set(i[s].name,i[s]);var l=ae.load(t,r,n);return L(l.regions,function(u){var v=u.name;!o.get(v)&&i.push({name:v})}),i},a}(),df=new gf;const Ll=df;var yf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=ae.getGeoResource(t.map);if(i&&i.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),ra(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,r=this.option;r.regions=Ll.getFilledRegions(r.regions,r.map,r.nameMap,r.nameProperty);var n={};this._optionModelMap=lv(r.regions||[],function(i,o){var s=o.name;return s&&(i.set(s,new Wt(o,t,t.ecModel)),o.selected&&(n[s]=!0)),i},q()),r.selectedMap||(r.selectedMap=n)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Wt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,r){var n=this.getRegionModel(t),i=r==="normal"?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};if(ot(i))return o.status=r,i(o);if(rt(i))return i.replace("{a}",t!=null?t:"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var r=this.option,n=r.selectedMode;if(n){n!=="multiple"&&(r.selectedMap=null);var i=r.selectedMap||(r.selectedMap={});i[t]=!0}},e.prototype.unSelect=function(t){var r=this.option.selectedMap;r&&(r[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var r=this.option.selectedMap;return!!(r&&r[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(Kt);const mf=yf;function io(a,e){return a.pointToProjected?a.pointToProjected(e):a.pointToData(e)}function qn(a,e,t,r){var n=a.getZoom(),i=a.getCenter(),o=e.zoom,s=a.projectedToPoint?a.projectedToPoint(i):a.dataToPoint(i);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,a.setCenter(io(a,s),r)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(n*o,u),l)/n}a.scaleX*=o,a.scaleY*=o;var v=(e.originX-a.x)*(o-1),c=(e.originY-a.y)*(o-1);a.x-=v,a.y-=c,a.updateTransform(),a.setCenter(io(a,s),r),a.setZoom(o*n)}return{center:a.getCenter(),zoom:a.getZoom()}}var Sf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,r){this._api=r},e.prototype.render=function(t,r,n,i){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new Il(n));var o=this._mapDraw;o.draw(t,r,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,r,n)},e.prototype._handleRegionClick=function(t){var r;uv(t.target,function(n){return(r=at(n).eventData)!=null},!0),r&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:r.name})},e.prototype.updateSelectStatus=function(t,r,n){var i=this;this._mapDraw.group.traverse(function(o){var s=at(o).eventData;if(s)return i._model.isSelected(s.name)?n.enterSelect(o):n.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(jt);const xf=Sf;function _f(a,e,t){ae.registerMap(a,e,t)}function Cl(a){a.registerCoordinateSystem("geo",Ll),a.registerComponentModel(mf),a.registerComponentView(xf),a.registerImpl("registerMap",_f),a.registerImpl("getMap",function(t){return ae.getMapForUser(t)});function e(t,r){r.update="geo:updateSelectStatus",a.registerAction(r,function(n,i){var o={},s=[];return i.eachComponent({mainType:"geo",query:n},function(l){l[t](n.name);var u=l.coordinateSystem;L(u.regions,function(c){o[c.name]=l.isSelected(c.name)||!1});var v=[];L(o,function(c,h){o[h]&&v.push(h)}),s.push({geoIndex:l.componentIndex,name:v})}),{selected:o,allSelected:s,name:n.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),a.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,r,n){var i=t.componentType||"series";r.eachComponent({mainType:i,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=qn(s,t,o.get("scaleLimit"),n);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),i==="series"&&L(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function bf(a){Y(Cl),a.registerChartView(sf),a.registerSeriesModel(uf),a.registerLayout(hf),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,cf),vv("map",a.registerAction)}function wf(a){var e=a;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],r,n;r=t.pop();)if(n=r.children,r.isExpand&&n.length)for(var i=n.length,o=i-1;o>=0;o--){var s=n[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function Tf(a,e){var t=a.isExpand?a.children:[],r=a.parentNode.children,n=a.hierNode.i?r[a.hierNode.i-1]:null;if(t.length){Af(a);var i=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;n?(a.hierNode.prelim=n.hierNode.prelim+e(a,n),a.hierNode.modifier=a.hierNode.prelim-i):a.hierNode.prelim=i}else n&&(a.hierNode.prelim=n.hierNode.prelim+e(a,n));a.parentNode.hierNode.defaultAncestor=Lf(a,n,a.parentNode.hierNode.defaultAncestor||r[0],e)}function If(a){var e=a.hierNode.prelim+a.parentNode.hierNode.modifier;a.setLayout({x:e},!0),a.hierNode.modifier+=a.parentNode.hierNode.modifier}function oo(a){return arguments.length?a:Mf}function Ue(a,e){return a-=Math.PI/2,{x:e*Math.cos(a),y:e*Math.sin(a)}}function Df(a,e){return Jt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Af(a){for(var e=a.children,t=e.length,r=0,n=0;--t>=0;){var i=e[t];i.hierNode.prelim+=r,i.hierNode.modifier+=r,n+=i.hierNode.change,r+=i.hierNode.shift+n}}function Lf(a,e,t,r){if(e){for(var n=a,i=a,o=i.parentNode.children[0],s=e,l=n.hierNode.modifier,u=i.hierNode.modifier,v=o.hierNode.modifier,c=s.hierNode.modifier;s=ma(s),i=Sa(i),s&&i;){n=ma(n),o=Sa(o),n.hierNode.ancestor=a;var h=s.hierNode.prelim+c-i.hierNode.prelim-u+r(s,i);h>0&&(Pf(Cf(s,a,t),a,h),u+=h,l+=h),c+=s.hierNode.modifier,u+=i.hierNode.modifier,l+=n.hierNode.modifier,v+=o.hierNode.modifier}s&&!ma(n)&&(n.hierNode.thread=s,n.hierNode.modifier+=c-l),i&&!Sa(o)&&(o.hierNode.thread=i,o.hierNode.modifier+=u-v,t=a)}return t}function ma(a){var e=a.children;return e.length&&a.isExpand?e[e.length-1]:a.hierNode.thread}function Sa(a){var e=a.children;return e.length&&a.isExpand?e[0]:a.hierNode.thread}function Cf(a,e,t){return a.hierNode.ancestor.parentNode===e.parentNode?a.hierNode.ancestor:t}function Pf(a,e,t){var r=t/(e.hierNode.i-a.hierNode.i);e.hierNode.change-=r,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,a.hierNode.change+=r}function Mf(a,e){return a.parentNode===e.parentNode?1:2}var Ef=function(){function a(){this.parentPoint=[],this.childPoints=[]}return a}(),Rf=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Ef},e.prototype.buildPath=function(t,r){var n=r.childPoints,i=n.length,o=r.parentPoint,s=n[0],l=n[i-1];if(i===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=r.orient,v=u==="TB"||u==="BT"?0:1,c=1-v,h=z(r.forkPosition,1),f=[];f[v]=o[v],f[c]=o[c]+(l[c]-o[c])*h,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[v]=s[v],t.lineTo(f[0],f[1]),f[v]=l[v],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<i-1;p++){var g=n[p];t.moveTo(g[0],g[1]),f[v]=g[v],t.lineTo(f[0],f[1])}},e}(_t),Nf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new U,t}return e.prototype.init=function(t,r){this._controller=new ta(r.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,r,n){var i=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,r,n);var u=this._data;i.diff(u).add(function(v){so(i,v)&&lo(i,v,null,s,t)}).update(function(v,c){var h=u.getItemGraphicEl(c);if(!so(i,v)){h&&vo(u,c,h,s,t);return}lo(i,v,h,s,t)}).remove(function(v){var c=u.getItemGraphicEl(v);c&&vo(u,v,c,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&i.eachItemGraphicEl(function(v,c){v.off("click").on("click",function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:c})})}),this._data=i},e.prototype._updateViewCoordSys=function(t,r){var n=t.getData(),i=[];n.each(function(c){var h=n.getItemLayout(c);h&&!isNaN(h.x)&&!isNaN(h.y)&&i.push([+h.x,+h.y])});var o=[],s=[];aa(i,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var v=t.coordinateSystem=new gr;v.zoomLimit=t.get("scaleLimit"),v.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),v.setCenter(t.get("center"),r),v.setZoom(t.get("zoom")),this.group.attr({x:v.x,y:v.y,scaleX:v.scaleX,scaleY:v.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Nn(u,n,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Yn(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){Xn(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var r=t.getData(),n=this._getNodeGlobalScale(t);r.eachItemGraphicEl(function(i,o){i.setSymbolScale(n)})},e.prototype._getNodeGlobalScale=function(t){var r=t.coordinateSystem;if(r.type!=="view")return 1;var n=this._nodeScaleRatio,i=r.scaleX||1,o=r.getZoom(),s=(o-1)*n+1;return s/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(ht);function so(a,e){var t=a.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function lo(a,e,t,r,n){var i=!t,o=a.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",v=a.tree.root,c=o.parentNode===v?o:o.parentNode||o,h=a.getItemGraphicEl(c.dataIndex),f=c.getLayout(),p=h?{x:h.__oldX,y:h.__oldY,rawX:h.__radialOldRawX,rawY:h.__radialOldRawY}:f,g=o.getLayout();i?(t=new Qs(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=g.rawX,t.__radialRawY=g.rawY,r.add(t),a.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,lt(t,{x:g.x,y:g.y},n);var d=t.getSymbolPath();if(n.get("layout")==="radial"){var y=v.children[0],S=y.getLayout(),m=y.children.length,x=void 0,_=void 0;if(g.x===S.x&&o.isExpand===!0&&y.children.length){var b={x:(y.children[0].getLayout().x+y.children[m-1].getLayout().x)/2,y:(y.children[0].getLayout().y+y.children[m-1].getLayout().y)/2};x=Math.atan2(b.y-S.y,b.x-S.x),x<0&&(x=Math.PI*2+x),_=b.x<S.x,_&&(x=x-Math.PI)}else x=Math.atan2(g.y-S.y,g.x-S.x),x<0&&(x=Math.PI*2+x),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(_=g.x<S.x,_&&(x=x-Math.PI)):(_=g.x>S.x,_||(x=x-Math.PI));var w=_?"left":"right",T=s.getModel("label"),I=T.get("rotate"),D=I*(Math.PI/180),A=d.getTextContent();A&&(d.setTextConfig({position:T.get("position")||w,rotation:I==null?-x:D,origin:"center"}),A.setStyle("verticalAlign","middle"))}var M=s.get(["emphasis","focus"]),E=M==="relative"?ln(o.getAncestorsIndices(),o.getDescendantIndices()):M==="ancestor"?o.getAncestorsIndices():M==="descendant"?o.getDescendantIndices():null;E&&(at(t).focus=E),Vf(n,o,v,t,p,f,g,r),t.__edge&&(t.onHoverStateChange=function(C){if(C!=="blur"){var P=o.parentNode&&a.getItemGraphicEl(o.parentNode.dataIndex);P&&P.hoverState===cv||js(t.__edge,C)}})}function Vf(a,e,t,r,n,i,o,s){var l=e.getModel(),u=a.get("edgeShape"),v=a.get("layout"),c=a.getOrient(),h=a.get(["lineStyle","curveness"]),f=a.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),g=r.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(g||(g=r.__edge=new tl({shape:yn(v,c,h,n,n)})),lt(g,{shape:yn(v,c,h,i,o)},a));else if(u==="polyline"&&v==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var d=e.children,y=[],S=0;S<d.length;S++){var m=d[S].getLayout();y.push([m.x,m.y])}g||(g=r.__edge=new Rf({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:c,forkPosition:f}})),lt(g,{shape:{parentPoint:[o.x,o.y],childPoints:y}},a)}g&&!(u==="polyline"&&!e.isExpand)&&(g.useStyle(nt({strokeNoScale:!0,fill:null},p)),Ct(g,l,"lineStyle"),Re(g),s.add(g))}function uo(a,e,t,r,n){var i=e.tree.root,o=Pl(i,a),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(a.dataIndex);if(u){var v=e.getItemGraphicEl(s.dataIndex),c=v.__edge,h=u.__edge||(s.isExpand===!1||s.children.length===1?c:void 0),f=r.get("edgeShape"),p=r.get("layout"),g=r.get("orient"),d=r.get(["lineStyle","curveness"]);h&&(f==="curve"?Mr(h,{shape:yn(p,g,d,l,l),style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}):f==="polyline"&&r.get("layout")==="orthogonal"&&Mr(h,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},r,{cb:function(){t.remove(h)},removeOpt:n}))}}function Pl(a,e){for(var t=e.parentNode===a?e:e.parentNode||e,r;r=t.getLayout(),r==null;)t=t.parentNode===a?t:t.parentNode||t;return{source:t,sourceLayout:r}}function vo(a,e,t,r,n){var i=a.tree.getNodeByDataIndex(e),o=a.tree.root,s=Pl(o,i).sourceLayout,l={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};Mr(t,{x:s.x+1,y:s.y+1},n,{cb:function(){r.remove(t),a.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,a.hostModel,{fadeLabel:!0,animation:l}),i.children.forEach(function(u){uo(u,a,r,n,l)}),uo(i,a,r,n,l)}function yn(a,e,t,r,n){var i,o,s,l,u,v,c,h;if(a==="radial"){u=r.rawX,c=r.rawY,v=n.rawX,h=n.rawY;var f=Ue(u,c),p=Ue(u,c+(h-c)*t),g=Ue(v,h+(c-h)*t),d=Ue(v,h);return{x1:f.x||0,y1:f.y||0,x2:d.x||0,y2:d.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:g.x||0,cpy2:g.y||0}}else u=r.x,c=r.y,v=n.x,h=n.y,(e==="LR"||e==="RL")&&(i=u+(v-u)*t,o=c,s=v+(u-v)*t,l=h),(e==="TB"||e==="BT")&&(i=u,o=c+(h-c)*t,s=v,l=h+(c-h)*t);return{x1:u,y1:c,x2:v,y2:h,cpx1:i,cpy1:o,cpx2:s,cpy2:l}}const kf=Nf;var Vt=xt();function Ml(a){var e=a.mainData,t=a.datas;t||(t={main:e},a.datasAttr={main:"data"}),a.datas=a.mainData=null,El(e,t,a),L(t,function(r){L(e.TRANSFERABLE_METHODS,function(n){r.wrapMethod(n,st(Gf,a))})}),e.wrapMethod("cloneShallow",st(Of,a)),L(e.CHANGABLE_METHODS,function(r){e.wrapMethod(r,st(zf,a))}),Ks(t[e.dataType]===e)}function Gf(a,e){if(Wf(this)){var t=$({},Vt(this).datas);t[this.dataType]=e,El(e,t,a)}else Kn(e,this.dataType,Vt(this).mainData,a);return e}function zf(a,e){return a.struct&&a.struct.update(),e}function Of(a,e){return L(Vt(e).datas,function(t,r){t!==e&&Kn(t.cloneShallow(),r,e,a)}),e}function Bf(a){var e=Vt(this).mainData;return a==null||e==null?e:Vt(e).datas[a]}function Ff(){var a=Vt(this).mainData;return a==null?[{data:a}]:O(ir(Vt(a).datas),function(e){return{type:e,data:Vt(a).datas[e]}})}function Wf(a){return Vt(a).mainData===a}function El(a,e,t){Vt(a).datas={},L(e,function(r,n){Kn(r,n,a,t)})}function Kn(a,e,t,r){Vt(t).datas[e]=a,Vt(a).mainData=t,a.dataType=e,r.struct&&(a[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=a),a.getLinkedData=Bf,a.getLinkedDataAll=Ff}var $f=function(){function a(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return a.prototype.isRemoved=function(){return this.dataIndex<0},a.prototype.eachNode=function(e,t,r){ot(e)&&(r=t,t=e,e=null),e=e||{},rt(e)&&(e={order:e});var n=e.order||"preorder",i=this[e.attr||"children"],o;n==="preorder"&&(o=t.call(r,this));for(var s=0;!o&&s<i.length;s++)i[s].eachNode(e,t,r);n==="postorder"&&t.call(r,this)},a.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var r=0;r<this.children.length;r++){var n=this.children[r];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},a.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].getNodeById(e);if(i)return i}},a.prototype.contains=function(e){if(e===this)return!0;for(var t=0,r=this.children,n=r.length;t<n;t++){var i=r[t].contains(e);if(i)return i}},a.prototype.getAncestors=function(e){for(var t=[],r=e?this:this.parentNode;r;)t.push(r),r=r.parentNode;return t.reverse(),t},a.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},a.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},a.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},a.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},a.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},a.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},a.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},a.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},a.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},a.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},a.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},a.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},a}(),jn=function(){function a(e){this.type="tree",this._nodes=[],this.hostModel=e}return a.prototype.eachNode=function(e,t,r){this.root.eachNode(e,t,r)},a.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},a.prototype.getNodeById=function(e){return this.root.getNodeById(e)},a.prototype.update=function(){for(var e=this.data,t=this._nodes,r=0,n=t.length;r<n;r++)t[r].dataIndex=-1;for(var r=0,n=e.count();r<n;r++)t[e.getRawIndex(r)].dataIndex=r},a.prototype.clearLayouts=function(){this.data.clearItemLayouts()},a.createTree=function(e,t,r){var n=new a(t),i=[],o=1;s(e);function s(v,c){var h=v.value;o=Math.max(o,F(h)?h.length:1),i.push(v);var f=new $f(vr(v.name,""),n);c?Hf(f,c):n.root=f,n._nodes.push(f);var p=v.children;if(p)for(var g=0;g<p.length;g++)s(p[g],f)}n.root.updateDepthAndHeight(0);var l=Gn(i,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new Lt(l,t);return u.initData(i),r&&r(u),Ml({mainData:u,struct:n,structAttr:"tree"}),n.update(),n},a}();function Hf(a,e){var t=e.children;a.parentNode!==e&&(t.push(a),a.parentNode=e)}function tr(a,e,t){if(a&&Bt(e,a.type)>=0){var r=t.getData().tree.root,n=a.targetNode;if(rt(n)&&(n=r.getNodeById(n)),n&&r.contains(n))return{node:n};var i=a.targetNodeId;if(i!=null&&(n=r.getNodeById(i)))return{node:n}}}function Rl(a){for(var e=[];a;)a=a.parentNode,a&&e.push(a);return e.reverse()}function Jn(a,e){var t=Rl(a);return Bt(t,e)>=0}function la(a,e){for(var t=[];a;){var r=a.dataIndex;t.push({name:a.name,dataIndex:r,value:e.getRawValue(r)}),a=a.parentNode}return t.reverse(),t}var Uf=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var r={name:t.name,children:t.data},n=t.leaves||{},i=new Wt(n,this,this.ecModel),o=jn.createTree(r,this,s);function s(c){c.wrapMethod("getItemModel",function(h,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(h.parentModel=i),h})}var l=0;o.eachNode("preorder",function(c){c.depth>l&&(l=c.depth)});var u=t.expandAndCollapse,v=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(c){var h=c.hostTree.data.getRawDataItem(c.dataIndex);c.isExpand=h&&h.collapsed!=null?!h.collapsed:c.depth<=v}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,r,n){for(var i=this.getData().tree,o=i.root.children[0],s=i.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return It("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=la(n,this),r.collapsed=!n.isExpand,r},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(pt);const Yf=Uf;function Xf(a,e,t){for(var r=[a],n=[],i;i=r.pop();)if(n.push(i),i.isExpand){var o=i.children;if(o.length)for(var s=0;s<o.length;s++)r.push(o[s])}for(;i=n.pop();)e(i,t)}function Be(a,e){for(var t=[a],r;r=t.pop();)if(e(r),r.isExpand){var n=r.children;if(n.length)for(var i=n.length-1;i>=0;i--)t.push(n[i])}}function Zf(a,e){a.eachSeriesByType("tree",function(t){qf(t,e)})}function qf(a,e){var t=Df(a,e);a.layoutInfo=t;var r=a.get("layout"),n=0,i=0,o=null;r==="radial"?(n=2*Math.PI,i=Math.min(t.height,t.width)/2,o=oo(function(m,x){return(m.parentNode===x.parentNode?1:2)/m.depth})):(n=t.width,i=t.height,o=oo());var s=a.getData().tree.root,l=s.children[0];if(l){wf(s),Xf(l,Tf,o),s.hierNode.modifier=-l.hierNode.prelim,Be(l,If);var u=l,v=l,c=l;Be(l,function(m){var x=m.getLayout().x;x<u.getLayout().x&&(u=m),x>v.getLayout().x&&(v=m),m.depth>c.depth&&(c=m)});var h=u===v?1:o(u,v)/2,f=h-u.getLayout().x,p=0,g=0,d=0,y=0;if(r==="radial")p=n/(v.getLayout().x+h+f),g=i/(c.depth-1||1),Be(l,function(m){d=(m.getLayout().x+f)*p,y=(m.depth-1)*g;var x=Ue(d,y);m.setLayout({x:x.x,y:x.y,rawX:d,rawY:y},!0)});else{var S=a.getOrient();S==="RL"||S==="LR"?(g=i/(v.getLayout().x+h+f),p=n/(c.depth-1||1),Be(l,function(m){y=(m.getLayout().x+f)*g,d=S==="LR"?(m.depth-1)*p:n-(m.depth-1)*p,m.setLayout({x:d,y},!0)})):(S==="TB"||S==="BT")&&(p=n/(v.getLayout().x+h+f),g=i/(c.depth-1||1),Be(l,function(m){d=(m.getLayout().x+f)*p,y=S==="TB"?(m.depth-1)*g:i-(m.depth-1)*g,m.setLayout({x:d,y},!0)}))}}}function Kf(a){a.eachSeriesByType("tree",function(e){var t=e.getData(),r=t.tree;r.eachNode(function(n){var i=n.getModel(),o=i.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(n.dataIndex,"style");$(s,o)})})}function jf(a){a.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(r){var n=e.dataIndex,i=r.getData().tree,o=i.getNodeByDataIndex(n);o.isExpand=!o.isExpand})}),a.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,r){t.eachComponent({mainType:"series",subType:"tree",query:e},function(n){var i=n.coordinateSystem,o=qn(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}function Jf(a){a.registerChartView(kf),a.registerSeriesModel(Yf),a.registerLayout(Zf),a.registerVisual(Kf),jf(a)}var co=["treemapZoomToNode","treemapRender","treemapMove"];function Qf(a){for(var e=0;e<co.length;e++)a.registerAction({type:co[e],update:"updateView"},je);a.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,r){r.eachComponent({mainType:"series",subType:"treemap",query:t},n);function n(i,o){var s=["treemapZoomToNode","treemapRootToNode"],l=tr(t,s,i);if(l){var u=i.getViewRoot();u&&(t.direction=Jn(u,l.node)?"rollUp":"drillDown"),i.resetViewRoot(l.node)}}})}function Nl(a){var e=a.getData(),t=e.tree,r={};t.eachNode(function(n){for(var i=n;i&&i.depth>1;)i=i.parentNode;var o=un(a.ecModel,i.name||i.dataIndex+"",r);n.setVisual("decal",o)})}var tp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};Vl(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},s=new Wt({itemStyle:o},this,r);i=t.levels=ep(i,r);var l=O(i||[],function(c){return new Wt(c,s,r)},this),u=jn.createTree(n,this,v);function v(c){c.wrapMethod("getItemModel",function(h,f){var p=u.getNodeByDataIndex(f),g=p?l[p.depth]:null;return h.parentModel=g||s,h})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return It("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=la(n,this),r.treePathInfo=r.treeAncestors,r},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},$(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var r=this._idIndexMap;r||(r=this._idIndexMap=q(),this._idIndexMapCount=0);var n=r.get(t);return n==null&&r.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Nl(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(pt);function Vl(a){var e=0;L(a.children,function(r){Vl(r);var n=r.value;F(n)&&(n=n[0]),e+=n});var t=a.value;F(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),F(a.value)?a.value[0]=t:a.value=t}function ep(a,e){var t=Ot(e.get("color")),r=Ot(e.get(["aria","decal","decals"]));if(t){a=a||[];var n,i;L(a,function(s){var l=new Wt(s),u=l.get("color"),v=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(n=!0),(l.get(["itemStyle","decal"])||v&&v!=="none")&&(i=!0)});var o=a[0]||(a[0]={});return n||(o.color=t.slice()),!i&&r&&(o.decal=r.slice()),a}}const rp=tp;var ap=8,ho=8,xa=5,np=function(){function a(e){this.group=new U,e.add(this.group)}return a.prototype.render=function(e,t,r,n){var i=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!i.get("show")||!r)){var s=i.getModel("itemStyle"),l=i.getModel("emphasis"),u=s.getModel("textStyle"),v=l.getModel(["itemStyle","textStyle"]),c={pos:{left:i.get("left"),right:i.get("right"),top:i.get("top"),bottom:i.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:i.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(r,c,u),this._renderContent(e,c,s,l,u,v,n),hv(o,c.pos,c.box)}},a.prototype._prepare=function(e,t,r){for(var n=e;n;n=n.parentNode){var i=vr(n.getModel().get("name"),""),o=r.getTextRect(i),s=Math.max(o.width+ap*2,t.emptyItemWidth);t.totalWidth+=s+ho,t.renderList.push({node:n,text:i,width:s})}},a.prototype._renderContent=function(e,t,r,n,i,o,s){for(var l=0,u=t.emptyItemWidth,v=e.get(["breadcrumb","height"]),c=fv(t.pos,t.box),h=t.totalWidth,f=t.renderList,p=n.getModel("itemStyle").getItemStyle(),g=f.length-1;g>=0;g--){var d=f[g],y=d.node,S=d.width,m=d.text;h>c.width&&(h-=S-u,S=u,m=null);var x=new re({shape:{points:ip(l,0,S,v,g===f.length-1,g===0)},style:nt(r.getItemStyle(),{lineJoin:"bevel"}),textContent:new Rt({style:St(i,{text:m})}),textConfig:{position:"inside"},z2:ur*1e4,onclick:st(s,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=St(o,{text:m}),x.ensureState("emphasis").style=p,ct(x,n.get("focus"),n.get("blurScope"),n.get("disabled")),this.group.add(x),op(x,e,y),l+=S+ho}},a.prototype.remove=function(){this.group.removeAll()},a}();function ip(a,e,t,r,n,i){var o=[[n?a:a-xa,e],[a+t,e],[a+t,e+r],[n?a:a-xa,e+r]];return!i&&o.splice(2,0,[a+t+xa,e+r/2]),!n&&o.push([a,e+r/2]),o}function op(a,e,t){at(a).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&la(t,e)}}const sp=np;var lp=function(){function a(){this._storage=[],this._elExistsMap={}}return a.prototype.add=function(e,t,r,n,i){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:r,delay:n,easing:i}),!0)},a.prototype.finished=function(e){return this._finishedCallback=e,this},a.prototype.start=function(){for(var e=this,t=this._storage.length,r=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},n=0,i=this._storage.length;n<i;n++){var o=this._storage[n];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:r,aborted:r})}return this},a}();function up(){return new lp}var mn=U,fo=Dt,po=3,go="label",yo="upperLabel",vp=ur*10,cp=ur*2,hp=ur*3,de=el([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),mo=function(a){var e=de(a);return e.stroke=e.fill=e.lineWidth=null,e},kr=xt(),fp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=Fe(),t}return e.prototype.render=function(t,r,n,i){var o=r.findComponents({mainType:"series",subType:"treemap",query:i});if(!(Bt(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=r;var s=["treemapZoomToNode","treemapRootToNode"],l=tr(i,s,t),u=i&&i.type,v=t.layoutInfo,c=!this._oldTree,h=this._storage,f=u==="treemapRootToNode"&&l&&h?{rootNodeGroup:h.nodeGroup[l.node.getRawIndex()],direction:i.direction}:null,p=this._giveContainerGroup(v),g=t.get("animation"),d=this._doRender(p,t,f);g&&!c&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,d,t,f):d.renderFinally(),this._resetController(n),this._renderBreadcrumb(t,n,l)}},e.prototype._giveContainerGroup=function(t){var r=this._containerGroup;return r||(r=this._containerGroup=new mn,this._initEvents(r),this.group.add(r)),r.x=t.x,r.y=t.y,r},e.prototype._doRender=function(t,r,n){var i=r.getData().tree,o=this._oldTree,s=Fe(),l=Fe(),u=this._storage,v=[];function c(d,y,S,m){return pp(r,l,u,n,s,v,d,y,S,m)}f(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var h=p(u);return this._oldTree=i,this._storage=l,{lastsForAnimation:s,willDeleteEls:h,renderFinally:g};function f(d,y,S,m,x){m?(y=d,L(d,function(w,T){!w.isRemoved()&&b(T,T)})):new Ne(y,d,_,_).add(b).update(b).remove(st(b,null)).execute();function _(w){return w.getId()}function b(w,T){var I=w!=null?d[w]:null,D=T!=null?y[T]:null,A=c(I,D,S,x);A&&f(I&&I.viewChildren||[],D&&D.viewChildren||[],A,m,x+1)}}function p(d){var y=Fe();return d&&L(d,function(S,m){var x=y[m];L(S,function(_){_&&(x.push(_),kr(_).willDelete=!0)})}),y}function g(){L(h,function(d){L(d,function(y){y.parent&&y.parent.remove(y)})}),L(v,function(d){d.invisible=!0,d.dirty()})}},e.prototype._doAnimation=function(t,r,n,i){var o=n.get("animationDurationUpdate"),s=n.get("animationEasing"),l=(ot(o)?0:o)||0,u=(ot(s)?null:s)||"cubicOut",v=up();L(r.willDeleteEls,function(c,h){L(c,function(f,p){if(!f.invisible){var g=f.parent,d,y=kr(g);if(i&&i.direction==="drillDown")d=g===i.rootNodeGroup?{shape:{x:0,y:0,width:y.nodeWidth,height:y.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var S=0,m=0;y.willDelete||(S=y.nodeWidth/2,m=y.nodeHeight/2),d=h==="nodeGroup"?{x:S,y:m,style:{opacity:0}}:{shape:{x:S,y:m,width:0,height:0},style:{opacity:0}}}d&&v.add(f,d,l,0,u)}})}),L(this._storage,function(c,h){L(c,function(f,p){var g=r.lastsForAnimation[h][p],d={};g&&(f instanceof U?g.oldX!=null&&(d.x=f.x,d.y=f.y,f.x=g.oldX,f.y=g.oldY):(g.oldShape&&(d.shape=$({},f.shape),f.setShape(g.oldShape)),g.fadein?(f.setStyle("opacity",0),d.style={opacity:1}):f.style.opacity!==1&&(d.style={opacity:1})),v.add(f,d,l,0,u))})},this),this._state="animating",v.finished(tt(function(){this._state="ready",r.renderFinally()},this)).start()},e.prototype._resetController=function(t){var r=this._controller;r||(r=this._controller=new ta(t.getZr()),r.enable(this.seriesModel.get("roam")),r.on("pan",tt(this._onPan,this)),r.on("zoom",tt(this._onZoom,this)));var n=new vt(0,0,t.getWidth(),t.getHeight());r.setPointerChecker(function(i,o,s){return n.contain(o,s)})},e.prototype._clearController=function(){var t=this._controller;t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>po||Math.abs(t.dy)>po)){var r=this.seriesModel.getData().tree.root;if(!r)return;var n=r.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},e.prototype._onZoom=function(t){var r=t.originX,n=t.originY;if(this._state!=="animating"){var i=this.seriesModel.getData().tree.root;if(!i)return;var o=i.getLayout();if(!o)return;var s=new vt(o.x,o.y,o.width,o.height),l=this.seriesModel.layoutInfo;r-=l.x,n-=l.y;var u=lr();Ee(u,u,[-r,-n]),qs(u,u,[t.scale,t.scale]),Ee(u,u,[r,n]),s.applyTransform(u),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:s.x,y:s.y,width:s.width,height:s.height}})}},e.prototype._initEvents=function(t){var r=this;t.on("click",function(n){if(r._state==="ready"){var i=r.seriesModel.get("nodeClick",!0);if(i){var o=r.findTarget(n.offsetX,n.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)r._rootToNode(o);else if(i==="zoomToNode")r._zoomToNode(o);else if(i==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),v=l.get("target",!0)||"blank";u&&rl(u,v)}}}}},this)},e.prototype._renderBreadcrumb=function(t,r,n){var i=this;n||(n=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(r.getWidth()/2,r.getHeight()/2),n||(n={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new sp(this.group))).render(t,r,n.node,function(o){i._state!=="animating"&&(Jn(t.getViewRoot(),o)?i._rootToNode({node:o}):i._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=Fe(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,r){var n,i=this.seriesModel.getViewRoot();return i.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,r),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)n={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),n},e.type="treemap",e}(ht);function Fe(){return{nodeGroup:[],background:[],content:[]}}function pp(a,e,t,r,n,i,o,s,l,u){if(!o)return;var v=o.getLayout(),c=a.getData(),h=o.getModel();if(c.setItemGraphicEl(o.dataIndex,null),!v||!v.isInView)return;var f=v.width,p=v.height,g=v.borderWidth,d=v.invisible,y=o.getRawIndex(),S=s&&s.getRawIndex(),m=o.viewChildren,x=v.upperHeight,_=m&&m.length,b=h.getModel("itemStyle"),w=h.getModel(["emphasis","itemStyle"]),T=h.getModel(["blur","itemStyle"]),I=h.getModel(["select","itemStyle"]),D=b.get("borderRadius")||0,A=X("nodeGroup",mn);if(!A)return;if(l.add(A),A.x=v.x||0,A.y=v.y||0,A.markRedraw(),kr(A).nodeWidth=f,kr(A).nodeHeight=p,v.isAboveViewRoot)return A;var M=X("background",fo,u,cp);M&&G(A,M,_&&v.upperLabelHeight);var E=h.getModel("emphasis"),C=E.get("focus"),P=E.get("blurScope"),R=E.get("disabled"),N=C==="ancestor"?o.getAncestorsIndices():C==="descendant"?o.getDescendantIndices():C;if(_)_i(A)&&dr(A,!1),M&&(dr(M,!R),c.setItemGraphicEl(o.dataIndex,M),bi(M,N,P));else{var k=X("content",fo,u,hp);k&&W(A,k),M.disableMorphing=!0,M&&_i(M)&&dr(M,!1),dr(A,!R),c.setItemGraphicEl(o.dataIndex,A),bi(A,N,P)}return A;function G(Z,H,et){var j=at(H);if(j.dataIndex=o.dataIndex,j.seriesIndex=a.seriesIndex,H.setShape({x:0,y:0,width:f,height:p,r:D}),d)B(H);else{H.invisible=!1;var it=o.getVisual("style"),bt=it.stroke,Gt=mo(b);Gt.fill=bt;var ft=de(w);ft.fill=w.get("borderColor");var zt=de(T);zt.fill=T.get("borderColor");var Qt=de(I);if(Qt.fill=I.get("borderColor"),et){var _e=f-2*g;J(H,bt,it.opacity,{x:g,y:0,width:_e,height:x})}else H.removeTextContent();H.setStyle(Gt),H.ensureState("emphasis").style=ft,H.ensureState("blur").style=zt,H.ensureState("select").style=Qt,Re(H)}Z.add(H)}function W(Z,H){var et=at(H);et.dataIndex=o.dataIndex,et.seriesIndex=a.seriesIndex;var j=Math.max(f-2*g,0),it=Math.max(p-2*g,0);if(H.culling=!0,H.setShape({x:g,y:g,width:j,height:it,r:D}),d)B(H);else{H.invisible=!1;var bt=o.getVisual("style"),Gt=bt.fill,ft=mo(b);ft.fill=Gt,ft.decal=bt.decal;var zt=de(w),Qt=de(T),_e=de(I);J(H,Gt,bt.opacity,null),H.setStyle(ft),H.ensureState("emphasis").style=zt,H.ensureState("blur").style=Qt,H.ensureState("select").style=_e,Re(H)}Z.add(H)}function B(Z){!Z.invisible&&i.push(Z)}function J(Z,H,et,j){var it=h.getModel(j?yo:go),bt=vr(h.get("name"),null),Gt=it.getShallow("show");kt(Z,Tt(h,j?yo:go),{defaultText:Gt?bt:null,inheritColor:H,defaultOpacity:et,labelFetcher:a,labelDataIndex:o.dataIndex});var ft=Z.getTextContent();if(ft){var zt=ft.style,Qt=pv(zt.padding||0);j&&(Z.setTextConfig({layoutRect:j}),ft.disableLabelLayout=!0),ft.beforeUpdate=function(){var yi=Math.max((j?j.width:Z.shape.width)-Qt[1]-Qt[3],0),mi=Math.max((j?j.height:Z.shape.height)-Qt[0]-Qt[2],0);(zt.width!==yi||zt.height!==mi)&&ft.setStyle({width:yi,height:mi})},zt.truncateMinChar=2,zt.lineOverflow="truncate",K(zt,j,v);var _e=ft.getState("emphasis");K(_e?_e.style:null,j,v)}}function K(Z,H,et){var j=Z?Z.text:null;if(!H&&et.isLeafRoot&&j!=null){var it=a.get("drillDownIcon",!0);Z.text=it?it+" "+j:j}}function X(Z,H,et,j){var it=S!=null&&t[Z][S],bt=n[Z];return it?(t[Z][S]=null,Q(bt,it)):d||(it=new H,it instanceof Ke&&(it.z2=gp(et,j)),dt(bt,it)),e[Z][y]=it}function Q(Z,H){var et=Z[y]={};H instanceof mn?(et.oldX=H.x,et.oldY=H.y):et.oldShape=$({},H.shape)}function dt(Z,H){var et=Z[y]={},j=o.parentNode,it=H instanceof U;if(j&&(!r||r.direction==="drillDown")){var bt=0,Gt=0,ft=n.background[j.getRawIndex()];!r&&ft&&ft.oldShape&&(bt=ft.oldShape.width,Gt=ft.oldShape.height),it?(et.oldX=0,et.oldY=Gt):et.oldShape={x:bt,y:Gt,width:0,height:0}}et.fadein=!it}}function gp(a,e){return a*vp+e}const dp=fp;var yp="itemStyle",kl=xt();const mp={seriesType:"treemap",reset:function(a){var e=a.getData().tree,t=e.root;t.isRemoved()||Gl(t,{},a.getViewRoot().getAncestors(),a)}};function Gl(a,e,t,r){var n=a.getModel(),i=a.getLayout(),o=a.hostTree.data;if(!(!i||i.invisible||!i.isInView)){var s=n.getModel(yp),l=Sp(s,e,r),u=o.ensureUniqueItemVisual(a.dataIndex,"style"),v=s.get("borderColor"),c=s.get("borderColorSaturation"),h;c!=null&&(h=So(l),v=xp(c,h)),u.stroke=v;var f=a.viewChildren;if(!f||!f.length)h=So(l),u.fill=h;else{var p=_p(a,n,i,s,l,f);L(f,function(g,d){if(g.depth>=t.length||g===t[g.depth]){var y=bp(n,l,g,d,p,r);Gl(g,y,t,r)}})}}}function Sp(a,e,t){var r=$({},e),n=t.designatedVisualItemStyle;return L(["color","colorAlpha","colorSaturation"],function(i){n[i]=e[i];var o=a.get(i);n[i]=null,o!=null&&(r[i]=o)}),r}function So(a){var e=_a(a,"color");if(e){var t=_a(a,"colorAlpha"),r=_a(a,"colorSaturation");return r&&(e=al(e,null,null,r)),t&&(e=vn(e,t)),e}}function xp(a,e){return e!=null?al(e,null,null,a):null}function _a(a,e){var t=a[e];if(t!=null&&t!=="none")return t}function _p(a,e,t,r,n,i){if(!(!i||!i.length)){var o=ba(e,"color")||n.color!=null&&n.color!=="none"&&(ba(e,"colorAlpha")||ba(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var v=e.get("colorMappingBy"),c={type:o.name,dataExtent:u,visual:o.range};c.type==="color"&&(v==="index"||v==="id")?(c.mappingMethod="category",c.loop=!0):c.mappingMethod="linear";var h=new nl(c);return kl(h).drColorMappingBy=v,h}}}function ba(a,e){var t=a.get(e);return F(t)&&t.length?{name:e,range:t}:null}function bp(a,e,t,r,n,i){var o=$({},e);if(n){var s=n.type,l=s==="color"&&kl(n).drColorMappingBy,u=l==="index"?r:l==="id"?i.mapIdToIndex(t.getId()):t.getValue(a.get("visualDimension"));o[s]=n.mapValueToVisual(u)}return o}var er=Math.max,Gr=Math.min,xo=Et,Qn=L,zl=["itemStyle","borderWidth"],wp=["itemStyle","gapWidth"],Tp=["upperLabel","show"],Ip=["upperLabel","height"];const Dp={seriesType:"treemap",reset:function(a,e,t,r){var n=t.getWidth(),i=t.getHeight(),o=a.option,s=Jt(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=z(xo(s.width,l[0]),n),v=z(xo(s.height,l[1]),i),c=r&&r.type,h=["treemapZoomToNode","treemapRootToNode"],f=tr(r,h,a),p=c==="treemapRender"||c==="treemapMove"?r.rootRect:null,g=a.getViewRoot(),d=Rl(g);if(c!=="treemapMove"){var y=c==="treemapZoomToNode"?Ep(a,f,g,u,v):p?[p.width,p.height]:[u,v],S=o.sort;S&&S!=="asc"&&S!=="desc"&&(S="desc");var m={squareRatio:o.squareRatio,sort:S,leafDepth:o.leafDepth};g.hostTree.clearLayouts();var x={x:0,y:0,width:y[0],height:y[1],area:y[0]*y[1]};g.setLayout(x),Ol(g,m,!1,0),x=g.getLayout(),Qn(d,function(b,w){var T=(d[w+1]||g).getValue();b.setLayout($({dataExtent:[T,T],borderWidth:0,upperHeight:0},x))})}var _=a.getData().tree.root;_.setLayout(Rp(s,p,f),!0),a.setLayoutInfo(s),Bl(_,new vt(-s.x,-s.y,n,i),d,g,0)}};function Ol(a,e,t,r){var n,i;if(!a.isRemoved()){var o=a.getLayout();n=o.width,i=o.height;var s=a.getModel(),l=s.get(zl),u=s.get(wp)/2,v=Fl(s),c=Math.max(l,v),h=l-u,f=c-u;a.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:v},!0),n=er(n-2*h,0),i=er(i-h-f,0);var p=n*i,g=Ap(a,s,p,e,t,r);if(g.length){var d={x:h,y:f,width:n,height:i},y=Gr(n,i),S=1/0,m=[];m.area=0;for(var x=0,_=g.length;x<_;){var b=g[x];m.push(b),m.area+=b.getLayout().area;var w=Mp(m,y,e.squareRatio);w<=S?(x++,S=w):(m.area-=m.pop().getLayout().area,_o(m,y,d,u,!1),y=Gr(d.width,d.height),m.length=m.area=0,S=1/0)}if(m.length&&_o(m,y,d,u,!0),!t){var T=s.get("childrenVisibleMin");T!=null&&p<T&&(t=!0)}for(var x=0,_=g.length;x<_;x++)Ol(g[x],e,t,r+1)}}}function Ap(a,e,t,r,n,i){var o=a.children||[],s=r.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=r.leafDepth!=null&&r.leafDepth<=i;if(n&&!l)return a.viewChildren=[];o=Nt(o,function(f){return!f.isRemoved()}),Cp(o,s);var u=Pp(e,o,s);if(u.sum===0)return a.viewChildren=[];if(u.sum=Lp(e,t,u.sum,s,o),u.sum===0)return a.viewChildren=[];for(var v=0,c=o.length;v<c;v++){var h=o[v].getValue()/u.sum*t;o[v].setLayout({area:h})}return l&&(o.length&&a.setLayout({isLeafRoot:!0},!0),o.length=0),a.viewChildren=o,a.setLayout({dataExtent:u.dataExtent},!0),o}function Lp(a,e,t,r,n){if(!r)return t;for(var i=a.get("visibleMin"),o=n.length,s=o,l=o-1;l>=0;l--){var u=n[r==="asc"?o-l-1:l].getValue();u/t*e<i&&(s=l,t-=u)}return r==="asc"?n.splice(0,o-s):n.splice(s,o-s),t}function Cp(a,e){return e&&a.sort(function(t,r){var n=e==="asc"?t.getValue()-r.getValue():r.getValue()-t.getValue();return n===0?e==="asc"?t.dataIndex-r.dataIndex:r.dataIndex-t.dataIndex:n}),a}function Pp(a,e,t){for(var r=0,n=0,i=e.length;n<i;n++)r+=e[n].getValue();var o=a.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],Qn(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:r,dataExtent:s}}function Mp(a,e,t){for(var r=0,n=1/0,i=0,o=void 0,s=a.length;i<s;i++)o=a[i].getLayout().area,o&&(o<n&&(n=o),o>r&&(r=o));var l=a.area*a.area,u=e*e*t;return l?er(u*r/l,l/(u*n)):1/0}function _o(a,e,t,r,n){var i=e===t.width?0:1,o=1-i,s=["x","y"],l=["width","height"],u=t[s[i]],v=e?a.area/e:0;(n||v>t[l[o]])&&(v=t[l[o]]);for(var c=0,h=a.length;c<h;c++){var f=a[c],p={},g=v?f.getLayout().area/v:0,d=p[l[o]]=er(v-2*r,0),y=t[s[i]]+t[l[i]]-u,S=c===h-1||y<g?y:g,m=p[l[i]]=er(S-2*r,0);p[s[o]]=t[s[o]]+Gr(r,d/2),p[s[i]]=u+Gr(r,m/2),u+=S,f.setLayout(p,!0)}t[s[o]]+=v,t[l[o]]-=v}function Ep(a,e,t,r,n){var i=(e||{}).node,o=[r,n];if(!i||i===t)return o;for(var s,l=r*n,u=l*a.option.zoomToNodeRatio;s=i.parentNode;){for(var v=0,c=s.children,h=0,f=c.length;h<f;h++)v+=c[h].getValue();var p=i.getValue();if(p===0)return o;u*=v/p;var g=s.getModel(),d=g.get(zl),y=Math.max(d,Fl(g));u+=4*d*d+(3*d+y)*Math.pow(u,.5),u>wi&&(u=wi),i=s}u<l&&(u=l);var S=Math.pow(u/l,.5);return[r*S,n*S]}function Rp(a,e,t){if(e)return{x:e.x,y:e.y};var r={x:0,y:0};if(!t)return r;var n=t.node,i=n.getLayout();if(!i)return r;for(var o=[i.width/2,i.height/2],s=n;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:a.width/2-o[0],y:a.height/2-o[1]}}function Bl(a,e,t,r,n){var i=a.getLayout(),o=t[n],s=o&&o===a;if(!(o&&!s||n===t.length&&a!==r)){a.setLayout({isInView:!0,invisible:!s&&!e.intersect(i),isAboveViewRoot:s},!0);var l=new vt(e.x-i.x,e.y-i.y,e.width,e.height);Qn(a.viewChildren||[],function(u){Bl(u,l,t,r,n+1)})}}function Fl(a){return a.get(Tp)?a.get(Ip):0}function Np(a){a.registerSeriesModel(rp),a.registerChartView(dp),a.registerVisual(mp),a.registerLayout(Dp),Qf(a)}function Vp(a){var e=a.findComponents({mainType:"legend"});!e||!e.length||a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getGraph(),i=n.data,o=r.mapArray(r.getName);i.filterSelf(function(s){var l=i.getItemModel(s),u=l.getShallow("category");if(u!=null){Zt(u)&&(u=o[u]);for(var v=0;v<e.length;v++)if(!e[v].isSelected(u))return!1}return!0})})}function kp(a){var e={};a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),n=t.getData(),i={};r.each(function(o){var s=r.getName(o);i["ec-"+s]=o;var l=r.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),r.setItemVisual(o,"style",u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++){var h=l.getShallow(v[c],!0);h!=null&&r.setItemVisual(o,v[c],h)}}),r.count()&&n.each(function(o){var s=n.getItemModel(o),l=s.getShallow("category");if(l!=null){rt(l)&&(l=i["ec-"+l]);var u=r.getItemVisual(l,"style"),v=n.ensureUniqueItemVisual(o,"style");$(v,u);for(var c=["symbol","symbolSize","symbolKeepAspect"],h=0;h<c.length;h++)n.setItemVisual(o,c[h],r.getItemVisual(l,c[h]))}})})}function br(a){return a instanceof Array||(a=[a,a]),a}function Gp(a){a.eachSeriesByType("graph",function(e){var t=e.getGraph(),r=e.getEdgeData(),n=br(e.get("edgeSymbol")),i=br(e.get("edgeSymbolSize"));r.setVisual("fromSymbol",n&&n[0]),r.setVisual("toSymbol",n&&n[1]),r.setVisual("fromSymbolSize",i&&i[0]),r.setVisual("toSymbolSize",i&&i[1]),r.setVisual("style",e.getModel("lineStyle").getLineStyle()),r.each(function(o){var s=r.getItemModel(o),l=t.getEdgeByIndex(o),u=br(s.getShallow("symbol",!0)),v=br(s.getShallow("symbolSize",!0)),c=s.getModel("lineStyle").getLineStyle(),h=r.ensureUniqueItemVisual(o,"style");switch($(h,c),h.stroke){case"source":{var f=l.node1.getVisual("style");h.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");h.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),v[0]&&l.setVisual("fromSymbolSize",v[0]),v[1]&&l.setVisual("toSymbolSize",v[1])})})}var Sn="-->",ua=function(a){return a.get("autoCurveness")||null},Wl=function(a,e){var t=ua(a),r=20,n=[];if(Zt(t))r=t;else if(F(t)){a.__curvenessList=t;return}e>r&&(r=e);var i=r%2?r+2:r+3;n=[];for(var o=0;o<i;o++)n.push((o%2?o+1:o)/10*(o%2?-1:1));a.__curvenessList=n},rr=function(a,e,t){var r=[a.id,a.dataIndex].join("."),n=[e.id,e.dataIndex].join(".");return[t.uid,r,n].join(Sn)},$l=function(a){var e=a.split(Sn);return[e[0],e[2],e[1]].join(Sn)},zp=function(a,e){var t=rr(a.node1,a.node2,e);return e.__edgeMap[t]},Op=function(a,e){var t=xn(rr(a.node1,a.node2,e),e),r=xn(rr(a.node2,a.node1,e),e);return t+r},xn=function(a,e){var t=e.__edgeMap;return t[a]?t[a].length:0};function Bp(a){ua(a)&&(a.__curvenessList=[],a.__edgeMap={},Wl(a))}function Fp(a,e,t,r){if(ua(t)){var n=rr(a,e,t),i=t.__edgeMap,o=i[$l(n)];i[n]&&!o?i[n].isForward=!0:o&&i[n]&&(o.isForward=!0,i[n].isForward=!1),i[n]=i[n]||[],i[n].push(r)}}function ti(a,e,t,r){var n=ua(e),i=F(n);if(!n)return null;var o=zp(a,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=Op(a,e);Wl(e,u),a.lineStyle=a.lineStyle||{};var v=rr(a.node1,a.node2,e),c=e.__curvenessList,h=i||u%2?0:1;if(o.isForward)return c[h+s];var f=$l(v),p=xn(f,e),g=c[s+p+h];return r?i?n&&n[0]===0?(p+h)%2?g:-g:((p%2?0:1)+h)%2?g:-g:(p+h)%2?g:-g:c[s+p+h]}function Hl(a){var e=a.coordinateSystem;if(!(e&&e.type!=="view")){var t=a.getGraph();t.eachNode(function(r){var n=r.getModel();r.setLayout([+n.get("x"),+n.get("y")])}),ei(t,a)}}function ei(a,e){a.eachEdge(function(t,r){var n=cr(t.getModel().get(["lineStyle","curveness"]),-ti(t,e,r,!0),0),i=se(t.node1.getLayout()),o=se(t.node2.getLayout()),s=[i,o];+n&&s.push([(i[0]+o[0])/2-(i[1]-o[1])*n,(i[1]+o[1])/2-(o[0]-i[0])*n]),t.setLayout(s)})}function Wp(a,e){a.eachSeriesByType("graph",function(t){var r=t.get("layout"),n=t.coordinateSystem;if(n&&n.type!=="view"){var i=t.getData(),o=[];L(n.dimensions,function(h){o=o.concat(i.mapDimensionsAll(h))});for(var s=0;s<i.count();s++){for(var l=[],u=!1,v=0;v<o.length;v++){var c=i.get(o[v],s);isNaN(c)||(u=!0),l.push(c)}u?i.setItemLayout(s,n.dataToPoint(l)):i.setItemLayout(s,[NaN,NaN])}ei(i.graph,t)}else(!r||r==="none")&&Hl(t)})}function Ye(a){var e=a.coordinateSystem;if(e.type!=="view")return 1;var t=a.option.nodeScaleRatio,r=e.scaleX,n=e.getZoom(),i=(n-1)*t+1;return i/r}function Xe(a){var e=a.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var bo=Math.PI,wa=[];function ri(a,e,t,r){var n=a.coordinateSystem;if(!(n&&n.type!=="view")){var i=n.getBoundingRect(),o=a.getData(),s=o.graph,l=i.width/2+i.x,u=i.height/2+i.y,v=Math.min(i.width,i.height)/2,c=o.count();if(o.setLayout({cx:l,cy:u}),!!c){if(t){var h=n.pointToData(r),f=h[0],p=h[1],g=[f-l,p-u];hr(g,g),gv(g,g,v),t.setLayout([l+g[0],u+g[1]],!0);var d=a.get(["circular","rotateLabel"]);Ul(t,d,l,u)}$p[e](a,s,o,v,l,u,c),s.eachEdge(function(y,S){var m=cr(y.getModel().get(["lineStyle","curveness"]),ti(y,a,S),0),x=se(y.node1.getLayout()),_=se(y.node2.getLayout()),b,w=(x[0]+_[0])/2,T=(x[1]+_[1])/2;+m&&(m*=3,b=[l*m+w*(1-m),u*m+T*(1-m)]),y.setLayout([x,_,b])})}}}var $p={value:function(a,e,t,r,n,i,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(v){var c=v.getValue("value"),h=u*(l?c:1)/2;s+=h,v.setLayout([r*Math.cos(s)+n,r*Math.sin(s)+i]),s+=h})},symbolSize:function(a,e,t,r,n,i,o){var s=0;wa.length=o;var l=Ye(a);e.eachNode(function(c){var h=Xe(c);isNaN(h)&&(h=2),h<0&&(h=0),h*=l;var f=Math.asin(h/2/r);isNaN(f)&&(f=bo/2),wa[c.dataIndex]=f,s+=f*2});var u=(2*bo-s)/o/2,v=0;e.eachNode(function(c){var h=u+wa[c.dataIndex];v+=h,(!c.getLayout()||!c.getLayout().fixed)&&c.setLayout([r*Math.cos(v)+n,r*Math.sin(v)+i]),v+=h})}};function Ul(a,e,t,r){var n=a.getGraphicEl();if(n){var i=a.getModel(),o=i.get(["label","rotate"])||0,s=n.getSymbolPath();if(e){var l=a.getLayout(),u=Math.atan2(l[1]-r,l[0]-t);u<0&&(u=Math.PI*2+u);var v=l[0]<t;v&&(u=u-Math.PI);var c=v?"left":"right";s.setTextConfig({rotation:-u,position:c,origin:"center"});var h=s.ensureState("emphasis");$(h.textConfig||(h.textConfig={}),{position:c})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function Hp(a){a.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&ri(e,"symbolSize")})}var we=cn;function Up(a,e,t){for(var r=a,n=e,i=t.rect,o=i.width,s=i.height,l=[i.x+o/2,i.y+s/2],u=t.gravity==null?.1:t.gravity,v=0;v<r.length;v++){var c=r[v];c.p||(c.p=dv(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=se(c.p),c.edges=null}var h=t.friction==null?.6:t.friction,f=h,p,g;return{warmUp:function(){f=h*.8},setFixed:function(d){r[d].fixed=!0},setUnfixed:function(d){r[d].fixed=!1},beforeStep:function(d){p=d},afterStep:function(d){g=d},step:function(d){p&&p(r,n);for(var y=[],S=r.length,m=0;m<n.length;m++){var x=n[m];if(!x.ignoreForceLayout){var _=x.n1,b=x.n2;Ce(y,b.p,_.p);var w=Ti(y)-x.d,T=b.w/(_.w+b.w);isNaN(T)&&(T=0),hr(y,y),!_.fixed&&we(_.p,_.p,y,T*w*f),!b.fixed&&we(b.p,b.p,y,-(1-T)*w*f)}}for(var m=0;m<S;m++){var I=r[m];I.fixed||(Ce(y,l,I.p),we(I.p,I.p,y,u*f))}for(var m=0;m<S;m++)for(var _=r[m],D=m+1;D<S;D++){var b=r[D];Ce(y,b.p,_.p);var w=Ti(y);w===0&&(yv(y,Math.random()-.5,Math.random()-.5),w=1);var A=(_.rep+b.rep)/w/w;!_.fixed&&we(_.pp,_.pp,y,A),!b.fixed&&we(b.pp,b.pp,y,-A)}for(var M=[],m=0;m<S;m++){var I=r[m];I.fixed||(Ce(M,I.p,I.pp),we(I.p,I.p,M,f),mt(I.pp,I.p))}f=f*.992;var E=f<.01;g&&g(r,n,E),d&&d(E)}}}function Yp(a){a.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var r=e.preservedPoints||{},n=e.getGraph(),i=n.data,o=n.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?i.each(function(m){var x=i.getId(m);i.setItemLayout(m,r[x]||[NaN,NaN])}):!l||l==="none"?Hl(e):l==="circular"&&ri(e,"value");var u=i.getDataExtent("value"),v=o.getDataExtent("value"),c=s.get("repulsion"),h=s.get("edgeLength"),f=F(c)?c:[c,c],p=F(h)?h:[h,h];p=[p[1],p[0]];var g=i.mapArray("value",function(m,x){var _=i.getItemLayout(x),b=Ht(m,u,f);return isNaN(b)&&(b=(f[0]+f[1])/2),{w:b,rep:b,fixed:i.getItemModel(x).get("fixed"),p:!_||isNaN(_[0])||isNaN(_[1])?null:_}}),d=o.mapArray("value",function(m,x){var _=n.getEdgeByIndex(x),b=Ht(m,v,p);isNaN(b)&&(b=(p[0]+p[1])/2);var w=_.getModel(),T=cr(_.getModel().get(["lineStyle","curveness"]),-ti(_,e,x,!0),0);return{n1:g[_.node1.dataIndex],n2:g[_.node2.dataIndex],d:b,curveness:T,ignoreForceLayout:w.get("ignoreForceLayout")}}),y=t.getBoundingRect(),S=Up(g,d,{rect:y,gravity:s.get("gravity"),friction:s.get("friction")});S.beforeStep(function(m,x){for(var _=0,b=m.length;_<b;_++)m[_].fixed&&mt(m[_].p,n.getNodeByIndex(_).getLayout())}),S.afterStep(function(m,x,_){for(var b=0,w=m.length;b<w;b++)m[b].fixed||n.getNodeByIndex(b).setLayout(m[b].p),r[i.getId(b)]=m[b].p;for(var b=0,w=x.length;b<w;b++){var T=x[b],I=n.getEdgeByIndex(b),D=T.n1.p,A=T.n2.p,M=I.getLayout();M=M?M.slice():[],M[0]=M[0]||[],M[1]=M[1]||[],mt(M[0],D),mt(M[1],A),+T.curveness&&(M[2]=[(D[0]+A[0])/2-(D[1]-A[1])*T.curveness,(D[1]+A[1])/2-(A[0]-D[0])*T.curveness]),I.setLayout(M)}}),e.forceLayout=S,e.preservedPoints=r,S.step()}else e.forceLayout=null})}function Xp(a,e,t){var r=$(a.getBoxLayoutParams(),{aspect:t});return Jt(r,{width:e.getWidth(),height:e.getHeight()})}function Zp(a,e){var t=[];return a.eachSeriesByType("graph",function(r){var n=r.get("coordinateSystem");if(!n||n==="view"){var i=r.getData(),o=i.mapArray(function(d){var y=i.getItemModel(d);return[+y.get("x"),+y.get("y")]}),s=[],l=[];aa(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),v=Xp(r,e,u);isNaN(u)&&(s=[v.x,v.y],l=[v.x+v.width,v.y+v.height]);var c=l[0]-s[0],h=l[1]-s[1],f=v.width,p=v.height,g=r.coordinateSystem=new gr;g.zoomLimit=r.get("scaleLimit"),g.setBoundingRect(s[0],s[1],c,h),g.setViewRect(v.x,v.y,f,p),g.setCenter(r.get("center"),e),g.setZoom(r.get("zoom")),t.push(g)}}),t}var wo=xe.prototype,Ta=tl.prototype,Yl=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(Yl);function Ia(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var qp=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Yl},e.prototype.buildPath=function(t,r){Ia(r)?wo.buildPath.call(this,t,r):Ta.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return Ia(this.shape)?wo.pointAt.call(this,t):Ta.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,n=Ia(r)?[r.x2-r.x1,r.y2-r.y1]:Ta.tangentAt.call(this,t);return hr(n,n)},e}(_t),Da=["fromSymbol","toSymbol"];function To(a){return"_"+a+"Type"}function Io(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=or(n),u=fr(o||0,l);return r+l+u+(i||"")+(s||"")}function Do(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var n=e.getItemVisual(t,a+"Size"),i=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=or(n),u=fr(o||0,l),v=Yt(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return v.__specifiedRotation=i==null||isNaN(i)?void 0:+i*Math.PI/180||0,v.name=a,v}}function Kp(a){var e=new qp({name:"line",subPixelOptimize:!0});return _n(e.shape,a),e}function _n(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var jp=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createLine(t,r,n),i}return e.prototype._createLine=function(t,r,n){var i=t.hostModel,o=t.getItemLayout(r),s=Kp(o);s.shape.percent=0,wt(s,{shape:{percent:1}},i,r),this.add(s),L(Da,function(l){var u=Do(l,t,r);this.add(u),this[To(l)]=Io(l,t,r)},this),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};_n(l.shape,s),lt(o,l,i,r),L(Da,function(u){var v=Io(u,t,r),c=To(u);if(this[c]!==v){this.remove(this.childOfName(u));var h=Do(u,t,r);this.add(h)}this[c]=v},this),this._updateCommonStl(t,r,n)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,n){var i=t.hostModel,o=this.childOfName("line"),s=n&&n.emphasisLineStyle,l=n&&n.blurLineStyle,u=n&&n.selectLineStyle,v=n&&n.labelStatesModels,c=n&&n.emphasisDisabled,h=n&&n.focus,f=n&&n.blurScope;if(!n||t.hasItemOption){var p=t.getItemModel(r),g=p.getModel("emphasis");s=g.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),c=g.get("disabled"),h=g.get("focus"),f=g.get("blurScope"),v=Tt(p)}var d=t.getItemVisual(r,"style"),y=d.stroke;o.useStyle(d),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,L(Da,function(b){var w=this.childOfName(b);if(w){w.setColor(y),w.style.opacity=d.opacity;for(var T=0;T<hn.length;T++){var I=hn[T],D=o.getState(I);if(D){var A=D.style||{},M=w.ensureState(I),E=M.style||(M.style={});A.stroke!=null&&(E[w.__isEmptyBrush?"stroke":"fill"]=A.stroke),A.opacity!=null&&(E.opacity=A.opacity)}}w.markRedraw()}},this);var S=i.getRawValue(r);kt(this,v,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(b,w){return i.getFormattedLabel(b,w,t.dataType)}},inheritColor:y||"#000",defaultOpacity:d.opacity,defaultText:(S==null?t.getName(r):isFinite(S)?zn(S):S)+""});var m=this.getTextContent();if(m){var x=v.normal;m.__align=m.style.align,m.__verticalAlign=m.style.verticalAlign,m.__position=x.get("position")||"middle";var _=x.get("distance");F(_)||(_=[_,_]),m.__labelDistance=_}this.setTextConfig({position:null,local:!0,inside:!1}),ct(this,h,f,c)},e.prototype.highlight=function(){On(this)},e.prototype.downplay=function(){Bn(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");_n(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.getTextContent();if(!r&&!n&&(!i||i.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,v=l.pointAt(0),c=l.pointAt(u),h=Ce([],c,v);hr(h,h);function f(D,A){var M=D.__specifiedRotation;if(M==null){var E=l.tangentAt(A);D.attr("rotation",(A===1?-1:1)*Math.PI/2-Math.atan2(E[1],E[0]))}else D.attr("rotation",M)}if(r&&(r.setPosition(v),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),n&&(n.setPosition(c),f(n,1),n.scaleX=n.scaleY=o*u,n.markRedraw()),i&&!i.ignore){i.x=i.y=0,i.originX=i.originY=0;var p=void 0,g=void 0,d=i.__labelDistance,y=d[0]*o,S=d[1]*o,m=u/2,x=l.tangentAt(m),_=[x[1],-x[0]],b=l.pointAt(m);_[1]>0&&(_[0]=-_[0],_[1]=-_[1]);var w=x[0]<0?-1:1;if(i.__position!=="start"&&i.__position!=="end"){var T=-Math.atan2(x[1],x[0]);c[0]<v[0]&&(T=Math.PI+T),i.rotation=T}var I=void 0;switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":I=-S,g="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":I=S,g="top";break;default:I=0,g="middle"}switch(i.__position){case"end":i.x=h[0]*y+c[0],i.y=h[1]*S+c[1],p=h[0]>.8?"left":h[0]<-.8?"right":"center",g=h[1]>.8?"top":h[1]<-.8?"bottom":"middle";break;case"start":i.x=-h[0]*y+v[0],i.y=-h[1]*S+v[1],p=h[0]>.8?"right":h[0]<-.8?"left":"center",g=h[1]>.8?"bottom":h[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":i.x=y*w+v[0],i.y=v[1]+I,p=x[0]<0?"right":"left",i.originX=-y*w,i.originY=-I;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":i.x=b[0],i.y=b[1]+I,p="center",i.originY=-I;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":i.x=-y*w+c[0],i.y=c[1]+I,p=x[0]>=0?"right":"left",i.originX=y*w,i.originY=-I;break}i.scaleX=i.scaleY=o,i.setStyle({verticalAlign:i.__verticalAlign||g,align:i.__align||p})}},e}(U);const ai=jp;var ni=function(){function a(e){this.group=new U,this._LineCtor=e||ai}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,n=r.group,i=r._lineData;r._lineData=e,i||n.removeAll();var o=Ao(e);e.diff(i).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(i,e,l,s,o)}).remove(function(s){n.remove(i.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=Ao(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!Jp(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var i=t.getItemLayout(n);if(Aa(i)){var o=new this._LineCtor(t,n,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(n,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){na(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var n=e.getItemLayout(t);if(Aa(n)){var i=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,i),this.group.add(i)}},a.prototype._doUpdate=function(e,t,r,n,i){var o=e.getItemGraphicEl(r);if(!Aa(t.getItemLayout(n))){this.group.remove(o);return}o?o.updateData(t,n,i):o=new this._LineCtor(t,n,i),t.setItemGraphicEl(n,o),this.group.add(o)},a}();function Jp(a){return a.animators&&a.animators.length>0}function Ao(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:Tt(e)}}function Lo(a){return isNaN(a[0])||isNaN(a[1])}function Aa(a){return a&&!Lo(a[0])&&!Lo(a[1])}var La=[],Ca=[],Pa=[],Te=il,Ma=Sv,Co=Math.abs;function Po(a,e,t){for(var r=a[0],n=a[1],i=a[2],o=1/0,s,l=t*t,u=.1,v=.1;v<=.9;v+=.1){La[0]=Te(r[0],n[0],i[0],v),La[1]=Te(r[1],n[1],i[1],v);var c=Co(Ma(La,e)-l);c<o&&(o=c,s=v)}for(var h=0;h<32;h++){var f=s+u;Ca[0]=Te(r[0],n[0],i[0],s),Ca[1]=Te(r[1],n[1],i[1],s),Pa[0]=Te(r[0],n[0],i[0],f),Pa[1]=Te(r[1],n[1],i[1],f);var c=Ma(Ca,e)-l;if(Co(c)<.01)break;var p=Ma(Pa,e)-l;u/=2,c<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function Ea(a,e){var t=[],r=mv,n=[[],[],[]],i=[[],[]],o=[];e/=2,a.eachEdge(function(s,l){var u=s.getLayout(),v=s.getVisual("fromSymbol"),c=s.getVisual("toSymbol");u.__original||(u.__original=[se(u[0]),se(u[1])],u[2]&&u.__original.push(se(u[2])));var h=u.__original;if(u[2]!=null){if(mt(n[0],h[0]),mt(n[1],h[2]),mt(n[2],h[1]),v&&v!=="none"){var f=Xe(s.node1),p=Po(n,h[0],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[0][0]=t[3],n[1][0]=t[4],r(n[0][1],n[1][1],n[2][1],p,t),n[0][1]=t[3],n[1][1]=t[4]}if(c&&c!=="none"){var f=Xe(s.node2),p=Po(n,h[1],f*e);r(n[0][0],n[1][0],n[2][0],p,t),n[1][0]=t[1],n[2][0]=t[2],r(n[0][1],n[1][1],n[2][1],p,t),n[1][1]=t[1],n[2][1]=t[2]}mt(u[0],n[0]),mt(u[1],n[2]),mt(u[2],n[1])}else{if(mt(i[0],h[0]),mt(i[1],h[1]),Ce(o,i[1],i[0]),hr(o,o),v&&v!=="none"){var f=Xe(s.node1);cn(i[0],i[0],o,f*e)}if(c&&c!=="none"){var f=Xe(s.node2);cn(i[1],i[1],o,-f*e)}mt(u[0],i[0]),mt(u[1],i[1])}})}function Mo(a){return a.type==="view"}var Qp=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){var n=new Zr,i=new ni,o=this.group;this._controller=new ta(r.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,r,n){var i=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(Mo(o)){var v={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(v):lt(u,v,t)}Ea(t.getGraph(),Ye(t));var c=t.getData();s.updateData(c);var h=t.getEdgeData();l.updateData(h),this._updateNodeAndLinkScale(),this._updateController(t,r,n),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var g=t.get("layout");c.graph.eachNode(function(m){var x=m.dataIndex,_=m.getGraphicEl(),b=m.getModel();if(_){_.off("drag").off("dragend");var w=b.get("draggable");w&&_.on("drag",function(I){switch(g){case"force":f.warmUp(),!i._layouting&&i._startForceLayoutIteration(f,p),f.setFixed(x),c.setItemLayout(x,[_.x,_.y]);break;case"circular":c.setItemLayout(x,[_.x,_.y]),m.setLayout({fixed:!0},!0),ri(t,"symbolSize",m,[I.offsetX,I.offsetY]),i.updateLayout(t);break;case"none":default:c.setItemLayout(x,[_.x,_.y]),ei(t.getGraph(),t),i.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(x)}),_.setDraggable(w,!!b.get("cursor"));var T=b.get(["emphasis","focus"]);T==="adjacency"&&(at(_).focus=m.getAdjacentDataIndices())}}),c.graph.eachEdge(function(m){var x=m.getGraphicEl(),_=m.getModel().get(["emphasis","focus"]);x&&_==="adjacency"&&(at(x).focus={edge:[m.dataIndex],node:[m.node1.dataIndex,m.node2.dataIndex]})});var d=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),y=c.getLayout("cx"),S=c.getLayout("cy");c.graph.eachNode(function(m){Ul(m,d,y,S)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,r){var n=this;(function i(){t.step(function(o){n.updateLayout(n._model),(n._layouting=!o)&&(r?n._layoutTimeout=setTimeout(i,16):i())})})()},e.prototype._updateController=function(t,r,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Nn(u,n,t)}),!Mo(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Yn(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){Xn(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(),Ea(t.getGraph(),Ye(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,r=t.getData(),n=Ye(t);r.eachItemGraphicEl(function(i,o){i&&i.setSymbolScale(n)})},e.prototype.updateLayout=function(t){Ea(t.getGraph(),Ye(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(ht);const tg=Qp;function Ie(a){return"_EC_"+a}var eg=function(){function a(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return a.prototype.isDirected=function(){return this._directed},a.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var r=this._nodesMap;if(!r[Ie(e)]){var n=new ye(e,t);return n.hostGraph=this,this.nodes.push(n),r[Ie(e)]=n,n}},a.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},a.prototype.getNodeById=function(e){return this._nodesMap[Ie(e)]},a.prototype.addEdge=function(e,t,r){var n=this._nodesMap,i=this._edgesMap;if(Zt(e)&&(e=this.nodes[e]),Zt(t)&&(t=this.nodes[t]),e instanceof ye||(e=n[Ie(e)]),t instanceof ye||(t=n[Ie(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new Xl(e,t,r);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),i[o]=s,s}},a.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},a.prototype.getEdge=function(e,t){e instanceof ye&&(e=e.id),t instanceof ye&&(t=t.id);var r=this._edgesMap;return this._directed?r[e+"-"+t]:r[e+"-"+t]||r[t+"-"+e]},a.prototype.eachNode=function(e,t){for(var r=this.nodes,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&e.call(t,r[i],i)},a.prototype.eachEdge=function(e,t){for(var r=this.edges,n=r.length,i=0;i<n;i++)r[i].dataIndex>=0&&r[i].node1.dataIndex>=0&&r[i].node2.dataIndex>=0&&e.call(t,r[i],i)},a.prototype.breadthFirstTraverse=function(e,t,r,n){if(t instanceof ye||(t=this._nodesMap[Ie(t)]),!!t){for(var i=r==="out"?"outEdges":r==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[i],o=0;o<u.length;o++){var v=u[o],c=v.node1===l?v.node2:v.node1;if(!c.__visited){if(e.call(n,c,l))return;s.push(c),c.__visited=!0}}}},a.prototype.update=function(){for(var e=this.data,t=this.edgeData,r=this.nodes,n=this.edges,i=0,o=r.length;i<o;i++)r[i].dataIndex=-1;for(var i=0,o=e.count();i<o;i++)r[e.getRawIndex(i)].dataIndex=i;t.filterSelf(function(s){var l=n[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var i=0,o=n.length;i<o;i++)n[i].dataIndex=-1;for(var i=0,o=t.count();i<o;i++)n[t.getRawIndex(i)].dataIndex=i},a.prototype.clone=function(){for(var e=new a(this._directed),t=this.nodes,r=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(var n=0;n<r.length;n++){var i=r[n];e.addEdge(i.node1.id,i.node2.id,i.dataIndex)}return e},a}(),ye=function(){function a(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e==null?"":e,this.dataIndex=t==null?-1:t}return a.prototype.degree=function(){return this.edges.length},a.prototype.inDegree=function(){return this.inEdges.length},a.prototype.outDegree=function(){return this.outEdges.length},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var r=this.edges[t];r.dataIndex<0||(e.edge.push(r.dataIndex),e.node.push(r.node1.dataIndex,r.node2.dataIndex))}return e},a.prototype.getTrajectoryDataIndices=function(){for(var e=q(),t=q(),r=0;r<this.edges.length;r++){var n=this.edges[r];if(!(n.dataIndex<0)){e.set(n.dataIndex,!0);for(var i=[n.node1],o=[n.node2],s=0;s<i.length;){var l=i[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),i.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var v=o[s];s++,t.set(v.dataIndex,!0);for(var u=0;u<v.outEdges.length;u++)e.set(v.outEdges[u].dataIndex,!0),o.push(v.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},a}(),Xl=function(){function a(e,t,r){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=r==null?-1:r}return a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.edgeData.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},a.prototype.getTrajectoryDataIndices=function(){var e=q(),t=q();e.set(this.dataIndex,!0);for(var r=[this.node1],n=[this.node2],i=0;i<r.length;){var o=r[i];i++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),r.push(o.inEdges[s].node1)}for(i=0;i<n.length;){var l=n[i];i++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),n.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},a}();function Zl(a,e){return{getValue:function(t){var r=this[a][e];return r.getStore().get(r.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,r){this.dataIndex>=0&&this[a][e].setItemVisual(this.dataIndex,t,r)},getVisual:function(t){return this[a][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,r){this.dataIndex>=0&&this[a][e].setItemLayout(this.dataIndex,t,r)},getLayout:function(){return this[a][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[a][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[a][e].getRawIndex(this.dataIndex)}}}qt(ye,Zl("hostGraph","data"));qt(Xl,Zl("hostGraph","edgeData"));function ql(a,e,t,r,n){for(var i=new eg(r),o=0;o<a.length;o++)i.addNode(Et(a[o].id,a[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var v=e[o],c=v.source,h=v.target;i.addEdge(c,h,u)&&(l.push(v),s.push(Et(vr(v.id,null),c+" > "+h)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=Ve(a,t);else{var g=ol.get(f),d=g?g.dimensions||[]:[];Bt(d,"value")<0&&d.concat(["value"]);var y=Gn(a,{coordDimensions:d,encodeDefine:t.getEncode()}).dimensions;p=new Lt(y,t),p.initData(a)}var S=new Lt(["value"],t);return S.initData(l,s),n&&n(p,S),Ml({mainData:p,struct:i,structAttr:"graph",datas:{node:p,edge:S},datasAttr:{node:"data",edge:"edgeData"}}),i.update(),i}var rg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments);var r=this;function n(){return r._categoriesData}this.legendVisualProvider=new Kr(n,n),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){a.prototype.mergeDefaultAndTheme.apply(this,arguments),ra(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,r){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=this;if(i&&n){Bp(this);var s=ql(i,n,this,!0,l);return L(s.edges,function(u){Fp(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,v){u.wrapMethod("getItemModel",function(p){var g=o._categoriesModels,d=p.getShallow("category"),y=g[d];return y&&(y.parentModel=p.parentModel,p.parentModel=y),p});var c=Wt.prototype.getModel;function h(p,g){var d=c.call(this,p,g);return d.resolveParentPath=f,d}v.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=h,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var g=p.slice();return p[0]==="label"?g[0]="edgeLabel":p[1]==="label"&&(g[1]="edgeLabel"),g}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,r,n){if(n==="edge"){var i=this.getData(),o=this.getDataParams(t,n),s=i.graph.getEdgeByIndex(t),l=i.getName(s.node1.dataIndex),u=i.getName(s.node2.dataIndex),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),It("nameValue",{name:v.join(" > "),value:o.value,noValue:o.value==null})}var c=xv({series:this,dataIndex:t,multipleSeries:r});return c},e.prototype._updateCategoriesData=function(){var t=O(this.option.categories||[],function(n){return n.value!=null?n:$({value:0},n)}),r=new Lt(["value"],this);r.initData(t),this._categoriesData=r,this._categoriesModels=r.mapArray(function(n){return r.getItemModel(n)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return a.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(pt);const ag=rg;var ng={type:"graphRoam",event:"graphRoam",update:"none"};function ig(a){a.registerChartView(tg),a.registerSeriesModel(ag),a.registerProcessor(Vp),a.registerVisual(kp),a.registerVisual(Gp),a.registerLayout(Wp),a.registerLayout(a.PRIORITY.VISUAL.POST_CHART_LAYOUT,Hp),a.registerLayout(Yp),a.registerCoordinateSystem("graphView",{dimensions:gr.dimensions,create:Zp}),a.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},je),a.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},je),a.registerAction(ng,function(e,t,r){t.eachComponent({mainType:"series",query:e},function(n){var i=n.coordinateSystem,o=qn(i,e,void 0,r);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}var og=function(){function a(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return a}(),sg=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="pointer",r}return e.prototype.getDefaultShape=function(){return new og},e.prototype.buildPath=function(t,r){var n=Math.cos,i=Math.sin,o=r.r,s=r.width,l=r.angle,u=r.x-n(l)*s*(s>=o/3?1:2),v=r.y-i(l)*s*(s>=o/3?1:2);l=r.angle-Math.PI/2,t.moveTo(u,v),t.lineTo(r.x+n(l)*s,r.y+i(l)*s),t.lineTo(r.x+n(r.angle)*o,r.y+i(r.angle)*o),t.lineTo(r.x-n(l)*s,r.y-i(l)*s),t.lineTo(u,v)},e}(_t);function lg(a,e){var t=a.get("center"),r=e.getWidth(),n=e.getHeight(),i=Math.min(r,n),o=z(t[0],e.getWidth()),s=z(t[1],e.getHeight()),l=z(a.get("radius"),i/2);return{cx:o,cy:s,r:l}}function wr(a,e){var t=a==null?"":a+"";return e&&(rt(e)?t=e.replace("{value}",t):ot(e)&&(t=e(a))),t}var ug=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=lg(t,n);this._renderMain(t,r,n,i,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,r,n,i,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,v=-t.get("endAngle")/180*Math.PI,c=t.getModel("axisLine"),h=c.get("roundCap"),f=h?Di:Je,p=c.get("show"),g=c.getModel("lineStyle"),d=g.get("width"),y=[u,v];_v(y,!l),u=y[0],v=y[1];for(var S=v-u,m=u,x=[],_=0;p&&_<i.length;_++){var b=Math.min(Math.max(i[_][0],0),1);v=u+S*b;var w=new f({shape:{startAngle:m,endAngle:v,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-d,r:o.r},silent:!0});w.setStyle({fill:i[_][1]}),w.setStyle(g.getLineStyle(["color","width"])),x.push(w),m=v}x.reverse(),L(x,function(I){return s.add(I)});var T=function(I){if(I<=0)return i[0][1];var D;for(D=0;D<i.length;D++)if(i[D][0]>=I&&(D===0?0:i[D-1][0])<I)return i[D][1];return i[D-1][1]};this._renderTicks(t,r,n,T,o,u,v,l,d),this._renderTitleAndDetail(t,r,n,T,o),this._renderAnchor(t,o),this._renderPointer(t,r,n,T,o,u,v,l,d)},e.prototype._renderTicks=function(t,r,n,i,o,s,l,u,v){for(var c=this.group,h=o.cx,f=o.cy,p=o.r,g=+t.get("min"),d=+t.get("max"),y=t.getModel("splitLine"),S=t.getModel("axisTick"),m=t.getModel("axisLabel"),x=t.get("splitNumber"),_=S.get("splitNumber"),b=z(y.get("length"),p),w=z(S.get("length"),p),T=s,I=(l-s)/x,D=I/_,A=y.getModel("lineStyle").getLineStyle(),M=S.getModel("lineStyle").getLineStyle(),E=y.get("distance"),C,P,R=0;R<=x;R++){if(C=Math.cos(T),P=Math.sin(T),y.get("show")){var N=E?E+v:v,k=new xe({shape:{x1:C*(p-N)+h,y1:P*(p-N)+f,x2:C*(p-b-N)+h,y2:P*(p-b-N)+f},style:A,silent:!0});A.stroke==="auto"&&k.setStyle({stroke:i(R/x)}),c.add(k)}if(m.get("show")){var N=m.get("distance")+E,G=wr(zn(R/x*(d-g)+g),m.get("formatter")),W=i(R/x),B=C*(p-b-N)+h,J=P*(p-b-N)+f,K=m.get("rotate"),X=0;K==="radial"?(X=-T+2*Math.PI,X>Math.PI/2&&(X+=Math.PI)):K==="tangential"?X=-T-Math.PI/2:Zt(K)&&(X=K*Math.PI/180),X===0?c.add(new Rt({style:St(m,{text:G,x:B,y:J,verticalAlign:P<-.8?"top":P>.8?"bottom":"middle",align:C<-.4?"left":C>.4?"right":"center"},{inheritColor:W}),silent:!0})):c.add(new Rt({style:St(m,{text:G,x:B,y:J,verticalAlign:"middle",align:"center"},{inheritColor:W}),silent:!0,originX:B,originY:J,rotation:X}))}if(S.get("show")&&R!==x){var N=S.get("distance");N=N?N+v:v;for(var Q=0;Q<=_;Q++){C=Math.cos(T),P=Math.sin(T);var dt=new xe({shape:{x1:C*(p-N)+h,y1:P*(p-N)+f,x2:C*(p-w-N)+h,y2:P*(p-w-N)+f},silent:!0,style:M});M.stroke==="auto"&&dt.setStyle({stroke:i((R+Q/_)/x)}),c.add(dt),T+=D}T-=D}else T+=I}},e.prototype._renderPointer=function(t,r,n,i,o,s,l,u,v){var c=this.group,h=this._data,f=this._progressEls,p=[],g=t.get(["pointer","show"]),d=t.getModel("progress"),y=d.get("show"),S=t.getData(),m=S.mapDimension("value"),x=+t.get("min"),_=+t.get("max"),b=[x,_],w=[s,l];function T(D,A){var M=S.getItemModel(D),E=M.getModel("pointer"),C=z(E.get("width"),o.r),P=z(E.get("length"),o.r),R=t.get(["pointer","icon"]),N=E.get("offsetCenter"),k=z(N[0],o.r),G=z(N[1],o.r),W=E.get("keepAspect"),B;return R?B=Yt(R,k-C/2,G-P,C,P,null,W):B=new sg({shape:{angle:-Math.PI/2,width:C,r:P,x:k,y:G}}),B.rotation=-(A+Math.PI/2),B.x=o.cx,B.y=o.cy,B}function I(D,A){var M=d.get("roundCap"),E=M?Di:Je,C=d.get("overlap"),P=C?d.get("width"):v/S.count(),R=C?o.r-P:o.r-(D+1)*P,N=C?o.r:o.r-D*P,k=new E({shape:{startAngle:s,endAngle:A,cx:o.cx,cy:o.cy,clockwise:u,r0:R,r:N}});return C&&(k.z2=_-S.get(m,D)%_),k}(y||g)&&(S.diff(h).add(function(D){var A=S.get(m,D);if(g){var M=T(D,s);wt(M,{rotation:-((isNaN(+A)?w[0]:Ht(A,b,w,!0))+Math.PI/2)},t),c.add(M),S.setItemGraphicEl(D,M)}if(y){var E=I(D,s),C=d.get("clip");wt(E,{shape:{endAngle:Ht(A,b,w,C)}},t),c.add(E),Ii(t.seriesIndex,S.dataType,D,E),p[D]=E}}).update(function(D,A){var M=S.get(m,D);if(g){var E=h.getItemGraphicEl(A),C=E?E.rotation:s,P=T(D,C);P.rotation=C,lt(P,{rotation:-((isNaN(+M)?w[0]:Ht(M,b,w,!0))+Math.PI/2)},t),c.add(P),S.setItemGraphicEl(D,P)}if(y){var R=f[A],N=R?R.shape.endAngle:s,k=I(D,N),G=d.get("clip");lt(k,{shape:{endAngle:Ht(M,b,w,G)}},t),c.add(k),Ii(t.seriesIndex,S.dataType,D,k),p[D]=k}}).execute(),S.each(function(D){var A=S.getItemModel(D),M=A.getModel("emphasis"),E=M.get("focus"),C=M.get("blurScope"),P=M.get("disabled");if(g){var R=S.getItemGraphicEl(D),N=S.getItemVisual(D,"style"),k=N.fill;if(R instanceof ke){var G=R.style;R.useStyle($({image:G.image,x:G.x,y:G.y,width:G.width,height:G.height},N))}else R.useStyle(N),R.type!=="pointer"&&R.setColor(k);R.setStyle(A.getModel(["pointer","itemStyle"]).getItemStyle()),R.style.fill==="auto"&&R.setStyle("fill",i(Ht(S.get(m,D),b,[0,1],!0))),R.z2EmphasisLift=0,Ct(R,A),ct(R,E,C,P)}if(y){var W=p[D];W.useStyle(S.getItemVisual(D,"style")),W.setStyle(A.getModel(["progress","itemStyle"]).getItemStyle()),W.z2EmphasisLift=0,Ct(W,A),ct(W,E,C,P)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,r){var n=t.getModel("anchor"),i=n.get("show");if(i){var o=n.get("size"),s=n.get("icon"),l=n.get("offsetCenter"),u=n.get("keepAspect"),v=Yt(s,r.cx-o/2+z(l[0],r.r),r.cy-o/2+z(l[1],r.r),o,o,null,u);v.z2=n.get("showAbove")?1:0,v.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(v)}},e.prototype._renderTitleAndDetail=function(t,r,n,i,o){var s=this,l=t.getData(),u=l.mapDimension("value"),v=+t.get("min"),c=+t.get("max"),h=new U,f=[],p=[],g=t.isAnimationEnabled(),d=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(y){f[y]=new Rt({silent:!0}),p[y]=new Rt({silent:!0})}).update(function(y,S){f[y]=s._titleEls[S],p[y]=s._detailEls[S]}).execute(),l.each(function(y){var S=l.getItemModel(y),m=l.get(u,y),x=new U,_=i(Ht(m,[v,c],[0,1],!0)),b=S.getModel("title");if(b.get("show")){var w=b.get("offsetCenter"),T=o.cx+z(w[0],o.r),I=o.cy+z(w[1],o.r),D=f[y];D.attr({z2:d?0:2,style:St(b,{x:T,y:I,text:l.getName(y),align:"center",verticalAlign:"middle"},{inheritColor:_})}),x.add(D)}var A=S.getModel("detail");if(A.get("show")){var M=A.get("offsetCenter"),E=o.cx+z(M[0],o.r),C=o.cy+z(M[1],o.r),P=z(A.get("width"),o.r),R=z(A.get("height"),o.r),N=t.get(["progress","show"])?l.getItemVisual(y,"style").fill:_,D=p[y],k=A.get("formatter");D.attr({z2:d?0:2,style:St(A,{x:E,y:C,text:wr(m,k),width:isNaN(P)?null:P,height:isNaN(R)?null:R,align:"center",verticalAlign:"middle"},{inheritColor:N})}),bv(D,{normal:A},m,function(W){return wr(W,k)}),g&&Xs(D,y,l,t,{getFormattedLabel:function(W,B,J,K,X,Q){return wr(Q?Q.interpolatedValue:m,k)}}),x.add(D)}h.add(x)}),this.group.add(h),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(ht);const vg=ug;var cg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,r){return sr(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(pt);const hg=cg;function fg(a){a.registerChartView(vg),a.registerSeriesModel(hg)}var pg=["itemStyle","opacity"],gg=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=n,o=new fe,s=new Rt;return i.setTextContent(s),n.setTextGuideLine(o),n.updateData(t,r,!0),n}return e.prototype.updateData=function(t,r,n){var i=this,o=t.hostModel,s=t.getItemModel(r),l=t.getItemLayout(r),u=s.getModel("emphasis"),v=s.get(pg);v=v==null?1:v,n||ue(i),i.useStyle(t.getItemVisual(r,"style")),i.style.lineJoin="round",n?(i.setShape({points:l.points}),i.style.opacity=0,wt(i,{style:{opacity:v}},o,r)):lt(i,{style:{opacity:v},shape:{points:l.points}},o,r),Ct(i,s),this._updateLabel(t,r),ct(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),s=t.hostModel,l=t.getItemModel(r),u=t.getItemLayout(r),v=u.label,c=t.getItemVisual(r,"style"),h=c.fill;kt(o,Tt(l),{labelFetcher:t.hostModel,labelDataIndex:r,defaultOpacity:c.opacity,defaultText:t.getName(r)},{normal:{align:v.textAlign,verticalAlign:v.verticalAlign}}),n.setTextConfig({local:!0,inside:!!v.inside,insideStroke:h,outsideFill:h});var f=v.linePoints;i.setShape({points:f}),n.textGuideLineConfig={anchor:f?new ie(f[0][0],f[0][1]):null},lt(o,{style:{x:v.x,y:v.y}},s,r),o.attr({rotation:v.rotation,originX:v.x,originY:v.y,z2:10}),Us(n,Ys(l),{stroke:h})},e}(re),dg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._data,s=this.group;i.diff(o).add(function(l){var u=new gg(i,l);i.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var v=o.getItemGraphicEl(u);v.updateData(i,l),s.add(v),i.setItemGraphicEl(l,v)}).remove(function(l){var u=o.getItemGraphicEl(l);wv(u,t,l)}).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(ht);const yg=dg;var mg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new Kr(tt(this.getData,this),tt(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,r){return sr(this,{coordDimensions:["value"],encodeDefaulter:st(Js,this)})},e.prototype._defaultLabelLine=function(t){ra(t,"labelLine",["show"]);var r=t.labelLine,n=t.emphasis.labelLine;r.show=r.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var r=this.getData(),n=a.prototype.getDataParams.call(this,t),i=r.mapDimension("value"),o=r.getSum(i);return n.percent=o?+(r.get(i,t)/o*100).toFixed(2):0,n.$vars.push("percent"),n},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(pt);const Sg=mg;function xg(a,e){return Jt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function _g(a,e){for(var t=a.mapDimension("value"),r=a.mapArray(t,function(l){return l}),n=[],i=e==="ascending",o=0,s=a.count();o<s;o++)n[o]=o;return ot(e)?n.sort(e):e!=="none"&&n.sort(function(l,u){return i?r[l]-r[u]:r[u]-r[l]}),n}function bg(a){var e=a.hostModel,t=e.get("orient");a.each(function(r){var n=a.getItemModel(r),i=n.getModel("label"),o=i.get("position"),s=n.getModel("labelLine"),l=a.getItemLayout(r),u=l.points,v=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",c,h,f,p;if(v)o==="insideLeft"?(h=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,c="left"):o==="insideRight"?(h=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,c="right"):(h=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,c="center"),p=[[h,f],[h,f]];else{var g=void 0,d=void 0,y=void 0,S=void 0,m=s.get("length");o==="left"?(g=(u[3][0]+u[0][0])/2,d=(u[3][1]+u[0][1])/2,y=g-m,h=y-5,c="right"):o==="right"?(g=(u[1][0]+u[2][0])/2,d=(u[1][1]+u[2][1])/2,y=g+m,h=y+5,c="left"):o==="top"?(g=(u[3][0]+u[0][0])/2,d=(u[3][1]+u[0][1])/2,S=d-m,f=S-5,c="center"):o==="bottom"?(g=(u[1][0]+u[2][0])/2,d=(u[1][1]+u[2][1])/2,S=d+m,f=S+5,c="center"):o==="rightTop"?(g=t==="horizontal"?u[3][0]:u[1][0],d=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(S=d-m,f=S-5,c="center"):(y=g+m,h=y+5,c="top")):o==="rightBottom"?(g=u[2][0],d=u[2][1],t==="horizontal"?(S=d+m,f=S+5,c="center"):(y=g+m,h=y+5,c="bottom")):o==="leftTop"?(g=u[0][0],d=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(S=d-m,f=S-5,c="center"):(y=g-m,h=y-5,c="right")):o==="leftBottom"?(g=t==="horizontal"?u[1][0]:u[3][0],d=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(S=d+m,f=S+5,c="center"):(y=g-m,h=y-5,c="right")):(g=(u[1][0]+u[2][0])/2,d=(u[1][1]+u[2][1])/2,t==="horizontal"?(S=d+m,f=S+5,c="center"):(y=g+m,h=y+5,c="left")),t==="horizontal"?(y=g,h=y):(S=d,f=S),p=[[g,d],[y,S]]}l.label={linePoints:p,x:h,y:f,verticalAlign:"middle",textAlign:c,inside:v}})}function wg(a,e){a.eachSeriesByType("funnel",function(t){var r=t.getData(),n=r.mapDimension("value"),i=t.get("sort"),o=xg(t,e),s=t.get("orient"),l=o.width,u=o.height,v=_g(r,i),c=o.x,h=o.y,f=s==="horizontal"?[z(t.get("minSize"),u),z(t.get("maxSize"),u)]:[z(t.get("minSize"),l),z(t.get("maxSize"),l)],p=r.getDataExtent(n),g=t.get("min"),d=t.get("max");g==null&&(g=Math.min(p[0],0)),d==null&&(d=p[1]);var y=t.get("funnelAlign"),S=t.get("gap"),m=s==="horizontal"?l:u,x=(m-S*(r.count()-1))/r.count(),_=function(C,P){if(s==="horizontal"){var R=r.get(n,C)||0,N=Ht(R,[g,d],f,!0),k=void 0;switch(y){case"top":k=h;break;case"center":k=h+(u-N)/2;break;case"bottom":k=h+(u-N);break}return[[P,k],[P,k+N]]}var G=r.get(n,C)||0,W=Ht(G,[g,d],f,!0),B;switch(y){case"left":B=c;break;case"center":B=c+(l-W)/2;break;case"right":B=c+l-W;break}return[[B,P],[B+W,P]]};i==="ascending"&&(x=-x,S=-S,s==="horizontal"?c+=l:h+=u,v=v.reverse());for(var b=0;b<v.length;b++){var w=v[b],T=v[b+1],I=r.getItemModel(w);if(s==="horizontal"){var D=I.get(["itemStyle","width"]);D==null?D=x:(D=z(D,l),i==="ascending"&&(D=-D));var A=_(w,c),M=_(T,c+D);c+=D+S,r.setItemLayout(w,{points:A.concat(M.slice().reverse())})}else{var E=I.get(["itemStyle","height"]);E==null?E=x:(E=z(E,u),i==="ascending"&&(E=-E));var A=_(w,h),M=_(T,h+E);h+=E+S,r.setItemLayout(w,{points:A.concat(M.slice().reverse())})}}bg(r)})}function Tg(a){a.registerChartView(yg),a.registerSeriesModel(Sg),a.registerLayout(wg),a.registerProcessor(Qr("funnel"))}var Ig=.3,Dg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new U,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,v=u.dimensions,c=Ro(t);s.diff(l).add(h).update(f).remove(p).execute();function h(d){var y=Eo(s,o,d,v,u);Ra(y,s,d,c)}function f(d,y){var S=l.getItemGraphicEl(y),m=Kl(s,d,v,u);s.setItemGraphicEl(d,S),lt(S,{shape:{points:m}},t,d),ue(S),Ra(S,s,d,c)}function p(d){var y=l.getItemGraphicEl(d);o.remove(y)}if(!this._initialized){this._initialized=!0;var g=Ag(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(g)}this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,r,n){for(var i=r.getData(),o=r.coordinateSystem,s=o.dimensions,l=Ro(r),u=this._progressiveEls=[],v=t.start;v<t.end;v++){var c=Eo(i,this._dataGroup,v,s,o);c.incremental=!0,Ra(c,i,v,l),u.push(c)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(ht);function Ag(a,e,t){var r=a.model,n=a.getRect(),i=new Dt({shape:{x:n.x,y:n.y,width:n.width,height:n.height}}),o=r.get("layout")==="horizontal"?"width":"height";return i.setShape(o,0),wt(i,{shape:{width:n.width,height:n.height}},e,t),i}function Kl(a,e,t,r){for(var n=[],i=0;i<t.length;i++){var o=t[i],s=a.get(a.mapDimension(o),e);Lg(s,r.getAxis(o).type)||n.push(r.dataToPoint(s,o))}return n}function Eo(a,e,t,r,n){var i=Kl(a,t,r,n),o=new fe({shape:{points:i},z2:10});return e.add(o),a.setItemGraphicEl(t,o),o}function Ro(a){var e=a.get("smooth",!0);return e===!0&&(e=Ig),e=Tv(e),Iv(e)&&(e=0),{smooth:e}}function Ra(a,e,t,r){a.useStyle(e.getItemVisual(t,"style")),a.style.fill=null,a.setShape("smooth",r.smooth);var n=e.getItemModel(t),i=n.getModel("emphasis");Ct(a,n,"lineStyle"),ct(a,i.get("focus"),i.get("blurScope"),i.get("disabled"))}function Lg(a,e){return e==="category"?a==null:a==null||isNaN(a)}const Cg=Dg;var Pg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,r){return Ve(null,this,{useEncodeDefaulter:tt(Mg,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var r=this.coordinateSystem,n=this.getData(),i=[];return r.eachActiveState(n,function(o,s){t===o&&i.push(n.getRawIndex(s))}),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(pt);function Mg(a){var e=a.ecModel.getComponent("parallel",a.get("parallelIndex"));if(e){var t={};return L(e.dimensions,function(r){var n=Eg(r);t[r]=n}),t}}function Eg(a){return+a.replace("dim","")}const Rg=Pg;var Ng=["lineStyle","opacity"],Vg={seriesType:"parallel",reset:function(a,e){var t=a.coordinateSystem,r={normal:a.get(["lineStyle","opacity"]),active:a.get("activeOpacity"),inactive:a.get("inactiveOpacity")};return{progress:function(n,i){t.eachActiveState(i,function(o,s){var l=r[o];if(o==="normal"&&i.hasItemOption){var u=i.getItemModel(s).get(Ng,!0);u!=null&&(l=u)}var v=i.ensureUniqueItemVisual(s,"style");v.opacity=l},n.start,n.end)}}}};const kg=Vg;function Gg(a){zg(a),Og(a)}function zg(a){if(!a.parallel){var e=!1;L(a.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(a.parallel=[{}])}}function Og(a){var e=Ot(a.parallelAxis);L(e,function(t){if(pr(t)){var r=t.parallelIndex||0,n=Ot(a.parallel)[r];n&&n.parallelAxisDefault&&Xt(t,n.parallelAxisDefault,!1)}})}var Bg=5,Fg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this._model=t,this._api=n,this._handlers||(this._handlers={},L(Wg,function(i,o){n.getZr().on(o,this._handlers[o]=tt(i,this))},this)),sl(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,r){Dv(this,"_throttledDispatchExpand"),L(this._handlers,function(n,i){r.getZr().off(i,n)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction($({type:"parallelAxisExpand"},t))},e.type="parallel",e}(jt),Wg={mousedown:function(a){Na(this,"click")&&(this._mouseDownPoint=[a.offsetX,a.offsetY])},mouseup:function(a){var e=this._mouseDownPoint;if(Na(this,"click")&&e){var t=[a.offsetX,a.offsetY],r=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(r>Bg)return;var n=this._model.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]);n.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:n.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(a){if(!(this._mouseDownPoint||!Na(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]),r=t.behavior;r==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(r==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:r==="jump"?null:{duration:0}})}}};function Na(a,e){var t=a._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}const $g=Fg;var Hg=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var r=this.option;t&&Xt(r,t,!0),this._initDimensions()},e.prototype.contains=function(t,r){var n=t.get("parallelIndex");return n!=null&&r.getComponent("parallel",n)===this},e.prototype.setAxisExpand=function(t){L(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(r){t.hasOwnProperty(r)&&(this.option[r]=t[r])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],r=this.parallelAxisIndex=[],n=Nt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(i){return(i.get("parallelIndex")||0)===this.componentIndex},this);L(n,function(i){t.push("dim"+i.get("dim")),r.push(i.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(Kt);const Ug=Hg;var Yg=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(Jr);const Xg=Yg;var Va=L,jl=Math.min,Jl=Math.max,No=Math.floor,Zg=Math.ceil,Vo=zn,qg=Math.PI,Kg=function(){function a(e,t,r){this.type="parallel",this._axesMap=q(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=e.dimensions,i=e.parallelAxisIndex;Va(n,function(o,s){var l=i[s],u=t.getComponent("parallelAxis",l),v=this._axesMap.set(o,new Xg(o,ll(u),[0,0],u.get("type"),l)),c=v.type==="category";v.onBand=c&&u.get("boundaryGap"),v.inverse=u.get("inverse"),u.axis=v,v.model=u,v.coordinateSystem=u.coordinateSystem=this},this)},a.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},a.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),r=t.axisBase,n=t.layoutBase,i=t.pixelDimIndex,o=e[1-i],s=e[i];return o>=r&&o<=r+t.axisLength&&s>=n&&s<=n+t.layoutLength},a.prototype.getModel=function(){return this._model},a.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(r){if(e.contains(r,t)){var n=r.getData();Va(this.dimensions,function(i){var o=this._axesMap.get(i);o.scale.unionExtentFromData(n,n.mapDimension(i)),ul(o.scale,o.model)},this)}},this)},a.prototype.resize=function(e,t){this._rect=Jt(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},a.prototype.getRect=function(){return this._rect},a.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,r=["x","y"],n=["width","height"],i=e.get("layout"),o=i==="horizontal"?0:1,s=t[n[o]],l=[0,s],u=this.dimensions.length,v=Tr(e.get("axisExpandWidth"),l),c=Tr(e.get("axisExpandCount")||0,[0,u]),h=e.get("axisExpandable")&&u>3&&u>c&&c>1&&v>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=Tr(f[1]-f[0],l),f[1]=f[0]+p;else{p=Tr(v*(c-1),l);var g=e.get("axisExpandCenter")||No(u/2);f=[v*g-p/2],f[1]=f[0]+p}var d=(s-p)/(u-c);d<3&&(d=0);var y=[No(Vo(f[0]/v,1))+1,Zg(Vo(f[1]/v,1))-1],S=d/v*f[0];return{layout:i,pixelDimIndex:o,layoutBase:t[r[o]],layoutLength:s,axisBase:t[r[1-o]],axisLength:t[n[1-o]],axisExpandable:h,axisExpandWidth:v,axisCollapseWidth:d,axisExpandWindow:f,axisCount:u,winInnerIndices:y,axisExpandWindow0Pos:S}},a.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,r=this.dimensions,n=this._makeLayoutInfo(),i=n.layout;t.each(function(o){var s=[0,n.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),Va(r,function(o,s){var l=(n.axisExpandable?Jg:jg)(s,n),u={horizontal:{x:l.position,y:n.axisLength},vertical:{x:0,y:l.position}},v={horizontal:qg/2,vertical:0},c=[u[i].x+e.x,u[i].y+e.y],h=v[i],f=lr();Rn(f,f,h),Ee(f,f,c),this._axesLayout[o]={position:c,rotation:h,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},a.prototype.getAxis=function(e){return this._axesMap.get(e)},a.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},a.prototype.eachActiveState=function(e,t,r,n){r==null&&(r=0),n==null&&(n=e.count());var i=this._axesMap,o=this.dimensions,s=[],l=[];L(o,function(d){s.push(e.mapDimension(d)),l.push(i.get(d).model)});for(var u=this.hasAxisBrushed(),v=r;v<n;v++){var c=void 0;if(!u)c="normal";else{c="active";for(var h=e.getValues(s,v),f=0,p=o.length;f<p;f++){var g=l[f].getActiveState(h[f]);if(g==="inactive"){c="inactive";break}}}t(c,v)}},a.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,r=!1,n=0,i=e.length;n<i;n++)t.get(e[n]).model.getActiveState()!=="normal"&&(r=!0);return r},a.prototype.axisCoordToPoint=function(e,t){var r=this._axesLayout[t];return vl([e,0],r.transform)},a.prototype.getAxisLayout=function(e){return Ft(this._axesLayout[e])},a.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),r=t.pixelDimIndex,n=t.axisExpandWindow.slice(),i=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var s=e[r]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",v=t.axisCollapseWidth,c=this._model.get("axisExpandSlideTriggerArea"),h=c[0]!=null;if(v)h&&v&&s<i*c[0]?(u="jump",l=s-i*c[2]):h&&v&&s>i*(1-c[0])?(u="jump",l=s-i*(1-c[2])):(l=s-i*c[1])>=0&&(l=s-i*(1-c[1]))<=0&&(l=0),l*=t.axisExpandWidth/v,l?Av(l,n,o,"all"):u="none";else{var f=n[1]-n[0],p=o[1]*s/f;n=[Jl(0,p-f/2)],n[1]=jl(o[1],n[0]+f),n[0]=n[1]-f}return{axisExpandWindow:n,behavior:u}},a}();function Tr(a,e){return jl(Jl(a,e[0]),e[1])}function jg(a,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*a,axisNameAvailableWidth:t,axisLabelShow:!0}}function Jg(a,e){var t=e.layoutLength,r=e.axisExpandWidth,n=e.axisCount,i=e.axisCollapseWidth,o=e.winInnerIndices,s,l=i,u=!1,v;return a<o[0]?(s=a*i,v=i):a<=o[1]?(s=e.axisExpandWindow0Pos+a*r-e.axisExpandWindow[0],l=r,u=!0):(s=t-(n-1-a)*i,v=i),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:v}}function Qg(a,e){var t=[];return a.eachComponent("parallel",function(r,n){var i=new Kg(r,a,e);i.name="parallel_"+n,i.resize(r,e),r.coordinateSystem=i,i.model=r,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="parallel"){var n=r.getReferringComponents("parallel",ea).models[0];r.coordinateSystem=n.coordinateSystem}}),t}var td={create:Qg};const ed=td;var Ql=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return el([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var r=this.activeIntervals=Ft(t);if(r)for(var n=r.length-1;n>=0;n--)Qe(r[n])},e.prototype.getActiveState=function(t){var r=this.activeIntervals;if(!r.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(r.length===1){var n=r[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=r.length;i<o;i++)if(r[i][0]<=t&&t<=r[i][1])return"active";return"inactive"},e}(Kt);qt(Ql,Mn);const ko=Ql;var rd=["axisLine","axisTickLabel","axisName"],ad=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){a.prototype.init.apply(this,arguments),(this._brushController=new cl(r.getZr())).on("brush",tt(this._onBrush,this))},e.prototype.render=function(t,r,n,i){if(!nd(t,r,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new U,this.group.add(this._axisGroup),!!t.get("show")){var s=od(t,r),l=s.coordinateSystem,u=t.getAreaSelectStyle(),v=u.width,c=t.axis.dim,h=l.getAxisLayout(c),f=$({strokeContainThreshold:v},h),p=new En(t,f);L(rd,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,v,n),hl(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,r,n,i,o,s){var l=n.axis.getExtent(),u=l[1]-l[0],v=Math.min(30,Math.abs(u)*.1),c=vt.create({x:l[0],y:-o/2,width:u,height:o});c.x-=v,c.width+=2*v,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Lv(c),isTargetByCursor:Cv(c,s,i),getLinearBrushOtherExtent:Pv(c,0)}]).enableBrush({brushType:"lineX",brushStyle:r,removeOnClick:!0}).updateCovers(id(n))},e.prototype._onBrush=function(t){var r=t.areas,n=this.axisModel,i=n.axis,o=O(r,function(s){return[i.coordToData(s.range[0],!0),i.coordToData(s.range[1],!0)]});(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(jt);function nd(a,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===a}function id(a){var e=a.axis;return O(a.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function od(a,e){return e.getComponent("parallel",a.get("parallelIndex"))}const sd=ad;var ld={type:"axisAreaSelect",event:"axisAreaSelected"};function ud(a){a.registerAction(ld,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(r){r.axis.model.setActiveIntervals(e.intervals)})}),a.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(r){r.setAxisExpand(e)})})}var vd={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function tu(a){a.registerComponentView($g),a.registerComponentModel(Ug),a.registerCoordinateSystem("parallel",ed),a.registerPreprocessor(Gg),a.registerComponentModel(ko),a.registerComponentView(sd),fl(a,"parallel",ko,vd),ud(a)}function cd(a){Y(tu),a.registerChartView(Cg),a.registerSeriesModel(Rg),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,kg)}var hd=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return a}(),fd=function(a){V(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new hd},e.prototype.buildPath=function(t,r){var n=r.extent;t.moveTo(r.x1,r.y1),t.bezierCurveTo(r.cpx1,r.cpy1,r.cpx2,r.cpy2,r.x2,r.y2),r.orient==="vertical"?(t.lineTo(r.x2+n,r.y2),t.bezierCurveTo(r.cpx2+n,r.cpy2,r.cpx1+n,r.cpy1,r.x1+n,r.y1)):(t.lineTo(r.x2,r.y2+n),t.bezierCurveTo(r.cpx2,r.cpy2+n,r.cpx1,r.cpy1+n,r.x1,r.y1+n)),t.closePath()},e.prototype.highlight=function(){On(this)},e.prototype.downplay=function(){Bn(this)},e}(_t),pd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,r,n){var i=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,v=l.height,c=t.getData(),h=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var g=new fd,d=at(g);d.dataIndex=p.dataIndex,d.seriesIndex=t.seriesIndex,d.dataType="edge";var y=p.getModel(),S=y.getModel("lineStyle"),m=S.get("curveness"),x=p.node1.getLayout(),_=p.node1.getModel(),b=_.get("localX"),w=_.get("localY"),T=p.node2.getLayout(),I=p.node2.getModel(),D=I.get("localX"),A=I.get("localY"),M=p.getLayout(),E,C,P,R,N,k,G,W;g.shape.extent=Math.max(1,M.dy),g.shape.orient=f,f==="vertical"?(E=(b!=null?b*u:x.x)+M.sy,C=(w!=null?w*v:x.y)+x.dy,P=(D!=null?D*u:T.x)+M.ty,R=A!=null?A*v:T.y,N=E,k=C*(1-m)+R*m,G=P,W=C*m+R*(1-m)):(E=(b!=null?b*u:x.x)+x.dx,C=(w!=null?w*v:x.y)+M.sy,P=D!=null?D*u:T.x,R=(A!=null?A*v:T.y)+M.ty,N=E*(1-m)+P*m,k=C,G=E*m+P*(1-m),W=R),g.setShape({x1:E,y1:C,x2:P,y2:R,cpx1:N,cpy1:k,cpx2:G,cpy2:W}),g.useStyle(S.getItemStyle()),Go(g.style,f,p);var B=""+y.get("value"),J=Tt(y,"edgeLabel");kt(g,J,{labelFetcher:{getFormattedLabel:function(Q,dt,Z,H,et,j){return t.getFormattedLabel(Q,dt,"edge",H,cr(et,J.normal&&J.normal.get("formatter"),B),j)}},labelDataIndex:p.dataIndex,defaultText:B}),g.setTextConfig({position:"inside"});var K=y.getModel("emphasis");Ct(g,y,"lineStyle",function(Q){var dt=Q.getItemStyle();return Go(dt,f,p),dt}),s.add(g),h.setItemGraphicEl(p.dataIndex,g);var X=K.get("focus");ct(g,X==="adjacency"?p.getAdjacentDataIndices():X==="trajectory"?p.getTrajectoryDataIndices():X,K.get("blurScope"),K.get("disabled"))}),o.eachNode(function(p){var g=p.getLayout(),d=p.getModel(),y=d.get("localX"),S=d.get("localY"),m=d.getModel("emphasis"),x=new Dt({shape:{x:y!=null?y*u:g.x,y:S!=null?S*v:g.y,width:g.dx,height:g.dy},style:d.getModel("itemStyle").getItemStyle(),z2:10});kt(x,Tt(d),{labelFetcher:{getFormattedLabel:function(b,w){return t.getFormattedLabel(b,w,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),x.disableLabelAnimation=!0,x.setStyle("fill",p.getVisual("color")),x.setStyle("decal",p.getVisual("style").decal),Ct(x,d),s.add(x),c.setItemGraphicEl(p.dataIndex,x),at(x).dataType="node";var _=m.get("focus");ct(x,_==="adjacency"?p.getAdjacentDataIndices():_==="trajectory"?p.getTrajectoryDataIndices():_,m.get("blurScope"),m.get("disabled"))}),c.eachItemGraphicEl(function(p,g){var d=c.getItemModel(g);d.get("draggable")&&(p.drift=function(y,S){i._focusAdjacencyDisabled=!0,this.shape.x+=y,this.shape.y+=S,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:c.getRawIndex(g),localX:this.shape.x/u,localY:this.shape.y/v})},p.ondragend=function(){i._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(gd(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(ht);function Go(a,e,t){switch(a.fill){case"source":a.fill=t.node1.getVisual("color"),a.decal=t.node1.getVisual("style").decal;break;case"target":a.fill=t.node2.getVisual("color"),a.decal=t.node2.getVisual("style").decal;break;case"gradient":var r=t.node1.getVisual("color"),n=t.node2.getVisual("color");rt(r)&&rt(n)&&(a.fill=new Zs(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:r,offset:0},{color:n,offset:1}]))}}function gd(a,e,t){var r=new Dt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return wt(r,{shape:{width:a.width+20}},e,t),r}const dd=pd;var yd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){var n=t.edges||t.links,i=t.data||t.nodes,o=t.levels;this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Wt(o[l],this,r));if(i&&n){var u=ql(i,n,this,!0,v);return u.data}function v(c,h){c.wrapMethod("getItemModel",function(f,p){var g=f.parentModel,d=g.getData().getItemLayout(p);if(d){var y=d.depth,S=g.levelModels[y];S&&(f.parentModel=S)}return f}),h.wrapMethod("getItemModel",function(f,p){var g=f.parentModel,d=g.getGraph().getEdgeByIndex(p),y=d.node1.getLayout();if(y){var S=y.depth,m=g.levelModels[S];m&&(f.parentModel=m)}return f})}},e.prototype.setNodePosition=function(t,r){var n=this.option.data||this.option.nodes,i=n[t];i.localX=r[0],i.localY=r[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,r,n){function i(f){return isNaN(f)||f==null}if(n==="edge"){var o=this.getDataParams(t,n),s=o.data,l=o.value,u=s.source+" -- "+s.target;return It("nameValue",{name:u,value:l,noValue:i(l)})}else{var v=this.getGraph().getNodeByIndex(t),c=v.getLayout().value,h=this.getDataParams(t,n).data.name;return It("nameValue",{name:h!=null?h+"":null,value:c,noValue:i(c)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,r){var n=a.prototype.getDataParams.call(this,t,r);if(n.value==null&&r==="node"){var i=this.getGraph().getNodeByIndex(t),o=i.getLayout().value;n.value=o}return n},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(pt);const md=yd;function Sd(a,e){a.eachSeriesByType("sankey",function(t){var r=t.get("nodeWidth"),n=t.get("nodeGap"),i=xd(t,e);t.layoutInfo=i;var o=i.width,s=i.height,l=t.getGraph(),u=l.nodes,v=l.edges;bd(u);var c=Nt(u,function(g){return g.getLayout().value===0}),h=c.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");_d(u,v,r,n,o,s,h,f,p)})}function xd(a,e){return Jt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function _d(a,e,t,r,n,i,o,s,l){wd(a,e,t,n,i,s,l),Ad(a,e,i,n,r,o,s),kd(a,s)}function bd(a){L(a,function(e){var t=le(e.outEdges,zr),r=le(e.inEdges,zr),n=e.getValue()||0,i=Math.max(t,r,n);e.setLayout({value:i},!0)})}function wd(a,e,t,r,n,i,o){for(var s=[],l=[],u=[],v=[],c=0,h=0;h<e.length;h++)s[h]=1;for(var h=0;h<a.length;h++)l[h]=a[h].inEdges.length,l[h]===0&&u.push(a[h]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var g=u[p],d=g.hostGraph.data.getRawDataItem(g.dataIndex),y=d.depth!=null&&d.depth>=0;y&&d.depth>f&&(f=d.depth),g.setLayout({depth:y?d.depth:c},!0),i==="vertical"?g.setLayout({dy:t},!0):g.setLayout({dx:t},!0);for(var S=0;S<g.outEdges.length;S++){var m=g.outEdges[S],x=e.indexOf(m);s[x]=0;var _=m.node2,b=a.indexOf(_);--l[b]===0&&v.indexOf(_)<0&&v.push(_)}}++c,u=v,v=[]}for(var h=0;h<s.length;h++)if(s[h]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var w=f>c-1?f:c-1;o&&o!=="left"&&Td(a,o,i,w);var T=i==="vertical"?(n-t)/w:(r-t)/w;Dd(a,T,i)}function eu(a){var e=a.hostGraph.data.getRawDataItem(a.dataIndex);return e.depth!=null&&e.depth>=0}function Td(a,e,t,r){if(e==="right"){for(var n=[],i=a,o=0;i.length;){for(var s=0;s<i.length;s++){var l=i[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var v=l.inEdges[u];n.indexOf(v.node1)<0&&n.push(v.node1)}}i=n,n=[],++o}L(a,function(c){eu(c)||c.setLayout({depth:Math.max(0,r-c.getLayout().skNodeHeight)},!0)})}else e==="justify"&&Id(a,r)}function Id(a,e){L(a,function(t){!eu(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function Dd(a,e,t){L(a,function(r){var n=r.getLayout().depth*e;t==="vertical"?r.setLayout({y:n},!0):r.setLayout({x:n},!0)})}function Ad(a,e,t,r,n,i,o){var s=Ld(a,o);Cd(s,e,t,r,n,o),ka(s,n,t,r,o);for(var l=1;i>0;i--)l*=.99,Pd(s,l,o),ka(s,n,t,r,o),Vd(s,l,o),ka(s,n,t,r,o)}function Ld(a,e){var t=[],r=e==="vertical"?"y":"x",n=fn(a,function(i){return i.getLayout()[r]});return n.keys.sort(function(i,o){return i-o}),L(n.keys,function(i){t.push(n.buckets.get(i))}),t}function Cd(a,e,t,r,n,i){var o=1/0;L(a,function(s){var l=s.length,u=0;L(s,function(c){u+=c.getLayout().value});var v=i==="vertical"?(r-(l-1)*n)/u:(t-(l-1)*n)/u;v<o&&(o=v)}),L(a,function(s){L(s,function(l,u){var v=l.getLayout().value*o;i==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:v},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:v},!0))})}),L(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function ka(a,e,t,r,n){var i=n==="vertical"?"x":"y";L(a,function(o){o.sort(function(g,d){return g.getLayout()[i]-d.getLayout()[i]});for(var s,l,u,v=0,c=o.length,h=n==="vertical"?"dx":"dy",f=0;f<c;f++)l=o[f],u=v-l.getLayout()[i],u>0&&(s=l.getLayout()[i]+u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]+l.getLayout()[h]+e;var p=n==="vertical"?r:t;if(u=v-e-p,u>0){s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),v=s;for(var f=c-2;f>=0;--f)l=o[f],u=l.getLayout()[i]+l.getLayout()[h]+e-v,u>0&&(s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]}})}function Pd(a,e,t){L(a.slice().reverse(),function(r){L(r,function(n){if(n.outEdges.length){var i=le(n.outEdges,Md,t)/le(n.outEdges,zr);if(isNaN(i)){var o=n.outEdges.length;i=o?le(n.outEdges,Ed,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ve(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ve(n,t))*e;n.setLayout({y:l},!0)}}})})}function Md(a,e){return ve(a.node2,e)*a.getValue()}function Ed(a,e){return ve(a.node2,e)}function Rd(a,e){return ve(a.node1,e)*a.getValue()}function Nd(a,e){return ve(a.node1,e)}function ve(a,e){return e==="vertical"?a.getLayout().x+a.getLayout().dx/2:a.getLayout().y+a.getLayout().dy/2}function zr(a){return a.getValue()}function le(a,e,t){for(var r=0,n=a.length,i=-1;++i<n;){var o=+e(a[i],t);isNaN(o)||(r+=o)}return r}function Vd(a,e,t){L(a,function(r){L(r,function(n){if(n.inEdges.length){var i=le(n.inEdges,Rd,t)/le(n.inEdges,zr);if(isNaN(i)){var o=n.inEdges.length;i=o?le(n.inEdges,Nd,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ve(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ve(n,t))*e;n.setLayout({y:l},!0)}}})})}function kd(a,e){var t=e==="vertical"?"x":"y";L(a,function(r){r.outEdges.sort(function(n,i){return n.node2.getLayout()[t]-i.node2.getLayout()[t]}),r.inEdges.sort(function(n,i){return n.node1.getLayout()[t]-i.node1.getLayout()[t]})}),L(a,function(r){var n=0,i=0;L(r.outEdges,function(o){o.setLayout({sy:n},!0),n+=o.getLayout().dy}),L(r.inEdges,function(o){o.setLayout({ty:i},!0),i+=o.getLayout().dy})})}function Gd(a){a.eachSeriesByType("sankey",function(e){var t=e.getGraph(),r=t.nodes,n=t.edges;if(r.length){var i=1/0,o=-1/0;L(r,function(s){var l=s.getLayout().value;l<i&&(i=l),l>o&&(o=l)}),L(r,function(s){var l=new nl({type:"color",mappingMethod:"linear",dataExtent:[i,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),v=s.getModel().get(["itemStyle","color"]);v!=null?(s.setVisual("color",v),s.setVisual("style",{fill:v})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}n.length&&L(n,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function zd(a){a.registerChartView(dd),a.registerSeriesModel(md),a.registerLayout(Sd),a.registerVisual(Gd),a.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(r){r.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var ru=function(){function a(){}return a.prototype.getInitialData=function(e,t){var r,n=t.getComponent("xAxis",this.get("xAxisIndex")),i=t.getComponent("yAxis",this.get("yAxisIndex")),o=n.get("type"),s=i.get("type"),l;o==="category"?(e.layout="horizontal",r=n.getOrdinalMeta(),l=!0):s==="category"?(e.layout="vertical",r=i.getOrdinalMeta(),l=!0):e.layout=e.layout||"horizontal";var u=["x","y"],v=e.layout==="horizontal"?0:1,c=this._baseAxisDim=u[v],h=u[1-v],f=[n,i],p=f[v].get("type"),g=f[1-v].get("type"),d=e.data;if(d&&l){var y=[];L(d,function(x,_){var b;F(x)?(b=x.slice(),x.unshift(_)):F(x.value)?(b=$({},x),b.value=b.value.slice(),x.value.unshift(_)):b=x,y.push(b)}),e.data=y}var S=this.defaultValueDimensions,m=[{name:c,type:pn(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:h,type:pn(g),dimsDef:S.slice()}];return sr(this,{coordDimensions:m,dimensionsCount:S.length+1,encodeDefaulter:st(Mv,m,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),au=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(pt);qt(au,ru,!0);const Od=au;var Bd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;i.diff(s).add(function(u){if(i.hasValue(u)){var v=i.getItemLayout(u),c=zo(v,i,u,l,!0);i.setItemGraphicEl(u,c),o.add(c)}}).update(function(u,v){var c=s.getItemGraphicEl(v);if(!i.hasValue(u)){o.remove(c);return}var h=i.getItemLayout(u);c?(ue(c),nu(h,c,i,u)):c=zo(h,i,u,l),o.add(c),i.setItemGraphicEl(u,c)}).remove(function(u){var v=s.getItemGraphicEl(u);v&&o.remove(v)}).execute(),this._data=i},e.prototype.remove=function(t){var r=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl(function(i){i&&r.remove(i)})},e.type="boxplot",e}(ht),Fd=function(){function a(){}return a}(),Wd=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="boxplotBoxPath",r}return e.prototype.getDefaultShape=function(){return new Fd},e.prototype.buildPath=function(t,r){var n=r.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},e}(_t);function zo(a,e,t,r,n){var i=a.ends,o=new Wd({shape:{points:n?$d(i,r,a):i}});return nu(a,o,e,t,n),o}function nu(a,e,t,r,n){var i=t.hostModel,o=qr[n?"initProps":"updateProps"];o(e,{shape:{points:a.ends}},i,r),e.useStyle(t.getItemVisual(r,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(r),l=s.getModel("emphasis");Ct(e,s),ct(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function $d(a,e,t){return O(a,function(r){return r=r.slice(),r[e]=t.initBaseline,r})}const Hd=Bd;var Ze=L;function Ud(a){var e=Yd(a);Ze(e,function(t){var r=t.seriesModels;r.length&&(Xd(t),Ze(r,function(n,i){Zd(n,t.boxOffsetList[i],t.boxWidthList[i])}))})}function Yd(a){var e=[],t=[];return a.eachSeriesByType("boxplot",function(r){var n=r.getBaseAxis(),i=Bt(t,n);i<0&&(i=t.length,t[i]=n,e[i]={axis:n,seriesModels:[]}),e[i].seriesModels.push(r)}),e}function Xd(a){var e=a.axis,t=a.seriesModels,r=t.length,n=a.boxWidthList=[],i=a.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;Ze(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}Ze(t,function(p){var g=p.get("boxWidth");F(g)||(g=[g,g]),o.push([z(g[0],s)||0,z(g[1],s)||0])});var v=s*.8-2,c=v/r*.3,h=(v-c*(r-1))/r,f=h/2-v/2;Ze(t,function(p,g){i.push(f),f+=c+h,n.push(Math.min(Math.max(h,o[g][0]),o[g][1]))})}function Zd(a,e,t){var r=a.coordinateSystem,n=a.getData(),i=t/2,o=a.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=n.mapDimension(l[o]),v=n.mapDimensionsAll(l[s]);if(u==null||v.length<5)return;for(var c=0;c<n.count();c++){var h=n.get(u,c),f=m(h,v[2],c),p=m(h,v[0],c),g=m(h,v[1],c),d=m(h,v[3],c),y=m(h,v[4],c),S=[];x(S,g,!1),x(S,d,!0),S.push(p,g,y,d),_(S,p),_(S,y),_(S,f),n.setItemLayout(c,{initBaseline:f[s],ends:S})}function m(b,w,T){var I=n.get(w,T),D=[];D[o]=b,D[s]=I;var A;return isNaN(b)||isNaN(I)?A=[NaN,NaN]:(A=r.dataToPoint(D),A[o]+=e),A}function x(b,w,T){var I=w.slice(),D=w.slice();I[o]+=i,D[o]-=i,T?b.push(I,D):b.push(D,I)}function _(b,w){var T=w.slice(),I=w.slice();T[o]-=i,I[o]+=i,b.push(T,I)}}function qd(a,e){e=e||{};for(var t=[],r=[],n=e.boundIQR,i=n==="none"||n===0,o=0;o<a.length;o++){var s=Qe(a[o].slice()),l=va(s,.25),u=va(s,.5),v=va(s,.75),c=s[0],h=s[s.length-1],f=(n==null?1.5:n)*(v-l),p=i?c:Math.max(c,l-f),g=i?h:Math.min(h,v+f),d=e.itemNameFormatter,y=ot(d)?d({value:o}):rt(d)?d.replace("{value}",o+""):o+"";t.push([y,p,l,u,v,g]);for(var S=0;S<s.length;S++){var m=s[S];if(m<p||m>g){var x=[y,m];r.push(x)}}}return{boxData:t,outliers:r}}var Kd={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==pl){var r="";ut(r)}var n=qd(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};function jd(a){a.registerSeriesModel(Od),a.registerChartView(Hd),a.registerLayout(Ud),a.registerTransform(Kd)}var Jd=["color","borderColor"],Qd=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,n){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,n,i){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){na(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),n=this._data,i=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||i.removeAll(),r.diff(n).add(function(v){if(r.hasValue(v)){var c=r.getItemLayout(v);if(s&&Oo(u,c))return;var h=Ga(c,v,!0);wt(h,{shape:{points:c.ends}},t,v),za(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}}).update(function(v,c){var h=n.getItemGraphicEl(c);if(!r.hasValue(v)){i.remove(h);return}var f=r.getItemLayout(v);if(s&&Oo(u,f)){i.remove(h);return}h?(lt(h,{shape:{points:f.ends}},t,v),ue(h)):h=Ga(f),za(h,r,v,o),i.add(h),r.setItemGraphicEl(v,h)}).remove(function(v){var c=n.getItemGraphicEl(v);c&&i.remove(c)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),Bo(t,this.group);var r=t.get("clip",!0)?ia(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var n=r.getData(),i=n.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=n.getItemLayout(o),l=Ga(s);za(l,n,o,i),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){Bo(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(ht),ty=function(){function a(){}return a}(),ey=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new ty},e.prototype.buildPath=function(t,r){var n=r.points;this.__simpleBox?(t.moveTo(n[4][0],n[4][1]),t.lineTo(n[6][0],n[6][1])):(t.moveTo(n[0][0],n[0][1]),t.lineTo(n[1][0],n[1][1]),t.lineTo(n[2][0],n[2][1]),t.lineTo(n[3][0],n[3][1]),t.closePath(),t.moveTo(n[4][0],n[4][1]),t.lineTo(n[5][0],n[5][1]),t.moveTo(n[6][0],n[6][1]),t.lineTo(n[7][0],n[7][1]))},e}(_t);function Ga(a,e,t){var r=a.ends;return new ey({shape:{points:t?ry(r,a):r},z2:100})}function Oo(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function za(a,e,t,r){var n=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,Ct(a,n)}function ry(a,e){return O(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var ay=function(){function a(){}return a}(),Oa=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new ay},e.prototype.buildPath=function(t,r){for(var n=r.points,i=0;i<n.length;)if(this.__sign===n[i++]){var o=n[i++];t.moveTo(o,n[i++]),t.lineTo(o,n[i++])}else i+=3},e}(_t);function Bo(a,e,t,r){var n=a.getData(),i=n.getLayout("largePoints"),o=new Oa({shape:{points:i},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new Oa({shape:{points:i},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new Oa({shape:{points:i},__sign:0,ignoreCoarsePointer:!0});e.add(l),Ba(1,o,a),Ba(-1,s,a),Ba(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function Ba(a,e,t,r){var n=t.get(["itemStyle",a>0?"borderColor":"borderColor0"])||t.get(["itemStyle",a>0?"color":"color0"]);a===0&&(n=t.get(["itemStyle","borderColorDoji"]));var i=t.getModel("itemStyle").getItemStyle(Jd);e.useStyle(i),e.style.fill=null,e.style.stroke=n}const ny=Qd;var iu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,n){var i=r.getItemLayout(t);return i&&n.rect(i.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(pt);qt(iu,ru,!0);const iy=iu;function oy(a){!a||!F(a.series)||L(a.series,function(e){pr(e)&&e.type==="k"&&(e.type="candlestick")})}var sy=["itemStyle","borderColor"],ly=["itemStyle","borderColor0"],uy=["itemStyle","borderColorDoji"],vy=["itemStyle","color"],cy=["itemStyle","color0"],hy={seriesType:"candlestick",plan:Fn(),performRawSeries:!0,reset:function(a,e){function t(i,o){return o.get(i>0?vy:cy)}function r(i,o){return o.get(i===0?uy:i>0?sy:ly)}if(!e.isSeriesFiltered(a)){var n=a.pipelineContext.large;return!n&&{progress:function(i,o){for(var s;(s=i.next())!=null;){var l=o.getItemModel(s),u=o.getItemLayout(s).sign,v=l.getItemStyle();v.fill=t(u,l),v.stroke=r(u,l)||v.fill;var c=o.ensureUniqueItemVisual(s,"style");$(c,v)}}}}}};const fy=hy;var py={seriesType:"candlestick",plan:Fn(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=gy(a,t),n=0,i=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[n])),l=O(t.mapDimensionsAll(o[i]),t.getDimensionIndex,t),u=l[0],v=l[1],c=l[2],h=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(g,d){for(var y,S=d.getStore();(y=g.next())!=null;){var m=S.get(s,y),x=S.get(u,y),_=S.get(v,y),b=S.get(c,y),w=S.get(h,y),T=Math.min(x,_),I=Math.max(x,_),D=N(T,m),A=N(I,m),M=N(b,m),E=N(w,m),C=[];k(C,A,0),k(C,D,1),C.push(W(E),W(A),W(M),W(D));var P=d.getItemModel(y),R=!!P.get(["itemStyle","borderColorDoji"]);d.setItemLayout(y,{sign:Fo(S,y,x,_,v,R),initBaseline:x>_?A[i]:D[i],ends:C,brushRect:G(b,w,m)})}function N(B,J){var K=[];return K[n]=J,K[i]=B,isNaN(J)||isNaN(B)?[NaN,NaN]:e.dataToPoint(K)}function k(B,J,K){var X=J.slice(),Q=J.slice();X[n]=ca(X[n]+r/2,1,!1),Q[n]=ca(Q[n]-r/2,1,!0),K?B.push(X,Q):B.push(Q,X)}function G(B,J,K){var X=N(B,K),Q=N(J,K);return X[n]-=r/2,Q[n]-=r/2,{x:X[0],y:X[1],width:r,height:Q[1]-X[1]}}function W(B){return B[n]=ca(B[n],1),B}}function p(g,d){for(var y=Ev(g.count*4),S=0,m,x=[],_=[],b,w=d.getStore(),T=!!a.get(["itemStyle","borderColorDoji"]);(b=g.next())!=null;){var I=w.get(s,b),D=w.get(u,b),A=w.get(v,b),M=w.get(c,b),E=w.get(h,b);if(isNaN(I)||isNaN(M)||isNaN(E)){y[S++]=NaN,S+=3;continue}y[S++]=Fo(w,b,D,A,v,T),x[n]=I,x[i]=M,m=e.dataToPoint(x,null,_),y[S++]=m?m[0]:NaN,y[S++]=m?m[1]:NaN,x[i]=E,m=e.dataToPoint(x,null,_),y[S++]=m?m[1]:NaN}d.setLayout("largePoints",y)}}};function Fo(a,e,t,r,n,i){var o;return t>r?o=-1:t<r?o=1:o=i?0:e>0?a.get(n,e-1)<=r?1:-1:1,o}function gy(a,e){var t=a.getBaseAxis(),r,n=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),i=z(Ut(a.get("barMaxWidth"),n),n),o=z(Ut(a.get("barMinWidth"),1),n),s=a.get("barWidth");return s!=null?z(s,n):Math.max(Math.min(n/2,i),o)}const dy=py;function yy(a){a.registerChartView(ny),a.registerSeriesModel(iy),a.registerPreprocessor(oy),a.registerVisual(fy),a.registerLayout(dy)}function Wo(a,e){var t=e.rippleEffectColor||e.color;a.eachChild(function(r){r.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var my=function(a){V(e,a);function e(t,r){var n=a.call(this)||this,i=new Qs(t,r),o=new U;return n.add(i),n.add(o),n.updateData(t,r),n}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var r=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),s=0;s<i;s++){var l=Yt(r,-1,-1,2,2,n);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/i*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}Wo(o,t)},e.prototype.updateEffectAnimation=function(t){for(var r=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var s=i[o];if(r[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}Wo(n,t)},e.prototype.highlight=function(){On(this)},e.prototype.downplay=function(){Bn(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,r){var n=this,i=t.hostModel;this.childAt(0).updateData(t,r);var o=this.childAt(1),s=t.getItemModel(r),l=t.getItemVisual(r,"symbol"),u=or(t.getItemVisual(r,"symbolSize")),v=t.getItemVisual(r,"style"),c=v&&v.fill,h=s.getModel("emphasis");o.setScale(u),o.traverse(function(d){d.setStyle("fill",c)});var f=fr(t.getItemVisual(r,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(r,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var g={};g.showEffectOn=i.get("showEffectOn"),g.rippleScale=s.get(["rippleEffect","scale"]),g.brushType=s.get(["rippleEffect","brushType"]),g.period=s.get(["rippleEffect","period"])*1e3,g.effectOffset=r/t.count(),g.z=i.getShallow("z")||0,g.zlevel=i.getShallow("zlevel")||0,g.symbolType=l,g.color=c,g.rippleEffectColor=s.get(["rippleEffect","color"]),g.rippleNumber=s.get(["rippleEffect","number"]),g.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(g):this.startEffectAnimation(g),this._effectCfg=g):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(d){d==="emphasis"?g.showEffectOn!=="render"&&n.startEffectAnimation(g):d==="normal"&&g.showEffectOn!=="render"&&n.stopEffectAnimation()}),this._effectCfg=g,ct(this,h.get("focus"),h.get("blurScope"),h.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(U);const Sy=my;var xy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new Zr(Sy)},e.prototype.render=function(t,r,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var r=t.coordinateSystem,n=r&&r.getArea&&r.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,r,n){var i=t.getData();this.group.dirty();var o=Xr("").reset(t,r,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var r=t.coordinateSystem;r&&r.getRoamTransform&&(this.group.transform=Rv(r.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(ht);const _y=xy;var by=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return Ve(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,r,n){return n.point(r.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(pt);const wy=by;function Ty(a){a.registerChartView(_y),a.registerSeriesModel(wy),a.registerLayout(Xr("effectScatter"))}var Iy=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i.add(i.createLine(t,r,n)),i._updateEffectSymbol(t,r),i}return e.prototype.createLine=function(t,r,n){return new ai(t,r,n)},e.prototype._updateEffectSymbol=function(t,r){var n=t.getItemModel(r),i=n.getModel("effect"),o=i.get("symbolSize"),s=i.get("symbol");F(o)||(o=[o,o]);var l=t.getItemVisual(r,"style"),u=i.get("color")||l&&l.stroke,v=this.childAt(1);this._symbolType!==s&&(this.remove(v),v=Yt(s,-.5,-.5,1,1,u),v.z2=100,v.culling=!0,this.add(v)),v&&(v.setStyle("shadowColor",u),v.setStyle(i.getItemStyle(["color"])),v.scaleX=o[0],v.scaleY=o[1],v.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,i,r))},e.prototype._updateEffectAnimation=function(t,r,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),s=r.get("period")*1e3,l=r.get("loop"),u=r.get("roundTrip"),v=r.get("constantSpeed"),c=Et(r.get("delay"),function(f){return f/t.count()*s/3});if(i.ignore=!0,this._updateAnimationPoints(i,o),v>0&&(s=this._getLineLength(i)/v*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){i.stopAnimation();var h=void 0;ot(c)?h=c(n):h=c,i.__t>0&&(h=-s*i.__t),this._animateSymbol(i,s,h,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,r,n,i,o){if(r>0){t.__t=0;var s=this,l=t.animate("",i).when(o?r*2:r,{__t:o?2:1}).delay(n).during(function(){s._updateSymbolPosition(t)});i||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return He(t.__p1,t.__cp1)+He(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,r){t.__p1=r[0],t.__p2=r[1],t.__cp1=r[2]||[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]},e.prototype.updateData=function(t,r,n){this.childAt(0).updateData(t,r,n),this._updateEffectSymbol(t,r)},e.prototype._updateSymbolPosition=function(t){var r=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=il,v=Nv;s[0]=u(r[0],i[0],n[0],o),s[1]=u(r[1],i[1],n[1],o);var c=t.__t<1?v(r[0],i[0],n[0],o):v(n[0],i[0],r[0],1-o),h=t.__t<1?v(r[1],i[1],n[1],o):v(n[1],i[1],r[1],1-o);t.rotation=-Math.atan2(h,c)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=He(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*He(r,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,r){this.childAt(0).updateLayout(t,r);var n=t.getItemModel(r).getModel("effect");this._updateEffectAnimation(t,n,r)},e}(U);const ou=Iy;var Dy=function(a){V(e,a);function e(t,r,n){var i=a.call(this)||this;return i._createPolyline(t,r,n),i}return e.prototype._createPolyline=function(t,r,n){var i=t.getItemLayout(r),o=new fe({shape:{points:i}});this.add(o),this._updateCommonStl(t,r,n)},e.prototype.updateData=function(t,r,n){var i=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(r)}};lt(o,s,i,r),this._updateCommonStl(t,r,n)},e.prototype._updateCommonStl=function(t,r,n){var i=this.childAt(0),o=t.getItemModel(r),s=n&&n.emphasisLineStyle,l=n&&n.focus,u=n&&n.blurScope,v=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var c=o.getModel("emphasis");s=c.getModel("lineStyle").getLineStyle(),v=c.get("disabled"),l=c.get("focus"),u=c.get("blurScope")}i.useStyle(t.getItemVisual(r,"style")),i.style.fill=null,i.style.strokeNoScale=!0;var h=i.ensureState("emphasis");h.style=s,ct(this,l,u,v)},e.prototype.updateLayout=function(t,r){var n=this.childAt(0);n.setShape("points",t.getItemLayout(r))},e}(U);const su=Dy;var Ay=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,r,n){return new su(t,r,n)},e.prototype._updateAnimationPoints=function(t,r){this._points=r;for(var n=[0],i=0,o=1;o<r.length;o++){var s=r[o-1],l=r[o];i+=He(s,l),n.push(i)}if(i===0){this._length=0;return}for(var o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var r=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var s=this._lastFrame,l;if(r<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(i[l]<=r);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(i[l]>r);l++);l=Math.min(l-1,o-2)}var v=(r-i[l])/(i[l+1]-i[l]),c=n[l],h=n[l+1];t.x=c[0]*(1-v)+v*h[0],t.y=c[1]*(1-v)+v*h[1];var f=t.__t<1?h[0]-c[0]:c[0]-h[0],p=t.__t<1?h[1]-c[1]:c[1]-h[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=r,t.ignore=!1}},e}(ou);const Ly=Ay;var Cy=function(){function a(){this.polyline=!1,this.curveness=0,this.segs=[]}return a}(),Py=function(a){V(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Cy},e.prototype.buildPath=function(t,r){var n=r.segs,i=r.curveness,o;if(r.polyline)for(o=this._off;o<n.length;){var s=n[o++];if(s>0){t.moveTo(n[o++],n[o++]);for(var l=1;l<s;l++)t.lineTo(n[o++],n[o++])}}else for(o=this._off;o<n.length;){var u=n[o++],v=n[o++],c=n[o++],h=n[o++];if(t.moveTo(u,v),i>0){var f=(u+c)/2-(v-h)*i,p=(v+h)/2-(c-u)*i;t.quadraticCurveTo(f,p,c,h)}else t.lineTo(c,h)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,r){var n=this.shape,i=n.segs,o=n.curveness,s=this.style.lineWidth;if(n.polyline)for(var l=0,u=0;u<i.length;){var v=i[u++];if(v>0)for(var c=i[u++],h=i[u++],f=1;f<v;f++){var p=i[u++],g=i[u++];if(Ai(c,h,p,g,s,t,r))return l}l++}else for(var l=0,u=0;u<i.length;){var c=i[u++],h=i[u++],p=i[u++],g=i[u++];if(o>0){var d=(c+p)/2-(h-g)*o,y=(h+g)/2-(p-c)*o;if(Vv(c,h,d,y,p,g,s,t,r))return l}else if(Ai(c,h,p,g,s,t,r))return l;l++}return-1},e.prototype.contain=function(t,r){var n=this.transformCoordToLocal(t,r),i=this.getBoundingRect();if(t=n[0],r=n[1],i.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,n=r.segs,i=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<n.length;){var v=n[u++],c=n[u++];i=Math.min(v,i),s=Math.max(v,s),o=Math.min(c,o),l=Math.max(c,l)}t=this._rect=new vt(i,o,s,l)}return t},e}(_t),My=function(){function a(){this.group=new U}return a.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},a.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},a.prototype.incrementalUpdate=function(e,t){var r=this._newAdded[0],n=t.getLayout("linesPoints"),i=r&&r.shape.segs;if(i&&i.length<2e4){var o=i.length,s=new Float32Array(o+n.length);s.set(i),s.set(n,o),r.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:n}),this._setCommon(l,t),l.__startIndex=e.start}},a.prototype.remove=function(){this._clear()},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new Py({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},a.prototype._setCommon=function(e,t,r){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get(["lineStyle","curveness"])}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var i=t.getVisual("style");i&&i.stroke&&e.setStyle("stroke",i.stroke),e.setStyle("fill",null);var o=at(e);o.seriesIndex=n.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Ey={seriesType:"lines",plan:Fn(),reset:function(a){var e=a.coordinateSystem;if(e){var t=a.get("polyline"),r=a.pipelineContext.large;return{progress:function(n,i){var o=[];if(r){var s=void 0,l=n.end-n.start;if(t){for(var u=0,v=n.start;v<n.end;v++)u+=a.getLineCoordsCount(v);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var c=0,h=[],v=n.start;v<n.end;v++){var f=a.getLineCoords(v,o);t&&(s[c++]=f);for(var p=0;p<f;p++)h=e.dataToPoint(o[p],!1,h),s[c++]=h[0],s[c++]=h[1]}i.setLayout("linesPoints",s)}else for(var v=n.start;v<n.end;v++){var g=i.getItemModel(v),f=a.getLineCoords(v,o),d=[];if(t)for(var y=0;y<f;y++)d.push(e.dataToPoint(o[y]));else{d[0]=e.dataToPoint(o[0]),d[1]=e.dataToPoint(o[1]);var S=g.get(["lineStyle","curveness"]);+S&&(d[2]=[(d[0][0]+d[1][0])/2-(d[0][1]-d[1][1])*S,(d[0][1]+d[1][1])/2-(d[1][0]-d[0][0])*S])}i.setItemLayout(v,d)}}}}}};const lu=Ey;var Ry=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=n.getZr(),v=u.painter.getType()==="svg";v||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!v&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(v||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(i);var c=t.get("clip",!0)&&ia(t.coordinateSystem,!1,t);c?this.group.setClipPath(c):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,n){var i=t.getData(),o=this._updateLineDraw(i,t);o.incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,n){this._lineDraw.incrementalUpdate(t,r.getData()),this._finished=t.end===r.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,r,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=lu.reset(t,r,n);s.progress&&s.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,r){var n=this._lineDraw,i=this._showEffect(r),o=!!r.get("polyline"),s=r.pipelineContext,l=s.large;return(!n||i!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(n&&n.remove(),n=this._lineDraw=l?new My:new ni(o?i?Ly:su:i?ou:ai),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=l),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var r=t.getZr(),n=r.painter.getType()==="svg";!n&&this._lastZlevel!=null&&r.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,r){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(r)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.type="lines",e}(ht);const Ny=Ry;var Vy=typeof Uint32Array=="undefined"?Array:Uint32Array,ky=typeof Float64Array=="undefined"?Array:Float64Array;function $o(a){var e=a.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(a.data=O(e,function(t){var r=[t[0].coord,t[1].coord],n={coords:r};return t[0].name&&(n.fromName=t[0].name),t[1].name&&(n.toName=t[1].name),kn([n,t[0],t[1]])}))}var Gy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],$o(t);var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count)),a.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if($o(t),t.data){var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count))}a.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var r=this._processFlatCoordsArray(t.data);r.flatCoords&&(this._flatCoords?(this._flatCoords=ln(this._flatCoords,r.flatCoords),this._flatCoordsOffset=ln(this._flatCoordsOffset,r.flatCoordsOffset)):(this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset),t.data=new Float32Array(r.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var r=this.getData().getItemModel(t),n=r.option instanceof Array?r.option:r.getShallow("coords");return n},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,r){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[t*2],i=this._flatCoordsOffset[t*2+1],o=0;o<i;o++)r[o]=r[o]||[],r[o][0]=this._flatCoords[n+o*2],r[o][1]=this._flatCoords[n+o*2+1];return i}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)r[o]=r[o]||[],r[o][0]=s[o][0],r[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var r=0;if(this._flatCoords&&(r=this._flatCoords.length),Zt(t[0])){for(var n=t.length,i=new Vy(n),o=new ky(n),s=0,l=0,u=0,v=0;v<n;){u++;var c=t[v++];i[l++]=s+r,i[l++]=c;for(var h=0;h<c;h++){var f=t[v++],p=t[v++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,r){var n=new Lt(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],function(i,o,s,l){if(i instanceof Array)return NaN;n.hasItemOption=!0;var u=i.value;if(u!=null)return u instanceof Array?u[l]:u}),n},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),It("nameValue",{name:v.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t==null?this.option.large?1e4:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t==null?this.option.large?2e4:this.get("progressiveThreshold"):t},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),r=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&r>0?r+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(pt);const zy=Gy;function Ir(a){return a instanceof Array||(a=[a,a]),a}var Oy={seriesType:"lines",reset:function(a){var e=Ir(a.get("symbol")),t=Ir(a.get("symbolSize")),r=a.getData();r.setVisual("fromSymbol",e&&e[0]),r.setVisual("toSymbol",e&&e[1]),r.setVisual("fromSymbolSize",t&&t[0]),r.setVisual("toSymbolSize",t&&t[1]);function n(i,o){var s=i.getItemModel(o),l=Ir(s.getShallow("symbol",!0)),u=Ir(s.getShallow("symbolSize",!0));l[0]&&i.setItemVisual(o,"fromSymbol",l[0]),l[1]&&i.setItemVisual(o,"toSymbol",l[1]),u[0]&&i.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&i.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:r.hasItemOption?n:null}}};const By=Oy;function Fy(a){a.registerChartView(Ny),a.registerSeriesModel(zy),a.registerLayout(lu),a.registerVisual(By)}var Wy=256,$y=function(){function a(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=Li.createCanvas();this.canvas=e}return a.prototype.update=function(e,t,r,n,i,o){var s=this._getBrush(),l=this._getGradient(i,"inRange"),u=this._getGradient(i,"outOfRange"),v=this.pointSize+this.blurSize,c=this.canvas,h=c.getContext("2d"),f=e.length;c.width=t,c.height=r;for(var p=0;p<f;++p){var g=e[p],d=g[0],y=g[1],S=g[2],m=n(S);h.globalAlpha=m,h.drawImage(s,d-v,y-v)}if(!c.width||!c.height)return c;for(var x=h.getImageData(0,0,c.width,c.height),_=x.data,b=0,w=_.length,T=this.minOpacity,I=this.maxOpacity,D=I-T;b<w;){var m=_[b+3]/256,A=Math.floor(m*(Wy-1))*4;if(m>0){var M=o(m)?l:u;m>0&&(m=m*D+T),_[b++]=M[A],_[b++]=M[A+1],_[b++]=M[A+2],_[b++]=M[A+3]*m*256}else b+=4}return h.putImageData(x,0,0),c},a.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=Li.createCanvas()),t=this.pointSize+this.blurSize,r=t*2;e.width=r,e.height=r;var n=e.getContext("2d");return n.clearRect(0,0,r,r),n.shadowOffsetX=r,n.shadowBlur=this.blurSize,n.shadowColor="#000",n.beginPath(),n.arc(-t,t,this.pointSize,0,Math.PI*2,!0),n.closePath(),n.fill(),e},a.prototype._getGradient=function(e,t){for(var r=this._gradientPixels,n=r[t]||(r[t]=new Uint8ClampedArray(256*4)),i=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,i),n[o++]=i[0],n[o++]=i[1],n[o++]=i[2],n[o++]=i[3];return n},a}();const Hy=$y;function Uy(a,e,t){var r=a[1]-a[0];e=O(e,function(o){return{interval:[(o.interval[0]-a[0])/r,(o.interval[1]-a[0])/r]}});var n=e.length,i=0;return function(o){var s;for(s=i;s<n;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}if(s===n)for(s=i-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}return s>=0&&s<n&&t[s]}}function Yy(a,e){var t=a[1]-a[0];return e=[(e[0]-a[0])/t,(e[1]-a[0])/t],function(r){return r>=e[0]&&r<=e[1]}}function Ho(a){var e=a.dimensions;return e[0]==="lng"&&e[1]==="lat"}var Xy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i;r.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(i=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):Ho(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,r,n,i){var o=r.coordinateSystem;o&&(Ho(o)?this.render(r,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(r,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){na(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,r,n,i,o){var s=t.coordinateSystem,l=oa(s,"cartesian2d"),u,v,c,h;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,v=p.getBandWidth()+.5,c=f.scale.getExtent(),h=p.scale.getExtent()}for(var g=this.group,d=t.getData(),y=t.getModel(["emphasis","itemStyle"]).getItemStyle(),S=t.getModel(["blur","itemStyle"]).getItemStyle(),m=t.getModel(["select","itemStyle"]).getItemStyle(),x=t.get(["itemStyle","borderRadius"]),_=Tt(t),b=t.getModel("emphasis"),w=b.get("focus"),T=b.get("blurScope"),I=b.get("disabled"),D=l?[d.mapDimension("x"),d.mapDimension("y"),d.mapDimension("value")]:[d.mapDimension("time"),d.mapDimension("value")],A=n;A<i;A++){var M=void 0,E=d.getItemVisual(A,"style");if(l){var C=d.get(D[0],A),P=d.get(D[1],A);if(isNaN(d.get(D[2],A))||isNaN(C)||isNaN(P)||C<c[0]||C>c[1]||P<h[0]||P>h[1])continue;var R=s.dataToPoint([C,P]);M=new Dt({shape:{x:R[0]-u/2,y:R[1]-v/2,width:u,height:v},style:E})}else{if(isNaN(d.get(D[1],A)))continue;M=new Dt({z2:1,shape:s.dataToRect([d.get(D[0],A)]).contentShape,style:E})}if(d.hasItemOption){var N=d.getItemModel(A),k=N.getModel("emphasis");y=k.getModel("itemStyle").getItemStyle(),S=N.getModel(["blur","itemStyle"]).getItemStyle(),m=N.getModel(["select","itemStyle"]).getItemStyle(),x=N.get(["itemStyle","borderRadius"]),w=k.get("focus"),T=k.get("blurScope"),I=k.get("disabled"),_=Tt(N)}M.shape.r=x;var G=t.getRawValue(A),W="-";G&&G[2]!=null&&(W=G[2]+""),kt(M,_,{labelFetcher:t,labelDataIndex:A,defaultOpacity:E.opacity,defaultText:W}),M.ensureState("emphasis").style=y,M.ensureState("blur").style=S,M.ensureState("select").style=m,ct(M,w,T,I),M.incremental=o,o&&(M.states.emphasis.hoverLayer=!0),g.add(M),d.setItemGraphicEl(A,M),this._progressiveEls&&this._progressiveEls.push(M)}},e.prototype._renderOnGeo=function(t,r,n,i){var o=n.targetVisuals.inRange,s=n.targetVisuals.outOfRange,l=r.getData(),u=this._hmLayer||this._hmLayer||new Hy;u.blurSize=r.get("blurSize"),u.pointSize=r.get("pointSize"),u.minOpacity=r.get("minOpacity"),u.maxOpacity=r.get("maxOpacity");var v=t.getViewRect().clone(),c=t.getRoamTransform();v.applyTransform(c);var h=Math.max(v.x,0),f=Math.max(v.y,0),p=Math.min(v.width+v.x,i.getWidth()),g=Math.min(v.height+v.y,i.getHeight()),d=p-h,y=g-f,S=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],m=l.mapArray(S,function(w,T,I){var D=t.dataToPoint([w,T]);return D[0]-=h,D[1]-=f,D.push(I),D}),x=n.getExtent(),_=n.type==="visualMap.continuous"?Yy(x,n.option.range):Uy(x,n.getPieceList(),n.option.selected);u.update(m,d,y,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},_);var b=new ke({style:{width:d,height:y,x:h,y:f,image:u.canvas},silent:!0});this.group.add(b)},e.type="heatmap",e}(ht);const Zy=Xy;var qy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return Ve(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=ol.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(pt);const Ky=qy;function jy(a){a.registerChartView(Zy),a.registerSeriesModel(Ky)}var Jy=["itemStyle","borderWidth"],Uo=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],Fa=new jr,Qy=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),v=u.isHorizontal(),c=l.master.getRect(),h={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[c.x,c.x+c.width],[c.y,c.y+c.height]],isHorizontal:v,valueDim:Uo[+v],categoryDim:Uo[1-+v]};o.diff(s).add(function(p){if(o.hasValue(p)){var g=Xo(o,p),d=Yo(o,p,g,h),y=Zo(o,h,d);o.setItemGraphicEl(p,y),i.add(y),Ko(y,h,d)}}).update(function(p,g){var d=s.getItemGraphicEl(g);if(!o.hasValue(p)){i.remove(d);return}var y=Xo(o,p),S=Yo(o,p,y,h),m=pu(o,S);d&&m!==d.__pictorialShapeStr&&(i.remove(d),o.setItemGraphicEl(p,null),d=null),d?om(d,h,S):d=Zo(o,h,S,!0),o.setItemGraphicEl(p,d),d.__pictorialSymbolMeta=S,i.add(d),Ko(d,h,S)}).remove(function(p){var g=s.getItemGraphicEl(p);g&&qo(s,p,g.__pictorialSymbolMeta.animationModel,g)}).execute();var f=t.get("clip",!0)?ia(t.coordinateSystem,!1,t):null;return f?i.setClipPath(f):i.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,r){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(o){qo(i,at(o).dataIndex,t,o)}):n.removeAll()},e.type="pictorialBar",e}(ht);function Yo(a,e,t,r){var n=a.getItemLayout(e),i=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,v=t.get("symbolPatternSize")||2,c=t.isAnimationEnabled(),h={dataIndex:e,layout:n,itemModel:t,symbolType:a.getItemVisual(e,"symbol")||"circle",style:a.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:i,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:v,rotation:u,animationModel:c?t:null,hoverScale:c&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};tm(t,i,n,r,h),em(a,e,n,i,o,h.boundingLength,h.pxSign,v,r,h),rm(t,h.symbolScale,u,r,h);var f=h.symbolSize,p=fr(t.get("symbolOffset"),f);return am(t,f,n,i,o,p,s,h.valueLineWidth,h.boundingLength,h.repeatCutLength,r,h),h}function tm(a,e,t,r,n){var i=r.valueDim,o=a.get("symbolBoundingData"),s=r.coordSys.getOtherAxis(r.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[i.wh]<=0),v;if(F(o)){var c=[Wa(s,o[0])-l,Wa(s,o[1])-l];c[1]<c[0]&&c.reverse(),v=c[u]}else o!=null?v=Wa(s,o)-l:e?v=r.coordSysExtent[i.index][u]-l:v=t[i.wh];n.boundingLength=v,e&&(n.repeatCutLength=t[i.wh]),n.pxSign=v>0?1:-1}function Wa(a,e){return a.toGlobalCoord(a.dataToCoord(a.scale.parse(e)))}function em(a,e,t,r,n,i,o,s,l,u){var v=l.valueDim,c=l.categoryDim,h=Math.abs(t[c.wh]),f=a.getItemVisual(e,"symbolSize"),p;F(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[c.index]=z(p[c.index],h),p[v.index]=z(p[v.index],r?h:Math.abs(i)),u.symbolSize=p;var g=u.symbolScale=[p[0]/s,p[1]/s];g[v.index]*=(l.isHorizontal?-1:1)*o}function rm(a,e,t,r,n){var i=a.get(Jy)||0;i&&(Fa.attr({scaleX:e[0],scaleY:e[1],rotation:t}),Fa.updateTransform(),i/=Fa.getLineScale(),i*=e[r.valueDim.index]),n.valueLineWidth=i||0}function am(a,e,t,r,n,i,o,s,l,u,v,c){var h=v.categoryDim,f=v.valueDim,p=c.pxSign,g=Math.max(e[f.index]+s,0),d=g;if(r){var y=Math.abs(l),S=Et(a.get("symbolMargin"),"15%")+"",m=!1;S.lastIndexOf("!")===S.length-1&&(m=!0,S=S.slice(0,S.length-1));var x=z(S,e[f.index]),_=Math.max(g+x*2,0),b=m?0:x*2,w=kv(r),T=w?r:jo((y+b)/_),I=y-T*g;x=I/2/(m?T:Math.max(T-1,1)),_=g+x*2,b=m?0:x*2,!w&&r!=="fixed"&&(T=u?jo((Math.abs(u)+b)/_):0),d=T*_-b,c.repeatTimes=T,c.symbolMargin=x}var D=p*(d/2),A=c.pathPosition=[];A[h.index]=t[h.wh]/2,A[f.index]=o==="start"?D:o==="end"?l-D:l/2,i&&(A[0]+=i[0],A[1]+=i[1]);var M=c.bundlePosition=[];M[h.index]=t[h.xy],M[f.index]=t[f.xy];var E=c.barRectShape=$({},t);E[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(A[f.index]+D)),E[h.wh]=t[h.wh];var C=c.clipShape={};C[h.xy]=-t[h.xy],C[h.wh]=v.ecSize[h.wh],C[f.xy]=0,C[f.wh]=t[f.wh]}function uu(a){var e=a.symbolPatternSize,t=Yt(a.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function vu(a,e,t,r){var n=a.__pictorialBundle,i=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,v=0,c=i[e.valueDim.index]+o+t.symbolMargin*2;for(ii(a,function(g){g.__pictorialAnimationIndex=v,g.__pictorialRepeatTimes=u,v<u?Me(g,null,p(v),t,r):Me(g,null,{scaleX:0,scaleY:0},t,r,function(){n.remove(g)}),v++});v<u;v++){var h=uu(t);h.__pictorialAnimationIndex=v,h.__pictorialRepeatTimes=u,n.add(h);var f=p(v);Me(h,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,r)}function p(g){var d=s.slice(),y=t.pxSign,S=g;return(t.symbolRepeatDirection==="start"?y>0:y<0)&&(S=u-1-g),d[l.index]=c*(S-u/2+.5)+s[l.index],{x:d[0],y:d[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function cu(a,e,t,r){var n=a.__pictorialBundle,i=a.__pictorialMainPath;i?Me(i,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,r):(i=a.__pictorialMainPath=uu(t),n.add(i),Me(i,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,r))}function hu(a,e,t){var r=$({},e.barRectShape),n=a.__pictorialBarRect;n?Me(n,null,{shape:r},e,t):(n=a.__pictorialBarRect=new Dt({z2:2,shape:r,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),n.disableMorphing=!0,a.add(n))}function fu(a,e,t,r){if(t.symbolClip){var n=a.__pictorialClipPath,i=$({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(n)lt(n,{shape:i},s,l);else{i[o.wh]=0,n=new Dt({shape:i}),a.__pictorialBundle.setClipPath(n),a.__pictorialClipPath=n;var u={};u[o.wh]=t.clipShape[o.wh],qr[r?"updateProps":"initProps"](n,{shape:u},s,l)}}}function Xo(a,e){var t=a.getItemModel(e);return t.getAnimationDelayParams=nm,t.isAnimationEnabled=im,t}function nm(a){return{index:a.__pictorialAnimationIndex,count:a.__pictorialRepeatTimes}}function im(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function Zo(a,e,t,r){var n=new U,i=new U;return n.add(i),n.__pictorialBundle=i,i.x=t.bundlePosition[0],i.y=t.bundlePosition[1],t.symbolRepeat?vu(n,e,t):cu(n,e,t),hu(n,t,r),fu(n,e,t,r),n.__pictorialShapeStr=pu(a,t),n.__pictorialSymbolMeta=t,n}function om(a,e,t){var r=t.animationModel,n=t.dataIndex,i=a.__pictorialBundle;lt(i,{x:t.bundlePosition[0],y:t.bundlePosition[1]},r,n),t.symbolRepeat?vu(a,e,t,!0):cu(a,e,t,!0),hu(a,t,!0),fu(a,e,t,!0)}function qo(a,e,t,r){var n=r.__pictorialBarRect;n&&n.removeTextContent();var i=[];ii(r,function(o){i.push(o)}),r.__pictorialMainPath&&i.push(r.__pictorialMainPath),r.__pictorialClipPath&&(t=null),L(i,function(o){Mr(o,{scaleX:0,scaleY:0},t,e,function(){r.parent&&r.parent.remove(r)})}),a.setItemGraphicEl(e,null)}function pu(a,e){return[a.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function ii(a,e,t){L(a.__pictorialBundle.children(),function(r){r!==a.__pictorialBarRect&&e.call(t,r)})}function Me(a,e,t,r,n,i){e&&a.attr(e),r.symbolClip&&!n?t&&a.attr(t):t&&qr[n?"updateProps":"initProps"](a,t,r.animationModel,r.dataIndex,i)}function Ko(a,e,t){var r=t.dataIndex,n=t.itemModel,i=n.getModel("emphasis"),o=i.getModel("itemStyle").getItemStyle(),s=n.getModel(["blur","itemStyle"]).getItemStyle(),l=n.getModel(["select","itemStyle"]).getItemStyle(),u=n.getShallow("cursor"),v=i.get("focus"),c=i.get("blurScope"),h=i.get("scale");ii(a,function(g){if(g instanceof ke){var d=g.style;g.useStyle($({image:d.image,x:d.x,y:d.y,width:d.width,height:d.height},t.style))}else g.useStyle(t.style);var y=g.ensureState("emphasis");y.style=o,h&&(y.scaleX=g.scaleX*1.1,y.scaleY=g.scaleY*1.1),g.ensureState("blur").style=s,g.ensureState("select").style=l,u&&(g.cursor=u),g.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=a.__pictorialBarRect;p.ignoreClip=!0,kt(p,Tt(n),{labelFetcher:e.seriesModel,labelDataIndex:r,defaultText:gn(e.seriesModel.getData(),r),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),ct(a,v,c,i.get("disabled"))}function jo(a){var e=Math.round(a);return Math.abs(a-e)<1e-4?e:Math.ceil(a)}const sm=Qy;var lm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,a.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=gl(Ci.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(Ci);const um=lm;function vm(a){a.registerChartView(sm),a.registerSeriesModel(um),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,st(Gv,"pictorialBar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,zv("pictorialBar"))}var cm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,r,n){var i=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=i.getLayout("layoutInfo"),v=u.rect,c=u.boundaryGap;s.x=0,s.y=v.y+c[0];function h(d){return d.name}var f=new Ne(this._layersSeries||[],l,h,h),p=[];f.add(tt(g,this,"add")).update(tt(g,this,"update")).remove(tt(g,this,"remove")).execute();function g(d,y,S){var m=o._layers;if(d==="remove"){s.remove(m[y]);return}for(var x=[],_=[],b,w=l[y].indices,T=0;T<w.length;T++){var I=i.getItemLayout(w[T]),D=I.x,A=I.y0,M=I.y;x.push(D,A),_.push(D,A+M),b=i.getItemVisual(w[T],"style")}var E,C=i.getItemLayout(w[0]),P=t.getModel("label"),R=P.get("margin"),N=t.getModel("emphasis");if(d==="add"){var k=p[y]=new U;E=new Ov({shape:{points:x,stackedOnPoints:_,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),k.add(E),s.add(k),t.isAnimationEnabled()&&E.setClipPath(hm(E.getBoundingRect(),t,function(){E.removeClipPath()}))}else{var k=m[S];E=k.childAt(0),s.add(k),p[y]=k,lt(E,{shape:{points:x,stackedOnPoints:_}},t),ue(E)}kt(E,Tt(t),{labelDataIndex:w[T-1],defaultText:i.getName(w[T-1]),inheritColor:b.fill},{normal:{verticalAlign:"middle"}}),E.setTextConfig({position:null,local:!0});var G=E.getTextContent();G&&(G.x=C.x-R,G.y=C.y0+C.y/2),E.useStyle(b),i.setItemGraphicEl(y,E),Ct(E,t),ct(E,N.get("focus"),N.get("blurScope"),N.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(ht);function hm(a,e,t){var r=new Dt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return wt(r,{shape:{x:a.x-50,width:a.width+100,height:a.height+20}},e,t),r}const fm=cm;var $a=2,pm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new Kr(tt(this.getData,this),tt(this.getRawData,this))},e.prototype.fixData=function(t){var r=t.length,n={},i=fn(t,function(h){return n.hasOwnProperty(h[0]+"")||(n[h[0]+""]=-1),h[2]}),o=[];i.buckets.each(function(h,f){o.push({name:f,dataList:h})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,v=0;v<o[l].dataList.length;++v){var c=o[l].dataList[v][0]+"";n[c]=l}for(var c in n)n.hasOwnProperty(c)&&n[c]!==l&&(n[c]=l,t[r]=[c,0,u],r++)}return t},e.prototype.getInitialData=function(t,r){for(var n=this.getReferringComponents("singleAxis",ea).models[0],i=n.get("type"),o=Nt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=q(),v=0,c=0;c<s.length;++c)l.push(s[c][$a]),u.get(s[c][$a])||(u.set(s[c][$a],v),v++);var h=Gn(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:pn(i)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new Lt(h,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),r=t.count(),n=[],i=0;i<r;++i)n[i]=i;var o=t.mapDimension("single"),s=fn(n,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,v){u.sort(function(c,h){return t.get(o,c)-t.get(o,h)}),l.push({name:v,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,r,n){F(t)||(t=t?[t]:[]);for(var i=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,v=0;v<l;++v){for(var c=Number.MAX_VALUE,h=-1,f=o[v].indices.length,p=0;p<f;++p){var g=i.get(t[0],o[v].indices[p]),d=Math.abs(g-r);d<=c&&(u=g,c=d,h=o[v].indices[p])}s.push(h)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=i.getName(t),s=i.get(i.mapDimension("value"),t);return It("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(pt);const gm=pm;function dm(a,e){a.eachSeriesByType("themeRiver",function(t){var r=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var s=t.get("boundaryGap"),l=n.getAxis();if(i.boundaryGap=s,l.orient==="horizontal"){s[0]=z(s[0],o.height),s[1]=z(s[1],o.height);var u=o.height-s[0]-s[1];Jo(r,t,u)}else{s[0]=z(s[0],o.width),s[1]=z(s[1],o.width);var v=o.width-s[0]-s[1];Jo(r,t,v)}r.setLayout("layoutInfo",i)})}function Jo(a,e,t){if(a.count())for(var r=e.coordinateSystem,n=e.getLayerSeries(),i=a.mapDimension("single"),o=a.mapDimension("value"),s=O(n,function(d){return O(d.indices,function(y){var S=r.dataToPoint(a.get(i,y));return S[1]=a.get(o,y),S})}),l=ym(s),u=l.y0,v=t/l.max,c=n.length,h=n[0].indices.length,f,p=0;p<h;++p){f=u[p]*v,a.setItemLayout(n[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*v});for(var g=1;g<c;++g)f+=s[g-1][p][1]*v,a.setItemLayout(n[g].indices[p],{layerIndex:g,x:s[g][p][0],y0:f,y:s[g][p][1]*v})}}function ym(a){for(var e=a.length,t=a[0].length,r=[],n=[],i=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=a[l][o][1];s>i&&(i=s),r.push(s)}for(var u=0;u<t;++u)n[u]=(i-r[u])/2;i=0;for(var v=0;v<t;++v){var c=r[v]+n[v];c>i&&(i=c)}return{y0:n,max:i}}function mm(a){a.registerChartView(fm),a.registerSeriesModel(gm),a.registerLayout(dm),a.registerProcessor(Qr("themeRiver"))}var Sm=2,xm=4,Qo=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this)||this;o.z2=Sm,o.textConfig={inside:!0},at(o).seriesIndex=r.seriesIndex;var s=new Rt({z2:xm,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,r,n,i),o}return e.prototype.updateData=function(t,r,n,i,o){this.node=r,r.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var s=this;at(s).dataIndex=r.dataIndex;var l=r.getModel(),u=l.getModel("emphasis"),v=r.getLayout(),c=$({},v);c.label=null;var h=r.getVisual("style");h.lineJoin="bevel";var f=r.getVisual("decal");f&&(h.decal=Vn(f,o));var p=Pi(l.getModel("itemStyle"),c,!0);$(c,p),L(hn,function(S){var m=s.ensureState(S),x=l.getModel([S,"itemStyle"]);m.style=x.getItemStyle();var _=Pi(x,c);_&&(m.shape=_)}),t?(s.setShape(c),s.shape.r=v.r0,wt(s,{shape:{r:v.r}},n,r.dataIndex)):(lt(s,{shape:c},n),ue(s)),s.useStyle(h),this._updateLabel(n);var g=l.getShallow("cursor");g&&s.attr("cursor",g),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var d=u.get("focus"),y=d==="ancestor"?r.getAncestorsIndices():d==="descendant"?r.getDescendantIndices():d;ct(this,y,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var r=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),v=Math.sin(l),c=this,h=c.getTextContent(),f=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,g=i.get("show")&&!(p!=null&&Math.abs(s)<p);h.ignore=!g,L(Bv,function(y){var S=y==="normal"?n.getModel("label"):n.getModel([y,"label"]),m=y==="normal",x=m?h:h.ensureState(y),_=t.getFormattedLabel(f,y);m&&(_=_||r.node.name),x.style=St(S,{},null,y!=="normal",!0),_&&(x.style.text=_);var b=S.get("show");b!=null&&!m&&(x.ignore=!b);var w=d(S,"position"),T=m?c:c.states[y],I=T.style.fill;T.textConfig={outsideFill:S.get("color")==="inherit"?I:null,inside:w!=="outside"};var D,A=d(S,"distance")||0,M=d(S,"align"),E=d(S,"rotate"),C=Math.PI*.5,P=Math.PI*1.5,R=$e(E==="tangential"?Math.PI/2-l:l),N=R>C&&!Fv(R-C)&&R<P;w==="outside"?(D=o.r+A,M=N?"right":"left"):!M||M==="center"?(s===2*Math.PI&&o.r0===0?D=0:D=(o.r+o.r0)/2,M="center"):M==="left"?(D=o.r0+A,M=N?"right":"left"):M==="right"&&(D=o.r-A,M=N?"left":"right"),x.style.align=M,x.style.verticalAlign=d(S,"verticalAlign")||"middle",x.x=D*u+o.cx,x.y=D*v+o.cy;var k=0;E==="radial"?k=$e(-l)+(N?Math.PI:0):E==="tangential"?k=$e(Math.PI/2-l)+(N?Math.PI:0):Zt(E)&&(k=E*Math.PI/180),x.rotation=$e(k)});function d(y,S){var m=y.get(S);return m==null?i.get(S):m}h.dirtyStyle()},e}(Je),bn="sunburstRootToNode",ts="sunburstHighlight",_m="sunburstUnhighlight";function bm(a){a.registerAction({type:bn,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},r);function r(n,i){var o=tr(e,[bn],n);if(o){var s=n.getViewRoot();s&&(e.direction=Jn(s,o.node)?"rollUp":"drillDown"),n.resetViewRoot(o.node)}}}),a.registerAction({type:ts,update:"none"},function(e,t,r){e=$({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},n);function n(i){var o=tr(e,[ts],i);o&&(e.dataIndex=o.node.dataIndex)}r.dispatchAction($(e,{type:"highlight"}))}),a.registerAction({type:_m,update:"updateView"},function(e,t,r){e=$({},e),r.dispatchAction($(e,{type:"downplay"}))})}var wm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=r;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),v=this.group,c=t.get("renderLabelForZeroData"),h=[];u.eachNode(function(S){h.push(S)});var f=this._oldChildren||[];p(h,f),y(l,u),this._initEvents(),this._oldChildren=h;function p(S,m){if(S.length===0&&m.length===0)return;new Ne(m,S,x,x).add(_).update(_).remove(st(_,null)).execute();function x(b){return b.getId()}function _(b,w){var T=b==null?null:S[b],I=w==null?null:m[w];g(T,I)}}function g(S,m){if(!c&&S&&!S.getValue()&&(S=null),S!==l&&m!==l){if(m&&m.piece)S?(m.piece.updateData(!1,S,t,r,n),s.setItemGraphicEl(S.dataIndex,m.piece)):d(m);else if(S){var x=new Qo(S,t,r,n);v.add(x),s.setItemGraphicEl(S.dataIndex,x)}}}function d(S){S&&S.piece&&(v.remove(S.piece),S.piece=null)}function y(S,m){m.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,S,t,r,n):(o.virtualPiece=new Qo(S,t,r,n),v.add(o.virtualPiece)),m.piece.off("click"),o.virtualPiece.on("click",function(x){o._rootToNode(m.parentNode)})):o.virtualPiece&&(v.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(r){var n=!1,i=t.seriesModel.getViewRoot();i.eachNode(function(o){if(!n&&o.piece&&o.piece===r.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var v=l.get("target",!0)||"_blank";rl(u,v)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:bn,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,r){var n=r.getData(),i=n.getItemLayout(0);if(i){var o=t[0]-i.cx,s=t[1]-i.cy,l=Math.sqrt(o*o+s*s);return l<=i.r&&l>=i.r0}},e.type="sunburst",e}(ht);const Tm=wm;var Im=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,r){var n={name:t.name,children:t.data};gu(n);var i=this._levelModels=O(t.levels||[],function(l){return new Wt(l,this,r)},this),o=jn.createTree(n,this,s);function s(l){l.wrapMethod("getItemModel",function(u,v){var c=o.getNodeByDataIndex(v),h=i[c.depth];return h&&(u.parentModel=h),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return r.treePathInfo=la(n,this),r},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Nl(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(pt);function gu(a){var e=0;L(a.children,function(r){gu(r);var n=r.value;F(n)&&(n=n[0]),e+=n});var t=a.value;F(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),F(a.value)?a.value[0]=t:a.value=t}const Dm=Im;var es=Math.PI/180;function Am(a,e,t){e.eachSeriesByType(a,function(r){var n=r.get("center"),i=r.get("radius");F(i)||(i=[0,i]),F(n)||(n=[n,n]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=z(n[0],o),v=z(n[1],s),c=z(i[0],l/2),h=z(i[1],l/2),f=-r.get("startAngle")*es,p=r.get("minAngle")*es,g=r.getData().tree.root,d=r.getViewRoot(),y=d.depth,S=r.get("sort");S!=null&&du(d,S);var m=0;L(d.children,function(R){!isNaN(R.getValue())&&m++});var x=d.getValue(),_=Math.PI/(x||m)*2,b=d.depth>0,w=d.height-(b?-1:1),T=(h-c)/(w||1),I=r.get("clockwise"),D=r.get("stillShowZeroSum"),A=I?1:-1,M=function(R,N){if(R){var k=N;if(R!==g){var G=R.getValue(),W=x===0&&D?_:G*_;W<p&&(W=p),k=N+A*W;var B=R.depth-y-(b?-1:1),J=c+T*B,K=c+T*(B+1),X=r.getLevelModel(R);if(X){var Q=X.get("r0",!0),dt=X.get("r",!0),Z=X.get("radius",!0);Z!=null&&(Q=Z[0],dt=Z[1]),Q!=null&&(J=z(Q,l/2)),dt!=null&&(K=z(dt,l/2))}R.setLayout({angle:W,startAngle:N,endAngle:k,clockwise:I,cx:u,cy:v,r0:J,r:K})}if(R.children&&R.children.length){var H=0;L(R.children,function(et){H+=M(et,N+H)})}return k-N}};if(b){var E=c,C=c+T,P=Math.PI*2;g.setLayout({angle:P,startAngle:f,endAngle:f+P,clockwise:I,cx:u,cy:v,r0:E,r:C})}M(d,f)})}function du(a,e){var t=a.children||[];a.children=Lm(t,e),t.length&&L(a.children,function(r){du(r,e)})}function Lm(a,e){if(ot(e)){var t=O(a,function(n,i){var o=n.getValue();return{params:{depth:n.depth,height:n.height,dataIndex:n.dataIndex,getValue:function(){return o}},index:i}});return t.sort(function(n,i){return e(n.params,i.params)}),O(t,function(n){return a[n.index]})}else{var r=e==="asc";return a.sort(function(n,i){var o=(n.getValue()-i.getValue())*(r?1:-1);return o===0?(n.dataIndex-i.dataIndex)*(r?-1:1):o})}}function Cm(a){var e={};function t(r,n,i){for(var o=r;o&&o.depth>1;)o=o.parentNode;var s=n.getColorFromPalette(o.name||o.dataIndex+"",e);return r.depth>1&&rt(s)&&(s=Wv(s,(r.depth-1)/(i-1)*.5)),s}a.eachSeriesByType("sunburst",function(r){var n=r.getData(),i=n.tree;i.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,r,i.root.height));var u=n.ensureUniqueItemVisual(o.dataIndex,"style");$(u,l)})})}function Pm(a){a.registerChartView(Tm),a.registerSeriesModel(Dm),a.registerLayout(st(Am,"sunburst")),a.registerProcessor(st(Qr,"sunburst")),a.registerVisual(Cm),bm(a)}var rs={color:"fill",borderColor:"stroke"},Mm={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},te=xt(),Em=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,r){return Ve(null,this)},e.prototype.getDataParams=function(t,r,n){var i=a.prototype.getDataParams.call(this,t,r);return n&&(i.info=te(n).info),i},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(pt);const Rm=Em;function Nm(a,e){return e=e||[0,0],O(["x","y"],function(t,r){var n=this.getAxis(t),i=e[r],o=a[r]/2;return n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))},this)}function Vm(a){var e=a.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:tt(Nm,a)}}}function km(a,e){return e=e||[0,0],O([0,1],function(t){var r=e[t],n=a[t]/2,i=[],o=[];return i[t]=r-n,o[t]=r+n,i[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(i)[t]-this.dataToPoint(o)[t])},this)}function Gm(a){var e=a.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:a.getZoom()},api:{coord:function(t){return a.dataToPoint(t)},size:tt(km,a)}}}function zm(a,e){var t=this.getAxis(),r=e instanceof Array?e[0]:e,n=(a instanceof Array?a[0]:a)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(r-n)-t.dataToCoord(r+n))}function Om(a){var e=a.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:tt(zm,a)}}}function Bm(a,e){return e=e||[0,0],O(["Radius","Angle"],function(t,r){var n="get"+t+"Axis",i=this[n](),o=e[r],s=a[r]/2,l=i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(o-s)-i.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function Fm(a){var e=a.getRadiusAxis(),t=a.getAngleAxis(),r=e.getExtent();return r[0]>r[1]&&r.reverse(),{coordSys:{type:"polar",cx:a.cx,cy:a.cy,r:r[1],r0:r[0]},api:{coord:function(n){var i=e.dataToRadius(n[0]),o=t.dataToAngle(n[1]),s=a.coordToPoint([i,o]);return s.push(i,o*Math.PI/180),s},size:tt(Bm,a)}}}function Wm(a){var e=a.getRect(),t=a.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:a.getCellWidth(),cellHeight:a.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(r,n){return a.dataToPoint(r,n)}}}}var ee="emphasis",oe="normal",oi="blur",si="select",ce=[oe,ee,oi,si],Ha={normal:["itemStyle"],emphasis:[ee,"itemStyle"],blur:[oi,"itemStyle"],select:[si,"itemStyle"]},Ua={normal:["label"],emphasis:[ee,"label"],blur:[oi,"label"],select:[si,"label"]},$m=["x","y"],Hm="e\0\0",Mt={normal:{},emphasis:{},blur:{},select:{}},Um={cartesian2d:Vm,geo:Gm,single:Om,polar:Fm,calendar:Wm};function wn(a){return a instanceof _t}function Tn(a){return a instanceof Ke}function Ym(a,e){e.copyTransform(a),Tn(e)&&Tn(a)&&(e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel,e.invisible=a.invisible,e.ignore=a.ignore,wn(e)&&wn(a)&&e.setShape(a.shape))}var Xm=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n,i){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=as(t,s,r,n);o||l.removeAll(),s.diff(o).add(function(c){Ya(n,null,c,u(c,i),t,l,s)}).remove(function(c){var h=o.getItemGraphicEl(c);h&&Wn(h,te(h).option,t)}).update(function(c,h){var f=o.getItemGraphicEl(h);Ya(n,f,c,u(c,i),t,l,s)}).execute();var v=t.get("clip",!0)?ia(t.coordinateSystem,!1,t):null;v?l.setClipPath(v):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,r,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,r,n,i,o){var s=r.getData(),l=as(r,s,n,i),u=this._progressiveEls=[];function v(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var c=t.start;c<t.end;c++){var h=Ya(null,null,c,l(c,o),r,this.group,s);h&&(h.traverse(v),u.push(h))}},e.prototype.eachRendered=function(t){na(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,r,n,i){var o=r.element;if(o==null||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(ht);const Zm=Xm;function li(a){var e=a.type,t;if(e==="path"){var r=a.shape,n=r.width!=null&&r.height!=null?{x:r.x||0,y:r.y||0,width:r.width,height:r.height}:null,i=Su(r);t=Xv(i,null,n,r.layout||"center"),te(t).customPathData=i}else if(e==="image")t=new ke({}),te(t).customImagePath=a.style.image;else if(e==="text")t=new Rt({});else if(e==="group")t=new U;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=Zv(e);if(!o){var s="";ut(s)}t=new o}return te(t).customGraphicType=e,t.name=a.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function ui(a,e,t,r,n,i,o){qv(e);var s=n&&n.normal.cfg;s&&e.setTextConfig(s),r&&r.transition==null&&(r.transition=$m);var l=r&&r.style;if(l){if(e.type==="text"){var u=l;gt(u,"textFill")&&(u.fill=u.textFill),gt(u,"textStroke")&&(u.stroke=u.textStroke)}var v=void 0,c=wn(e)?l.decal:null;a&&c&&(c.dirty=!0,v=Vn(c,a)),l.__decalPattern=v}if(Tn(e)&&l){var v=l.__decalPattern;v&&(l.decal=v)}Kv(e,r,i,{dataIndex:t,isInit:o,clearStyle:!0}),jv(e,r.keyframeAnimation,i)}function yu(a,e,t,r,n){var i=e.isGroup?null:e,o=n&&n[a].cfg;if(i){var s=i.ensureState(a);if(r===!1){var l=i.getState(a);l&&(l.style=null)}else s.style=r||null;o&&(s.textConfig=o),Re(i)}}function qm(a,e,t){if(!a.isGroup){var r=a,n=t.currentZ,i=t.currentZLevel;r.z=n,r.zlevel=i;var o=e.z2;o!=null&&(r.z2=o||0);for(var s=0;s<ce.length;s++)Km(r,e,ce[s])}}function Km(a,e,t){var r=t===oe,n=r?e:Or(e,t),i=n?n.z2:null,o;i!=null&&(o=r?a:a.ensureState(t),o.z2=i||0)}function as(a,e,t,r){var n=a.get("renderItem"),i=a.coordinateSystem,o={};i&&(o=i.prepareCustoms?i.prepareCustoms(i):Um[i.type](i));for(var s=nt({getWidth:r.getWidth,getHeight:r.getHeight,getZr:r.getZr,getDevicePixelRatio:r.getDevicePixelRatio,value:x,style:b,ordinalRawValue:_,styleEmphasis:w,visual:D,barLayout:A,currentSeriesIndices:M,font:E},o.api||{}),l={context:{},seriesId:a.id,seriesName:a.name,seriesIndex:a.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:jm(a.getData())},u,v,c={},h={},f={},p={},g=0;g<ce.length;g++){var d=ce[g];f[d]=a.getModel(Ha[d]),p[d]=a.getModel(Ua[d])}function y(C){return C===u?v||(v=e.getItemModel(C)):e.getItemModel(C)}function S(C,P){return e.hasItemOption?C===u?c[P]||(c[P]=y(C).getModel(Ha[P])):y(C).getModel(Ha[P]):f[P]}function m(C,P){return e.hasItemOption?C===u?h[P]||(h[P]=y(C).getModel(Ua[P])):y(C).getModel(Ua[P]):p[P]}return function(C,P){return u=C,v=null,c={},h={},n&&n(nt({dataIndexInside:C,dataIndex:e.getRawIndex(C),actionType:P?P.type:null},l),s)};function x(C,P){return P==null&&(P=u),e.getStore().get(e.getDimensionIndex(C||0),P)}function _(C,P){P==null&&(P=u),C=C||0;var R=e.getDimensionInfo(C);if(!R){var N=e.getDimensionIndex(C);return N>=0?e.getStore().get(N,P):void 0}var k=e.get(R.name,P),G=R&&R.ordinalMeta;return G?G.categories[k]:k}function b(C,P){P==null&&(P=u);var R=e.getItemVisual(P,"style"),N=R&&R.fill,k=R&&R.opacity,G=S(P,oe).getItemStyle();N!=null&&(G.fill=N),k!=null&&(G.opacity=k);var W={inheritColor:rt(N)?N:"#000"},B=m(P,oe),J=St(B,null,W,!1,!0);J.text=B.getShallow("show")?Ut(a.getFormattedLabel(P,oe),gn(e,P)):null;var K=Mi(B,W,!1);return I(C,G),G=Ei(G,J,K),C&&T(G,C),G.legacy=!0,G}function w(C,P){P==null&&(P=u);var R=S(P,ee).getItemStyle(),N=m(P,ee),k=St(N,null,null,!0,!0);k.text=N.getShallow("show")?cr(a.getFormattedLabel(P,ee),a.getFormattedLabel(P,oe),gn(e,P)):null;var G=Mi(N,null,!0);return I(C,R),R=Ei(R,k,G),C&&T(R,C),R.legacy=!0,R}function T(C,P){for(var R in P)gt(P,R)&&(C[R]=P[R])}function I(C,P){C&&(C.textFill&&(P.textFill=C.textFill),C.textPosition&&(P.textPosition=C.textPosition))}function D(C,P){if(P==null&&(P=u),gt(rs,C)){var R=e.getItemVisual(P,"style");return R?R[rs[C]]:null}if(gt(Mm,C))return e.getItemVisual(P,C)}function A(C){if(i.type==="cartesian2d"){var P=i.getBaseAxis();return $v(nt({axis:P},C))}}function M(){return t.getCurrentSeriesIndices()}function E(C){return Hv(C,t)}}function jm(a){var e={};return L(a.dimensions,function(t){var r=a.getDimensionInfo(t);if(!r.isExtraCoord){var n=r.coordDim,i=e[n]=e[n]||[];i[r.coordDimIndex]=a.getDimensionIndex(t)}}),e}function Ya(a,e,t,r,n,i,o){if(!r){i.remove(e);return}var s=vi(a,e,t,r,n,i);return s&&o.setItemGraphicEl(t,s),s&&ct(s,r.focus,r.blurScope,r.emphasisDisabled),s}function vi(a,e,t,r,n,i){var o=-1,s=e;e&&mu(e,r,n)&&(o=Bt(i.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=li(r),s&&Ym(s,u)),r.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),Mt.normal.cfg=Mt.normal.conOpt=Mt.emphasis.cfg=Mt.emphasis.conOpt=Mt.blur.cfg=Mt.blur.conOpt=Mt.select.cfg=Mt.select.conOpt=null,Mt.isLegacy=!1,Qm(u,t,r,n,l,Mt),Jm(u,t,r,n,l),ui(a,u,t,r,Mt,n,l),gt(r,"info")&&(te(u).info=r.info);for(var v=0;v<ce.length;v++){var c=ce[v];if(c!==oe){var h=Or(r,c),f=ci(r,h,c);yu(c,u,h,f,Mt)}}return qm(u,r,n),r.type==="group"&&t0(a,u,t,r,n),o>=0?i.replaceAt(u,o):i.add(u),u}function mu(a,e,t){var r=te(a),n=e.type,i=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||n!=null&&n!==r.customGraphicType||n==="path"&&n0(i)&&Su(i)!==r.customPathData||n==="image"&&gt(o,"image")&&o.image!==r.customImagePath}function Jm(a,e,t,r,n){var i=t.clipPath;if(i===!1)a&&a.getClipPath()&&a.removeClipPath();else if(i){var o=a.getClipPath();o&&mu(o,i,r)&&(o=null),o||(o=li(i),a.setClipPath(o)),ui(null,o,e,i,null,r,n)}}function Qm(a,e,t,r,n,i){if(!a.isGroup){ns(t,null,i),ns(t,ee,i);var o=i.normal.conOpt,s=i.emphasis.conOpt,l=i.blur.conOpt,u=i.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var v=a.getTextContent();if(o===!1)v&&a.removeTextContent();else{o=i.normal.conOpt=o||{type:"text"},v?v.clearStates():(v=li(o),a.setTextContent(v)),ui(null,v,e,o,null,r,n);for(var c=o&&o.style,h=0;h<ce.length;h++){var f=ce[h];if(f!==oe){var p=i[f].conOpt;yu(f,v,p,ci(o,p,f),null)}}c?v.dirty():v.markRedraw()}}}}function ns(a,e,t){var r=e?Or(a,e):a,n=e?ci(a,r,ee):a.style,i=a.type,o=r?r.textConfig:null,s=a.textContent,l=s?e?Or(s,e):s:null;if(n&&(t.isLegacy||Uv(n,i,!!o,!!l))){t.isLegacy=!0;var u=Yv(n,i,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var v=l;!v.type&&(v.type="text")}var c=e?t[e]:t.normal;c.cfg=o,c.conOpt=l}function Or(a,e){return e?a?a[e]:null:a}function ci(a,e,t){var r=e&&e.style;return r==null&&t===ee&&a&&(r=a.styleEmphasis),r}function t0(a,e,t,r,n){var i=r.children,o=i?i.length:0,s=r.$mergeChildren,l=s==="byName"||r.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){r0({api:a,oldChildren:e.children()||[],newChildren:i||[],dataIndex:t,seriesModel:n,group:e});return}u&&e.removeAll();for(var v=0;v<o;v++){var c=i[v],h=e.childAt(v);c?(c.ignore==null&&(c.ignore=!1),vi(a,h,t,c,n,e)):h.ignore=!0}for(var f=e.childCount()-1;f>=v;f--){var p=e.childAt(f);e0(e,p,n)}}}function e0(a,e,t){e&&Wn(e,te(a).option,t)}function r0(a){new Ne(a.oldChildren,a.newChildren,is,is,a).add(os).update(os).remove(a0).execute()}function is(a,e){var t=a&&a.name;return t!=null?t:Hm+e}function os(a,e){var t=this.context,r=a!=null?t.newChildren[a]:null,n=e!=null?t.oldChildren[e]:null;vi(t.api,n,t.dataIndex,r,t.seriesModel,t.group)}function a0(a){var e=this.context,t=e.oldChildren[a];t&&Wn(t,te(t).option,e.seriesModel)}function Su(a){return a&&(a.pathData||a.d)}function n0(a){return a&&(gt(a,"pathData")||gt(a,"d"))}function i0(a){a.registerChartView(Zm),a.registerSeriesModel(Rm)}function In(a,e){e=e||{};var t=a.coordinateSystem,r=a.axis,n={},i=r.position,o=r.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};n.position=[o==="vertical"?u.vertical[i]:l[0],o==="horizontal"?u.horizontal[i]:l[3]];var v={horizontal:0,vertical:1};n.rotation=Math.PI/2*v[o];var c={top:-1,bottom:1,right:1,left:-1};n.labelDirection=n.tickDirection=n.nameDirection=c[i],a.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),Et(e.labelInside,a.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var h=e.rotate;return h==null&&(h=a.get(["axisLabel","rotate"])),n.labelRotation=i==="top"?-h:h,n.z2=1,n}var o0=["axisLine","axisTickLabel","axisName"],s0=["splitArea","splitLine"],l0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,r,n,i){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new U;var l=In(t),u=new En(t,l);L(o0,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),L(s0,function(v){t.get([v,"show"])&&u0[v](this,this.group,this._axisGroup,t)},this),hl(s,this._axisGroup,t),a.prototype.render.call(this,t,r,n,i)},e.prototype.remove=function(){Jv(this)},e.type="singleAxis",e}(dl),u0={splitLine:function(a,e,t,r){var n=r.axis;if(!n.scale.isBlank()){var i=r.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=r.coordinateSystem.getRect(),v=n.isHorizontal(),c=[],h=0,f=n.getTicksCoords({tickModel:i}),p=[],g=[],d=0;d<f.length;++d){var y=n.toGlobalCoord(f[d].coord);v?(p[0]=y,p[1]=u.y,g[0]=y,g[1]=u.y+u.height):(p[0]=u.x,p[1]=y,g[0]=u.x+u.width,g[1]=y);var S=new xe({shape:{x1:p[0],y1:p[1],x2:g[0],y2:g[1]},silent:!0});Qv(S.shape,l);var m=h++%s.length;c[m]=c[m]||[],c[m].push(S)}for(var x=o.getLineStyle(["color"]),d=0;d<c.length;++d)e.add(nn(c[d],{style:nt({stroke:s[d%s.length]},x),silent:!0}))}},splitArea:function(a,e,t,r){tc(a,t,r,r)}};const v0=l0;var xu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(Kt);qt(xu,Mn.prototype);const Xa=xu;var c0=function(a){V(e,a);function e(t,r,n,i,o){var s=a.call(this,t,r,n)||this;return s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,r){return this.coordinateSystem.pointToData(t)[0]},e}(Jr);const h0=c0;var _u=["single"],f0=function(){function a(e,t,r){this.type="single",this.dimension="single",this.dimensions=_u,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var n=this.dimension,i=new h0(n,ll(e),[0,0],e.get("type"),e.get("position")),o=i.type==="category";i.onBand=o&&e.get("boundaryGap"),i.inverse=e.get("inverse"),i.orient=e.get("orient"),e.axis=i,i.model=e,i.coordinateSystem=this,this._axis=i},a.prototype.update=function(e,t){e.eachSeries(function(r){if(r.coordinateSystem===this){var n=r.getData();L(n.mapDimensionsAll(this.dimension),function(i){this._axis.scale.unionExtentFromData(n,i)},this),ul(this._axis.scale,this._axis.model)}},this)},a.prototype.resize=function(e,t){this._rect=Jt({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},a.prototype.getRect=function(){return this._rect},a.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,r=t.isHorizontal(),n=r?[0,e.width]:[0,e.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),this._updateAxisTransform(t,r?e.x:e.y)},a.prototype._updateAxisTransform=function(e,t){var r=e.getExtent(),n=r[0]+r[1],i=e.isHorizontal();e.toGlobalCoord=i?function(o){return o+t}:function(o){return n-o+t},e.toLocalCoord=i?function(o){return o-t}:function(o){return n-o+t}},a.prototype.getAxis=function(){return this._axis},a.prototype.getBaseAxis=function(){return this._axis},a.prototype.getAxes=function(){return[this._axis]},a.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},a.prototype.containPoint=function(e){var t=this.getRect(),r=this.getAxis(),n=r.orient;return n==="horizontal"?r.contain(r.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:r.contain(r.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},a.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},a.prototype.dataToPoint=function(e){var t=this.getAxis(),r=this.getRect(),n=[],i=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),n[i]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-i]=i===0?r.y+r.height/2:r.x+r.width/2,n},a.prototype.convertToPixel=function(e,t,r){var n=ss(t);return n===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=ss(t);return n===this?this.pointToData(r):null},a}();function ss(a){var e=a.seriesModel,t=a.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function p0(a,e){var t=[];return a.eachComponent("singleAxis",function(r,n){var i=new f0(r,a,e);i.name="single_"+n,i.resize(r,e),r.coordinateSystem=i,t.push(i)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="singleAxis"){var n=r.getReferringComponents("singleAxis",ea).models[0];r.coordinateSystem=n&&n.coordinateSystem}}),t}var g0={create:p0,dimensions:_u};const d0=g0;var ls=["x","y"],y0=["width","height"],m0=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,n,i,o){var s=n.axis,l=s.coordinateSystem,u=Za(l,1-Br(s)),v=l.dataToPoint(r)[0],c=i.get("type");if(c&&c!=="none"){var h=ec(i),f=S0[c](s,v,u);f.style=h,t.graphicKey=f.type,t.pointer=f}var p=In(n);rc(r,t,p,n,i,o)},e.prototype.getHandleTransform=function(t,r,n){var i=In(r,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=ac(r.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,n,i){var o=n.axis,s=o.coordinateSystem,l=Br(o),u=Za(s,l),v=[t.x,t.y];v[l]+=r[l],v[l]=Math.min(u[1],v[l]),v[l]=Math.max(u[0],v[l]);var c=Za(s,1-l),h=(c[1]+c[0])/2,f=[h,h];return f[l]=v[l],{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(nc),S0={line:function(a,e,t){var r=ic([e,t[0]],[e,t[1]],Br(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=a.getBandWidth(),n=t[1]-t[0];return{type:"Rect",shape:oc([e-r/2,t[0]],[r,n],Br(a))}}};function Br(a){return a.isHorizontal()?0:1}function Za(a,e){var t=a.getRect();return[t[ls[e]],t[ls[e]]+t[y0[e]]]}const x0=m0;var _0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(jt);function b0(a){Y(yl),dl.registerAxisPointerClass("SingleAxisPointer",x0),a.registerComponentView(_0),a.registerComponentView(v0),a.registerComponentModel(Xa),fl(a,"single",Xa,Xa.defaultOption),a.registerCoordinateSystem("single",d0)}var w0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,n){var i=sc(t);a.prototype.init.apply(this,arguments),us(t,i)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),us(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(Kt);function us(a,e){var t=a.cellSize,r;F(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var n=O([0,1],function(i){return lc(e,i)&&(r[i]="auto"),r[i]!=null&&r[i]!=="auto"});uc(a,e,{type:"box",ignoreSize:n})}const T0=w0;var I0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,n){var i=this.group;i.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,i),this._renderLines(t,s,l,i),this._renderYearText(t,s,l,i),this._renderMonthText(t,u,l,i),this._renderWeekText(t,u,s,l,i)},e.prototype._renderDayRect=function(t,r,n){for(var i=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=i.getCellWidth(),l=i.getCellHeight(),u=r.start.time;u<=r.end.time;u=i.getNextNDay(u,1).time){var v=i.dataToRect([u],!1).tl,c=new Dt({shape:{x:v[0],y:v[1],width:s,height:l},cursor:"default",style:o});n.add(c)}},e.prototype._renderLines=function(t,r,n,i){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),v=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var c=r.start,h=0;c.time<=r.end.time;h++){p(c.formatedDate),h===0&&(c=s.getDateInfo(r.start.y+"-"+r.start.m));var f=c.date;f.setMonth(f.getMonth()+1),c=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(g){o._firstDayOfMonth.push(s.getDateInfo(g)),o._firstDayPoints.push(s.dataToRect([g],!1).tl);var d=o._getLinePointsOfOneWeek(t,g,n);o._tlpoints.push(d[0]),o._blpoints.push(d[d.length-1]),u&&o._drawSplitline(d,l,i)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,v,n),l,i),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,v,n),l,i)},e.prototype._getEdgesPoints=function(t,r,n){var i=[t[0].slice(),t[t.length-1].slice()],o=n==="horizontal"?0:1;return i[0][o]=i[0][o]-r/2,i[1][o]=i[1][o]+r/2,i},e.prototype._drawSplitline=function(t,r,n){var i=new fe({z2:20,shape:{points:t},style:r});n.add(i)},e.prototype._getLinePointsOfOneWeek=function(t,r,n){for(var i=t.coordinateSystem,o=i.getDateInfo(r),s=[],l=0;l<7;l++){var u=i.getNextNDay(o.time,l),v=i.dataToRect([u.time],!1);s[2*u.day]=v.tl,s[2*u.day+1]=v[n==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return rt(t)&&t?vc(t,r):ot(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,n,i,o){var s=r[0],l=r[1],u=["center","bottom"];i==="bottom"?(l+=o,u=["center","top"]):i==="left"?s-=o:i==="right"?(s+=o,u=["center","top"]):l-=o;var v=0;return(i==="left"||i==="right")&&(v=Math.PI/2),{rotation:v,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,n,i){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=n!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],v=(u[0][0]+u[1][0])/2,c=(u[0][1]+u[1][1])/2,h=n==="horizontal"?0:1,f={top:[v,u[h][1]],bottom:[v,u[1-h][1]],left:[u[1-h][0],c],right:[u[h][0],c]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var g=o.get("formatter"),d={start:r.start.y,end:r.end.y,nameMap:p},y=this._formatterLabel(g,d),S=new Rt({z2:30,style:St(o,{text:y})});S.attr(this._yearTextPositionControl(S,f[l],n,l,s)),i.add(S)}},e.prototype._monthTextPositionControl=function(t,r,n,i,o){var s="left",l="top",u=t[0],v=t[1];return n==="horizontal"?(v=v+o,r&&(s="center"),i==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),i==="start"&&(s="right")),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,n,i){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),v=o.get("align"),c=[this._tlpoints,this._blpoints];(!s||rt(s))&&(s&&(r=Ri(s)||r),s=r.get(["time","monthAbbr"])||[]);var h=u==="start"?0:1,f=n==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=v==="center",g=0;g<c[h].length-1;g++){var d=c[h][g].slice(),y=this._firstDayOfMonth[g];if(p){var S=this._firstDayPoints[g];d[f]=(S[f]+c[0][g+1][f])/2}var m=o.get("formatter"),x=s[+y.m-1],_={yyyy:y.y,yy:(y.y+"").slice(2),MM:y.m,M:+y.m,nameMap:x},b=this._formatterLabel(m,_),w=new Rt({z2:30,style:$(St(o,{text:b}),this._monthTextPositionControl(d,p,n,u,l))});i.add(w)}}},e.prototype._weekTextPositionControl=function(t,r,n,i,o){var s="center",l="middle",u=t[0],v=t[1],c=n==="start";return r==="horizontal"?(u=u+i+(c?1:-1)*o[0]/2,s=c?"right":"left"):(v=v+i+(c?1:-1)*o[1]/2,l=c?"bottom":"top"),{x:u,y:v,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,n,i,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),v=s.get("nameMap"),c=s.get("margin"),h=l.getFirstDayOfWeek();if(!v||rt(v)){v&&(r=Ri(v)||r);var f=r.get(["time","dayOfWeekShort"]);v=f||O(r.get(["time","dayOfWeekAbbr"]),function(_){return _[0]})}var p=l.getNextNDay(n.end.time,7-n.lweek).time,g=[l.getCellWidth(),l.getCellHeight()];c=z(c,Math.min(g[1],g[0])),u==="start"&&(p=l.getNextNDay(n.start.time,-(7+n.fweek)).time,c=-c);for(var d=0;d<7;d++){var y=l.getNextNDay(p,d),S=l.dataToRect([y.time],!1).center,m=d;m=Math.abs((d+h)%7);var x=new Rt({z2:30,style:$(St(s,{text:v[m]}),this._weekTextPositionControl(S,i,u,c,g))});o.add(x)}}},e.type="calendar",e}(jt);const D0=I0;var qa=864e5,A0=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=cc(e);var t=e.getFullYear(),r=e.getMonth()+1,n=r<10?"0"+r:""+r,i=e.getDate(),o=i<10?"0"+i:""+i,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:n,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+n+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,n=["width","height"],i=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];L([0,1],function(c){v(i,c)&&(o[n[c]]=i[c]*s[c])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=Jt(o,l);L([0,1],function(c){v(i,c)||(i[c]=u[n[c]]/s[c])});function v(c,h){return c[h]!=null&&c[h]!=="auto"}this._sw=i[0],this._sh=i[1]},a.prototype.dataToPoint=function(e,t){F(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),n=this._rangeInfo,i=r.formatedDate;if(t&&!(r.time>=n.start.time&&r.time<n.end.time+qa))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([n.start.time,i]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,n=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,n):this._getDateByWeeksAndDay(t,r-1,n)},a.prototype.convertToPixel=function(e,t,r){var n=vs(t);return n===this?n.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var n=vs(t);return n===this?n.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(F(e)&&e.length===1&&(e=e[0]),F(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var n=this.getDateInfo(r),i=n.date;i.setMonth(i.getMonth()+1);var o=this.getNextNDay(i,-1);t=[n.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var n=Math.floor(t[1].time/qa)-Math.floor(t[0].time/qa)+1,i=new Date(t[0].time),o=i.getDate(),s=t[1].date.getDate();i.setDate(o+n-1);var l=i.getDate();if(l!==s)for(var u=i.getTime()-t[1].time>0?1:-1;(l=i.getDate())!==s&&(i.getTime()-t[1].time)*u>0;)n-=u,i.setDate(l-u);var v=Math.floor((n+t[0].day+6)/7),c=r?-v+1:v-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:n,weeks:v,nthWeek:c,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var n=this._getRangeInfo(r);if(e>n.weeks||e===0&&t<n.fweek||e===n.weeks&&t>n.lweek)return null;var i=(e-1)*7-n.fweek+t,o=new Date(n.start.time);return o.setDate(+n.start.d+i),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(n){var i=new a(n);r.push(i),n.coordinateSystem=i}),e.eachSeries(function(n){n.get("coordinateSystem")==="calendar"&&(n.coordinateSystem=r[n.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function vs(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}const L0=A0;function C0(a){a.registerComponentModel(T0),a.registerComponentView(D0),a.registerCoordinateSystem("calendar",L0)}var P0=["rect","polygon","keep","clear"];function M0(a,e){var t=Ot(a?a.brush:[]);if(t.length){var r=[];L(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(r=r.concat(u))});var n=a&&a.toolbox;F(n)&&(n=n[0]),n||(n={feature:{}},a.toolbox=[n]);var i=n.feature||(n.feature={}),o=i.brush||(i.brush={}),s=o.type||(o.type=[]);s.push.apply(s,r),E0(s),e&&!s.length&&s.push.apply(s,P0)}}function E0(a){var e={};L(a,function(t){e[t]=1}),a.length=0,L(e,function(t,r){a.push(r)})}function R0(a){var e=a.brushType,t={point:function(r){return cs[e].point(r,t,a)},rect:function(r){return cs[e].rect(r,t,a)}};return t}var cs={lineX:hs(0),lineY:hs(1),rect:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])},rect:function(a,e,t){return a&&t.boundingRect.intersect(a)}},polygon:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])&&Ge(t.range,a[0],a[1])},rect:function(a,e,t){var r=t.range;if(!a||r.length<=1)return!1;var n=a.x,i=a.y,o=a.width,s=a.height,l=r[0];if(Ge(r,n,i)||Ge(r,n+o,i)||Ge(r,n,i+s)||Ge(r,n+o,i+s)||vt.create(a).contain(l[0],l[1])||yr(n,i,n+o,i,r)||yr(n,i,n,i+s,r)||yr(n+o,i,n+o,i+s,r)||yr(n,i+s,n+o,i+s,r))return!0}}};function hs(a){var e=["x","y"],t=["width","height"];return{point:function(r,n,i){if(r){var o=i.range,s=r[a];return We(s,o)}},rect:function(r,n,i){if(r){var o=i.range,s=[r[e[a]],r[e[a]]+r[t[a]]];return s[1]<s[0]&&s.reverse(),We(s[0],o)||We(s[1],o)||We(o[0],s)||We(o[1],s)}}}}function We(a,e){return e[0]<=a&&a<=e[1]}var fs=["inBrush","outOfBrush"],Ka="__ecBrushSelect",Dn="__ecInBrushSelectEvent";function bu(a){a.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new pc(e.option,a);t.setInputRanges(e.areas,a)})}function N0(a,e,t){var r=[],n,i;a.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),bu(a),a.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:Ft(o.areas),selected:[]};r.push(l);var u=o.option,v=u.brushLink,c=[],h=[],f=[],p=!1;s||(n=u.throttleType,i=u.throttleDelay);var g=O(o.areas,function(_){var b=z0[_.brushType],w=nt({boundingRect:b?b(_):void 0},_);return w.selectors=R0(w),w}),d=hc(o.option,fs,function(_){_.mappingMethod="fixed"});F(v)&&L(v,function(_){c[_]=1});function y(_){return v==="all"||!!c[_]}function S(_){return!!_.length}a.eachSeries(function(_,b){var w=f[b]=[];_.subType==="parallel"?m(_,b):x(_,b,w)});function m(_,b){var w=_.coordinateSystem;p=p||w.hasAxisBrushed(),y(b)&&w.eachActiveState(_.getData(),function(T,I){T==="active"&&(h[I]=1)})}function x(_,b,w){if(!(!_.brushSelector||G0(o,b))&&(L(g,function(I){o.brushTargetManager.controlSeries(I,_,a)&&w.push(I),p=p||S(w)}),y(b)&&S(w))){var T=_.getData();T.each(function(I){ps(_,w,T,I)&&(h[I]=1)})}}a.eachSeries(function(_,b){var w={seriesId:_.id,seriesIndex:b,seriesName:_.name,dataIndex:[]};l.selected.push(w);var T=f[b],I=_.getData(),D=y(b)?function(A){return h[A]?(w.dataIndex.push(I.getRawIndex(A)),"inBrush"):"outOfBrush"}:function(A){return ps(_,T,I,A)?(w.dataIndex.push(I.getRawIndex(A)),"inBrush"):"outOfBrush"};(y(b)?p:S(T))&&fc(fs,d,I,D)})}),V0(e,n,i,r,t)}function V0(a,e,t,r,n){if(n){var i=a.getZr();if(!i[Dn]){i[Ka]||(i[Ka]=k0);var o=sl(i,Ka,t,e);o(a,r)}}}function k0(a,e){if(!a.isDisposed()){var t=a.getZr();t[Dn]=!0,a.dispatchAction({type:"brushSelect",batch:e}),t[Dn]=!1}}function ps(a,e,t,r){for(var n=0,i=e.length;n<i;n++){var o=e[n];if(a.brushSelector(r,t,o.selectors,o))return!0}}function G0(a,e){var t=a.option.seriesIndex;return t!=null&&t!=="all"&&(F(t)?Bt(t,e)<0:e!==t)}var z0={rect:function(a){return gs(a.range)},polygon:function(a){for(var e,t=a.range,r=0,n=t.length;r<n;r++){e=e||[[1/0,-1/0],[1/0,-1/0]];var i=t[r];i[0]<e[0][0]&&(e[0][0]=i[0]),i[0]>e[0][1]&&(e[0][1]=i[0]),i[1]<e[1][0]&&(e[1][0]=i[1]),i[1]>e[1][1]&&(e[1][1]=i[1])}return e&&gs(e)}};function gs(a){return new vt(a[0][0],a[1][0],a[0][1]-a[0][0],a[1][1]-a[1][0])}var O0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r,this.model,(this._brushController=new cl(r.getZr())).on("brush",tt(this._onBrush,this)).mount()},e.prototype.render=function(t,r,n,i){this.model=t,this._updateController(t,r,n,i)},e.prototype.updateTransform=function(t,r,n,i){bu(r),this._updateController(t,r,n,i)},e.prototype.updateVisual=function(t,r,n,i){this.updateTransform(t,r,n,i)},e.prototype.updateView=function(t,r,n,i){this._updateController(t,r,n,i)},e.prototype._updateController=function(t,r,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var r=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:r,areas:Ft(n),$from:r}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:r,areas:Ft(n),$from:r})},e.type="brush",e}(jt);const B0=O0;var F0="#ddd",W0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,r){var n=this.option;!r&&gc(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:F0},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=O(t,function(r){return ds(this.option,r)},this))},e.prototype.setBrushOption=function(t){this.brushOption=ds(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(Kt);function ds(a,e){return Xt({brushType:a.brushType,brushMode:a.brushMode,transformable:a.transformable,brushStyle:new Wt(a.brushStyle).getItemStyle(),removeOnClick:a.removeOnClick,z:a.z},e,!0)}const $0=W0;var H0=["rect","polygon","lineX","lineY","keep","clear"],U0=function(a){V(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,n){var i,o,s;r.eachComponent({mainType:"brush"},function(l){i=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=i,this._brushMode=o,L(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===i)?"emphasis":"normal")})},e.prototype.updateView=function(t,r,n){this.render(t,r,n)},e.prototype.getIcons=function(){var t=this.model,r=t.get("icon",!0),n={};return L(t.get("type",!0),function(i){r[i]&&(n[i]=r[i])}),n},e.prototype.onclick=function(t,r,n){var i=this._brushType,o=this._brushMode;n==="clear"?(r.dispatchAction({type:"axisAreaSelect",intervals:[]}),r.dispatchAction({type:"brush",command:"clear",areas:[]})):r.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:n==="keep"?i:i===n?!1:n,brushMode:n==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var r={show:!0,type:H0.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return r},e}(dc);const Y0=U0;function X0(a){a.registerComponentView(B0),a.registerComponentModel($0),a.registerPreprocessor(M0),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,N0),a.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(r){r.setAreas(e.areas)})}),a.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},je),a.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},je),yc("brush",Y0)}var ys=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],n=t.axisType,i=this._names=[],o;n==="category"?(o=[],L(r,function(u,v){var c=vr(mc(u),""),h;pr(u)?(h=Ft(u),h.value=v):h=v,o.push(h),i.push(c)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[n]||"number",l=this._data=new Lt([{name:"value",type:s}],this);l.initData(o,i)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(Kt),wu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=gl(ys.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(ys);qt(wu,ml.prototype);const Z0=wu;var q0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(jt);const K0=q0;var j0=function(a){V(e,a);function e(t,r,n,i){var o=a.call(this,t,r,n)||this;return o.type=i||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(Jr);const J0=j0;var ja=Math.PI,ms=xt(),Q0=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,n){if(this.model=t,this.api=n,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(i,t);t.formatTooltip=function(u){var v=l.scale.getLabel({value:u});return It("nameValue",{noName:!0,value:v})},L(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](i,o,l,t)},this),this._renderAxisLabel(i,s,l,t),this._position(i,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var n=t.get(["label","position"]),i=t.get("orient"),o=eS(t,r),s;n==null||n==="auto"?s=i==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":rt(n)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[i][n]:s=n;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},v={horizontal:0,vertical:ja/2},c=i==="vertical"?o.height:o.width,h=t.getModel("controlStyle"),f=h.get("show",!0),p=f?h.get("itemSize"):0,g=f?h.get("itemGap"):0,d=p+g,y=t.get(["label","rotate"])||0;y=y*ja/180;var S,m,x,_=h.get("position",!0),b=f&&h.get("showPlayBtn",!0),w=f&&h.get("showPrevBtn",!0),T=f&&h.get("showNextBtn",!0),I=0,D=c;_==="left"||_==="bottom"?(b&&(S=[0,0],I+=d),w&&(m=[I,0],I+=d),T&&(x=[D-p,0],D-=d)):(b&&(S=[D-p,0],D-=d),w&&(m=[0,0],I+=d),T&&(x=[D-p,0],D-=d));var A=[I,D];return t.get("inverse")&&A.reverse(),{viewRect:o,mainLength:c,orient:i,rotation:v[i],labelRotation:y,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[i],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[i],playPosition:S,prevBtnPosition:m,nextBtnPosition:x,axisExtent:A,controlSize:p,controlGap:g}},e.prototype._position=function(t,r){var n=this._mainGroup,i=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=lr(),l=o.x,u=o.y+o.height;Ee(s,s,[-l,-u]),Rn(s,s,-ja/2),Ee(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var v=S(o),c=S(n.getBoundingRect()),h=S(i.getBoundingRect()),f=[n.x,n.y],p=[i.x,i.y];p[0]=f[0]=v[0][0];var g=t.labelPosOpt;if(g==null||rt(g)){var d=g==="+"?0:1;m(f,c,v,1,d),m(p,h,v,1,1-d)}else{var d=g>=0?0:1;m(f,c,v,1,d),p[1]=f[1]+g}n.setPosition(f),i.setPosition(p),n.rotation=i.rotation=t.rotation,y(n),y(i);function y(x){x.originX=v[0][0]-x.x,x.originY=v[1][0]-x.y}function S(x){return[[x.x,x.x+x.width],[x.y,x.y+x.height]]}function m(x,_,b,w,T){x[w]+=b[w][T]-_[w][T]}},e.prototype._createAxis=function(t,r){var n=r.getData(),i=r.get("axisType"),o=tS(r,i);o.getTicks=function(){return n.mapArray(["value"],function(u){return{value:u}})};var s=n.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new J0("value",o,t.axisExtent,i);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new U;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,n,i){var o=n.getExtent();if(i.get(["lineStyle","show"])){var s=new xe({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:$({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new xe({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:nt({lineCap:"round",lineWidth:s.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,n,i){var o=this,s=i.getData(),l=n.scale.getTicks();this._tickSymbols=[],L(l,function(u){var v=n.dataToCoord(u.value),c=s.getItemModel(u.value),h=c.getModel("itemStyle"),f=c.getModel(["emphasis","itemStyle"]),p=c.getModel(["progress","itemStyle"]),g={x:v,y:0,onclick:tt(o._changeTimeline,o,u.value)},d=Ss(c,h,r,g);d.ensureState("emphasis").style=f.getItemStyle(),d.ensureState("progress").style=p.getItemStyle(),ha(d);var y=at(d);c.get("tooltip")?(y.dataIndex=u.value,y.dataModel=i):y.dataIndex=y.dataModel=null,o._tickSymbols.push(d)})},e.prototype._renderAxisLabel=function(t,r,n,i){var o=this,s=n.getLabelModel();if(s.get("show")){var l=i.getData(),u=n.getViewLabels();this._tickLabels=[],L(u,function(v){var c=v.tickValue,h=l.getItemModel(c),f=h.getModel("label"),p=h.getModel(["emphasis","label"]),g=h.getModel(["progress","label"]),d=n.dataToCoord(v.tickValue),y=new Rt({x:d,y:0,rotation:t.labelRotation-t.rotation,onclick:tt(o._changeTimeline,o,c),silent:!1,style:St(f,{text:v.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});y.ensureState("emphasis").style=St(p),y.ensureState("progress").style=St(g),r.add(y),ha(y),ms(y).dataIndex=c,o._tickLabels.push(y)})}},e.prototype._renderControl=function(t,r,n,i){var o=t.controlSize,s=t.rotation,l=i.getModel("controlStyle").getItemStyle(),u=i.getModel(["emphasis","controlStyle"]).getItemStyle(),v=i.getPlayState(),c=i.get("inverse",!0);h(t.nextBtnPosition,"next",tt(this._changeTimeline,this,c?"-":"+")),h(t.prevBtnPosition,"prev",tt(this._changeTimeline,this,c?"+":"-")),h(t.playPosition,v?"stop":"play",tt(this._handlePlayClick,this,!v),!0);function h(f,p,g,d){if(f){var y=Sc(Ut(i.get(["controlStyle",p+"BtnSize"]),o),o),S=[0,-y/2,y,y],m=rS(i,p+"Icon",S,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:d?-s:0,rectHover:!0,style:l,onclick:g});m.ensureState("emphasis").style=u,r.add(m),ha(m)}}},e.prototype._renderCurrentPointer=function(t,r,n,i){var o=i.getData(),s=i.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,v={onCreate:function(c){c.draggable=!0,c.drift=tt(u._handlePointerDrag,u),c.ondragend=tt(u._handlePointerDragend,u),xs(c,u._progressLine,s,n,i,!0)},onUpdate:function(c){xs(c,u._progressLine,s,n,i)}};this._currentPointer=Ss(l,l,this._mainGroup,{},this._currentPointer,v)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var n=this._toAxisCoord(t)[0],i=this._axis,o=Qe(i.getExtent().slice());n>o[1]&&(n=o[1]),n<o[0]&&(n=o[0]),this._currentPointer.x=n,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=n,s.dirty());var l=this._findNearestTick(n),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return vl(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),n=1/0,i,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),v=Math.abs(u-t);v<n&&(n=v,i=l)}),i},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,n=this._tickLabels;if(r)for(var i=0;i<r.length;i++)r&&r[i]&&r[i].toggleState("progress",i<t);if(n)for(var i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",ms(n[i]).dataIndex<=t)},e.type="timeline.slider",e}(K0);function tS(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new _c({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new xc({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new on}}function eS(a,e){return Jt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function rS(a,e,t,r){var n=r.style,i=bc(a.get(["controlStyle",e]),r||{},new vt(t[0],t[1],t[2],t[3]));return n&&i.setStyle(n),i}function Ss(a,e,t,r,n,i){var o=e.get("color");if(n)n.setColor(o),t.add(n),i&&i.onUpdate(n);else{var s=a.get("symbol");n=Yt(s,-1,-1,2,2,o),n.setStyle("strokeNoScale",!0),t.add(n),i&&i.onCreate(n)}var l=e.getItemStyle(["color"]);n.setStyle(l),r=Xt({rectHover:!0,z2:100},r,!0);var u=or(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var v=fr(a.get("symbolOffset"),u);v&&(r.x=(r.x||0)+v[0],r.y=(r.y||0)+v[1]);var c=a.get("symbolRotate");return r.rotation=(c||0)*Math.PI/180||0,n.attr(r),n.updateTransform(),n}function xs(a,e,t,r,n,i){if(!a.dragging){var o=n.getModel("checkpointStyle"),s=r.dataToCoord(n.getData().get("value",t));if(i||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}const aS=Q0;function nS(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var n=t.getComponent("timeline");return n&&e.currentIndex!=null&&(n.setCurrentIndex(e.currentIndex),!n.get("loop",!0)&&n.isIndexMax()&&n.getPlayState()&&(n.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:n.get("replaceMerge",!0)}),nt({currentIndex:n.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function iS(a){var e=a&&a.timeline;F(e)||(e=e?[e]:[]),L(e,function(t){t&&oS(t)})}function oS(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),_s(a),Se(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});Se(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!Se(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}L(a.data||[],function(n){pr(n)&&!F(n)&&(!Se(n,"value")&&Se(n,"name")&&(n.value=n.name),_s(n))})}function _s(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},n=r.normal||(r.normal={}),i={normal:1,emphasis:1};L(r,function(o,s){!i[s]&&!Se(n,s)&&(n[s]=o)}),t.label&&!Se(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function Se(a,e){return a.hasOwnProperty(e)}function sS(a){a.registerComponentModel(Z0),a.registerComponentView(aS),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),nS(a),a.registerPreprocessor(iS)}function hi(a,e){if(!a)return!1;for(var t=F(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function Dr(a){ra(a,"label",["show"])}var Ar=xt(),Tu=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},e.prototype.isAnimationEnabled=function(){if(wc.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,n,i){var o=this.mainType;n||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=Ar(s)[o];if(!l||!l.data){Ar(s)[o]=null;return}u?u._mergeOption(l,r,!0):(i&&Dr(l),L(l.data,function(v){v instanceof Array?(Dr(v[0]),Dr(v[1])):Dr(v)}),u=this.createMarkerModelFromSeries(l,this,r),$(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),Ar(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return It("section",{header:this.name,blocks:[It("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.getMarkerModelFromSeries=function(t,r){return Ar(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(Kt);qt(Tu,ml.prototype);const he=Tu;var lS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(he);const uS=lS;function An(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function vS(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function Lr(a,e,t,r,n,i){var o=[],s=Tc(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=fi(e,l,a),v=e.indicesOfNearest(l,u)[0];o[n]=e.get(t,v),o[i]=e.get(l,v);var c=e.get(r,v),h=Ic(e.get(r,v));return h=Math.min(h,20),h>=0&&(o[i]=+o[i].toFixed(h)),[o,c]}var Ja={min:st(Lr,"min"),max:st(Lr,"max"),average:st(Lr,"average"),median:st(Lr,"median")};function ar(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,n=r&&r.dimensions;if(!vS(e)&&!F(e.coord)&&F(n)){var i=Iu(e,t,r,a);if(e=Ft(e),e.type&&Ja[e.type]&&i.baseAxis&&i.valueAxis){var o=Bt(n,i.baseAxis.dim),s=Bt(n,i.valueAxis.dim),l=Ja[e.type](t,i.baseDataDim,i.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!F(n))e.coord=[];else for(var u=e.coord,v=0;v<2;v++)Ja[u[v]]&&(u[v]=fi(t,t.mapDimension(n[v]),u[v]));return e}}function Iu(a,e,t,r){var n={};return a.valueIndex!=null||a.valueDim!=null?(n.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,n.valueAxis=t.getAxis(cS(r,n.valueDataDim)),n.baseAxis=t.getOtherAxis(n.valueAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim)):(n.baseAxis=r.getBaseAxis(),n.valueAxis=t.getOtherAxis(n.baseAxis),n.baseDataDim=e.mapDimension(n.baseAxis.dim),n.valueDataDim=e.mapDimension(n.valueAxis.dim)),n}function cS(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function nr(a,e){return a&&a.containData&&e.coord&&!An(e)?a.containData(e.coord):!0}function hS(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!An(e)&&!An(t)?a.containZone(e.coord,t.coord):!0}function Du(a,e){return a?function(t,r,n,i){var o=i<2?t.coord&&t.coord[i]:t.value;return Er(o,e[i])}:function(t,r,n,i){return Er(t.value,e[i])}}function fi(a,e,t){if(t==="average"){var r=0,n=0;return a.each(e,function(i,o){isNaN(i)||(r+=i,n++)}),r/n}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var Qa=xt(),fS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=q()},e.prototype.render=function(t,r,n){var i=this,o=this.markerGroupMap;o.each(function(s){Qa(s).keep=!1}),r.eachSeries(function(s){var l=he.getMarkerModelFromSeries(s,i.type);l&&i.renderSeries(s,l,r,n)}),o.each(function(s){!Qa(s).keep&&i.group.remove(s.group)})},e.prototype.markKeep=function(t){Qa(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var n=this;L(t,function(i){var o=he.getMarkerModelFromSeries(i,n.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?Dc(l):Ac(l))})}})},e.type="marker",e}(jt);const pi=fS;function bs(a,e,t){var r=e.coordinateSystem;a.each(function(n){var i=a.getItemModel(n),o,s=z(i.get("x"),t.getWidth()),l=z(i.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(a.getValues(a.dimensions,n));else if(r){var u=a.get(r.dimensions[0],n),v=a.get(r.dimensions[1],n);o=r.dataToPoint([u,v])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),a.setItemLayout(n,o)})}var pS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=he.getMarkerModelFromSeries(i,"markPoint");o&&(bs(o.getData(),i,n),this.markerGroupMap.get(i.id).updateLayout())},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new Zr),c=gS(o,t,r);r.setData(c),bs(r.getData(),t,i),c.each(function(h){var f=c.getItemModel(h),p=f.getShallow("symbol"),g=f.getShallow("symbolSize"),d=f.getShallow("symbolRotate"),y=f.getShallow("symbolOffset"),S=f.getShallow("symbolKeepAspect");if(ot(p)||ot(g)||ot(d)||ot(y)){var m=r.getRawValue(h),x=r.getDataParams(h);ot(p)&&(p=p(m,x)),ot(g)&&(g=g(m,x)),ot(d)&&(d=d(m,x)),ot(y)&&(y=y(m,x))}var _=f.getModel("itemStyle").getItemStyle(),b=$n(l,"color");_.fill||(_.fill=b),c.setItemVisual(h,{symbol:p,symbolSize:g,symbolRotate:d,symbolOffset:y,symbolKeepAspect:S,style:_})}),v.updateData(c),this.group.add(v.group),c.eachItemGraphicEl(function(h){h.traverse(function(f){at(f).dataModel=r})}),this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markPoint",e}(pi);function gS(a,e,t){var r;a?r=O(a&&a.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return $($({},l),{name:s,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Lt(r,t),i=O(t.get("data"),st(ar,e));a&&(i=Nt(i,st(nr,a)));var o=Du(!!a,r);return n.initData(i,null,o),n}const dS=pS;function yS(a){a.registerComponentModel(uS),a.registerComponentView(dS),a.registerPreprocessor(function(e){hi(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var mS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(he);const SS=mS;var Cr=xt(),xS=function(a,e,t,r){var n=a.getData(),i;if(F(r))i=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=Et(r.yAxis,r.xAxis);else{var u=Iu(r,n,e,a);s=u.valueAxis;var v=Lc(n,u.valueDataDim);l=fi(n,v,o)}var c=s.dim==="x"?0:1,h=1-c,f=Ft(r),p={coord:[]};f.type=null,f.coord=[],f.coord[h]=-1/0,p.coord[h]=1/0;var g=t.get("precision");g>=0&&Zt(l)&&(l=+l.toFixed(Math.min(g,20))),f.coord[c]=p.coord[c]=l,i=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else i=[]}var d=[ar(a,i[0]),ar(a,i[1]),$({},i[2])];return d[2].type=d[2].type||null,Xt(d[2],d[0]),Xt(d[2],d[1]),d};function Fr(a){return!isNaN(a)&&!isFinite(a)}function ws(a,e,t,r){var n=1-a,i=r.dimensions[a];return Fr(e[n])&&Fr(t[n])&&e[a]===t[a]&&r.getAxis(i).containData(e[a])}function _S(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(ws(1,t,r,a)||ws(0,t,r,a)))return!0}return nr(a,e[0])&&nr(a,e[1])}function tn(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=z(o.get("x"),n.getWidth()),u=z(o.get("y"),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var v=i.dimensions,c=a.get(v[0],e),h=a.get(v[1],e);s=i.dataToPoint([c,h])}if(oa(i,"cartesian2d")){var f=i.getAxis("x"),p=i.getAxis("y"),v=i.dimensions;Fr(a.get(v[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):Fr(a.get(v[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var bS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=he.getMarkerModelFromSeries(i,"markLine");if(o){var s=o.getData(),l=Cr(o).from,u=Cr(o).to;l.each(function(v){tn(l,v,!0,i,n),tn(u,v,!1,i,n)}),s.each(function(v){s.setItemLayout(v,[l.getItemLayout(v),u.getItemLayout(v)])}),this.markerGroupMap.get(i.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new ni);this.group.add(v.group);var c=wS(o,t,r),h=c.from,f=c.to,p=c.line;Cr(r).from=h,Cr(r).to=f,r.setData(p);var g=r.get("symbol"),d=r.get("symbolSize"),y=r.get("symbolRotate"),S=r.get("symbolOffset");F(g)||(g=[g,g]),F(d)||(d=[d,d]),F(y)||(y=[y,y]),F(S)||(S=[S,S]),c.from.each(function(x){m(h,x,!0),m(f,x,!1)}),p.each(function(x){var _=p.getItemModel(x).getModel("lineStyle").getLineStyle();p.setItemLayout(x,[h.getItemLayout(x),f.getItemLayout(x)]),_.stroke==null&&(_.stroke=h.getItemVisual(x,"style").fill),p.setItemVisual(x,{fromSymbolKeepAspect:h.getItemVisual(x,"symbolKeepAspect"),fromSymbolOffset:h.getItemVisual(x,"symbolOffset"),fromSymbolRotate:h.getItemVisual(x,"symbolRotate"),fromSymbolSize:h.getItemVisual(x,"symbolSize"),fromSymbol:h.getItemVisual(x,"symbol"),toSymbolKeepAspect:f.getItemVisual(x,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(x,"symbolOffset"),toSymbolRotate:f.getItemVisual(x,"symbolRotate"),toSymbolSize:f.getItemVisual(x,"symbolSize"),toSymbol:f.getItemVisual(x,"symbol"),style:_})}),v.updateData(p),c.line.eachItemGraphicEl(function(x){at(x).dataModel=r,x.traverse(function(_){at(_).dataModel=r})});function m(x,_,b){var w=x.getItemModel(_);tn(x,_,b,t,i);var T=w.getModel("itemStyle").getItemStyle();T.fill==null&&(T.fill=$n(l,"color")),x.setItemVisual(_,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:Ut(w.get("symbolOffset",!0),S[b?0:1]),symbolRotate:Ut(w.get("symbolRotate",!0),y[b?0:1]),symbolSize:Ut(w.get("symbolSize"),d[b?0:1]),symbol:Ut(w.get("symbol",!0),g[b?0:1]),style:T})}this.markKeep(v),v.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(pi);function wS(a,e,t){var r;a?r=O(a&&a.dimensions,function(u){var v=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return $($({},v),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var n=new Lt(r,t),i=new Lt(r,t),o=new Lt([],t),s=O(t.get("data"),st(xS,e,a,t));a&&(s=Nt(s,st(_S,a)));var l=Du(!!a,r);return n.initData(O(s,function(u){return u[0]}),null,l),i.initData(O(s,function(u){return u[1]}),null,l),o.initData(O(s,function(u){return u[2]})),o.hasItemOption=!0,{from:n,to:i,line:o}}const TS=bS;function IS(a){a.registerComponentModel(SS),a.registerComponentView(TS),a.registerPreprocessor(function(e){hi(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var DS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,n){return new e(t,r,n)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(he);const AS=DS;var Pr=xt(),LS=function(a,e,t,r){var n=r[0],i=r[1];if(!(!n||!i)){var o=ar(a,n),s=ar(a,i),l=o.coord,u=s.coord;l[0]=Et(l[0],-1/0),l[1]=Et(l[1],-1/0),u[0]=Et(u[0],1/0),u[1]=Et(u[1],1/0);var v=kn([{},o,s]);return v.coord=[o.coord,s.coord],v.x0=o.x,v.y0=o.y,v.x1=s.x,v.y1=s.y,v}};function Wr(a){return!isNaN(a)&&!isFinite(a)}function Ts(a,e,t,r){var n=1-a;return Wr(e[n])&&Wr(t[n])}function CS(a,e){var t=e.coord[0],r=e.coord[1],n={coord:t,x:e.x0,y:e.y0},i={coord:r,x:e.x1,y:e.y1};return oa(a,"cartesian2d")?t&&r&&(Ts(1,t,r)||Ts(0,t,r))?!0:hS(a,n,i):nr(a,n)||nr(a,i)}function Is(a,e,t,r,n){var i=r.coordinateSystem,o=a.getItemModel(e),s,l=z(o.get(t[0]),n.getWidth()),u=z(o.get(t[1]),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var v=a.getValues(["x0","y0"],e),c=a.getValues(["x1","y1"],e),h=i.clampData(v),f=i.clampData(c),p=[];t[0]==="x0"?p[0]=h[0]>f[0]?c[0]:v[0]:p[0]=h[0]>f[0]?v[0]:c[0],t[1]==="y0"?p[1]=h[1]>f[1]?c[1]:v[1]:p[1]=h[1]>f[1]?v[1]:c[1],s=r.getMarkerPosition(p,t,!0)}else{var g=a.get(t[0],e),d=a.get(t[1],e),y=[g,d];i.clampData&&i.clampData(y,y),s=i.dataToPoint(y,!0)}if(oa(i,"cartesian2d")){var S=i.getAxis("x"),m=i.getAxis("y"),g=a.get(t[0],e),d=a.get(t[1],e);Wr(g)?s[0]=S.toGlobalCoord(S.getExtent()[t[0]==="x0"?0:1]):Wr(d)&&(s[1]=m.toGlobalCoord(m.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var Ds=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],PS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,n){r.eachSeries(function(i){var o=he.getMarkerModelFromSeries(i,"markArea");if(o){var s=o.getData();s.each(function(l){var u=O(Ds,function(c){return Is(s,l,c,i,n)});s.setItemLayout(l,u);var v=s.getItemGraphicEl(l);v.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,{group:new U});this.group.add(v.group),this.markKeep(v);var c=MS(o,t,r);r.setData(c),c.each(function(h){var f=O(Ds,function(T){return Is(c,h,T,t,i)}),p=o.getAxis("x").scale,g=o.getAxis("y").scale,d=p.getExtent(),y=g.getExtent(),S=[p.parse(c.get("x0",h)),p.parse(c.get("x1",h))],m=[g.parse(c.get("y0",h)),g.parse(c.get("y1",h))];Qe(S),Qe(m);var x=!(d[0]>S[1]||d[1]<S[0]||y[0]>m[1]||y[1]<m[0]),_=!x;c.setItemLayout(h,{points:f,allClipped:_});var b=c.getItemModel(h).getModel("itemStyle").getItemStyle(),w=$n(l,"color");b.fill||(b.fill=w,rt(b.fill)&&(b.fill=vn(b.fill,.4))),b.stroke||(b.stroke=w),c.setItemVisual(h,"style",b)}),c.diff(Pr(v).data).add(function(h){var f=c.getItemLayout(h);if(!f.allClipped){var p=new re({shape:{points:f.points}});c.setItemGraphicEl(h,p),v.group.add(p)}}).update(function(h,f){var p=Pr(v).data.getItemGraphicEl(f),g=c.getItemLayout(h);g.allClipped?p&&v.group.remove(p):(p?lt(p,{shape:{points:g.points}},r,h):p=new re({shape:{points:g.points}}),c.setItemGraphicEl(h,p),v.group.add(p))}).remove(function(h){var f=Pr(v).data.getItemGraphicEl(h);v.group.remove(f)}).execute(),c.eachItemGraphicEl(function(h,f){var p=c.getItemModel(f),g=c.getItemVisual(f,"style");h.useStyle(c.getItemVisual(f,"style")),kt(h,Tt(p),{labelFetcher:r,labelDataIndex:f,defaultText:c.getName(f)||"",inheritColor:rt(g.fill)?vn(g.fill,1):"#000"}),Ct(h,p),ct(h,null,null,p.get(["emphasis","disabled"])),at(h).dataModel=r}),Pr(v).data=c,v.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(pi);function MS(a,e,t){var r,n,i=["x0","y0","x1","y1"];if(a){var o=O(a&&a.dimensions,function(u){var v=e.getData(),c=v.getDimensionInfo(v.mapDimension(u))||{};return $($({},c),{name:u,ordinalMeta:null})});n=O(i,function(u,v){return{name:u,type:o[v%2].type}}),r=new Lt(n,t)}else n=[{name:"value",type:"float"}],r=new Lt(n,t);var s=O(t.get("data"),st(LS,e,a,t));a&&(s=Nt(s,st(CS,a)));var l=a?function(u,v,c,h){var f=u.coord[Math.floor(h/2)][h%2];return Er(f,n[h])}:function(u,v,c,h){return Er(u.value,n[h])};return r.initData(s,null,l),r.hasItemOption=!0,r}const ES=PS;function RS(a){a.registerComponentModel(AS),a.registerComponentView(ES),a.registerPreprocessor(function(e){hi(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var NS={label:{enabled:!0},decal:{show:!1}},As=xt(),VS={};function kS(a,e){var t=a.getModel("aria");if(!t.get("enabled"))return;var r=Ft(NS);Xt(r.label,a.getLocaleModel().get("aria"),!1),Xt(t.option,r,!1),n(),i();function n(){var u=t.getModel("decal"),v=u.get("show");if(v){var c=q();a.eachSeries(function(h){if(!h.isColorBySeries()){var f=c.get(h.type);f||(f={},c.set(h.type,f)),As(h).scope=f}}),a.eachRawSeries(function(h){if(a.isSeriesFiltered(h))return;if(ot(h.enableAriaDecal)){h.enableAriaDecal();return}var f=h.getData();if(h.isColorBySeries()){var S=un(h.ecModel,h.name,VS,a.getSeriesCount()),m=f.getVisual("decal");f.setVisual("decal",x(m,S))}else{var p=h.getRawData(),g={},d=As(h).scope;f.each(function(_){var b=f.getRawIndex(_);g[b]=_});var y=p.count();p.each(function(_){var b=g[_],w=p.getName(_)||_+"",T=un(h.ecModel,w,d,y),I=f.getItemVisual(b,"decal");f.setItemVisual(b,"decal",x(I,T))})}function x(_,b){var w=_?$($({},b),_):b;return w.dirty=!0,w}})}}function i(){var u=a.getLocaleModel().get("aria"),v=t.getModel("label");if(v.option=nt(v.option,u),!!v.get("enabled")){var c=e.getZr().dom;if(v.get("description")){c.setAttribute("aria-label",v.get("description"));return}var h=a.getSeriesCount(),f=v.get(["data","maxCount"])||10,p=v.get(["series","maxCount"])||10,g=Math.min(h,p),d;if(!(h<1)){var y=s();if(y){var S=v.get(["general","withTitle"]);d=o(S,{title:y})}else d=v.get(["general","withoutTitle"]);var m=[],x=h>1?v.get(["series","multiple","prefix"]):v.get(["series","single","prefix"]);d+=o(x,{seriesCount:h}),a.eachSeries(function(T,I){if(I<g){var D=void 0,A=T.get("name"),M=A?"withName":"withoutName";D=h>1?v.get(["series","multiple",M]):v.get(["series","single",M]),D=o(D,{seriesId:T.seriesIndex,seriesName:T.get("name"),seriesType:l(T.subType)});var E=T.getData();if(E.count()>f){var C=v.get(["data","partialData"]);D+=o(C,{displayCnt:f})}else D+=v.get(["data","allData"]);for(var P=v.get(["data","separator","middle"]),R=v.get(["data","separator","end"]),N=[],k=0;k<E.count();k++)if(k<f){var G=E.getName(k),W=E.getValues(k),B=v.get(["data",G?"withName":"withoutName"]);N.push(o(B,{name:G,value:W.join(P)}))}D+=N.join(P)+R,m.push(D)}});var _=v.getModel(["series","multiple","separator"]),b=_.get("middle"),w=_.get("end");d+=m.join(b)+w,c.setAttribute("aria-label",d)}}}function o(u,v){if(!rt(u))return u;var c=u;return L(v,function(h,f){c=c.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),h)}),c}function s(){var u=a.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var v=a.getLocaleModel().get(["series","typeNames"]);return v[u]||v.chart}}function GS(a){if(!(!a||!a.aria)){var e=a.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},L(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function zS(a){a.registerPreprocessor(GS),a.registerVisual(a.PRIORITY.VISUAL.ARIA,kS)}var Ls={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},OS=function(){function a(e){var t=this._condVal=rt(e)?new RegExp(e):Mc(e)?e:null;if(t==null){var r="";ut(r)}}return a.prototype.evaluate=function(e){var t=typeof e;return rt(t)?this._condVal.test(e):Zt(t)?this._condVal.test(e+""):!1},a}(),BS=function(){function a(){}return a.prototype.evaluate=function(){return this.value},a}(),FS=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(!e[t].evaluate())return!1;return!0},a}(),WS=function(){function a(){}return a.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(e[t].evaluate())return!0;return!1},a}(),$S=function(){function a(){}return a.prototype.evaluate=function(){return!this.child.evaluate()},a}(),HS=function(){function a(){}return a.prototype.evaluate=function(){for(var e=!!this.valueParser,t=this.getValue,r=t(this.valueGetterParam),n=e?this.valueParser(r):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(e?n:r))return!1;return!0},a}();function gi(a,e){if(a===!0||a===!1){var t=new BS;return t.value=a,t}var r="";return Au(a)||ut(r),a.and?Cs("and",a,e):a.or?Cs("or",a,e):a.not?US(a,e):YS(a,e)}function Cs(a,e,t){var r=e[a],n="";F(r)||ut(n),r.length||ut(n);var i=a==="and"?new FS:new WS;return i.children=O(r,function(o){return gi(o,t)}),i.children.length||ut(n),i}function US(a,e){var t=a.not,r="";Au(t)||ut(r);var n=new $S;return n.child=gi(t,e),n.child||ut(r),n}function YS(a,e){for(var t="",r=e.prepareGetValue(a),n=[],i=ir(a),o=a.parser,s=o?Sl(o):null,l=0;l<i.length;l++){var u=i[l];if(!(u==="parser"||e.valueGetterAttrMap.get(u))){var v=gt(Ls,u)?Ls[u]:u,c=a[u],h=s?s(c):c,f=Cc(v,h)||v==="reg"&&new OS(h);f||ut(t),n.push(f)}}n.length||ut(t);var p=new HS;return p.valueGetterParam=r,p.valueParser=s,p.getValue=e.getValue,p.subCondList=n,p}function Au(a){return pr(a)&&!Pc(a)}var XS=function(){function a(e,t){this._cond=gi(e,t)}return a.prototype.evaluate=function(){return this._cond.evaluate()},a}();function ZS(a,e){return new XS(a,e)}var qS={type:"echarts:filter",transform:function(a){for(var e=a.upstream,t,r=ZS(a.config,{valueGetterAttrMap:q({dimension:!0}),prepareGetValue:function(s){var l="",u=s.dimension;gt(s,"dimension")||ut(l);var v=e.getDimensionInfo(u);return v||ut(l),{dimIdx:v.index}},getValue:function(s){return e.retrieveValueFromItem(t,s.dimIdx)}}),n=[],i=0,o=e.count();i<o;i++)t=e.getRawDataItem(i),r.evaluate()&&n.push(t);return{data:n}}},KS={type:"echarts:sort",transform:function(a){var e=a.upstream,t=a.config,r="",n=Ot(t);n.length||ut(r);var i=[];L(n,function(v){var c=v.dimension,h=v.order,f=v.parser,p=v.incomparable;if(c==null&&ut(r),h!=="asc"&&h!=="desc"&&ut(r),p&&p!=="min"&&p!=="max"){var g="";ut(g)}if(h!=="asc"&&h!=="desc"){var d="";ut(d)}var y=e.getDimensionInfo(c);y||ut(r);var S=f?Sl(f):null;f&&!S&&ut(r),i.push({dimIdx:y.index,parser:S,comparator:new Ec(h,p)})});var o=e.sourceFormat;o!==pl&&o!==Rc&&ut(r);for(var s=[],l=0,u=e.count();l<u;l++)s.push(e.getRawDataItem(l));return s.sort(function(v,c){for(var h=0;h<i.length;h++){var f=i[h],p=e.retrieveValueFromItem(v,f.dimIdx),g=e.retrieveValueFromItem(c,f.dimIdx);f.parser&&(p=f.parser(p),g=f.parser(g));var d=f.comparator.evaluate(p,g);if(d!==0)return d}return 0}),{data:s}}};function jS(a){a.registerTransform(qS),a.registerTransform(KS)}var JS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.prototype.init=function(t,r,n){a.prototype.init.call(this,t,r,n),this._sourceManager=new Nc(this),Ni(this)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),Ni(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:Vc},e}(Kt),QS=function(a){V(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataset",t}return e.type="dataset",e}(jt);function t1(a){a.registerComponentModel(JS),a.registerComponentView(QS)}var $t=kc.CMD;function Pe(a,e){return Math.abs(a-e)<1e-5}function Ln(a){var e=a.data,t=a.len(),r=[],n,i=0,o=0,s=0,l=0;function u(E,C){n&&n.length>2&&r.push(n),n=[E,C]}function v(E,C,P,R){Pe(E,P)&&Pe(C,R)||n.push(E,C,P,R,P,R)}function c(E,C,P,R,N,k){var G=Math.abs(C-E),W=Math.tan(G/4)*4/3,B=C<E?-1:1,J=Math.cos(E),K=Math.sin(E),X=Math.cos(C),Q=Math.sin(C),dt=J*N+P,Z=K*k+R,H=X*N+P,et=Q*k+R,j=N*W*B,it=k*W*B;n.push(dt-j*K,Z+it*J,H+j*Q,et-it*X,H,et)}for(var h,f,p,g,d=0;d<t;){var y=e[d++],S=d===1;switch(S&&(i=e[d],o=e[d+1],s=i,l=o,(y===$t.L||y===$t.C||y===$t.Q)&&(n=[s,l])),y){case $t.M:i=s=e[d++],o=l=e[d++],u(s,l);break;case $t.L:h=e[d++],f=e[d++],v(i,o,h,f),i=h,o=f;break;case $t.C:n.push(e[d++],e[d++],e[d++],e[d++],i=e[d++],o=e[d++]);break;case $t.Q:h=e[d++],f=e[d++],p=e[d++],g=e[d++],n.push(i+2/3*(h-i),o+2/3*(f-o),p+2/3*(h-p),g+2/3*(f-g),p,g),i=p,o=g;break;case $t.A:var m=e[d++],x=e[d++],_=e[d++],b=e[d++],w=e[d++],T=e[d++]+w;d+=1;var I=!e[d++];h=Math.cos(w)*_+m,f=Math.sin(w)*b+x,S?(s=h,l=f,u(s,l)):v(i,o,h,f),i=Math.cos(T)*_+m,o=Math.sin(T)*b+x;for(var D=(I?-1:1)*Math.PI/2,A=w;I?A>T:A<T;A+=D){var M=I?Math.max(A+D,T):Math.min(A+D,T);c(A,M,m,x,_,b)}break;case $t.R:s=i=e[d++],l=o=e[d++],h=s+e[d++],f=l+e[d++],u(h,l),v(h,l,h,f),v(h,f,s,f),v(s,f,s,l),v(s,l,h,l);break;case $t.Z:n&&v(i,o,s,l),i=s,o=l;break}}return n&&n.length>2&&r.push(n),r}function Cn(a,e,t,r,n,i,o,s,l,u){if(Pe(a,t)&&Pe(e,r)&&Pe(n,o)&&Pe(i,s)){l.push(o,s);return}var v=2/u,c=v*v,h=o-a,f=s-e,p=Math.sqrt(h*h+f*f);h/=p,f/=p;var g=t-a,d=r-e,y=n-o,S=i-s,m=g*g+d*d,x=y*y+S*S;if(m<c&&x<c){l.push(o,s);return}var _=h*g+f*d,b=-h*y-f*S,w=m-_*_,T=x-b*b;if(w<c&&_>=0&&T<c&&b>=0){l.push(o,s);return}var I=[],D=[];Rr(a,t,n,o,.5,I),Rr(e,r,i,s,.5,D),Cn(I[0],D[0],I[1],D[1],I[2],D[2],I[3],D[3],l,u),Cn(I[4],D[4],I[5],D[5],I[6],D[6],I[7],D[7],l,u)}function e1(a,e){var t=Ln(a),r=[];e=e||1;for(var n=0;n<t.length;n++){var i=t[n],o=[],s=i[0],l=i[1];o.push(s,l);for(var u=2;u<i.length;){var v=i[u++],c=i[u++],h=i[u++],f=i[u++],p=i[u++],g=i[u++];Cn(s,l,v,c,h,f,p,g,o,e),s=p,l=g}r.push(o)}return r}function Lu(a,e,t){var r=a[e],n=a[1-e],i=Math.abs(r/n),o=Math.ceil(Math.sqrt(i*t)),s=Math.floor(t/o);s===0&&(s=1,o=t);for(var l=[],u=0;u<o;u++)l.push(s);var v=o*s,c=t-v;if(c>0)for(var u=0;u<c;u++)l[u%o]+=1;return l}function Ps(a,e,t){for(var r=a.r0,n=a.r,i=a.startAngle,o=a.endAngle,s=Math.abs(o-i),l=s*n,u=n-r,v=l>Math.abs(u),c=Lu([l,u],v?0:1,e),h=(v?s:u)/c.length,f=0;f<c.length;f++)for(var p=(v?u:s)/c[f],g=0;g<c[f];g++){var d={};v?(d.startAngle=i+h*f,d.endAngle=i+h*(f+1),d.r0=r+p*g,d.r=r+p*(g+1)):(d.startAngle=i+p*g,d.endAngle=i+p*(g+1),d.r0=r+h*f,d.r=r+h*(f+1)),d.clockwise=a.clockwise,d.cx=a.cx,d.cy=a.cy,t.push(d)}}function r1(a,e,t){for(var r=a.width,n=a.height,i=r>n,o=Lu([r,n],i?0:1,e),s=i?"width":"height",l=i?"height":"width",u=i?"x":"y",v=i?"y":"x",c=a[s]/o.length,h=0;h<o.length;h++)for(var f=a[l]/o[h],p=0;p<o[h];p++){var g={};g[u]=h*c,g[v]=p*f,g[s]=c,g[l]=f,g.x+=a.x,g.y+=a.y,t.push(g)}}function Ms(a,e,t,r){return a*r-t*e}function a1(a,e,t,r,n,i,o,s){var l=t-a,u=r-e,v=o-n,c=s-i,h=Ms(v,c,l,u);if(Math.abs(h)<1e-6)return null;var f=a-n,p=e-i,g=Ms(f,p,v,c)/h;return g<0||g>1?null:new ie(g*l+a,g*u+e)}function n1(a,e,t){var r=new ie;ie.sub(r,t,e),r.normalize();var n=new ie;ie.sub(n,a,e);var i=n.dot(r);return i}function De(a,e){var t=a[a.length-1];t&&t[0]===e[0]&&t[1]===e[1]||a.push(e)}function i1(a,e,t){for(var r=a.length,n=[],i=0;i<r;i++){var o=a[i],s=a[(i+1)%r],l=a1(o[0],o[1],s[0],s[1],e.x,e.y,t.x,t.y);l&&n.push({projPt:n1(l,e,t),pt:l,idx:i})}if(n.length<2)return[{points:a},{points:a}];n.sort(function(d,y){return d.projPt-y.projPt});var u=n[0],v=n[n.length-1];if(v.idx<u.idx){var c=u;u=v,v=c}for(var h=[u.pt.x,u.pt.y],f=[v.pt.x,v.pt.y],p=[h],g=[f],i=u.idx+1;i<=v.idx;i++)De(p,a[i].slice());De(p,f),De(p,h);for(var i=v.idx+1;i<=u.idx+r;i++)De(g,a[i%r].slice());return De(g,h),De(g,f),[{points:p},{points:g}]}function Es(a){var e=a.points,t=[],r=[];aa(e,t,r);var n=new vt(t[0],t[1],r[0]-t[0],r[1]-t[1]),i=n.width,o=n.height,s=n.x,l=n.y,u=new ie,v=new ie;return i>o?(u.x=v.x=s+i/2,u.y=l,v.y=l+o):(u.y=v.y=l+o/2,u.x=s,v.x=s+i),i1(e,u,v)}function $r(a,e,t,r){if(t===1)r.push(e);else{var n=Math.floor(t/2),i=a(e);$r(a,i[0],n,r),$r(a,i[1],t-n,r)}return r}function o1(a,e){for(var t=[],r=0;r<e;r++)t.push(Hn(a));return t}function s1(a,e){e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel}function l1(a){for(var e=[],t=0;t<a.length;)e.push([a[t++],a[t++]]);return e}function u1(a,e){var t=[],r=a.shape,n;switch(a.type){case"rect":r1(r,e,t),n=Dt;break;case"sector":Ps(r,e,t),n=Je;break;case"circle":Ps({r0:0,r:r.r,startAngle:0,endAngle:Math.PI*2,cx:r.cx,cy:r.cy},e,t),n=Je;break;default:var i=a.getComputedTransform(),o=i?Math.sqrt(Math.max(i[0]*i[0]+i[1]*i[1],i[2]*i[2]+i[3]*i[3])):1,s=O(e1(a.getUpdatedPathProxy(),o),function(y){return l1(y)}),l=s.length;if(l===0)$r(Es,{points:s[0]},e,t);else if(l===e)for(var u=0;u<l;u++)t.push({points:s[u]});else{var v=0,c=O(s,function(y){var S=[],m=[];aa(y,S,m);var x=(m[1]-S[1])*(m[0]-S[0]);return v+=x,{poly:y,area:x}});c.sort(function(y,S){return S.area-y.area});for(var h=e,u=0;u<l;u++){var f=c[u];if(h<=0)break;var p=u===l-1?h:Math.ceil(f.area/v*e);p<0||($r(Es,{points:f.poly},p,t),h-=p)}}n=re;break}if(!n)return o1(a,e);for(var g=[],u=0;u<t.length;u++){var d=new n;d.setShape(t[u]),s1(a,d),g.push(d)}return g}function v1(a,e){var t=a.length,r=e.length;if(t===r)return[a,e];for(var n=[],i=[],o=t<r?a:e,s=Math.min(t,r),l=Math.abs(r-t)/6,u=(s-2)/6,v=Math.ceil(l/u)+1,c=[o[0],o[1]],h=l,f=2;f<s;){var p=o[f-2],g=o[f-1],d=o[f++],y=o[f++],S=o[f++],m=o[f++],x=o[f++],_=o[f++];if(h<=0){c.push(d,y,S,m,x,_);continue}for(var b=Math.min(h,v-1)+1,w=1;w<=b;w++){var T=w/b;Rr(p,d,S,x,T,n),Rr(g,y,m,_,T,i),p=n[3],g=i[3],c.push(n[1],i[1],n[2],i[2],p,g),d=n[5],y=i[5],S=n[6],m=i[6]}h-=b-1}return o===a?[c,e]:[a,c]}function Rs(a,e){for(var t=a.length,r=a[t-2],n=a[t-1],i=[],o=0;o<e.length;)i[o++]=r,i[o++]=n;return i}function c1(a,e){for(var t,r,n,i=[],o=[],s=0;s<Math.max(a.length,e.length);s++){var l=a[s],u=e[s],v=void 0,c=void 0;l?u?(t=v1(l,u),v=t[0],c=t[1],r=v,n=c):(c=Rs(n||l,l),v=l):(v=Rs(r||u,u),c=u),i.push(v),o.push(c)}return[i,o]}function Ns(a){for(var e=0,t=0,r=0,n=a.length,i=0,o=n-2;i<n;o=i,i+=2){var s=a[o],l=a[o+1],u=a[i],v=a[i+1],c=s*v-u*l;e+=c,t+=(s+u)*c,r+=(l+v)*c}return e===0?[a[0]||0,a[1]||0]:[t/e/3,r/e/3,e]}function h1(a,e,t,r){for(var n=(a.length-2)/6,i=1/0,o=0,s=a.length,l=s-2,u=0;u<n;u++){for(var v=u*6,c=0,h=0;h<s;h+=2){var f=h===0?v:(v+h-2)%l+2,p=a[f]-t[0],g=a[f+1]-t[1],d=e[h]-r[0],y=e[h+1]-r[1],S=d-p,m=y-g;c+=S*S+m*m}c<i&&(i=c,o=u)}return o}function f1(a){for(var e=[],t=a.length,r=0;r<t;r+=2)e[r]=a[t-r-2],e[r+1]=a[t-r-1];return e}function p1(a,e,t,r){for(var n=[],i,o=0;o<a.length;o++){var s=a[o],l=e[o],u=Ns(s),v=Ns(l);i==null&&(i=u[2]<0!=v[2]<0);var c=[],h=[],f=0,p=1/0,g=[],d=s.length;i&&(s=f1(s));for(var y=h1(s,l,u,v)*6,S=d-2,m=0;m<S;m+=2){var x=(y+m)%S+2;c[m+2]=s[x]-u[0],c[m+3]=s[x+1]-u[1]}if(c[0]=s[y]-u[0],c[1]=s[y+1]-u[1],t>0)for(var _=r/t,b=-r/2;b<=r/2;b+=_){for(var w=Math.sin(b),T=Math.cos(b),I=0,m=0;m<s.length;m+=2){var D=c[m],A=c[m+1],M=l[m]-v[0],E=l[m+1]-v[1],C=M*T-E*w,P=M*w+E*T;g[m]=C,g[m+1]=P;var R=C-D,N=P-A;I+=R*R+N*N}if(I<p){p=I,f=b;for(var k=0;k<g.length;k++)h[k]=g[k]}}else for(var G=0;G<d;G+=2)h[G]=l[G]-v[0],h[G+1]=l[G+1]-v[1];n.push({from:c,to:h,fromCp:u,toCp:v,rotation:-f})}return n}function Hr(a){return a.__isCombineMorphing}var Cu="__mOriginal_";function Ur(a,e,t){var r=Cu+e,n=a[r]||a[e];a[r]||(a[r]=a[e]);var i=t.replace,o=t.after,s=t.before;a[e]=function(){var l=arguments,u;return s&&s.apply(this,l),i?u=i.apply(this,l):u=n.apply(this,l),o&&o.apply(this,l),u}}function qe(a,e){var t=Cu+e;a[t]&&(a[e]=a[t],a[t]=null)}function Vs(a,e){for(var t=0;t<a.length;t++)for(var r=a[t],n=0;n<r.length;){var i=r[n],o=r[n+1];r[n++]=e[0]*i+e[2]*o+e[4],r[n++]=e[1]*i+e[3]*o+e[5]}}function Pu(a,e){var t=a.getUpdatedPathProxy(),r=e.getUpdatedPathProxy(),n=c1(Ln(t),Ln(r)),i=n[0],o=n[1],s=a.getComputedTransform(),l=e.getComputedTransform();function u(){this.transform=null}s&&Vs(i,s),l&&Vs(o,l),Ur(e,"updateTransform",{replace:u}),e.transform=null;var v=p1(i,o,10,Math.PI),c=[];Ur(e,"buildPath",{replace:function(h){for(var f=e.__morphT,p=1-f,g=[],d=0;d<v.length;d++){var y=v[d],S=y.from,m=y.to,x=y.rotation*f,_=y.fromCp,b=y.toCp,w=Math.sin(x),T=Math.cos(x);Gc(g,_,b,f);for(var I=0;I<S.length;I+=2){var D=S[I],A=S[I+1],M=m[I],E=m[I+1],C=D*p+M*f,P=A*p+E*f;c[I]=C*T-P*w+g[0],c[I+1]=C*w+P*T+g[1]}var R=c[0],N=c[1];h.moveTo(R,N);for(var I=2;I<S.length;){var M=c[I++],E=c[I++],k=c[I++],G=c[I++],W=c[I++],B=c[I++];R===M&&N===E&&k===W&&G===B?h.lineTo(W,B):h.bezierCurveTo(M,E,k,G,W,B),R=W,N=B}}}})}function di(a,e,t){if(!a||!e)return e;var r=t.done,n=t.during;Pu(a,e),e.__morphT=0;function i(){qe(e,"buildPath"),qe(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return e.animateTo({__morphT:1},nt({during:function(o){e.dirtyShape(),n&&n(o)},done:function(){i(),r&&r()}},t)),e}function g1(a,e,t,r,n,i){var o=16;a=n===t?0:Math.round(32767*(a-t)/(n-t)),e=i===r?0:Math.round(32767*(e-r)/(i-r));for(var s=0,l,u=(1<<o)/2;u>0;u/=2){var v=0,c=0;(a&u)>0&&(v=1),(e&u)>0&&(c=1),s+=u*u*(3*v^c),c===0&&(v===1&&(a=u-1-a,e=u-1-e),l=a,a=e,e=l)}return s}function Yr(a){var e=1/0,t=1/0,r=-1/0,n=-1/0,i=O(a,function(s){var l=s.getBoundingRect(),u=s.getComputedTransform(),v=l.x+l.width/2+(u?u[4]:0),c=l.y+l.height/2+(u?u[5]:0);return e=Math.min(v,e),t=Math.min(c,t),r=Math.max(v,r),n=Math.max(c,n),[v,c]}),o=O(i,function(s,l){return{cp:s,z:g1(s[0],s[1],e,t,r,n),path:a[l]}});return o.sort(function(s,l){return s.z-l.z}).map(function(s){return s.path})}function Mu(a){return u1(a.path,a.count)}function Pn(){return{fromIndividuals:[],toIndividuals:[],count:0}}function d1(a,e,t){var r=[];function n(_){for(var b=0;b<_.length;b++){var w=_[b];Hr(w)?n(w.childrenRef()):w instanceof _t&&r.push(w)}}n(a);var i=r.length;if(!i)return Pn();var o=t.dividePath||Mu,s=o({path:e,count:i});if(s.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Pn();r=Yr(r),s=Yr(s);for(var l=t.done,u=t.during,v=t.individualDelay,c=new Le,h=0;h<i;h++){var f=r[h],p=s[h];p.parent=e,p.copyTransform(c),v||Pu(f,p)}e.__isCombineMorphing=!0,e.childrenRef=function(){return s};function g(_){for(var b=0;b<s.length;b++)s[b].addSelfToZr(_)}Ur(e,"addSelfToZr",{after:function(_){g(_)}}),Ur(e,"removeSelfFromZr",{after:function(_){for(var b=0;b<s.length;b++)s[b].removeSelfFromZr(_)}});function d(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,qe(e,"addSelfToZr"),qe(e,"removeSelfFromZr")}var y=s.length;if(v)for(var S=y,m=function(){S--,S===0&&(d(),l&&l())},h=0;h<y;h++){var x=v?nt({delay:(t.delay||0)+v(h,y,r[h],s[h]),done:m},t):t;di(r[h],s[h],x)}else e.__morphT=0,e.animateTo({__morphT:1},nt({during:function(_){for(var b=0;b<y;b++){var w=s[b];w.__morphT=e.__morphT,w.dirtyShape()}u&&u(_)},done:function(){d();for(var _=0;_<a.length;_++)qe(a[_],"updateTransform");l&&l()}},t));return e.__zr&&g(e.__zr),{fromIndividuals:r,toIndividuals:s,count:y}}function y1(a,e,t){var r=e.length,n=[],i=t.dividePath||Mu;function o(f){for(var p=0;p<f.length;p++){var g=f[p];Hr(g)?o(g.childrenRef()):g instanceof _t&&n.push(g)}}if(Hr(a)){o(a.childrenRef());var s=n.length;if(s<r)for(var l=0,u=s;u<r;u++)n.push(Hn(n[l++%s]));n.length=r}else{n=i({path:a,count:r});for(var v=a.getComputedTransform(),u=0;u<n.length;u++)n[u].setLocalTransform(v);if(n.length!==r)return console.error("Invalid morphing: unmatched splitted path"),Pn()}n=Yr(n),e=Yr(e);for(var c=t.individualDelay,u=0;u<r;u++){var h=c?nt({delay:(t.delay||0)+c(u,r,n[u],e[u])},t):t;di(n[u],e[u],h)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}function ks(a){return F(a[0])}function Gs(a,e){for(var t=[],r=a.length,n=0;n<r;n++)t.push({one:a[n],many:[]});for(var n=0;n<e.length;n++){var i=e[n].length,o=void 0;for(o=0;o<i;o++)t[o%r].many.push(e[n][o])}for(var s=0,n=r-1;n>=0;n--)if(!t[n].many.length){var l=t[s].many;if(l.length<=1)if(s)s=0;else return t;var i=l.length,u=Math.ceil(i/2);t[n].many=l.slice(u,i),t[s].many=l.slice(0,u),s++}return t}var m1={clone:function(a){for(var e=[],t=1-Math.pow(1-a.path.style.opacity,1/a.count),r=0;r<a.count;r++){var n=Hn(a.path);n.setStyle("opacity",t),e.push(n)}return e},split:null};function en(a,e,t,r,n,i){if(!a.length||!e.length)return;var o=Un("update",r,n);if(!(o&&o.duration>0))return;var s=r.getModel("universalTransition").get("delay"),l=Object.assign({setToFinal:!0},o),u,v;ks(a)&&(u=a,v=e),ks(e)&&(u=e,v=a);function c(y,S,m,x,_){var b=y.many,w=y.one;if(b.length===1&&!_){var T=S?b[0]:w,I=S?w:b[0];if(Hr(T))c({many:[T],one:I},!0,m,x,!0);else{var D=s?nt({delay:s(m,x)},l):l;di(T,I,D),i(T,I,T,I,D)}}else for(var A=nt({dividePath:m1[t],individualDelay:s&&function(N,k,G,W){return s(N+m,x)}},l),M=S?d1(b,w,A):y1(w,b,A),E=M.fromIndividuals,C=M.toIndividuals,P=E.length,R=0;R<P;R++){var D=s?nt({delay:s(R,P)},l):l;i(E[R],C[R],S?b[R]:y.one,S?y.one:b[R],D)}}for(var h=u?u===a:a.length>e.length,f=u?Gs(v,u):Gs(h?e:a,[h?a:e]),p=0,g=0;g<f.length;g++)p+=f[g].many.length;for(var d=0,g=0;g<f.length;g++)c(f[g],h,d,p),d+=f[g].many.length}function me(a){if(!a)return[];if(F(a)){for(var e=[],t=0;t<a.length;t++)e.push(me(a[t]));return e}var r=[];return a.traverse(function(n){n instanceof _t&&!n.disableMorphing&&!n.invisible&&!n.ignore&&r.push(n)}),r}var Eu=1e4,S1=0,zs=1,Os=2,x1=xt();function _1(a,e){for(var t=a.dimensions,r=0;r<t.length;r++){var n=a.getDimensionInfo(t[r]);if(n&&n.otherDims[e]===0)return t[r]}}function b1(a,e,t){var r=a.getDimensionInfo(t),n=r&&r.ordinalMeta;if(r){var i=a.get(r.name,e);return n&&n.categories[i]||i+""}}function Bs(a,e,t,r){var n=r?"itemChildGroupId":"itemGroupId",i=_1(a,n);if(i){var o=b1(a,e,i);return o}var s=a.getRawDataItem(e),l=r?"childGroupId":"groupId";if(s&&s[l])return s[l]+"";if(!r)return t||a.getId(e)}function Fs(a){var e=[];return L(a,function(t){var r=t.data,n=t.dataGroupId;if(!(r.count()>Eu))for(var i=r.getIndices(),o=0;o<i.length;o++)e.push({data:r,groupId:Bs(r,o,n,!1),childGroupId:Bs(r,o,n,!0),divide:t.divide,dataIndex:o})}),e}function rn(a,e,t){a.traverse(function(r){r instanceof _t&&wt(r,{style:{opacity:0}},e,{dataIndex:t,isFrom:!0})})}function an(a){if(a.parent){var e=a.getComputedTransform();a.setLocalTransform(e),a.parent.remove(a)}}function Ae(a){a.stopAnimation(),a.isGroup&&a.traverse(function(e){e.stopAnimation()})}function w1(a,e,t){var r=Un("update",t,e);r&&a.traverse(function(n){if(n instanceof Ke){var i=zc(n);i&&n.animateFrom({style:i},r)}})}function T1(a,e){var t=a.length;if(t!==e.length)return!1;for(var r=0;r<t;r++){var n=a[r],i=e[r];if(n.data.getId(n.dataIndex)!==i.data.getId(i.dataIndex))return!1}return!0}function Ru(a,e,t){var r=Fs(a),n=Fs(e);function i(m,x,_,b,w){(_||m)&&x.animateFrom({style:_&&_!==m?$($({},_.style),m.style):m.style},w)}var o=!1,s=S1,l=q(),u=q();r.forEach(function(m){m.groupId&&l.set(m.groupId,!0),m.childGroupId&&u.set(m.childGroupId,!0)});for(var v=0;v<n.length;v++){var c=n[v].groupId;if(u.get(c)){s=zs;break}var h=n[v].childGroupId;if(h&&l.get(h)){s=Os;break}}function f(m,x){return function(_){var b=_.data,w=_.dataIndex;return x?b.getId(w):m?s===zs?_.childGroupId:_.groupId:s===Os?_.childGroupId:_.groupId}}var p=T1(r,n),g={};if(!p)for(var v=0;v<n.length;v++){var d=n[v],y=d.data.getItemGraphicEl(d.dataIndex);y&&(g[y.id]=!0)}function S(m,x){var _=r[x],b=n[m],w=b.data.hostModel,T=_.data.getItemGraphicEl(_.dataIndex),I=b.data.getItemGraphicEl(b.dataIndex);if(T===I){I&&w1(I,b.dataIndex,w);return}T&&g[T.id]||I&&(Ae(I),T?(Ae(T),an(T),o=!0,en(me(T),me(I),b.divide,w,m,i)):rn(I,w,m))}new Ne(r,n,f(!0,p),f(!1,p),null,"multiple").update(S).updateManyToOne(function(m,x){var _=n[m],b=_.data,w=b.hostModel,T=b.getItemGraphicEl(_.dataIndex),I=Nt(O(x,function(D){return r[D].data.getItemGraphicEl(r[D].dataIndex)}),function(D){return D&&D!==T&&!g[D.id]});T&&(Ae(T),I.length?(L(I,function(D){Ae(D),an(D)}),o=!0,en(me(I),me(T),_.divide,w,m,i)):rn(T,w,_.dataIndex))}).updateOneToMany(function(m,x){var _=r[x],b=_.data.getItemGraphicEl(_.dataIndex);if(!(b&&g[b.id])){var w=Nt(O(m,function(I){return n[I].data.getItemGraphicEl(n[I].dataIndex)}),function(I){return I&&I!==b}),T=n[m[0]].data.hostModel;w.length&&(L(w,function(I){return Ae(I)}),b?(Ae(b),an(b),o=!0,en(me(b),me(w),_.divide,T,m[0],i)):L(w,function(I){return rn(I,T,m[0])}))}}).updateManyToMany(function(m,x){new Ne(x,m,function(_){return r[_].data.getId(r[_].dataIndex)},function(_){return n[_].data.getId(n[_].dataIndex)}).update(function(_,b){S(m[_],x[b])}).execute()}).execute(),o&&L(e,function(m){var x=m.data,_=x.hostModel,b=_&&t.getViewOfSeriesModel(_),w=Un("update",_,0);b&&_.isAnimationEnabled()&&w&&w.duration>0&&b.group.traverse(function(T){T instanceof _t&&!T.animators.length&&T.animateFrom({style:{opacity:0}},w)})})}function Ws(a){var e=a.getModel("universalTransition").get("seriesKey");return e||a.id}function $s(a){return F(a)?a.sort().join(","):a}function ne(a){if(a.hostModel)return a.hostModel.getModel("universalTransition").get("divideShape")}function I1(a,e){var t=q(),r=q(),n=q();return L(a.oldSeries,function(i,o){var s=a.oldDataGroupIds[o],l=a.oldData[o],u=Ws(i),v=$s(u);r.set(v,{dataGroupId:s,data:l}),F(u)&&L(u,function(c){n.set(c,{key:v,dataGroupId:s,data:l})})}),L(e.updatedSeries,function(i){if(i.isUniversalTransitionEnabled()&&i.isAnimationEnabled()){var o=i.get("dataGroupId"),s=i.getData(),l=Ws(i),u=$s(l),v=r.get(u);if(v)t.set(u,{oldSeries:[{dataGroupId:v.dataGroupId,divide:ne(v.data),data:v.data}],newSeries:[{dataGroupId:o,divide:ne(s),data:s}]});else if(F(l)){var c=[];L(l,function(p){var g=r.get(p);g.data&&c.push({dataGroupId:g.dataGroupId,divide:ne(g.data),data:g.data})}),c.length&&t.set(u,{oldSeries:c,newSeries:[{dataGroupId:o,data:s,divide:ne(s)}]})}else{var h=n.get(l);if(h){var f=t.get(h.key);f||(f={oldSeries:[{dataGroupId:h.dataGroupId,data:h.data,divide:ne(h.data)}],newSeries:[]},t.set(h.key,f)),f.newSeries.push({dataGroupId:o,data:s,divide:ne(s)})}}}}),t}function Hs(a,e){for(var t=0;t<a.length;t++){var r=e.seriesIndex!=null&&e.seriesIndex===a[t].seriesIndex||e.seriesId!=null&&e.seriesId===a[t].id;if(r)return t}}function D1(a,e,t,r){var n=[],i=[];L(Ot(a.from),function(o){var s=Hs(e.oldSeries,o);s>=0&&n.push({dataGroupId:e.oldDataGroupIds[s],data:e.oldData[s],divide:ne(e.oldData[s]),groupIdDim:o.dimension})}),L(Ot(a.to),function(o){var s=Hs(t.updatedSeries,o);if(s>=0){var l=t.updatedSeries[s].getData();i.push({dataGroupId:e.oldDataGroupIds[s],data:l,divide:ne(l),groupIdDim:o.dimension})}}),n.length>0&&i.length>0&&Ru(n,i,r)}function A1(a){a.registerUpdateLifecycle("series:beforeupdate",function(e,t,r){L(Ot(r.seriesTransition),function(n){L(Ot(n.to),function(i){for(var o=r.updatedSeries,s=0;s<o.length;s++)(i.seriesIndex!=null&&i.seriesIndex===o[s].seriesIndex||i.seriesId!=null&&i.seriesId===o[s].id)&&(o[s][fa]=!0)})})}),a.registerUpdateLifecycle("series:transition",function(e,t,r){var n=x1(t);if(n.oldSeries&&r.updatedSeries&&r.optionChanged){var i=r.seriesTransition;if(i)L(Ot(i),function(f){D1(f,n,r,t)});else{var o=I1(n,r);L(o.keys(),function(f){var p=o.get(f);Ru(p.oldSeries,p.newSeries,t)})}L(r.updatedSeries,function(f){f[fa]&&(f[fa]=!1)})}for(var s=e.getSeries(),l=n.oldSeries=[],u=n.oldDataGroupIds=[],v=n.oldData=[],c=0;c<s.length;c++){var h=s[c].getData();h.count()<Eu&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),v.push(h))}})}Y([Oc]);Y([Bc]);Y([Fc,Wc,$c,gh,Eh,bf,Jf,Np,ig,fg,Tg,cd,zd,jd,yy,Ty,Fy,jy,vm,mm,Pm,i0]);Y(Hc);Y(Uc);Y(Cl);Y(b0);Y(tu);Y(C0);Y(Yc);Y(Xc);Y(Zc);Y(yl);Y(X0);Y(qc);Y(sS);Y(yS);Y(IS);Y(RS);Y(Kc);Y(jc);Y(Jc);Y(Qc);Y(th);Y(eh);Y(rh);Y(zS);Y(jS);Y(t1);Y(A1);Y(sh);
