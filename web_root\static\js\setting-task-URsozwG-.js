var de=Object.defineProperty,ue=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable;var Y=(n,g,b)=>g in n?de(n,g,{enumerable:!0,configurable:!0,writable:!0,value:b}):n[g]=b,H=(n,g)=>{for(var b in g||(g={}))ne.call(g,b)&&Y(n,b,g[b]);if(G)for(var b of G(g))_e.call(g,b)&&Y(n,b,g[b]);return n},K=(n,g)=>ue(n,ie(g));var Q=(n,g,b)=>new Promise((O,L)=>{var A=h=>{try{T(b.next(h))}catch(c){L(c)}},m=h=>{try{T(b.throw(h))}catch(c){L(c)}},T=h=>h.done?O(h.value):Promise.resolve(h.value).then(A,m);T((b=b.apply(n,g)).next())});import{d as ce,l as E,n as pe,b6 as me,r as y,o as p,c as V,b as t,h as l,F as S,p as B,e as v,U as he,t as D,f as N,q as f,g as q,y as w,T as X,ac as be,ad as ge,_ as fe}from"./index-B63pSD2p.js";import{a as ve}from"./marking-mode-CLpbbjcA.js";import{g as ye}from"./formal-task-CUOmIYGE.js";var ke={VITE_PORT:"8848",VITE_HIDE_HOME:"true",VITE_PUBLIC_PATH:"/",VITE_ROUTER_HISTORY:"hash",VITE_CDN:"false",VITE_COMPRESSION:"none",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const R=n=>(be("data-v-52b8288d"),n=n(),ge(),n),Ve={class:"all-task-box"},qe={class:"info"},we=R(()=>v("span",{class:"line"},null,-1)),xe={class:"text"},Te={class:"text"},Ue={class:"add-task-form"},Le={class:"text"},Fe=["title"],Ee=R(()=>v("span",{innerHTML:"最终结果取分方式: <br/>最高分: 取所有评分中的最高分<br/>最低分: 取所有评分中的最低分<br/>平均分: 取所有评分求平均分<br/>（注：如有仲裁则取仲裁分数）"},null,-1)),Ne=R(()=>v("span",{innerHTML:"最终结果计算方式：<br/>四舍五入：1.553 -> 1.55<br/>向上舍入：1.553 -> 1.56<br/>向下舍入：1.553 -> 1.55"},null,-1)),We=["title"],Pe={key:0,class:"add-task-form"},Ce={class:"text"},Se=R(()=>v("span",{innerHTML:"评分专家人数>3时的分差取值：<br/>平均值：所有评分的平均值<br/>最大偏差：最高分和最低分的差值<br/>最小偏差：最低两个评分的差值<br/>"},null,-1)),Ie=ce({__name:"setting-task",props:{addFlag:{type:String,default:"add"},isFullscreen:{type:Boolean,default:!1},taskType:{type:Number,default:1}},emits:["deleteTask"],setup(n,{expose:g,emit:b}){const{VITE_PUBLIC_PATH:O}=ke,L=n,A=b;E(null);const m={itemWidth:"150px",labelWidth:"108px",rules:{task_name:[{required:!0,message:"请输入任务名称",trigger:["blur","change"]}],process_id:[{required:!0,message:"请选择阅卷模式",trigger:["blur","change"]}],fetch_score_way:[{required:!0,message:"请选择取分方式",trigger:["blur","change"]}],fetch_score_option:[{required:!0,message:"请选择分值计算",trigger:["blur","change"]}],group_id_list:[{required:!0,message:"请选择阅卷小组",trigger:["blur","change"]}],mark_score_step:[{required:!0,message:"请设置评分步长",trigger:["blur","change"]}],arbitrate_threshold:[{required:!0,message:"请设置仲裁阈值",trigger:["blur","change"]}],arbitrate_score_diff:[{required:!0,message:"请选择分差取值",trigger:["blur","change"]}],arbitrate_deviation:[{required:!0,message:"请输入离差阈值",trigger:["blur","change"]}],try_mark_ques_num:[{required:!0,message:"请输入试评题数",trigger:["blur","change"]}],allow_diff_score:[{required:!0,message:"请输入允许偏差分数",trigger:["blur","change"]}]}},T=E({}),h=E({manualProcessList:[],groupList:{}}),c=E([]),Z={group_id_list:[],arbitrate_threshold_type:1,deviation_threshold_type:1};E([]),E(280),pe(()=>{ee(),te()});const ee=()=>{T.value={},me({method:"get",url:`${O}marking-default-value.json`}).then(({data:r})=>{for(let a in r)a.startsWith("#")||(T.value[a]=r[a])}).catch(()=>{throw"请在public文件夹下添加marking-default-value.json配置文件"})},te=()=>{ve({page_size:-1}).then(a=>{var u,i,s;if(a.code&&a.code===200&&((i=(u=a.data)==null?void 0:u.data)==null||i.forEach(d=>{d.label=d.process_name,d.value=d.process_id}),h.value.manualProcessList=(s=a.data)==null?void 0:s.data,L.addFlag==="edit")){let d=c.value[0];const _=h.value.manualProcessList.find(x=>x.process_id===d.process_id);d.expert_num=_.expert_num,d.arbitrator_num=_.arbitrator_num,_.arbitrate_threshold&&(d.arbitrate_threshold=_.arbitrate_threshold)}})},M=(r,a=!1)=>{ye(r).then(u=>{u.code&&u.code===200?(h.value.groupList[r.ques_code]=u.data.data||[],a&&c.value.forEach(i=>{var s;i.ques_code===r.ques_code&&(s=h.value.groupList[i.ques_code])!=null&&s.length&&(i.group_id_list=[h.value.groupList[i.ques_code][0].group_id])})):X.error(u.msg)})},j=(r,a)=>{if(r==="process_id"){const u=h.value.manualProcessList.find(i=>i.process_id===a.process_id);a.expert_num=u.expert_num,a.arbitrator_num=u.arbitrator_num,u.arbitrate_threshold&&(a.arbitrate_threshold=u.arbitrate_threshold)}},le=(r,a)=>{if(c.value.length===0)r.forEach((u,i)=>{z(u,a)});else{const u=r.map(s=>s.ques_code);c.value=JSON.parse(JSON.stringify(c.value)).filter(s=>u.includes(s.ques_code));const i=c.value.map(s=>s.ques_code);r.forEach((s,d)=>{const{ques_code:_}=s;i.includes(_)||z(s,a)})}},z=(r,a)=>{var I;const{project_id:u,subject_id:i,subject_name:s,ques_id:d,ques_code:_,business_type_name:x,ques_score:U,ques_type_code:F}=r;let W=[];M({project_id:u,subject_id:i,ques_code:_},!0);let k=K(H(H({},Z),T.value),{project_id:u,subject_id:i,ques_id:d,ques_code:_,task_name:s+_,groupList:W,business_type_name:x,ques_score:U,ques_type_code:F});L.addFlag==="add"&&L.taskType===1&&(I=h.value.manualProcessList)!=null&&I.length&&(k.process_id=h.value.manualProcessList[0].process_id,j("process_id",k)),a&&a.hasOwnProperty(r.ques_code)&&(k.hasTask=!0,k.task_name=a[r.ques_code].task_name,k.group_id_list=a[r.ques_code].group_list.map(P=>P.group_id),k.group_id_name=a[r.ques_code].group_list.map(P=>P.group_name)),c.value.push(k)},ae=r=>{c.value.push(r[0]);const{project_id:a,subject_id:u,ques_code:i,process_id:s}=r[0];M({project_id:a,subject_id:u,ques_code:i})},se=r=>{c.value.splice(r,1),A("deleteTask")},oe=()=>Q(this,null,function*(){var a,u,i;let r=0;for(let s=0;s<=c.value.length;s++)if(c.value[s]){yield(a=c.value[s].taskRef)==null?void 0:a.validate(_=>{_||r++});const d=c.value[s].arbitrator_num;d&&d>0&&(yield(i=(u=c.value[s])==null?void 0:u.arbitratorRef)==null?void 0:i.validate(_=>{_||r++}))}if(r===0)return c.value;X.warning("请按要求填写！")});function $(r){const a=parseInt(r.replace(/[^0123456789]+/g,""));return isNaN(a)?"":a}function J(r){const a=parseInt(r);return isNaN(a)?"":a}return g({setSelectionList:le,getTaskParams:oe,setFormDataList:ae}),(r,a)=>{const u=y("el-text"),i=y("el-input"),s=y("el-form-item"),d=y("el-option"),_=y("el-select"),x=y("InfoFilled"),U=y("el-icon"),F=y("el-tooltip"),W=y("el-input-number"),k=y("el-form"),I=y("Delete"),P=y("el-scrollbar");return p(),V("div",null,[t(P,{always:"",height:n.isFullscreen?"80vh":"590px"},{default:l(()=>[(p(!0),V(S,null,B(c.value,(e,re)=>(p(),V("div",Ve,[v("div",{class:he(["every-task-box",n.addFlag==="add"?"every-task-bg dark:!bg-black eye-box ":""])},[v("div",qe,[we,v("span",xe,"试题编号："+D(e.ques_code),1),v("span",Te,"总分："+D(e.ques_score),1)]),v("div",Ue,[v("div",Le,[t(u,null,{default:l(()=>[N("任务信息")]),_:1})]),v("div",null,[t(k,{ref_for:!0,ref:o=>e.taskRef=o,inline:!0,"validate-on-rule-change":!1,"label-width":m.labelWidth,model:e,rules:m.rules,disabled:e.disabled},{default:l(()=>[t(s,{label:"任务名称",prop:"task_name"},{default:l(()=>[e.hasTask?(p(),V("div",{key:0,style:f({width:m.itemWidth}),title:e.task_name,class:"task-name-box"},D(e.task_name),13,Fe)):(p(),q(i,{key:1,title:e.task_name,modelValue:e.task_name,"onUpdate:modelValue":o=>e.task_name=o,style:f({width:m.itemWidth}),placeholder:"请输入",clearable:""},null,8,["title","modelValue","onUpdate:modelValue","style"]))]),_:2},1024),n.taskType===1?(p(),q(s,{key:0,label:"阅卷模式",prop:"process_id"},{default:l(()=>[t(_,{modelValue:e.process_id,"onUpdate:modelValue":o=>e.process_id=o,placeholder:"请选择",style:f({width:m.itemWidth}),clearable:"",filterable:"",onChange:o=>j("process_id",e)},{default:l(()=>[(p(!0),V(S,null,B(h.value.manualProcessList,o=>(p(),q(d,{key:o.process_id,value:o.process_id,label:o.process_name},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","style","onChange"])]),_:2},1024)):w("",!0),n.taskType===1?(p(),V(S,{key:1},[e.expert_num&&e.expert_num>=2?(p(),q(s,{key:0,label:"取分方式",prop:"fetch_score_way"},{label:l(()=>[N(" 取分方式 "),t(F,{effect:"light"},{content:l(()=>[Ee]),default:l(()=>[t(u,null,{default:l(()=>[t(U,{color:"#9FA6AF"},{default:l(()=>[t(x)]),_:1})]),_:1})]),_:1})]),default:l(()=>[t(_,{modelValue:e.fetch_score_way,"onUpdate:modelValue":o=>e.fetch_score_way=o,placeholder:"请选择",style:f({width:m.itemWidth}),clearable:"",filterable:""},{default:l(()=>[t(d,{label:"最高分",value:1}),t(d,{label:"最低分",value:2}),t(d,{label:"平均分",value:3}),e.expert_num&&e.expert_num>=3?(p(),q(d,{key:0,label:"去除最高和最低分取平均分",value:4})):w("",!0)]),_:2},1032,["modelValue","onUpdate:modelValue","style"])]),_:2},1024)):w("",!0),e.fetch_score_way===3?(p(),q(s,{key:1,label:"分值计算",prop:"fetch_score_option"},{label:l(()=>[N(" 分值计算 "),t(F,{effect:"light"},{content:l(()=>[Ne]),default:l(()=>[t(u,null,{default:l(()=>[t(U,{color:"#9FA6AF"},{default:l(()=>[t(x)]),_:1})]),_:1})]),_:1})]),default:l(()=>[t(_,{modelValue:e.fetch_score_option,"onUpdate:modelValue":o=>e.fetch_score_option=o,placeholder:"请选择",style:f({width:m.itemWidth}),clearable:"",filterable:""},{default:l(()=>[t(d,{label:"四舍五入",value:3}),t(d,{label:"向上舍入",value:2}),t(d,{label:"向下舍入",value:1})]),_:2},1032,["modelValue","onUpdate:modelValue","style"])]),_:2},1024)):w("",!0)],64)):w("",!0),t(s,{label:"阅卷小组",prop:"group_id_list"},{default:l(()=>{var o;return[e.hasTask?(p(),V("div",{key:0,style:f({width:m.itemWidth}),title:e.group_id_name,class:"task-name-box"},D((o=e.group_id_name)==null?void 0:o.join(",")),13,We)):(p(),q(_,{key:1,modelValue:e.group_id_list,"onUpdate:modelValue":C=>e.group_id_list=C,placeholder:"请选择",style:f({width:m.itemWidth}),clearable:"",multiple:""},{default:l(()=>[(p(!0),V(S,null,B(h.value.groupList[e.ques_code],C=>(p(),q(d,{key:C.group_id,value:C.group_id,label:C.group_name},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","style"]))]}),_:2},1024),n.taskType===2?(p(),V(S,{key:2},[t(s,{label:"作答数",prop:"try_mark_ques_num"},{default:l(()=>[t(W,{title:e.try_mark_ques_num,modelValue:e.try_mark_ques_num,"onUpdate:modelValue":o=>e.try_mark_ques_num=o,style:f({width:m.itemWidth}),placeholder:"请输入",clearable:"",controls:!1,min:0,step:1,"step-strictly":""},null,8,["title","modelValue","onUpdate:modelValue","style"])]),_:2},1024),t(s,{label:"允许偏差分数",prop:"allow_diff_score"},{default:l(()=>[t(W,{title:e.allow_diff_score,modelValue:e.allow_diff_score,"onUpdate:modelValue":o=>e.allow_diff_score=o,style:f({width:m.itemWidth}),placeholder:"请输入",clearable:"",controls:!1,min:0,max:Number(e.ques_score)},null,8,["title","modelValue","onUpdate:modelValue","style","max"])]),_:2},1024)],64)):w("",!0),t(s,{label:"评分步长",prop:"mark_score_step"},{default:l(()=>[t(W,{modelValue:e.mark_score_step,"onUpdate:modelValue":o=>e.mark_score_step=o,style:f({width:m.itemWidth}),min:Number(e.ques_score)<.5?Number(e.ques_score):.5,max:Number(e.ques_score),step:.5,"controls-position":"right",onChange:j},null,8,["modelValue","onUpdate:modelValue","style","min","max"])]),_:2},1024)]),_:2},1032,["label-width","model","rules","disabled"])])]),e.arbitrator_num&&e.arbitrator_num>=1?(p(),V("div",Pe,[v("div",Ce,[t(u,null,{default:l(()=>[N("仲裁设置")]),_:1})]),v("div",null,[t(k,{ref_for:!0,ref:o=>e.arbitratorRef=o,inline:!0,"validate-on-rule-change":!1,"label-width":m.labelWidth,model:e,rules:m.rules,disabled:e.disabled},{default:l(()=>[t(s,{label:"仲裁阈值",prop:"arbitrate_threshold"},{default:l(()=>[t(i,{modelValue:e.arbitrate_threshold,"onUpdate:modelValue":o=>e.arbitrate_threshold=o,style:f({width:m.itemWidth}),formatter:$,parser:J,placeholder:"请输入",clearable:""},{append:l(()=>[t(_,{modelValue:e.arbitrate_threshold_type,"onUpdate:modelValue":o=>e.arbitrate_threshold_type=o,style:{width:"58px"}},{default:l(()=>[t(d,{label:"%",value:1}),t(d,{label:"分",value:2})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["modelValue","onUpdate:modelValue","style"])]),_:2},1024),t(s,{label:"离差阈值",prop:"arbitrate_deviation"},{label:l(()=>[N(" 离差阈值 "),t(F,{effect:"light",content:"计算仲裁有效量使用，符合离差阈值范围内的仲裁，属有效阅卷量"},{default:l(()=>[t(u,null,{default:l(()=>[t(U,{color:"#9FA6AF"},{default:l(()=>[t(x)]),_:1})]),_:1})]),_:1})]),default:l(()=>[t(i,{modelValue:e.arbitrate_deviation,"onUpdate:modelValue":o=>e.arbitrate_deviation=o,style:f({width:m.itemWidth}),formatter:$,parser:J,placeholder:"请输入",clearable:""},{append:l(()=>[t(_,{modelValue:e.deviation_threshold_type,"onUpdate:modelValue":o=>e.deviation_threshold_type=o,style:{width:"58px"}},{default:l(()=>[t(d,{label:"%",value:1}),t(d,{label:"分",value:2})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["modelValue","onUpdate:modelValue","style"])]),_:2},1024),e.expert_num&&e.expert_num>2?(p(),q(s,{key:0,label:"分差取值",prop:"arbitrate_score_diff"},{label:l(()=>[N(" 分差取值 "),t(F,{effect:"light"},{content:l(()=>[Se]),default:l(()=>[t(u,null,{default:l(()=>[t(U,{color:"#9FA6AF"},{default:l(()=>[t(x)]),_:1})]),_:1})]),_:1})]),default:l(()=>[t(_,{modelValue:e.arbitrate_score_diff,"onUpdate:modelValue":o=>e.arbitrate_score_diff=o,placeholder:"请选择",style:f({width:m.itemWidth}),clearable:"",filterable:""},{default:l(()=>[t(d,{label:"平均值",value:1}),t(d,{label:"最大偏差",value:3}),t(d,{label:"最小偏差",value:2})]),_:2},1032,["modelValue","onUpdate:modelValue","style"])]),_:2},1024)):w("",!0)]),_:2},1032,["label-width","model","rules","disabled"])])])):w("",!0)],2),n.addFlag==="add"?(p(),q(U,{key:0,class:"cursor-pointer",size:"20",onClick:o=>se(re)},{default:l(()=>[t(I)]),_:2},1032,["onClick"])):w("",!0)]))),256))]),_:1},8,["height"])])}}}),je=fe(Ie,[["__scopeId","data-v-52b8288d"]]);export{je as default};
