var M=Object.defineProperty;var D=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var S=(a,e,t)=>e in a?M(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,k=(a,e)=>{for(var t in e||(e={}))N.call(e,t)&&S(a,t,e[t]);if(D)for(var t of D(e))U.call(e,t)&&S(a,t,e[t]);return a};import{d as B,l as r,P as J,n as L,ao as E,T,r as h,o as v,c as w,e as _,b as d,h as y,u as z,aN as C,y as V,ac as $,ad as A,_ as Q}from"./index-B63pSD2p.js";import{g as G}from"./plagiarizing-answer-DCON8hS9.js";import{c as K,a as X}from"./calculateTableHeight-BjE6OFD1.js";import{p as Y,g as Z,a as ee}from"./common-methods-BWkba4Bo.js";import ae from"./multi-detail-BEo7a2qA.js";import te from"./multi-judge-CWfakgGE.js";import"./test-paper-management-DjV_45YZ.js";import"./judge-copy-BAX6vJkk.js";const le=a=>($("data-v-5d9bf775"),a=a(),A(),a),oe={class:"zf-first-box"},ne={class:"zf-second-box"},re=le(()=>_("div",{style:{height:"6px",background:"#e0e2e8"}},null,-1)),ie={class:"task-btn-box"},se=["onClick"],pe=["onClick"],ue=B({name:"multi-plagiarizing",__name:"multi-plagiarizing",setup(a){const e=r(null),t=r(null),b=r(!1),f=r(!1),c=r({}),j=J({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>Y.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>i.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>i.value},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"任务名称",prop:"username3",type:"input",defaultValue:"",placeholder:"请输入任务名称",clearable:!0},{label:"考生密号",prop:"username4",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"判定状态",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定状态",optionData:()=>i.value},{label:"相似度",prop:"username",type:"selectInput",defaultValue:[0,null],leftFilterable:!0,leftPlaceholder:"请选择",leftWidth:"76px",leftClearable:!1,leftOptionData:()=>[{label:"大于",value:0},{label:"等于",value:1},{label:"小于",value:2}],rightPlaceholder:"请输入",rightWidth:"74px",rightClearable:!0},{label:"判定结果",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定结果",optionData:()=>i.value}]}),i=r([]),s=r({field:[{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px"},{prop:"ques_order",label:"题号",minWidth:"90px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"task_name",label:"任务名称",minWidth:"120px"},{prop:"stu_secret_num",label:"考生密号（参考卷）",minWidth:"160px"},{prop:"work_unit",label:"相似人数",minWidth:"120px"},{prop:"work_unit1",label:"判定状态",minWidth:"120px"},{prop:"role_name",label:"人工判定结果",minWidth:"120px",formatter:o=>"-"},{prop:"operation",label:"操作",type:"slot",minWidth:"310px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),g=r([]);let x=null;L(()=>{Z(),K(x,e.value,s.value,!1,58),m()}),E(()=>{X(x)});const O=(o,l)=>{o.prop==="project_id"&&(i.value=[],c.value.subject_id&&(c.value.subject_id=null),l&&ee(l).then(p=>{i.value=p||[]}))},m=()=>{let{currentPage:o,pageSize:l}=s.value.pageOptions,p=k({current_page:o,page_size:l},c.value);g.value=[1],G(p).then(u=>{u.code&&u.code===200||T.error(u.msg)}),g.value=[{answer_similarity_id:"as_001",answer_id:"ans_123",similarity_answer_list:[{answer_id:"ans_124",similarity:.92},{answer_id:"ans_125",similarity:.88}],subject_name:"系统架构设计师（案例分析）",paper_name:"—",ques_order:"-",ques_code:"19612348928",task_name:"案例分析第一题",stu_secret_num:"2003939999020001",region:"20039399990200011",work_unit:"9",work_unit1:"已判定"}]},W=o=>{s.value.pageOptions.pageSize=o,m()},F=o=>{s.value.pageOptions.currentPage=o,m()},P=()=>{b.value=!0},q=()=>{f.value=!0};function H(){i.value=[]}return(o,l)=>{const p=h("form-component"),u=h("el-card"),R=h("table-component");return v(),w("div",oe,[_("div",ne,[d(u,null,{default:y(()=>[_("div",{ref_key:"formDivRef",ref:e},[d(p,{ref_key:"formRef",ref:t,modelValue:c.value,"onUpdate:modelValue":l[0]||(l[0]=n=>c.value=n),"form-options":j,"is-query-btn":!0,onOnchangeFn:O,onQueryDataFn:m,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:1}),re,d(u,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:y(()=>[d(R,{minHeight:s.value.styleOptions.minHeight,"table-options":s.value,"table-data":g.value,onOnHandleSizeChange:W,onOnHandleCurrentChange:F},{operation:y(n=>[_("div",ie,[z(C)("plagiarizing-answer/multi-plagiarizing-detail")?(v(),w("span",{key:0,class:"task-btn",onClick:I=>P(n.row)},"详情",8,se)):V("",!0),z(C)("plagiarizing-answer/multi-plagiarizing-judge")?(v(),w("span",{key:1,class:"task-btn",onClick:I=>q(n.row)},"判定",8,pe)):V("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),d(ae,{isShowDialog:b.value,"onUpdate:isShowDialog":l[1]||(l[1]=n=>b.value=n)},null,8,["isShowDialog"]),d(te,{isShowDialog:f.value,"onUpdate:isShowDialog":l[2]||(l[2]=n=>f.value=n)},null,8,["isShowDialog"])])}}}),we=Q(ue,[["__scopeId","data-v-5d9bf775"]]);export{we as default};
