var q=Object.defineProperty;var D=Object.getOwnPropertySymbols;var Q=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var R=(d,s,r)=>s in d?q(d,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):d[s]=r,W=(d,s)=>{for(var r in s||(s={}))Q.call(s,r)&&R(d,r,s[r]);if(D)for(var r of D(s))I.call(s,r)&&R(d,r,s[r]);return d};import G from"./add-re-marking-DllkzCCd.js";import{f as J}from"./handleMethod-BIjqYEft.js";import{c as K,a as X}from"./calculateTableHeight-BjE6OFD1.js";import{g as Y,b as Z,d as ee,e as te,f as ae}from"./re-marking-BVznCWni.js";import{p as oe,g as ne,a as le}from"./common-methods-BWkba4Bo.js";import{t as j}from"./convertNumber-CmbNKqvY.js";import{b as se}from"./management-B6Jg-NXx.js";import{d as re,l as u,P as pe,n as ie,ao as ue,T as w,r as b,o as ce,c as de,e as c,b as p,h as i,f as g,t as _e,u as me,aV as fe,_ as be}from"./index-B63pSD2p.js";import"./index-BCxZUZMh.js";import"./test-paper-management-DjV_45YZ.js";const ge={class:"zf-first-box"},ke={class:"zf-second-box"},he={class:"zf-flex-end"},ve={class:"task-btn-box"},xe={class:"task-btn"},ye={class:"task-btn"},Ce={class:"task-btn"},we={class:"task-btn"},Oe=re({name:"re-marking",__name:"index",setup(d){const s=u([{value:1,label:"未开始"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}]),r=u(null),F=u(null),O=u(null),m=u({}),B=pe({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>oe.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>k.value},{label:"复评状态",prop:"task_state",type:"select",defaultValue:"",placeholder:"请选择复评状态",clearable:!0,optionData:()=>s.value},{label:"复评轮次",prop:"round_count",type:"select",defaultValue:"",placeholder:"请选择复评轮次",clearable:!0,optionData:()=>v.value},{label:"复评员",prop:"repeat_user_id_list",type:"select",defaultValue:"",placeholder:"请选择复评员",clearable:!0,multiple:!0,optionData:()=>S.value}]}),k=u([]),v=u([]),L=()=>{Z().then(e=>{var o,t;if(e.code==200&&e.code){let n=(o=e.data)!=null&&o.round_count?(t=e.data)==null?void 0:t.round_count:0;if(v.value=[],n>0)for(let a=0;a<n;a++)v.value.push({label:`第${j(Number(a)+1)}轮`,value:Number(a)+1})}})},S=u([]),M=e=>{ee({current_page:1,page_size:1e4,role_id:"9",system_user_type:2}).then(t=>{var n;t.code&&t.code===200&&((n=t.data.data)==null||n.forEach(a=>{a.label=a.name,a.value=a.user_id}),S.value=t.data.data)})},_=u({field:[{prop:"project_name",label:"所属资格",minWidth:"90px"},{prop:"subject_name",label:"所属科目",minWidth:"140px"},{prop:"round_count",label:"复评轮次",type:"slot",minWidth:"90px"},{prop:"example_count",label:"复评总量",minWidth:"90px"},{prop:"repeat_task_count",label:"已复评量",minWidth:"90px"},{prop:"repeat_task_progress",label:"复评进度",type:"slot",minWidth:"140px"},{prop:"task_state",label:"复评状态",minWidth:"90px",formatter:e=>J(e.task_state,s.value)},{prop:"repeat_user_name_list",label:"复评人"},{prop:"username",label:"创建人",minWidth:"100px"},{prop:"created_time",label:"创建时间",minWidth:"180px"},{prop:"operation",label:"操作",type:"slot",minWidth:"200px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),x=e=>{let o="";e.task_state==1&&(o=2),e.task_state==2&&(o=4),e.task_state==4&&(o=2),P(e,o)},P=(e,o)=>{te({repeat_task_id:e.repeat_task_id,new_state:o}).then(t=>{t.code&&t.code==200&&f()})},V=u([]);let z=null;ie(()=>{ne(),M(),L(),K(z,r.value,_.value),f()}),ue(()=>{X(z)});const f=()=>{let{currentPage:e,pageSize:o}=_.value.pageOptions,t=W({current_page:e,page_size:o,task_type:3},m.value);Y(t).then(n=>{n.code&&n.code===200?(n.data.repeat_tasks.forEach(a=>{let h=a.example_count?a.example_count:0,y=a.repeat_task_count?a.repeat_task_count:0;h==0?a.percent=0:a.percent=se(y,h)}),V.value=n.data.repeat_tasks,_.value.pageOptions.total=n.data.total):w.error(n.msg)})},H=e=>{_.value.pageOptions.currentPage=1,_.value.pageOptions.pageSize=e,f()},N=e=>{_.value.pageOptions.currentPage=e,f()},T=()=>{O.value.openDialog("add")};function E(){k.value=[]}const $=(e,o)=>{e.prop==="project_id"&&(k.value=[],m.value.subject_id&&(m.value.subject_id=null),m.value.round_id&&(m.value.round_id=null),o&&le(o).then(t=>{k.value=t||[]}))},A=e=>{fe.confirm("确定删除该复评任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let o={repeat_task_id:e.repeat_task_id};ae(o).then(t=>{t.code&&t.code===200?(w.success(t.msg),f()):w.warning(t.msg)})}).catch(()=>{})};return(e,o)=>{const t=b("form-component"),n=b("el-card"),a=b("el-button"),h=b("Auth"),y=b("el-progress"),U=b("table-component");return ce(),de("div",ge,[c("div",ke,[p(n,null,{default:i(()=>[c("div",{ref_key:"formDivRef",ref:r},[p(t,{ref_key:"formRef",ref:F,modelValue:m.value,"onUpdate:modelValue":o[0]||(o[0]=l=>m.value=l),"form-options":B,"is-query-btn":!0,onOnchangeFn:$,onQueryDataFn:f,onResetFields:E},null,8,["modelValue","form-options"])],512)]),_:1}),p(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:i(()=>[c("div",he,[p(h,{value:"re-marking/reMark"},{default:i(()=>[p(a,{type:"primary",onClick:T},{default:i(()=>[g("整卷复评")]),_:1})]),_:1})]),p(U,{minHeight:_.value.styleOptions.minHeight,"table-options":_.value,"table-data":V.value,onOnHandleSizeChange:H,onOnHandleCurrentChange:N},{round_count:i(l=>[c("span",null,"第"+_e(me(j)(l.row.round_count))+"轮",1)]),repeat_task_progress:i(l=>[p(y,{percentage:l.row.percent},null,8,["percentage"])]),operation:i(l=>[c("div",ve,[c("span",xe,[p(a,{link:"",type:"primary",disabled:l.row.task_state!==1,onClick:C=>x(l.row)},{default:i(()=>[g("开始")]),_:2},1032,["disabled","onClick"])]),c("span",ye,[p(a,{onClick:C=>x(l.row),link:"",type:"primary",disabled:l.row.task_state!==2},{default:i(()=>[g("暂停")]),_:2},1032,["onClick","disabled"])]),c("span",Ce,[p(a,{onClick:C=>x(l.row),link:"",type:"primary",disabled:l.row.task_state!==4},{default:i(()=>[g("继续")]),_:2},1032,["onClick","disabled"])]),c("span",we,[p(a,{onClick:C=>A(l.row),link:"",type:"primary",disabled:l.row.task_state!==1},{default:i(()=>[g("删除")]),_:2},1032,["onClick","disabled"])])])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),p(G,{ref_key:"addReMarkingRef",ref:O,onQueryListFn:f},null,512)])}}}),Pe=be(Oe,[["__scopeId","data-v-bb06336d"]]);export{Pe as default};
