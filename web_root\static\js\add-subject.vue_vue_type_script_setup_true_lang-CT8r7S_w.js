import{p as q}from"./common-methods-BWkba4Bo.js";import{aQ as n,aR as p,d as U,l as r,P as B,r as _,o as E,g as T,h as u,e as j,b as f,f as N,C as z,T as c}from"./index-B63pSD2p.js";const Q=l=>n.request("post",p("/v1/project/get_subject"),{data:l}),I=l=>n.request("post",p("/v1/project/create_subject"),{data:l}),J=l=>n.request("post",p("/v1/project/update_subject"),{data:l}),G=l=>n.request("post",p("/v1/project/delete_subject"),{data:l}),H=l=>n.request("post",p("/v1/project/update_subject_state"),{data:l}),L={class:"zf-dialog-first-box"},P={class:"zf-dialog-second-box"},W={class:"footer-btn"},K=U({__name:"add-subject",emits:["queryData"],setup(l,{expose:D,emit:w}){const h=w,g=r("添加科目"),i=r(!1),v=r("01"),y=r(0),d=r({}),x=B({column:3,labelWidth:"108px",itemWidth:"250px",rules:{project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_name:[{required:!0,message:"请输入科目名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,e,a)=>{if(e.length>50)return a(new Error("科目名称长度不能超过50！"));a()}}],subject_code:[{required:!0,message:"请输入科目编码",trigger:["blur","change"]}],spy_num:[{required:!0,message:"请输入份数",trigger:["blur","change"]}],subject_total_score:[{required:!0,message:"请输入总分",trigger:["blur","change"]}],subject_pass_score:[{required:!0,message:"请输入及格分",trigger:["blur","change"]}]},fields:[{label:"科目名称",prop:"subject_name",type:"input",defaultValue:"",placeholder:"请输入科目名称",clearable:!0},{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>(q.value.forEach(t=>{t.disabled=!t.is_active}),q.value)},{label:"科目编码",prop:"subject_code",type:"template",defaultValue:"",placeholder:"请输入科目编码",clearable:!0},{label:"考试模式",prop:"exam_mode",type:"radio",defaultValue:0,clearable:!0,optionData:()=>[{label:"抽参",value:0},{label:"抽卷",value:1}]},{label:"每",prop:"spy_num",type:"inputNumber",defaultValue:"",placeholder:"",clearable:!0,width:"120px",isNomal:"true",unit:"份，分发一次回评卷",min:1,max:9999,step:1,stepStrictly:!0},{label:"总分",prop:"subject_total_score",type:"inputNumber",defaultValue:"",placeholder:"请输入总分",width:"120px",clearable:!0,isNomal:"true",unit:"",min:1,max:100,step:1,stepStrictly:!0},{label:"及格分",prop:"subject_pass_score",type:"inputNumber",defaultValue:"",placeholder:"请输入及格分",width:"120px",clearable:!0,isNomal:"true",unit:"",min:1,max:100,step:1,stepStrictly:!0},{label:"评分限制",prop:"subject_score_interval",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择评分限制",optionData:()=>[{label:"允许输入1位小数",value:.1},{label:"允许输入2位小数",value:.01},{label:"只允许整数",value:1},{label:"只允许0.5倍数",value:.5}]},{label:"备注",prop:"remark",type:"textarea",defaultValue:"",placeholder:"请输入备注",clearable:!0}]}),s=r(null),b=r(!1),F=(t,e)=>{i.value=!0,v.value=t,t==="01"?(g.value="添加科目",b.value=!1):t==="02"&&(g.value="编辑科目",y.value=e.subject_id,e.subject_code?b.value=!0:b.value=!1,e.exam_mode=e.exam_mode==null||e.exam_mode==null?0:e.exam_mode,z(()=>{x.fields.map(a=>{e.hasOwnProperty(a.prop)&&s.value.setCardData(a.prop,e[a.prop])})}))},m=()=>{i.value=!1,s.value.resetFieldsFn()},C=()=>{s.value.formValidate().then(()=>{let t=JSON.parse(JSON.stringify(s.value.getAllCardData()));t.subject_total_score=Number(t.subject_total_score),t.subject_score_interval=t.subject_score_interval,t.subject_pass_score=Number(t.subject_pass_score),t.spy_num=Number(t.spy_num),v.value==="01"?k(t):v.value==="02"&&(t.subject_id=y.value,S(t))}).catch(()=>{c.warning("请按要求填写！")})},k=t=>{I(t).then(e=>{e.code&&e.code===200?(c.success(e.msg),m(),h("queryData")):c.error(e.msg)})},S=t=>{J(t).then(e=>{e.code&&e.code===200?(c.success(e.msg),m(),h("queryData")):c.error(e.msg)})};return D({openDialog:F}),(t,e)=>{const a=_("el-input"),O=_("form-component"),V=_("el-button"),R=_("el-dialog");return E(),T(R,{modelValue:i.value,"onUpdate:modelValue":e[2]||(e[2]=o=>i.value=o),title:g.value,width:"440px","align-center":"","close-on-click-modal":!1,"before-close":m,draggable:""},{footer:u(()=>[j("div",W,[f(V,{onClick:m},{default:u(()=>[N("取消")]),_:1}),f(V,{type:"primary",onClick:C},{default:u(()=>[N("确定")]),_:1})])]),default:u(()=>[j("div",L,[j("div",P,[f(O,{ref_key:"formRef",ref:s,modelValue:d.value,"onUpdate:modelValue":e[1]||(e[1]=o=>d.value=o),"form-options":x,"is-query-btn":!1,onOninputFn:t.oninputFn},{subject_code:u(()=>[f(a,{style:{width:"250px"},placeholder:"请输入科目编码",modelValue:d.value.subject_code,"onUpdate:modelValue":e[0]||(e[0]=o=>d.value.subject_code=o),disabled:b.value,clearable:""},null,8,["modelValue","disabled"])]),_:1},8,["modelValue","form-options","onOninputFn"])])])]),_:1},8,["modelValue","title"])}}});export{K as _,G as d,Q as g,H as u};
