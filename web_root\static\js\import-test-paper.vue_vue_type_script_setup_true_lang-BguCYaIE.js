var S=(V,C,p)=>new Promise((n,_)=>{var F=r=>{try{u(p.next(r))}catch(c){_(c)}},m=r=>{try{u(p.throw(r))}catch(c){_(c)}},u=r=>r.done?n(r.value):Promise.resolve(r.value).then(F,m);u((p=p.apply(V,C)).next())});import{a as U}from"./common-methods-BWkba4Bo.js";import{d as W}from"./test-paper-management-DjV_45YZ.js";import{b as G}from"./scoring-rules-BR2vQ7G3.js";import{u as I}from"./uploadRequest-IEYs8WTn.js";import{d as K,l as o,P as Q,r as w,j as X,w as Y,o as j,g as q,h as v,e as D,b as B,f as N,c as Z,y as $,C as ee,T as k}from"./index-B63pSD2p.js";const ae={class:"zf-dialog-first-box"},le={class:"zf-dialog-second-box"},te={key:0,class:"text-[var(--el-color-primary)] ml-[82px]"},re=D("p",null,"提示：该试卷已存在作答信息",-1),oe=[re],se={class:"footer-btn"},fe=K({__name:"import-test-paper",props:{projectList:Array},emits:["queryData"],setup(V,{expose:C,emit:p}){o("import-test-paper");const n=o(!1),_=p,F=V,m=o("导入试卷"),u=o(!1),r=o(!1),c=o({}),g=o([]),b=o([]),H=o({}),d=Q({column:3,labelWidth:"150px",itemWidth:"300px",rules:{project_id:[{required:!0,message:"请选择资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择科目",trigger:["blur","change"]}],mark_rule_id:[{required:!0,message:"请选择评分规则",trigger:["blur","change"]}],file:[{required:!0,message:"请选择试卷",trigger:["blur","change"]}]},fields:[{label:"资格",prop:"project_id",type:"select",placeholder:"请选择资格",defaultValue:"",clearable:!0,optionData:()=>F.projectList},{label:"科目",prop:"subject_id",type:"select",placeholder:"请选择科目",clearable:!0,defaultValue:"",optionData:()=>g.value},{label:"试卷名称",prop:"paper_name",type:"input",defaultValue:"",placeholder:"请输入试卷名称",isHidden:!0,clearable:!0},{label:"试卷编号",prop:"paper_id",type:"input",defaultValue:"",placeholder:"请输入试卷编号",isHidden:!0,clearable:!0},{label:"评分规则",prop:"mark_rule_id",type:"select",placeholder:"请选择评分规则",clearable:!0,defaultValue:"",optionData:()=>b.value},{label:"试卷选择",prop:"file",type:"upload",width:"234px",defaultValue:"",placeholder:"请选择试卷",clearable:!0},{label:"备注",prop:"remark",type:"textarea",rows:3,defaultValue:"",placeholder:"请输入备注",clearable:!0}]}),O=o(),t=o(null),R=(l,a)=>{let e={page_size:-1};e.project_id=l||t.value.getCardData("project_id"),e.subject_id=a||t.value.getCardData("subject_id"),G(e).then(s=>{var i;s.code&&s.code===200&&(b.value=(i=s.data)==null?void 0:i.data,b.value.forEach(f=>{f.label=f.rule_name,f.value=f.rule_id}))})},E=(l,a)=>{u.value=!0,l==="01"?(m.value="编辑试卷信息",r.value=!0,c.value=a,ee(()=>{d.rules.paper_name=[{required:!0,message:"请输入试卷名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(e,s,i)=>{if(s.length>100)return i(new Error("试卷名称长度不能超过100！"));i()}}],d.fields.map(e=>{e.prop==="file"?e.isHidden=!0:e.prop==="paper_name"?(e.isHidden=!1,a.lock_state===2&&(e.disabled=!0)):(e.prop==="project_id"||e.prop==="subject_id")&&a.lock_state===2&&(e.disabled=!0),a.hasOwnProperty(e.prop)&&t.value.setCardData(e.prop,a[e.prop])})}),U(a.project_id).then(e=>{g.value=e||[]}),R(a.project_id,a.subject_id)):l==="02"&&(m.value="导入试卷",r.value=!1,d.fields.map(e=>{e.prop==="file"?e.isHidden=!1:e.prop==="paper_name"&&(e.isHidden=!0)}))},h=()=>{t.value.resetFieldsFn(),u.value=!1,r.value=!1,g.value=[],d.fields.forEach(l=>{(l.prop==="project_id"||l.prop==="subject_id"||l.prop==="paper_name")&&(l.disabled=!1)})},L=(l,a)=>{l.prop==="project_id"?(T(),x(),a&&U(a).then(e=>{g.value=e||[]})):l.prop==="subject_id"&&(a&&R(),x())},T=()=>{g.value=[],t.value.getCardData("subject_id")&&(t.value.setCardData("subject_id",null),setTimeout(()=>{t.value.clearValidateFn("subject_id")},100))},x=()=>{b.value=[],t.value.getCardData("mark_rule_id")&&(t.value.setCardData("mark_rule_id",null),setTimeout(()=>{t.value.clearValidateFn("mark_rule_id")},100))},J=l=>{O.value=l,t.value.setCardData("file",l.name)},P=()=>{t.value.setCardData("file","")},z=()=>S(this,null,function*(){d.rules.paper_name=[],t.value.formValidate().then(()=>{n.value=!0;let l=JSON.parse(JSON.stringify(t.value.getAllCardData()));l.file=O.value.raw,I("post","/v1/paper/create_paper",l).then(a=>{a.code&&a.code===200?(k({message:"导入成功！",type:"success"}),_("queryData"),h()):k({message:a.msg,type:"warning"}),n.value=!1}).catch(()=>{n.value=!1})}).catch(()=>{k({type:"warning",message:"请按要求填写信息！"})})}),A=()=>{let l=JSON.parse(JSON.stringify(t.value.getAllCardData()));W(l).then(a=>{a.code&&a.code===200&&(h(),k.success("修改成功！"),_("queryData"))})};return C({openDialog:E}),(l,a)=>{const e=w("form-component"),s=w("el-button"),i=w("el-dialog"),f=X("loading");return Y((j(),q(i,{"element-loading-text":"数据导入中...",modelValue:u.value,"onUpdate:modelValue":a[1]||(a[1]=y=>u.value=y),title:m.value,"align-center":"",width:"560px","close-on-click-modal":!1,"before-close":h,draggable:""},{footer:v(()=>[D("div",se,[B(s,{onClick:h},{default:v(()=>[N("取消")]),_:1}),r.value?(j(),q(s,{key:1,type:"primary",onClick:A},{default:v(()=>[N("确定")]),_:1})):(j(),q(s,{key:0,type:"primary",onClick:z},{default:v(()=>[N("导入")]),_:1}))])]),default:v(()=>{var y;return[D("div",ae,[D("div",le,[B(e,{ref_key:"formRef",ref:t,modelValue:H.value,"onUpdate:modelValue":a[0]||(a[0]=M=>H.value=M),"form-options":d,onOnchangeFn:L,onGetUploadFile:J,onRemoveUploadFile:P,"is-query-btn":!1},null,8,["modelValue","form-options"]),((y=c.value)==null?void 0:y.lock_state)===2?(j(),Z("div",te,oe)):$("",!0)])])]}),_:1},8,["modelValue","title"])),[[f,n.value]])}}});export{fe as _};
