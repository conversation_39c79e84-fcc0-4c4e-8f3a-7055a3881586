import{c as T,u as z}from"./examinees-management-aJjscxsO.js";import{a as y}from"./common-methods-BWkba4Bo.js";import{d as J,l,P as M,n as P,r as _,o as V,g as R,h as p,e as f,b,f as w,c as U,y as W,C as A,T as s}from"./index-B63pSD2p.js";const I={class:"zf-dialog-first-box"},G={class:"zf-dialog-second-box"},H={key:0,class:"mb-[10px] text-[var(--el-color-primary)]"},K={class:"footer-btn"},Z=J({__name:"add-examines",props:["projectList"],emits:["queryData"],setup(j,{expose:D,emit:C}){const h=C,E=j,c=l("新增考生"),u=l(!1),m=l("01"),g=l({}),o=l([]),v=l({}),n=M({column:3,labelWidth:"108px",itemWidth:"250px",rules:{allow_exam_num:[{required:!0,message:"请输入准考证号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&/[^0123456789]+/g.test(e))return r(new Error("准考证号只能包含数字！"));r()}}],exam_area_code:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>15)return r(new Error("考区编号不能超过15位！"));r()}},{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&/[^0123456789]+/g.test(e))return r(new Error("考区编号只能包含数字！"));r()}}],exam_area_name:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>40)return r(new Error("考区名称不能超过40位！"));r()}}],exam_point_code:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>15)return r(new Error("考点编号不能超过15位！"));r()}},{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&/[^0123456789]+/g.test(e))return r(new Error("考点编号只能包含数字！"));r()}}],exam_point_name:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>40)return r(new Error("考点名称不能超过40位！"));r()}}],exam_room_code:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>15)return r(new Error("考场编号不能超过15位！"));r()}},{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&/[^0123456789]+/g.test(e))return r(new Error("考场编号只能包含数字！"));r()}}],exam_room_name:[{trigger:["blur","change"],validator:(t,e,r)=>{if(e&&e.length>40)return r(new Error("考场名称不能超过40位！"));r()}}],project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id_list:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}]},fields:[{label:"所属资格",prop:"project_id",type:"select",defaultValue:"",placeholder:"请选择所属资格",clearable:!0,optionData:()=>E.projectList},{label:"所属科目",prop:"subject_id_list",type:"select",defaultValue:[],placeholder:"请选择所属科目",clearable:!0,multiple:!0,optionData:()=>o.value},{label:"准考证号",prop:"allow_exam_num",type:"input",defaultValue:"",placeholder:"请输入准考证号",clearable:!0},{label:"考区编号",prop:"exam_area_code",type:"input",defaultValue:"",placeholder:"请输入考区编号",clearable:!0},{label:"考区名称",prop:"exam_area_name",type:"input",defaultValue:"",placeholder:"请输入考区名称",clearable:!0},{label:"考点编号",prop:"exam_point_code",type:"input",defaultValue:"",placeholder:"请输入考点编号",clearable:!0},{label:"考点名称",prop:"exam_point_name",type:"input",defaultValue:"",placeholder:"请输入考点名称",clearable:!0},{label:"考场编号",prop:"exam_room_code",type:"input",defaultValue:"",placeholder:"请输入考场编号",clearable:!0},{label:"考场名称",prop:"exam_room_name",type:"input",defaultValue:"",placeholder:"请输入考场名称",clearable:!0}]}),a=l(null);P(()=>{});const F=(t,e)=>{u.value=!0,m.value=t,t==="01"?c.value="新增考生":t==="02"&&(c.value="编辑考生信息",g.value=e,y(e.project_id).then(r=>{o.value=r}),A(()=>{n.fields.map(r=>{e.hasOwnProperty(r.prop)&&a.value.setCardData(r.prop,e[r.prop]),r.prop==="allow_exam_num"&&(r.disabled=!0)}),a.value.setCardData("subject_id_list",[e.subject_id])})),(e==null?void 0:e.from_tool)===1&&(n.disabled=!0)},i=()=>{u.value=!1,n.fields.map(t=>{t.prop==="allow_exam_num"&&(t.disabled=!1)}),a.value.resetFieldsFn(),n.disabled=!1},q=(t,e)=>{t.prop==="project_id"&&(e?(o.value=[],a.value.getCardData("subject_id")&&a.value.setCardData("subject_id",null),y(e).then(r=>{o.value=r||[]})):(o.value=[],a.value.getCardData("subject_id")&&a.value.setCardData("subject_id",null)))},N=()=>{a.value.formValidate().then(()=>{let t=JSON.parse(JSON.stringify(a.value.getAllCardData()));m.value==="01"?O(t):m.value==="02"&&(t.stu_id=g.value.stu_id,S(t))}).catch(()=>{s.warning("请按要求填写！")})},O=t=>{T(t).then(e=>{e.code&&e.code===200?(s.success(e.msg),h("queryData"),i()):s.warning(e.msg)})},S=t=>{z(t).then(e=>{e.code&&e.code===200?(s.success(e.msg),h("queryData"),i()):s.warning(e.msg)})};return D({openDialog:F}),(t,e)=>{const r=_("form-component"),x=_("el-button"),B=_("el-dialog");return V(),R(B,{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=d=>u.value=d),title:c.value,width:"440px","align-center":"","before-close":i,"close-on-click-modal":!1,draggable:""},{footer:p(()=>[f("div",K,[b(x,{onClick:i},{default:p(()=>[w("取消")]),_:1}),b(x,{type:"primary",onClick:N},{default:p(()=>[w("确定")]),_:1})])]),default:p(()=>{var d;return[f("div",I,[f("div",G,[((d=g.value)==null?void 0:d.from_tool)===1?(V(),U("div",H,"提示：数据来源于导入工具，不允许编辑 ")):W("",!0),b(r,{ref_key:"formRef",ref:a,modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=L=>v.value=L),"form-options":n,"is-query-btn":!1,onOnchangeFn:q},null,8,["modelValue","form-options"])])])]}),_:1},8,["modelValue","title"])}}});export{Z as _};
