var B=(I,V,m)=>new Promise((a,f)=>{var C=u=>{try{r(m.next(u))}catch(i){f(i)}},k=u=>{try{r(m.throw(u))}catch(i){f(i)}},r=u=>u.done?a(u.value):Promise.resolve(u.value).then(C,k);r((m=m.apply(I,V)).next())});import{M as S,l as le}from"./rule-BOjT6TPp.js";import{c as ne,b as te,a as re,_ as ue,l as ie}from"./logo-DT3YnI87.js";import{d as K,m as X,l as y,n as Y,ao as de,g as ee,h as t,r as b,o as R,e as _,b as l,f as q,T as U,Q as x,aD as ce,_ as oe,x as pe,P as _e,a2 as me,c as fe,u as p,i as ge,aE as he,aF as P,$ as we,aG as F,aa as Q,t as A,y as ve,aH as ye}from"./index-B63pSD2p.js";const be={class:"form_wrapper"},Ce=K({__name:"init-userinfo",props:{modelValue:{type:Boolean,required:!0,default:!1},title:{type:String,required:!0,default:""},ZIndex:{required:!1,type:Number,default:2001},loginUserInfo:{required:!1,type:Object,default:null}},emits:["sureCallBack"],setup(I,{emit:V}){const m=I,a=V,f=X({get:()=>m.modelValue,set:i=>a("update:modelValue",i)}),C=()=>{f.value=!1,a("closeDialog")},k=()=>B(this,null,function*(){var n;if(yield r==null?void 0:r.value.IsFormValid()){let g=((n=r.value)==null?void 0:n.formValue)||{};if(g.new_password!=g.sure_new_password)return U.warning("新密码和确认密码必须相同");let d=JSON.parse(JSON.stringify(g));d.new_password=x(d.new_password),d.sure_new_password=x(d.sure_new_password),ce(d).then(w=>{var v;if(w.code==200)U.success(w.msg),a("sureCallBack",d);else return U.error((v=w.msg)!=null?v:"后端接口异常")})}}),r=y(null),u=y([{prop:"user_id"},{label:"姓名",type:"input",placeholder:"请输入姓名",required:!0,prop:"user_real_name"},{label:"新密码",type:"passWord",placeholder:"请输入密码",required:!0,prop:"new_password"},{label:"确认密码",type:"passWord",placeholder:"请输入确认密码",required:!0,prop:"sure_new_password"},{label:"身份证号",type:"input",placeholder:"请输入身份证号",required:!1,prop:"user_ID_number"},{label:"手机号",type:"input",placeholder:"请输入手机号",required:!1,prop:"user_phone_number"}]);return Y(()=>{}),de(()=>{}),(i,n)=>{const g=b("DynamicFormComponent"),d=b("el-button"),w=b("DialogComponent");return R(),ee(w,{isShowDialog:f.value,width:"500px",showClose:!1,onCloseDialog:C,onSure:i.saveUserInfo,beforeClose:C,title:"修改信息",fullscreen:!1},{content:t(()=>[_("div",be,[l(g,{list:u.value,editEntity:I.loginUserInfo,ref_key:"formFilterRef",ref:r},null,8,["list","editEntity"])])]),footer:t(()=>[l(d,{type:"primary",onClick:k},{default:t(()=>[q("确认")]),_:1})]),_:1},8,["isShowDialog","onSure"])}}}),Ie=oe(Ce,[["__scopeId","data-v-d087f882"]]),Ve="/static/png/login-DL6ziCZb.png",Se={class:"login-total-box"},ke={class:"login-left-box"},De=["src"],Ue={class:"login-right-box"},xe={class:"login-sys-tit-box"},Ne=["src"],Be={class:"login-sys-tit"},Fe={class:"welcome-user-box"},Re={class:"welcome-sub-tit"},qe=K({name:"Login",__name:"index",setup(I){const V=X(()=>Q().currentComponents),m=ge(),a=y(!1),f=y(),{initStorage:C}=ne();C();const{dataTheme:k,dataThemeChange:r}=te();r();const{title:u,subTitle:i}=pe(),n=_e({username:"",password:""});re();const g=y(!1);function d(){g.value=!0}function w(){g.value=!1}const v=y(!1),T=y(null);let O=null;const ae=e=>{a.value=!1,v.value=!1,E(O,e)},E=(e,s)=>{var c;s&&e.data&&e.data.user_info&&(e.data.user_info.id_card=s.user_ID_number,e.data.user_info.phone_number=s.user_phone_number,e.data.user_info.name=s.user_real_name),(c=e.data)!=null&&c.role_data?(sessionStorage.setItem("roleInfo",JSON.stringify(e.data.role_data)),d(),a.value=!1):(he(e.data),P().then(h=>{m.push(we(!0).path),F("登录成功",{type:"success"})}))},J=(e,s)=>B(this,null,function*(){a.value=!0,e&&(yield e.validate((c,h)=>{if(c){let D=x(JSON.parse(JSON.stringify(n.password))),N={username:n.username,password:D};a.value=!0,ye(N).then(o=>{var M,$,z,H,W,Z,j,G;o.code&&o.code===200?o.data.already_login?E(o,null):(T.value={user_id:($=(M=o.data)==null?void 0:M.user_info)==null?void 0:$.user_id,user_real_name:(H=(z=o.data)==null?void 0:z.user_info)==null?void 0:H.name,new_password:"",sure_new_password:"",user_ID_number:(Z=(W=o.data)==null?void 0:W.user_info)==null?void 0:Z.id_card,user_phone_number:(G=(j=o.data)==null?void 0:j.user_info)==null?void 0:G.phone_number},v.value=!0,O=o):(U.warning(o.msg),a.value=!1)}).catch(()=>{a.value=!1})}else return a.value=!1,h}))});function se(e){let s=x(JSON.parse(JSON.stringify(n.password))),c={username:n.username,password:s,role_id_list:[]};c.role_id_list.push(e),Q().loginByUsername(c).then(h=>{h.code&&h.code===200?P().then(D=>{m.push("/welcome"),F("登录成功",{type:"success"})}):(F(h.msg,{type:"error"}),a.value=!1)}).catch(()=>{a.value=!1})}function L({code:e}){e==="Enter"&&J(f.value)}return Y(()=>{window.document.addEventListener("keypress",L)}),me(()=>{window.document.removeEventListener("keypress",L)}),(e,s)=>{const c=b("el-input"),h=b("el-form-item"),D=b("el-button"),N=b("el-form");return R(),fe("div",Se,[_("div",ke,[_("img",{src:p(Ve),class:"login-wave"},null,8,De)]),_("div",Ue,[l(p(S),null,{default:t(()=>[_("div",xe,[_("img",{src:p(ie),alt:"logo"},null,8,Ne),_("div",Be,A(p(u)),1)])]),_:1}),l(p(S),null,{default:t(()=>[_("div",Fe,[q("欢迎登录 | "),_("span",Re,A(p(i)),1)]),_("div",null,[V.value===0?(R(),ee(N,{key:0,ref_key:"ruleFormRef",ref:f,model:n,rules:p(le),size:"large"},{default:t(()=>[l(p(S),{delay:100},{default:t(()=>[l(h,{rules:[{required:!0,message:"请输入账号",trigger:"blur"}],prop:"username"},{default:t(()=>[l(c,{modelValue:n.username,"onUpdate:modelValue":s[0]||(s[0]=o=>n.username=o),clearable:"",placeholder:"账号"},null,8,["modelValue"])]),_:1})]),_:1}),l(p(S),{delay:150},{default:t(()=>[l(h,{prop:"password"},{default:t(()=>[l(c,{modelValue:n.password,"onUpdate:modelValue":s[1]||(s[1]=o=>n.password=o),clearable:"","show-password":"",placeholder:"密码"},null,8,["modelValue"])]),_:1})]),_:1}),l(p(S),{delay:250},{default:t(()=>[l(D,{class:"w-full mt-4",size:"default",type:"primary",loading:a.value,onClick:s[2]||(s[2]=o=>J(f.value))},{default:t(()=>[q(" 登录 ")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])):ve("",!0)])]),_:1})]),l(ue,{show:g.value,onCloseChangeRole:w,onRoleLogin:se},null,8,["show"]),l(Ie,{modelValue:v.value,"onUpdate:modelValue":s[3]||(s[3]=o=>v.value=o),loginUserInfo:T.value,onSureCallBack:ae},null,8,["modelValue","loginUserInfo"])])}}}),Le=oe(qe,[["__scopeId","data-v-585648fc"]]);export{Le as default};
