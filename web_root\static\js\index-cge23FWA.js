import{aQ as L,aR as N,d as X,i as Y,l as p,P as Z,n as ee,ao as te,T as x,r as u,o as y,c as ae,e as O,b as s,h as n,f as m,u as S,aN as j,g as V,y as H,aV as oe,_ as le}from"./index-B63pSD2p.js";import{g as ne}from"./test-paper-management-DjV_45YZ.js";import{p as re,g as se,a as ie}from"./common-methods-BWkba4Bo.js";import{q as pe,g as ue}from"./rules-form-CST-rV3v.js";import{c as ce,a as de}from"./calculateTableHeight-BjE6OFD1.js";import"./scoring-rules-BR2vQ7G3.js";const _e=(f,C=!0)=>L.request("post",N("/v1/ai_mark/get_ai_mark_list"),{data:f},{},C),me=f=>L.request("post",N("/v1/ai_mark/start_ai_mark"),{data:f}),fe={class:"zf-first-box"},he={class:"zf-second-box"},be={class:"flex justify-end mb-[10px]"},ge=X({name:"ai-rate",__name:"index",setup(f){const C=Y(),W=p(null),r=p(null),h=p([]),b=p([]),z=p({}),P=Z({column:3,labelWidth:"68px",itemWidth:"240px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择科目",optionData:()=>re.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>h.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>b.value},{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!0,clearable:!0,optionData:()=>pe},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"评分状态",prop:"mark_state_list",type:"select",defaultValue:[],placeholder:"请选择评分状态",clearable:!0,multiple:!0,optionData:()=>[{label:"未评分",value:1},{label:"评分中",value:2},{label:"已评分",value:3},{label:"已暂停",value:4},{label:"已取消",value:5}]}]}),c=p({field:[{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"paper_name",label:"试卷",minWidth:"120px"},{prop:"ques_type_name",label:"题型",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"ques_order",label:"试题序号",minWidth:"120px"},{prop:"mark_state_str",label:"评分状态",minWidth:"100px"},{prop:"progress_count",label:"评分情况",minWidth:"100px"},{prop:"progress",label:"评分进度",minWidth:"160px",type:"progress",showOverflowTooltip:!0},{prop:"start_time",label:"开始评分时间",minWidth:"160px",sortable:!0},{prop:"end_time",label:"评分结束时间",minWidth:"160px",sortable:!0},{prop:"opera",label:"操作",type:"slot",fixed:"right",minWidth:"158px",showOverflowTooltip:!0}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),w=p([]),k=p([]);let g=null,F=null;ee(()=>{ce(F,W.value,c.value),se(),ue(),d()}),te(()=>{de(F),T()});const E=(t,a)=>{t.prop==="project_id"?(h.value=[],b.value=[],r.value.getCardData("subject_id")&&r.value.setCardData("subject_id",null),r.value.getCardData("paper_id")&&r.value.setCardData("paper_id",null),a&&ie(a).then(l=>{h.value=l||[]})):t.prop==="subject_id"&&(b.value=[],r.value.getCardData("paper_id")&&r.value.setCardData("paper_id",null),a&&A())},A=()=>{const{project_id:t,subject_id:a}=r.value.getAllCardData();ne({project_id:t,subject_id:a,page_size:-1}).then(o=>{o.code&&o.code===200&&(o.data.data.forEach(e=>{e.label=e.paper_name,e.value=e.paper_id}),b.value=o.data.data)})},d=t=>{let a=JSON.parse(JSON.stringify(r.value.getAllCardData()));a.ques_type_code_list.length||delete a.ques_type_code_list;let{currentPage:l,pageSize:o}=c.value.pageOptions;a.current_page=l,a.page_size=o,_e(a,!t).then(e=>{e.code&&e.code===200?(e.data.is_refresh===0?T():e.data.is_refresh===1&&M(),t==="auto"?w.value.forEach((_,D)=>{["mark_state","mark_state_str","progress_count","progress","end_time"].forEach(v=>{_[v]=e.data.data[D][v]})}):w.value=e.data.data,c.value.pageOptions.total=e.data.total):x.warning(e.msg)})},q=(t,a)=>{let l="";t==="start"?l="确定开始评分吗？":t==="restart"?l="确定重新评分吗？":t==="batch"&&(l="确定批量开始评分吗？"),oe.confirm(l,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let o={ques_info:[]};if(["start","restart"].includes(t)){const{paper_id:e,ques_id:_}=a;o.ques_info=[{paper_id:e,ques_id:_}]}else["batch"].includes(t)&&(o.ques_info=k.value.map(e=>({paper_id:e.paper_id,ques_id:e.ques_id})));me(o).then(e=>{e.code&&e.code===200?(x.success(e.msg),d()):x.warning(e.msg)})}).catch(()=>{})},I=t=>{if(!k.value.length){x.warning("至少选择一条数据！");return}t==="start"&&q("batch")},M=()=>{g||(g=setInterval(()=>{d("auto")},2e4))},T=()=>{g&&clearInterval(g)},Q=t=>{C.push({path:"/grade-management/score-result/index"})},U=t=>{k.value=t},$=t=>{c.value.pageOptions.pageSize=t,d()},J=t=>{c.value.pageOptions.currentPage=t,d()};function G(){h.value=[]}return(t,a)=>{const l=u("form-component"),o=u("el-card"),e=u("el-button"),_=u("el-dropdown-item"),D=u("el-dropdown-menu"),B=u("el-dropdown"),v=u("Auth"),K=u("table-component");return y(),ae("div",fe,[O("div",he,[s(o,null,{default:n(()=>[O("div",{ref_key:"formDivRef",ref:W,class:"query-box"},[s(l,{ref_key:"formRef",ref:r,modelValue:z.value,"onUpdate:modelValue":a[0]||(a[0]=i=>z.value=i),"form-options":P,"is-query-btn":!0,onQueryDataFn:d,onOnchangeFn:E,onResetFields:G},null,8,["modelValue","form-options"])],512)]),_:1}),s(o,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:n(()=>[s(v,{value:"ai-rate/batch"},{default:n(()=>[O("div",be,[s(B,{onCommand:I},{dropdown:n(()=>[s(D,null,{default:n(()=>[s(_,{command:"start"},{default:n(()=>[m("批量开始")]),_:1})]),_:1})]),default:n(()=>[s(e,{type:"primary"},{default:n(()=>[m(" 批量评分 ")]),_:1})]),_:1})])]),_:1}),s(K,{"min-height":c.value.styleOptions.minHeight,"table-options":c.value,"table-data":w.value,onOnHandleSelectionChange:U,onOnHandleSizeChange:$,onOnHandleCurrentChange:J},{opera:n(i=>[S(j)("ai-rate/start")&&i.row.mark_state===1?(y(),V(e,{key:0,type:"text",class:"text-btn",onClick:R=>q("start",i.row)},{default:n(()=>[m("开始评分 ")]),_:2},1032,["onClick"])):S(j)("ai-rate/restart")&&i.row.mark_state===3?(y(),V(e,{key:1,type:"text",class:"text-btn",onClick:R=>q("restart",i.row)},{default:n(()=>[m("重新评分 ")]),_:2},1032,["onClick"])):H("",!0),S(j)("ai-rate/result")&&i.row.mark_state!==1?(y(),V(e,{key:2,type:"text",class:"text-btn",onClick:R=>Q(i.row)},{default:n(()=>[m("评分结果 ")]),_:2},1032,["onClick"])):H("",!0)]),_:1},8,["min-height","table-options","table-data"])]),_:1})])])}}}),qe=le(ge,[["__scopeId","data-v-a646bd10"]]);export{qe as default};
