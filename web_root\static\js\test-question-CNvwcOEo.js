import P from"./question-info-CiM3dtIw.js";import{h as U}from"./handleImages-D-nd439N.js";import{d as X,aS as B,l as m,aO as Y,m as Z,aa as ee,B as te,C as le,r as v,o as s,c as n,b as d,e as k,h as q,t as I,y as h,u as N,F as ae,p as se,g as O,f as ue,_ as ne}from"./index-B63pSD2p.js";import"./op-mark-step-DW83lcNi.js";import"./manual-B17ad5iK.js";const oe={key:0,class:"empty-test-box"},ie={key:1,class:"ques-marking-box"},re={key:0},de={key:0,class:"ques-inline-box"},ce={key:0,class:"whitespace-nowrap"},fe=["innerHTML"],me={key:1,class:"ques-inline-box"},qe={key:0},_e=["innerHTML"],ve={key:1},he={key:0,class:"ques-inline-box"},De={key:0,class:"whitespace-nowrap"},ke=["innerHTML"],ge={key:1,class:"ques-inline-box"},xe={key:0},ye=["innerHTML"],be=X({name:"test-question",__name:"test-question",props:B({quesDetail:{type:Object,default:()=>{}},maxHeight:{type:Number,default:580}},{currentSmallQuestion:{},currentSmallQuestionModifiers:{}}),emits:B(["changeCurIndex"],["update:currentSmallQuestion"]),setup(e,{expose:j,emit:z}){const i=e,w=z,g=m(null),T=m(null),R=m(null);m(""),m(["1","2","3","4"]);const Q=m([]),c=m(""),f=m([]),r=Y(e,"currentSmallQuestion"),V=Z(()=>{var a;return(a=ee())==null?void 0:a.roles}),p=m([]);te(()=>i.quesDetail,()=>{var u,H;p.value=[],f.value=[];let a=!1;(H=(u=i.quesDetail)==null?void 0:u.children)==null||H.map((l,o)=>{V.value[0]==="3"?(l.expert_mark_score===null&&l.final_mark_score===null||l.mark_state===6)&&(f.value.push(l.ques_order),a||(c.value=i.quesDetail.children[o].ques_order,r.value=i.quesDetail.children[o],a=!0)):V.value[0]==="4"?l.mark_state===2&&(f.value.push(l.ques_order),a||(c.value=i.quesDetail.children[o].ques_order,r.value=i.quesDetail.children[o],a=!0)):V.value[0]==="5"&&l.mark_state===4&&(f.value.push(l.ques_order),a||(c.value=i.quesDetail.children[o].ques_order,r.value=i.quesDetail.children[o],a=!0)),p.value.push(l.ques_order)}),le(()=>{let l=null;T.value?l=T.value:l=R.value;const o=l.wrapRef;l.setScrollTop(o.scrollHeight)})});const A=()=>g.value.getMarkParseFn(),E=()=>{g.value.setMarkParseFn()},$=()=>{g.value.clearMarkParseFn()},G=(a,u)=>{c.value=a,r.value=i.quesDetail.children[u],w("changeCurIndex")};return j({getMarkParseFn:A,setMarkParseFn:E,clearMarkParseFn:$,autoChangeComIndex:()=>{f.value=f.value.filter(a=>a.toString()!==c.value.toString()),c.value=f.value[0],r.value=i.quesDetail.children.filter(a=>a.ques_order===c.value)[0],w("changeCurIndex")},unMarkQuestion:f}),(a,u)=>{var F;const H=v("el-empty"),l=v("el-collapse-item"),o=v("el-collapse"),J=v("el-radio-button"),K=v("el-badge"),W=v("el-radio-group"),L=v("el-scrollbar");return s(),n("div",null,[!e.quesDetail||((F=Object.keys(e.quesDetail))==null?void 0:F.length)===0?(s(),n("div",oe,[d(H,{description:"暂无任务"})])):(s(),n("div",ie,[e.quesDetail.f_ques_desc&&!e.quesDetail.is_f_type?(s(),n("div",re,[k("div",null,[d(L,{"max-height":e.maxHeight,always:"",ref_key:"fScrollbarRef",ref:T},{default:q(()=>[d(o,{modelValue:Q.value,"onUpdate:modelValue":u[0]||(u[0]=t=>Q.value=t)},{default:q(()=>[d(l,{title:"试题材料",name:"1"},{default:q(()=>{var t,_,D,x,y,b,S,M,C;return[(t=e.quesDetail)!=null&&t.f_ques_desc.html?(s(),n("div",de,[(_=e.quesDetail)!=null&&_.f_ques_order?(s(),n("div",ce,I((D=e.quesDetail)==null?void 0:D.f_ques_order)+"．",1)):h("",!0),k("div",{innerHTML:N(U)((y=(x=e.quesDetail)==null?void 0:x.f_ques_desc)==null?void 0:y.html)},null,8,fe)])):(s(),n("div",me,[(b=e.quesDetail)!=null&&b.f_ques_order?(s(),n("div",qe,I((S=e.quesDetail)==null?void 0:S.f_ques_order)+"．",1)):h("",!0),k("div",{innerHTML:(C=(M=e.quesDetail)==null?void 0:M.f_ques_desc)==null?void 0:C.text},null,8,_e)]))]}),_:1})]),_:1},8,["modelValue"]),k("div",null,[d(W,{modelValue:c.value,"onUpdate:modelValue":u[1]||(u[1]=t=>c.value=t),size:"small",class:"mb-[5px]"},{default:q(()=>[(s(!0),n(ae,null,se(p.value,(t,_)=>(s(),O(K,{class:"!mr[30px] !mt-[10px]","is-dot":f.value.includes(t),offset:[10,5]},{default:q(()=>[d(J,{value:t,onClick:D=>G(t,_)},{default:q(()=>[ue(I(t),1)]),_:2},1032,["value","onClick"])]),_:2},1032,["is-dot"]))),256))]),_:1},8,["modelValue"]),d(P,{ref_key:"questionInfoRef",ref:g,currentSmallQuestion:r.value,"onUpdate:currentSmallQuestion":u[2]||(u[2]=t=>r.value=t),quesDetail:r.value},null,8,["currentSmallQuestion","quesDetail"])])]),_:1},8,["max-height"])])])):h("",!0),e.quesDetail.is_f_type&&e.quesDetail.f_ques_desc||!e.quesDetail.f_ques_desc?(s(),n("div",ve,[d(L,{"max-height":e.maxHeight,always:"",ref_key:"scrollbarRef",ref:R},{default:q(()=>[e.quesDetail.is_f_type&&e.quesDetail.f_ques_desc?(s(),O(o,{key:0,modelValue:Q.value,"onUpdate:modelValue":u[3]||(u[3]=t=>Q.value=t)},{default:q(()=>[d(l,{title:"试题材料",name:"1"},{default:q(()=>{var t,_,D,x,y,b,S,M,C;return[(t=e.quesDetail)!=null&&t.f_ques_desc.html?(s(),n("div",he,[(_=e.quesDetail)!=null&&_.f_ques_order?(s(),n("div",De,I((D=e.quesDetail)==null?void 0:D.f_ques_order)+"．",1)):h("",!0),k("div",{innerHTML:N(U)((y=(x=e.quesDetail)==null?void 0:x.f_ques_desc)==null?void 0:y.html)},null,8,ke)])):(s(),n("div",ge,[(b=e.quesDetail)!=null&&b.f_ques_order?(s(),n("div",xe,I((S=e.quesDetail)==null?void 0:S.f_ques_order)+"．",1)):h("",!0),k("div",{innerHTML:(C=(M=e.quesDetail)==null?void 0:M.f_ques_desc)==null?void 0:C.text},null,8,ye)]))]}),_:1})]),_:1},8,["modelValue"])):h("",!0),d(P,{ref_key:"questionInfoRef",ref:g,currentSmallQuestion:r.value,"onUpdate:currentSmallQuestion":u[4]||(u[4]=t=>r.value=t),quesDetail:e.quesDetail},null,8,["currentSmallQuestion","quesDetail"])]),_:1},8,["max-height"])])):h("",!0)]))])}}}),Te=ne(be,[["__scopeId","data-v-a7e3cc9b"]]);export{Te as default};
