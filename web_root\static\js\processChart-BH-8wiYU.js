import"./index-C9GYnvBh.js";import{l as i,n as s,a2 as c,B as d,o as f,c as h,dk as u}from"./index-B63pSD2p.js";const p={__name:"processChart",props:{data:{type:Number,default:0},fullScreen:{type:Boolean,default:!1},hiddenLegend:{type:Boolean,default:!1},legendData:{type:Array,default:[]}},setup(r,{expose:o}){const e=r,l=i(null);let t=null;function a(){t||(t=u(l.value)),t.setOption({color:["#1684fc","#e8e8e8"],tooltip:{trigger:"item",show:!0,formatter:"{c}%"},legend:{show:!e.hiddenLegend,orient:"bottom",left:"right",top:"center",itemWidth:e.fullScreen?50:25,itemHeight:e.fullScreen?28:14,textStyle:{fontSize:e.fullScreen?24:12}},title:{show:!0,text:`${e.data}%`,y:"center",padding:[0,0,0,0],textAlign:"center",left:e.hiddenLegend?"50%":"32%",textStyle:{fontSize:e.fullScreen?36:16,fontWeight:400,color:"black"}},series:[{name:"数据",type:"pie",radius:["66%","96%"],center:[e.hiddenLegend?"50%":"32%","50%"],label:{show:!1},labelLine:{show:!1},data:[{value:e.data,name:e.legendData[0],emphasis:{scale:0,itemStyle:{color:"#1684fccc"}}},{value:100-e.data,name:e.legendData[1],emphasis:{scale:0,itemStyle:{color:"#e8e8e899"}}}]}]})}s(()=>{a(),window.addEventListener("resize",n)}),c(()=>{t&&t.dispose(),window.removeEventListener("resize",n)});function n(){t&&t.resize()}return d([()=>e.data,()=>e.legendData],a,{deep:!0}),o({renderChart:a,resizeChart:n}),(m,g)=>(f(),h("div",{ref_key:"chartRef",ref:l,style:{width:"100%",height:"100%"}},null,512))}},S=p;export{S as P};
