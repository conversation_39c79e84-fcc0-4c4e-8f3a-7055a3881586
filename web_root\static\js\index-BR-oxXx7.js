import{g as ee}from"./test-paper-management-DjV_45YZ.js";import ae from"./manual-scoring-DuUqn2Bj.js";import te from"./progress-bar-DwHuQJ3k.js";import{d as le,l as o,P as re,aN as ne,n as oe,ao as se,T as h,r as d,o as pe,c as ie,e as v,b as s,h as c,f as ue,u as de,aV as ce,ac as _e,ad as me,_ as fe}from"./index-B63pSD2p.js";import{d as P}from"./validate-Dc6ka3px.js";import{p as ge,g as he,a as be}from"./common-methods-BWkba4Bo.js";import{q as ve,g as we}from"./rules-form-CST-rV3v.js";import{b as xe,c as ye,i as De,s as ke}from"./intelligent-marking-CyZREtXG.js";import"./handleImages-D-nd439N.js";import"./fullscreen-exit-line-DVwpkItP.js";import"./scoring-rules-BR2vQ7G3.js";const Se=w=>(_e("data-v-45bfd1e5"),w=w(),me(),w),Ce={class:"zf-first-box"},Re={class:"zf-second-box"},Ve={class:"flex"},Oe=Se(()=>v("span",{class:"mr-[3px] ml-[3px]"},"-",-1)),Be={class:"flex justify-end mb-[10px]"},Fe=le({name:"intelligent-marking",__name:"index",setup(w){const F=o(null),W=o(null),V=o(null),i=o(null),j=o(null),_=o({rules:{minRange:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&!P(a))return t(new Error("请输入数字！"));t()}}],maxRange:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&!P(a))return t(new Error("请输入数字！"));t()}}]},scoreData:{maxRange:"",minRange:""}}),x=o([]),y=o([]),I=re({column:3,labelWidth:"70px",itemWidth:"215px",rules:{ques_code:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>30)return t(new Error("试题编号长度不能超过30！"));t()}}]},fields:[{label:"项目",prop:"project_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择项目",optionData:()=>ge.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>x.value},{label:"试卷名称",prop:"paper_code",type:"select",defaultValue:"",placeholder:"请选择试卷名称",clearable:!0,optionData:()=>y.value},{label:"试题类型",prop:"ques_type_code",type:"select",placeholder:"请选择试题类型",defaultValue:"",clearable:!0,optionData:()=>ve},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"试题序号",prop:"ques_order",type:"input",defaultValue:"",placeholder:"请输入试题序号",clearable:!0},{label:"分组id",prop:"same_answer_group_id",type:"input",defaultValue:"",placeholder:"请输入分组id",clearable:!0},{label:"考生答案",prop:"stu_answer",type:"input",defaultValue:"",placeholder:"请输入考生答案",clearable:!0},{label:"考生得分",prop:"stu_score",type:"template",defaultValue:"",placeholder:"请输入考生答案",clearable:!0},{label:"判断结果",prop:"mark_result",type:"select",defaultValue:null,placeholder:"请选择判断结果",clearable:!0,optionData:()=>[{label:"正确",value:1},{label:"错误",value:2},{label:"部分正确",value:3}]},{label:"评分状态",prop:"mark_state",type:"select",clearable:!0,defaultValue:[],multiple:!0,placeholder:"请选择评分状态",optionData:()=>[{label:"未评分",value:1},{label:"评分成功",value:2},{label:"评分失败",value:3},{label:"作答答案待人工判断",value:4}]},{label:"评分时间",prop:"search_time",type:"datetimerange",valueFormat:"YYYY-MM-DD HH:mm:ss",clearable:!0,defaultValue:"",optionData:()=>[]}]}),u=o({field:[{prop:"same_answer_group_id",label:"分组ID",minWidth:"160px"},{prop:"subject_name",label:"科目",minWidth:"80px"},{prop:"paper_name",label:"试卷名称",minWidth:"120px"},{prop:"ques_type_name",label:"试题类型",minWidth:"120px"},{prop:"ques_order",label:"试题序号",minWidth:"110px",sortable:!0},{prop:"ques_code",label:"试题编号",minWidth:"180px"},{prop:"standard_answer_format",label:"参考答案",minWidth:"240px"},{prop:"stu_answer_format",label:"考生答案",minWidth:"180px"},{prop:"same_answer_count",label:"考生数量",minWidth:"90px"},{prop:"answer_parse",label:"评分解析",minWidth:"120px"},{prop:"total_score",label:"试题分数",minWidth:"90px"},{prop:"stu_score",label:"考生得分",minWidth:"100px"},{prop:"mark_role_format",label:"评分角色",minWidth:"120px"},{prop:"mark_state",label:"阅卷状态",minWidth:"100px"},{prop:"updated_time",label:"评分时间",minWidth:"170px"},{prop:"",label:"操作",type:"template",minWidth:"100px",fixed:"right",templateGroup:[{title:()=>ne("intelligent-marking/manual")?"评分详情":"",clickBtn(e){F.value.openDialog(e)}}]}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let b=o([]),D=o([]);const N=o(0),E=o(1),m=o(1e3);let k=[],f=0,O=null;oe(()=>{u.value.styleOptions.minHeight=window.innerHeight-276-V.value.clientHeight+"px",window.addEventListener("resize",q),he(),we(),g()});const q=()=>{var e;u.value.styleOptions.minHeight=window.innerHeight-276-((e=V.value)==null?void 0:e.clientHeight)+"px"};se(()=>{window.removeEventListener("resize",q)});const g=()=>{j.value.validate(e=>{e?(M(),k=[]):h.warning("请按规则填写！")})},A=()=>{let e=[];const{minRange:a,maxRange:t}=_.value.scoreData;return(a||t)&&(a?t?Number(a)>Number(t)?e=[t,a]:e=[a,t]:e=[a,a]:e=[t,t]),e},M=()=>{let e=JSON.parse(JSON.stringify(i.value.getAllCardData())),{currentPage:a,pageSize:t}=u.value.pageOptions;e.current_page=a,e.page_size=t,e.stu_score_range=A(),e.mark_result&&(e.mark_result=Number(e.mark_result)),xe(e).then(r=>{var n;r.code&&r.code===200?((n=r.data)==null?void 0:n.data.length)>0?(r.data.data.map(l=>{var p,S,C,R;l.ques_type_code==="D"?(l.standard_answer_format=H(l.standard_answer),l.stu_answer_format=H(l.stu_answer)):l.ques_type_code==="B"?(l.standard_answer_format=((p=l.standard_answer)==null?void 0:p[0])==="1"?["正确"]:["错误"],l.stu_answer&&(l.stu_answer_format=((S=l.stu_answer)==null?void 0:S[0])==="1"?["正确"]:["错误"])):(l.standard_answer_format=l.standard_answer,l.stu_answer_format=l.stu_answer),l.standard_answer_format=(C=l.standard_answer_format)==null?void 0:C.join("；"),l.stu_answer_format=(R=l.stu_answer_format)==null?void 0:R.join("；"),l.mark_role_format=$("mark_role",l.mark_role)}),b.value=r.data.data,u.value.pageOptions.total=r.data.total):(b.value=[],u.value.pageOptions.total=0):(h.warning(r.msg),b.value=[])})},H=e=>e==null?void 0:e.map((a,t)=>`(${t+1})${a}`),L=(e,a)=>{e.prop==="project_id"?(x.value=[],y.value=[],i.value.getCardData("subject_id")&&i.value.setCardData("subject_id",null),i.value.getCardData("paper_code")&&i.value.setCardData("paper_code",null),a&&be(a).then(t=>{x.value=t||[]})):e.prop==="subject_id"?(y.value=[],i.value.getCardData("paper_code")&&i.value.setCardData("paper_code",null),a&&T()):e.prop==="search_time"&&i.value.setCardData("search_time",a)},T=()=>{const{project_id:e,subject_id:a}=i.value.getAllCardData();ee({project_id:e,subject_id:a,page_size:-1}).then(r=>{r.code&&r.code===200&&(r.data.data.forEach(n=>{n.label=n.paper_name,n.value=n.paper_code}),y.value=r.data.data)})},J=()=>{var e,a;if(((e=b.value)==null?void 0:e.length)>0){let t={};if(((a=D.value)==null?void 0:a.length)>0){let r=[],n=[];D.value.map(l=>r.push(l.answer_id)),D.value.map(l=>{const p=l.paper_code;n.indexOf(p)===-1&&n.push(l.paper_code)}),t.answer_id_list=r,t.paper_code_list=n}else t=JSON.parse(JSON.stringify(i.value.getAllCardData())),t.stu_score_range=A();ce.confirm("确定启动智能阅卷吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{Q(t)}).catch(()=>{})}else h.warning("暂无试题可评分！")},Q=e=>{ye(e).then(a=>{if(a.code&&a.code===200){const{answer_id_list:t,mark_state:r,total:n}=a.data;O=r,f=n,N.value=f%m.value===0?parseInt(f/m.value):parseInt(f/m.value)+1;for(let p=0;p<n;p+=m.value)k.push(t.slice(p,p+m.value));const l={answer_id_list:k[0],mark_state:O,total:f};z(l)}else h.warning(a.msg)})},Y=e=>{const a={answer_id_list:k[e],mark_state:O,total:f};z(a)},z=e=>{De(e).then(a=>{a.code&&a.code===200?U(a.data):h.warning(a.msg)})},U=e=>{ke(e).then(a=>{var t,r;if(a.code&&a.code===200){let n={answer_id_list:e.answer_id_list?e.answer_id_list:null,await_mark_item_id:(t=a.data)==null?void 0:t.await_mark_item_id,total:(r=a.data)==null?void 0:r.total};W.value.openDialog(n)}else h.warning(a.msg)})},$=(e,a)=>{let t=[];e==="mark_result"?t=[{label:"正确",value:1},{label:"错误",value:2},{label:"部分正确",value:3}]:e==="mark_role"&&(t=[{label:"AI评分",value:1},{label:"人工评分",value:2}]);let r="";return t.map(n=>{n.value===a&&(r=n.label)}),e==="mark_result"&&a===4&&(r=""),r},G=e=>{D.value=e},K=e=>{u.value.pageOptions.pageSize=e,g()},X=e=>{u.value.pageOptions.currentPage=e,g()};function Z(){x.value=[]}return(e,a)=>{const t=d("el-input"),r=d("el-form-item"),n=d("el-form"),l=d("form-component"),p=d("el-card"),S=d("el-button"),C=d("Auth"),R=d("table-component");return pe(),ie("div",Ce,[v("div",Re,[s(p,null,{default:c(()=>[v("div",{ref_key:"formDivRef",ref:V,class:"query-box"},[s(l,{ref_key:"formRef",ref:i,"form-options":I,"is-query-btn":!0,onQueryDataFn:g,onOnchangeFn:L,onResetFields:Z},{stu_score:c(({scope:We})=>[s(n,{ref_key:"stuScoreFormRef",ref:j,model:_.value.scoreData,rules:_.value.rules},{default:c(()=>[v("div",Ve,[s(r,{class:"!mr-[0px]",prop:"minRange"},{default:c(()=>[s(t,{modelValue:_.value.scoreData.minRange,"onUpdate:modelValue":a[0]||(a[0]=B=>_.value.scoreData.minRange=B),style:{width:"101px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1}),Oe,s(r,{prop:"maxRange"},{default:c(()=>[s(t,{modelValue:_.value.scoreData.maxRange,"onUpdate:modelValue":a[1]||(a[1]=B=>_.value.scoreData.maxRange=B),style:{width:"102px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["form-options"])],512)]),_:1}),s(p,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:c(()=>[s(C,{value:"intelligent-marking/intelligent"},{default:c(()=>[v("div",Be,[s(S,{type:"primary",onClick:J},{default:c(()=>[ue("智能阅卷")]),_:1})])]),_:1}),s(R,{"min-height":u.value.styleOptions.minHeight,"table-options":u.value,"table-data":de(b),onOnHandleSelectionChange:G,onOnHandleSizeChange:K,onOnHandleCurrentChange:X},null,8,["min-height","table-options","table-data"])]),_:1})]),s(ae,{ref_key:"manualScoringRef",ref:F,onQueryData:g},null,512),s(te,{ref_key:"progressBarRef",ref:W,batchCount:N.value,currentBatch:E.value,oneBatchNum:m.value,onQueryData:g,onInitStart:Y},null,8,["batchCount","currentBatch","oneBatchNum"])])}}}),Te=fe(Fe,[["__scopeId","data-v-45bfd1e5"]]);export{Te as default};
