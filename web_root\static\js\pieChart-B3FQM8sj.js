import"./index-C9GYnvBh.js";import{l,n as s,a2 as d,B as f,o as c,c as h,dk as u}from"./index-B63pSD2p.js";const L={__name:"pieChart",props:{data:{type:Array,required:!0},colors:{type:Array,default:()=>[]},isShowLabelAndLine:{type:Boolean,default:!0},title:{type:Object,default:{}},fullScreen:{type:Boolean,default:!1},hiddenLegend:{type:Boolean,default:!1}},setup(a,{expose:i}){const e=a,r=l(null);let t=null;function o(){t||(t=u(r.value)),t.setOption({color:e.colors,tooltip:{trigger:"item",show:e.isShowLabelAndLine},legend:{show:!e.hiddenLegend,orient:"bottom",left:"right",itemWidth:e.fullScreen?50:25,itemHeight:e.fullScreen?28:14,textStyle:{fontSize:e.fullScreen?24:12}},title:e.title,series:[{name:"数据",type:"pie",radius:["46%","70%"],label:{show:e.isShowLabelAndLine,position:"outer",formatter:"{a|{b}}:{a|{c}}",overflow:"none",rich:{a:{align:"center",fontSize:e.fullScreen?24:12}}},labelLine:{show:e.isShowLabelAndLine},data:e.data,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0,0,0,0.5)"}}}]})}s(()=>{o(),window.addEventListener("resize",n)}),d(()=>{t&&t.dispose(),window.removeEventListener("resize",n)});function n(){t&&t.resize()}return f([()=>e.data,()=>e.colors],o,{deep:!0}),i({renderChart:o,resizeChart:n}),(p,w)=>(c(),h("div",{ref_key:"chartRef",ref:r,style:{width:"100%",height:"100%"}},null,512))}};export{L as default};
