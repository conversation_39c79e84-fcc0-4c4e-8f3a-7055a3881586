import{d,o as e,c,e as s,u as r,t as o,F as l,p as _,ac as p,ad as h,_ as u}from"./index-B63pSD2p.js";const b="/static/png/checkSearch-BHl6QaAA.png",a=t=>(p("data-v-9b227bb1"),t=t(),h(),t),f={class:"step-list"},m=["src"],v={class:"info1"},S=a(()=>s("div",{class:"info1"},"现在，你需要",-1)),g={class:"stepList"},k={class:"dark:!bg-black eye-box"},I={class:"step"},x=a(()=>s("br",null,null,-1)),B=d({__name:"index",props:{title:String,stepList:Array},setup(t){return(y,A)=>(e(),c("div",f,[s("img",{src:r(b),class:"check-info-img"},null,8,m),s("div",v,o(t.title)+"会在这里显示",1),S,s("div",g,[(e(!0),c(l,null,_(t.stepList,(n,i)=>(e(),c("div",k,[s("div",I,o(i+1),1),s("div",null,o(n),1)]))),256)),x])]))}}),L=u(B,[["__scopeId","data-v-9b227bb1"]]),w=L;export{w as S};
