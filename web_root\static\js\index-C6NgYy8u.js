var P=Object.defineProperty;var j=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var O=(l,o,e)=>o in l?P(l,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[o]=e,k=(l,o)=>{for(var e in o||(o={}))w.call(o,e)&&O(l,e,o[e]);if(j)for(var e of j(o))H.call(o,e)&&O(l,e,o[e]);return l};var C=(l,o,e)=>new Promise((f,d)=>{var i=t=>{try{s(e.next(t))}catch(u){d(u)}},b=t=>{try{s(e.throw(t))}catch(u){d(u)}},s=t=>t.done?f(t.value):Promise.resolve(t.value).then(i,b);s((e=e.apply(l,o)).next())});import{f as L}from"./handleMethod-BIjqYEft.js";import{aQ as N,aR as B,d as U,i as E,l as n,P as M,n as Q,ao as T,T as A,r as g,o as S,c as V,e as v,b as _,h as x,u as I,aN as $,y as G,_ as J}from"./index-B63pSD2p.js";import{c as K,a as X}from"./calculateTableHeight-BjE6OFD1.js";import{p as Y,g as Z,a as ee}from"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";const te=l=>N.request("post",B("/v1/exception/get_answer_exception_list"),{data:l}),ae={class:"zf-first-box"},oe={class:"zf-second-box"},le={class:"task-btn-box"},ne=["onClick"],se=U({name:"problem-paper",__name:"index",setup(l){const o=E(),e=n(null),f=n(null),d=n([{label:"未处理",value:0},{label:"已处理",value:1}]);n({});const i=n({}),b=M({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>Y.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>s.value},{label:"试题编号",prop:"ques_code",type:"input",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请输入试题编号",optionData:()=>[]},{label:"处理状态",prop:"handler_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择处理状态",optionData:()=>d.value}]}),s=n([]),t=n({field:[{prop:"project_name",label:"所属资格",minWidth:"190px"},{prop:"subject_name",label:"所属科目",minWidth:"200px"},{prop:"ques_code",label:"试题编号",minWidth:"200px"},{prop:"task_name",label:"任务名称",minWidth:"180px"},{prop:"stu_secret_num",label:"考生密号",minWidth:"200px"},{prop:"created_time",label:"提交时间",minWidth:"170px"},{prop:"handler_state",label:"处理状态",minWidth:"100px",formatter:a=>L(a.handler_state,d.value)||"未处理"},{prop:"operation",label:"操作",type:"slot",minWidth:"80px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),u=n([]);let y=null;Q(()=>{Z(),K(y,e.value,t.value,!1),m()}),T(()=>{X(y)});const z=(a,p)=>{a.prop==="project_id"&&(s.value=[],i.value.subject_id&&(i.value.subject_id=null),p&&ee(p).then(c=>{s.value=c||[]}))},m=()=>C(this,null,function*(){let{currentPage:a,pageSize:p}=t.value.pageOptions,c=k({current_page:a,page_size:p,is_show_self:0},i.value);const r=yield te(c);r.code&&r.code===200?(u.value=r.data.items,t.value.pageOptions.total=r.data.total):A.error(r.msg)}),D=a=>{t.value.pageOptions.currentPage=1,t.value.pageOptions.pageSize=a,m()},R=a=>{t.value.pageOptions.currentPage=a,m()};n(!1),n("");const W=a=>{o.push({path:"/manual-marking/start-marking/index",query:{type:2,project_id:a.project_id,subject_id:a.subject_id,ques_code:a.ques_code,stu_secret_num:a.stu_secret_num}})};function F(){s.value=[]}return(a,p)=>{const c=g("form-component"),r=g("el-card"),q=g("table-component");return S(),V("div",ae,[v("div",oe,[_(r,null,{default:x(()=>[v("div",{ref_key:"formDivRef",ref:e},[_(c,{ref_key:"formRef",ref:f,modelValue:i.value,"onUpdate:modelValue":p[0]||(p[0]=h=>i.value=h),"form-options":b,"is-query-btn":!0,onOnchangeFn:z,onQueryDataFn:m,onResetFields:F},null,8,["modelValue","form-options"])],512)]),_:1}),_(r,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:x(()=>[_(q,{minHeight:t.value.styleOptions.minHeight,"table-options":t.value,"table-data":u.value,onOnHandleSizeChange:D,onOnHandleCurrentChange:R},{operation:x(h=>[v("div",le,[I($)("problem-paper/dispose")?(S(),V("span",{key:0,class:"task-btn",onClick:re=>W(h.row)},"处理",8,ne)):G("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})])])}}}),_e=J(se,[["__scopeId","data-v-6660e005"]]);export{_e as default};
