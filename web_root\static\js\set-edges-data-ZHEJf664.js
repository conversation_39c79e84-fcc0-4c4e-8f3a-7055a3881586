import{d as E,l as s,P as V,r as g,w as A,z as N,o as k,c as H,b as d,e as q,h,f as _,T as Q,_ as B}from"./index-B63pSD2p.js";import{n as L}from"./validate-Dc6ka3px.js";const R={class:"mode-edge-box bg-white dark:bg-black"},T={class:"w-full flex justify-end"},W=E({__name:"set-edges-data",emits:["onConfirmFn"],setup(j,{expose:y,emit:D}){const f=D,u=s(!1),a=s(null),r=s(""),p=s({}),o=V({column:3,labelWidth:"80px",itemWidth:"230px",inline:!0,noDrag:!0,rules:{},fields:[{label:"仲裁阈值",prop:"threshold",type:"input",defaultValue:"",placeholder:"请输入仲裁阈值",clearable:!0,suffix:"%"},{label:"是否仲裁",prop:"isArbitrate",type:"select",defaultValue:"",placeholder:"请选择是否仲裁",isHidden:!0,clearable:!0,optionData:()=>m.value},{label:"是否质检",prop:"isQuality",type:"select",defaultValue:"",placeholder:"请选择是否质检",isHidden:!0,clearable:!0,optionData:()=>v.value},{label:"是否合格",prop:"isEligible",type:"select",defaultValue:"",placeholder:"请选择是否合格",isHidden:!0,clearable:!0,optionData:()=>b.value}]}),w={isArbitrate:[{required:!0,message:"请选择是否仲裁",trigger:["blur","change"]}],isEligible:[{required:!0,message:"请选择是否合格",trigger:["blur","change"]}],threshold:[{required:!0,message:"请输入仲裁阈值",trigger:["blur","change"]},{trigger:["blur","change"],validator:(l,e,t)=>{if(L(e))t();else return t(new Error("请输入100以内的正数（至多两位小数）！"))}}]},m=s([{label:"否",value:1},{label:"是",value:2}]),b=s([{label:"是",value:1},{label:"否",value:2}]),v=s([{label:"是",value:1},{label:"否",value:2}]),F=(l,e)=>{a.value&&a.value.resetFieldsFn(),u.value=!0,r.value=l,l==="01"?i("isArbitrate",Number(e.logic)?e.logic:1):l==="02"?i("isEligible",Number(e.logic)?e.logic:1):l==="03"?i("threshold",Number(e.percentage)?e.percentage:10):l==="04"&&i("isQuality",Number(e.logic)?e.logic:1)},i=(l,e)=>{o.fields.forEach(t=>{t.prop===l?(t.isHidden=!1,o.rules[l]=w[l]):(t.isHidden=!0,o.rules[t.prop]=[])}),a.value.setCardData(l,e)},x=()=>{a.value.formValidate().then(()=>{let l,e=[];if(r.value==="01"||r.value==="02"||r.value==="04"){const t=a.value.getAllCardData();r.value==="01"?(l=t.isArbitrate,e=m.value):r.value==="02"?(l=t.isEligible,e=b.value):r.value==="04"&&(l=t.isQuality,e=v.value);const n=e.filter(c=>c.value===l);f("onConfirmFn",n[0])}else r.value==="03"&&f("onConfirmFn",a.value.getCardData("threshold"))}).catch(()=>{Q.warning("请按要求填写！")})},C=()=>{u.value=!1,a.value.resetFieldsFn()};return y({openDrawer:F}),(l,e)=>{const t=g("form-component"),n=g("el-button");return A((k(),H("div",R,[d(t,{ref_key:"formRef",ref:a,modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=c=>p.value=c),"form-options":o,"is-query-btn":!1},null,8,["modelValue","form-options"]),q("div",T,[d(n,{onClick:C},{default:h(()=>[_("关闭")]),_:1}),d(n,{type:"primary",onClick:x},{default:h(()=>[_("确定")]),_:1})])],512)),[[N,u.value]])}}}),M=B(W,[["__scopeId","data-v-ba652943"]]);export{M as default};
