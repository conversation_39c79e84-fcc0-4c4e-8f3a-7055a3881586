import{l as a,m as s}from"./index-B63pSD2p.js";const t=a(1),o=a(0),l=a({x:0,y:0}),e=a(!1);a({x:0,y:0});a(null);a(null);a(null);a(null);a(null);const u=s(()=>({transform:`scale(${t.value}) rotate(${o.value}deg)`,cursor:e.value?"grabbing":"grab",left:`${l.value.x}px`,top:`${l.value.y}px`})),i=()=>{t.value=Math.min(t.value+.2,5)},m=()=>{t.value=Math.max(t.value-.2,.2)},d=()=>{o.value-=90},g=()=>{t.value=1,o.value=0,l.value={x:0,y:0}},c={width:24,height:24,body:'<path fill="currentColor" d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617Zm-2.006-.742A6.977 6.977 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.977 6.977 0 0 0 4.875-1.975l.15-.15ZM10 10V7h2v3h3v2h-3v3h-2v-3H7v-2h3Z"/>'},Z=c,n={width:24,height:24,body:'<path fill="currentColor" d="m18.031 16.617l4.283 4.282l-1.415 1.415l-4.282-4.283A8.96 8.96 0 0 1 11 20c-4.968 0-9-4.032-9-9s4.032-9 9-9s9 4.032 9 9a8.96 8.96 0 0 1-1.969 5.617Zm-2.006-.742A6.977 6.977 0 0 0 18 11c0-3.867-3.133-7-7-7s-7 3.133-7 7s3.133 7 7 7a6.977 6.977 0 0 0 4.875-1.975l.15-.15ZM7 10h8v2H7v-2Z"/>'},p=n,h={width:24,height:24,body:'<path fill="currentColor" d="M2 4c0-.552.455-1 .992-1h18.016c.548 0 .992.445.992 1v14c0 .552-.455 1-.992 1H2.992A.994.994 0 0 1 2 18V4Zm2 1v12h16V5H4Zm1 15h14v2H5v-2Z"/>'},f=h,v={width:24,height:24,body:'<path fill="currentColor" d="m13.414 6l1.829 1.828l-1.415 1.415L9.586 5L13.828.757l1.415 1.415L13.414 4H16a5 5 0 0 1 5 5v4h-2V9a3 3 0 0 0-3-3h-2.586ZM15 11v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1Zm-2 1H5v8h8v-8Z"/>'},y=v;export{y as A,f as T,Z,m as a,p as b,d as c,u as i,g as r,i as z};
