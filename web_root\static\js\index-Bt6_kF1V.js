import{d as R,i as L,l as r,P as E,n as A,ao as M,T as h,r as _,o as v,c as J,e as D,b as f,h as u,u as O,aN as S,g as k,f as w,y as V,aV as Q,_ as U}from"./index-B63pSD2p.js";import{g as $}from"./test-paper-management-DjV_45YZ.js";import{p as I,g as G,a as K}from"./common-methods-BWkba4Bo.js";import{q as X,g as Y}from"./rules-form-CST-rV3v.js";import{c as Z,a as ee}from"./calculateTableHeight-BjE6OFD1.js";import{o as te,n as ae}from"./paper-sample-DNcQEVKm.js";import"./scoring-rules-BR2vQ7G3.js";const le={class:"zf-first-box"},oe={class:"zf-second-box"},ne=R({name:"sample-score",__name:"index",setup(re){const j=L(),x=r(null),o=r(null),g=r([]),c=r([]),y=r({}),W=E({column:3,labelWidth:"68px",itemWidth:"240px",rules:{},fields:[{label:"项目",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择科目",optionData:()=>I.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>g.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>c.value},{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!0,clearable:!0,optionData:()=>X},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"评分状态",prop:"mark_state_list",type:"select",defaultValue:[],placeholder:"请选择评分状态",clearable:!0,multiple:!0,optionData:()=>[{label:"未评分",value:1},{label:"评分中",value:2},{label:"已评分",value:3},{label:"已暂停",value:4},{label:"已取消",value:5}]}]}),s=r({field:[{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"paper_name",label:"试卷",minWidth:"120px"},{prop:"ques_type_name",label:"题型",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"ques_order",label:"试题序号",minWidth:"120px"},{prop:"mark_state_str",label:"评分状态",minWidth:"100px"},{prop:"progress_count",label:"评分情况",minWidth:"100px"},{prop:"progress",label:"评分进度",minWidth:"160px",type:"progress",showOverflowTooltip:!0},{prop:"start_time",label:"开始评分时间",minWidth:"160px",sortable:!0},{prop:"end_time",label:"评分结束时间",minWidth:"160px",sortable:!0},{prop:"opera",label:"操作",type:"slot",fixed:"right",minWidth:"158px",showOverflowTooltip:!0}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),b=r([]),q=r([]);let C=null;A(()=>{Z(C,x.value,s.value),G(),Y(),i()}),M(()=>{ee(C)});const z=(t,e)=>{t.prop==="project_id"?(g.value=[],c.value=[],o.value.getCardData("subject_id")&&o.value.setCardData("subject_id",null),o.value.getCardData("paper_id")&&o.value.setCardData("paper_id",null),e&&K(e).then(a=>{g.value=a||[]})):t.prop==="subject_id"&&(c.value=[],o.value.getCardData("paper_id")&&o.value.setCardData("paper_id",null),e&&B())},B=()=>{const{project_id:t,subject_id:e}=o.value.getAllCardData();$({project_id:t,subject_id:e,page_size:-1}).then(n=>{n.code&&n.code===200&&(n.data.data.forEach(l=>{l.label=l.paper_name,l.value=l.paper_id}),c.value=n.data.data)})},i=t=>{let e=JSON.parse(JSON.stringify(o.value.getAllCardData()));e.ques_type_code_list.length||delete e.ques_type_code_list;let{currentPage:a,pageSize:n}=s.value.pageOptions;e.current_page=a,e.page_size=n,te(e).then(l=>{var d;if(l.code!=200)return h.error("后端接口异常:"+l.msg);b.value=((d=l.data)==null?void 0:d.list)||[],b.value.forEach(p=>{var m;p.updated_time=(m=p.updated_time)==null?void 0:m.replace("T"," ")})})},T=()=>{j.push({path:"/manual-marking/marking-paper/index"})},F=t=>{Q.confirm("确定删除该试卷抽样数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let e={sample_id:t.paper_sample_id};ae(e).then(a=>{a.code&&a.code===200?(h.success(a.msg),i()):h.warning(a.msg)})}).catch(()=>{})},H=t=>{q.value=t},N=t=>{s.value.pageOptions.pageSize=t,i()},P=t=>{s.value.pageOptions.currentPage=t,i()};return(t,e)=>{const a=_("form-component"),n=_("el-card"),l=_("el-button"),d=_("table-component");return v(),J("div",le,[D("div",oe,[f(n,null,{default:u(()=>[D("div",{ref_key:"formDivRef",ref:x,class:"query-box"},[f(a,{ref_key:"formRef",ref:o,modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=p=>y.value=p),"form-options":W,"is-query-btn":!0,onQueryDataFn:i,onOnchangeFn:z},null,8,["modelValue","form-options"])],512)]),_:1}),f(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:u(()=>[f(d,{"min-height":s.value.styleOptions.minHeight,"table-options":s.value,"table-data":b.value,onOnHandleSelectionChange:H,onOnHandleSizeChange:N,onOnHandleCurrentChange:P},{opera:u(p=>[O(S)("sample-score/start")?(v(),k(l,{key:0,type:"text",class:"text-btn",onClick:m=>T(p.row)},{default:u(()=>[w("开始评分 ")]),_:2},1032,["onClick"])):V("",!0),O(S)("sample-score/delete")?(v(),k(l,{key:1,type:"text",class:"text-btn",onClick:m=>F(p.row)},{default:u(()=>[w("删除 ")]),_:2},1032,["onClick"])):V("",!0)]),_:1},8,["min-height","table-options","table-data"])]),_:1})])])}}}),_e=U(ne,[["__scopeId","data-v-8aa685f0"]]);export{_e as default};
