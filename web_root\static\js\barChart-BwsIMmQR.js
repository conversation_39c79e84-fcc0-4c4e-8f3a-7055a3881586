var E=Object.defineProperty;var A=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var B=(o,n,t)=>n in o?E(o,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[n]=t,k=(o,n)=>{for(var t in n||(n={}))w.call(n,t)&&B(o,t,n[t]);if(A)for(var t of A(n))z.call(n,t)&&B(o,t,n[t]);return o};var L=(o,n,t)=>new Promise((e,c)=>{var s=i=>{try{u(t.next(i))}catch(p){c(p)}},v=i=>{try{u(t.throw(i))}catch(p){c(p)}},u=i=>i.done?e(i.value):Promise.resolve(i.value).then(s,v);u((t=t.apply(o,n)).next())});import{d as F,l as O,B as R,di as S,n as M,C as N,a2 as $,o as j,c as T,dj as U,_ as G}from"./index-B63pSD2p.js";const I=F({__name:"barChart",props:{xData:{},series:{},groupedData:{},seriesNames:{},colors:{},stacked:{type:Boolean},showLegend:{type:Boolean,default:!0},barWidth:{default:20},yAxis:{default:{}}},setup(o,{expose:n}){const t=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#9B59B6","#1ABC9C"],e=o,c=O(null);let s=null;function v(){var d,f,x,m;if(e.series&&e.series.length>0){const h=e.series.map((l,a)=>{var r;return(r=l.name)!=null?r:`系列${a+1}`});return{series:e.series.map((l,a)=>{var r,g,b,C;return{name:(r=l.name)!=null?r:`系列${a+1}`,type:"bar",data:l.data,barWidth:e.barWidth,barMaxWidth:30/(e.series.length!==0?e.series.length:1),barMinWidth:20/(e.series.length!==0?e.series.length:1),barGap:0,stack:e.stacked?"total":void 0,label:l.label?l.label:void 0,itemStyle:{color:(C=(b=l.color)!=null?b:(g=e.colors)==null?void 0:g[a])!=null?C:t[a%t.length]}}}),legendData:h}}if(e.groupedData&&e.groupedData.length>0){const h=Math.max(...e.groupedData.map(a=>{var r;return(r=a==null?void 0:a.length)!=null?r:0})),y=[],l=[];for(let a=0;a<h;a++){const r=(f=(d=e.seriesNames)==null?void 0:d[a])!=null?f:`系列${a+1}`;y.push(r),l.push({name:r,type:"bar",data:e.groupedData.map(g=>{var b;return(b=g==null?void 0:g[a])!=null?b:0}),barWidth:e.barWidth,stack:e.stacked?"total":void 0,itemStyle:{color:(m=(x=e.colors)==null?void 0:x[a])!=null?m:t[a%t.length]}})}return{series:l,legendData:y}}return{series:[],legendData:[]}}function u(){var h;const{series:d,legendData:f}=v(),x=d.length>1,m=(h=e.showLegend)!=null?h:x;return{tooltip:{trigger:"axis",valueFormatter:e.yAxis?e.yAxis.valueFormatter:null,axisPointer:{type:"shadow"}},legend:m?{data:f}:void 0,grid:{containLabel:!0,left:10,right:20,bottom:10,top:m?35:20},xAxis:{type:"category",axisLabel:{interval:0,overflow:"truncate",width:(c.value.offsetWidth-80)/e.xData.length},data:e.xData},yAxis:k({type:"value"},e.yAxis),series:d}}function i(){!s&&c.value&&(s=U.init(c.value),s.setOption(u()))}function p(){s==null||s.resize()}function D(){s&&s.setOption(u(),!0)}R(()=>[e.xData,e.series,e.groupedData,e.seriesNames,e.colors,e.stacked,e.barWidth,e.showLegend],()=>{D()},{deep:!0});const W=S.debounce(()=>{D()},100),_=()=>{p(),W()};return M(()=>L(this,null,function*(){yield N(),i(),window.addEventListener("resize",_)})),$(()=>{var d;window.removeEventListener("resize",_),(d=s==null?void 0:s.dispose)==null||d.call(s),s=null}),n({renderChart:i,resizeChart:p}),(d,f)=>(j(),T("div",{ref_key:"containerRef",ref:c,class:"bar-chart"},null,512))}}),H=G(I,[["__scopeId","data-v-c24827be"]]);export{H as default};
