var fe=Object.defineProperty,ve=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var X=Object.getOwnPropertySymbols;var be=Object.prototype.hasOwnProperty,he=Object.prototype.propertyIsEnumerable;var Y=(c,n,p)=>n in c?fe(c,n,{enumerable:!0,configurable:!0,writable:!0,value:p}):c[n]=p,V=(c,n)=>{for(var p in n||(n={}))be.call(n,p)&&Y(c,p,n[p]);if(X)for(var p of X(n))he.call(n,p)&&Y(c,p,n[p]);return c},O=(c,n)=>ve(c,me(n));import{_ as qe,g as ye,a as xe,b as Fe,c as Ve,u as Z}from"./group-member.vue_vue_type_script_setup_true_lang-CCOO0MKG.js";import{g as De,p as je,a as ee}from"./common-methods-BWkba4Bo.js";import{h as U}from"./validate-Dc6ka3px.js";import{F as He,E as we}from"./fullscreen-exit-line-DVwpkItP.js";import{d as ke,l as s,P as Ce,m as le,n as Ee,r as v,o as E,g as re,h as _,e as D,b as d,q as ae,f as ue,c as N,u as oe,F as Re,p as Le,t as Me,C as P,T as f,_ as Pe}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const Qe={class:"fullscreen-content"},Te={class:"flex-c"},Ge=["title"],We={class:"w-full flex-bc"},Oe=ke({__name:"add-group",emits:["queryData"],setup(c,{expose:n,emit:p}){const Q=p,T=s("创建小组"),R=s(!1),y=s(null),g=s(null),L=s(!1),b=s("first"),u=s({unionQues:[null,null]}),t={subjectRules:{group_level:[{required:!0,message:"请选择小组层级",trigger:["blur","change"]}],project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],group_code:[{required:!0,message:"请输入小组编号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(r,e,l)=>{if(e&&!U(e))return l(new Error("请输入数字！"));l()}},{min:3,max:3,message:"请输入3位数的小组编号",trigger:["blur","change"]}],group_name:[{required:!0,message:"请输入小组名称",trigger:["blur","change"]}]},subjectFields:[{label:"小组层级",prop:"group_level",type:"radio",defaultValue:1,clearable:!0,optionData:()=>[{label:"科目级",value:1},{label:"题组级",value:2},{label:"小组级",value:3}]},{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>je.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>M.value},{label:"小组编号",prop:"group_code",type:"text",showWordLimit:!0,maxlength:3,defaultValue:"",placeholder:"请输入小组编号",clearable:!0},{label:"小组名称",prop:"group_name",type:"input",defaultValue:"",placeholder:"请输入小组名称",clearable:!0}],quesRules:{group_level:[{required:!0,message:"请选择小组层级",trigger:["blur","change"]}],parent_group_id:[{required:!0,message:"请选择所属父级",trigger:["blur","change"]}],group_code:[{required:!0,message:"请输入小组编号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(r,e,l)=>{if(e&&!U(e))return l(new Error("请输入数字！"));l()}},{min:4,max:4,message:"请输入4位数的小组编号",trigger:["blur","change"]}],group_name:[{required:!0,message:"请输入小组名称",trigger:["blur","change"]}],ques_code:[{required:!0,message:"请选择关联题号",trigger:["blur","change"]}]},quesFields:[{label:"小组层级",prop:"group_level",type:"radio",defaultValue:1,clearable:!0,optionData:()=>[{label:"科目级",value:1},{label:"题组级",value:2},{label:"小组级",value:3}]},{label:"所属父级",prop:"parent_group_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属父级",props:{value:"group_id",label:"group_name"},optionData:()=>j.value},{label:"小组编号",prop:"group_code",width:"320px",type:"text",defaultValue:"",showWordLimit:!0,maxlength:4,placeholder:"请输入小组编号",clearable:!0,leftTextWidth:"40px",leftText:()=>z.value},{label:"小组名称",prop:"group_name",type:"input",defaultValue:"",placeholder:"请输入小组名称",clearable:!0},{label:"关联试题",prop:"ques_code",type:"template",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择关联试题",optionData:()=>x.value},{label:"关联试题",prop:"unionQues",type:"doubleSelect",isHidden:!0,clearable:!0,defaultValue:[null,null],width:"175px",filterable:!0,leftPlaceholder:"请选择试卷",rightPlaceholder:"请选择题号",leftOptionData:()=>[],rightOptionData:()=>[]}],groupRules:{group_level:[{required:!0,message:"请选择小组层级",trigger:["blur","change"]}],parent_group_id:[{required:!0,message:"请选择所属父级",trigger:["blur","change"]}],group_code:[{required:!0,message:"请输入小组编号",trigger:["blur","change"]},{trigger:["blur","change"],validator:(r,e,l)=>{if(e&&!U(e))return l(new Error("请输入数字！"));l()}},{min:2,max:2,message:"请输入2位数的小组编号",trigger:["blur","change"]}],group_name:[{required:!0,message:"请输入小组名称",trigger:["blur","change"]}]},groupFields:[{label:"小组层级",prop:"group_level",type:"radio",defaultValue:1,clearable:!0,optionData:()=>[{label:"科目级",value:1},{label:"题组级",value:2},{label:"小组级",value:3}]},{label:"所属父级",prop:"parent_group_id",type:"cascader",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属父级",props:{value:"group_id",label:"group_name"},optionData:()=>H.value},{label:"小组编号",prop:"group_code",type:"text",defaultValue:"",showWordLimit:!0,maxlength:2,placeholder:"请输入小组编号",clearable:!0,width:"300px",leftTextWidth:"60px",leftText:()=>B.value},{label:"小组名称",prop:"group_name",type:"input",defaultValue:"",placeholder:"请输入小组名称",clearable:!0}]},j=s([]),H=s([]),o=Ce({labelWidth:"80px",itemWidth:"360px",inline:!0,rules:t.subjectRules,fields:t.subjectFields}),M=s([]),x=s([]),F=s("add"),G=s(!0),h=s(!1),z=le(()=>u.value.parent_group_id&&j.value.length?j.value.find(r=>r.group_id===u.value.parent_group_id).group_code:""),B=le(()=>{var r;if(u.value.parent_group_id&&H.value.length)if(typeof u.value.parent_group_id=="string"){let e="";return H.value.forEach(l=>{l.children.forEach(a=>{a.group_id===u.value.parent_group_id&&(e=a.group_code)})}),e}else{const e=H.value.find(l=>l.group_id===u.value.parent_group_id[0]);return((r=e==null?void 0:e.children)==null?void 0:r.find(l=>l.group_id===u.value.parent_group_id[1]).group_code)||null}return null}),W=s({});Ee(()=>{De()});const S=()=>{L.value=!L.value},te=(r,e)=>{R.value=!0,F.value=r,F.value==="add"?T.value="创建小组":F.value==="edit"&&(W.value=e,h.value=!1,P(()=>{g.value.getHumanGroupMember(e.group_id)}),T.value="编辑小组",e.group_level===1?(o.fields=t.subjectFields,o.rules=t.subjectRules,ee(e.project_id).then(l=>{M.value=l||[]})):e.group_level===2?(A(e.group_level),J(e.exam_mode,e.parent_group_id),o.fields=t.quesFields,o.rules=t.quesRules):e.group_level===3&&(o.fields=t.groupFields,o.rules=t.groupRules,I()),P(()=>{o.fields.forEach(l=>{l.prop==="group_level"&&(l.disabled=!0),e.hasOwnProperty(l.prop)&&(e.group_level!==1&&l.prop==="group_code"?u.value[l.prop]=e.group_code.slice(e.parent_group_code.length):u.value[l.prop]=e[l.prop])}),e.tab==="second"&&(b.value="second",P(()=>{g.value.getHumanMember()}))}))},w=()=>{R.value=!1,y.value.resetFieldsFn(),$(),b.value="first",o.fields=t.subjectFields,o.rules=t.subjectRules,x.value=[],o.fields.forEach(r=>{r.prop==="group_level"&&r.disabled&&(r.disabled=!1)})},se=()=>{g.value.getHumanMember()},$=()=>{var r;u.value={group_level:1,unionQues:[null,null]},(r=g.value)==null||r.clearData()},ne=(r,e)=>{if(r.prop==="project_id")M.value=[],u.value.subject_id&&(u.value.subject_id=null),e&&ee(e).then(l=>{M.value=l||[]});else if(r.prop==="group_level"){const l=e;e===1?(h.value=!1,o.fields=t.subjectFields,o.rules=t.subjectRules,y.value.resetFieldsFn()):e===2?(h.value=!0,o.fields=t.quesFields,o.rules=t.quesRules,A(),y.value.resetFieldsFn()):e===3&&(h.value=!0,I(),o.fields=t.groupFields,o.rules=t.groupRules,y.value.resetFieldsFn()),F.value==="add"&&o.fields.forEach(a=>{a.prop==="group_level"&&a.disabled&&(a.disabled=!1)}),$(),u.value.group_level=l}else if(r.prop==="parent_group_id"){e?h.value=!1:h.value=!0;const l=j.value.find(a=>a.group_id===e);u.value.group_level===2&&e?(J(l.exam_mode),u.value.ques_code="",u.value.unionQues=[],x.value=[],o.fields.forEach(a=>{l.exam_mode===0?a.prop==="ques_code"?a.isHidden&&(a.isHidden=!1):a.prop==="unionQues"&&(a.isHidden||(a.isHidden=!0)):l.exam_mode===1&&(a.prop==="unionQues"?a.isHidden&&(a.isHidden=!1):a.prop==="ques_code"&&(a.isHidden||(a.isHidden=!0)))})):(u.value.ques_code="",u.value.unionQues=[],x.value=[])}},A=r=>{let e={group_level:r||u.value.group_level};ye(e).then(l=>{l.code&&l.code===200?j.value=l.data.data:f.error(l.msg)})},I=()=>{xe({page_size:-1}).then(e=>{var l,a;e.code&&e.code===200?((a=(l=e.data)==null?void 0:l.data)==null||a.forEach(i=>{i.children.length||(i.disabled=!0),i.children.forEach(k=>{k.children=[]})}),H.value=e.data.data||[]):f.error(e.msg)})},J=(r,e)=>{let l={parent_group_id:e||u.value.parent_group_id,exam_mode:r};Fe(l).then(a=>{a.code&&a.code===200?x.value=a.data.ques_code_list.map(i=>({value:i,label:i})):f.error(a.msg)})},ie=()=>{y.value.formValidate().then(()=>{let r=V({},u.value);r.group_level===2?r.group_code=z.value+r.group_code:r.group_level===3&&(typeof r.parent_group_id!="string"&&(r.parent_group_id=r.parent_group_id[1]),r.group_code=B.value+r.group_code),delete r.unionQues,F.value==="add"?pe(r):de(r)}).catch(()=>{f.warning("请按要求填写！")})},pe=r=>{Ve(r).then(e=>{if(e.code&&e.code===200)if(g.value.setHumanGroupMember(e.data.group_id),Q("queryData"),f.success(e.msg),!G.value)b.value=="second"&&w(),b.value="second",P(()=>{g.value.getHumanMember()});else{u.value.group_name="",u.value.ques_code="",u.value.unionQues=[];let l=u.value.group_code.length,i=Number(u.value.group_code)+1+"",k=i.length;for(let C=0;C<l-k;C++)i="0"+i;u.value.group_code=i}else f.error(e.msg)})},de=r=>{if(b.value=="second")g.value.handleValidateForm().then(e=>{let l=V(O(V({},r),{group_id:W.value.group_id}),g.value.getMember());Z(l).then(a=>{a.code&&a.code===200?(Q("queryData"),f.success(a.msg),w()):f.error(a.msg)})});else{let e=V(O(V({},r),{group_id:W.value.group_id}),g.value.getMember());Z(e).then(l=>{l.code&&l.code===200?(Q("queryData"),f.success(l.msg),w()):f.error(l.msg)})}};return n({openDialog:te}),(r,e)=>{const l=v("iconify-icon-offline"),a=v("el-option"),i=v("el-select"),k=v("form-component"),C=v("el-tab-pane"),ce=v("el-tabs"),ge=v("el-checkbox"),K=v("el-button"),_e=v("el-dialog");return E(),re(_e,{modelValue:R.value,"onUpdate:modelValue":e[4]||(e[4]=m=>R.value=m),title:T.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",size:"70%",fullscreen:L.value,"close-on-click-modal":!1,"before-close":w},{footer:_(()=>[D("div",We,[d(ge,{style:ae({visibility:F.value==="add"?"visible":"hidden"}),modelValue:G.value,"onUpdate:modelValue":e[3]||(e[3]=m=>G.value=m),label:"连续添加",size:"large"},null,8,["style","modelValue"]),D("div",null,[d(K,{onClick:w},{default:_(()=>[ue("取消")]),_:1}),d(K,{type:"primary",onClick:ie},{default:_(()=>[ue("保存")]),_:1})])])]),default:_(()=>[D("div",Qe,[L.value?(E(),N("i",{key:1,class:"cursor-pointer mr-[12px]",onClick:S},[d(l,{icon:oe(we)},null,8,["icon"])])):(E(),N("i",{key:0,class:"cursor-pointer mr-[12px]",onClick:S},[d(l,{icon:oe(He)},null,8,["icon"])]))]),D("div",null,[d(ce,{modelValue:b.value,"onUpdate:modelValue":e[2]||(e[2]=m=>b.value=m),class:"demo-tabs",onTabClick:se},{default:_(()=>[d(C,{label:"基本信息",name:"first"},{default:_(()=>[D("div",Te,[d(k,{ref_key:"formRef",ref:y,modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=m=>u.value=m),"form-options":o,"is-query-btn":!1,onOnchangeFn:ne},{ques_code:_(({scope:m})=>[d(i,{modelValue:u.value.ques_code,"onUpdate:modelValue":e[0]||(e[0]=q=>u.value.ques_code=q),placeholder:"请选择关联试题",style:ae(`width: ${o.itemWidth}`)},{default:_(()=>[(E(!0),N(Re,null,Le(x.value,q=>(E(),re(a,{key:q.value,label:q.label,value:q.value},{default:_(()=>[D("p",{title:q.label},Me(q.label),9,Ge)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","style"])]),_:1},8,["modelValue","form-options"])])]),_:1}),d(C,{label:"组员设置",name:"second",disabled:h.value},{default:_(()=>[d(qe,{ref_key:"groupMemberRef",ref:g,level:u.value.group_level,parentFormData:u.value},null,8,["level","parentFormData"])]),_:1},8,["disabled"])]),_:1},8,["modelValue"])])]),_:1},8,["modelValue","title","fullscreen"])}}}),Ie=Pe(Oe,[["__scopeId","data-v-7f4b0352"]]);export{Ie as default};
