function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["static/js/takeParam-CJH-ckby.js","static/js/paper-sample-DNcQEVKm.js","static/js/index-B63pSD2p.js","static/css/index-Bko8je_6.css","static/js/index-BCxZUZMh.js","static/css/index-D5nR6sP3.css","static/js/downloadRequest-CdE2PBjt.js","static/css/takeParam-B85qg6x9.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var H=Object.defineProperty;var k=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var z=(s,l,a)=>l in s?H(s,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[l]=a,B=(s,l)=>{for(var a in l||(l={}))L.call(l,a)&&z(s,a,l[a]);if(k)for(var a of k(l))N.call(l,a)&&z(s,a,l[a]);return s};import{d as U,l as n,P as M,aN as j,T as d,aV as I,n as G,ao as Q,r as _,o as D,c as Z,e as S,b as u,h as g,f as J,g as K,u as X,y as Y,aU as $,Z as ee,_ as te}from"./index-B63pSD2p.js";import{c as ae,a as le}from"./calculateTableHeight-BjE6OFD1.js";import{p as oe,g as ne,a as se}from"./common-methods-BWkba4Bo.js";import{n as pe,o as re}from"./paper-sample-DNcQEVKm.js";import{d as ie}from"./downloadRequest-CdE2PBjt.js";import"./test-paper-management-DjV_45YZ.js";const ue={class:"zf-first-box"},ce={class:"zf-second-box"},me={class:"zf-flex-end"},de=U({name:"paper-sample",__name:"index",setup(s){const l=$(()=>ee(()=>import("./takeParam-CJH-ckby.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),a=n(null),P=n(null),c=n({}),w=M({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>oe.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>h.value},{label:"考试模式",prop:"exam_mode",type:"select",defaultValue:null,clearable:!0,optionData:()=>[{label:"抽参",value:0},{label:"抽卷",value:1}]}]}),h=n([]),p=n({field:[{prop:"subject_name",label:"科目",minWidth:"200px"},{prop:"exam_mode",label:"考试模式",minWidth:"120px"},{prop:"sample_count",label:"抽样量",minWidth:"90px"},{prop:"username",label:"更新人",minWidth:"140px"},{prop:"updated_time",label:"更新时间",minWidth:"120px"},{prop:"",label:"操作",type:"template",minWidth:"310px",fixed:"right",templateGroup:[{title:()=>j("paper-sample/export")?"导出":"",clickBtn(e){T({sample_id:e.paper_sample_id})}},{title:()=>j("paper-sample/delete")?"删除":"",clickBtn(e){W(e)}}]}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),T=e=>{ie("post","/v1/prepare/export_stu_answer_list_by_sample_id","试卷模板",e,e,"zip").then(t=>{t&&t.data&&t.data.type=="application/json"?d({message:"后端异常，原因:"+t.msg,type:"error"}):d.success("导出成功")})},W=e=>{I.confirm("确定删除该试卷抽样数据吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let t={sample_id:e.paper_sample_id};pe(t).then(o=>{o.code&&o.code===200?(d.success(o.msg),r()):d.warning(o.msg)})}).catch(()=>{})},x=n([]);let C=null;G(()=>{ne(),ae(C,a.value,p.value),r()}),Q(()=>{le(C)});const A=(e,t)=>{e.prop==="project_id"&&(h.value=[],c.value.subject_id&&(c.value.subject_id=null),t&&se(t).then(o=>{h.value=o||[]}))},r=()=>{let{currentPage:e,pageSize:t}=p.value.pageOptions,o=B({current_page:e,page_size:t},c.value);re(o).then(i=>{var f;if(i.code!=200)return d.error("后端接口异常:"+i.msg);x.value=((f=i.data)==null?void 0:f.list)||[],x.value.forEach(v=>{var b;v.updated_time=(b=v.updated_time)==null?void 0:b.replace("T"," ")})})},F=e=>{p.value.pageOptions.pageSize=e,r()},R=e=>{p.value.pageOptions.currentPage=e,r()},V=n(null),O=n(null),m=n(!1),q=()=>{V.value="",O.value="",m.value=!0},E=()=>{m.value=!1,r()};return(e,t)=>{const o=_("form-component"),i=_("el-card"),f=_("el-button"),v=_("Auth"),b=_("table-component");return D(),Z("div",ue,[S("div",ce,[u(i,null,{default:g(()=>[S("div",{ref_key:"formDivRef",ref:a},[u(o,{ref_key:"formRef",ref:P,modelValue:c.value,"onUpdate:modelValue":t[0]||(t[0]=y=>c.value=y),"form-options":w,"is-query-btn":!0,onOnchangeFn:A,onQueryDataFn:r},null,8,["modelValue","form-options"])],512)]),_:1}),u(i,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:g(()=>[S("div",me,[u(v,{value:"paper-sample/paperSampleCreate"},{default:g(()=>[u(f,{class:"mr-[12px]",type:"primary",onClick:q},{default:g(()=>[J("试卷抽样")]),_:1})]),_:1})]),u(b,{minHeight:p.value.styleOptions.minHeight,"table-options":p.value,"table-data":x.value,onOnHandleSizeChange:F,onOnHandleCurrentChange:R},null,8,["minHeight","table-options","table-data"])]),_:1}),m.value?(D(),K(X(l),{key:0,modelValue:m.value,"onUpdate:modelValue":t[1]||(t[1]=y=>m.value=y),sample_id:V.value,query_conditions:O.value,isAi:!0,onSaveSuccess:E},null,8,["modelValue","sample_id","query_conditions"])):Y("",!0)])])}}}),ye=te(de,[["__scopeId","data-v-2ea81e3f"]]);export{ye as default};
