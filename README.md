# 卓帆电子化考试阅卷管理系统 - 一键启动器

## 文件说明

本启动器包含以下文件：

### 主要程序文件
- **`一键启动器.py`** - GUI图形界面版本启动器（推荐使用）
- **`简易启动器.py`** - 命令行版本启动器（备用方案）
- **`启动器.bat`** - Windows批处理启动脚本

### 配置和说明文件
- **`requirements.txt`** - Python依赖包列表
- **`一键启动器使用说明.md`** - 详细使用说明
- **`README.md`** - 本文件

## 快速开始

### 最简单的使用方法
1. 双击运行 **`启动器.bat`**
2. 选择启动器版本（推荐选择1-GUI版本）
3. 在启动器界面中点击"一键启动"

### 功能特点
- ✅ 按正确顺序启动：Redis → 主程序 → 数据服务
- ✅ 按安全顺序关闭：数据服务 → 主程序 → Redis  
- ✅ 实时状态监控
- ✅ 详细操作日志
- ✅ 图形界面和命令行两种模式
- ✅ 自动环境检查和依赖安装

### 启动顺序说明
1. **Redis服务** (`Redis-x64-3.0.504\redis-server.exe`) - 数据缓存服务
2. **主程序** (`卓帆电子化考试阅卷管理系统V1.0.exe`) - 核心应用程序  
3. **数据服务** (`定时任务V1.0.exe`) - 后台数据处理服务

### 关闭顺序说明
1. **数据服务** - 先停止后台数据处理
2. **主程序** - 再关闭核心应用
3. **Redis服务** - 最后关闭数据缓存

## 系统要求
- Windows 操作系统
- Python 3.6+ （会自动检查和提示安装）
- 必需的程序文件在正确位置

## 使用方式

### 方式一：批处理文件（最简单）
```bash
# 双击运行
启动器.bat
```

### 方式二：直接运行Python脚本
```bash
# GUI版本
python 一键启动器.py

# 命令行版本  
python 简易启动器.py
```

## 故障排除

### 常见问题
1. **Python未安装** - 批处理文件会自动检查并提示安装
2. **依赖包缺失** - 会自动安装psutil包
3. **程序文件未找到** - 检查文件路径是否正确
4. **权限不足** - 以管理员身份运行

### 技术支持
如遇问题请查看 `一键启动器使用说明.md` 中的详细故障排除指南。

---
**版本**: 1.0  
**开发日期**: 2025-09-08
