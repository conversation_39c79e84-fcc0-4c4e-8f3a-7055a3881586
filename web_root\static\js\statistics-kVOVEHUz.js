function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["static/js/pieChart-B3FQM8sj.js","static/js/index-C9GYnvBh.js","static/js/index-B63pSD2p.js","static/css/index-Bko8je_6.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{d as v,l as t,r as g,o as x,c as C,e as a,b as n,h as o,f as i,u as d,aU as y,C as R,Z as b}from"./index-B63pSD2p.js";const k={style:{"margin-top":"22px",height:"150px",width:"300px"}},z={style:{"margin-top":"22px",height:"150px",width:"300px"}},E={style:{"margin-top":"22px",height:"150px",width:"300px"}},V=v({__name:"statistics",setup(w,{expose:_}){const l=y(()=>b(()=>import("./pieChart-B3FQM8sj.js"),__vite__mapDeps([0,1,2,3]))),u=t(null),c=t(null),p=t(null),h=t([{value:23,name:"满分"},{value:45,name:"3分"}]),f=t([{value:23,name:"满分"},{value:45,name:"4分"}]),m=t([{value:23,name:"满分"},{value:45,name:"5分"}]);return _({resizeEcharts:()=>{R(()=>{var r,s,e;(r=u.value)==null||r.resizeChart(),(s=c.value)==null||s.resizeChart(),(e=p.value)==null||e.resizeChart()})}}),(r,s)=>{const e=g("el-text");return x(),C("div",null,[a("p",null,[n(e,null,{default:o(()=>[i("样卷：14份")]),_:1})]),a("div",k,[n(d(l),{ref_key:"sampleRef",ref:u,data:h.value,colors:["red","rgb(104,187,196)"],hiddenLegend:"true"},null,8,["data","colors"])]),a("p",null,[n(e,null,{default:o(()=>[i("培训卷：14份")]),_:1})]),a("div",z,[n(d(l),{ref_key:"trainingRef",ref:c,data:f.value,colors:["rgb(80,135,236)","rgb(104,187,196)"],hiddenLegend:"true"},null,8,["data","colors"])]),a("p",null,[n(e,null,{default:o(()=>[i("试评卷：14份")]),_:1})]),a("div",E,[n(d(l),{ref_key:"trialRef",ref:p,data:m.value,colors:["rgb(80,135,236)","rgb(104,187,196)"],hiddenLegend:"true"},null,8,["data","colors"])])])}}});export{V as default};
