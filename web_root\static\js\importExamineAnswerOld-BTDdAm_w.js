var x=(_,w,r)=>new Promise((p,d)=>{var a=e=>{try{t(r.next(e))}catch(s){d(s)}},u=e=>{try{t(r.throw(e))}catch(s){d(s)}},t=e=>e.done?p(e.value):Promise.resolve(e.value).then(a,u);t((r=r.apply(_,w)).next())});import{u as F}from"./uploadRequest-IEYs8WTn.js";import{d as I,n as Z,ao as q,P as S,l as v,B as E,r as m,o as N,g as R,h as l,b as i,f as h,T as f}from"./index-B63pSD2p.js";const A=I({__name:"importExamineAnswerOld",props:{isShowDialog:{type:Boolean,required:!0,default:!1},title:{type:String,default:""},ZIndex:{required:!1,type:Number,default:2001},ImportCallBack:{type:Function,default:()=>{}}},emits:["update:isShowDialog","closeDialog"],setup(_,{emit:w}){const r=_,p=w;Z(()=>{}),q(()=>{});const d=S({visible:!1}),a=S({password:""}),u=v(null),t=v([]),e=v(!1),s=()=>{if(e.value)return f.warning("请等待考生作答信息导入完成");d.visible=!1,p("update:isShowDialog",!1),p("closeDialog")},b=()=>{a.password="",u.value=null,t.value=[]},y=(n,c)=>{u.value=(n==null?void 0:n.raw)||null,t.value=c.slice(-1)},B=()=>{u.value=null,t.value=[]},D=()=>x(this,null,function*(){if(!a.password){f.warning("请输入文件密码");return}if(!u.value){f.warning("请选择ZIP文件");return}let n="/v1/transfer/import_stu_answer_zip",c={file:u.value,unzip_password:a.password};p("uploadBegin"),F("post",n,c,!1).then(o=>{o.code&&o.code===200?(f.success(o.msg),p("ImportCallBack"),s(),b()):f.warning(o.msg)}).catch(o=>{f.error((o==null?void 0:o.msg)||"上传失败"),p("uploadError")})});return E(()=>r.isShowDialog,n=>{d.visible=n,b()},{deep:!0,immediate:!0}),(n,c)=>{const o=m("el-input"),C=m("el-form-item"),g=m("el-button"),k=m("el-upload"),z=m("el-form"),U=m("DialogComponent");return N(),R(U,{isShowDialog:d.visible,width:"500px",onCloseDialog:s,onSure:D,beforeClose:s,title:"导入考生作答信息",fullscreen:!1},{content:l(()=>[i(z,{model:a,"label-width":"100px"},{default:l(()=>[i(C,{label:"文件密码"},{default:l(()=>[i(o,{modelValue:a.password,"onUpdate:modelValue":c[0]||(c[0]=V=>a.password=V),type:"password",placeholder:"请输入文件密码","show-password":"",autocomplete:"new-password"},null,8,["modelValue"])]),_:1}),i(C,{label:"选择文件"},{default:l(()=>[i(k,{class:"zip-uploader",action:"#","auto-upload":!1,"show-file-list":!0,limit:1,accept:".zip","on-change":y,"on-remove":B,"file-list":t.value},{default:l(()=>[i(g,null,{default:l(()=>[h("选择上传文件")]),_:1})]),_:1},8,["file-list"])]),_:1})]),_:1},8,["model"])]),footer:l(()=>[i(g,{onClick:s,disabled:e.value},{default:l(()=>[h("取消")]),_:1},8,["disabled"]),i(g,{type:"primary",onClick:D,loading:e.value},{default:l(()=>[h("确认上传")]),_:1},8,["loading"])]),_:1},8,["isShowDialog"])}}});export{A as default};
