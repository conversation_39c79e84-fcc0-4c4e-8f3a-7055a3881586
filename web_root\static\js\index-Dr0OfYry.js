import{aQ as T,aR as I,d as P,l as g,az as U,i as B,P as J,an as L,n as M,ao as Q,N as j,R as K,T as R,r as c,o as G,c as X,e as f,b as i,h as m,f as Y,u as Z,ac as $,ad as ee,_ as ae}from"./index-B63pSD2p.js";import{d as H}from"./validate-Dc6ka3px.js";const w=_=>T.request("post",I("/v1/manual_read/get_read_task_result"),{data:_}),te=_=>($("data-v-741e2875"),_=_(),ee(),_),ne={class:"zf-first-box"},oe={class:"zf-second-box"},se={class:"inline-row-box"},re={class:"flex"},le=te(()=>f("span",{class:"mr-[3px] ml-[3px]"},"-",-1)),ie={class:"ml-[12px]"},ue=P({name:"marking-details",__name:"index",setup(_){const v=g(null),q=g(null),y=U(),V=B(),O=g({}),z=J({column:3,labelWidth:"100px",itemWidth:"240px",rules:{},fields:[{label:"考生密号",prop:"stu_secret_num",type:"input",defaultValue:null,placeholder:"请输入考生密号",clearable:!0},{label:"考生得分",prop:"score_range1",type:"template",defaultValue:null,placeholder:"请输入考生得分",clearable:!0}]}),d=g({rules:{minRange:[{trigger:["blur","change"],validator:(a,e,t)=>{if(e&&!H(e))return t(new Error("请输入数字！"));t()}}],maxRange:[{trigger:["blur","change"],validator:(a,e,t)=>{if(e&&!H(e))return t(new Error("请输入数字！"));t()}}]},scoreData:{maxRange:"",minRange:""}}),o=g({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"paper_name",label:"试卷名称",minWidth:"120px"},{prop:"ques_desc_text",label:"试题",minWidth:"240px"},{prop:"standard_answer",label:"参考答案",minWidth:"160px"},{prop:"stu_answer",label:"考生答案",minWidth:"160px"},{prop:"ques_score",label:"试题分数",minWidth:"160px"},{prop:"mark_score",label:"考生得分",minWidth:"90px"},{prop:"quality_result",label:"质检结果",minWidth:"100px"},{prop:"quality_suggestion",label:"质检意见",minWidth:"210px"},{prop:"marked_time",label:"评分时间",minWidth:"160px",sortable:!0}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let x=g([]);L(()=>{const a=y.query.path,e=[],t=[],u=[],p=["quality_result","quality_suggestion"];a==="/manual-marking/my-marking-task/index"?o.value.field.forEach(n=>{(t.includes(n.prop)||u.includes(n.prop)||p.includes(n.prop))&&(n.isHidden=!0)}):a==="/manual-marking/my-arbitration-task/index"?o.value.field.forEach(n=>{(e.includes(n.prop)||p.includes(n.prop))&&(n.isHidden=!0)}):a==="/manual-marking/my-quality-task/index"&&o.value.field.forEach(n=>{e.includes(n.prop)&&(n.isHidden=!0)})}),M(()=>{var a;h(),o.value.styleOptions.minHeight=window.innerHeight-234-((a=v.value)==null?void 0:a.clientHeight)+"px",window.addEventListener("resize",S)});const S=()=>{var a;o.value.styleOptions.minHeight=window.innerHeight-234-((a=v.value)==null?void 0:a.clientHeight)+"px"};Q(()=>{window.removeEventListener("resize",S)});const h=()=>{const{m_read_task_id:a,paper_id:e,path:t}=y.query,{userId:u,roles:p}=j().getItem(K);let n=JSON.parse(JSON.stringify(q.value.getAllCardData()));const{currentPage:k,pageSize:b}=o.value.pageOptions;let s={m_read_task_id:a,paper_id:e,execute_user_id:u,execute_role_id:p[0],current_page:k,page_size:b};s=Object.assign(n,s);const{minRange:l,maxRange:r}=JSON.parse(JSON.stringify(d.value.scoreData));(l||r)&&(l?r?Number(l)>Number(r)?s.score_range=[r,l]:s.score_range=[l,r]:s.score_range=[l,l]:s.score_range=[r,r]),t==="/manual-marking/my-marking-task/index"?E(s):t==="/manual-marking/my-arbitration-task/index"?N(s):t==="/manual-marking/my-quality-task/index"&&W(s)},E=a=>{w(a).then(e=>{e.code&&e.code===200?(x.value=e.data.data,o.value.pageOptions.total=e.data.total,x.value.forEach(t=>{t.ques_desc_text=t.ques_desc.text})):R.warning(e.msg)})},N=a=>{w(a).then(e=>{e.code&&e.code===200?D(e.data):R.warning(e.msg)})},W=a=>{w(a).then(e=>{e.code&&e.code===200?D(e.data):R.warning(e.msg)})},D=a=>{var e;(e=a.data)==null||e.forEach(t=>{if(t.ques_desc_text=t.ques_desc.text,t.expert_data&&t.expert_data.length>0){let u=[],p=[];t.expert_data.forEach(n=>{u.push(n.name),p.push(n.mark_score)}),t.expert=u,t.expert_score=p}t.expert_data&&t.expert_data.length>0}),x.value=a.data,o.value.pageOptions.total=a.total},C=()=>{V.push(y.query.path)},A=a=>{o.value.pageOptions.pageSize=a,h()},F=a=>{o.value.pageOptions.currentPage=a,h()};return(a,e)=>{const t=c("el-input"),u=c("el-form-item"),p=c("el-form"),n=c("form-component"),k=c("el-button"),b=c("el-card"),s=c("table-component");return G(),X("div",ne,[f("div",oe,[i(b,null,{default:m(()=>[f("div",se,[f("div",{ref_key:"formDivRef",ref:v,class:"det-form-box"},[i(n,{ref_key:"formRef",ref:q,modelValue:O.value,"onUpdate:modelValue":e[2]||(e[2]=l=>O.value=l),"form-options":z,"is-query-btn":!0,onQueryDataFn:h},{score_range1:m(({scope:l})=>[i(p,{ref:"stuScoreFormRef",model:d.value.scoreData,rules:d.value.rules},{default:m(()=>[f("div",re,[i(u,{class:"!mr-[0px]",prop:"minRange"},{default:m(()=>[i(t,{modelValue:d.value.scoreData.minRange,"onUpdate:modelValue":e[0]||(e[0]=r=>d.value.scoreData.minRange=r),style:{width:"106px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1}),le,i(u,{prop:"maxRange"},{default:m(()=>[i(t,{modelValue:d.value.scoreData.maxRange,"onUpdate:modelValue":e[1]||(e[1]=r=>d.value.scoreData.maxRange=r),style:{width:"108px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","form-options"])],512),f("div",ie,[i(k,{type:"primary",onClick:C},{default:m(()=>[Y("返回")]),_:1})])])]),_:1}),i(b,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:m(()=>[i(s,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":Z(x),onOnHandleSizeChange:A,onOnHandleCurrentChange:F},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),ce=ae(ue,[["__scopeId","data-v-741e2875"]]);export{ce as default};
