function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["static/js/scoreEdit-CtfVWjM9.js","static/js/index-B63pSD2p.js","static/css/index-Bko8je_6.css","static/css/scoreEdit-5Y48nKYX.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{aQ as U,aR as I,d as Q,m as $,i as Z,l as p,P as G,n as J,ao as K,T as C,r as h,o as b,c as g,e as q,b as v,h as _,y as f,f as E,t as X,u as F,aN as ee,g as te,aU as ae,i7 as L,Z as le,_ as re}from"./index-B63pSD2p.js";import{g as ne}from"./test-paper-management-DjV_45YZ.js";import{p as oe,g as se,a as pe}from"./common-methods-BWkba4Bo.js";import{c as ie,a as ue}from"./calculateTableHeight-BjE6OFD1.js";import{g as de}from"./user-management-B1vGxPiG.js";const ce=x=>[{label:"1",value:1},{label:"2",value:2},{label:"3",value:3},{label:"4",value:4},{label:"5",value:5},{label:"6",value:6},{label:"7",value:7},{label:"8",value:8},{label:"9",value:9},{label:"10",value:10}],_e=x=>U.request("post",I("/v1/human_mark/get_mark_record"),{data:x}),me={class:"zf-first-box"},be={class:"zf-second-box"},ve={key:0},he={key:1},ge={key:2},fe=Q({name:"score-inquiry",__name:"index",setup(x){const P=ae(()=>le(()=>import("./scoreEdit-CtfVWjM9.js"),__vite__mapDeps([0,1,2,3]))),i=$(()=>!(window.innerWidth>1600));Z();const j=p(null),o=p(null),y=p([]),k=p([]),S=p({}),H=ce(),O=p([]),M=G({column:3,labelWidth:"68px",itemWidth:"240px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",width:i.value?"200px":"220px",clearable:!0,defaultValue:"",placeholder:"请选择资格",optionData:()=>oe.value},{label:"所属科目",prop:"subject_id",type:"select",width:i.value?"200px":"220px",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>y.value},{label:"评分阶段",prop:"task_type",type:"select",clearable:!0,defaultValue:null,width:i.value?"120px":"140px",placeholder:"请选择评分阶段",optionData:()=>[{label:"正评",value:1},{label:"复评",value:3}]},{label:"轮次",prop:"round_count",type:"select",width:i.value?"60px":"100px",clearable:!0,defaultValue:null,placeholder:"请选择轮次",optionData:()=>H},{label:"评阅员",prop:"user_id",type:"select",width:i.value?"100px":"150px",defaultValue:"",placeholder:"请选择评阅员",clearable:!0,optionData:()=>O.value},{label:"试题编号",prop:"ques_code",type:"input",width:i.value?"160px":"220px",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"考生密号",prop:"stu_secret_num",type:"input",defaultValue:"",width:i.value?"160px":"200px",placeholder:"请输入考生密号",clearable:!0},{label:"评阅时间",prop:"review_time",type:"datetimerange",width:"350px",valueFormat:"YYYY-MM-DD HH:mm:ss",clearable:!0,defaultValue:"",optionData:()=>[]},{label:"评分分数",prop:"score_range",type:"doubleInput",defaultValue:[null,null],placeholder:"请输入",clearable:!0,width:"80px"}]}),u=p({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"科目",minWidth:"140px"},{prop:"task",label:"阅卷任务",minWidth:"160px"},{prop:"stu_secret_num",label:"考生密号",minWidth:"140px"},{prop:"ques_name",label:"试题名称",minWidth:"140px"},{prop:"ques_code",label:"试题编号",minWidth:"140px"},{prop:"mark_state_str",label:"评分阶段",type:"slot",minWidth:"80px"},{prop:"round_count",label:"轮次",minWidth:"50px"},{prop:"m_user_name",label:"评阅人",minWidth:"80px"},{prop:"mark_score",label:"评分分数",minWidth:"65px"},{prop:"mark_time",label:"评阅时间",type:"slot",minWidth:"150px"}],styleOptions:{isShowSort:!1,isShowSelection:!1,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),R=p([]);p([]);let W=null;const w=()=>{const{project_id:n,subject_id:t}=o.value.getAllCardData();de({current_page:1,page_size:1e7,system_user_type:2,project_id:n,subject_id:t}).then(e=>{var a;O.value=(((a=e.data)==null?void 0:a.data)||[]).map(d=>({label:d.name,value:d.user_id}))})};J(()=>{ie(W,j.value,u.value),se(),w(),m()}),K(()=>{ue(W)});const V=(n,t)=>{if(n.prop=="review_time"&&t&&t.length==2){let s=[];const e=L(t[0]).format("YYYY-MM-DD 00:00:00"),a=L(t[1]).format("YYYY-MM-DD 23:59:59");s=[e,a],o.value.setCardData("review_time",s)}n.prop==="project_id"?(y.value=[],k.value=[],o.value.getCardData("subject_id")&&o.value.setCardData("subject_id",null),o.value.getCardData("paper_id")&&o.value.setCardData("paper_id",null),t&&pe(t).then(s=>{y.value=s||[]}),w()):n.prop==="subject_id"&&(k.value=[],o.value.getCardData("paper_id")&&o.value.setCardData("paper_id",null),t&&A(),w())},A=()=>{const{project_id:n,subject_id:t}=o.value.getAllCardData();ne({project_id:n,subject_id:t,page_size:-1}).then(e=>{e.code&&e.code===200&&(e.data.data.forEach(a=>{a.label=a.paper_name,a.value=a.paper_id}),k.value=e.data.data)})},m=n=>{let{currentPage:t,pageSize:s}=u.value.pageOptions,e=o.value.getAllCardData(),a={current_page:t,page_size:s,project_id:e==null?void 0:e.project_id,subject_id:e==null?void 0:e.subject_id,task_type:e==null?void 0:e.task_type,round_count:e==null?void 0:e.round_count,ques_code:e==null?void 0:e.ques_code,user_id:e==null?void 0:e.user_id,stu_secret_num:e==null?void 0:e.stu_secret_num,mark_score_range:[]};var d=e.score_range.filter(r=>r);if(d.length>0){const[r,c]=d,Y=/^(?:[1-9]?\d|100)(?:\.\d{1,2})?$/;if(r&&!Y.test(r))return C.warning("请输入正确的分数开始值");if(r&&a.mark_score_range.push(parseFloat(r)),c&&!Y.test(c))return C.warning("请输入正确的分数结束值");c&&a.mark_score_range.push(parseFloat(c))}var l=e.review_time;l&&l.length>1&&(a.start_time=l[0],a.end_time=l[1]),_e(a).then(r=>{var c;r.code&&r.code===200?(R.value=((c=r.data)==null?void 0:c.data)||[],u.value.pageOptions.total=r.data.total):C.error(r.msg)})},T=n=>{u.value.pageOptions.pageSize=n,m()},B=n=>{u.value.pageOptions.currentPage=n,m()},D=p(!1),z=p(null),N=n=>{z.value=n,D.value=!0};return(n,t)=>{const s=h("form-component"),e=h("el-card"),a=h("el-button"),d=h("table-component");return b(),g("div",me,[q("div",be,[v(e,null,{default:_(()=>[q("div",{ref_key:"formDivRef",ref:j,class:"query-box"},[v(s,{ref_key:"formRef",ref:o,modelValue:S.value,"onUpdate:modelValue":t[0]||(t[0]=l=>S.value=l),"form-options":M,"is-query-btn":!0,onQueryDataFn:m,onOnchangeFn:V},null,8,["modelValue","form-options"])],512)]),_:1}),v(e,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:_(()=>[v(d,{"min-height":u.value.styleOptions.minHeight,"table-options":u.value,"table-data":R.value,onOnHandleSizeChange:T,onOnHandleCurrentChange:B},{mark_state_str:_(l=>[l.row.mark_state==1?(b(),g("span",ve,"正评")):f("",!0),l.row.mark_state==2?(b(),g("span",he,"试评")):f("",!0),l.row.mark_state==3?(b(),g("span",ge,"复评")):f("",!0)]),mark_time:_(l=>{var r;return[E(X((r=l.row.mark_time)==null?void 0:r.replace("T"," ")),1)]}),operation:_(l=>[F(ee)("score-inquiry/edit")?(b(),te(a,{key:0,type:"text",class:"text-btn",onClick:r=>N(l.row)},{default:_(()=>[E("编辑 ")]),_:2},1032,["onClick"])):f("",!0)]),_:1},8,["min-height","table-options","table-data"])]),_:1})]),v(F(P),{modelValue:D.value,"onUpdate:modelValue":t[1]||(t[1]=l=>D.value=l),editEntity:z.value,onSureCallBack:m},null,8,["modelValue","editEntity"])])}}}),Ce=re(fe,[["__scopeId","data-v-ad95a250"]]);export{Ce as default};
