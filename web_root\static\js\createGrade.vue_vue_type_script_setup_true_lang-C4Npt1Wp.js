import{c as A,b as H}from"./score-result-DfeCiQTg.js";import{g as O}from"./test-paper-management-DjV_45YZ.js";import{p as S,g as T,a as q}from"./common-methods-BWkba4Bo.js";import{d as L,l as r,P as M,n as P,r as _,o as g,g as m,h as s,e as x,b as j,f as b,t as I,T as v,aV as J}from"./index-B63pSD2p.js";const R={class:"w-full flex justify-end mb-1 text-[var(--el-color-primary)]"},U={class:"footer-btn"},Y=L({__name:"createGrade",emits:["queryData","afterCreateGrade"],setup(W,{expose:k,emit:B}){const y=B,h=r("生成成绩"),c=r(!1),t=r(null),i=r([]),p=r([]),C=r({}),u=M({column:3,labelWidth:"68px",itemWidth:"100%",inline:!0,rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>S.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择所属科目",optionData:()=>i.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>p.value},{label:"考生密号",prop:"stu_secret_num",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"成绩类型",prop:"grade_type",type:"radio",defaultValue:1,placeholder:"请输入考生密号",clearable:!0,optionData:()=>[{label:"AI成绩",value:1},{label:"人工阅卷成绩",value:2}]},{label:"评分成绩",prop:"score_range",type:"doubleInput",defaultValue:[null,null],placeholder:"请输入",clearable:!0,width:"194px"}]}),D=r(""),V=r("条件为空将生成全部考生的成绩"),F=a=>{c.value=!0,a&&(D.value=a),a==="clear"?(h.value="清除人工阅卷成绩",V.value="条件为空将清除全部考生的人工阅卷成绩",u.fields.forEach(e=>{e.prop==="grade_type"&&(e.isHidden=!0),e.prop==="score_range"&&(e.isHidden=!1)})):u.fields.forEach(e=>{e.prop==="score_range"&&(e.isHidden=!0)})},d=()=>{c.value=!1,t.value.resetFieldsFn(),u.fields.forEach(a=>{a.prop==="grade_type"&&(a.isHidden=!1)})};P(()=>{T()});const N=(a,e)=>{a.prop==="project_id"?(i.value=[],p.value=[],t.value.getCardData("subject_id")&&t.value.setCardData("subject_id",null),t.value.getCardData("paper_id")&&t.value.setCardData("paper_id",null),e&&q(e).then(n=>{i.value=n||[]})):a.prop==="subject_id"&&(p.value=[],t.value.getCardData("paper_id")&&t.value.setCardData("paper_id",null),e&&w())},w=()=>{const{project_id:a,subject_id:e}=t.value.getAllCardData();O({project_id:a,subject_id:e,page_size:-1}).then(l=>{l.code&&l.code===200&&(l.data.data.forEach(o=>{o.label=o.paper_name,o.value=o.paper_id}),p.value=l.data.data)})},E=()=>{let a=t.value.getAllCardData();A(a).then(e=>{e.code&&e.code===200||v.warning(e.msg)}),d(),y("afterCreateGrade")},G=()=>{J.confirm("确定清除人工阅卷成绩？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a=JSON.parse(JSON.stringify(t.value.getAllCardData()));if(delete a.grade_type,a.score_range[0]==""&&a.score_range[1]=="")delete a.score_range;else if(Number(a.score_range[0])>Number(a.score_range[1])){const e=a.score_range[0];a.score_range[0]=a.score_range[1],a.score_range[1]=e}H(a).then(e=>{e.code&&e.code===200?(y("queryData"),v.success(e.msg),d()):v.error(e.msg)})}).catch(()=>{})};return k({openDialog:F}),(a,e)=>{const n=_("form-component"),l=_("el-button"),o=_("el-dialog");return g(),m(o,{modelValue:c.value,"onUpdate:modelValue":e[1]||(e[1]=f=>c.value=f),title:h.value,width:"500px","align-center":"","close-on-click-modal":!1,"before-close":d,draggable:""},{footer:s(()=>[x("div",U,[j(l,{onClick:d},{default:s(()=>[b("取消")]),_:1}),D.value==="clear"?(g(),m(l,{key:0,type:"primary",onClick:G},{default:s(()=>[b("确定")]),_:1})):(g(),m(l,{key:1,type:"primary",onClick:E},{default:s(()=>[b("生成成绩")]),_:1}))])]),default:s(()=>[x("div",R,"提示："+I(V.value),1),j(n,{ref_key:"formRef",ref:t,modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=f=>C.value=f),"form-options":u,"is-query-btn":!1,onOnchangeFn:N},null,8,["modelValue","form-options"])]),_:1},8,["modelValue","title"])}}});export{Y as _};
