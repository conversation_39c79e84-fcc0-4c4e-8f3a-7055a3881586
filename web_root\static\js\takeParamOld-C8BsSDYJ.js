var W=(h,$,V)=>new Promise((k,T)=>{var z=m=>{try{g(V.next(m))}catch(n){T(n)}},A=m=>{try{g(V.throw(m))}catch(n){T(n)}},g=m=>m.done?k(m.value):Promise.resolve(m.value).then(z,A);g((V=V.apply(h,$)).next())});import{a as pe,b as ce,c as re,d as N,j as me,k as ve,f as he,h as ge,g as be,l as xe,m as ye,i as fe}from"./paper-sample-DNcQEVKm.js";import{S as we}from"./index-BCxZUZMh.js";import{d as Ve,m as Se,l as x,P as je,B as X,T as y,r as c,o as i,g as C,h as q,e as o,b as _,c as f,t as I,F as E,p as Y,u as Z,f as U,y as qe,ac as ke,ad as Pe,_ as <PERSON>}from"./index-B63pSD2p.js";const w=h=>(ke("data-v-9ab24c56"),h=h(),Pe(),h),Te={class:"content_root"},ze={class:"left"},Be=w(()=>o("div",{class:"_title"},"抽取范围",-1)),Ce=w(()=>o("div",{class:"_title"},"抽取数量",-1)),Ie={key:0,class:"dump_div"},Ue=w(()=>o("span",null,"每题抽",-1)),$e=w(()=>o("span",null,"份",-1)),Ae={style:{"margin-left":"10px"}},Qe={key:1,style:{height:"calc(100% - 480px)"}},Le={class:"dump_div_ai"},Me={class:"item_left"},Ne={class:"d_1"},Ee=w(()=>o("span",{class:"span_t"},"AI得分",-1)),Oe={key:0},Je={key:1},Ge={class:"d_1"},He=w(()=>o("span",{class:"span_t"},"每题抽",-1)),Ke=w(()=>o("span",null,"份",-1)),We={style:{"margin-left":"10px"}},Xe={class:"item_right"},Ye={class:"operator_div"},Ze={class:"extract_div"},Re={class:"right"},Fe=w(()=>o("div",{class:"_title"},"抽取结果",-1)),et={class:"table_wrapper"},tt=Ve({__name:"takeParamOld",props:{modelValue:{type:Boolean,default:!1},isAi:{type:Boolean,default:!1},sample_id:{type:String,default:""},query_conditions:{type:String,default:""}},emits:[""],setup(h,{emit:$}){const V=$,k=h,T=()=>{var a,e,s,u,r,p,j;let t={subject_id:(s=(e=(a=n==null?void 0:n.value)==null?void 0:a.formValue)==null?void 0:e.subject_id)!=null?s:"",ques_code:(p=(r=(u=n==null?void 0:n.value)==null?void 0:u.formValue)==null?void 0:r.bs_ques_code)!=null?p:"",answer_id_list:P.value,query_conditions:JSON.stringify({formValue:((j=n==null?void 0:n.value)==null?void 0:j.formValue)||{},aiConditions:v.value})};ge(t).then(b=>{if(b.code!=200)return y.error(`接口异常，${b.msg}`);y.success("保存成功"),V("saveSuccess")})},z=Se({get:()=>k.modelValue,set:t=>V("update:modelValue",t)}),A=x([{label:"所属资格",required:!0,placeholder:"请选择资格",source:pe,extraParam:{page_size:-1},type:"select",prop:"project_id",mappingField:{label:"project_name",value:"project_id"}},{label:"所属科目",required:!0,placeholder:"请选择科目",isMultiple:!1,source:ce,upProp:"project_id",type:"select",prop:"subject_id",mappingField:{label:"subject_name",value:"subject_id"}},{label:"场次",placeholder:"请选择场次",isMultiple:!1,source:re,type:"select",prop:"district"},{label:"考区",placeholder:"请选择考区",isMultiple:!1,upProp:["project_id","subject_id"],source:N,extraParam:{place_type:1},type:"cascader",prop:"exam_area_code",mappingField:{label:"name",value:"code"},cascaderProps:{checkStrictly:!0,emitPath:!1,value:"code",label:"name"}},{label:"考点",placeholder:"请选择考点",isMultiple:!1,source:N,extraParam:{place_type:2},upProp:["project_id","subject_id","exam_area_code"],type:"select",prop:"exam_point_code",mappingField:{label:"exam_point_name",value:"exam_point_code"}},{label:"考场",placeholder:"请选择考场",isMultiple:!1,source:N,extraParam:{place_type:3},upProp:["project_id","subject_id","exam_area_code","exam_point_code"],type:"select",prop:"exam_room_code",mappingField:{label:"exam_room_name",value:"exam_room_code"}},{label:"所属题型",placeholder:"请选择题型",required:!0,isMultiple:!1,source:me,upProp:["project_id","subject_id"],type:"select",prop:"bs_quesType_id",mappingField:{label:"ques_type_name",value:"business_id"}},{label:"试题编号",placeholder:"请选择试题编号",required:!0,isMultiple:!1,source:ve,upProp:["project_id","subject_id","bs_quesType_id"],type:"select",prop:"bs_ques_code",mappingField:{label:"ques_code",value:"ques_id"},extraParam:{read_state:1,format_by_ques_type:!1,is_paper:!1},getActualParam:(t,a)=>{let e=a.bs_quesType_id;if(e&&e.length>0){let s=e.find(u=>u.business_id==(t==null?void 0:t.bs_quesType_id));if(s)return{business_id:s==null?void 0:s.business_id,ques_type_code:s==null?void 0:s.ques_type_code}}}}]),g=je({dumpQuesCount:0,canDumpTotal:100}),m=[{prop:"exam_session",label:"场次",width:80},{prop:"exam_area_name",label:"考区",width:220},{prop:"exam_point_name",label:"考点",width:220},{prop:"exam_room_code",label:"考场",width:180},{prop:"ques_type_name",label:"题型",width:200},{prop:"ques_code",label:"试题编号",width:200},{prop:"stu_secret_num",label:"考生密号",width:200},{prop:"ques_score",label:"题分数",width:100},{prop:"set_std_score",label:"Ai分数"}],n=x(null),R=t=>{let a=[];return t.operator=="7"?(a.push(6),a.push(t.minVal),a.push(t.maxVal)):(a.push(t.operator),a.push(t.value)),a},F=()=>v.value.map(t=>{let a=[];return t.operator=="7"?(a.push(6),a.push(t.minVal),a.push(t.maxVal)):(a.push(t.operator),a.push(t.value)),a}),O=(t,a)=>{var u,r;let e=(u=n==null?void 0:n.value)==null?void 0:u.formValue,s={project_id:e==null?void 0:e.project_id,subject_id:(r=e==null?void 0:e.subject_id)!=null?r:"",exam_session_list:e!=null&&e.district?[e==null?void 0:e.district]:[],exam_area_code_list:e!=null&&e.exam_area_code?[e==null?void 0:e.exam_area_code]:[],exam_point_code_list:e!=null&&e.exam_point_code?[e==null?void 0:e.exam_point_code]:[],exam_room_code_list:e!=null&&e.exam_room_code?[e==null?void 0:e.exam_room_code]:[],ques_type_code_list:e!=null&&e.bs_quesType_id?[e==null?void 0:e.bs_quesType_id]:[],ques_code_list:e!=null&&e.bs_ques_code?[e==null?void 0:e.bs_ques_code]:[""]};return t=="single"?(s.number_list=[],s.score_list=[R(a)]):(s.number_list=v.value.map(p=>p.dumpQuesCount),s.score_list=F()),s},v=x([{dumpQuesCount:null,operator:"1",minVal:null,maxVal:null,value:null}]),ee=be(),te=()=>{v.value.push({dumpQuesCount:null,operator:"1",minVal:null,maxVal:null,value:null})},ae=t=>{v.value.splice(t,1)},Q=t=>t?(t=t.replace(/\D/g,""),t=t.replace(/^0+(\d)/,"$1"),t):"",J=t=>{if(t.operator=="7"){if(t.minVal||!t.maxVal)return}else if(!t.value)return;let a=O("single",t);xe(a).then(e=>{if(e.code!=200)return y.error(`接口异常，${e.msg}`);t.canDumpTotal=e==null?void 0:e.data.data})},se=()=>{let t=!0,a="";var e=v.value.filter(s=>{let u=!0;return s.operator=="7"?(!s.minVal||!s.maxVal)&&(u=!1,a="请输入AI得分区间最大值和最小值"):s.value||(u=!1,a="请输入AI得分的值"),s.dumpQuesCount||(u=!1,a="请输入抽取题数"),!u});return e&&e.length>0&&(t=!1,y.warning(a)),t},S=x(null),le=()=>W(this,null,function*(){(yield n==null?void 0:n.value.IsFormValid())&&se()&&oe()}),P=x([]),oe=()=>{var a,e;M.value=2,D.value=[];let t=O(null,null);t.current_page=((a=S.value)==null?void 0:a.currentPage)||1,t.page_size=((e=S.value)==null?void 0:e.pageSize)||10,ye(t).then(s=>{var u,r,p,j,b,B;if(s.code!=200)return y.error(`接口异常，${s.msg}`);s.data.answer_ids=(u=s.data)==null?void 0:u.answer_ids,D.value=(r=s.data)==null?void 0:r.data,P.value=(p=s.data)==null?void 0:p.answer_ids,S.value.total=(B=(b=(j=s.data)==null?void 0:j.answer_ids)==null?void 0:b.length)!=null?B:0})},D=x([]),L=()=>{D.value=[];const t=ne();if(t.length==0)return y.error("未获取到作答Id集合");fe({answer_id_list:t}).then(e=>{var s;if(e.code!=200)return y.error(`接口异常，${e.msg}`);D.value=((s=e.data)==null?void 0:s.data)||[]})};function ne(){var t=S.value.currentPage,a=S.value.pageSize;const e=(t-1)*a,s=e+a;return P.value.slice(e,s)||[]}const ue=t=>{L()},de=t=>{L()},M=x(1),G=x(null);return X(()=>k.sample_id,t=>{t&&(M.value=2,he({sample_id:t}).then(a=>{var e,s;if(a.code!=200)return y.error(`接口异常，${a.msg}`);P.value=(((e=a.data)==null?void 0:e.data)||[]).map(u=>u.answer_id),S.value.total=(s=P.value.length)!=null?s:0,L()}))},{immediate:!0}),X(()=>k.query_conditions,t=>{if(!t)return;let a=JSON.parse(t);G.value=a==null?void 0:a.formValue,v.value=a==null?void 0:a.aiConditions},{immediate:!0,deep:!0}),(t,a)=>{const e=c("DynamicFormComponent"),s=c("el-input-number"),u=c("el-option"),r=c("el-select"),p=c("el-input"),j=c("Delete"),b=c("el-icon"),B=c("Plus"),H=c("el-button"),_e=c("CommonTableComponent"),ie=c("FullDialogComponent");return i(),C(ie,{modelValue:z.value,"onUpdate:modelValue":a[1]||(a[1]=l=>z.value=l),title:"试卷抽样",onCloseDialog:t.closeDialog,onOnSure:T},{content:q(()=>[o("div",Te,[o("div",ze,[Be,_(e,{list:A.value,editEntity:G.value,ref_key:"formFilterRef",ref:n},null,8,["list","editEntity"]),Ce,h.isAi?(i(),f("div",Qe,[o("div",Le,[(i(!0),f(E,null,Y(v.value,(l,K)=>(i(),f("div",{class:"ai_item",key:K},[o("div",Me,[o("div",Ne,[Ee,_(r,{modelValue:l.operator,"onUpdate:modelValue":d=>l.operator=d,size:"small",style:{width:"90px"}},{default:q(()=>[(i(!0),f(E,null,Y(Z(ee),d=>(i(),C(u,{key:d.value,label:d.text,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),l.operator==7?(i(),f("span",Oe,[_(p,{style:{width:"48px"},modelValue:l.minVal,"onUpdate:modelValue":d=>l.minVal=d,size:"small",onInput:d=>l.minVal=Q(l.minVal)},null,8,["modelValue","onUpdate:modelValue","onInput"]),U(" - "),_(p,{style:{width:"48px"},modelValue:l.maxVal,"onUpdate:modelValue":d=>l.maxVal=d,size:"small",onInput:d=>l.maxVal=Q(l.maxVal),onBlur:d=>J(l)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])])):(i(),f("span",Je,[_(p,{style:{width:"100px"},modelValue:l.value,"onUpdate:modelValue":d=>l.value=d,size:"small",onInput:d=>l.value=Q(l.value),onBlur:d=>J(l)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"])]))]),o("div",Ge,[He,_(s,{modelValue:l.dumpQuesCount,"onUpdate:modelValue":d=>l.dumpQuesCount=d,min:1,max:l.canDumpTotal,size:"small",style:{width:"90px"}},null,8,["modelValue","onUpdate:modelValue","max"]),Ke,o("span",We,"可抽量："+I(l.canDumpTotal),1)])]),o("div",Xe,[v.value.length>1?(i(),C(b,{key:0,class:"pointer",onClick:d=>ae(K),title:"删除"},{default:q(()=>[_(j)]),_:2},1032,["onClick"])):qe("",!0)])]))),128))]),o("div",Ye,[_(H,{type:"text",onClick:te},{default:q(()=>[_(b,null,{default:q(()=>[_(B)]),_:1}),U(" 添加 ")]),_:1})])])):(i(),f("div",Ie,[Ue,_(s,{modelValue:g.dumpQuesCount,"onUpdate:modelValue":a[0]||(a[0]=l=>g.dumpQuesCount=l),min:1,max:g.canDumpTotal,size:"small",style:{width:"100px"}},null,8,["modelValue","max"]),$e,o("span",Ae,"可抽量："+I(g.canDumpTotal),1)])),o("div",Ze,[_(H,{type:"primary",onClick:le,style:{width:"70%"}},{default:q(()=>[U("开始抽取")]),_:1})])]),o("div",Re,[M.value==1?(i(),C(Z(we),{key:0,title:"抽取的样本",stepList:["选择左侧试卷抽样条件","点击【开始抽取】按钮，抽取试卷抽样数据"]})):(i(),f(E,{key:1},[Fe,o("div",et,[_(_e,{ref_key:"extractTableRef",ref:S,onSizeChange:ue,onCurrentPageChange:de,columns:m,"show-pagination":!0,autoLoad:!1,data:D.value,showIndex:!0,"row-key":"project_id",border:"",stripe:"",height:"100%"},{"column-exam_area_name":q(({row:l})=>[U(I(l.exam_province_name)+"/"+I(l.exam_city_name),1)]),_:1},8,["data"])])],64))])])]),_:1},8,["modelValue","onCloseDialog"])}}}),nt=De(tt,[["__scopeId","data-v-9ab24c56"]]);export{nt as default};
