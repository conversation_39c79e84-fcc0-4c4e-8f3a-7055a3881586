function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["static/js/pieChart-B3FQM8sj.js","static/js/index-C9GYnvBh.js","static/js/index-B63pSD2p.js","static/css/index-Bko8je_6.css","static/js/multiLineChart-3u8d6ahe.js","static/css/multiLineChart-CLsMXBpf.css","static/js/barChart-BwsIMmQR.js","static/css/barChart-Cs3F8eFd.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
var Pe=Object.defineProperty,Ke=Object.defineProperties;var Ne=Object.getOwnPropertyDescriptors;var le=Object.getOwnPropertySymbols;var ze=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable;var ie=(t,l,r)=>l in t?Pe(t,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[l]=r,B=(t,l)=>{for(var r in l||(l={}))ze.call(l,r)&&ie(t,r,l[r]);if(le)for(var r of le(l))Le.call(l,r)&&ie(t,r,l[r]);return t},w=(t,l)=>Ke(t,Ne(l));var H=(t,l,r)=>new Promise((I,N)=>{var M=a=>{try{z(r.next(a))}catch(V){N(V)}},j=a=>{try{z(r.throw(a))}catch(V){N(V)}},z=a=>a.done?I(a.value):Promise.resolve(a.value).then(M,j);z((r=r.apply(t,l)).next())});import{aC as ve,aQ as h,aR as D,d as Ee,x as Ue,l as x,P as re,n as Ye,an as Ie,m as ce,r as F,o as y,c as U,e as o,f as de,t as T,u as C,b as i,h as c,aT as W,w as Ve,F as _e,p as ue,g as K,z as Se,aU as Z,C as Y,ac as Je,ad as Oe,Z as $,_ as qe}from"./index-B63pSD2p.js";const Ge="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFQAAABRCAYAAABMpoFyAAAAAXNSR0IArs4c6QAACdBJREFUeF7tnN9zE9cVx79X2pVkCyNhm/hn/YvfdEzsUBI7hcbOJExm8gB97MRMcPMH1H5uO4G+daYzcZ7azrQIhnT6GPKWAkOc2iGUtsFOJukUEpAJBhtjYzv6ZevH7dwrrbuWdlcraVdat7tPntXZs/d+9px7zj33XhPYl6EEiKHabGWwgRpsBDZQG6jBBAxWZ1uoDdRgAgarsy3UBmowAYPV2RZqAzWYgMHqbAu1gRpMwGB1toXaQA0mYLA6Uy30tT/TjmQifoKADDgEYfTDn5Cgwe23nDpTgHKQyfj7BKRno8cUZy+fEs9YjoDBDTIF6Kt/oj2EJm7J25qiGLx6Shw3uP2WU2cKUNbL4++tnwfIm1KPbaA6v/1AgPpFMf4RoSR4+ZT4Y+kxdt8lxMdByLPsng1UB9ANmJmxMhsac33QxDgBfJQIvVfeIFM61G5pkZJc/vjF9SnJAhkFCoxfGRIH5USOX0ycTJFUx9Uh19iWJqWz8SUBffW9+DKzvv/H4KPGtySgzOUFMX6aACMEpD3zkg8uD4kndX7Q/zmxkoDKabxyMT7gIBihhFy68oZwvtyk6K0BP+LrHeT56xUdp4sCyoJNKQEmGkAHKLhFV/0UH2fDjwYwkAROAOjx7Gz/xlnb0giHwwtgGYReIi98ckF6ht58sQcp5/sAOjL3gqA4S/onyv5R2fsLBnr8YvwMCN4GMLYeF86OD5NlvdZIA/BHgHcAnN4AwiABo9uGwQEs/g5jHjd+xv4WG7og1rYoqT9P+iaGH/+x+ahYU/0Xf1tTdY4QxXAloBYBVB7ZaTBFybDeGVA4ADZ7+u90VEbBAQwuPkFPJIJ3mloB144GuJr3qn8riuH7f/viF9HV0K49g0fgEIVcWRrvJP03ylo/KAgoT9bFxFOFXo5dHhJHtSw1HAALVMw1FS8KLC88wmo0irbt24HGw91wVvs1jX/m+nQi8nRVqGmoQ+sPDirI0nHSN7kpjdPrTcXKFQQ0E3g+yn4ZBVauDImavQ8HwAojbKhQvSJhYGE+/fP+1/pBnApWJ3s6thLCvcl0yaC9rxvVdQpNoHSQ9E+WrYZQENB0OS4hDfZThI9/ZMohOKfylebm38X5FMWb1V5A0OD0eA6IRgCX1wPidMJb50NtZwvEKo/ih5j/8hssBR9CrHKj69hzCq5Pp0jfZG+xFlfocwUBLVS5XP7+r/GHUAhvsXveGqC2DnA4cjUmEsCjbwHXNi9i34W5ABsfGw92wdfakPNAKp7AnWs3kUokUb+nDTv3SumwTLSMAcp0oCyyhylOEIKO+Tn8PBaByLrKrJQFHzlU5uJCbTNQVYdUCoithLH8YB6RxRVOp+FgF7fW7Ou7uSd48M9/8dudR3vh8W3LFlmG29lJesd1ZyTFGo+pQEMB9JB0IJJyRKyvAQtzQCIJeDxAQ3O66UR0w9N+CETMdW3JrTWAYebG5xx8da0P7f2HcnlQepb0TyoWuFkua9SEwDSgLHlPArcIkBMpGNT5h7ykh/qd6SHA1bQXgj/XpSUy3/7jK4TmF+He7uVjZfYVj8Zw96+fcddXseRl0HivlEbxmdVaclNODCAIglHywsQly1loJIARmk7iFa/lJWBlOe3yLW2Ad792VNczVi7cnsGTO/f5mKuYm1JcIP0TfFKxdvW5L13bvEq5FiubFT0pMM1CQ+cwTghe0vrSszNp12d5Z8uxY3mNYuXBPB5O3+bAuo71Kkb+uxOfYW01DNXclNLBR9N3hiihbzU/u0/9nUVOCioKNBYF5h+l+3Tg9fxAmZw0VqoBiywuY+bGF1yncm5Kx4PXP98ffbraqBLAMpBTo6Tvk4JruKYBXfwtfgWKXwqidt7JxtJYDNjeUAsKwi3L9z31sVRPMp8vN52d+vf66uxjl2oAY0gp/Zj0Tw7kdZssAdOAPv0N2udWEaQ0Hc19OwBPVW7zWN45ex8QPC4kYutcgCXprYcPKqU//Hc5sN0vP5+jlI23zPXj0TXFAMUC2NfX/q5hxRYEGj2Hlx4v4PfhEDYGKimiywmw3DPmfAbJlABKKZbuPeSRml2thw+gprFeEVi+ZF7KTdV0SAGMfTylj2IZCw0H8DblFfx0usQscOlJejrJrqYWwOVO/y34GniJTj5nZ9Y1O32bp0iq0RqAngCl5a7yrEFx/LaCy4cCGCNI1zKzL2mOLrqA5tZ0Il+1O9ddpefyRmtZgPK1PgPNiK1Clrl+cj2hPLRUGihL5FPAPTWrYFNJliaxZN7nB3YeUC0ecxV6go8ko5bsFxpQNslXGmgoALZYF9DqhLw8t+dH3RBqtOud+YIPexezMqcgKBeYSyFaaaB66p08QmfSpM4Xu+HZoQ1UPs6pFUZKYab5bKWB6rFQKUixNKnp0F74NfJNqbNL92Yx/9VdzQBlCtRKA82U6YKEbN74oNRZNo9fo150Hs0tcijJf33tJs8pazua0fD9Xabwy1FaaaCsQXqtlMmyAFW3T7m+md05+XRy98tHVKv3hpK2AlAOVUdRhMmxEt7KqoC2Hx7Ju3bE5KXynfb820CklgGaLipv2myr1U2xthligz43ZhFdbW3JQJRpVVYBytoSPofzINjYbJuvs57OXjg8OcsW+R4z93crAS0kQDEqzmof3O0KyxbmItPWbiWgrKX5KvbZvcm3BFJ2tlYDmnH9KRDwLeH5LlYg8ezSF6Dy6TLkdysCZbvoUkDOThO1DhcSoAyBpqXEikC3dICyKtAtG6CsCnTLBigrAy1kBsVkLRKgPiB9EwWfFTBtkS57vM9sy9E9g2LLI5obbs2OShpbd7ReXTag3Eo1lkiUGulu64bTq10zNY3rVgBaaIByeLzwdOor8RkOtsiNumW1UJ5G5dkang1G4+CC4Qw3KXQ7dxSz/bHsQIsKUB29IC7lHcymUJVtKitUf0WA5lshze6Es6YO7lbljXKFdliXfJHuzjMUXS8wQUjvop706vIFKDpN+iYVj/7owVAxoPwQGAUrnihsis9ter6NEXo6q0vGkewtZTdzxYBm0qgeUL6PdNOJZrWOm5+b0ndJ3+SILvAqQhUFWkzUN61uWkIgkrOtONCMpebddSJvtPGpFJ2GWxgoJk3KNlRLAJXcn1C+FqWrIK20c684Vy3dzS1nofIGsehPKUb0jKssUIn17ZqnR1QhU8qOlZ8x+tiiZSw0u+N8RkVxGoSfm1e/KC4I/voPXc37GkHJSRCidVCCnSAbB6VjRoOUGmhZoHKC/B8WgG+X9FPA7wSCHmCKDPOz9jkX/fRoem88oel8krL/xpMIluOo95YAWtzYWJmnbKAGc7eB2kANJmCwOttCbaAGEzBY3X8AJNbdfwg5E3kAAAAASUVORK5CYII=",He="/static/png/first-C4tL7qsU.png",Me="/static/png/second-BdOuzpzp.png",je="/static/png/third-ufRKkg_J.png";ve();const Xe=(t,l=!0)=>h.request("post",D("/v1/human_mark/get_my_human_task_list"),{data:t},{},l),Qe=(t,l=!0)=>h.request("post",D("/v1/official_mark/get_round_by_task"),{data:t},{},l),We=t=>h.request("post",D("/v1/score_analysis/get_effective_review_ranking"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>({success:!0,data:[{rank:1,user_id:"125269182531046473728",name:"王宇",id_card_last_six:"473728"},{rank:2,user_id:"125269192062115774464",name:"宁肖",id_card_last_six:"774464"},{rank:3,user_id:"125271552852362526720",name:"张本青",id_card_last_six:"526720"},{rank:4,user_id:"125271557074047991808",name:"张洋",id_card_last_six:"991808"},{rank:5,user_id:"125271560936564457472",name:"陈浩",id_card_last_six:"457472"},{rank:6,user_id:"125271564859748384768",name:"刘峰",id_card_last_six:"384768"}],code:200})),Ze=t=>{const l=w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0});return Promise.all([h.request("post",D("/v1/score_analysis/calculate_reviewed_count"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_effective_review_count"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_invalid_reviewed_count"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_arbitration_count"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_average_speed"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_average_score"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_max_score"),{data:l},{},!1),h.request("post",D("/v1/score_analysis/calculate_min_score"),{data:l},{},!1)]).then(r=>({reviewed_count:140,effective_review_count:128,invalid_reviewed_count:15,arbitration_count:11.4,average_speed:40,average_score:"15.0",max_score:"25.0",min_score:"0.0"})).catch(r=>(console.error(r),{reviewed_count:"-",effective_review_count:"-",invalid_reviewed_count:"-",arbitration_count:"-",average_speed:"-",average_score:"-",max_score:"-",min_score:"-"}))},$e=t=>h.request("post",D("/v1/score_analysis/calculate_arbitration_pie_chart"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>({success:!0,data:{quality_data:{x_data:["评阅通过","仲裁"],y_data:[{value:124,name:"评阅通过"},{value:16,name:"仲裁"}]}},code:200})),ea=t=>h.request("post",D("/v1/score_analysis/calculate_score_distribution"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>({success:!0,data:{legend:["分数分布"],x_data:["1-5","6-10","11-15","16-20","21及以上"],y_data:[3,15,52,51,19]},code:200})),aa=t=>h.request("post",D("/v1/score_analysis/calculate_workload"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>{const l=t.mark_time;let r;return l?r={x_data:["8-9","9-10","10-11","11-12","12-13","13-14","14-15","15-16","16-17","17-18","18-19"],y_data:[28,42,37,33,null,null,null,null,null,null,null]}:r=[{date:"2025-08-18",count:140}],{success:!0,data:r,code:200}}),ta=t=>h.request("post",D("/v1/score_analysis/calculate_cumulative_average_score_with_time"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>{const l=t.mark_time;let r;return l?r=[{time:"08:20:00",person_average_value:13.8,group_average_value:12.7},{time:"08:40:00",person_average_value:16.4,group_average_value:15.3},{time:"09:20:00",person_average_value:15.2,group_average_value:14.7},{time:"09:40:00",person_average_value:13.4,group_average_value:16.1},{time:"10:20:00",person_average_value:15.8,group_average_value:14.6},{time:"10:40:00",person_average_value:13.9,group_average_value:14.6},{time:"11:20:00",person_average_value:14.8,group_average_value:16.2},{time:"11:40:00",person_average_value:15.8,group_average_value:14.2}]:r=[{date:"2025-08-18 08:05:00",person_average_value:13.8,group_average_value:12.7},{date:"2025-08-18 08:52:00",person_average_value:16.4,group_average_value:15.3},{date:"2025-08-18 09:33:00",person_average_value:15.2,group_average_value:16.7},{date:"2025-08-18 10:08:00",person_average_value:16.4,group_average_value:16.2},{date:"2025-08-18 11:42:00",person_average_value:13.2,group_average_value:14.1}],{success:!0,data:r,code:200}}),sa=t=>h.request("post",D("/v1/score_analysis/record_spy_paper_score"),{data:w(B({},t),{date_range:t.date_range?t.date_range.join("至"):void 0})},{},!1).then(()=>({success:!0,data:{legend:["当前分数","上次分数","差异数据"],x_data:["第1次","第2次","第3次","第4次"],y1_data:[14.5,13.8,15.6,null],y2_data:[14.2,14.5,13.4,null],y3_data:[.3,-.7,-2.2,null]},code:200})),k=t=>(Je("data-v-d64e186b"),t=t(),Oe(),t),oa={class:"dashboard"},na={class:"top bg-[#fff] dark:bg-black"},la={class:"t_1"},ia=k(()=>o("img",{src:Ge,alt:"Image",class:"handleImg"},null,-1)),ra={class:"search_div"},ca={class:"flex-container"},da={class:"left-content"},_a={class:"card-item"},ua={class:"card_num"},va={class:"card-value"},fa=k(()=>o("div",{class:"card-title"},"已阅量",-1)),ga={class:"card-item"},ma={class:"card_num"},pa={class:"card-value"},ha=k(()=>o("div",{class:"card-title"},"有效评卷量",-1)),Da={class:"card-item"},ya={class:"card_num"},ba={class:"card-value"},xa=k(()=>o("div",{class:"card-title"},"无效评卷量",-1)),ka={class:"card-item"},Aa={class:"card_num"},Ba={class:"card-value"},Ca=k(()=>o("div",{class:"card-title"},"仲裁率(%)",-1)),wa={class:"card-item"},Fa={class:"card_num"},Ta={class:"card-value"},Ra=k(()=>o("div",{class:"card-title"},"平均速度(份/时)",-1)),Pa=k(()=>o("div",{class:"section-title",style:{margin:"10px 10px 4px 0px"}},"分数分布",-1)),Ka={class:"chart-placeholder fenshuChart"},Na={class:"abs_div"},za=k(()=>o("div",{class:"section-title"},"评分稳定性",-1)),La={class:"pingFenLine"},Ea={style:{height:"100%"}},Ua={class:"right-content"},Ya={class:"score-info"},Ia={class:"score-item"},Va={class:"score_val"},Sa=k(()=>o("div",{class:"score_t"},"平均分",-1)),Ja={class:"score-item"},Oa={class:"score_val"},qa=k(()=>o("div",{class:"score_t"},"最高分",-1)),Ga={class:"score-item"},Ha={class:"score_val"},Ma=k(()=>o("div",{class:"score_t"},"最低分",-1)),ja=k(()=>o("div",{class:"section-title mg_bottom"},"阅卷员排名",-1)),Xa={class:"table_div"},Qa={key:0,class:"tableIcon",src:He},Wa={key:1,class:"tableIcon",src:Me},Za={key:2,class:"tableIcon",src:je},$a={key:3},et={class:"fullscreen-chart-wrapper"},at=k(()=>o("template",null,null,-1)),tt=Ee({__name:"reviewerHomePage",setup(t){const{name:l}=Ue(),r=Z(()=>$(()=>import("./pieChart-B3FQM8sj.js"),__vite__mapDeps([0,1,2,3]))),I=Z(()=>$(()=>import("./multiLineChart-3u8d6ahe.js"),__vite__mapDeps([4,1,2,3,5]))),N=Z(()=>$(()=>import("./barChart-BwsIMmQR.js"),__vite__mapDeps([6,2,3,7]))),M=ve(),j=x([{prop:"rank",label:"排名",width:80,align:"center"},{prop:"name",label:"阅卷员",width:130,align:"center"},{prop:"id_card_last_six",label:"有效评卷量",align:"center"}]),z=x([{rank:1,name:"王宇",count:15},{rank:2,name:"宁肖",count:13},{rank:3,name:"张本青",count:12},{rank:4,name:"张洋",count:11},{rank:5,name:"陈浩",count:9},{rank:6,name:"刘峰",count:8}]),a=re({stataic:{assignedFinish:234,effectiveMarking:23,noeffectiveMarking:12,arbitrationRate:12,averageSpeed:28,average_score:null,max_score:null,min_score:null},leftCenterData:{zhongcaiData:[],zhijianData:[{value:40,name:"质检通过"},{value:20,name:"质检不通过"}]},leftCenterTabActive:"zhongcai",leftBottomActiveTab:"gongzuoliang",leftBottomData:[{title:"工作量监控",tabName:"gongzuoliang",xData:["8-9","9-10","10-11","11-12","12-13","13-14","14-15","15-16","16-17","17-18","18-19"],yData:[{name:"份数",label:{show:!0,position:"top",color:"#409EFF",formatter:"{c}份"},data:[14,28,23,2,18,12,23,18,22,5,21],color:"#409EFF"}],yAxis:{axisLabel:{formatter:"{value}份"}}},{title:"评分一致性",tabName:"pingfenyizhi",xData:["08:00","09:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00"],yData:[{name:"评阅员平均分",type:"line",data:[1,2,4,5,2,5,2,2,3,5,2]},{name:"小组平均分",type:"line",data:[3,5,4,2,2,4,5,2,2,3,3]}],unitText:"分"},{title:"评阅稳定性",tabName:"pingfenwending",xData:["第一次","第二次","第三次","第四次","第五次"],yData:[{name:"当前分数",type:"line",data:[3,2,2,5,2,5,2,2,3,5,2]},{name:"上次分数",type:"line",data:[3,5,4,4,2,4,5,2,2,3,3]},{name:"差异数据",type:"line",data:[0,3,2,1,0,1,3,0,1,2,1]}],unitText:"分"}],fenshuFenBu:{xData:["1分","2分","3分","4分","5分"],series:[{data:[100,140,230,100,130],color:"#409EFF",name:"人数",label:{show:!0,position:"top",color:"#409EFF"}}]}});Ye(()=>{V()});const V=()=>{ye(),be(),Ae(),ne(),we()};Ie(()=>{});const ee=x(null),fe=x(null),ge=n=>H(this,null,function*(){var e,d;yield Y(),n.paneName=="zhongcai"?(yield Y(),(e=ee.value)==null||e.resizeChart()):(yield Y(),(d=fe.value)==null||d.resizeChart())}),ae=x([]),me=n=>H(this,null,function*(){var d;yield Y();let e=a.leftBottomData.findIndex(v=>v.tabName==n.paneName);e!=-1&&(yield Y(),(d=ae.value[e-1])==null||d.resizeChart())}),J=x(null),O=x(null),pe=x(null),te=x(null),X=n=>{let e=parseInt(n);if(n=="1")b.title="仲裁情况";else if(n=="2")b.title="分数分布";else if(n=="3"){let d=a.leftBottomData.findIndex(v=>v.tabName==a.leftBottomActiveTab);d!=-1&&(e+=d),b.title="评分稳定性"}b.type=e,b.visible=!0},b=re({visible:!1,type:"pie",title:""}),se=()=>{b.visible=!1},he=()=>{Y(()=>H(this,null,function*(){var n;b.type=="1"&&J.value&&J.value.resizeChart?J.value.resizeChart():b.type=="2"&&O.value&&O.value.resizeChart?O.value.resizeChart():(yield Y(),(n=te.value)==null||n.resizeChart())}))},S=ce(()=>{let n=a.leftBottomData.findIndex(e=>e.tabName==a.leftBottomActiveTab);return n!=-1?a.leftBottomData[n]:null}),De=x([{label:"评卷任务",required:!1,placeholder:"请选择评卷任务",source:Xe,type:"select",prop:"task_id",extraParam:{task_type:1},mappingField:{label:"task_name",value:"task_id"},width:180},{label:"轮次",required:!1,placeholder:"请选择轮次",source:Qe,upProp:"task_id",type:"select",prop:"round_id",mappingField:{label:"round_count",value:"round_id"},width:120},{label:"阅卷时间",required:!1,placeholder:"请选择阅卷时间",type:"dateRange",prop:"makingTime",dateProps:{startPlaceholder:"开始日期",endPlaceholder:"结束日期",valueFormat:"YYYY-MM-DD"},width:300}]),oe=x(null),L=ce(()=>{var e;let n=(e=oe.value)==null?void 0:e.formValue;return w(B({},n),{round_count:"1",task_type:1,round_id:"125269968669785718784",task_id:"125269968669785718785",user_id:M.userId})}),ye=()=>{let n=L.value;Ze(n).then(e=>{a.stataic.assignedFinish=e==null?void 0:e.reviewed_count,a.stataic.effectiveMarking=e==null?void 0:e.effective_review_count,a.stataic.noeffectiveMarking=e==null?void 0:e.invalid_reviewed_count,a.stataic.arbitrationRate=e==null?void 0:e.arbitration_count,a.stataic.averageSpeed=e==null?void 0:e.average_speed,a.stataic.average_score=e==null?void 0:e.average_score,a.stataic.max_score=e==null?void 0:e.max_score,a.stataic.min_score=e==null?void 0:e.min_score})},be=()=>{xe(),ke()},xe=()=>{a.leftCenterData.zhongcaiData=[],$e(L.value).then(n=>{var e,d;n.code===200&&(a.leftCenterData.zhongcaiData=(d=(e=n.data)==null?void 0:e.quality_data)==null?void 0:d.y_data)})},ke=()=>{a.fenshuFenBu.xData=[],a.fenshuFenBu.series[0].data=[],ea(L.value).then(n=>{var e,d,v,_,f;n.code===200&&(a.fenshuFenBu.xData=(v=(d=(e=n.data)==null?void 0:e.x_data)==null?void 0:d.map(m=>`${m}分`))!=null?v:[],a.fenshuFenBu.series[0].data=(f=(_=n.data)==null?void 0:_.y_data)!=null?f:[])})},Ae=()=>{z.value=[],We(L.value).then(n=>{n.code===200&&(z.value=n.data||[])})},q=x("-1"),G=x([{label:"全部",value:"-1"}]),ne=()=>{Be(),Ce()},Be=()=>{a.leftBottomData[0].xData=[],a.leftBottomData[0].yData[0].data=[];const n=q.value;aa(w(B({},L.value),{mark_time:n==="-1"?void 0:n})).then(e=>{var d,v,_,f,m,g,A,R,u,P;if(e.code===200)if(n==="-1"){G.value=[{label:"全部",value:"-1"}];const E=[];((v=(d=e.data)==null?void 0:d.map(p=>p.date))!=null?v:[]).forEach(p=>{const Q=new Date(p),Te=("0"+(Q.getMonth()+1)).slice(-2),Re=("0"+Q.getDate()).slice(-2);E.push({label:`${Te}月${Re}日`,value:p,time:Q.getTime()})}),G.value=G.value.concat(E),a.leftBottomData[0].xData=(f=(_=e.data)==null?void 0:_.map(p=>p.date.slice(-5).replace("-","/")))!=null?f:[],a.leftBottomData[0].yData[0].data=(g=(m=e.data)==null?void 0:m.map(p=>p.count))!=null?g:[]}else a.leftBottomData[0].xData=(R=(A=e.data)==null?void 0:A.x_data)!=null?R:[],a.leftBottomData[0].yData[0].data=(P=(u=e.data)==null?void 0:u.y_data)!=null?P:[]})},Ce=()=>{a.leftBottomData[1].xData=[],a.leftBottomData[1].yData[0].data=[],a.leftBottomData[1].yData[1].data=[];const n=q.value;ta(w(B({},L.value),{mark_time:n==="-1"?void 0:n})).then(e=>{var d,v,_,f,m;if(e.code===200)if(n==="-1")a.leftBottomData[1].xData=(v=(d=e.data)==null?void 0:d.map(g=>g.date))!=null?v:[],a.leftBottomData[1].yData[0].data=((_=e.data)==null?void 0:_.map(g=>g.person_average_value||0))||[],a.leftBottomData[1].yData[1].data=((f=e.data)==null?void 0:f.map(g=>g.group_average_value||0))||[];else{const g=(m=e.data)!=null?m:[],A=[];g.forEach(u=>{u.dateTime=new Date(`${n}${u.time}`).getTime(),u.splitTime=`${u.time.substr(0,2)}:${u.time.substr(3,2)<=30?"00":"30"}`}),g.sort((u,P)=>u.dateTime-P.dateTime);const R=new Map;g.forEach(u=>{R.has(u.splitTime)?R.get(u.splitTime).push(u):R.set(u.splitTime,[u])}),R.forEach((u,P)=>{A.push(P);let E=0,s=0;u.forEach(p=>{p.person_average_value&&(E+=p.person_average_value),p.group_average_value&&(s+=p.group_average_value)}),a.leftBottomData[1].yData[0].data.push(Number((E/u.length).toFixed(2))),a.leftBottomData[1].yData[1].data.push(Number((s/u.length).toFixed(2)))}),a.leftBottomData[1].xData=A}})},we=()=>{a.leftBottomData[2].xData=[],a.leftBottomData[2].yData=[],sa(B({},L.value)).then(n=>{var e,d,v,_;a.leftBottomData[2].xData=(d=(e=n.data)==null?void 0:e.x_data)!=null?d:[],(v=n.data)!=null&&v.legend&&(a.leftBottomData[2].yData=(_=n.data)==null?void 0:_.legend.map((f,m)=>{var g;return n.data[`y${m+1}_data`].length!==0&&(n.data[`y${m+1}_data`]=n.data[`y${m+1}_data`].map(A=>A!=null?A:0)),{name:f,type:"line",data:n.data[`y${m+1}_data`].length!==0?(g=n.data[`y${m+1}_data`])!=null?g:0:[0,0,0,0,0]}}))})},Fe=()=>{V()};return(n,e)=>{const d=F("DynamicFormComponent"),v=F("el-button"),_=F("el-card"),f=F("el-col"),m=F("el-row"),g=F("el-tab-pane"),A=F("el-tabs"),R=F("el-option"),u=F("el-select"),P=F("CommonTableComponent"),E=F("DialogComponent");return y(),U("div",oa,[o("div",na,[o("div",la,[ia,de(" 欢迎回来，"+T(C(l)),1)]),o("div",ra,[i(d,{list:De.value,ref_key:"formFilterRef",ref:oe},null,8,["list"]),i(v,{link:"",type:"primary",onClick:Fe,class:"reflush-btn"},{default:c(()=>[de(" 查询 ")]),_:1})])]),o("div",ca,[o("div",da,[i(m,{gutter:0,class:"top-stats"},{default:c(()=>[i(f,{class:"flexAvg"},{default:c(()=>[i(_,null,{default:c(()=>{var s;return[o("div",_a,[o("div",ua,[o("div",va,T((s=a.stataic.assignedFinish)!=null?s:"—"),1),fa])])]}),_:1})]),_:1}),i(f,{class:"flexAvg"},{default:c(()=>[i(_,null,{default:c(()=>{var s;return[o("div",ga,[o("div",ma,[o("div",pa,T((s=a.stataic.effectiveMarking)!=null?s:"—"),1),ha])])]}),_:1})]),_:1}),i(f,{class:"flexAvg"},{default:c(()=>[i(_,null,{default:c(()=>{var s;return[o("div",Da,[o("div",ya,[o("div",ba,T((s=a.stataic.noeffectiveMarking)!=null?s:"—"),1),xa])])]}),_:1})]),_:1}),i(f,{class:"flexAvg"},{default:c(()=>[i(_,null,{default:c(()=>{var s;return[o("div",ka,[o("div",Aa,[o("div",Ba,T((s=a.stataic.arbitrationRate)!=null?s:"—"),1),Ca])])]}),_:1})]),_:1}),i(f,{class:"flexAvg"},{default:c(()=>[i(_,null,{default:c(()=>{var s;return[o("div",wa,[o("div",Fa,[o("div",Ta,T((s=a.stataic.averageSpeed)!=null?s:"—"),1),Ra])])]}),_:1})]),_:1})]),_:1}),i(m,{gutter:10,class:"left_center"},{default:c(()=>[i(f,{span:12},{default:c(()=>[i(_,{class:"chart-card"},{default:c(()=>[i(v,{class:"fullscreen-btn",title:"全屏",icon:C(W),circle:"",text:"",onClick:e[0]||(e[0]=s=>X("1"))},null,8,["icon"]),i(A,{modelValue:a.leftCenterTabActive,"onUpdate:modelValue":e[1]||(e[1]=s=>a.leftCenterTabActive=s),class:"tab_1",onTabClick:ge},{default:c(()=>[i(g,{label:"仲裁情况",name:"zhongcai"},{default:c(()=>[i(C(r),{ref_key:"zhongCaiChartRef",ref:ee,data:a.leftCenterData.zhongcaiData,colors:["rgb(109,157,244)","rgb(131,229,240)"]},null,8,["data","colors"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),i(f,{span:12},{default:c(()=>[i(_,{class:"chart-card"},{default:c(()=>[i(v,{class:"fullscreen-btn",title:"全屏",icon:C(W),circle:"",text:"",onClick:e[2]||(e[2]=s=>X("2"))},null,8,["icon"]),Pa,o("div",Ka,[i(C(N),{ref:"fenShuBarChartRef",xData:a.fenshuFenBu.xData,series:a.fenshuFenBu.series,showLegend:!1},null,8,["xData","series"])])]),_:1})]),_:1})]),_:1}),i(_,{class:"left-bottom"},{default:c(()=>[o("div",Na,[Ve(i(u,{modelValue:q.value,"onUpdate:modelValue":e[3]||(e[3]=s=>q.value=s),placeholder:"请选择",style:{width:"110px"},onChange:ne},{default:c(()=>[(y(!0),U(_e,null,ue(G.value,s=>(y(),K(R,{label:s.label,value:s.value,key:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),[[Se,["gongzuoliang","pingfenyizhi"].includes(a.leftBottomActiveTab)]]),i(v,{title:"全屏",icon:C(W),circle:"",text:"",onClick:e[4]||(e[4]=s=>X("3"))},null,8,["icon"])]),za,o("div",La,[i(A,{modelValue:a.leftBottomActiveTab,"onUpdate:modelValue":e[5]||(e[5]=s=>a.leftBottomActiveTab=s),class:"tab_2",onTabClick:me},{default:c(()=>[(y(!0),U(_e,null,ue(a.leftBottomData,s=>(y(),K(g,{key:s.tabName,label:s.title,name:s.tabName},{default:c(()=>[o("div",Ea,[s.tabName=="gongzuoliang"?(y(),K(C(N),{key:0,ref_for:!0,ref:"gongzuoliangChartRef",xData:s.xData,yAxis:s.yAxis,series:s.yData,showLegend:!1},null,8,["xData","yAxis","series"])):(y(),K(C(I),{key:1,xData:s.xData,unitText:s.unitText,series:s.yData,ref_for:!0,ref_key:"mulLineChartRefs",ref:ae},null,8,["xData","unitText","series"]))])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])]),_:1})]),o("div",Ua,[i(_,{class:"right_t1"},{default:c(()=>[o("div",Ya,[o("div",Ia,[o("div",Va,T(a.stataic.average_score),1),Sa]),o("div",Ja,[o("div",Oa,T(a.stataic.max_score),1),qa]),o("div",Ga,[o("div",Ha,T(a.stataic.min_score),1),Ma])])]),_:1}),i(_,{class:"right_t2"},{default:c(()=>[ja,o("div",Xa,[i(P,{ref:"rankTableRef",columns:j.value,"show-pagination":!1,autoLoad:!1,data:z.value,"row-key":"user_id",border:"",stripe:"",height:"100%"},{"column-rank":c(({row:s})=>[o("span",null,[s.rank==1?(y(),U("img",Qa)):s.rank==2?(y(),U("img",Wa)):s.rank==3?(y(),U("img",Za)):(y(),U("span",$a,T(s.rank),1))])]),_:1},8,["columns","data"])])]),_:1})])]),i(E,{isShowDialog:b.visible,onCloseDialog:se,onOpenInit:he,beforeClose:se,title:b.title,fullscreen:!0,class:"rootDialogClass"},{content:c(()=>{var s,p;return[o("div",et,[b.type=="1"?(y(),K(C(r),{key:0,ref_key:"fsPieRef",ref:J,data:a.leftCenterData.zhongcaiData,colors:["rgb(109,157,244)","rgb(131,229,240)"]},null,8,["data","colors"])):b.type=="2"?(y(),K(C(N),{key:1,ref_key:"fsBarRef",ref:O,xData:a.fenshuFenBu.xData,series:a.fenshuFenBu.series,showLegend:!1},null,8,["xData","series"])):b.type=="3"&&S.value?(y(),K(C(N),{key:2,ref_key:"fsLineChartRef",ref:pe,xData:(s=S.value)==null?void 0:s.xData,series:(p=S.value)==null?void 0:p.yData,showLegend:!1},null,8,["xData","series"])):(y(),K(C(I),{key:3,xData:S.value.xData,unitText:S.value.unitText,series:S.value.yData,ref_key:"lineChartDialogRef",ref:te},null,8,["xData","unitText","series"]))])]}),footer:c(()=>[at]),_:1},8,["isShowDialog","title"])])}}}),nt=qe(tt,[["__scopeId","data-v-d64e186b"]]);export{nt as default};
