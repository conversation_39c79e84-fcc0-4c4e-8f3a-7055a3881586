import{d as f,l as m,r as n,o as x,g as V,h as e,e as o,b as t,f as l,_ as v}from"./index-B63pSD2p.js";const w={class:"ai-score-box"},C=f({__name:"ai-score",setup(b,{expose:d}){const r=m(!1),_=u=>{r.value=!0},c=()=>{r.value=!1};return d({openDrawer:_}),(u,s)=>{const a=n("el-text"),p=n("el-drawer");return x(),V(p,{modelValue:r.value,"onUpdate:modelValue":s[0]||(s[0]=i=>r.value=i),title:"查看AI评分","before-close":c,size:"30%"},{default:e(()=>[o("div",w,[o("p",null,[t(a,{size:"large"},{default:e(()=>[l("AI评分")]),_:1})]),o("p",null,[t(a,{type:"primary",size:"large"},{default:e(()=>[l("9.00")]),_:1}),l("  "),t(a,null,{default:e(()=>[l("分")]),_:1})]),o("p",null,[t(a,{size:"large"},{default:e(()=>[l("AI评析")]),_:1})]),o("div",null,[t(a,null,{default:e(()=>[l(" 考生回答了MVC架构包含Model（模型）、View（视图）和Controller（控制器）三种元素。 Model（模型）：负责管理应用的数据和业务逻辑。 View（视图）：负责展示数据，与用户进行交互。 Controller（控制器）：负责处理用户输入，控制模型和视图的交互。得满分。 ")]),_:1})])])]),_:1},8,["modelValue"])}}}),z=v(C,[["__scopeId","data-v-33bbe026"]]);export{z as default};
