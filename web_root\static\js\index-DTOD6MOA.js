import t from"./index-DsZQJ3pV.js";import{d as r,o,c as e,b as a}from"./index-B63pSD2p.js";import"./batch-add-CKTplHOz.js";import"./setting-task-URsozwG-.js";import"./marking-mode-CLpbbjcA.js";import"./formal-task-CUOmIYGE.js";import"./fullscreen-exit-line-DVwpkItP.js";import"./question-DElFsEXd.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./handleMethod-BIjqYEft.js";import"./calculateTableHeight-BjE6OFD1.js";const g=r({name:"trial-task",__name:"index",setup(i){return(m,p)=>(o(),e("div",null,[a(t,{entrance:"trial-task"})]))}});export{g as default};
