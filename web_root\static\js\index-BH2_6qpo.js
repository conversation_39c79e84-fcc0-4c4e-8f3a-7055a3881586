var ue=Object.defineProperty,ce=Object.defineProperties;var de=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var fe=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable;var K=(o,u,t)=>u in o?ue(o,u,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[u]=t,X=(o,u)=>{for(var t in u||(u={}))fe.call(u,t)&&K(o,t,u[t]);if(G)for(var t of G(u))ge.call(u,t)&&K(o,t,u[t]);return o},Y=(o,u)=>ce(o,de(u));import me from"./add-users-B_4OYwGe.js";import _e from"./add-expert-TgR65NrV.js";import he from"./permission-config-Bzm1MNg5.js";import xe from"./data-permission-B4Yb7yTr.js";import{a as ve,b as ye,g as be,u as ke,r as Se,d as Fe}from"./user-management-B1vGxPiG.js";import{g as we}from"./roles-management-DlteFevS.js";import{l as c,T as f,d as Be,m as Ce,aa as De,P as Pe,aN as r,n as We,ao as Te,aV as T,r as F,j as Re,w as Oe,o as x,c as je,e as z,b as _,h as g,u as d,g as v,f as y,y as b,dg as Ee,_ as Ve}from"./index-B63pSD2p.js";import{d as Z}from"./downloadRequest-CdE2PBjt.js";import{u as ze}from"./uploadRequest-IEYs8WTn.js";import{c as Le,a as $e}from"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./validate-Dc6ka3px.js";import"./index-CMAj5lxj.js";const R=c([]),w=c(""),Ue=()=>{ve().then(o=>{o.code&&o.code===200?R.value=o.data.data:f.warning(o.msg)})},Ne=()=>{let o={};return new Promise(u=>{ye(o).then(t=>{if(t.code&&t.code===200)w.value=t.data.data;else{f.warning(t.msg);return}})})},He={class:"zf-first-box","element-loading-text":"数据导入中..."},Ae={class:"zf-second-box"},qe={class:"upload-btn-box"},Me=Be({name:"users",__name:"index",props:{makingStaffFlag:{type:Boolean,default:!1}},setup(o){const u=Ce(()=>{var e;return(e=De())==null?void 0:e.roles}),t=o,B=c(!1),O=c(null),j=c(null),E=c(null),V=c(null),L=c(null),C=c(null),D=c(null),S=c([]),ee=[{label:"人工",value:1},{label:"AI",value:2}],$=c({}),U=Pe({labelWidth:"70px",itemWidth:"160px",rules:{username:[{trigger:["blur","change"],validator:(e,a,n)=>{if(a&&a.length>50)return n(new Error("账号长度不能超过50！"));n()}}]},fields:[{label:"用户名",prop:"name",type:"input",defaultValue:"",placeholder:"请输入用户名",clearable:!0},{label:"行政区域",prop:"region",type:"cascader",defaultValue:"",placeholder:"请选择行政区域",optionData:()=>R.value,props:{multiple:!0,checkStrictly:!0,value:"code",label:"name"},showAllLevel:!1,clearable:!0,collapseTags:!0},{label:"所属角色",prop:"role_id_list",type:"select",defaultValue:"",placeholder:"请选择所属角色",clearable:!0,collapseTags:!0,multiple:!0,optionData:()=>S.value},{label:"是否实名",prop:"is_real_name",type:"select",defaultValue:null,placeholder:"请选择是否实名",clearable:!0,multiple:!1,collapseTags:!0,optionData:()=>[{label:"是",value:!0},{label:"否",value:!1}]},{label:"轮次",prop:"round_count_list",type:"select",defaultValue:null,placeholder:"请选择轮次",clearable:!0,multiple:!0,collapseTags:!0,optionData:()=>[{label:"第1轮",value:1},{label:"第2轮",value:2},{label:"第3轮",value:3},{label:"第4轮",value:4},{label:"第5轮",value:5},{label:"第6轮",value:6}]}]});t.makingStaffFlag||(U.fields=[{label:"用户名",prop:"name",type:"input",defaultValue:"",placeholder:"请输入用户名",clearable:!0},{label:"所属角色",prop:"role_id_list",type:"select",defaultValue:"",placeholder:"请选择所属角色",clearable:!0,collapseTags:!0,multiple:!0,optionData:()=>S.value}]);const m=c({field:[{prop:"name",label:"用户名",minWidth:"120px"},{prop:"username",label:"用户账号",minWidth:"120px"},{prop:"id_card_encipher",label:"身份证号",minWidth:"140px"},{prop:"phone_encipher",label:"手机号",minWidth:"110px"},{prop:"role_name",label:"所属角色",minWidth:"120px"},{prop:"project_name_list",label:"所属资格",minWidth:"220px",formatter:e=>{var a;return(a=e.project_name_list)!=null&&a.length?e.project_name_list.join(","):"-"}},{prop:"subject_name_list",label:"所属科目",minWidth:"220px",formatter:e=>{var a;return(a=e.subject_name_list)!=null&&a.length?e.subject_name_list.join(","):"-"}},{prop:"knowledge_show",label:"所属试题",minWidth:"200px",formatter:e=>{var a;return(a=e.knowledge_show)!=null&&a.length?e.knowledge_show.join(","):"-"}},{prop:"round_count",label:"所属轮次",minWidth:"120px",formatter:e=>e.round_count?`第${e.round_count}轮`:"-"},{prop:"region",label:"行政区域",minWidth:"160px"},{prop:"work_unit",label:"所在单位",minWidth:"120px"},{prop:"is_active",label:"使用状态",type:"switch",minWidth:"100px",inactiveText:"禁用",activeText:"启用",inlinePrompt:!0,beforeChange:e=>H(e),onchange:e=>{}},{prop:"",label:"操作",type:"template",minWidth:"310px",fixed:"right",templateGroup:[{title:()=>r("users/auth")&&!t.makingStaffFlag||t.makingStaffFlag&&r("examiners/auth")?"功能权限":"",clickBtn(e){E.value.openDialog("01",e)}},{title:()=>r("user/data")&&!t.makingStaffFlag||t.makingStaffFlag&&r("examiners/data")?"数据权限":"",clickBtn(e){V.value.openDialog("01",e)}},{title:()=>!t.makingStaffFlag&&r("users/password")||t.makingStaffFlag&&r("examiners/password")?"重置密码":"",clickBtn(e){q(e)}},{title:()=>!t.makingStaffFlag&&r("users/edit")||t.makingStaffFlag&&r("examiners/edit")?"编辑":"",clickBtn(e){j.value.openDialog("02",e)}},{title:()=>!t.makingStaffFlag&&r("users/delete")||t.makingStaffFlag&&r("examiners/delete")?"删除":"",clickBtn(e){I(e)}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});t.makingStaffFlag||(m.value.field=[{prop:"name",label:"用户名",minWidth:"120px"},{prop:"username",label:"用户账号",minWidth:"120px"},{prop:"id_card_encipher",label:"身份证号",minWidth:"140px"},{prop:"phone_encipher",label:"手机号",minWidth:"110px"},{prop:"role_name",label:"所属角色",minWidth:"120px"},{prop:"is_active",label:"使用状态",type:"switch",minWidth:"100px",inactiveText:"禁用",activeText:"启用",inlinePrompt:!0,beforeChange:e=>H(e),onchange:e=>{}},{prop:"u_user_name",label:"更新人",minWidth:"160px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"",label:"操作",type:"template",minWidth:"310px",fixed:"right",templateGroup:[{title:()=>r("users/auth")&&!t.makingStaffFlag||t.makingStaffFlag&&r("examiners/auth")?"功能权限":"",clickBtn(e){E.value.openDialog("01",e)}},{title:()=>r("user/data")&&!t.makingStaffFlag||t.makingStaffFlag&&r("examiners/data")?"数据权限":"",clickBtn(e){V.value.openDialog("01",e)}},{title:()=>!t.makingStaffFlag&&r("users/password")||t.makingStaffFlag&&r("examiners/password")?"重置密码":"",clickBtn(e){q(e)}},{title:()=>!t.makingStaffFlag&&r("users/edit")||t.makingStaffFlag&&r("examiners/edit")?"编辑":"",clickBtn(e){O.value.openDialog("02",e)}},{title:()=>!t.makingStaffFlag&&r("users/delete")||t.makingStaffFlag&&r("examiners/delete")?"删除":"",clickBtn(e){I(e)}}]}]);let P=c([]),N=null;We(()=>{Le(N,L.value,m.value),le(),Ue(),h(),Ne().then(e=>{w.value=e.toString()})}),Te(()=>{$e(N)});function te(){var i;let e=JSON.parse(JSON.stringify(C.value.getAllCardData())),{currentPage:a,pageSize:n}=m.value.pageOptions,l={current_page:a,page_size:n,system_user_type:2};return l=Object.assign(e,l),l.region&&((i=l.region)==null?void 0:i.length)>0&&(l.province_code_list=[],l.city_code_list=[],l.district_code_list=[],l.region.forEach(s=>{s.length===1?l.province_code_list.push(s[0]):s.length===2?l.city_code_list.push(s[1]):s.length===3&&l.district_code_list.push(s[2])}),delete l.region),l}const ae=()=>{C.value.formValidate().then(()=>{T.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z("post","/v1/user/export_get_user_list",void 0,{},Y(X({},te()),{page_size:-1}),"xlsx").then(e=>{e&&e.data&&e.data.type=="application/json"&&f({message:"暂无导出信息！",type:"warning"})}).catch(()=>{f({type:"error",message:"导出失败"})})}).catch(()=>{})})},le=()=>{let e={page_size:-1};t.makingStaffFlag?e.system_user_type=2:e.system_user_type=1,we(e).then(a=>{a.code&&a.code===200&&(a.data.data.forEach(n=>{n.label=n.role_name,n.value=n.role_id}),S.value=a.data.data)})},h=()=>{var i;let e=JSON.parse(JSON.stringify(C.value.getAllCardData())),{currentPage:a,pageSize:n}=m.value.pageOptions,l={current_page:a,page_size:n};l=Object.assign(e,l),l.region&&((i=l.region)==null?void 0:i.length)>0&&(l.province_code_list=[],l.city_code_list=[],l.district_code_list=[],l.region.forEach(s=>{s.length===1?l.province_code_list.push(s[0]):s.length===2?l.city_code_list.push(s[1]):s.length===3&&l.district_code_list.push(s[2])}),delete l.region),t.makingStaffFlag?l.system_user_type=2:l.system_user_type=1,P.value=[],be(l).then(s=>{s.code&&s.code===200&&(P.value=s.data.data,P.value.forEach(p=>{var W,k,Q;p.id_card_encipher=p.id_card?p.id_card.replace(/(.{4}).*(.{4}$)/,"$1********$2"):"",p.phone_encipher=p.phone?p.phone.replace(/(.{3}).*(.{4}$)/,"$1****$2"):"",p.region=[(W=p.province_name)!=null?W:"",(k=p.city_name)!=null?k:"",(Q=p.district_name)!=null?Q:""].filter(Boolean).join("/"),p.user_type_name=ne(p.user_type,ee)}),!u.value.includes("1")&&!u.value.includes("2")&&m.value.field.forEach(p=>{p.prop==="is_active"&&(p.disabled=!0)}),m.value.pageOptions.total=s.data.total)})},ne=(e,a)=>{let n="";return a.map(l=>{l.value===e&&(n=l.label)}),n},H=e=>new Promise((a,n)=>{let l="";e.is_active?l="禁用":l="启用",T.confirm(`确定${l}账号：${e.name} 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>re(e)).catch(()=>{n(!1)})}),re=e=>{const{user_id:a,is_active:n}=e;ke({user_id:a,is_active:!n}).then(i=>i.code&&i.code===200?(f.success(i.msg),h(),Promise.resolve(i)):(f.warning(i.msg),Promise.reject(i)))},A=()=>{Z("get","/v1/common/get_template_file","阅卷人员模板",{temp_type:"4",temp_format:1},{},"xlsx").then(a=>{a&&a.data&&a.data.type=="application/json"&&f({message:"暂无模板信息！",type:"warning"})})},q=e=>{const a=`<strong>确定重置密码为 <span style="color: #F56C6C">${w.value}</span> 吗？</strong>`;T.confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning",dangerouslyUseHTMLString:!0}).then(()=>{let n={user_id:e.user_id};Se(n).then(l=>{l.code&&l.code===200?f.success(l.msg):f.warning(l.msg)})}).catch(()=>{})},M=e=>{D.value.clearFiles();const a=e[0];a.uid=Ee(),D.value.handleStart(a)},J=e=>{B.value=!0;let a={file:e.raw};ze("post","/v1/manual_people/import_manual_people",a).then(n=>{n.code&&n.code===200?(f.success(n.msg),h()):f.warning(n.msg),B.value=!1}).catch(()=>{B.value=!1})},oe=e=>{O.value.openDialog(e)},ie=e=>{j.value.openDialog(e)},I=e=>{T.confirm("确定删除该用户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a={user_id:e.user_id};Fe(a).then(n=>{n.code&&n.code===200?(f.success(n.msg),h()):f.warning(n.msg)})}).catch(()=>{})},se=e=>{m.value.pageOptions.pageSize=e,h()},pe=e=>{m.value.pageOptions.currentPage=e,h()};return(e,a)=>{const n=F("form-component"),l=F("el-card"),i=F("el-button"),s=F("el-upload"),p=F("table-component"),W=Re("loading");return Oe((x(),je("div",He,[z("div",Ae,[_(l,null,{default:g(()=>[z("div",{ref_key:"formDivRef",ref:L},[_(n,{ref_key:"formRef",ref:C,modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=k=>$.value=k),"form-options":U,"is-query-btn":!0,onQueryDataFn:h},null,8,["modelValue","form-options"])],512)]),_:1}),_(l,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:g(()=>[z("div",qe,[t.makingStaffFlag&&d(r)("examiners/download")?(x(),v(i,{key:0,style:{"margin-right":"10px"},type:"primary",onClick:A},{default:g(()=>[y("下载模板 ")]),_:1})):b("",!0),!t.makingStaffFlag&&d(r)("users/download")?(x(),v(i,{key:1,style:{"margin-right":"10px"},type:"primary",onClick:A},{default:g(()=>[y("下载模板 ")]),_:1})):b("",!0),t.makingStaffFlag&&d(r)("examiners/import")?(x(),v(s,{key:2,ref_key:"uploadRef",ref:D,style:{"margin-right":"10px"},action:"",limit:1,"on-change":J,"on-exceed":M,"auto-upload":!1,accept:".docx,.xlsx","show-file-list":!1},{trigger:g(()=>[_(i,{type:"primary"},{default:g(()=>[y("导入")]),_:1})]),_:1},512)):b("",!0),!t.makingStaffFlag&&d(r)("users/import")?(x(),v(s,{key:3,ref_key:"uploadRef",ref:D,style:{"margin-right":"10px"},action:"",limit:1,"on-change":J,"on-exceed":M,"auto-upload":!1,accept:".docx,.xlsx","show-file-list":!1},{trigger:g(()=>[_(i,{type:"primary"},{default:g(()=>[y("导入")]),_:1})]),_:1},512)):b("",!0),!o.makingStaffFlag&&d(r)("users/export")?(x(),v(i,{key:4,type:"primary"},{default:g(()=>[y("导出")]),_:1})):b("",!0),o.makingStaffFlag&&d(r)("examiners/export")?(x(),v(i,{key:5,type:"primary",onClick:ae},{default:g(()=>[y("导出")]),_:1})):b("",!0),!t.makingStaffFlag&&d(r)("users/add")?(x(),v(i,{key:6,type:"primary",onClick:a[1]||(a[1]=k=>oe("01"))},{default:g(()=>[y("创建 ")]),_:1})):b("",!0),t.makingStaffFlag&&d(r)("examiners/add")?(x(),v(i,{key:7,type:"primary",onClick:a[2]||(a[2]=k=>ie("04"))},{default:g(()=>[y("创建 ")]),_:1})):b("",!0)]),_(p,{minHeight:m.value.styleOptions.minHeight,"table-options":m.value,"table-data":d(P),onOnHandleSizeChange:se,onOnHandleCurrentChange:pe},null,8,["minHeight","table-options","table-data"])]),_:1})]),_(me,{ref_key:"addUsersRef",ref:O,"roles-list":S.value,defaultPassword:d(w),regionList:d(R),makingStaffFlag:o.makingStaffFlag,onQueryData:h},null,8,["roles-list","defaultPassword","regionList","makingStaffFlag"]),_(_e,{ref_key:"addExpertRef",ref:j,"roles-list":S.value,defaultPassword:d(w),regionList:d(R),onQueryData:h},null,8,["roles-list","defaultPassword","regionList"]),_(he,{ref_key:"permissionConfigRef",ref:E},null,512),_(xe,{ref_key:"dataPermissionRef",ref:V},null,512)])),[[W,B.value]])}}}),it=Ve(Me,[["__scopeId","data-v-8d5ff394"]]);export{it as default};
