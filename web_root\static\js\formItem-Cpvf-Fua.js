import{d as _e,l as v,an as xe,n as Ve,ao as ke,r,o as a,c as i,e as S,b as c,h as s,F as w,p as E,w as we,g as u,q as h,dd as ge,f as y,t as O,s as N,y as b,ae as Fe,aw as Ce,z as Ue,u as q,de as $e,df as Be,U as De,dg as Oe,ac as Se,ad as Ie,_ as Ae}from"./index-B63pSD2p.js";const Ee=t=>(Se("data-v-020ba5dc"),t=t(),Ie(),t),Le={key:10},Me=["innerHTML"],Pe={key:11},We=Ee(()=>S("span",{class:"mr-[3px] ml-[3px]"},"-",-1)),ze=["innerHTML"],He={key:14},Te={type:"primary",class:"unit-box"},Ne={key:0,class:"el-upload__tip upload-form-text"},qe={key:0,class:"flex"},Qe=_e({name:"formComponent",__name:"formItem",props:{formOptions:{type:Object,default:()=>{}},isQueryBtn:{type:Boolean,default:!0},isDownloadPluginBtn:{type:Boolean,default:!1}},emits:["onchangeFn","onblurFn","onfocusFn","onclickSuffixFn","queryDataFn","getUploadFile","removeUploadFile","downloadPluginFn"],setup(t,{expose:Q,emit:R}){const I=t,_=R,V=v(null),g=v(null),o=v({});v("");const F=v(null),L=v(null),M=v(null),m=v(),C=v(!0),A=v(!1);let U=null;xe(()=>{P()}),Ve(()=>{var n,d;I.formOptions.noDrag&&((n=L.value)==null||n.forEach(p=>{const f=p==null?void 0:p.$el.querySelector("input");f&&(f.classList.add("no-drag"),f.classList.add("data-drag-cancel"),f.setAttribute("data-drag-cancel","drag"+Math.random()))}),(d=M.value)==null||d.forEach(p=>{const f=p==null?void 0:p.$el.querySelector("input");f&&(f.classList.add("no-drag"),f.classList.add("data-drag-cancel"),f.setAttribute("data-drag-cancel","drag"+Math.random()))})),I.isQueryBtn&&V.value.offsetHeight<=50&&(A.value=!1),I.isQueryBtn&&(C.value=!1,U=new ResizeObserver(p=>{p.forEach(f=>{V.value&&(V.value.offsetHeight>50?A.value=!0:A.value=!1)})}),V.value&&U.observe(V.value))}),ke(()=>{U&&(U.disconnect(),U=null)});const P=()=>{var n;(n=I.formOptions.fields)==null||n.map(d=>{typeof d.defaultValue=="function"?o.value[d.prop]=d.defaultValue():(Array.isArray(d.defaultValue)||d.defaultValue||d.defaultValue===!1||d.defaultValue===0)&&(o.value[d.prop]=d.defaultValue)})},j=(n,d)=>{o.value[n]=d},G=()=>o.value,J=n=>o.value[n],W=n=>{m.value=n,_("getUploadFile",n)},K=n=>{F.value[0].clearFiles();const d=n[0];d.uid=Oe(),F.value[0].handleStart(d)},X=()=>{_("removeUploadFile")},Y=()=>new Promise((n,d)=>{g.value.validate(p=>{p?n():d()})}),Z=n=>{n?g.value.clearValidate(n):g.value.clearValidate()},z=()=>{g.value.resetFields(),F.value&&F.value[0].clearFiles(),P()},k=n=>{_("onclickSuffixFn",n)},x=(n,d)=>{_("onchangeFn",n,d)},$=(n,d)=>{_("onblurFn",n,d)},B=(n,d)=>{_("onfocusFn",n,d)},ee=()=>{_("queryDataFn")},le=()=>{_("downloadPluginFn")},H=()=>{C.value=!C.value};return Q({setCardData:j,getAllCardData:G,getCardData:J,getUploadFile:W,formValidate:Y,clearValidateFn:Z,resetFieldsFn:z}),(n,d)=>{const p=r("el-icon"),f=r("el-input"),oe=r("el-input-number"),ae=r("el-option"),ne=r("el-select"),de=r("el-radio"),se=r("el-radio-group"),te=r("el-checkbox"),re=r("el-checkbox-group"),ue=r("el-date-picker"),pe=r("el-cascader"),ce=r("el-switch"),fe=r("el-slider"),D=r("el-button"),ie=r("el-upload"),ve=r("el-tooltip"),T=r("el-form-item"),he=r("el-form"),ye=r("ArrowUp"),be=r("ArrowDown");return a(),i("div",{class:De([C.value?"":"form-pick-up-box"])},[S("div",{ref_key:"formBoxRef",ref:V,class:"form-box"},[c(he,{ref_key:"formRef",ref:g,rules:t.formOptions.rules,inline:!t.formOptions.inline,model:o.value,class:"form-custom-box","label-position":"right","validate-on-rule-change":!1,"label-width":t.formOptions.labelWidth,disabled:t.formOptions.disabled},{default:s(()=>[(a(!0),i(w,null,E(t.formOptions.fields,e=>(a(),i(w,null,[we(c(T,{label:e.label,prop:e.prop},{default:s(()=>[e.type==="input"||e.type==="textarea"?(a(),u(f,{key:0,ref_for:!0,ref_key:"elInputRef",ref:L,type:e.type,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,"show-password":e.showPassword,rows:e.rows,autocomplete:"new-password",style:h({width:e.width?e.width:t.formOptions.itemWidth}),clearable:e.clearable,placeholder:e.placeholder,disabled:e.disabled?e.disabled:!1,"suffix-icon":e.suffixShow?"MoreFilled":"",readonly:e.readonly,onChange:l=>x(e,o.value[e.prop]),onClick:l=>k(e),onBlur:l=>$(e,o.value[e.prop]),onFocus:l=>B(e,o.value[e.prop])},ge({_:2},[e.suffix?{name:"suffix",fn:s(()=>[y(O(e.suffix),1)]),key:"0"}:e.suffixIcon?{name:"suffix",fn:s(()=>[c(p,{class:"suffix-icon-box",onClick:l=>k(e)},{default:s(()=>[(a(),u(N(e.suffixIcon)))]),_:2},1032,["onClick"])]),key:"1"}:void 0]),1032,["type","modelValue","onUpdate:modelValue","show-password","rows","style","clearable","placeholder","disabled","suffix-icon","readonly","onChange","onClick","onBlur","onFocus"])):e.type==="inputNumber"?(a(),u(oe,{key:1,ref_for:!0,ref_key:"elInputNumberRef",ref:M,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,disabled:e.disabled,min:e.min,max:e.max,step:e.step,"step-strictly":e.stepStrictly,style:h({width:e.width?e.width:t.formOptions.itemWidth}),onChange:l=>x(e,o.value[e.prop]),onClick:l=>k(e),onBlur:l=>$(e,o.value[e.prop]),onFocus:l=>B(e,o.value[e.prop])},null,8,["modelValue","onUpdate:modelValue","disabled","min","max","step","step-strictly","style","onChange","onClick","onBlur","onFocus"])):e.type==="select"?(a(),u(ne,{key:2,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,multiple:e.multiple?e.multiple:!1,style:h({width:e.width?e.width:t.formOptions.itemWidth}),clearable:e.clearable,placeholder:e.placeholder,disabled:e.disabled?e.disabled:!1,onChange:l=>x(e,o.value[e.prop]),onClick:l=>k(e),onBlur:l=>$(e,o.value[e.prop]),onFocus:l=>B(e,o.value[e.prop])},{default:s(()=>[(a(!0),i(w,null,E(e.optionData(),l=>(a(),u(ae,{label:e.props?l[e.props.label]:l.label,value:e.props?l[e.props.value]:l.value,disabled:l.disabled},null,8,["label","value","disabled"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","multiple","style","clearable","placeholder","disabled","onChange","onClick","onBlur","onFocus"])):e.type==="radio"?(a(),u(se,{key:3,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,disabled:e.disabled?e.disabled:!1,onChange:l=>x(e,o.value[e.prop])},{default:s(()=>[(a(!0),i(w,null,E(e.optionData(),l=>(a(),u(de,{value:l.value,disabled:l.disabled},{default:s(()=>[y(O(l.label),1)]),_:2},1032,["value","disabled"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])):e.type==="checkbox"?(a(),u(re,{key:4,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,disabled:e.disabled?e.disabled:!1,onChange:l=>x(e,o.value[e.prop])},{default:s(()=>[(a(!0),i(w,null,E(e.optionData(),l=>(a(),u(te,{value:l.value,disabled:l.disabled},{default:s(()=>[y(O(l.label),1)]),_:2},1032,["value","disabled"]))),256))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])):e.type==="year"||e.type==="month"||e.type==="date"||e.type==="daterange"||e.type==="datetimerange"?(a(),u(ue,{style:h({width:e.width?e.width:t.formOptions.itemWidth}),modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,type:e.type,"range-separator":"-",key:e.prop,"append-to-body":!1,"popper-options":e.popperOption,clearable:e.clearable,placeholder:e.placeholder,disabled:e.disabled?e.disabled:!1,format:e.valueFormat,"value-format":e.valueFormat,"start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:l=>x(e,o.value[e.prop]),onClick:l=>k(e),onBlur:l=>$(e,o.value[e.prop]),onFocus:l=>B(e,o.value[e.prop])},null,8,["style","modelValue","onUpdate:modelValue","type","popper-options","clearable","placeholder","disabled","format","value-format","onChange","onClick","onBlur","onFocus"])):e.type==="cascader"?(a(),u(pe,{key:6,style:h({width:e.width?e.width:t.formOptions.itemWidth}),modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,options:e.optionData(),props:e.props,"show-all-levels":e.showAllLevel,clearable:e.clearable,placeholder:e.placeholder,onChange:l=>x(e,o.value[e.prop]),onClick:l=>k(e),onBlur:l=>$(e,o.value[e.prop]),onFocus:l=>B(e,o.value[e.prop])},null,8,["style","modelValue","onUpdate:modelValue","options","props","show-all-levels","clearable","placeholder","onChange","onClick","onBlur","onFocus"])):e.type==="switch"?(a(),u(ce,{key:7,modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,"active-text":e.activeText,"inactive-text":e.inactiveText,style:h(e.style),"inline-prompt":e.inlinePrompt,onChange:l=>x(e,o.value[e.prop])},null,8,["modelValue","onUpdate:modelValue","active-text","inactive-text","style","inline-prompt","onChange"])):e.type==="slider"?(a(),i("div",{key:8,style:h([{margin:"0 3px"},e.style])},[c(fe,{modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,min:e.min,max:e.max,step:e.step},null,8,["modelValue","onUpdate:modelValue","min","max","step"])],4)):e.type==="upload"?(a(),u(ie,{key:9,ref_for:!0,ref_key:"uploadRef",ref:F,class:"upload-demo",action:"",limit:1,"on-change":W,"on-exceed":K,"on-remove":X,"auto-upload":!1,accept:".docx,.xlsx"},{trigger:s(()=>[c(f,{style:h([{width:e.width?e.width:t.formOptions.itemWidth},{"margin-right":"5px"}]),modelValue:o.value[e.prop],"onUpdate:modelValue":l=>o.value[e.prop]=l,placeholder:"请选择试卷"},null,8,["style","modelValue","onUpdate:modelValue"]),c(D,{type:"primary"},{default:s(()=>[y("浏览")]),_:1})]),_:2},1536)):e.type==="text"?(a(),i("div",Le,[S("span",{innerHTML:e.text()},null,8,Me)])):e.type==="doubleInput"?(a(),i("div",Pe,[c(f,{modelValue:o.value[e.prop][0],"onUpdate:modelValue":l=>o.value[e.prop][0]=l,style:h({width:e.width?e.width:t.formOptions.itemWidth}),placeholder:e.placeholder,clearable:e.clearable},null,8,["modelValue","onUpdate:modelValue","style","placeholder","clearable"]),We,c(f,{modelValue:o.value[e.prop][1],"onUpdate:modelValue":l=>o.value[e.prop][1]=l,style:h({width:e.width?e.width:t.formOptions.itemWidth}),placeholder:e.placeholder,clearable:e.clearable},null,8,["modelValue","onUpdate:modelValue","style","placeholder","clearable"])])):b("",!0),e.type==="template"?Fe(n.$slots,e.prop,Ce({key:12,scope:e},o.value[e.prop]),void 0,!0):b("",!0),e.icon?(a(),u(ve,{key:13,effect:"light"},{content:s(()=>[S("span",{innerHTML:e.iconStyle.info},null,8,ze)]),default:s(()=>[c(p,{class:"ml-1",size:e.iconStyle.size,color:e.iconStyle.color},{default:s(()=>[(a(),u(N(e.icon)))]),_:2},1032,["size","color"])]),_:2},1024)):b("",!0),e.unit?(a(),i("div",He,[S("span",Te,O(typeof e.unit=="function"?e.unit():e.unit),1)])):b("",!0),e.buttonInfo?(a(),u(D,{key:15,class:"ml-[5px]",type:e.buttonInfo.btnType,onClick:e.buttonInfo.clickBtn,disabled:e.buttonInfo.disabled},{default:s(()=>[y(O(e.buttonInfo.button),1)]),_:2},1032,["type","onClick","disabled"])):b("",!0)]),_:2},1032,["label","prop"]),[[Ue,!e.isHidden]]),e.type==="upload"&&!e.isHidden?(a(),i("div",Ne,"最多上传一个docx/xlsx文件 ")):b("",!0)],64))),256)),t.isDownloadPluginBtn?(a(),u(T,{key:0},{default:s(()=>[c(D,{type:"primary",onClick:le},{default:s(()=>[y("下载插件")]),_:1})]),_:1})):b("",!0)]),_:3},8,["rules","inline","model","label-width","disabled"]),t.isQueryBtn?(a(),i("div",qe,[A.value?(a(),i(w,{key:0},[C.value?(a(),i("span",{key:0,class:"form-expand-btn",onClick:H},[c(p,null,{default:s(()=>[c(ye)]),_:1}),y("收起")])):(a(),i("span",{key:1,class:"form-expand-btn",onClick:H},[c(p,null,{default:s(()=>[c(be)]),_:1}),y("展开")]))],64)):b("",!0),c(D,{icon:q($e),onClick:z},{default:s(()=>[y("重置")]),_:1},8,["icon"]),c(D,{type:"primary",icon:q(Be),onClick:ee},{default:s(()=>[y("查询")]),_:1},8,["icon"])])):b("",!0)],512)],2)}}}),me=Ae(Qe,[["__scopeId","data-v-020ba5dc"]]);export{me as default};
