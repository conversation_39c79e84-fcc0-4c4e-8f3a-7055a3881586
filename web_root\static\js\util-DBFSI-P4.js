import{b2 as T}from"./index-B63pSD2p.js";var X={},Y=function(c){return c&&typeof c=="object"&&typeof c.copy=="function"&&typeof c.fill=="function"&&typeof c.readUInt8=="function"},U={exports:{}};typeof Object.create=="function"?U.exports=function(c,g){c.super_=g,c.prototype=Object.create(g.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}})}:U.exports=function(c,g){c.super_=g;var d=function(){};d.prototype=g.prototype,c.prototype=new d,c.prototype.constructor=c};var k=U.exports;(function(t){var c={},g=/%[sdj%]/g;t.format=function(e){if(!O(e)){for(var n=[],r=0;r<arguments.length;r++)n.push(p(arguments[r]));return n.join(" ")}for(var r=1,o=arguments,l=o.length,f=String(e).replace(g,function(s){if(s==="%%")return"%";if(r>=l)return s;switch(s){case"%s":return String(o[r++]);case"%d":return Number(o[r++]);case"%j":try{return JSON.stringify(o[r++])}catch(u){return"[Circular]"}default:return s}}),i=o[r];r<l;i=o[++r])b(i)||!m(i)?f+=" "+i:f+=" "+p(i);return f},t.deprecate=function(e,n){if(a(T.process))return function(){return t.deprecate(e,n).apply(this,arguments)};if(process.noDeprecation===!0)return e;var r=!1;function o(){if(!r){if(process.throwDeprecation)throw new Error(n);process.traceDeprecation?console.trace(n):console.error(n),r=!0}return e.apply(this,arguments)}return o};var d={},E;t.debuglog=function(e){if(a(E)&&(E=c.NODE_DEBUG||""),e=e.toUpperCase(),!d[e])if(new RegExp("\\b"+e+"\\b","i").test(E)){var n=process.pid;d[e]=function(){var r=t.format.apply(t,arguments);console.error("%s %d: %s",e,n,r)}}else d[e]=function(){};return d[e]};function p(e,n){var r={seen:[],stylize:F};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),_(n)?r.showHidden=n:n&&t._extend(r,n),a(r.showHidden)&&(r.showHidden=!1),a(r.depth)&&(r.depth=2),a(r.colors)&&(r.colors=!1),a(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=C),h(r,e,r.depth)}t.inspect=p,p.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},p.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function C(e,n){var r=p.styles[n];return r?"\x1B["+p.colors[r][0]+"m"+e+"\x1B["+p.colors[r][1]+"m":e}function F(e,n){return e}function $(e){var n={};return e.forEach(function(r,o){n[r]=!0}),n}function h(e,n,r){if(e.customInspect&&n&&w(n.inspect)&&n.inspect!==t.inspect&&!(n.constructor&&n.constructor.prototype===n)){var o=n.inspect(r,e);return O(o)||(o=h(e,o,r)),o}var l=G(e,n);if(l)return l;var f=Object.keys(n),i=$(f);if(e.showHidden&&(f=Object.getOwnPropertyNames(n)),j(n)&&(f.indexOf("message")>=0||f.indexOf("description")>=0))return D(n);if(f.length===0){if(w(n)){var s=n.name?": "+n.name:"";return e.stylize("[Function"+s+"]","special")}if(S(n))return e.stylize(RegExp.prototype.toString.call(n),"regexp");if(A(n))return e.stylize(Date.prototype.toString.call(n),"date");if(j(n))return D(n)}var u="",y=!1,z=["{","}"];if(v(n)&&(y=!0,z=["[","]"]),w(n)){var L=n.name?": "+n.name:"";u=" [Function"+L+"]"}if(S(n)&&(u=" "+RegExp.prototype.toString.call(n)),A(n)&&(u=" "+Date.prototype.toUTCString.call(n)),j(n)&&(u=" "+D(n)),f.length===0&&(!y||n.length==0))return z[0]+u+z[1];if(r<0)return S(n)?e.stylize(RegExp.prototype.toString.call(n),"regexp"):e.stylize("[Object]","special");e.seen.push(n);var R;return y?R=I(e,n,r,i,f):R=f.map(function(Q){return N(e,n,r,i,Q,y)}),e.seen.pop(),M(R,u,z)}function G(e,n){if(a(n))return e.stylize("undefined","undefined");if(O(n)){var r="'"+JSON.stringify(n).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(H(n))return e.stylize(""+n,"number");if(_(n))return e.stylize(""+n,"boolean");if(b(n))return e.stylize("null","null")}function D(e){return"["+Error.prototype.toString.call(e)+"]"}function I(e,n,r,o,l){for(var f=[],i=0,s=n.length;i<s;++i)J(n,String(i))?f.push(N(e,n,r,o,String(i),!0)):f.push("");return l.forEach(function(u){u.match(/^\d+$/)||f.push(N(e,n,r,o,u,!0))}),f}function N(e,n,r,o,l,f){var i,s,u;if(u=Object.getOwnPropertyDescriptor(n,l)||{value:n[l]},u.get?u.set?s=e.stylize("[Getter/Setter]","special"):s=e.stylize("[Getter]","special"):u.set&&(s=e.stylize("[Setter]","special")),J(o,l)||(i="["+l+"]"),s||(e.seen.indexOf(u.value)<0?(b(r)?s=h(e,u.value,null):s=h(e,u.value,r-1),s.indexOf(`
`)>-1&&(f?s=s.split(`
`).map(function(y){return"  "+y}).join(`
`).substr(2):s=`
`+s.split(`
`).map(function(y){return"   "+y}).join(`
`))):s=e.stylize("[Circular]","special")),a(i)){if(f&&l.match(/^\d+$/))return s;i=JSON.stringify(""+l),i.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(i=i.substr(1,i.length-2),i=e.stylize(i,"name")):(i=i.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),i=e.stylize(i,"string"))}return i+": "+s}function M(e,n,r){var o=e.reduce(function(l,f){return f.indexOf(`
`)>=0,l+f.replace(/\u001b\[\d\d?m/g,"").length+1},0);return o>60?r[0]+(n===""?"":n+`
 `)+" "+e.join(`,
  `)+" "+r[1]:r[0]+n+" "+e.join(", ")+" "+r[1]}function v(e){return Array.isArray(e)}t.isArray=v;function _(e){return typeof e=="boolean"}t.isBoolean=_;function b(e){return e===null}t.isNull=b;function Z(e){return e==null}t.isNullOrUndefined=Z;function H(e){return typeof e=="number"}t.isNumber=H;function O(e){return typeof e=="string"}t.isString=O;function V(e){return typeof e=="symbol"}t.isSymbol=V;function a(e){return e===void 0}t.isUndefined=a;function S(e){return m(e)&&B(e)==="[object RegExp]"}t.isRegExp=S;function m(e){return typeof e=="object"&&e!==null}t.isObject=m;function A(e){return m(e)&&B(e)==="[object Date]"}t.isDate=A;function j(e){return m(e)&&(B(e)==="[object Error]"||e instanceof Error)}t.isError=j;function w(e){return typeof e=="function"}t.isFunction=w;function W(e){return e===null||typeof e=="boolean"||typeof e=="number"||typeof e=="string"||typeof e=="symbol"||typeof e=="undefined"}t.isPrimitive=W,t.isBuffer=Y;function B(e){return Object.prototype.toString.call(e)}function P(e){return e<10?"0"+e.toString(10):e.toString(10)}var q=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function K(){var e=new Date,n=[P(e.getHours()),P(e.getMinutes()),P(e.getSeconds())].join(":");return[e.getDate(),q[e.getMonth()],n].join(" ")}t.log=function(){console.log("%s - %s",K(),t.format.apply(t,arguments))},t.inherits=k,t._extend=function(e,n){if(!n||!m(n))return e;for(var r=Object.keys(n),o=r.length;o--;)e[r[o]]=n[r[o]];return e};function J(e,n){return Object.prototype.hasOwnProperty.call(e,n)}})(X);export{X as u};
