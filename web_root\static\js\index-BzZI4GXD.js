import r from"./single-plagiarizing-Ce-tTkRU.js";import m from"./multi-plagiarizing-MLw8n7b3.js";import{d as p,l as _,r as i,o as c,c as d,e as f,b as e,h as a,_ as u}from"./index-B63pSD2p.js";import"./plagiarizing-answer-DCON8hS9.js";import"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./single-detail-CJkFVjgq.js";import"./judge-copy-BAX6vJkk.js";import"./single-judge-DgOCcZyG.js";import"./multi-detail-BEo7a2qA.js";import"./multi-judge-CWfakgGE.js";const b={class:"zf-first-box"},g={class:"zf-second-box"},x=p({name:"plagiarizing-answer",__name:"index",setup(v){const o=_("single");return(z,t)=>{const n=i("el-tab-pane"),s=i("el-tabs");return c(),d("div",b,[f("div",g,[e(s,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=l=>o.value=l),class:"monitor-container dark:!bg-black eye-box"},{default:a(()=>[e(n,{label:"单人抄袭",name:"single"},{default:a(()=>[e(r)]),_:1}),e(n,{label:"多人抄袭",name:"multi"},{default:a(()=>[e(m)]),_:1})]),_:1},8,["modelValue"])])])}}}),S=u(x,[["__scopeId","data-v-4e76a001"]]);export{S as default};
