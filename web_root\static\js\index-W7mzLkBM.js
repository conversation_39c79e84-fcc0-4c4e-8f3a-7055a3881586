import{g as E,a as R,d as H,u as N,b as A,_ as L}from"./add-project.vue_vue_type_script_setup_true_lang-DaQlfrc8.js";import{d as q,l,P as M,aN as y,n as J,ao as Q,aV as j,T as u,r as p,o as U,c as $,e as f,b as c,h as d,f as G,u as I,_ as K}from"./index-B63pSD2p.js";import{p as X,g as B}from"./common-methods-BWkba4Bo.js";import{c as Y,a as Z}from"./calculateTableHeight-BjE6OFD1.js";import"./test-paper-management-DjV_45YZ.js";const ee={class:"zf-first-box"},te={class:"zf-second-box"},ae={class:"upload-btn-box"},ne=q({name:"project-management",__name:"index",setup(oe){const g=l(null),m=l(null),_=l(null),C=l([]),h=l([]),x=l({}),O=M({column:3,labelWidth:"68px",itemWidth:"240px",rules:{project_name:[{trigger:["blur","change"],validator:(e,t,a)=>{if(t&&t.length>50)return a(new Error("资格名称长度不能超过50！"));a()}}]},fields:[{label:"资格名称",prop:"project_id",type:"select",defaultValue:"",placeholder:"请选择资格名称",clearable:!0,optionData:()=>X.value},{label:"考试方式",prop:"exam_way_id",type:"select",defaultValue:"",placeholder:"请选择考试方式",clearable:!0,multiple:!0,collapseTags:!0,optionData:()=>h.value}]}),r=l({field:[{prop:"project_name",label:"资格名称",minWidth:"160px"},{prop:"project_id",label:"编码",minWidth:"160px"},{prop:"exam_type",label:"考试类型",minWidth:"160px"},{prop:"exam_way",label:"考试方式",minWidth:"120px"},{prop:"is_active",label:"使用状态",type:"switch",minWidth:"100px",inactiveText:"禁用",activeText:"启用",inlinePrompt:!0,activeValue:!0,inactiveValue:!1,beforeChange:e=>T(e),onchange:e=>{}},{prop:"remark",label:"备注",minWidth:"160px",formatter:e=>{var t;return(t=e.remark)!=null?t:"-"}},{prop:"u_user_name",label:"更新人",minWidth:"120px"},{prop:"updated_time",label:"更新时间",minWidth:"160px",sortable:!0},{prop:"operation",label:"操作",type:"template",minWidth:"120px",templateGroup:[{title:()=>y("project-management/edit")?"编辑":"",clickBtn(e){_.value.openDialog("02",e)}},{title:()=>y("project-management/delete")?"删除":"",clickBtn(e){D(e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let v=l([]),b=null;J(()=>{Y(b,g.value,r.value),B(),V(),w(),i()}),Q(()=>{Z(b)});const i=()=>{let e=JSON.parse(JSON.stringify(m.value.getAllCardData())),{currentPage:t,pageSize:a}=r.value.pageOptions,n={current_page:t,page_size:a};n=Object.assign(e,n),E(n).then(o=>{o.code&&o.code===200&&(v.value=o.data.data,r.value.pageOptions.total=o.data.total)})},w=()=>{R({}).then(e=>{e.code&&e.code===200&&(e.data.data.forEach(t=>{t.value=t.exam_way_id,t.label=t.exam_way_name}),h.value=e.data.data)})},P=()=>{B(),m.value.resetFieldsFn(),i()},W=()=>{_.value.openDialog("01")},D=e=>{j.confirm("确定删除该资格吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let t={project_id:e.project_id};H(t).then(a=>{a.code&&a.code===200?(u.success(a.msg),i()):u.warning(a.msg)})}).catch(()=>{})},T=e=>new Promise((t,a)=>{let n="";e.is_active?n="禁用":n="启用",j.confirm(`确定${n}该资格吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let o={project_id:e.project_id,is_active:!e.is_active};N(o).then(s=>{s.code&&s.code===200?(u.success(s.msg),t(!0)):(u.error(s.msg),a(!1))}).catch(()=>{a(!1)})}).catch(()=>{a(!1)})}),V=()=>{A({}).then(e=>{e.code&&e.code===200&&(e.data.data.forEach(t=>{t.value=t.exam_type_id,t.label=t.exam_type_name}),C.value=e.data.data)})},z=e=>{r.value.pageOptions.pageSize=e,i()},F=e=>{r.value.pageOptions.currentPage=e,i()};return(e,t)=>{const a=p("form-component"),n=p("el-card"),o=p("el-button"),s=p("Auth"),S=p("table-component");return U(),$("div",ee,[f("div",te,[c(n,null,{default:d(()=>[f("div",{ref_key:"formDivRef",ref:g},[c(a,{ref_key:"formRef",ref:m,modelValue:x.value,"onUpdate:modelValue":t[0]||(t[0]=k=>x.value=k),"form-options":O,"is-query-btn":!0,onQueryDataFn:i},null,8,["modelValue","form-options"])],512)]),_:1}),c(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:d(()=>[c(s,{value:"project-management/add"},{default:d(()=>[f("div",ae,[c(o,{type:"primary",onClick:W},{default:d(()=>[G("创建")]),_:1})])]),_:1}),c(S,{minHeight:r.value.styleOptions.minHeight,"table-options":r.value,"table-data":I(v),onOnHandleSizeChange:z,onOnHandleCurrentChange:F},null,8,["minHeight","table-options","table-data"])]),_:1})]),c(L,{ref_key:"addProjectRef",ref:_,onQueryData:P},null,512)])}}}),pe=K(ne,[["__scopeId","data-v-09c94cf3"]]);export{pe as default};
