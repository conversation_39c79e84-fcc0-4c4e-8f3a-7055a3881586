import{_ as u,a as y}from"./judge-copy-BAX6vJkk.js";import{d as k,l as i,aO as C,r as a,o as D,g as H,h as s,e,b as n,f as r,ac as O,ad as V,_ as I}from"./index-B63pSD2p.js";const t=o=>(O("data-v-b7617012"),o=o(),V(),o),N={class:"flex border-t border-b border-1 border-gray-300 single-detail-container"},R={class:"w-[400px] pr-2 pt-5 pb-5 flex flex-col"},j=t(()=>e("div",{class:"pb-2"},"疑似抄袭考生",-1)),B={class:"flex-1"},M=t(()=>e("div",{class:"flex-1 p-5 flex flex-col border-l border-1 border-gray-300"},[e("section",{class:"flex-1"},[e("div",null,"考生密号（参考考生）：2003939999020008"),e("img",{src:u})]),e("p",{class:"flex justify-end"},[e("span",null,"作答字数：67")])],-1)),W={class:"flex-1 p-5 flex flex-col border-l border-1 border-gray-300 relative"},P={class:"flex-1"},z=t(()=>e("span",null,"考生密号（对比考生）：2003939999020001",-1)),T=t(()=>e("div",{class:"flex flex-1 justify-end"},[e("p",{class:"text-[16px]"},[r("答案相似度"),e("span",{class:"text-red-500 text-[18px]"},"100%")])],-1)),U=t(()=>e("img",{src:u},null,-1)),Y=t(()=>e("img",{class:"errorImage absolute w-[150px]",style:{bottom:"100px",right:"40px"},src:y},null,-1)),q=t(()=>e("p",{class:"flex justify-end"},[e("span",null,"作答字数：67")],-1)),A={style:{flex:"auto"}},E=k({__name:"multi-detail",props:{isShowDialog:{},isShowDialogModifiers:{}},emits:["update:isShowDialog"],setup(o){const f=i(null),l=C(o,"isShowDialog"),c=i({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"work_unit",label:"相似度",minWidth:"70px"},{prop:"role_name",label:"判定结果",minWidth:"90px",formatter:d=>"-"}],styleOptions:{isShowSort:!0,isShowSelection:!1,minHeight:"calc(100vh - 200px)"},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}}),m=i([{stu_secret_num:"111111",work_unit:"89%"}]),h=d=>{},x=()=>{l.value=!1},b=()=>{l.value=!1};return(d,p)=>{const g=a("table-component"),v=a("hander"),_=a("el-button"),w=a("el-dialog");return D(),H(w,{title:"对比详情",modelValue:l.value,"onUpdate:modelValue":p[0]||(p[0]=S=>l.value=S),fullscreen:""},{footer:s(()=>[e("div",A,[n(_,{onClick:x},{default:s(()=>[r("未抄袭")]),_:1}),n(_,{type:"primary",onClick:b},{default:s(()=>[r("判定抄袭")]),_:1})])]),default:s(()=>[e("div",N,[e("div",R,[j,e("div",B,[n(g,{ref_key:"tableRef",ref:f,minHeight:c.value.styleOptions.minHeight,"table-options":c.value,"table-data":m.value,onOnHandleRowClick:h},null,8,["minHeight","table-options","table-data"])])]),M,e("div",W,[e("section",P,[n(v,{class:"flex"},{default:s(()=>[z,T]),_:1}),U,Y]),q])])]),_:1},8,["modelValue"])}}}),J=I(E,[["__scopeId","data-v-b7617012"]]);export{J as default};
