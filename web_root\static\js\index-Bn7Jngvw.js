var j=(f,S,p)=>new Promise((b,r)=>{var R=o=>{try{d(p.next(o))}catch(h){r(h)}},u=o=>{try{d(p.throw(o))}catch(h){r(h)}},d=o=>o.done?b(o.value):Promise.resolve(o.value).then(R,u);d((p=p.apply(f,S)).next())});import{g as ae}from"./test-paper-management-DjV_45YZ.js";import te from"./manual-scoring-D8qJdgle.js";import{g as le,a as re,P as ne,c as oe,o as se}from"./progress-bar-BJkNWrHK.js";import{d as ie,l as n,P as pe,aN as ue,n as ce,ao as de,T as D,C as H,r as _,o as V,c as z,e as k,b as i,h as g,u as N,g as me,f as _e,aV as ge,ac as fe,ad as he,_ as ve}from"./index-B63pSD2p.js";import{d as q}from"./validate-Dc6ka3px.js";import{p as be,g as xe,a as we}from"./common-methods-BWkba4Bo.js";import{g as ye}from"./rules-form-CST-rV3v.js";import"./intelligent-marking-CyZREtXG.js";import"./handleImages-D-nd439N.js";import"./fullscreen-exit-line-DVwpkItP.js";import"./scoring-rules-BR2vQ7G3.js";const De=f=>(fe("data-v-d3ef8b77"),f=f(),he(),f),ke={class:"zf-first-box"},Se={class:"zf-second-box"},Re={class:"flex"},Ce=De(()=>k("span",{class:"mr-[3px] ml-[3px]"},"-",-1)),Oe={style:{height:"45px"}},Ve={key:1,class:"mark-wrapper"},Fe=ie({name:"op-engine-marking",__name:"index",setup(f){const S=n(null),p=n(null),b=n(null),r=n(null),R=n(null),u=n({rules:{minRange:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&!q(a))return t(new Error("请输入数字！"));t()}}],maxRange:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&!q(a))return t(new Error("请输入数字！"));t()}}]},scoreData:{maxRange:"",minRange:""}}),d=n([]),o=n([]),h=n({}),M=pe({column:3,labelWidth:"70px",itemWidth:"215px",rules:{ques_code:[{trigger:["blur","change"],validator:(e,a,t)=>{if(a&&a.length>30)return t(new Error("试题编号长度不能超过30！"));t()}}]},fields:[{label:"项目",prop:"project_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择项目",optionData:()=>be.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>d.value},{label:"试卷名称",prop:"paper_code",type:"select",defaultValue:"",placeholder:"请选择试卷名称",clearable:!0,optionData:()=>o.value},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"试题序号",prop:"ques_order",type:"input",defaultValue:"",placeholder:"请输入试题序号",clearable:!0},{label:"考生得分",prop:"stu_score",type:"template",defaultValue:"",placeholder:"请输入考生答案",clearable:!0},{label:"判断结果",prop:"mark_result",type:"select",defaultValue:null,placeholder:"请选择判断结果",clearable:!0,optionData:()=>[{label:"正确",value:1},{label:"错误",value:2},{label:"部分正确",value:3}]},{label:"评分状态",prop:"mark_state",type:"select",clearable:!0,defaultValue:[],multiple:!0,placeholder:"请选择评分状态",optionData:()=>[{label:"未评分",value:1},{label:"评分成功",value:2},{label:"评分失败",value:3},{label:"作答答案待人工判断",value:4}]},{label:"评分时间",prop:"search_time",type:"datetimerange",valueFormat:"YYYY-MM-DD HH:mm:ss",clearable:!0,defaultValue:"",optionData:()=>[]}]}),c=n({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"subject_name",label:"科目",minWidth:"80px"},{prop:"paper_name",label:"试卷名称",minWidth:"120px"},{prop:"ques_order",label:"试题序号",minWidth:"110px",sortable:!0},{prop:"ques_code",label:"试题编号",minWidth:"180px"},{prop:"stu_answer_format",label:"考生答案",minWidth:"180px"},{prop:"answer_parse",label:"评分解析",minWidth:"120px"},{prop:"total_score",label:"试题分数",minWidth:"90px"},{prop:"stu_score",label:"考生得分",minWidth:"100px"},{prop:"mark_role_format",label:"评分角色",minWidth:"120px"},{prop:"mark_state",label:"阅卷状态",minWidth:"100px"},{prop:"updated_time",label:"评分时间",minWidth:"170px"},{prop:"",label:"操作",type:"template",minWidth:"100px",fixed:"right",templateGroup:[{title:()=>ue("intelligent-marking/manual")?"评分详情":"",clickBtn(e){S.value.openDialog(e)}}]}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let x=n([]),C=n([]),w=n(null);ce(()=>{Q(),c.value.styleOptions.minHeight=window.innerHeight-276-b.value.clientHeight+"px",window.addEventListener("resize",F),xe(),ye(),v()});const F=()=>{var e;c.value.styleOptions.minHeight=window.innerHeight-276-((e=b.value)==null?void 0:e.clientHeight)+"px"};de(()=>{window.removeEventListener("resize",F)});const v=()=>{R.value.validate(e=>{e?E():D.warning("请按规则填写！")})},P=()=>{w.value=null},B=()=>{let e=[];const{minRange:a,maxRange:t}=u.value.scoreData;return(a||t)&&(a?t?Number(a)>Number(t)?e=[t,a]:e=[a,t]:e=[a,a]:e=[t,t]),e},E=()=>{let e=JSON.parse(JSON.stringify(r.value.getAllCardData())),{currentPage:a,pageSize:t}=c.value.pageOptions;e.current_page=a,e.page_size=t,e.stu_score_range=B(),e.mark_result&&(e.mark_result=Number(e.mark_result)),le(e).then(l=>{var s;l.code&&l.code===200?((s=l.data)==null?void 0:s.data.length)>0?(l.data.data.map(m=>{var y;m.stu_answer_format=m.stu_answer,m.standard_answer_format=(y=m.standard_answer_format)==null?void 0:y.join("；"),m.mark_role_format=U("mark_role",m.mark_role)}),x.value=l.data.data,c.value.pageOptions.total=l.data.total):(x.value=[],c.value.pageOptions.total=0):(D.warning(l.msg),x.value=[])})},L=(e,a)=>{e.prop==="project_id"?(d.value=[],o.value=[],r.value.getCardData("subject_id")&&r.value.setCardData("subject_id",null),r.value.getCardData("paper_code")&&r.value.setCardData("paper_code",null),a&&we(a).then(t=>{d.value=t||[]})):e.prop==="subject_id"?(o.value=[],r.value.getCardData("paper_code")&&r.value.setCardData("paper_code",null),a&&A()):e.prop==="search_time"&&r.value.setCardData("search_time",a)},A=()=>{const{project_id:e,subject_id:a}=r.value.getAllCardData();ae({project_id:e,subject_id:a,page_size:-1}).then(l=>{l.code&&l.code===200&&(l.data.data.forEach(s=>{s.label=s.paper_name,s.value=s.paper_code}),o.value=l.data.data)})},I=()=>{var e;((e=x.value)==null?void 0:e.length)>0?ge.confirm("确定启动操作题评分吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{T()}).catch(()=>{}):D.warning("暂无试题可评分！")},T=()=>j(this,null,function*(){yield oe().then(e=>{e.code&&e.code===200?(w.value=e.data.record_id,J(),H(()=>{p.value.openDialog(e.data)})):D.warning(e.msg)})}),Q=()=>(re().then(e=>{if(e)return w.value=e.record_id,H(()=>{p.value.showCancelBtnFn(),p.value.openDialog(e)}),e.record_id}),null),J=()=>{var a;let e={};if(((a=C.value)==null?void 0:a.length)>0){let t=[];C.value.map(l=>t.push({answer_id:l.answer_id,ques_id:l.ques_id,op_file:l.stu_answer,score:l.score})),e.answer_data_list=t}else e=JSON.parse(JSON.stringify(r.value.getAllCardData())),e.stu_score_range=B();e.record_id=w.value,se(e).then(t=>{t.code&&t.code===200||D.warning(t.msg)})},U=(e,a)=>{let t=[];e==="mark_result"?t=[{label:"正确",value:1},{label:"错误",value:2},{label:"部分正确",value:3}]:e==="mark_role"&&(t=[{label:"AI评分",value:1},{label:"人工评分",value:2}]);let l="";return t.map(s=>{s.value===a&&(l=s.label)}),e==="mark_result"&&a===4&&(l=""),l},Y=e=>{C.value=e},G=e=>{c.value.pageOptions.pageSize=e,v()},K=e=>{c.value.pageOptions.currentPage=e,v()};function X(){d.value=[]}return(e,a)=>{const t=_("el-input"),l=_("el-form-item"),s=_("el-form"),m=_("form-component"),y=_("el-card"),Z=_("el-button"),$=_("Auth"),ee=_("table-component");return V(),z("div",ke,[k("div",Se,[i(y,null,{default:g(()=>[k("div",{ref_key:"formDivRef",ref:b,class:"query-box"},[i(m,{ref_key:"formRef",ref:r,modelValue:h.value,"onUpdate:modelValue":a[2]||(a[2]=W=>h.value=W),"form-options":M,"is-query-btn":!0,onQueryDataFn:v,onOnchangeFn:L,onResetFields:X},{stu_score:g(({scope:W})=>[i(s,{ref_key:"stuScoreFormRef",ref:R,model:u.value.scoreData,rules:u.value.rules},{default:g(()=>[k("div",Re,[i(l,{class:"!mr-[0px]",prop:"minRange"},{default:g(()=>[i(t,{modelValue:u.value.scoreData.minRange,"onUpdate:modelValue":a[0]||(a[0]=O=>u.value.scoreData.minRange=O),style:{width:"101px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1}),Ce,i(l,{prop:"maxRange"},{default:g(()=>[i(t,{modelValue:u.value.scoreData.maxRange,"onUpdate:modelValue":a[1]||(a[1]=O=>u.value.scoreData.maxRange=O),style:{width:"102px"},placeholder:"考生得分",clearable:""},null,8,["modelValue"])]),_:1})])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","form-options"])],512)]),_:1}),i(y,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:g(()=>[k("div",Oe,[N(w)?(V(),me(ne,{key:0,ref_key:"progressBarRef",ref:p,onQueryData:v,onDeleteRecordId:P,style:{"margin-bottom":"10px",width:"50%"}},null,512)):(V(),z("div",Ve,[i($,{value:"intelligent-marking/intelligent"},{default:g(()=>[i(Z,{type:"primary",onClick:I},{default:g(()=>[_e("操作题评分")]),_:1})]),_:1})]))]),i(ee,{"min-height":c.value.styleOptions.minHeight,"table-options":c.value,"table-data":N(x),onOnHandleSelectionChange:Y,onOnHandleSizeChange:G,onOnHandleCurrentChange:K},null,8,["min-height","table-options","table-data"])]),_:1})]),i(te,{ref_key:"manualScoringRef",ref:S,onQueryData:v},null,512)])}}}),Ie=ve(Fe,[["__scopeId","data-v-d3ef8b77"]]);export{Ie as default};
