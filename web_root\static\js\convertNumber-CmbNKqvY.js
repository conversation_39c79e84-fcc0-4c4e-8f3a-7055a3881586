function l(t){const n=["","十","百","千"],e=["零","一","二","三","四","五","六","七","八","九"];if(t===0)return e[0];if(t<=10)return e[t];let r="",o=!1;const c=t.toString();for(let s=0;s<c.length;s++){const i=c[c.length-1-s],a=e[i],u=n[s];i==="0"?o=!0:(o&&(r=e[0]+r,o=!1),r=a+u+r)}return r.replace(/零+/g,"零").replace(/零$/,"").replace(/^一十/,"十")}function f(t,n=!1){if(typeof t!="number"||t<1)return t.toString();let e="";for(;t>0;){const r=(t-1)%26,o=n?65:97;e=String.fromCharCode(o+r)+e,t=Math.floor((t-1)/26)}return e||t.toString()}function g(t){return t>=1&&t<=20?String.fromCodePoint(9312+t-1):t.toString()}function S(t,n){const e=t.trim(),r=Number(n);switch(e){case"一":return l(r);case"1":case"1．":return r.toString();case"1、":return r.toString()+"、";case"1.":return r.toString()+".";case"(1)":return`(${r.toString()})`;case"（1）":return`（${r.toString()}）`;case"1）":return`${r}）`;case"1)":return`${r})`;case"a":case"A":return f(r,e==="A");case"①":return g(r);default:return r.toString()+"．"}}export{S as a,l as t};
