import{o as y,c as b,e as B,ae as D,q as x,F as S,p as X,U as M,A as v,_ as $}from"./index-B63pSD2p.js";const k={y:{t:"top",m:"marginTop",b:"bottom"},x:{l:"left",m:"marginLeft",r:"right"}};function z(t){t==null||t.forEach((i,e)=>{document.documentElement.addEventListener(e,i)})}function L(t){t==null||t.forEach((i,e)=>{document.documentElement.removeEventListener(e,i)})}const Y={name:"vue-drag-resize",emits:["clicked","dragging","dragstop","resizing","resizestop","activated","deactivated"],props:{stickSize:{type:Number,default:8},parentScaleX:{type:Number,default:1},parentScaleY:{type:Number,default:1},isActive:{type:Boolean,default:!1},preventActiveBehavior:{type:Boolean,default:!1},isDraggable:{type:Boolean,default:!0},isResizable:{type:Boolean,default:!0},aspectRatio:{type:Boolean,default:!1},parentLimitation:{type:Boolean,default:!1},snapToGrid:{type:Boolean,default:!1},gridX:{type:Number,default:50,validator(t){return t>=0}},gridY:{type:Number,default:50,validator(t){return t>=0}},parentW:{type:Number,default:0,validator(t){return t>=0}},parentH:{type:Number,default:0,validator(t){return t>=0}},w:{type:[String,Number],default:200,validator(t){return typeof t=="string"?t==="auto":t>=0}},h:{type:[String,Number],default:200,validator(t){return typeof t=="string"?t==="auto":t>=0}},minw:{type:Number,default:50,validator(t){return t>=0}},minh:{type:Number,default:50,validator(t){return t>=0}},x:{type:Number,default:0,validator(t){return typeof t=="number"}},y:{type:Number,default:0,validator(t){return typeof t=="number"}},z:{type:[String,Number],default:"auto",validator(t){return typeof t=="string"?t==="auto":t>=0}},dragHandle:{type:String,default:null},dragCancel:{type:String,default:null},sticks:{type:Array,default(){return["tl","tm","tr","mr","br","bm","bl","ml"]}},axis:{type:String,default:"both",validator(t){return["x","y","both","none"].indexOf(t)!==-1}},contentClass:{type:String,required:!1,default:""}},data(){return{fixAspectRatio:null,active:null,zIndex:null,parentWidth:null,parentHeight:null,left:null,top:null,right:null,bottom:null,minHeight:null,_uid:null}},beforeCreate(){this.stickDrag=!1,this.bodyDrag=!1,this.dimensionsBeforeMove={pointerX:0,pointerY:0,x:0,y:0,w:0,h:0},this.limits={left:{min:null,max:null},right:{min:null,max:null},top:{min:null,max:null},bottom:{min:null,max:null}},this.currentStick=null},mounted(){this._uid="drag"+Math.random(),this.parentElement=this.$el.parentNode,this.parentWidth=this.parentW?this.parentW:this.parentElement.clientWidth,this.parentHeight=this.parentH?this.parentH:this.parentElement.clientHeight,this.left=this.x,this.top=this.y,this.right=this.parentWidth-(this.w==="auto"?this.$refs.container.scrollWidth:this.w)-this.left,this.bottom=this.parentHeight-(this.h==="auto"?this.$refs.container.scrollHeight:this.h)-this.top,this.domEvents=new Map([["mousemove",this.move],["mouseup",this.up],["mouseleave",this.up],["mousedown",this.deselect],["touchmove",this.move],["touchend",this.up],["touchcancel",this.up],["touchstart",this.up]]),z(this.domEvents),this.dragHandle&&[...this.$el.querySelectorAll(this.dragHandle)].forEach(t=>{t.setAttribute("data-drag-handle",this._uid)}),this.dragCancel&&[...this.$el.querySelectorAll(this.dragCancel)].forEach(t=>{t.setAttribute("data-drag-cancel",this._uid)})},beforeUnmount(){L(this.domEvents)},methods:{deselect(){this.preventActiveBehavior||(this.active=!1)},move(t){if(!this.stickDrag&&!this.bodyDrag)return;t.stopPropagation();const i=typeof t.pageX!="undefined"?t.pageX:t.touches[0].pageX,e=typeof t.pageY!="undefined"?t.pageY:t.touches[0].pageY,{dimensionsBeforeMove:n}=this,s={x:(n.pointerX-i)/this.parentScaleX,y:(n.pointerY-e)/this.parentScaleY};if(this.stickDrag&&this.stickMove(s),this.bodyDrag){if(this.axis==="x")s.y=0;else if(this.axis==="y")s.x=0;else if(this.axis==="none")return;this.bodyMove(s)}},up(t){this.stickDrag?this.stickUp(t):this.bodyDrag&&this.bodyUp(t)},bodyDown(t){const{target:i,button:e}=t;if(this.preventActiveBehavior||(this.active=!0),e&&e!==0||(this.$emit("clicked",t),!this.active)||this.dragHandle&&i.getAttribute("data-drag-handle")!==this._uid.toString()||this.dragCancel&&i.getAttribute("data-drag-cancel"))return;typeof t.stopPropagation!="undefined"&&t.stopPropagation(),typeof t.preventDefault!="undefined"&&t.preventDefault(),this.isDraggable&&(this.bodyDrag=!0);const n=typeof t.pageX!="undefined"?t.pageX:t.touches[0].pageX,s=typeof t.pageY!="undefined"?t.pageY:t.touches[0].pageY;this.saveDimensionsBeforeMove({pointerX:n,pointerY:s}),this.parentLimitation&&(this.limits=this.calcDragLimitation())},bodyMove(t){const{dimensionsBeforeMove:i,parentWidth:e,parentHeight:n,gridX:s,gridY:h,width:l,height:d}=this;let r=i.top-t.y,m=i.bottom+t.y,o=i.left-t.x,a=i.right+t.x;if(this.snapToGrid){let p=!0,w=!0,u=r-Math.floor(r/h)*h,f=n-m-Math.floor((n-m)/h)*h,c=o-Math.floor(o/s)*s,g=e-a-Math.floor((e-a)/s)*s;u>h/2&&(u-=h),f>h/2&&(f-=h),c>s/2&&(c-=s),g>s/2&&(g-=s),Math.abs(f)<Math.abs(u)&&(p=!1),Math.abs(g)<Math.abs(c)&&(w=!1),r-=p?u:f,m=n-d-r,o-=w?c:g,a=e-l-o}({newLeft:this.left,newRight:this.right,newTop:this.top,newBottom:this.bottom}=this.rectCorrectionByLimit({newLeft:o,newRight:a,newTop:r,newBottom:m})),this.$emit("dragging",this.rect)},bodyUp(){this.bodyDrag=!1,this.$emit("dragging",this.rect),this.$emit("dragstop",this.rect),this.dimensionsBeforeMove={pointerX:0,pointerY:0,x:0,y:0,w:0,h:0},this.limits={left:{min:null,max:null},right:{min:null,max:null},top:{min:null,max:null},bottom:{min:null,max:null}}},stickDown(t,i,e=!1){if((!this.isResizable||!this.active)&&!e)return;this.stickDrag=!0;const n=typeof i.pageX!="undefined"?i.pageX:i.touches[0].pageX,s=typeof i.pageY!="undefined"?i.pageY:i.touches[0].pageY;this.saveDimensionsBeforeMove({pointerX:n,pointerY:s}),this.currentStick=t,this.limits=this.calcResizeLimits()},saveDimensionsBeforeMove({pointerX:t,pointerY:i}){this.dimensionsBeforeMove.pointerX=t,this.dimensionsBeforeMove.pointerY=i,this.dimensionsBeforeMove.left=this.left,this.dimensionsBeforeMove.right=this.right,this.dimensionsBeforeMove.top=this.top,this.dimensionsBeforeMove.bottom=this.bottom,this.dimensionsBeforeMove.width=this.width,this.dimensionsBeforeMove.height=this.height,this.aspectFactor=this.width/this.height},stickMove(t){const{currentStick:i,dimensionsBeforeMove:e,gridY:n,gridX:s,snapToGrid:h,parentHeight:l,parentWidth:d}=this;let r=e.top,m=e.bottom,o=e.left,a=e.right;switch(i[0]){case"b":m=e.bottom+t.y,h&&(m=l-Math.round((l-m)/n)*n);break;case"t":r=e.top-t.y,h&&(r=Math.round(r/n)*n);break}switch(i[1]){case"r":a=e.right+t.x,h&&(a=d-Math.round((d-a)/s)*s);break;case"l":o=e.left-t.x,h&&(o=Math.round(o/s)*s);break}({newLeft:o,newRight:a,newTop:r,newBottom:m}=this.rectCorrectionByLimit({newLeft:o,newRight:a,newTop:r,newBottom:m})),this.aspectRatio&&({newLeft:o,newRight:a,newTop:r,newBottom:m}=this.rectCorrectionByAspectRatio({newLeft:o,newRight:a,newTop:r,newBottom:m})),this.left=o,this.right=a,this.top=r,this.bottom=m,this.$emit("resizing",this.rect)},stickUp(){this.stickDrag=!1,this.dimensionsBeforeMove={pointerX:0,pointerY:0,x:0,y:0,w:0,h:0},this.limits={left:{min:null,max:null},right:{min:null,max:null},top:{min:null,max:null},bottom:{min:null,max:null}},this.$emit("resizing",this.rect),this.$emit("resizestop",this.rect)},calcDragLimitation(){const{parentWidth:t,parentHeight:i}=this;return{left:{min:0,max:t-this.width},right:{min:0,max:t-this.width},top:{min:0,max:i-this.height},bottom:{min:0,max:i-this.height}}},calcResizeLimits(){const{aspectFactor:t,width:i,height:e,bottom:n,top:s,left:h,right:l}=this;let{minh:d,minw:r}=this;const m=this.parentLimitation?0:null;this.aspectRatio&&(r/d>t?d=r/t:r=t*d);const o={left:{min:m,max:h+(i-r)},right:{min:m,max:l+(i-r)},top:{min:m,max:s+(e-d)},bottom:{min:m,max:n+(e-d)}};if(this.aspectRatio){const a={left:{min:h-Math.min(s,n)*t*2,max:h+(e-d)/2*t*2},right:{min:l-Math.min(s,n)*t*2,max:l+(e-d)/2*t*2},top:{min:s-Math.min(h,l)/t*2,max:s+(i-r)/2/t*2},bottom:{min:n-Math.min(h,l)/t*2,max:n+(i-r)/2/t*2}};this.currentStick[0]==="m"?(o.left={min:Math.max(o.left.min,a.left.min),max:Math.min(o.left.max,a.left.max)},o.right={min:Math.max(o.right.min,a.right.min),max:Math.min(o.right.max,a.right.max)}):this.currentStick[1]==="m"&&(o.top={min:Math.max(o.top.min,a.top.min),max:Math.min(o.top.max,a.top.max)},o.bottom={min:Math.max(o.bottom.min,a.bottom.min),max:Math.min(o.bottom.max,a.bottom.max)})}return o},sideCorrectionByLimit(t,i){let e=i;return t.min!==null&&i<t.min?e=t.min:t.max!==null&&t.max<i&&(e=t.max),e},rectCorrectionByLimit(t){const{limits:i}=this;let{newRight:e,newLeft:n,newBottom:s,newTop:h}=t;return n=this.sideCorrectionByLimit(i.left,n),e=this.sideCorrectionByLimit(i.right,e),h=this.sideCorrectionByLimit(i.top,h),s=this.sideCorrectionByLimit(i.bottom,s),{newLeft:n,newRight:e,newTop:h,newBottom:s}},rectCorrectionByAspectRatio(t){let{newLeft:i,newRight:e,newTop:n,newBottom:s}=t;const{parentWidth:h,parentHeight:l,currentStick:d,aspectFactor:r,dimensionsBeforeMove:m}=this;let o=h-i-e,a=l-n-s;if(d[1]==="m"){const p=a-m.height;i-=p*r/2,e-=p*r/2}else if(d[0]==="m"){const p=o-m.width;n-=p/r/2,s-=p/r/2}else o/a>r?(o=r*a,d[1]==="l"?i=h-e-o:e=h-i-o):(a=o/r,d[0]==="t"?n=l-s-a:s=l-n-a);return{newLeft:i,newRight:e,newTop:n,newBottom:s}}},computed:{positionStyle(){return{top:this.top+"px",left:this.left+"px",zIndex:this.zIndex}},sizeStyle(){return{width:this.width+"px",height:this.height+"px"}},vdrStick(){return t=>{const i={width:`${this.stickSize/this.parentScaleX}px`,height:`${this.stickSize/this.parentScaleY}px`};return i[k.y[t[0]]]=`${this.stickSize/this.parentScaleX/-2}px`,i[k.x[t[1]]]=`${this.stickSize/this.parentScaleX/-2}px`,i}},width(){return this.parentWidth-this.left-this.right},height(){return this.parentHeight-this.top-this.bottom},rect(){return{left:Math.round(this.left),top:Math.round(this.top),width:Math.round(this.width),height:Math.round(this.height)}}},watch:{active(t){t?this.$emit("activated"):this.$emit("deactivated")},isActive:{immediate:!0,handler(t){this.active=t}},z:{immediate:!0,handler(t){(t>=0||t==="auto")&&(this.zIndex=t)}},x:{handler(t,i){if(this.stickDrag||this.bodyDrag||t===this.left)return;const e=i-t;this.bodyDown({pageX:this.left,pageY:this.top}),this.bodyMove({x:e,y:0}),this.$nextTick(()=>{this.bodyUp()})}},y:{handler(t,i){if(this.stickDrag||this.bodyDrag||t===this.top)return;const e=i-t;this.bodyDown({pageX:this.left,pageY:this.top}),this.bodyMove({x:0,y:e}),this.$nextTick(()=>{this.bodyUp()})}},w:{handler(t,i){if(this.stickDrag||this.bodyDrag||t===this.width)return;const e="mr",n=i-t;this.stickDown(e,{pageX:this.right,pageY:this.top+this.height/2},!0),this.stickMove({x:n,y:0}),this.$nextTick(()=>{this.stickUp()})}},h:{handler(t,i){if(this.stickDrag||this.bodyDrag||t===this.height)return;const e="bm",n=i-t;this.stickDown(e,{pageX:this.left+this.width/2,pageY:this.bottom},!0),this.stickMove({x:0,y:n}),this.$nextTick(()=>{this.stickUp()})}},parentW(t){this.right=t-this.width-this.left,this.parentWidth=t},parentH(t){this.bottom=t-this.height-this.top,this.parentHeight=t}}},H=["onMousedown","onTouchstart"];function C(t,i,e,n,s,h){return y(),b("div",{class:M(["vdr",`${t.active||t.isActive?"active":"inactive"} ${t.contentClass?t.contentClass:""}`]),style:x(t.positionStyle),onMousedown:i[0]||(i[0]=l=>t.bodyDown(l)),onTouchstart:i[1]||(i[1]=l=>t.bodyDown(l)),onTouchend:i[2]||(i[2]=l=>t.up(l)),ref:"container",tabindex:"0"},[B("div",{style:x(t.sizeStyle),class:"content-container",ref:"container2"},[D(t.$slots,"default")],4),(y(!0),b(S,null,X(t.sticks,l=>(y(),b("div",{class:M(["vdr-stick",["vdr-stick-"+l,t.isResizable?"":"not-resizable"]]),onMousedown:v(d=>t.stickDown(l,d),["stop","prevent"]),onTouchstart:v(d=>t.stickDown(l,d),["stop","prevent"]),style:x(t.vdrStick(l))},null,46,H))),256))],38)}const T=$(Y,[["render",C]]);export{T as V};
