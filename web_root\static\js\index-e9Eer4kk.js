import{d as k,l as n,P as w,aN as m,n as D,ao as H,r as p,o as W,c as F,e as f,b as r,h as g,u as V,_ as j}from"./index-B63pSD2p.js";const R={class:"zf-first-box"},B={class:"zf-second-box"},N=k({name:"ai-marking-task",__name:"index",setup(P){const s=n(null),c=n(null),_=n([]),u=n({}),h=w({column:3,labelWidth:"100px",itemWidth:"240px",rules:{},fields:[{label:"项目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择项目",optionData:()=>[]},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择科目",optionData:()=>[]},{label:"试卷名称",prop:"paper_name",type:"input",defaultValue:"",placeholder:"请输入试卷名称",clearable:!0}]}),t=n({field:[{prop:"project_name",label:"项目",minWidth:"120px"},{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"paper_name",label:"试卷名称",minWidth:"160px"},{prop:"total_score",label:"试卷总分",minWidth:"100px"},{prop:"grade",label:"考生总分",minWidth:"100px"},{prop:"created_time",label:"创建时间",minWidth:"160px",sortable:!0},{prop:"",label:"操作",type:"template",minWidth:"120px",templateGroup:[{title:()=>m("marking-group/edit")?"编辑":"",clickBtn(e){createGroupRef.value.openDialog("02",e)}},{title:()=>m("marking-group/delete")?"删除":"",clickBtn(e){deleteManualGroupFn(e)}}]}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let b=n([]);D(()=>{l(),window.addEventListener("resize",d)});const d=()=>{var e;t.value.styleOptions.minHeight=window.innerHeight-276-((e=s.value)==null?void 0:e.clientHeight)+"px"};H(()=>{window.removeEventListener("resize",d)});const v=e=>{},x=(e,a)=>{},l=()=>{let e=JSON.parse(JSON.stringify(c.value.getAllCardData())),{currentPage:a,pageSize:i}=t.value.pageOptions,o={current_page:a,page_size:i};o=Object.assign(e,o)},O=e=>{t.value.pageOptions.pageSize=e,l()},y=e=>{t.value.pageOptions.currentPage=e,l()};function S(){_.value=[]}return(e,a)=>{const i=p("form-component"),o=p("el-card"),C=p("table-component");return W(),F("div",R,[f("div",B,[r(o,null,{default:g(()=>[f("div",{ref_key:"formDivRef",ref:s},[r(i,{ref_key:"formRef",ref:c,modelValue:u.value,"onUpdate:modelValue":a[0]||(a[0]=z=>u.value=z),"form-options":h,"is-query-btn":!0,onOnchangeFn:x,onQueryDataFn:l,onResetFields:S},null,8,["modelValue","form-options"])],512)]),_:1}),r(o,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:g(()=>[r(C,{minHeight:t.value.styleOptions.minHeight,"table-options":t.value,"table-data":V(b),onOnHandleSelectionChange:v,onOnHandleSizeChange:O,onOnHandleCurrentChange:y},null,8,["minHeight","table-options","table-data"])]),_:1})])])}}}),E=j(N,[["__scopeId","data-v-aefc33a2"]]);export{E as default};
