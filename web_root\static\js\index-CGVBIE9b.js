import{d as w,l,i as H,P as N,aN as x,T as _,n as P,ao as A,aV as L,r as p,o as T,c as E,e as i,b as r,h as m,f as I,u as y,ac as M,ad as Y,_ as J}from"./index-B63pSD2p.js";import Q from"./add-scoring-rules-COt5dSEl.js";import{p as S,g as U,a as q}from"./common-methods-BWkba4Bo.js";import{b as G,d as K}from"./scoring-rules-BR2vQ7G3.js";import{c as X,a as Z}from"./calculateTableHeight-BjE6OFD1.js";import"./formItem-Cpvf-Fua.js";import"./validate-Dc6ka3px.js";import"./rules-form-CST-rV3v.js";import"./test-paper-management-DjV_45YZ.js";const $=c=>(M("data-v-af4b6525"),c=c(),Y(),c),ee={class:"zf-first-box"},te={class:"zf-second-box"},ae={class:"flex"},oe=$(()=>i("div",{class:"zf-tit-box"},[i("i"),i("span",null,"评分规则")],-1)),ne={class:"add-btn-box"},le=w({name:"scoring-rules",__name:"index",setup(c){const u=l(null);H();const d=l([]),g=l(null),f=l(null),h=l({}),O=N({column:3,labelWidth:"70px",itemWidth:"250px",rules:{c_name:[{trigger:["blur","change"],validator:(t,a,e)=>{if(a&&a.length>50)return e(new Error("创建人名称长度不能超过50！"));e()}}]},fields:[{label:"所属资格",prop:"project_id",type:"select",placeholder:"请选择所属资格",defaultValue:"",clearable:!0,optionData:()=>S.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,placeholder:"请选择所属科目",defaultValue:"",optionData:()=>d.value},{label:"考试年份",prop:"rule_year",type:"year",defaultValue:"",valueFormat:"YYYY",placeholder:"请选择考试年份",clearable:!0},{label:"创建人",prop:"c_name",type:"input",defaultValue:"",placeholder:"请输入创建人",clearable:!0}]}),o=l({field:[{prop:"rule_name",label:"规则名称",minWidth:"200px"},{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"rule_year",label:"考试年份",minWidth:"200px"},{prop:"c_name",label:"创建人",minWidth:"120px"},{prop:"created_time",label:"创建时间",minWidth:"160px",sortable:!0},{prop:"updated_time",label:"更新时间",minWidth:"150px"},{prop:"remark",label:"备注",minWidth:"180px"},{prop:"",label:"操作",type:"template",minWidth:"160px",fixed:"right",templateGroup:[{title:()=>x("scoring-rules/edit")?"编辑":"",clickBtn(t){u.value.openDialog("02",t)}},{title:()=>"详情",clickBtn(t){u.value.openDialog("03",t)}},{title:()=>x("scoring-rules/delete")?"删除":"",clickBtn(t){t.lock_state===2?_.warning("该规则已锁定！不可删除！"):k(t)}}]}],styleOptions:{isShowSort:!0,minHeight:window.innerHeight-330+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let b=l([]),v=null;P(()=>{X(v,g.value,o.value),U(),s()}),A(()=>{Z(v)});const C=(t,a)=>{t.prop==="project_id"&&(a?q(a).then(e=>{d.value=e||[]}):(d.value=[],f.value.setCardData("subject","")))},s=()=>{let{currentPage:t,pageSize:a}=o.value.pageOptions,e=JSON.parse(JSON.stringify(f.value.getAllCardData()));e.current_page=t,e.page_size=a,e.year&&(e.year=Number(e.year)),G(e).then(n=>{n.code&&n.code===200&&(b.value=n.data.data,o.value.pageOptions.total=n.data.total)})},R=()=>{u.value.openDialog("01")},k=t=>{L.confirm("确定删除该项评分规则吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let a={rule_id:t.rule_id};K(a).then(e=>{e.code&&e.code===200?(_.success(e.msg),s()):_.warning(e.msg)})}).catch(()=>{})},D=t=>{o.value.pageOptions.pageSize=t,s()},j=t=>{o.value.pageOptions.currentPage=t,s()};function B(){d.value=[]}return(t,a)=>{const e=p("form-component"),n=p("el-card"),F=p("el-button"),z=p("Auth"),V=p("table-component");return T(),E("div",ee,[i("div",te,[r(n,null,{default:m(()=>[i("div",{ref_key:"formDivRef",ref:g},[r(e,{ref_key:"formRef",ref:f,modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=W=>h.value=W),"form-options":O,onQueryDataFn:s,onOnchangeFn:C,onResetFields:B},null,8,["modelValue","form-options"])],512)]),_:1}),r(n,{class:"mt-[5px] pb-[20px]"},{default:m(()=>[i("div",ae,[oe,r(z,{value:"scoring-rules/add"},{default:m(()=>[i("div",ne,[r(F,{type:"primary",onClick:R},{default:m(()=>[I("创建")]),_:1})])]),_:1})]),r(V,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":y(b),onOnHandleSizeChange:D,onOnHandleCurrentChange:j},null,8,["minHeight","table-options","table-data"])]),_:1})]),r(Q,{ref_key:"addScoringRulesRef",ref:u,onQueryData:s,projectList:y(S)},null,8,["projectList"])])}}}),_e=J(le,[["__scopeId","data-v-af4b6525"]]);export{_e as default};
