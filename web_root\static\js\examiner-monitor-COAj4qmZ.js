var B=Object.defineProperty,E=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var z=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable;var V=(i,n,t)=>n in i?B(i,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[n]=t,W=(i,n)=>{for(var t in n||(n={}))N.call(n,t)&&V(i,t,n[t]);if(z)for(var t of z(n))A.call(n,t)&&V(i,t,n[t]);return i},C=(i,n)=>E(i,U(n));var y=(i,n,t)=>new Promise((k,e)=>{var D=s=>{try{d(t.next(s))}catch(u){e(u)}},_=s=>{try{d(t.throw(s))}catch(u){e(u)}},d=s=>s.done?k(s.value):Promise.resolve(s.value).then(D,_);d((t=t.apply(i,n)).next())});import{g as I,a as Q,q as $}from"./formal-monitor-BXl8VaF0.js";import{c as G,a as J}from"./calculateTableHeight-BjE6OFD1.js";import{p as K,g as X,a as Y}from"./common-methods-BWkba4Bo.js";import{d as Z}from"./downloadRequest-CdE2PBjt.js";import{d as ee,l as m,P as ae,n as te,ao as le,r as f,o as ne,c as oe,b as v,h as j,ae as re,e as F,f as se,aV as ie,T as R,C as ue,_ as pe}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const ce={class:"zf-flex-end"},de=ee({__name:"examiner-monitor",setup(i){const n=[{value:1,label:"未发起"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}],t=m(null),k=m(null),e=m({}),D=ae({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>K.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>_.value},{label:"任务",prop:"task_name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择任务",optionData:()=>d.value},{label:"执行状态",prop:"round_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择执行状态",optionData:()=>n},{label:"评阅员",prop:"name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择评阅员",optionData:()=>s.value}]}),_=m([]),d=m([]),s=m([]),u=m({field:[{prop:"project_name",label:"所属资格",minWidth:"220px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"name",label:"评阅员",minWidth:"140px"},{prop:"ques_group_name",label:"题组",minWidth:"140px"},{prop:"group_name",label:"小组",minWidth:"140px"},{prop:"reviewed_count",label:"已阅量",minWidth:"120px"},{prop:"average_speed1",label:"平均速度（份/时）",minWidth:"100px"},{prop:"average_speed2",label:"平均评分时间（秒/份）",minWidth:"120px"},{prop:"max_speed",label:"最长评分时间（秒）",minWidth:"120px"},{prop:"min_speed",label:"最短评分时间（秒）",minWidth:"120px"},{prop:"average_score",label:"平均分",minWidth:"120px"},{prop:"max_score",label:"最高分",minWidth:"120px"},{prop:"min_score",label:"最低分",minWidth:"120px"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),O=m([]);let w=null;te(()=>{X(),b(),g(),G(w,t.value,u.value,!0,58),h()}),le(()=>{J(w)});const M=(a,o)=>y(this,null,function*(){if(a.prop==="project_id"){if(_.value=[],d.value=[],s.value=[],e.value.subject_id&&(e.value.subject_id=null),e.value.task_name&&(e.value.task_name=null),e.value.name&&(e.value.name=null),o){const r=yield Y(o);_.value=r||[],b()}g()}a.prop==="subject_id"&&(d.value=[],s.value=[],e.value.task_name&&(e.value.task_name=null),e.value.name&&(e.value.name=null),o&&b(),g())}),b=()=>y(this,null,function*(){var p,c;const o=(c=(p=(yield I({page_size:-1,project_id:e.value.project_id,subject_id:e.value.subject_id,task_type:1})).data)==null?void 0:p.data)!=null?c:[],r=new Map(o.map(l=>[l.task_name,l]));d.value=[...r.values()].map(l=>({label:l.task_name,value:l.task_name}))}),g=()=>y(this,null,function*(){var c;const o=((c=(yield Q({page_size:"99999",user_type:1,system_user_type:2,role_id:"3"})).data)==null?void 0:c.data)||[];let r=o;e.value.project_id&&(r=o.filter(l=>l.project_id_list.includes(e.value.project_id))),e.value.subject_id&&(r=o.filter(l=>l.project_id_list.includes(e.value.project_id)&&l.subject_id_list.includes(e.value.subject_id)));const p=new Map(r.map(l=>[l.name,l]));s.value=[...p.values()].map(l=>({label:l.name,value:l.name}))});function S(){let{currentPage:a,pageSize:o}=u.value.pageOptions;return W({current_page:a,page_size:o,task_type:1,round_state_list:e.value.round_state?[e.value.round_state]:[],round_count:"1"},e.value)}const h=()=>{$(S()).then(a=>{var r,p,c;const o=((r=a.data)==null?void 0:r.data)||[];O.value=o,u.value.pageOptions.total=(c=(p=a.data)==null?void 0:p.total)!=null?c:0})},L=()=>{ie.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Z("post","/v1/survey_monitor/person_survey_monitor_export",void 0,{},C(W({},S()),{page_size:-1}),"xlsx").then(a=>{a&&a.data&&a.data.type=="application/json"&&R({message:"暂无导出信息！",type:"warning"})}).catch(()=>{R({type:"error",message:"导出失败"})})}).catch(()=>{})},T=a=>{u.value.pageOptions.pageSize=a,h()},q=a=>{u.value.pageOptions.currentPage=a,h()};function H(){_.value=[],d.value=[],s.value=[],ue(()=>{b(),g()})}return(a,o)=>{const r=f("form-component"),p=f("el-card"),c=f("el-button"),l=f("Auth"),P=f("table-component");return ne(),oe("div",null,[v(p,null,{default:j(()=>[re(a.$slots,"tabs",{},void 0,!0),F("div",{ref_key:"formDivRef",ref:t},[v(r,{ref_key:"formRef",ref:k,modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=x=>e.value=x),"form-options":D,"is-query-btn":!0,onOnchangeFn:M,onQueryDataFn:h,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:3}),v(p,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:j(()=>{var x;return[F("div",ce,[v(l,{value:"formal-monitor/examiner-export"},{default:j(()=>[v(c,{type:"primary",onClick:L},{default:j(()=>[se("导出")]),_:1})]),_:1})]),v(P,{minHeight:(x=u.value.styleOptions)==null?void 0:x.minHeight,"table-options":u.value,"table-data":O.value,onOnHandleSizeChange:T,onOnHandleCurrentChange:q},null,8,["minHeight","table-options","table-data"])]}),_:1})])}}}),xe=pe(de,[["__scopeId","data-v-c4a2b194"]]);export{xe as default};
