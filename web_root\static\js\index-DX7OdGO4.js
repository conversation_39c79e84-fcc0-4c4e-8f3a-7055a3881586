import m from"./await-confirmed-bELUdhOj.js";import c from"./have-confirmed-DAwSnCyP.js";import{d as i,l as _,r as s,o as d,c as p,e as f,b as e,h as o,_ as b}from"./index-B63pSD2p.js";import"./plagiarizing-answer-DCON8hS9.js";import"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./confirm-dialog-V4wtUUxX.js";import"./batch-confirm-dialog-OWMnJuCJ.js";const u={class:"zf-first-box"},v={class:"zf-second-box"},x=i({name:"objective-check",__name:"index",setup(h){const a=_("have");return(k,t)=>{const n=s("el-tab-pane"),r=s("el-tabs");return d(),p("div",u,[f("div",v,[e(r,{modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=l=>a.value=l),class:"monitor-container dark:!bg-black eye-box"},{default:o(()=>[e(n,{label:"成绩已确定",name:"have"},{default:o(()=>[e(c)]),_:1}),e(n,{label:"成绩待确定（2）",name:"await"},{default:o(()=>[e(m)]),_:1})]),_:1},8,["modelValue"])])])}}}),A=b(x,[["__scopeId","data-v-bce68a70"]]);export{A as default};
