#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将一键启动器打包成独立的exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("× PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("× PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['一键启动器.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.scrolledtext'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='卓帆考试系统一键启动器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 配置文件已创建")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean", 
            "launcher.spec"
        ])
        print("✓ exe文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"× exe文件构建失败: {e}")
        return False

def cleanup_build_files():
    """清理构建过程中的临时文件"""
    print("清理临时文件...")
    
    # 要删除的目录和文件
    cleanup_items = ['build', '__pycache__', 'launcher.spec']
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"✓ 已删除目录: {item}")
            else:
                os.remove(item)
                print(f"✓ 已删除文件: {item}")

def copy_exe_to_root():
    """将生成的exe文件复制到根目录"""
    dist_path = Path("dist")
    exe_file = dist_path / "卓帆考试系统一键启动器.exe"
    
    if exe_file.exists():
        # 复制到当前目录
        target_path = Path("卓帆考试系统一键启动器.exe")
        shutil.copy2(exe_file, target_path)
        print(f"✓ exe文件已复制到: {target_path.absolute()}")
        
        # 删除dist目录
        shutil.rmtree(dist_path)
        print("✓ 已清理dist目录")
        
        return target_path
    else:
        print("× 未找到生成的exe文件")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("卓帆考试系统一键启动器 - EXE打包工具")
    print("=" * 50)
    
    # 检查必要文件
    if not os.path.exists("一键启动器.py"):
        print("× 错误：未找到 '一键启动器.py' 文件")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("× 无法安装PyInstaller，请手动安装：pip install pyinstaller")
            return False
    
    # 创建配置文件
    create_spec_file()
    
    # 构建exe
    if not build_exe():
        return False
    
    # 复制exe到根目录
    exe_path = copy_exe_to_root()
    if not exe_path:
        return False
    
    # 清理临时文件
    cleanup_build_files()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print(f"✓ 生成的exe文件: {exe_path.absolute()}")
    print("✓ 现在您可以直接运行exe文件，无需Python环境")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n× 打包失败！")
            input("按回车键退出...")
        else:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n× 用户取消操作")
    except Exception as e:
        print(f"\n× 发生错误: {e}")
        input("按回车键退出...")
