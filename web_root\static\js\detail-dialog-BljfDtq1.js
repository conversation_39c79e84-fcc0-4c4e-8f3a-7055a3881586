var v=(u,c,t)=>new Promise((m,_)=>{var h=s=>{try{o(t.next(s))}catch(p){_(p)}},i=s=>{try{o(t.throw(s))}catch(p){_(p)}},o=s=>s.done?m(s.value):Promise.resolve(s.value).then(h,i);o((t=t.apply(u,c)).next())});import{d as x,aO as S,l as w,r as d,o as y,g as V,h as n,e as l,b as e,f as g,ac as k,ad as C,_ as N}from"./index-B63pSD2p.js";const a=u=>(k("data-v-14aeaeef"),u=u(),C(),u),I=a(()=>l("h4",null,"整卷",-1)),B=a(()=>l("ul",null,[l("li",null,[l("div",{class:"column"},[l("span",null,"对同数："),l("p",null,"80")]),l("div",{class:"column"},[l("span",null,"错同数："),l("p",null,"80")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"对同率："),l("p",null,"79%")]),l("div",{class:"column"},[l("span",null,"错同率："),l("p",null,"78%")])])],-1)),M=a(()=>l("h4",null,"单选题",-1)),U=a(()=>l("ul",null,[l("li",null,[l("div",{class:"column"},[l("span",null,"对同数："),l("p",null,"81")]),l("div",{class:"column"},[l("span",null,"错同数："),l("p",null,"80")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"对同率："),l("p",null,"82%")]),l("div",{class:"column"},[l("span",null,"错同率："),l("p",null,"82%")])])],-1)),j=a(()=>l("h4",null,"多选题",-1)),O=a(()=>l("ul",null,[l("li",null,[l("div",{class:"column"},[l("span",null,"对同数："),l("p",null,"80")]),l("div",{class:"column"},[l("span",null,"错同数："),l("p",null,"80")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"对同率："),l("p",null,"80%")]),l("div",{class:"column"},[l("span",null,"错同率："),l("p",null,"80%")])])],-1)),T=a(()=>l("h4",null,"计算题",-1)),q=a(()=>l("ul",null,[l("li",null,[l("div",{class:"column"},[l("span",null,"解题过程相似度："),l("p",null,"79")]),l("div",{class:"column"},[l("span",null,"结论相似度："),l("p",null,"79")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"公式表达式："),l("p",null,"80")]),l("div",{class:"column"},[l("span",null,"辅助线相似度："),l("p",null,"80")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"对同数："),l("p",null,"80")]),l("div",{class:"column"},[l("span",null,"错同数："),l("p",null,"80")])]),l("li",null,[l("div",{class:"column"},[l("span",null,"对同率："),l("p",null,"79%")]),l("div",{class:"column"},[l("span",null,"错同率："),l("p",null,"79%")])])],-1)),z=a(()=>l("div",{style:{height:"10px"}},null,-1)),A={slot:"footer",class:"flex justify-between footer"},E=a(()=>l("span",null,null,-1)),F=x({__name:"detail-dialog",props:{isShowDetailDialog:{},isShowDetailDialogModifiers:{}},emits:["update:isShowDetailDialog"],setup(u){const c=S(u,"isShowDetailDialog"),t=w("result"),m=()=>v(this,null,function*(){c.value=!1}),_=()=>v(this,null,function*(){c.value=!1});return(h,i)=>{const o=d("el-card"),s=d("el-scrollbar"),p=d("el-tab-pane"),b=d("el-tabs"),f=d("el-button"),D=d("el-dialog");return y(),V(D,{width:"800px",class:"sp-similar-paper-detail-dialog",modelValue:c.value,"onUpdate:modelValue":i[1]||(i[1]=r=>c.value=r),title:"雷同对比详情","align-center":"","close-on-click-modal":!1,draggable:!0,"append-to-body":!1,"destroy-on-close":!0},{default:n(()=>[l("div",null,[e(b,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=r=>t.value=r)},{default:n(()=>[e(p,{label:"作答结果分析",name:"result"},{default:n(()=>[e(s,{height:"calc(100vh - 400px - 48px)",class:"bg dark:!bg-black eye-box"},{default:n(()=>[e(o,null,{default:n(()=>[I,B]),_:1}),e(o,null,{default:n(()=>[M,U]),_:1}),e(o,null,{default:n(()=>[j,O]),_:1}),e(o,null,{default:n(()=>[T,q]),_:1}),z]),_:1})]),_:1})]),_:1},8,["modelValue"])]),l("div",A,[E,l("div",null,[e(f,{onClick:m},{default:n(()=>[g("未雷同")]),_:1}),e(f,{type:"primary",onClick:_},{default:n(()=>[g("判定雷同")]),_:1})])])]),_:1},8,["modelValue"])}}}),J=N(F,[["__scopeId","data-v-14aeaeef"]]);export{J as default};
