#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将简易启动器打包成独立的exe文件（命令行版本，更稳定）
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("× PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("× PyInstaller安装失败")
        return False

def create_simple_spec_file():
    """创建简易版PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['简易启动器.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['psutil'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='卓帆考试系统简易启动器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version_file=None,
)
'''
    
    with open('simple_launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 简易版配置文件已创建")

def build_simple_exe():
    """构建简易版exe文件"""
    print("开始构建简易版exe文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller", 
            "--clean", 
            "simple_launcher.spec"
        ])
        print("✓ 简易版exe文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"× 简易版exe文件构建失败: {e}")
        return False

def cleanup_simple_build_files():
    """清理构建过程中的临时文件"""
    print("清理临时文件...")
    
    # 要删除的目录和文件
    cleanup_items = ['build', '__pycache__', 'simple_launcher.spec']
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
                print(f"✓ 已删除目录: {item}")
            else:
                os.remove(item)
                print(f"✓ 已删除文件: {item}")

def copy_simple_exe_to_root():
    """将生成的简易版exe文件复制到根目录"""
    dist_path = Path("dist")
    exe_file = dist_path / "卓帆考试系统简易启动器.exe"
    
    if exe_file.exists():
        # 复制到当前目录
        target_path = Path("卓帆考试系统简易启动器.exe")
        shutil.copy2(exe_file, target_path)
        print(f"✓ 简易版exe文件已复制到: {target_path.absolute()}")
        
        # 删除dist目录
        shutil.rmtree(dist_path)
        print("✓ 已清理dist目录")
        
        return target_path
    else:
        print("× 未找到生成的简易版exe文件")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("卓帆考试系统简易启动器 - EXE打包工具（命令行版本）")
    print("=" * 60)
    
    # 检查必要文件
    if not os.path.exists("简易启动器.py"):
        print("× 错误：未找到 '简易启动器.py' 文件")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("× 无法安装PyInstaller，请手动安装：pip install pyinstaller")
            return False
    
    # 创建配置文件
    create_simple_spec_file()
    
    # 构建exe
    if not build_simple_exe():
        return False
    
    # 复制exe到根目录
    exe_path = copy_simple_exe_to_root()
    if not exe_path:
        return False
    
    # 清理临时文件
    cleanup_simple_build_files()
    
    print("\n" + "=" * 60)
    print("✓ 简易版打包完成！")
    print(f"✓ 生成的exe文件: {exe_path.absolute()}")
    print("✓ 这是命令行版本，更稳定，兼容性更好")
    print("✓ 现在您可以直接运行exe文件，无需Python环境")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n× 打包失败！")
            input("按回车键退出...")
        else:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n× 用户取消操作")
    except Exception as e:
        print(f"\n× 发生错误: {e}")
        input("按回车键退出...")
