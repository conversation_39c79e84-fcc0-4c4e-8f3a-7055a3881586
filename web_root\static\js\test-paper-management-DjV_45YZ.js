import{aQ as t,aR as s}from"./index-B63pSD2p.js";const p=e=>t.request("post",s("/v1/project/get_project"),{data:e},{},!1),a=e=>t.request("post",s("/v1/project/get_subject"),{data:e},{},!1),u=e=>t.request("post",s("/v1/paper/get_paper"),{data:e}),o=e=>t.request("post",s("/v1/ques/get_ques"),{data:e}),c=e=>t.request("post",s("/v1/paper/update_paper"),{data:e}),n=e=>t.request("post",s("/v1/ques/update_ques"),{data:e}),_=e=>t.request("post",s("/v1/paper/delete_paper"),{data:e});export{p as a,a as b,o as c,c as d,_ as e,u as g,n as u};
