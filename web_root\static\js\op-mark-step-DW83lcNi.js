import{d as x,aO as k,B as V,r as f,o as s,c as t,F as d,p as S,e as l,U as D,t as _,y as n,f as m,b as h,h as y,ac as L,ad as q,_ as N}from"./index-B63pSD2p.js";const w=a=>(L("data-v-ea1b73ea"),a=a(),q(),a),B={class:"readOnly-box"},F={class:"step-group"},I={key:0,class:"logic-text"},U={class:"step-item"},O={class:"step-content"},$={class:"order-id"},z={class:"description"},E={key:0,class:"step-score"},H=w(()=>l("span",{class:"step-score-text"},"分值",-1)),Q={class:"score-value"},T={key:0,class:"input-step-score"},j=x({name:"op-mark-step",__name:"op-mark-step",props:{quesDetail:{},quesDetailModifiers:{},opStepList:{},opStepListModifiers:{},isMark:{},isMarkModifiers:{}},emits:["update:quesDetail","update:opStepList","update:isMark"],setup(a){const c=k(a,"quesDetail"),b=k(a,"opStepList"),v=k(a,"isMark"),M=r=>{let e="";return r.logic!==1?v.value?e="step-place-mark title-back-color":e="step-place-read title-back-color":v.value?e="step-place-mark":e="step-place-read",e},g=(r,e)=>{c.value.step_score_list[r]=e};return V(()=>c.value.step_score_list,r=>{r&&(c.value.opMarkScore=parseFloat(c.value.step_score_list.reduce((e,u)=>e+u,0).toFixed(1)))},{deep:!0}),(r,e)=>{const u=f("el-button"),C=f("el-input-number");return s(),t("div",B,[(s(!0),t(d,null,S(b.value,(o,p)=>(s(),t("div",{key:p},[l("div",F,[l("div",{class:D(M(o))},[o.logic!==1?(s(),t("div",I,_(o.logic_text),1)):n("",!0),(s(!0),t(d,null,S(o.op_step_group,i=>(s(),t("div",U,[l("div",O,[l("div",$,_(i.order_id),1),l("div",z,_(i.description),1)])]))),256))],2),o.group_score!=null?(s(),t("div",E,[H,l("span",Q,_(o.group_score),1),m("分 ")])):n("",!0),v.value?(s(),t(d,{key:1},[c.value.isHistory?n("",!0):(s(),t(d,{key:0},[o.group_score!=null?(s(),t("div",T,[h(u,{type:"warning",onClick:i=>g(p,0)},{default:y(()=>[m("零分")]),_:2},1032,["onClick"]),h(C,{modelValue:c.value.step_score_list[p],"onUpdate:modelValue":i=>c.value.step_score_list[p]=i,precision:2,step:1,min:0,max:o.group_score,"controls-position":"right",class:"input-score"},null,8,["modelValue","onUpdate:modelValue","max"]),h(u,{type:"success",onClick:i=>g(p,o.group_score)},{default:y(()=>[m("满分")]),_:2},1032,["onClick"])])):n("",!0)],64))],64)):n("",!0)])]))),128))])}}}),G=N(j,[["__scopeId","data-v-ea1b73ea"]]);export{G as default};
