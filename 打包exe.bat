@echo off
chcp 65001 >nul
title 卓帆考试系统一键启动器 - EXE打包工具

echo ================================================
echo 卓帆考试系统一键启动器 - EXE打包工具
echo ================================================
echo.
echo 此工具将把Python启动器打包成独立的exe文件
echo 打包完成后，您就可以在没有Python环境的电脑上使用了
echo.

:: 检查Python环境
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo × 错误：未找到Python环境！
    echo   请先安装Python 3.6或更高版本
    echo   下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)
echo ✓ Python环境检查通过

:: 检查源文件
echo.
echo [2/4] 检查源文件...
if not exist "一键启动器.py" (
    echo × 错误：未找到 '一键启动器.py' 文件
    echo   请确保此文件在当前目录中
    echo.
    pause
    exit /b 1
)
echo ✓ 源文件检查通过

:: 安装依赖
echo.
echo [3/4] 检查和安装依赖包...
python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装psutil...
    pip install psutil
)

echo.
echo [4/4] 开始打包exe文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 运行打包脚本
python build_exe.py

echo.
echo 打包完成！
echo.
echo 使用说明：
echo 1. 生成的exe文件名为：卓帆考试系统一键启动器.exe
echo 2. 将此exe文件放在包含以下程序的目录中：
echo    - Redis-x64-3.0.504\redis-server.exe
echo    - 卓帆电子化考试阅卷管理系统V1.0.exe  
echo    - 定时任务V1.0.exe
echo 3. 双击exe文件即可使用，无需Python环境
echo.
pause
