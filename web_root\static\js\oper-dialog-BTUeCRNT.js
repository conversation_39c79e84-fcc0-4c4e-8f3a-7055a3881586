var S=(j,R,k)=>new Promise((C,c)=>{var f=d=>{try{v(k.next(d))}catch(b){c(b)}},F=d=>{try{v(k.throw(d))}catch(b){c(b)}},v=d=>d.done?C(d.value):Promise.resolve(d.value).then(f,F);v((k=k.apply(j,R)).next())});import{S as Ve}from"./index-BCxZUZMh.js";import{a as ke,b as qe,c as we,d as G,g as je}from"./paper-sample-DNcQEVKm.js";import{t as Ce}from"./convertNumber-CmbNKqvY.js";import{d as Se,l as i,B as De,r as _,o as p,g as D,h as s,e as r,b as o,f as m,c as w,F as T,p as B,u as J,t as M,T as A,ac as Ie,ad as Fe,_ as Pe}from"./index-B63pSD2p.js";const N=j=>(Ie("data-v-608588cf"),j=j(),Fe(),j),Ue={style:{"border-right":"1px solid #dcdcdc",position:"relative",width:"340px"}},$e={style:{width:"100%"}},He=N(()=>r("div",{class:"title"},"验收条件",-1)),Te={class:"score-div"},Be={key:0},Me={key:1},Ne=N(()=>r("div",{class:"title"},"抽取数量",-1)),Re=N(()=>r("div",null," 份  可抽量： 164",-1)),Ee={class:"check-result-div"},Le=N(()=>r("div",{class:"title"},"验收样本",-1)),ze={class:"conditions"},Oe={class:"conditionDetail"},Ge={style:{"margin-left":"12px"}},Je={class:"oper-table-btn"},Ke={class:"footer-btn"},Qe=Se({__name:"oper-dialog",setup(j,{expose:R}){const k=i(""),C=i(!1),c=i([{operator:"0",minVal:"",maxVal:""}]),f=i(),F=je(),v=i({}),d=i(),b=i(!1),y=i(0),ee=i({count:[{required:!0,message:"请输入抽取数量",trigger:["blur","change"]}]}),te=i([{label:"所属资格",required:!1,placeholder:"请选择所属资格",source:ke,extraParam:{page_size:-1},type:"select",prop:"project_id",mappingField:{label:"project_name",value:"project_id"}},{label:"所属科目",required:!0,placeholder:"请选择所属科目",isMultiple:!1,source:qe,upProp:"project_id",type:"select",prop:"subject_id",mappingField:{label:"subject_name",value:"subject_id"}},{label:"场次",placeholder:"请选择场次",isMultiple:!1,source:we,type:"select",prop:"district"},{label:"考区",placeholder:"请选择考区",isMultiple:!1,upProp:["project_id","subject_id"],source:G,extraParam:{place_type:1},type:"cascader",prop:"exam_area_code",mappingField:{label:"name",value:"code"},cascaderProps:{checkStrictly:!0,emitPath:!1,value:"code",label:"name"}},{label:"考点",placeholder:"请选择考点",isMultiple:!1,source:G,extraParam:{place_type:2},upProp:["project_id","subject_id","exam_area_code"],type:"select",prop:"exam_point_code",mappingField:{label:"exam_point_name",value:"exam_point_code"}},{label:"考场",placeholder:"请选择考场",isMultiple:!1,source:G,extraParam:{place_type:3},upProp:["project_id","subject_id","exam_area_code","exam_point_code"],type:"select",prop:"exam_room_code",mappingField:{label:"exam_room_name",value:"exam_room_code"}}]),ae=e=>S(this,null,function*(){C.value=!0,k.value=e,e=="edit"&&(V.value.push({searchHtml:"",checked:[],conditions:[],tableData:[{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"1081",stu_secret_num:"19434129071",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"7068",stu_secret_num:"19434129072",ques_type_score:"10",stu_score:"7",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"5823",stu_secret_num:"19434129073",ques_type_score:"10",stu_score:"8",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"4745",stu_secret_num:"19434129074",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003578",ques_type_code:"案例分析题",ques_code:"9913",stu_secret_num:"19434129075",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003578",ques_type_code:"案例分析题",ques_code:"6512",stu_secret_num:"19434129076",ques_type_score:"10",stu_score:"5",check_result:"不通过"}]}),setTimeout(()=>S(this,null,function*(){yield P(x),y.value=x,x=x+1}),0))}),E=e=>e?(e=e.replace(/\D/g,""),e=e.replace(/^0+(\d)/,"$1"),e):"",le=()=>{c.value.push({operator:"0",minVal:"",maxVal:""})},oe=e=>{if(c.value.length==1){A.warning("至少保留一条数据");return}c.value.splice(e,1)},g=i([]),K=i(),Q=i(200);let W=null;const P=e=>{W=new ResizeObserver(t=>{t.forEach(n=>{g.value[e]&&(Q.value=K.value.clientHeight-g.value[e].clientHeight-85+"px")})}),g.value[e]&&W.observe(g.value[e])},X=(e,t)=>{for(let n=0;n<e.length;n++){if(e[n].code===t)return e[n];if(e[n].children&&e[n].children.length>0){const q=X(e[n].children,t);if(q)return q}}return null},se=()=>{var q;let e="",t=(q=f.value)==null?void 0:q.formValue,n=f==null?void 0:f.value.getSources();if(t.project_id&&(e+="所属资格："+n.project_id.filter(u=>u.project_id==t.project_id)[0].project_name+"；"),t.subject_id&&(e+="所属科目："+n.subject_id.filter(u=>u.subject_id==t.subject_id)[0].project_name+"；"),t.district&&(e+="场次："+n.district.filter(u=>u.value==t.district)[0].label+"；"),t.exam_area_code&&(e+="考区："+X(n.exam_area_code,t.exam_area_code).name+"；"),t.exam_point_code&&(e+="考点："+n.exam_point_code.filter(u=>u.value==t.exam_point_code)[0].label+"；"),t.exam_room_code&&(e+="考场："+n.exam_room_code.filter(u=>u.value==t.exam_room_code)[0].label+"；"),c.value.length>0){let u="";for(let h in c.value){let U=F.filter(z=>z.value==c.value[h].operator)[0];U.value==7?c.value[h].minVal&&c.value[h].maxVal&&(u+=c.value[h].minVal+"-"+c.value[h].maxVal+"，"):c.value[h].minVal&&(u+=U.text+c.value[h].minVal+"，")}u&&(e+="总得分："+u.replace(/，$/,"")+"；")}return v.value.count&&(e+="每题抽样数："+v.value.count+"；"),e},V=i([]);let x=0;const ne=()=>S(this,null,function*(){let e=yield f==null?void 0:f.value.IsFormValid();d.value&&(yield d.value.validate((t,n)=>S(this,null,function*(){t&&e&&(b.value=!0,V.value.push({searchHtml:se(),checked:[],conditions:[],tableData:[{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"1081",stu_secret_num:"19434129071",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"7068",stu_secret_num:"19434129072",ques_type_score:"10",stu_score:"7",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"5823",stu_secret_num:"19434129073",ques_type_score:"10",stu_score:"8",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003577",ques_type_code:"案例分析题",ques_code:"4745",stu_secret_num:"19434129074",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003578",ques_type_code:"案例分析题",ques_code:"9913",stu_secret_num:"19434129075",ques_type_score:"10",stu_score:"6",check_result:"通过"},{exam_session:"1",exam_area_name:"广东省",exam_point_name:"北京师范大学（珠海校区）",exam_room_code:"40003578",ques_type_code:"案例分析题",ques_code:"6512",stu_secret_num:"19434129076",ques_type_score:"10",stu_score:"5",check_result:"不通过"}]}),b.value=!1,setTimeout(()=>S(this,null,function*(){yield P(x),y.value=x,x=x+1}),0))})))}),_e=i([{prop:"exam_session",label:"场次",width:60},{prop:"exam_area_name",label:"考区",width:120},{prop:"exam_point_name",label:"考点"},{prop:"exam_room_code",label:"考场",width:130},{prop:"ques_type_code",label:"题型"},{prop:"ques_code",label:"试题编号",width:100},{prop:"stu_secret_num",label:"考生密号",width:130},{prop:"ques_type_score",label:"题分数",width:90},{prop:"stu_score",label:"考生得分",width:90}]),ce=e=>{P(y.value)},re=e=>{V.value.splice(e,1),g.value.splice(e,1),y.value=0,P(y.value)},Y=(e,t,n)=>{e=="single"&&t.tableData.splice(n,1)},ue=(e,t)=>{e.checked=t};De(()=>V.value,e=>{e.length==0&&(y.value=0,g.value=[],x=0)},{immediate:!0,deep:!0});const ie=()=>{if(V.value.length==0)return A.warning("请筛选验收样本!"),!1;L()},L=()=>{C.value=!1,de(),V.value=[],x=0,g.value=[]},de=()=>{c.value=[{operator:"0",minVal:"",maxVal:""}],d.value&&d.value.resetFields(),v.value={}};return R({openDialog:ae}),(e,t)=>{const n=_("DynamicFormComponent"),q=_("el-option"),u=_("el-select"),h=_("el-input"),U=_("Delete"),z=_("Plus"),pe=_("el-icon"),$=_("el-text"),Z=_("el-form-item"),me=_("el-input-number"),he=_("el-form"),fe=_("el-scrollbar"),O=_("el-button"),ve=_("el-tag"),H=_("el-table-column"),xe=_("el-table"),be=_("el-tab-pane"),ye=_("el-tabs"),ge=_("el-dialog");return p(),D(ge,{modelValue:C.value,"onUpdate:modelValue":t[2]||(t[2]=l=>C.value=l),title:k.value=="add"?"创建验收":"编辑验收",width:"70%","align-center":"","close-on-click-modal":!1,"before-close":L,draggable:"","destroy-on-close":""},{footer:s(()=>[r("div",Ke,[o(O,{onClick:L},{default:s(()=>[m("取消")]),_:1}),o(O,{type:"primary",onClick:ie},{default:s(()=>[m("保存")]),_:1})])]),default:s(()=>[r("div",{ref_key:"mainRef",ref:K,class:"check-oper-form",style:{height:"60vh"}},[r("div",Ue,[o(fe,{always:"","max-height":"56vh",style:{"padding-right":"12px",width:"100%"}},{default:s(()=>[r("div",$e,[He,o(n,{list:te.value,ref_key:"formFilterRef",ref:f},null,8,["list"]),o(Z,{label:"总得分",style:{"margin-right":"0px"}},{default:s(()=>[(p(!0),w(T,null,B(c.value,(l,I)=>(p(),w("div",Te,[r("div",null,[o(u,{modelValue:l.operator,"onUpdate:modelValue":a=>l.operator=a,style:{width:"100px","margin-right":"6px"}},{default:s(()=>[(p(!0),w(T,null,B(J(F),a=>(p(),D(q,{key:a.value,label:a.text,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),l.operator==7?(p(),w("span",Be,[o(h,{style:{width:"49px"},modelValue:l.minVal,"onUpdate:modelValue":a=>l.minVal=a,onInput:a=>l.minVal=E(l.minVal)},null,8,["modelValue","onUpdate:modelValue","onInput"]),m(" - "),o(h,{style:{width:"49px"},modelValue:l.maxVal,"onUpdate:modelValue":a=>l.maxVal=a,onInput:a=>l.maxVal=E(l.maxVal)},null,8,["modelValue","onUpdate:modelValue","onInput"])])):(p(),w("span",Me,[o(h,{style:{width:"110px"},modelValue:l.minVal,"onUpdate:modelValue":a=>l.minVal=a,onInput:a=>l.minVal=E(l.minVal)},null,8,["modelValue","onUpdate:modelValue","onInput"])]))]),o(U,{style:{width:"1.3em",height:"1.3em",cursor:"pointer"},onClick:a=>oe(I)},null,8,["onClick"])]))),256)),o($,{type:"primary",style:{cursor:"pointer"},onClick:le},{default:s(()=>[o(pe,null,{default:s(()=>[o(z)]),_:1}),m("添加")]),_:1})]),_:1})]),r("div",null,[Ne,o(he,{model:v.value,rules:ee.value,ref_key:"formRef",ref:d,inline:""},{default:s(()=>[o(Z,{label:"每题抽",prop:"count"},{default:s(()=>[o(me,{modelValue:v.value.count,"onUpdate:modelValue":t[0]||(t[0]=l=>v.value.count=l),controls:!1,min:1,style:{width:"80px"},placeholder:"数量","step-strictly":""},null,8,["modelValue"]),Re]),_:1})]),_:1},8,["model","rules"])])]),_:1}),o(O,{type:"primary",style:{width:"90%",position:"absolute",botttom:"0px",bottom:"0px"},onClick:ne,disabled:b.value,loading:b.value},{default:s(()=>[m("验收抽样")]),_:1},8,["disabled","loading"])]),r("div",Ee,[Le,V.value.length==0?(p(),D(J(Ve),{key:0,title:"验收样本",stepList:["选择左侧验收条件","点击【验收抽样】按钮，抽取验收样本数据","保存验收样本后，可对样本进行逐个验收"]})):(p(),D(ye,{key:1,modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=l=>y.value=l),class:"demo-tabs",editable:"",onTabChange:ce,onTabRemove:re},{default:s(()=>[(p(!0),w(T,null,B(V.value,(l,I)=>(p(),D(be,{label:"第"+J(Ce)(I+1)+"次",name:I},{default:s(()=>[r("div",{ref_for:!0,ref:a=>g.value[I]=a},[r("div",ze,[r("div",Oe,[o(ve,null,{default:s(()=>[m("抽取条件")]),_:1}),r("div",Ge,M(l.searchHtml),1)]),r("div",null,"共抽："+M(l.tableData.length)+"条",1)]),r("div",Je,[m(" 已选中"),o($,{type:"primary"},{default:s(()=>[m(M(l.checked.length),1)]),_:2},1024),m("条数据  "),o($,{type:"primary",style:{cursor:"pointer"},onClick:a=>Y("all",l)},{default:s(()=>[m("移除")]),_:2},1032,["onClick"])])],512),o(xe,{border:"",stripe:"",height:Q.value,"scrollbar-always-on":"","show-overflow-tooltip":"",data:l.tableData,onSelectionChange:a=>{ue(l,a)}},{default:s(()=>[o(H,{type:"selection"}),o(H,{label:"序号",align:"center",width:"60"},{default:s(a=>[m(M(a.$index+1),1)]),_:1}),(p(!0),w(T,null,B(_e.value,a=>(p(),D(H,{label:a.label,prop:a.prop,key:a.prop,width:a.width,fixed:a.fixed,align:"center"},null,8,["label","prop","width","fixed"]))),128)),o(H,{label:"操作",width:"80px",align:"center",fixed:"right"},{default:s(a=>[o($,{type:"primary",style:{cursor:"pointer"},onClick:We=>Y("single",l,a.$index)},{default:s(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["height","data","onSelectionChange"])]),_:2},1032,["label","name"]))),256))]),_:1},8,["modelValue"]))])],512)]),_:1},8,["modelValue","title"])}}}),tt=Pe(Qe,[["__scopeId","data-v-608588cf"]]);export{tt as default};
