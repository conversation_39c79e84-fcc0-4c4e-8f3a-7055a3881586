import H from"./add-modules-DIbCS3qZ.js";import{_ as J}from"./add-button.vue_vue_type_script_setup_true_lang-Dq82ICT4.js";import{d as L,l as _,an as W,n as X,B as Y,il as Z,r as d,o as a,c as D,e as s,b as t,h as c,U as ee,g as r,y as f,t as ne,u as y,N as V,R as E,F as oe,f as k,aV as T,im as le,T as h,io as te,ac as ce,ad as se,_ as ie}from"./index-B63pSD2p.js";import"./validate-Dc6ka3px.js";const x=g=>(ce("data-v-69c70af1"),g=g(),se(),g),ae={class:"zf-first-box"},ue={class:"zf-second-box"},de=x(()=>s("div",{class:"zf-tit-box"},[s("i"),s("span",null,"功能权限管理")],-1)),re={class:"tree-box"},_e={class:"node-box"},fe=["onClick"],pe={key:0},me={class:"add-menu-box"},ke=x(()=>s("br",null,null,-1)),ge=x(()=>s("br",null,null,-1)),ye={class:"add-menu-box"},he=x(()=>s("br",null,null,-1)),xe=L({name:"authority-management",__name:"index",setup(g){const I=_(null),v=_(null),F=_(null),C=_(""),S={label:"module_name",children:"children"},N=_([]),b=_([]),B=_();W(()=>{}),X(()=>{p()}),Y(C,n=>{I.value.filter(n)});const p=n=>{b.value=[],n&&b.value.push(n),Z({}).then(o=>{o.code&&o.code===200&&(o.data.data.forEach(l=>{l.children.length===0?l.children=l.func_point:l.children.forEach(m=>{m.func_point.length>0&&(m.children=m.func_point)})}),N.value=o.data.data)})},z=(n,o)=>n?o.module_name.includes(n):!0,P=n=>{B.value=n.module_flag},M=(n,o)=>{v.value.openDialog(n,o)},O=n=>{v.value.openDialog("04",n)},Q=n=>{T.confirm("确定删除该菜单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let o={flag:n.flag,module_id:n.module_id};le(o).then(l=>{l.code&&l.code===200?(h.success(l.msg),p()):h.warning(l.msg)})}).catch(()=>{})},w=(n,o)=>{F.value.openDialog(n,o)},U=n=>{T.confirm("确定删除该功能吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{let o={func_point_id:n.module_id};te(o).then(l=>{l.code&&l.code===200?(h.success(l.msg),p()):h.warning(l.msg)})}).catch(()=>{})};return(n,o)=>{const l=d("el-input"),m=d("IconifyIconOffline"),A=d("DocumentRemove"),K=d("el-icon"),i=d("el-button"),R=d("el-popover"),j=d("el-tree"),q=d("el-scrollbar"),G=d("el-card");return a(),D("div",ae,[s("div",ue,[t(G,{class:"auth-box"},{default:c(()=>[de,s("div",re,[t(l,{placeholder:"搜索",modelValue:C.value,"onUpdate:modelValue":o[0]||(o[0]=$=>C.value=$),style:{width:"260px","margin-bottom":"5px"},clearable:""},null,8,["modelValue"]),t(q,{height:"720px",always:""},{default:c(()=>[t(j,{ref_key:"treeRef",ref:I,data:N.value,props:S,"node-key":"module_flag","filter-node-method":z,"default-expanded-keys":b.value},{default:c(({node:$,data:e})=>[s("div",_e,[s("div",{class:ee(["node-text",[B.value===e.module_flag?"check_node":""]]),onClick:u=>P(e)},[e.flag===1?(a(),r(m,{key:0,icon:e.icon,style:{"margin-right":"3px"}},null,8,["icon"])):e.flag===2?(a(),r(K,{key:1,style:{"margin-right":"3px"}},{default:c(()=>[t(A)]),_:1})):f("",!0),s("span",null,ne($.label),1)],10,fe),B.value===e.module_flag?(a(),D("div",pe,[e.flag===1||e.flag===2?(a(),r(i,{key:0,class:"icon-lick-box",link:"",title:"编辑",icon:"Edit",onClick:u=>O(e)},null,8,["onClick"])):e.flag===3?(a(),r(i,{key:1,class:"icon-lick-box",link:"",title:"编辑",icon:"Edit",onClick:u=>w("02",e)},null,8,["onClick"])):f("",!0),y(V)().getItem(y(E)).roles.includes("1")||y(V)().getItem(y(E)).roles.includes("2")?(a(),D(oe,{key:2},[e.flag===1?(a(),r(R,{key:0,trigger:"click"},{reference:c(()=>[t(i,{class:"icon-lick-box",link:"",title:"增加",icon:"Plus"})]),default:c(()=>[s("div",me,[t(i,{style:{"margin-bottom":"5px"},type:"primary",onClick:u=>M("01",e)},{default:c(()=>[k("添加同级菜单 ")]),_:2},1032,["onClick"]),ke,t(i,{type:"primary",style:{"margin-bottom":"5px"},onClick:u=>M("02",e)},{default:c(()=>[k("添加子级菜单 ")]),_:2},1032,["onClick"]),ge,t(i,{type:"primary",style:{width:"116px"},onClick:u=>w("01",e)},{default:c(()=>[k("添加功能点 ")]),_:2},1032,["onClick"])])]),_:2},1024)):e.flag===2?(a(),r(R,{key:1,trigger:"click"},{reference:c(()=>[t(i,{class:"icon-lick-box",link:"",title:"增加",icon:"Plus"})]),default:c(()=>[s("div",ye,[t(i,{style:{"margin-bottom":"5px"},type:"primary",onClick:u=>M("03",e)},{default:c(()=>[k("添加同级菜单 ")]),_:2},1032,["onClick"]),he,t(i,{type:"primary",style:{width:"116px"},onClick:u=>w("01",e)},{default:c(()=>[k("添加功能点 ")]),_:2},1032,["onClick"])])]),_:2},1024)):f("",!0),e.flag===1||e.flag===2?(a(),r(i,{key:2,class:"icon-lick-box",link:"",title:"删除",icon:"Minus",onClick:u=>Q(e)},null,8,["onClick"])):e.flag===3?(a(),r(i,{key:3,class:"icon-lick-box",link:"",title:"删除",icon:"Minus",onClick:u=>U(e)},null,8,["onClick"])):f("",!0)],64)):f("",!0)])):f("",!0)])]),_:1},8,["data","default-expanded-keys"])]),_:1})])]),_:1})]),t(H,{ref_key:"addModulesRef",ref:v,onQueryData:p},null,512),t(J,{ref_key:"addButtonRef",ref:F,onQueryData:p},null,512)])}}}),Me=ie(xe,[["__scopeId","data-v-69c70af1"]]);export{Me as default};
