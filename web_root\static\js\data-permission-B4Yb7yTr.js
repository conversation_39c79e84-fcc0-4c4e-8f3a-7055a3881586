import{j as $,k as G}from"./user-management-B1vGxPiG.js";import{d as H,l as c,B as J,r as d,o as F,g as K,h as a,e as u,b as s,f as y,U as O,t as U,c as Q,F as W,p as X,T as C,_ as Y}from"./index-B63pSD2p.js";const Z={class:"zf-dialog-first-box"},ee={class:"zf-dialog-second-box"},le={class:"tree-show-box"},te=["title"],ae={class:"footer-btn"},oe=H({__name:"data-permission",emits:["queryData"],setup(se,{expose:B,emit:ne}){const E=c("数据权限配置"),m=c(!1),r=c(null),v=c(""),T={label:"name",children:"children"},_=c([]),g=c([]),z=[{label:"一级",value:1},{label:"二级",value:2},{label:"三级",value:3},{label:"四级",value:4}],h=c(1),b=c({});J(v,l=>{r.value.filter(l)});const L=(l,e)=>{m.value=!0,b.value=e,R()},x=()=>{var e;m.value=!1,g.value=[],v.value="",r.value.setCheckedNodes([]);const l=(e=r.value.store)==null?void 0:e.nodesMap;if(l)for(let t in l)l[t].expanded=!1;h.value=1},R=()=>{let l={user_id:b.value.user_id};$(l).then(e=>{var t,o;e.code&&e.code===200?((o=(t=e.data)==null?void 0:t.data)==null?void 0:o.length)>0?(_.value=[{flag:"checkAll",name:"全选",children:e.data.data}],D(_.value[0].children,g.value)):_.value=[]:C.warning(e.msg)})},D=(l,e)=>{(l==null?void 0:l.length)>0&&(l==null||l.forEach(t=>{var o;t.show&&e.push(t.flag),((o=t.children)==null?void 0:o.length)>0&&D(t.children,e)}))},j=(l,e)=>{var t;return l?(t=e.name)==null?void 0:t.includes(l):!0},A=l=>{var e,t;if(((e=_.value)==null?void 0:e.length)>0){let o=function(i){i.expanded=i.level<=l,i.childNodes.length!==0&&i.childNodes.forEach(k=>{o(k)})};const p=(t=r.value)==null?void 0:t.root;p&&o(p)}},I=()=>{M()},M=()=>{let l={user_id:b.value.user_id,path_list:r.value.getCheckedNodes().filter(e=>!e.children||e.children.length==0).map(e=>e.path)};G(l).then(e=>{e.code&&e.code===200?(C.success(e.msg),x()):C.warning(e.msg)})};return B({openDialog:L}),(l,e)=>{const t=d("el-input"),o=d("el-form-item"),p=d("el-form"),i=d("el-tree"),k=d("el-scrollbar"),P=d("el-radio"),q=d("el-radio-group"),w=d("el-button"),S=d("el-dialog");return F(),K(S,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=n=>m.value=n),title:E.value,width:"440px","close-on-click-modal":!1,"before-close":x,"align-center":"",draggable:""},{footer:a(()=>[u("div",ae,[s(w,{onClick:x},{default:a(()=>[y("取消")]),_:1}),s(w,{type:"primary",onClick:I},{default:a(()=>[y("确定")]),_:1})])]),default:a(()=>[u("div",Z,[u("div",ee,[u("div",le,[s(p,null,{default:a(()=>[s(o,{label:"搜索内容"},{default:a(()=>[s(t,{placeholder:"请输入内容搜索",modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=n=>v.value=n),style:{"margin-bottom":"5px"},clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),s(k,{class:"tree-check-box",always:""},{default:a(()=>[s(i,{ref_key:"treeRef",ref:r,data:_.value,props:T,"default-checked-keys":g.value,"default-expanded-keys":["checkAll"],"show-checkbox":"","node-key":"flag","filter-node-method":j},{default:a(({node:n,data:f})=>{var V,N;return[u("div",{class:O([f.level===3?"version-tree-box":"project-text"])},[u("div",{class:"version-tree-text",title:(V=f.desc)!=null&&V.text?(N=f.desc)==null?void 0:N.text:f.name},U(f.name),9,te)],2)]}),_:1},8,["data","default-checked-keys"])]),_:1})]),u("div",null,[s(p,null,{default:a(()=>[s(o,{label:"展开层级"},{default:a(()=>[s(q,{modelValue:h.value,"onUpdate:modelValue":e[1]||(e[1]=n=>h.value=n)},{default:a(()=>[(F(),Q(W,null,X(z,(n,f)=>s(P,{value:n.value,onClick:V=>A(n.value)},{default:a(()=>[y(U(n.label),1)]),_:2},1032,["value","onClick"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})])])])]),_:1},8,["modelValue","title"])}}}),ue=Y(oe,[["__scopeId","data-v-bb3fed81"]]);export{ue as default};
