import{P as fe}from"./index-Astsj93h.js";import{aQ as T,aR as O,d as be,az as he,i as ge,l as _,P as ve,n as xe,C as Ce,a2 as ye,ao as we,T as f,r as v,o as C,c as Be,e as p,b as i,h as n,f as r,t as z,u as y,aN as q,g as k,y as H,aV as w,_ as qe}from"./index-B63pSD2p.js";import{g as ke}from"./test-paper-management-DjV_45YZ.js";import{p as Se,a as Y,g as De}from"./common-methods-BWkba4Bo.js";import{q as Te,g as Oe}from"./rules-form-CST-rV3v.js";import{d as je}from"./downloadRequest-CdE2PBjt.js";import{h as Fe,a as Ne}from"./handleCache-Dzg6LWBc.js";import{c as Ve,a as Re}from"./calculateTableHeight-BjE6OFD1.js";import"./scoring-rules-BR2vQ7G3.js";import"./pageCache-DQQfxtZI.js";const We=(m,P=!0)=>T.request("post",O("/v1/set_std/get_ques_set_std_list"),{data:m},{},P),ze=m=>T.request("post",O("/v1/set_std/start_set_std"),{data:m}),Pe=m=>T.request("post",O("/v1/set_std/pause_set_std"),{data:m}),Le=m=>T.request("post",O("/v1/set_std/cancel_set_std"),{data:m}),Ee=m=>T.request("post",O("/v1/set_std/continue_set_std"),{data:m}),Ae={class:"zf-first-box"},He={class:"zf-second-box"},$e={class:"flex justify-between mb-[10px]"},Ie={class:"calibrate-num-box"},Je=be({name:"calibrate",__name:"index",setup(m){const P=he(),Z=ge(),$=_(null),u=_(null),L=_(null),I=_({}),S=_([]),j=_([]),J=ve({column:3,labelWidth:"68px",itemWidth:"240px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择科目",optionData:()=>Se.value},{label:"科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择科目",optionData:()=>S.value},{label:"试卷名称",prop:"paper_id",type:"select",defaultValue:"",placeholder:"请选择试卷",clearable:!0,optionData:()=>j.value},{label:"试题类型",prop:"ques_type_code_list",type:"select",defaultValue:"",placeholder:"请选择题型",multiple:!0,clearable:!0,optionData:()=>Te},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"试题序号",prop:"ques_order",type:"input",defaultValue:"",placeholder:"请输入试题序号",clearable:!0},{label:"小题序号",prop:"small_ques_num",type:"input",defaultValue:"",placeholder:"请输入小题序号",clearable:!0},{label:"定标状态",prop:"set_std_state_list",type:"select",defaultValue:[],placeholder:"请选择定标状态",clearable:!0,multiple:!0,optionData:()=>[{label:"未定标",value:1},{label:"定标中",value:2},{label:"已定标",value:3},{label:"已暂停",value:4},{label:"已取消",value:5}]}]}),b=_({field:[{prop:"subject_name",label:"科目",minWidth:"120px"},{prop:"paper_name",label:"试卷",minWidth:"120px"},{prop:"ques_type_name",label:"题型",minWidth:"120px"},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"ques_order",label:"试题序号",minWidth:"86px"},{prop:"small_ques_num",label:"小题序号",minWidth:"86px"},{prop:"set_std_state_str",label:"定标状态",minWidth:"86px"},{prop:"progress_count",label:"定标情况",minWidth:"86px"},{prop:"error_num",label:"定标异常",minWidth:"106px",type:"template",fixed:!1,sortable:!0,clickBtn:e=>{X(e,"error")}},{prop:"progress",label:"定标进度",minWidth:"160px",type:"progress",showOverflowTooltip:!0},{prop:"set_std_start_time",label:"开始定标时间",minWidth:"160px",sortable:!0},{prop:"set_std_end_time",label:"结束定标时间",minWidth:"160px",sortable:!0},{prop:"opera",label:"操作",type:"slot",fixed:"right",minWidth:"200px",showOverflowTooltip:!0}],styleOptions:{isShowSort:!1,isShowSelection:!0,minHeight:window.innerHeight-390+"px"},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}}),F=_([]),B=_([]);let N=null;const E=_("");let U=null;const d=_({total:0,alreadyCalibrate:0,unCalibrate:0,errorCalibrate:0});xe(()=>{Ve(U,$.value,b.value),E.value=JSON.parse(JSON.stringify(P.path)),Fe(E.value,J,u.value,b.value),Ce(()=>{const{project_id:e,subject_id:t}=u.value.getAllCardData();e&&Y(e).then(a=>{S.value=a||[]}),t&&M()}),De(),Oe(),h()}),ye(()=>{Ne(u.value,b.value,E.value)}),we(()=>{Re(U),V()});const ee=(e,t)=>{e.prop==="project_id"?(S.value=[],j.value=[],u.value.getCardData("subject_id")&&u.value.setCardData("subject_id",null),u.value.getCardData("paper_id")&&u.value.setCardData("paper_id",null),t&&Y(t).then(a=>{S.value=a||[]})):e.prop==="subject_id"&&(j.value=[],u.value.getCardData("paper_id")&&u.value.setCardData("paper_id",null),t&&M())},M=()=>{const{project_id:e,subject_id:t}=u.value.getAllCardData();ke({project_id:e,subject_id:t,page_size:-1}).then(o=>{o.code&&o.code===200&&(o.data.data.forEach(l=>{l.label=l.paper_name,l.value=l.paper_id}),j.value=o.data.data)})},h=e=>{var l;let t=JSON.parse(JSON.stringify(u.value.getAllCardData()));(l=t.ques_type_code_list)!=null&&l.length||delete t.ques_type_code_list,t.set_std_state_list.length||delete t.set_std_state_list,t.small_ques_num==""&&delete t.small_ques_num;let{currentPage:a,pageSize:o}=b.value.pageOptions;t.current_page=a,t.page_size=o,We(t,!e).then(c=>{var x;if(c.code&&c.code===200){c.data.is_refresh===0?V():c.data.is_refresh===1&&re(),e==="auto"?F.value.forEach((g,R)=>{["set_std_state","set_std_state_str","progress_count","error_num","progress","set_std_end_time"].forEach(s=>{g[s]=c.data.data[R][s]})}):F.value=c.data.data;for(let g in d.value)d.value[g]=0;(x=F.value)==null||x.forEach((g,R)=>{const[W,s]=g.progress_count.split("/");d.value.total+=Number(s),d.value.alreadyCalibrate+=Number(W),d.value.unCalibrate=d.value.total-d.value.alreadyCalibrate,d.value.errorCalibrate+=Number(g.error_num)}),b.value.pageOptions.total=c.data.total}else f.warning(c.msg)})},te=e=>{w.confirm("确定开始定标？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{A(e)}).catch(()=>{})},A=e=>{ze({ques_id_list:e}).then(a=>{a.code&&a.code===200?(f.success(a.msg),h()):f.warning(a.msg)})},ae=e=>{w.confirm("确定暂停定标？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{Q(e)}).catch(()=>{})},Q=e=>{Pe({ques_id_list:e}).then(a=>{a.code&&a.code===200?(f.success(a.msg),V(),h()):f.warning(a.msg)})},le=e=>{if(!B.value.length){f.warning("至少选择一条数据！");return}let t="";switch(e){case"start":t="开始";break;case"pause":t="暂停";break;case"cancel":t="取消";break;case"continue":t="继续";break}w.confirm(`确定批量${t}定标？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{const a=B.value.map(o=>o.ques_id);e==="start"?A(a):e==="pause"?Q(a):e==="cancel"?K(a):e==="continue"&&G(a)}).catch(()=>{})},ne=e=>{w.confirm("确定继续定标？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{G(e)}).catch(()=>{})},G=e=>{Ee({ques_id_list:e,is_auto_trigger:!1}).then(a=>{a.code&&a.code===200?(f.success(a.msg),h()):f.warning(a.msg)})},se=e=>{w.confirm("确定取消定标？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{K(e)}).catch(()=>{})},K=e=>{Le({ques_id_list:e}).then(a=>{a.code&&a.code===200?(f.success(a.msg),V(),h()):f.warning(a.msg)})},oe=e=>{w.confirm("确定重新定标？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{A([e])}).catch(()=>{})},re=()=>{N||(N=setInterval(()=>{h("auto")},2e4))},V=()=>{N&&clearInterval(N)},X=(e,t)=>{const{ques_id:a,paper_id:o}=e;let l={ques_id:a,paper_id:o};t&&(l.flag=t),Z.push({path:"/ai-calibration/calibrate-detail/index",query:l})},ie=e=>{B.value=e},ue=e=>{b.value.pageOptions.pageSize=e,h()},ce=e=>{b.value.pageOptions.currentPage=e,h()},pe=()=>{let e={},t="确定导出选择的评分数据？";if(B.value.length){let o=[];e.ques_id_list=[],B.value.forEach(l=>{e.ques_id_list.push(l.ques_id),l.progress_count&&o.push(Number(l.progress_count.split("/")[1]))}),e.total_count=o.reduce((l,c)=>l+c,0)}else e=JSON.parse(JSON.stringify(u.value.getAllCardData())),e.ques_type_code_list.length||delete e.ques_type_code_list,e.set_std_state_list.length||delete e.set_std_state_list,t="确定导出查询的数据？";w.confirm(t,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",cancelButtonClass:"exitCancelBtnBox",type:"warning"}).then(()=>{e.ques_id_list=B.value.map(o=>o.ques_id),de(e)}).catch(()=>{})},de=e=>{je("post","/v1/std_transfer/export_answer_cluster","",{},e,"sqlite").then(t=>{L.value.manualClose()}),_e()},_e=()=>{L.value.openDialog()};function me(){S.value=[]}return(e,t)=>{const a=v("form-component"),o=v("el-card"),l=v("el-button"),c=v("Auth"),x=v("el-dropdown-item"),g=v("el-dropdown-menu"),R=v("el-dropdown"),W=v("table-component");return C(),Be("div",Ae,[p("div",He,[i(o,null,{default:n(()=>[p("div",{ref_key:"formDivRef",ref:$,class:"query-box"},[i(a,{ref_key:"formRef",ref:u,modelValue:I.value,"onUpdate:modelValue":t[0]||(t[0]=s=>I.value=s),"form-options":J,"is-query-btn":!0,onQueryDataFn:h,onOnchangeFn:ee,onResetFields:me},null,8,["modelValue","form-options"])],512)]),_:1}),i(o,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:n(()=>[p("div",$e,[p("div",Ie,[p("span",null,[r("定标总数："),p("strong",null,z(d.value.total),1)]),p("span",null,[r("已定标数："),p("strong",null,z(d.value.alreadyCalibrate),1)]),p("span",null,[r("未定标数："),p("strong",null,z(d.value.unCalibrate),1)]),p("span",null,[r("异常定标："),p("strong",null,z(d.value.errorCalibrate),1)])]),p("div",null,[i(c,{value:"calibrate/export"},{default:n(()=>[i(l,{class:"mr-[12px]",type:"primary",onClick:pe},{default:n(()=>[r("导出评分")]),_:1})]),_:1}),i(c,{value:"calibrate/batchCalibration"},{default:n(()=>[i(R,{onCommand:le},{dropdown:n(()=>[i(g,null,{default:n(()=>[i(x,{command:"start"},{default:n(()=>[r("批量开始")]),_:1}),i(x,{command:"pause"},{default:n(()=>[r("批量暂停")]),_:1}),i(x,{command:"continue"},{default:n(()=>[r("批量继续")]),_:1}),i(x,{command:"cancel"},{default:n(()=>[r("批量取消")]),_:1})]),_:1})]),default:n(()=>[i(l,{type:"primary"},{default:n(()=>[r(" 批量定标 ")]),_:1})]),_:1})]),_:1})])]),i(W,{"min-height":b.value.styleOptions.minHeight,"table-options":b.value,"table-data":F.value,onOnHandleSelectionChange:ie,onOnHandleSizeChange:ue,onOnHandleCurrentChange:ce},{opera:n(s=>[y(q)("calibrate/start")&&s.row.set_std_state===1?(C(),k(l,{key:0,type:"text",class:"text-btn",onClick:D=>te([s.row.ques_id])},{default:n(()=>[r("开始定标 ")]),_:2},1032,["onClick"])):y(q)("calibrate/pause")&&s.row.set_std_state===2?(C(),k(l,{key:1,type:"text",class:"text-btn",onClick:D=>ae([s.row.ques_id])},{default:n(()=>[r("暂停定标 ")]),_:2},1032,["onClick"])):y(q)("calibrate/continue")&&s.row.set_std_state===4?(C(),k(l,{key:2,type:"text",class:"text-btn",onClick:D=>ne([s.row.ques_id])},{default:n(()=>[r("继续定标 ")]),_:2},1032,["onClick"])):y(q)("calibrate/restart")&&s.row.set_std_state===3?(C(),k(l,{key:3,type:"text",class:"text-btn",onClick:D=>oe(s.row.ques_id)},{default:n(()=>[r("重新定标 ")]),_:2},1032,["onClick"])):H("",!0),y(q)("calibrate/cancel")&&[2,4].includes(s.row.set_std_state)?(C(),k(l,{key:4,type:"text",class:"text-btn",onClick:D=>se([s.row.ques_id])},{default:n(()=>[r("取消定标 ")]),_:2},1032,["onClick"])):H("",!0),y(q)("calibrate/detail")&&s.row.set_std_state!==1?(C(),k(l,{key:5,type:"text",class:"text-btn",onClick:D=>X(s.row)},{default:n(()=>[r("详情 ")]),_:2},1032,["onClick"])):H("",!0)]),_:1},8,["min-height","table-options","table-data"])]),_:1})]),i(y(fe),{ref_key:"progressBarRef",ref:L,title:"导出评分进度",apiInfo:{params:{record_type:4},url:"/v1/transfer/get_import_process"}},null,512)])}}}),at=qe(Je,[["__scopeId","data-v-b2404b55"]]);export{at as default};
