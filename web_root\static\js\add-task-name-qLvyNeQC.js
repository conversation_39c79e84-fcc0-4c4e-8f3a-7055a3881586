var B=Object.defineProperty,D=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var k=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var v=(o,e,a)=>e in o?B(o,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[e]=a,h=(o,e)=>{for(var a in e||(e={}))O.call(e,a)&&v(o,a,e[a]);if(k)for(var a of k(e))U.call(e,a)&&v(o,a,e[a]);return o},V=(o,e)=>D(o,E(e));import{c as P}from"./marking-task-DrCSMu9U.js";import{d as W,l as i,r as p,o as j,g as z,h as r,e as x,b as f,f as q,u as A,T as w,_ as F}from"./index-B63pSD2p.js";const H={class:"footer-btn"},I=W({__name:"add-task-name",emits:["createTask"],setup(o,{expose:e,emit:a}){const y=a,m=i(!1),C=i({field:[{prop:"ques_code",label:"试题编号",minWidth:"160px"},{prop:"m_read_task_name",label:"任务名称",minWidth:"240px",type:"slot"}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!1,currentPage:1,pageSize:10,total:0}});let u=i([]);const g=i({}),T=(_,t)=>{m.value=!0,g.value=t,_.forEach(c=>{const{ques_code:s,ques_id:l,subject_name:d,ques_order:n}=c;u.value.push({ques_code:s,ques_id:l,m_read_task_name:d+s,ques_order:n})})},b=()=>{u.value=[],m.value=!1},N=()=>{let _={task_list:[]};u.value.forEach(t=>{const{ques_code:c,ques_id:s,ques_order:l,m_read_task_name:d}=t;_.task_list.push(V(h({},g.value),{ques_code:c,ques_id:s,ques_order:l,m_read_task_name:d}))}),P(_).then(t=>{t.code&&t.code===200?(w.success(t.msg),y("createTask"),b()):w.warning(t.msg)})};return e({openDialog:T}),(_,t)=>{const c=p("el-input"),s=p("table-component"),l=p("el-button"),d=p("el-dialog");return j(),z(d,{modelValue:m.value,"onUpdate:modelValue":t[0]||(t[0]=n=>m.value=n),title:"设置任务名称","show-close":!0,"align-center":"","append-to-body":"",draggable:"","close-on-click-modal":!1,"before-close":b},{footer:r(()=>[x("div",H,[f(l,{type:"primary",onClick:N},{default:r(()=>[q("保存")]),_:1}),f(l,{onClick:b},{default:r(()=>[q("取消")]),_:1})])]),default:r(()=>[x("div",null,[f(s,{ref:"tableRef",class:"table-flex-box",minHeight:"400px","table-options":C.value,"table-data":A(u)},{m_read_task_name:r(n=>[f(c,{modelValue:n.row.m_read_task_name,"onUpdate:modelValue":S=>n.row.m_read_task_name=S},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["table-options","table-data"])])]),_:1},8,["modelValue"])}}}),J=F(I,[["__scopeId","data-v-6603c429"]]);export{J as default};
