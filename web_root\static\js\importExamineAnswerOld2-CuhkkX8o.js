var b=(_,y,a)=>new Promise((n,r)=>{var v=o=>{try{d(a.next(o))}catch(f){r(f)}},c=o=>{try{d(a.throw(o))}catch(f){r(f)}},d=o=>o.done?n(o.value):Promise.resolve(o.value).then(v,c);d((a=a.apply(_,y)).next())});import{a as N}from"./uploadRequest-IEYs8WTn.js";import{d as U,n as I,ao as R,P as C,B as V,l as g,r as u,o as k,g as q,h as s,b as i,e as m,f as D,c as A,t as F,y as M,T as h,_ as O}from"./index-B63pSD2p.js";const T={key:0,class:"selected-folder"},Z={class:"folder-info"},j=["title"],z={class:"file-count"},G=U({__name:"importExamineAnswerOld2",props:{isShowDialog:{type:Boolean,required:!0,default:!1},title:{type:String,default:""},ZIndex:{required:!1,type:Number,default:2001},getParams:{type:Function,default:()=>{}}},emits:["update:isShowDialog","closeDialog"],setup(_,{emit:y}){const a=_,n=y;I(()=>{}),R(()=>{});const r=C({visible:!1}),v=C({password:""}),c=()=>{r.visible=!1,n("update:isShowDialog",!1),n("closeDialog")},d=()=>b(this,null,function*(){if(!o.value.files.length){h.warning("请先选择文件夹");return}let t="/v1/transfer/upload-folder";const l=new FormData;if(o.value.files.forEach((e,p)=>{l.append("files",e)}),typeof a.getParams=="function"){let e=a.getParams();for(let p in e)l.append(p,e[p])}n("uploadBegin"),N("post",t,l,!1).then(e=>{e&&e.code&&e.code===200?(h.success(e.msg),n("uploadSuccess"),c()):(h.warning(e.msg||"上传失败，后端返回null"),n("uploadError"))}).catch(e=>{h.error((e==null?void 0:e.msg)||"上传失败"),n("uploadError")})});V(()=>a.isShowDialog,t=>{r.visible=t},{deep:!0,immediate:!0});const o=g({name:"",files:[],fileCount:0});g(),g();const f=g(),S=()=>{f.value.click()},x=t=>{const l=Array.from(t.target.files);if(l.length>0){const w=l[0].webkitRelativePath.split("/")[0];o.value={name:w,files:l,fileCount:l.length}}};return(t,l)=>{const e=u("el-button"),p=u("Folder"),w=u("el-icon"),P=u("el-form-item"),B=u("el-form"),E=u("DialogComponent");return k(),q(E,{isShowDialog:r.visible,width:"500px",onCloseDialog:c,onSure:d,beforeClose:c,title:_.title,fullscreen:!1},{content:s(()=>[i(B,{model:v,"label-width":"100px"},{default:s(()=>[i(P,{label:"选择文件"},{default:s(()=>[m("input",{ref_key:"folderInputRef",ref:f,type:"file",webkitdirectory:"",directory:"",multiple:"",style:{display:"none"},onChange:x},null,544),m("div",null,[i(e,{type:"primary",icon:t.Folder,onClick:S},{default:s(()=>[D(" 选择文件夹上传 ")]),_:1},8,["icon"]),o.value.name?(k(),A("div",T,[m("div",Z,[i(w,null,{default:s(()=>[i(p)]),_:1}),m("span",{class:"folder-name",title:o.value.name},F(o.value.name),9,j),m("span",z,"（"+F(o.value.fileCount)+" 个文件）",1)])])):M("",!0)])]),_:1})]),_:1},8,["model"])]),footer:s(()=>[i(e,{onClick:c,disabled:t.examineeUploading},{default:s(()=>[D("取消")]),_:1},8,["disabled"]),i(e,{type:"primary",onClick:d,loading:t.examineeUploading},{default:s(()=>[D("确认上传")]),_:1},8,["loading"])]),_:1},8,["isShowDialog","title"])}}}),L=O(G,[["__scopeId","data-v-39406f28"]]);export{L as default};
