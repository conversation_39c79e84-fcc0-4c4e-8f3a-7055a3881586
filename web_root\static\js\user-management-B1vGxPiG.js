import{aQ as t,aR as s}from"./index-B63pSD2p.js";const u=e=>t.request("post",s("/v1/user/register"),{data:e}),a=e=>t.request("post",s("/v1/user/get_user"),{data:e}),_=e=>t.request("post",s("/v1/user_module/update_user_module"),{data:e}),o=e=>t.request("post",s("/v1/user_data/get_user_data"),{data:e}),p=e=>t.request("post",s("/v1/user_data/update_user_data"),{data:e}),n=e=>t.request("post",s("/v1/user/update_user"),{data:e}),d=e=>t.request("post",s("/v1/user/change_password"),{data:e}),c=e=>t.request("post",s("/v1/user/reset_password"),{data:e}),g=e=>t.request("get",s("/v1/user/get_default_pwd"),{params:e}),q=e=>t.request("post",s("/v1/user/delete_user"),{data:e}),v=e=>t.request("get",s("/v1/user/get_region"),{params:e}),l=e=>t.request("post",s("/v1/user/update_user_state"),{data:e}),i=e=>t.request("post",s("/v1/project/get_subject_by_project_list"),{data:e});export{v as a,g as b,d as c,q as d,u as e,n as f,a as g,i as h,_ as i,o as j,p as k,c as r,l as u};
