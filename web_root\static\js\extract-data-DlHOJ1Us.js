import{S as Z}from"./index-BCxZUZMh.js";import{p as ee,g as le,a as te}from"./common-methods-BWkba4Bo.js";import{aQ as ae,aR as oe,d as se,i as ne,l as a,P as ue,ao as re,r as m,o as f,g as S,h as s,c as L,b as n,f as d,y as z,e as _,F as ie,t as ce,u as de,T as j,N as pe,_ as _e}from"./index-B63pSD2p.js";import{g as me}from"./base-DyvdloLK.js";import"./test-paper-management-DjV_45YZ.js";const fe=D=>ae.request("post",oe("/v1/human_quality/quality_sample"),{data:D}),ve={class:"re-form-box"},ge={class:"top"},be={class:"form-box"},he={class:"bottom"},ye={class:"re-table-box"},xe={key:0,class:"footer-btn"},qe=se({__name:"extract-data",setup(D,{expose:U}){const P=ne(),Q=a("抽样质检"),q=a(!1),t=a({score_list:[null,null]});a([]);const B=ue({labelWidth:"88px",itemWidth:"230px",inline:!0,rules:{project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],ques_code:[{required:!0,message:"请选择试题",trigger:["blur","change"]}]},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>ee.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>C.value},{label:"试题",prop:"ques_code",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择试题",props:{value:"ques_code",label:"ques_name"},optionData:()=>v.value},{label:"考生密号",prop:"stu_secret_num",type:"input",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请输入考生密号"},{label:"题得分",prop:"score",type:"template",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目"}]}),C=a([]),v=a([]),u=a({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"round_score",label:"正评分",minWidth:"160px"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,pageOptions:0}}),r=a([]),V=a([]),g=a("add"),b=a(!1);a({});const R=a(""),h=a(100),H=a(),y=a(),E=l=>{q.value=!0,g.value=l,le(),g.value==="result"||(b.value=!1)};let x=null;const T=()=>{x=new ResizeObserver(l=>{l.forEach(e=>{y.value&&(u.value.styleOptions.minHeight=H.value.clientHeight-y.value.clientHeight-40+"px")})}),y.value&&x.observe(y.value)};re(()=>{x&&(x.disconnect(),x=null)});const k=()=>{b.value=!1,q.value=!1,r.value=[],t.value={score_list:[null,null]},N.value=!1},W=()=>{let l={project_id:t.value.project_id,subject_id:t.value.subject_id,is_quality:1};me(l).then(e=>{e.code&&e.code===200?v.value=e.data:j.error(e.msg)})},N=a(!1),F=a(),I=()=>{F.value.formValidate().then(()=>{b.value||(b.value=!0),T(),M()}).catch(()=>{j.warning("请按要求填写！")})},$=l=>{u.value.pageOptions.currentPage=1,u.value.pageOptions.pageSize=l,r.value=V.value.slice(0,l)},J=l=>{u.value.pageOptions.currentPage=l;const e=u.value.pageOptions.pageSize,o=u.value.pageOptions.total,i=l*e>o?o:l*e;r.value=V.value.slice((l-1)*e,i-1)},O=(l,e)=>{if(l.prop==="project_id")C.value=[],v.value=[],t.value.subject_id&&(t.value.subject_id=null),t.value.ques_code&&(t.value.ques_code=null),e&&te(e).then(o=>{C.value=o||[]});else if(l.prop==="subject_id")v.value=[],e&&W();else if(l.prop==="ques_code")if(e){const o=v.value.find(i=>i.ques_code===e);h.value=Number(o.ques_score)}else h.value=100;else l.prop},M=()=>{let l=JSON.parse(JSON.stringify(t.value));l.score_list[0]===null&&l.score_list[1]===null?delete l.score_list:l.score_list[0]===null?l.score_list[0]=0:l.score_list[1]===null&&(l.score_list[1]=h.value),fe(l).then(e=>{var o;if(e.code&&e.code===200){const i=u.value.pageOptions.pageSize;((o=e.data.data)==null?void 0:o.length)>i?(V.value=e.data.data,r.value=V.value.slice(0,i)):r.value=e.data.data,u.value.pageOptions.total=e.data.total,R.value=e.data.round_id}else j.error(e.msg)})},A=()=>{if(r.value.length==0){j.warning("暂无数据，无法开始！");return}pe().setItem("stu_secret_num_list",r.value.map(l=>l.stu_secret_num)),P.push({path:"/manual-marking/start-marking/index",query:{project_id:t.value.project_id,subject_id:t.value.subject_id,type:3,ques_code:t.value.ques_code,reviewer_id:t.value.ques_code,round_id:R.value}}),k()};return U({openDialog:E}),(l,e)=>{const o=m("el-text"),i=m("el-input-number"),G=m("form-component"),K=m("el-scrollbar"),w=m("el-button"),X=m("table-component"),Y=m("el-dialog");return f(),S(Y,{modelValue:q.value,"onUpdate:modelValue":e[4]||(e[4]=c=>q.value=c),title:Q.value,"show-close":!0,"align-center":"","append-to-body":"",draggable:"",width:"80%",fullscreen:"","close-on-click-modal":!1,"before-close":k,"destroy-on-close":""},{footer:s(()=>[g.value==="add"?(f(),L("div",xe,[n(w,{onClick:k},{default:s(()=>[d("取消")]),_:1}),n(w,{type:"primary",onClick:A,loading:l.saveLoading},{default:s(()=>[d("开始质检")]),_:1},8,["loading"])])):z("",!0)]),default:s(()=>[_("div",{class:"flex h-full",ref_key:"mainRef",ref:H,style:{height:"84vh"}},[_("div",ve,[_("div",ge,[n(o,{size:"large"},{default:s(()=>[d("质检条件")]),_:1})]),g.value==="add"?(f(),L(ie,{key:0},[_("div",be,[n(K,{always:""},{default:s(()=>[n(G,{ref_key:"formRef",ref:F,modelValue:t.value,"onUpdate:modelValue":e[3]||(e[3]=c=>t.value=c),"form-options":B,"is-query-btn":!1,onOnchangeFn:O},{score:s(({scope:c})=>[n(i,{modelValue:t.value.score_list[0],"onUpdate:modelValue":e[0]||(e[0]=p=>t.value.score_list[0]=p),style:{width:"50px"},controls:!1,min:0,max:h.value,onChange:p=>O(c,t.value.score_list[0])},null,8,["modelValue","max","onChange"]),n(o,null,{default:s(()=>[d(" - ")]),_:1}),n(i,{modelValue:t.value.score_list[1],"onUpdate:modelValue":e[1]||(e[1]=p=>t.value.score_list[1]=p),style:{width:"50px","margin-right":"24px"},controls:!1,min:0,max:h.value,onChange:p=>O(c,t.value.score_list[0])},null,8,["modelValue","max","onChange"]),n(o,null,{default:s(()=>[d("抽")]),_:1}),n(i,{modelValue:t.value.sample_num,"onUpdate:modelValue":e[2]||(e[2]=p=>t.value.sample_num=p),style:{width:"60px",margin:"0 3px"},controls:!1,step:1,min:1,"step-strictly":"",onChange:p=>O(c,t.value.sample_num)},null,8,["modelValue","onChange"]),n(o,null,{default:s(()=>[d("份")]),_:1})]),_:1},8,["modelValue","form-options"])]),_:1})]),_("div",he,[n(w,{type:"primary",onClick:I,loading:N.value},{default:s(()=>[d("开始抽取 ")]),_:1},8,["loading"])])],64)):z("",!0)]),_("div",ye,[_("div",{class:"top",ref_key:"titleRef",ref:y},[_("div",null,[n(o,{size:"large"},{default:s(()=>[d("查询结果")]),_:1})]),g.value!=="result"?(f(),S(o,{key:0},{default:s(()=>{var c;return[d("已查数据："+ce((c=r.value)!=null&&c.length?r.value.length:"-")+"条 ",1)]}),_:1})):z("",!0)],512),b.value?(f(),S(X,{key:1,minHeight:u.value.styleOptions.minHeight,"table-options":u.value,"table-data":r.value,onOnHandleSizeChange:$,onOnHandleCurrentChange:J},null,8,["minHeight","table-options","table-data"])):(f(),S(de(Z),{key:0,title:"抽取的质检数据",stepList:["选择左侧质检条件","点击【开始抽取】按钮，抽取数据"]}))])],512)]),_:1},8,["modelValue","title"])}}}),ke=_e(qe,[["__scopeId","data-v-acdc9a17"]]);export{ke as default};
