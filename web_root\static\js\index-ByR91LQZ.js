import{_ as Q}from"./createGrade.vue_vue_type_script_setup_true_lang-C4Npt1Wp.js";import E from"./detail-DDbI7fJ6.js";import{P as T}from"./index-Astsj93h.js";import{d as M,e as $}from"./score-result-DfeCiQTg.js";import{g as K}from"./test-paper-management-DjV_45YZ.js";import{p as X,a as R,g as Y}from"./common-methods-BWkba4Bo.js";import{d as Z,az as ee,i as ae,l as t,P as te,aN as le,n as re,C as oe,a2 as ne,ao as se,T as z,r as m,o as ie,c as pe,e as D,b as n,h,u as F,ac as ue,ad as ce,_ as de}from"./index-B63pSD2p.js";import{h as _e,a as me}from"./handleCache-Dzg6LWBc.js";import{c as fe,a as ge}from"./calculateTableHeight-BjE6OFD1.js";import"./pageCache-DQQfxtZI.js";const ve=_=>(ue("data-v-16c5ffe5"),_=_(),ce(),_),be={class:"zf-first-box"},he={class:"zf-second-box"},De=ve(()=>D("span",{class:"mr-[3px] ml-[3px]"},"-",-1)),xe=Z({name:"score-result",__name:"index",setup(_){const P=ee(),B=ae(),x=t(null),r=t(null);t(null);const I=t(null),y=t(null),f=t(!1),C=t(""),j=t({}),d=t([]),g=t([]),v=t({score_range:["",""]}),u=t({mark_score_range:[]}),O=te({column:3,labelWidth:"68px",itemWidth:"220px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",placeholder:"请选择所属资格",optionData:()=>X.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,placeholder:"请选择所属科目",optionData:()=>d.value},{label:"考生密号",prop:"stu_secret_num",type:"input",placeholder:"请输入考生密号",clearable:!0,defaultValue:null,filterable:!0,optionData:()=>[]},{label:"考生总分",prop:"mark_score_range",type:"template",defaultValue:"",placeholder:"请输入",clearable:!0}]}),o=t({field:[{prop:"project_name",label:"所属资格",minWidth:"120px"},{prop:"subject_name",label:"所属科目",minWidth:"120px"},{prop:"exam_session",label:"场次",width:"110px"},{prop:"stu_secret_num",label:"考生密号",minWidth:"160px"},{prop:"subject_total_score",label:"试卷总分",width:"110px",sortable:!0},{prop:"score",label:"考生总分",width:"110px",sortable:!0,fixed:!1,clickBtn:e=>{const l=Object.assign({flag:"manual"},e);B.push({path:"/grade-management/score-details/index",query:l})}},{prop:"operation",label:"操作",type:"template",width:"80px",templateGroup:[{title:()=>(le("score-result/detail"),"详情"),clickBtn(e){C.value=e.student_subject_grade_id,j.value=e,f.value=!0}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:100,total:0}});let w=t([]);const N=t([]),b=t("");let S=null;re(()=>{fe(S,x.value,o.value,!1),b.value=JSON.parse(JSON.stringify(P.path)),_e(b.value,O,r.value,o.value,v.value),oe(()=>{const{project_id:e,subject_id:a}=r.value.getAllCardData();e&&R(e).then(l=>{d.value=l||[]}),a&&V()}),Y(),c(),q()}),ne(()=>{me(r.value,o.value,b.value,v.value)}),se(()=>{ge(S)});const H=e=>{N.value=e},A=(e,a)=>{e.prop==="project_id"?(d.value=[],g.value=[],r.value.getCardData("subject_id")&&r.value.setCardData("subject_id",null),r.value.getCardData("paper_id")&&r.value.setCardData("paper_id",null),a&&R(a).then(l=>{d.value=l||[]})):e.prop==="subject_id"&&(g.value=[],r.value.getCardData("paper_id")&&r.value.setCardData("paper_id",null),a&&V())},V=()=>{const{project_id:e,subject_id:a}=r.value.getAllCardData();K({project_id:e,subject_id:a,page_size:-1}).then(s=>{s.code&&s.code===200&&(s.data.data.forEach(i=>{i.label=i.paper_name,i.value=i.paper_id}),g.value=s.data.data)})},c=()=>{let e=G();M(e).then(a=>{a.code&&a.code===200?(w.value=a.data.data,o.value.pageOptions.total=a.data.total):z.warning(a.msg)})},G=()=>{let e=JSON.parse(JSON.stringify(r.value.getAllCardData())),{currentPage:a,pageSize:l}=o.value.pageOptions;if(e.current_page=a,e.page_size=l,Object.assign(e,v.value),e.score_range[0]==""&&e.score_range[1]=="")delete e.score_range,delete e.query_score_type;else if(Number(e.score_range[0])>Number(e.score_range[1])){const s=e.score_range[0];e.score_range[0]=e.score_range[1],e.score_range[1]=s}return e},U=e=>{o.value.pageOptions.pageSize=e,c()},W=e=>{o.value.pageOptions.currentPage=e,c()},k=()=>{y.value.openDialog()},q=()=>{$({}).then(a=>{a.code&&a.code===200?Object.keys(a.data).length&&a.data.progress!==100&&k():z.warning(a.msg)})};function J(){d.value=[]}return(e,a)=>{const l=m("el-input-number"),s=m("form-component"),i=m("el-card"),L=m("table-component");return ie(),pe("div",be,[D("div",he,[n(i,null,{default:h(()=>[D("div",{ref_key:"formDivRef",ref:x,class:"flex"},[n(s,{ref_key:"formRef",ref:r,modelValue:u.value,"onUpdate:modelValue":a[2]||(a[2]=p=>u.value=p),"form-options":O,"is-query-btn":!0,onOnchangeFn:A,onQueryDataFn:c,onResetFields:J},{mark_score_range:h(()=>[n(l,{modelValue:u.value.mark_score_range[0],"onUpdate:modelValue":a[0]||(a[0]=p=>u.value.mark_score_range[0]=p),style:{width:"102px"},"controls-position":"right",precision:1,placeholder:"请输入",controls:!1,clearable:""},null,8,["modelValue"]),De,n(l,{modelValue:u.value.mark_score_range[1],"onUpdate:modelValue":a[1]||(a[1]=p=>u.value.mark_score_range[1]=p),"controls-position":"right",precision:1,style:{width:"103px","margin-right":"18px"},placeholder:"请输入",controls:!1,clearable:""},null,8,["modelValue"])]),_:1},8,["modelValue","form-options"])],512)]),_:1}),n(i,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:h(()=>[n(L,{minHeight:o.value.styleOptions.minHeight,"table-options":o.value,"table-data":F(w),onOnHandleSelectionChange:H,onOnHandleSizeChange:U,onOnHandleCurrentChange:W},null,8,["minHeight","table-options","table-data"])]),_:1})]),n(Q,{ref_key:"createGradeRef",ref:I,onAfterCreateGrade:k,onQueryData:c},null,512),n(F(T),{ref_key:"progressBarRef",ref:y,title:"生成成绩进度",apiInfo:{params:{},url:"/v1/ai_mark/create_ai_grade_process"},onQueryData:c},null,512),n(E,{drawerVisible:f.value,"onUpdate:drawerVisible":a[3]||(a[3]=p=>f.value=p),detailId:C.value,rowDetailData:j.value},null,8,["drawerVisible","detailId","rowDetailData"])])}}}),Fe=de(xe,[["__scopeId","data-v-16c5ffe5"]]);export{Fe as default};
