#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卓帆电子化考试阅卷管理系统 - 简易启动器
适用于不支持完整GUI的环境
"""

import subprocess
import os
import time
import psutil
from datetime import datetime

class SimpleLauncher:
    def __init__(self):
        # 程序路径
        self.redis_path = r"Redis-x64-3.0.504\redis-server.exe"
        self.main_program_path = r"卓帆电子化考试阅卷管理系统V1.0.exe"
        self.service_program_path = r"定时任务V1.0.exe"
        
        # 进程管理
        self.redis_process = None
        self.main_process = None
        self.service_process = None
        
    def log_message(self, message):
        """打印日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def check_programs_exist(self):
        """检查程序文件是否存在"""
        missing_files = []
        
        if not os.path.exists(self.redis_path):
            missing_files.append(f"Redis服务器: {self.redis_path}")
            
        if not os.path.exists(self.main_program_path):
            missing_files.append(f"主程序: {self.main_program_path}")
            
        if not os.path.exists(self.service_program_path):
            missing_files.append(f"数据服务程序: {self.service_program_path}")
            
        if missing_files:
            self.log_message("错误：以下程序文件未找到：")
            for file in missing_files:
                self.log_message(f"  - {file}")
            return False
        return True
        
    def is_process_running(self, process_name):
        """检查进程是否正在运行"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False
        
    def kill_process_by_name(self, process_name):
        """根据进程名终止进程"""
        killed = False
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    proc.terminate()
                    killed = True
                    self.log_message(f"已终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                self.log_message(f"无法终止进程 {process_name}: {str(e)}")
        return killed
        
    def start_redis(self):
        """启动Redis服务"""
        try:
            if self.is_process_running("redis-server"):
                self.log_message("Redis已经在运行中")
                return True
                
            self.log_message("正在启动Redis服务...")
            self.redis_process = subprocess.Popen([self.redis_path], 
                                                 cwd=os.path.dirname(self.redis_path))
            time.sleep(3)  # 等待Redis启动
            
            if self.is_process_running("redis-server"):
                self.log_message("Redis服务启动成功")
                return True
            else:
                self.log_message("Redis服务启动失败")
                return False
                
        except Exception as e:
            self.log_message(f"启动Redis时出错: {str(e)}")
            return False
            
    def start_main_program(self):
        """启动主程序"""
        try:
            if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
                self.log_message("主程序已经在运行中")
                return True
                
            self.log_message("正在启动主程序...")
            self.main_process = subprocess.Popen([self.main_program_path])
            time.sleep(2)  # 等待主程序启动
            
            if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
                self.log_message("主程序启动成功")
                return True
            else:
                self.log_message("主程序启动失败")
                return False
                
        except Exception as e:
            self.log_message(f"启动主程序时出错: {str(e)}")
            return False
            
    def start_service_program(self):
        """启动数据服务程序"""
        try:
            if self.is_process_running("定时任务V1.0"):
                self.log_message("数据服务程序已经在运行中")
                return True
                
            self.log_message("正在启动数据服务程序...")
            self.service_process = subprocess.Popen([self.service_program_path])
            time.sleep(2)  # 等待服务程序启动
            
            if self.is_process_running("定时任务V1.0"):
                self.log_message("数据服务程序启动成功")
                return True
            else:
                self.log_message("数据服务程序启动失败")
                return False
                
        except Exception as e:
            self.log_message(f"启动数据服务程序时出错: {str(e)}")
            return False
            
    def stop_service_program(self):
        """关闭数据服务程序"""
        try:
            self.log_message("正在关闭数据服务程序...")
            if self.kill_process_by_name("定时任务V1.0"):
                time.sleep(1)
                self.log_message("数据服务程序已关闭")
                return True
            else:
                self.log_message("数据服务程序未在运行")
                return True
        except Exception as e:
            self.log_message(f"关闭数据服务程序时出错: {str(e)}")
            return False
            
    def stop_main_program(self):
        """关闭主程序"""
        try:
            self.log_message("正在关闭主程序...")
            if self.kill_process_by_name("卓帆电子化考试阅卷管理系统V1.0"):
                time.sleep(1)
                self.log_message("主程序已关闭")
                return True
            else:
                self.log_message("主程序未在运行")
                return True
        except Exception as e:
            self.log_message(f"关闭主程序时出错: {str(e)}")
            return False
            
    def stop_redis(self):
        """关闭Redis服务"""
        try:
            self.log_message("正在关闭Redis服务...")
            if self.kill_process_by_name("redis-server"):
                time.sleep(1)
                self.log_message("Redis服务已关闭")
                return True
            else:
                self.log_message("Redis服务未在运行")
                return True
        except Exception as e:
            self.log_message(f"关闭Redis服务时出错: {str(e)}")
            return False
            
    def start_all(self):
        """按顺序启动所有程序"""
        self.log_message("开始一键启动...")
        
        # 1. 启动Redis
        if not self.start_redis():
            self.log_message("启动失败：Redis服务启动失败")
            return False
            
        # 2. 启动主程序
        if not self.start_main_program():
            self.log_message("启动失败：主程序启动失败")
            return False
            
        # 3. 启动数据服务程序
        if not self.start_service_program():
            self.log_message("启动失败：数据服务程序启动失败")
            return False
            
        self.log_message("所有程序启动完成！")
        return True
        
    def stop_all(self):
        """按顺序关闭所有程序"""
        self.log_message("开始一键关闭...")
        
        # 1. 关闭数据服务程序
        self.stop_service_program()
        
        # 2. 关闭主程序
        self.stop_main_program()
        
        # 3. 关闭Redis
        self.stop_redis()
        
        self.log_message("所有程序关闭完成！")
        
    def show_status(self):
        """显示当前状态"""
        self.log_message("=== 当前系统状态 ===")
        
        if self.is_process_running("redis-server"):
            self.log_message("Redis: 运行中 ✓")
        else:
            self.log_message("Redis: 未启动 ✗")
            
        if self.is_process_running("卓帆电子化考试阅卷管理系统V1.0"):
            self.log_message("主程序: 运行中 ✓")
        else:
            self.log_message("主程序: 未启动 ✗")
            
        if self.is_process_running("定时任务V1.0"):
            self.log_message("数据服务: 运行中 ✓")
        else:
            self.log_message("数据服务: 未启动 ✗")
            
        self.log_message("==================")
        
    def run(self):
        """运行主程序"""
        self.log_message("卓帆电子化考试阅卷管理系统 - 简易启动器")
        self.log_message("==========================================")
        
        if not self.check_programs_exist():
            input("按回车键退出...")
            return
            
        while True:
            print("\n请选择操作：")
            print("1. 一键启动")
            print("2. 一键关闭") 
            print("3. 查看状态")
            print("4. 退出")
            
            choice = input("请输入选项 (1-4): ").strip()
            
            if choice == "1":
                self.start_all()
            elif choice == "2":
                self.stop_all()
            elif choice == "3":
                self.show_status()
            elif choice == "4":
                self.log_message("退出启动器")
                break
            else:
                print("无效选项，请重新输入")

if __name__ == "__main__":
    try:
        launcher = SimpleLauncher()
        launcher.run()
    except Exception as e:
        print(f"启动器运行出错: {str(e)}")
        input("按回车键退出...")
