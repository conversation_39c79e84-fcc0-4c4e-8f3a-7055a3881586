import{aQ as l,aR as s,d as R,l as r,P as W,n as B,r as f,o as L,g as U,h as d,e as g,b as v,f as w,C as z,T as c}from"./index-B63pSD2p.js";const K=a=>l.request("post",s("/v1/project/get_project"),{data:a}),J=a=>l.request("get",s("/v1/project/get_exam_type"),{params:a}),M=a=>l.request("get",s("/v1/project/get_exam_way"),{params:a}),S=a=>l.request("post",s("/v1/project/create_project"),{data:a}),A=a=>l.request("post",s("/v1/project/update_project"),{data:a}),X=a=>l.request("post",s("/v1/project/delete_project"),{data:a}),Y=a=>l.request("post",s("/v1/project/update_project_state"),{data:a}),H={class:"zf-dialog-first-box"},I={class:"zf-dialog-second-box"},Q={class:"footer-btn"},Z=R({__name:"add-project",emits:["queryData"],setup(a,{expose:D,emit:C}){const y=C,i=r("创建资格"),u=r(!1),_=r("01"),x=r(0),b=r({}),j=W({column:3,labelWidth:"108px",itemWidth:"250px",rules:{exam_type_id:[{required:!0,message:"请选择考试类型",trigger:["blur","change"]}],exam_way_id:[{required:!0,message:"请选择考试方式",trigger:["blur","change"]}],project_name:[{required:!0,message:"请输入项目",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,e,o)=>{if(e.length>50)return o(new Error("资格长度不能超过50！"));o()}}]},fields:[{label:"资格名称",prop:"project_name",type:"input",defaultValue:"",placeholder:"请输入资格名称",isHidden:!1,clearable:!0},{label:"考试类型",prop:"exam_type_id",type:"select",defaultValue:"",placeholder:"请选择考试类型",clearable:!0,optionData:()=>h.value},{label:"考试方式",prop:"exam_way_id",type:"select",defaultValue:"",placeholder:"请选择考试方式",clearable:!0,optionData:()=>V.value},{label:"备注",prop:"remark",type:"textarea",defaultValue:"",placeholder:"请输入备注",clearable:!0}]}),n=r(null),h=r([]),V=r([]);B(()=>{F(),k()});const F=()=>{J({}).then(t=>{t.code&&t.code===200&&(t.data.data.forEach(e=>{e.value=e.exam_type_id,e.label=e.exam_type_name}),h.value=t.data.data)})},k=()=>{M({}).then(t=>{t.code&&t.code===200&&(t.data.data.forEach(e=>{e.value=e.exam_way_id,e.label=e.exam_way_name}),V.value=t.data.data)})},E=(t,e)=>{u.value=!0,_.value=t,t==="01"?i.value="创建资格":t==="02"&&(i.value="编辑资格",x.value=e.project_id,z(()=>{j.fields.map(o=>{e.hasOwnProperty(o.prop)&&n.value.setCardData(o.prop,e[o.prop])})}))},p=()=>{u.value=!1,n.value.resetFieldsFn()},N=()=>{n.value.formValidate().then(()=>{let t=JSON.parse(JSON.stringify(n.value.getAllCardData()));_.value==="01"?T(t):_.value==="02"&&(t.project_id=x.value,O(t))}).catch(()=>{c.warning("请按要求填写！")})},T=t=>{S(t).then(e=>{e.code&&e.code===200?(c.success(e.msg),y("queryData"),p()):c.error(e.msg)})},O=t=>{A(t).then(e=>{e.code&&e.code===200?(c.success(e.msg),y("queryData"),p()):c.error(e.msg)})};return D({openDialog:E}),(t,e)=>{const o=f("form-component"),q=f("el-button"),P=f("el-dialog");return L(),U(P,{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=m=>u.value=m),title:i.value,width:"440px","align-center":"","close-on-click-modal":!1,"before-close":p,draggable:""},{footer:d(()=>[g("div",Q,[v(q,{onClick:p},{default:d(()=>[w("取消")]),_:1}),v(q,{type:"primary",onClick:N},{default:d(()=>[w("确定")]),_:1})])]),default:d(()=>[g("div",H,[g("div",I,[v(o,{ref_key:"formRef",ref:n,modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=m=>b.value=m),"form-options":j,"is-query-btn":!1},null,8,["modelValue","form-options"])])])]),_:1},8,["modelValue","title"])}}});export{Z as _,M as a,J as b,X as d,K as g,Y as u};
