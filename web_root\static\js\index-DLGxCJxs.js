var T=Object.defineProperty;var x=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var H=(n,t,o)=>t in n?T(n,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[t]=o,j=(n,t)=>{for(var o in t||(t={}))w.call(t,o)&&H(n,o,t[o]);if(x)for(var o of x(t))B.call(t,o)&&H(n,o,t[o]);return n};import{aQ as L,aR as U,d as Q,i as A,l as u,P as I,m as $,n as G,ao as J,T as K,r as m,o as O,c as X,e as W,b as _,h as d,f as S,t as C,u as h,aN as R,g as Y,y as Z,_ as ee}from"./index-B63pSD2p.js";import{f as te}from"./handleMethod-BIjqYEft.js";import{c as ae,a as ne}from"./calculateTableHeight-BjE6OFD1.js";import{p as oe,g as re,a as le}from"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";const se=(n,t=!0)=>L.request("post",U("/v1/human_mark/get_my_human_task_list"),{data:n},{},t),ie={class:"zf-first-box",ref:"bottomRef"},pe={class:"zf-second-box"},ue=Q({name:"my-marking-task",__name:"index",props:{entrance:{type:String,default:"my-marking-task"}},setup(n){const t=n,o=A(),g=u(null),V=u(null),c=u({}),q=I({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>oe.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>f.value},{label:"任务名称",prop:"task_name",type:"input",defaultValue:"",placeholder:"请输入任务名称",clearable:!0}]}),f=u([]),r=u({field:[{prop:"project_name",label:"所属资格",minWidth:"100px"},{prop:"subject_name",label:"所属科目",minWidth:"100px"},{prop:"task_name",label:"任务名称",minWidth:"100px"},{prop:"process_name",label:"阅卷模式",minWidth:"120px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px",isHidden:!0},{prop:"ques_order",label:"题号",minWidth:"120px",isHidden:!0},{prop:"ques_code",label:"试题编号",minWidth:"120px"},{prop:"marked_count",label:"已阅量",minWidth:"120px"},{prop:"round_count",label:"轮次",minWidth:"120px",isHidden:!0},{prop:"region",label:"平均速度（份/时）",minWidth:"150px"},{prop:"try_mark_result",label:"试评结果",minWidth:"120px",isHidden:!0,type:"slot"},{prop:"opera",label:"操作",type:"slot",minWidth:"100px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),k=u([]);let y=null;const z=$(()=>t.entrance==="my-trial-task"?(r.value.field.forEach(e=>{e.prop==="round_count"||e.prop==="try_mark_result"?e.isHidden=!1:e.prop==="process_name"&&(e.isHidden=!0)}),2):1);G(()=>{re(),ae(y,g.value,r.value,!1),b()}),J(()=>{ne(y)});const F=(e,s)=>{e.prop==="project_id"&&(f.value=[],c.value.subject_id&&(c.value.subject_id=null),s&&le(s).then(p=>{f.value=p||[]}))},b=()=>{let{currentPage:e,pageSize:s}=r.value.pageOptions,p=j({task_type:z.value,current_page:e,page_size:s},c.value);se(p).then(l=>{l.code&&l.code===200?(k.value=l.data.data,l.data.data.forEach(a=>{a.name="-"}),l.data.data.find(a=>a.exam_mode===1)?r.value.field.forEach(a=>{(a.prop==="paper_name"||a.prop==="ques_order")&&(a.isHidden=!0)}):r.value.field.forEach(a=>{(a.prop==="paper_name"||a.prop==="ques_order")&&(a.isHidden||(a.isHidden=!0))}),r.value.pageOptions.total=l.data.total):K.error(l.msg)})},P=e=>{r.value.pageOptions.currentPage=1,r.value.pageOptions.pageSize=e,b()},D=e=>{r.value.pageOptions.currentPage=e,b()};function E(){f.value=[]}const M=e=>{o.push({path:"/manual-marking/start-marking/index",query:{type:t.entrance==="my-marking-task"?0:6,project_id:e.project_id,subject_id:e.subject_id,task_id:e.task_id,ques_code:e.ques_code,round_id:e.round_id}})};return(e,s)=>{const p=m("form-component"),l=m("el-card"),v=m("el-text"),a=m("el-button"),N=m("table-component");return O(),X("div",ie,[W("div",pe,[_(l,null,{default:d(()=>[W("div",{ref_key:"formDivRef",ref:g},[_(p,{ref_key:"formRef",ref:V,modelValue:c.value,"onUpdate:modelValue":s[0]||(s[0]=i=>c.value=i),"form-options":q,"is-query-btn":!0,onOnchangeFn:F,onQueryDataFn:b,onResetFields:E},null,8,["modelValue","form-options"])],512)]),_:1}),_(l,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:d(()=>[_(N,{minHeight:r.value.styleOptions.minHeight,"table-options":r.value,"table-data":k.value,onOnHandleSizeChange:P,onOnHandleCurrentChange:D},{try_mark_result:d(i=>[_(v,{type:i.row.try_mark_result===2?"danger":"default"},{default:d(()=>[S(C(h(te)(i.row.try_mark_result,[{label:"未评",value:0},{label:"合格",value:1},{label:"不合格",value:2}])),1)]),_:2},1032,["type"])]),opera:d(i=>[h(R)("my-marking-task/start")||h(R)("my-trial-task/start")?(O(),Y(a,{key:0,link:"",type:"primary",disabled:i.row.round_state!==2,onClick:de=>M(i.row)},{default:d(()=>[S(C(n.entrance==="my-trial-task"?"开始试评":"开始正评"),1)]),_:2},1032,["disabled","onClick"])):Z("",!0)]),_:1},8,["minHeight","table-options","table-data"])]),_:1})])],512)}}}),ge=ee(ue,[["__scopeId","data-v-e642a504"]]);export{ge as default};
