var I=(q,z,j)=>new Promise((L,M)=>{var V=p=>{try{h(j.next(p))}catch(i){M(i)}},T=p=>{try{h(j.throw(p))}catch(i){M(i)}},h=p=>p.done?L(p.value):Promise.resolve(p.value).then(V,T);h((j=j.apply(q,z)).next())});import{d as be,l as f,P as De,r as y,j as Ce,o as g,g as E,h as b,u as F,c as S,b as D,f as ee,y as Y,w as ae,e as k,z as Fe,F as ke,p as qe,C as le,T as m,ac as we,ad as je,_ as Ve}from"./index-B63pSD2p.js";import Te from"./formItem-Cpvf-Fua.js";import{a as Ne}from"./validate-Dc6ka3px.js";import{a as te}from"./common-methods-BWkba4Bo.js";import{r,g as Oe,a as se,q as W,f as G,s as re,b as oe,m as ue,c as ce,d as ie}from"./rules-form-CST-rV3v.js";import{c as Ae,u as Be,a as Ee}from"./scoring-rules-BR2vQ7G3.js";import"./test-paper-management-DjV_45YZ.js";const ne=q=>(we("data-v-f4a7ee7d"),q=q(),je(),q),Se={class:"zf-dialog-first-box"},Le={class:"zf-dialog-second-box"},Me={class:""},Qe={class:"expand-btn-box"},Re={key:0,class:"tip-text-box"},Pe=ne(()=>k("p",null,"提示：该规则已被使用",-1)),Je=[Pe],Ue={class:"ml-[19px]"},Ye=ne(()=>k("div",{class:"line-box"},null,-1)),ze={class:"ques-template"},He={class:"w-[19px]"},Ie={key:0,class:"footer-btn"},We=be({__name:"add-scoring-rules",props:["projectList"],emits:["queryData"],setup(q,{expose:z,emit:j}){const L=j,M=q,V=f("新增评分规则"),T=f(!1),h=f(!1),p=f(!0);let i=f("01");const N=f({}),A=f([]),d=f(null),$=f({}),O=De({column:3,labelWidth:"80px",itemWidth:"186px",disabled:!1,rules:{rule_name:[{required:!0,message:"请输入规则名称",trigger:["blur","change"]},{trigger:["blur","change"],validator:(l,a,e)=>{if(a.length>30)return e(new Error("规则名称长度不能超过30！"));e()}}],project_id:[{required:!0,message:"请选择所属资格",trigger:["blur","change"]}],subject_id:[{required:!0,message:"请选择所属科目",trigger:["blur","change"]}],rule_year:[{required:!0,message:"请选择考试年份",trigger:["blur","change"]}]},fields:[{label:"规则名称",prop:"rule_name",type:"input",defaultValue:"",clearable:!0,placeholder:"请输入规则名称"},{label:"所属资格",prop:"project_id",type:"select",placeholder:"请选择所属资格",defaultValue:"",clearable:!0,optionData:()=>M.projectList},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,placeholder:"请选择所属科目",defaultValue:"",optionData:()=>A.value},{label:"考试年份",prop:"rule_year",type:"year",placeholder:"请选择考试年份",defaultValue:"",valueFormat:"YYYY",clearable:!0},{label:"备注",prop:"remark",type:"textarea",width:"485px",placeholder:"请输入备注",defaultValue:"",clearable:!0}]});f([{}]);const K=f([]),o=f([]),pe=(l,a)=>{i.value=l,T.value=!0,Oe("rule",["G"]),l==="01"?(V.value="新增评分规则",O.disabled=!1):(l==="02"||l==="03")&&(h.value=!0,N.value=a,l==="02"?(V.value="编辑评分规则",O.disabled=!1):V.value="预览评分规则",le(()=>{O.fields.map(e=>{a.hasOwnProperty(e.prop)&&(e.prop==="rule_year"?d.value.setCardData(e.prop,a[e.prop].toString()):d.value.setCardData(e.prop,a[e.prop])),l==="02"&&(a.lock_state===2?(e.prop==="project_id"||e.prop==="subject_id")&&(e.disabled=!0):(e.prop==="project_id"||e.prop==="subject_id")&&(e.disabled=!1))})}),te(a.project_id).then(e=>{A.value=e||[]}),a.rule_id&&ye(a.rule_id),l==="03"&&(O.disabled=!0))},X=()=>{p.value?p.value=!1:p.value=!0},de=(l,a)=>{l.prop==="project_id"&&(a?(te(a).then(e=>{A.value=e||[]}),d.value.getCardData("subject_id")&&(d.value.setCardData("subject_id",null),setTimeout(()=>{d.value.clearValidateFn("subject_id")},100))):(A.value=[],d.value.getCardData("subject_id")&&(d.value.setCardData("subject_id",null),setTimeout(()=>{d.value.clearValidateFn("subject_id")},100))))},fe=(l,a,e)=>{if(w(),l.label==="题型")if(!a)o.value[e].getCardData("type")&&(o.value[e].setCardData("type",null),setTimeout(()=>{o.value[e].clearValidateFn("type")},100)),o.value[e].getCardData("score")&&(o.value[e].setCardData("score",""),setTimeout(()=>{o.value[e].clearValidateFn("score")},100));else{o.value[e].getCardData("type")&&(o.value[e].setCardData("type",null),setTimeout(()=>{o.value[e].clearValidateFn("type")},100)),a==="A"?(r.value[e].fields=n(re),v(e,a)):a==="B"?(r.value[e].fields=n(oe),v(e,a)):a==="C"?(r.value[e].fields=n(ue),v(e,a)):a==="D"?(r.value[e].fields=n(ce),o.value[e].setCardData("count",1),v(e,a)):a==="E"&&(r.value[e].fields=n(ie),o.value[e].setCardData("count",1),v(e,a));let s=[];o.value.map(t=>{t.getCardData("ques_type")!==a&&s.push(t.getCardData("ques_type"))}),Z(s)}else l.label==="评分规则"&&o.value[e].getCardData("ques_type")==="C"&&(a===4||a===6?(r.value[e].fields.map(t=>{t.prop==="score"&&(t.isHidden=!1)}),r.value[e].rules.score=[{required:!0,message:"请输入指定分",trigger:["blur","change"]},{trigger:["blur","change"],validator:(t,u,c)=>{if(Ne(u))c();else return c(new Error("请输入正数（至多两位小数）！"))}}],setTimeout(()=>{o.value[e].clearValidateFn()},100)):(r.value[e].fields.map(t=>{t.prop==="score"&&(t.isHidden=!0)}),o.value[e].setCardData("score",""),r.value[e].rules.score=[]))},v=(l,a)=>{setTimeout(()=>{let e=se.filter(s=>{if(s.ques_type_code===a)return s});r.value[l].fields[1].optionData=()=>e[0].ques_mark_rule},500)},_e=()=>I(this,null,function*(){var s;w();let l=o.value.length,a=0,e=[];(s=o.value)==null||s.map(t=>{t.formValidate().then(()=>{if(e.push(t.getCardData("ques_type")),a+=1,l===a)if(r.value.length!==W.length){let u=n(G);u.id=Math.random(),r.value.push(u),r.value[r.value.length-2].fields[0].disabled=!0,Z(e)}else m.warning("已无题型可增加！")}).catch(()=>{a-=1,m.warning("请先完成上方题型的配置!")})})}),n=l=>{if(l===null||typeof l!="object")return l;const a=Array.isArray(l)?[]:{};for(let e in l)l.hasOwnProperty(e)&&(a[e]=n(l[e]));return a},Z=l=>{r.value[r.value.length-1].fields[0].optionData=()=>{let a=[];return W.map(e=>{l.indexOf(e.value)===-1&&a.push(e)}),a}},ve=l=>{if(r.value.length>=2)if(i.value==="02"&&N.value.lock_state===2&&o.value[l].getCardData("lock_state")===2)m.warning("该规则已使用，无法删除！");else{if(l!==r.value.length-1){w();const a=[];o.value.forEach((e,s)=>{s>l&&a.push(e.getAllCardData())}),r.value.splice(l,1),setTimeout(()=>{w(),a.forEach((s,t)=>{Object.keys(s).length>0?(r.value[l+t].fields.forEach(u=>{s.hasOwnProperty(u.prop)&&o.value[l+t].setCardData(u.prop,s[u.prop])}),o.value[l+t].clearValidateFn()):o.value[l+t].resetFieldsFn()});let e=[];o.value.forEach((s,t)=>{const u=o.value[t].getCardData("ques_type");e.push(s.getCardData("ques_type")),t!==o.value.length&&(r.value[t].fields[0].optionData=()=>{let c=[];const C=e.filter(_=>_!==u);return W.map(_=>{C.indexOf(_.value)===-1&&c.push(_)}),c})})},50)}else r.value.splice(l,1);r.value[r.value.length-1].fields[0].disabled=!1}else m.warning("至少存在一个题型规则！")},w=()=>{o.value=K.value.filter(l=>l!==null)},ge=()=>I(this,null,function*(){d.value.formValidate().then(()=>{var s;w();let l={},a=o.value.length,e=0;(s=o.value)==null||s.map(t=>{t.formValidate().then(()=>{e+=1;let u=t.getCardData("ques_type");if(l[u]=JSON.parse(JSON.stringify(t.getAllCardData())),se.map(c=>{var C;c.ques_type_code===u&&((C=c.ques_mark_rule)==null||C.map(_=>{t.getCardData("type")===_.type&&(l[u].comment=_.options)}))}),delete l[u].ques_type,delete l[u].lock_state,l[u].score?l[u].score=Number(l[u].score):delete l[u].score,a===e){let{project_id:c,subject_id:C,rule_name:_,rule_year:R,remark:H}=JSON.parse(JSON.stringify(d.value.getAllCardData())),B={project_id:c,subject_id:C,rule_name:_,rule_year:Number(R),remark:H,rule_dict:l};i.value==="01"?me(B):i.value==="02"&&(B.rule_id=N.value.rule_id,he(B))}}).catch(()=>{e-=1,m.warning("请先完成上方题型的配置！")})})}).catch(()=>{m.warning("请按要求填写信息！")})}),me=l=>{Ae(l).then(a=>{a.code&&a.code===200?(m.success(a.msg),L("queryData"),Q()):m.warning(a.msg)})},he=l=>{Be(l).then(a=>{a.code&&a.code===200?(m.success(a.msg),L("queryData"),Q()):m.warning(a.msg)})},ye=l=>{Ee({rule_id:l}).then(a=>{a.code&&a.code===200?le(()=>{var e;(e=a.data)==null||e.map((s,t)=>{if(t!==0){let u=n(G);u.id=Math.random(),r.value.push(u)}if(s.ques_type_code==="A")r.value[t].fields=n(re),v(t,s.ques_type_code);else if(s.ques_type_code==="B")r.value[t].fields=n(oe),v(t,s.ques_type_code);else if(s.ques_type_code==="C"){let u=n(ue);(s.mark_rule_detail.type===4||s.mark_rule_detail.type===6)&&u.forEach(c=>{c.prop==="score"&&(c.isHidden=!1)}),r.value[t].fields=u,v(t,s.ques_type_code)}else s.ques_type_code==="D"?(r.value[t].fields=n(ce),v(t,s.ques_type_code)):s.ques_type_code==="E"&&(r.value[t].fields=n(ie),v(t,s.ques_type_code));setTimeout(()=>{var u;w();for(let c in s.mark_rule_detail)o.value[t].setCardData(c,s.mark_rule_detail[c]);o.value[t].setCardData("ques_type",s.ques_type_code),((u=N.value)==null?void 0:u.lock_state)===2&&o.value[t].setCardData("lock_state",2),i.value==="03"?r.value[t].disabled=!0:r.value[t].disabled=!1},500),r.value[t].fields[0].disabled=!0}),setTimeout(()=>{h.value=!1},500)}):h.value=!1}).catch(()=>{h.value=!1})},Q=()=>{T.value=!1,N.value={},d.value.resetFieldsFn(),A.value=[],O.fields.forEach(a=>{(a.prop==="project_id"||a.prop==="subject_id")&&a.disabled&&(a.disabled=!1)}),r.value.length>1&&r.value.splice(1);let l=n(G);l.id=Math.random(),r.value[0]=n(l),w(),o.value.map(a=>{a.resetFieldsFn()})};return z({openDialog:pe}),(l,a)=>{const e=y("ArrowUpBold"),s=y("el-icon"),t=y("ArrowDownBold"),u=y("form-component"),c=y("Delete"),C=y("Plus"),_=y("el-scrollbar"),R=y("el-button"),H=y("el-dialog"),B=Ce("loading");return g(),E(H,{modelValue:T.value,"onUpdate:modelValue":a[1]||(a[1]=P=>T.value=P),title:V.value,width:"950px","close-on-click-modal":!1,"before-close":Q,"align-center":"",draggable:""},{footer:b(()=>[F(i)==="01"||F(i)==="02"?(g(),S("div",Ie,[D(R,{onClick:Q},{default:b(()=>[ee("取消")]),_:1}),D(R,{type:"primary",onClick:ge},{default:b(()=>[ee("确定")]),_:1})])):Y("",!0)]),default:b(()=>{var P;return[ae((g(),S("div",Se,[k("div",Le,[k("div",Me,[k("div",Qe,[p.value?(g(),E(s,{key:0,title:"收起",onClick:X},{default:b(()=>[D(e)]),_:1})):(g(),E(s,{key:1,title:"展开",onClick:X},{default:b(()=>[D(t)]),_:1}))]),ae(k("div",null,[((P=N.value)==null?void 0:P.lock_state)===2?(g(),S("div",Re,Je)):Y("",!0),k("div",Ue,[D(u,{ref_key:"formRef",ref:d,modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=x=>$.value=x),isQueryBtn:!1,"form-options":O,onOnchangeFn:de},null,8,["modelValue","form-options"])])],512),[[Fe,p.value]]),Ye,D(_,{"max-height":"460px",always:""},{default:b(()=>[(g(!0),S(ke,null,qe(F(r),(x,J)=>(g(),S("div",ze,[k("div",He,[F(i)==="01"||F(i)==="02"?(g(),E(s,{key:0,class:"ques-icon-box",onClick:U=>ve(J)},{default:b(()=>[D(c)]),_:2},1032,["onClick"])):Y("",!0)]),D(Te,{ref_for:!0,ref:U=>K.value[J]=U,isQueryBtn:!1,"form-options":F(r)[J],onOnchangeFn:(...U)=>fe(...U,J)},null,8,["form-options","onOnchangeFn"])]))),256)),F(i)==="01"||F(i)==="02"?(g(),E(s,{key:0,class:"ques-icon-box",onClick:_e},{default:b(()=>[D(C)]),_:1})):Y("",!0)]),_:1})])])])),[[B,h.value]])]}),_:1},8,["modelValue","title"])}}}),la=Ve(We,[["__scopeId","data-v-f4a7ee7d"]]);export{la as default};
