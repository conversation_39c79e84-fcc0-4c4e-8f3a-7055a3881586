var V=(a,f,d)=>new Promise((w,v)=>{var k=n=>{try{D(d.next(n))}catch(y){v(y)}},g=n=>{try{D(d.throw(n))}catch(y){v(y)}},D=n=>n.done?w(n.value):Promise.resolve(n.value).then(k,g);D((d=d.apply(a,f)).next())});import{d as M,aS as z,l as b,aO as F,m as L,B as O,r as m,o as x,g as S,h as r,e,b as o,t as i,f as T,w as A,c as U,F as W,p as G,z as H,ac as J,ad as K,_ as P}from"./index-B63pSD2p.js";import{a as Q}from"./score-result-DfeCiQTg.js";const u=a=>(J("data-v-4bfa7f28"),a=a(),K(),a),R=u(()=>e("div",{class:"gap dark:!bg-black eye-box"},null,-1)),X={class:"flex"},Y={class:"mr-2 flex-1"},Z=u(()=>e("h4",null,"评分信息",-1)),ee={class:"score-msg"},le={class:"column"},te=u(()=>e("span",null,"所属资格：",-1)),ae={class:"column"},se=u(()=>e("span",null,"所属科目：",-1)),oe={class:"column"},ne=u(()=>e("span",null,"场次：",-1)),ce={class:"column"},re=u(()=>e("span",null,"考生密号：",-1)),ie={class:"column"},ue=u(()=>e("span",null,"试卷总分：",-1)),de={class:"column"},_e=u(()=>e("span",null,"考生总分：",-1)),pe=u(()=>e("h4",null,"评分明细",-1)),me={style:{width:"350px"}},he=u(()=>e("h4",null,"评分记录",-1)),fe={class:"score-number"},we=M({__name:"detail",props:z({title:{type:String,default:"评分详情"},rowDetailData:{type:Object,default:()=>({})},detailId:{type:String,default:""}},{drawerVisible:{},drawerVisibleModifiers:{}}),emits:["update:drawerVisible"],setup(a){const f=b("60%"),d=b(!1),w=b([]),v=F(a,"drawerVisible"),k=a;b({});const g=b([]),D=L(()=>{const l=[];let t=0;return w.value.forEach((c,s)=>{s==0?(l.push(1),t=0):c.ques_type_name==w.value[s-1].ques_type_name?(l[t]+=1,l.push(0)):(l.push(1),t=s)}),l}),n=b(-1),y=({rowIndex:l})=>n.value==l?"highlight-row":"",I=()=>V(this,null,function*(){const l={student_subject_grade_id:k.detailId},t=yield Q(l);w.value=t.data});O(()=>k.detailId,l=>{l&&I()},{immediate:!0});const j=l=>{var s,h;const t=l.row,c=[];(s=t.repeat_mark)!=null&&s.length&&(t.repeat_mark.forEach(_=>{_.des=`复评（${_.round_count}轮）`}),c.push(...t.repeat_mark)),(h=t.official_mark)!=null&&h.length&&(t.official_mark.forEach(_=>{_.des="正评（1轮）",_.type&&(_.des=_.type)}),c.push(...t.official_mark)),g.value=c,n.value=l.$index,d.value=!0,d.value?f.value="calc(60% + 350px)":f.value="calc(60%)"},q=()=>{v.value=!1,d.value=!1,n.value=-1,f.value="calc(60%)"},C=({row:l,column:t,rowIndex:c,columnIndex:s})=>{if(s===0){const h=D.value[c];return h?{rowspan:h,colspan:1}:{rowspan:0,colspan:0}}return{rowspan:1,colspan:1}};return(l,t)=>{const c=m("el-card"),s=m("el-table-column"),h=m("el-button"),_=m("el-table"),B=m("el-timeline-item"),N=m("el-timeline"),$=m("el-scrollbar"),E=m("el-drawer");return x(),S(E,{modelValue:v.value,"onUpdate:modelValue":t[0]||(t[0]=p=>v.value=p),title:a.title,size:f.value,"before-close":q,"destroy-on-close":"","close-on-click-modal":!1,class:"score-result-detail-drawer-container"},{default:r(()=>[R,e("div",X,[e("section",Y,[o(c,null,{default:r(()=>[Z,e("ul",ee,[e("li",null,[e("div",le,[te,e("p",null,i(a.rowDetailData.project_name),1)]),e("div",ae,[se,e("p",null,i(a.rowDetailData.subject_name),1)])]),e("li",null,[e("div",oe,[ne,e("p",null,i(a.rowDetailData.exam_session),1)]),e("div",ce,[re,e("p",null,i(a.rowDetailData.stu_secret_num),1)])]),e("li",null,[e("div",ie,[ue,e("p",null,i(a.rowDetailData.subject_total_score),1)]),e("div",de,[_e,e("p",null,i(a.rowDetailData.score),1)])])])]),_:1}),o(c,null,{default:r(()=>[pe,o(_,{data:w.value,border:"",stripe:"","scrollbar-always-on":"","row-class-name":y,"span-method":C,style:{width:"calc(60vw - 40px)","margin-top":"20px",height:"calc(100vh - 360px)"}},{default:r(()=>[o(s,{prop:"ques_type_name",label:"题型",width:"180",align:"center"}),o(s,{prop:"ques_name",label:"试题名称",align:"center"}),o(s,{prop:"ques_code",label:"试题编号",align:"center"}),o(s,{prop:"ques_score",label:"试题分数",align:"center",width:"100"}),o(s,{prop:"stu_grade",label:"考生得分",align:"center",width:"100"}),o(s,{prop:"count",label:"评分次数",align:"center",width:"100"},{default:r(p=>[o(h,{type:"primary",link:"",onClick:ve=>j(p)},{default:r(()=>[T(i(p.row.count),1)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),A(e("section",me,[o(c,null,{default:r(()=>[o($,{height:"calc(100vh - 110px - 2px)",class:"bg dark:!bg-black eye-box"},{default:r(()=>[he,e("span",fe,"评分次数："+i(g.value.length),1),o(N,{class:"w-[300px] pl-2"},{default:r(()=>[(x(!0),U(W,null,G(g.value,p=>(x(),S(B,{timestamp:`${p.mark_time}  ${p.username}`,placement:"top",color:"var(--el-color-primary)"},{default:r(()=>[e("p",null,[e("span",null,i(p.des),1),e("span",null,"评分："+i(p.score)+"分",1)])]),_:2},1032,["timestamp"]))),256))]),_:1})]),_:1})]),_:1})],512),[[H,d.value]])])]),_:1},8,["modelValue","title","size"])}}}),ye=P(we,[["__scopeId","data-v-4bfa7f28"]]);export{ye as default};
