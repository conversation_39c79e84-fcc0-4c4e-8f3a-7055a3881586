import{f as b}from"./fabric-G9pQvVJl.js";import{d as T,l as m,n as U,ao as z,B as D,C as P,r as H,o as w,c as y,F as $,p as F,U as N,b as q,h as X,g as Z,y as Y,e as E,q as M,ac as J,ad as K,_ as G}from"./index-B63pSD2p.js";const Q={width:24,height:24,body:'<path fill="currentColor" d="m15.728 9.576l-1.414-1.414L5 17.476v1.414h1.414l9.314-9.314Zm1.414-1.414l1.414-1.414l-1.414-1.414l-1.414 1.414l1.414 1.414Zm-9.9 12.728H3v-4.243L16.435 3.212a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414L7.243 20.89Z"/>'},ee=Q,te={width:24,height:24,body:'<path fill="currentColor" d="M13 6v15h-2V6H5V4h14v2h-6Z"/>'},ae=te,le={width:24,height:24,body:'<path fill="currentColor" d="M3 4h18a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Zm1 2v12h16V6H4Z"/>'},ne=le,ce={width:24,height:24,body:'<path fill="currentColor" d="M21 19v2h-2v-2h2Zm-4 0v2h-2v-2h2Zm-4 0v2h-2v-2h2Zm-4 0v2H7v-2h2Zm-4 0v2H3v-2h2Zm16-4v2h-2v-2h2ZM5 15v2H3v-2h2Zm0-4v2H3v-2h2Zm11-8a5.002 5.002 0 0 1 4.995 4.783L21 8v5h-2V8a3.01 3.01 0 0 0-2.824-2.995L16 5h-5V3h5ZM5 7v2H3V7h2Zm0-4v2H3V3h2Zm4 0v2H7V3h2Z"/>'},oe=ce,ie={width:24,height:24,body:'<path fill="currentColor" d="M5.463 4.433A9.961 9.961 0 0 1 12 2c5.523 0 10 4.477 10 10c0 2.136-.67 4.116-1.81 5.74L17 12h3A8 8 0 0 0 6.46 6.228l-.997-1.795Zm13.074 15.134A9.961 9.961 0 0 1 12 22C6.477 22 2 17.523 2 12c0-2.136.67-4.116 1.81-5.74L7 12H4a8 8 0 0 0 13.54 5.772l.997 1.795Z"/>'},se=ie,re=(c,s,l)=>{let i=c,r=s,e=1;switch(l.mode){case"contain":if(l.containerWidth&&l.containerHeight){const n=l.containerWidth/c,h=l.containerHeight/s;e=Math.min(n,h)}else if(l.maxWidth&&l.maxHeight){const n=l.maxWidth/c,h=l.maxHeight/s;e=Math.min(n,h)}break;case"cover":if(l.containerWidth&&l.containerHeight){const n=l.containerWidth/c,h=l.containerHeight/s;e=Math.max(n,h)}break;case"width":l.maxWidth&&(e=l.maxWidth/c);break;case"height":l.maxHeight&&(e=l.maxHeight/s);break}return i=c*e,r=s*e,l.maxWidth&&i>l.maxWidth&&(e=l.maxWidth/c,i=l.maxWidth,r=s*e),l.maxHeight&&r>l.maxHeight&&(e=l.maxHeight/s,r=l.maxHeight,i=c*e),{width:Math.round(i),height:Math.round(r),ratio:e}},ve=c=>(J("data-v-286036e8"),c=c(),K(),c),ue={key:0,class:"fabric-tool-box"},de=["title"],he=ve(()=>E("canvas",{id:"annotation-canvas",class:"annotation-canvas"},null,-1)),fe=[he],me=T({__name:"annotation-view",props:{imageUrl:{default:""},imageStyle:{default:null},canvasWidth:{default:800},isEdit:{type:Boolean,default:!1},annotation:{default:null}},setup(c,{expose:s}){const l=c,i=m("select"),r=m("#e74c3c"),e=m(null),n=m(null),h=m(1),O=m(0),A=m([{label:"选择",value:"select",icon:oe,isActive:!1},{label:"直线",value:"line",icon:ee,isActive:!1},{label:"文字",value:"text",icon:ae,isActive:!1},{label:"矩形",value:"rect",icon:ne,isActive:!1},{label:"清除",value:"refresh",icon:se,isActive:!1}]);U(()=>{}),z(()=>{window.removeEventListener("resize",x),window.removeEventListener("keydown",_),e.value&&e.value.dispose()}),D(()=>l.imageUrl,a=>{a&&P(()=>{L()})},{immediate:!0});const L=()=>{e.value=new b.fabric.Canvas("annotation-canvas",{selection:!0,preserveObjectStacking:!0,backgroundColor:"#f8f9fa"}),B(),x(),window.addEventListener("resize",x),e.value.on("mouse:down",W),e.value.on("mouse:move",j),e.value.on("mouse:up",R),e.value.on("object:modified",u),e.value.on("object:added",u),e.value.on("object:removed",u),e.value.on("mouse:dblclick",V),window.addEventListener("keydown",_)},S=a=>{e.value&&b.fabric.Image.fromURL(a,t=>{n.value&&e.value.remove(n.value),n.value=t,re(t.width,t.height,{mode:"height",containerWidth:l.canvasWidth,containerHeight:500}),t.scaleToWidth(e.value.width*.9),t.set({left:e.value.width/2,top:e.value.height/2,originX:"center",originY:"center",selectable:!1,evented:!1}),e.value.add(t),e.value.sendToBack(t),h.value=t.scaleX,O.value=t.angle||0,e.value.renderAll()},{crossOrigin:"anonymous"})},C=a=>{A.value.forEach(t=>{t.value===a?t.isActive=!0:t.isActive=!1}),i.value=a,e.value.isDrawingMode=!1,e.value.selection=a==="select",a!=="select"&&e.value.discardActiveObject()};let g=!1,v=null,o=null;const W=a=>{if(i.value==="select"||!e.value)return;const t=e.value.getPointer(a.e);if(v=t,g=!0,i.value==="text"){const d=new b.fabric.IText("双击编辑",{left:t.x,top:t.y,fontSize:20,fill:r.value,fontFamily:"Arial",borderColor:r.value,hasControls:!0});e.value.add(d),C("select"),e.value.setActiveObject(d),d.enterEditing(),u()}},j=a=>{if(!g||!v)return;const t=e.value.getPointer(a.e);i.value==="line"?(o&&e.value.remove(o),o=new b.fabric.Line([v.x,v.y,t.x,t.y],{stroke:r.value,strokeWidth:2,selectable:!1}),e.value.add(o)):i.value==="rect"&&(o&&e.value.remove(o),o=new b.fabric.Rect({left:v.x,top:v.y,width:t.x-v.x,height:t.y-v.y,fill:"transparent",stroke:r.value,strokeWidth:2,selectable:!1}),e.value.add(o))},R=()=>{g&&o&&(o.set({selectable:!0,hasControls:!0}),e.value.setActiveObject(o),u()),g=!1,v=null,o=null},V=a=>{const t=a.target;t&&t.isType("i-text")&&(t.enterEditing(),t.hiddenTextarea.focus())},_=a=>{e.value&&a.key==="Delete"&&e.value.getActiveObjects().length>0&&(e.value.getActiveObjects().forEach(t=>{t!==n.value&&e.value.remove(t)}),e.value.discardActiveObject(),e.value.requestRenderAll(),u())},u=()=>e.value?e.value.getObjects().filter(t=>t!==n.value).map(t=>t.toJSON()):void 0,B=()=>{const a=[];try{if(l.imageUrl&&S(l.imageUrl),!e.value)return;e.value.getObjects().forEach(t=>{t!==n.value&&e.value.remove(t)}),a==null||a.forEach(t=>{b.fabric.util.enlivenObjects([t],d=>{d.forEach(k=>{e.value.add(k)}),e.value.renderAll()})}),e.value.renderAll()}catch(t){console.error("Failed to load annotations:",t)}},I=()=>u(),p=()=>{e.value&&(e.value.getObjects().forEach(a=>{a!==n.value&&e.value.remove(a)}),e.value.discardActiveObject(),e.value.renderAll(),u())},x=()=>{const a=document.querySelector(".annotation-canvas-container");!a||!e.value||(e.value.setWidth(a.clientWidth),e.value.setHeight(a.clientHeight-4),n.value&&(n.value.set({left:e.value.width/2,top:e.value.height/2,originX:"center",originY:"center",selectable:!1,evented:!1}),e.value.renderAll()))};return s({resizeCanvas:x,getAnnotations:I,clearCanvas:p}),(a,t)=>{const d=H("iconify-icon-offline"),k=H("el-text");return w(),y("div",{class:"flex",style:M({width:a.canvasWidth+"px"})},[a.isEdit?(w(),y("div",ue,[(w(!0),y($,null,F(A.value,f=>(w(),y("i",{title:f.label,class:N(f.isActive?"active":"")},[q(k,{size:"large"},{default:X(()=>[f.value==="refresh"?(w(),Z(d,{key:0,icon:f.icon,onClick:p},null,8,["icon"])):(w(),Z(d,{key:1,icon:f.icon,onClick:be=>C(f.value,f)},null,8,["icon","onClick"]))]),_:2},1024)],10,de))),256))])):Y("",!0),E("div",{class:"annotation-canvas-container",style:M(a.imageStyle)},fe,4)],4)}}}),xe=G(me,[["__scopeId","data-v-286036e8"]]);export{xe as default};
