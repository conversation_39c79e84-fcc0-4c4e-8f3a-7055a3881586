import M from"./project-monitor-BDByWCJn.js";import x from"./question-monitor-DEIylrrj.js";import g from"./group-monitor-BqJHRO1_.js";import h from"./examiner-monitor-COAj4qmZ.js";import{d as k,l as p,m as y,aC as C,n as S,r as c,o as t,c as _,e as V,g as u,h as d,b as w,F as B,p as j,s as q,_ as N}from"./index-B63pSD2p.js";import"./formal-monitor-BXl8VaF0.js";import"./calculateTableHeight-BjE6OFD1.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./downloadRequest-CdE2PBjt.js";const z={key:0,class:"zf-first-box"},E={class:"zf-second-box"},F=k({name:"formal-monitor",__name:"index",setup(I){const a=p(""),b=y(()=>{switch(a.value){case"projectMonitor":return M;case"questionMonitor":return x;case"groupMonitor":return g;case"examinerMonitor":return h;default:return null}}),f=C(),{roles:r}=f;function l(o,s){const i=new Set(s);return[...new Set(o)].filter(m=>i.has(m))}const e=p([]);return S(()=>{var o;l(["1","7","8"],r).length>0?e.value=[{label:"资格监控",name:"projectMonitor"},{label:"题组监控",name:"questionMonitor"},{label:"小组监控",name:"groupMonitor"},{label:"评阅员监控",name:"examinerMonitor"}]:l(["5"],r).length>0?e.value=[{label:"题组监控",name:"questionMonitor"},{label:"小组监控",name:"groupMonitor"},{label:"评阅员监控",name:"examinerMonitor"}]:l(["4"],r).length>0?e.value=[{label:"小组监控",name:"groupMonitor"},{label:"评阅员监控",name:"examinerMonitor"}]:l(["3"],r).length>0&&(e.value=[{label:"评阅员监控",name:"examinerMonitor"}]),a.value=(o=e.value[0])==null?void 0:o.name}),(o,s)=>{const i=c("el-tab-pane"),m=c("el-tabs"),v=c("el-empty");return e.value.length!==0?(t(),_("div",z,[V("div",E,[(t(),u(q(b.value),null,{tabs:d(()=>[w(m,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=n=>a.value=n),class:"monitor-container"},{default:d(()=>[(t(!0),_(B,null,j(e.value,n=>(t(),u(i,{key:n.name,label:n.label,name:n.name},null,8,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1}))])])):(t(),u(v,{key:1,class:"dark:!bg-black eye-box",description:"您没有权限，请联系管理员！"}))}}}),O=N(F,[["__scopeId","data-v-c5e65f2b"]]);export{O as default};
