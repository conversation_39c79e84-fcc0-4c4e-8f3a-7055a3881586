var Z=Object.defineProperty,ee=Object.defineProperties;var te=Object.getOwnPropertyDescriptors;var T=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,oe=Object.prototype.propertyIsEnumerable;var U=(u,l,r)=>l in u?Z(u,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):u[l]=r,$=(u,l)=>{for(var r in l||(l={}))ae.call(l,r)&&U(u,r,l[r]);if(T)for(var r of T(l))oe.call(l,r)&&U(u,r,l[r]);return u},B=(u,l)=>ee(u,te(l));import{g as E,u as le,s as ne}from"./re-marking-BVznCWni.js";import{c as re,a as se}from"./calculateTableHeight-BjE6OFD1.js";import{p as ie,g as ue,a as ce}from"./common-methods-BWkba4Bo.js";import{d as de,i as pe,l as i,P as _e,n as me,ao as fe,T as d,r as p,o as f,c as L,e as _,b as n,h as o,f as v,g as k,t as ge,y as ve,_ as he}from"./index-B63pSD2p.js";import{d as be}from"./downloadRequest-CdE2PBjt.js";import{r as ke,c as ye,t as xe,a as we}from"./management-B6Jg-NXx.js";import"./test-paper-management-DjV_45YZ.js";import"./handleMethod-BIjqYEft.js";const Ve={class:"zf-first-box"},Ce={class:"zf-second-box"},Se={class:"zf-flex-end"},Oe={class:"task-btn-box"},De={class:"task-btn"},ze={class:"footer-btn"},je={class:"extract-img"},Re=["src"],Pe={class:"extract-progress"},Fe={key:0,style:{"text-align":"center","margin-top":"6px"}},q=5,Ne=de({name:"management-check",__name:"index",setup(u){pe();const l=i(null),r=i(null),y=i({}),A=_e({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>ie.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>V.value},{label:"验收状态",prop:"task_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择验收状态",optionData:()=>ke},{label:"验收结果",prop:"verify_result",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择验收结果",optionData:()=>ye}]}),V=i([]),C=i([]),m=i({field:xe,styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}});let O=null;me(()=>{ue(),re(O,l.value,m.value),h()}),fe(()=>{se(O)});const M=(a,e)=>{a.prop==="project_id"&&(V.value=[],y.value.subject_id&&(y.value.subject_id=null),e&&ce(e).then(g=>{V.value=g||[]}))},h=()=>{let{currentPage:a,pageSize:e}=m.value.pageOptions,g=B($({current_page:a,page_size:e},y.value),{task_type:q});E(g).then(s=>{s.code&&s.code===200?(we(s.data.repeat_tasks),C.value=s.data.repeat_tasks,m.value.pageOptions.total=s.data.total):d.error(s.msg)})},W=a=>{m.value.pageOptions.currentPage=1,m.value.pageOptions.pageSize=a,h()},G=a=>{m.value.pageOptions.currentPage=a,h()},x=i(!1),b=i({}),c=i(""),D=a=>{b.value=a,c.value=b.value.score_threshold?b.value.score_threshold:null,x.value=!0},I=()=>{if(c.value===null||c.value===void 0||c.value===""){d.warning("请设置分差阈值");return}if(c.value===0){d.warning("分差阈值不能小于0");return}le({repeat_task_id:b.value.repeat_task_id,score_threshold:c.value}).then(a=>{a.code&&a.code==200?(x.value=!1,d.success(a.msg),h()):d.error(a.msg)})},z=i(!1),j=i(0),R=a=>{ne({repeat_task_id:a.repeat_task_id}).then(e=>{e.code&&e.code==200?(d.warning("抽取成功"),h()):d.error(e.msg)})},Q=()=>{},J=()=>{if(C.value.length==0){d.warning("暂无可下载的数据");return}let a="";E({current_page:1,page_size:1e3,task_type:q}).then(e=>{e.code&&e.code===200?e.data.repeat_tasks.filter(s=>!s.score_threshold).length==0?be("post","/v1/repeat_mark/export_manage_acceptance_excel","",{},a,"xlsx"):d.warning("分差阈值不能未空，请进行设置"):d.error(e.msg)})};return(a,e)=>{const g=p("form-component"),s=p("el-card"),w=p("el-button"),K=p("Auth"),P=p("el-progress"),F=p("el-text"),X=p("table-component"),N=p("el-input-number"),Y=p("el-form-item"),H=p("el-dialog");return f(),L("div",Ve,[_("div",Ce,[n(s,null,{default:o(()=>[_("div",{ref_key:"formDivRef",ref:l},[n(g,{ref_key:"formRef",ref:r,modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=t=>y.value=t),"form-options":A,"is-query-btn":!0,onOnchangeFn:M,onQueryDataFn:h},null,8,["modelValue","form-options"])],512)]),_:1}),n(s,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:o(()=>[_("div",Se,[n(K,{value:"management-check/downloadTable"},{default:o(()=>[n(w,{type:"primary",onClick:e[1]||(e[1]=t=>J())},{default:o(()=>[v("下载验收表")]),_:1})]),_:1})]),n(X,{minHeight:m.value.styleOptions.minHeight,"table-options":m.value,"table-data":C.value,onOnHandleSizeChange:W,onOnHandleCurrentChange:G},{check_progress:o(t=>[n(P,{percentage:t.row.percent},null,8,["percentage"])]),score_threshold:o(t=>[t.row.score_threshold?(f(),k(F,{key:1,style:{cursor:"pointer"},type:"primary",onClick:S=>D(t.row)},{default:o(()=>[v(ge(t.row.score_threshold),1)]),_:2},1032,["onClick"])):(f(),k(F,{key:0,style:{cursor:"pointer"},type:"primary",onClick:S=>D(t.row)},{default:o(()=>[v("设置")]),_:2},1032,["onClick"]))]),operation:o(t=>[_("div",Oe,[_("span",De,[t.row.task_state==1?(f(),k(w,{key:0,link:"",type:"primary",onClick:S=>R(t.row)},{default:o(()=>[v("抽取")]),_:2},1032,["onClick"])):(f(),k(w,{key:1,link:"",type:"primary",disabled:"",onClick:S=>R(t.row)},{default:o(()=>[v("抽取")]),_:2},1032,["onClick"]))])])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),n(H,{title:"设置分差阈值",width:"300px",draggable:"","close-on-click-modal":"","align-center":"",modelValue:x.value,"onUpdate:modelValue":e[5]||(e[5]=t=>x.value=t)},{footer:o(()=>[_("div",ze,[n(w,{onClick:e[4]||(e[4]=t=>x.value=!1)},{default:o(()=>[v("取消")]),_:1}),n(w,{type:"primary",onClick:I},{default:o(()=>[v("保存")]),_:1})])]),default:o(()=>[n(Y,{label:"分差阈值"},{default:o(()=>[b.value.subject_total_score?(f(),k(N,{key:0,min:0,max:Number(b.value.subject_total_score),modelValue:c.value,"onUpdate:modelValue":e[2]||(e[2]=t=>c.value=t)},null,8,["max","modelValue"])):(f(),k(N,{key:1,min:0,modelValue:c.value,"onUpdate:modelValue":e[3]||(e[3]=t=>c.value=t)},null,8,["modelValue"]))]),_:1})]),_:1},8,["modelValue"]),n(H,{title:"抽取验收试卷",width:"400px",draggable:"","close-on-click-modal":"","align-center":"",modelValue:z.value,"onUpdate:modelValue":e[6]||(e[6]=t=>z.value=t),onBeforeClose:Q},{default:o(()=>[_("div",je,[_("img",{src:a.loadingGif,style:{height:"200px"}},null,8,Re)]),_("div",Pe,[n(P,{style:{"marign-bottom":"60px"},"stroke-width":"10",percentage:j.value},null,8,["percentage"]),j.value>0?(f(),L("div",Fe," 正在抽取验收试卷... ")):ve("",!0)])]),_:1},8,["modelValue"])])}}}),Me=he(Ne,[["__scopeId","data-v-f8663e8d"]]);export{Me as default};
