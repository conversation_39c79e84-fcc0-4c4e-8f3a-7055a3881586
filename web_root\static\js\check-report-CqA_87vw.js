import{d as k,l as v,r as n,o as x,g as w,h as e,e as o,b as l,f as t,ac as y,ad as C,_ as V}from"./index-B63pSD2p.js";const c=s=>(y("data-v-4f4e9ea5"),s=s(),C(),s),T=c(()=>o("div",{class:"title"},"软件测评师考试阅卷报告",-1)),I=c(()=>o("div",{class:"tableTitle dark:!bg-black eye-box"},"资格信息",-1)),S=c(()=>o("div",{class:"tableTitle dark:!bg-black eye-box"},"试卷得分情况",-1)),B=c(()=>o("div",{class:"tableTitle dark:!bg-black eye-box"},"验收情况",-1)),N=c(()=>o("div",{class:"tableTitle dark:!bg-black eye-box"},"验收结论",-1)),D=c(()=>o("div",{class:"result-msg"},"验收通过",-1)),F=c(()=>o("div",{class:"remark"},"日期：2025-08-18   签名：",-1)),R={class:"footer-btn"},U=k({__name:"check-report",setup(s,{expose:b}){const d=v(!1),p=()=>{d.value=!0},r=()=>{d.value=!1};return b({openDialog:p}),(f,_)=>{const a=n("el-descriptions-item"),i=n("el-descriptions"),g=n("el-scrollbar"),u=n("el-button"),m=n("el-dialog");return x(),w(m,{title:"验收报告",modelValue:d.value,"onUpdate:modelValue":_[0]||(_[0]=h=>d.value=h),width:"40%","align-center":"","close-on-click-modal":!1,"before-close":r,draggable:""},{footer:e(()=>[o("div",R,[l(u,{onClick:r},{default:e(()=>[t("取消")]),_:1}),l(u,{type:"primary",onClick:f.confirmFn},{default:e(()=>[t("下载")]),_:1},8,["onClick"])])]),default:e(()=>[l(g,{always:"",class:"check-report-div",style:{height:"70vh"}},{default:e(()=>[T,I,l(i,{direction:"column",column:2,border:""},{default:e(()=>[l(a,{label:"科目",align:"center",width:"0px"},{default:e(()=>[t("系统架构分析师（案例分析）")]),_:1}),l(a,{label:"试卷密号",align:"center",width:"0px"},{default:e(()=>[t("567890-09876")]),_:1}),l(a,{label:"参考考生",align:"center"},{default:e(()=>[t("30876")]),_:1}),l(a,{label:"参与评阅员",align:"center"},{default:e(()=>[t("12")]),_:1})]),_:1}),S,l(i,{direction:"column",column:2,border:""},{default:e(()=>[l(a,{label:"试卷总分",align:"center",width:"0px"},{default:e(()=>[t("75")]),_:1}),l(a,{label:"平均分",align:"center",width:"0px"},{default:e(()=>[t("46")]),_:1}),l(a,{label:"最高分",align:"center"},{default:e(()=>[t("75")]),_:1}),l(a,{label:"最低分",align:"center"},{default:e(()=>[t("0")]),_:1}),l(a,{label:"满分率",align:"center"},{default:e(()=>[t("3%")]),_:1}),l(a,{label:"满分人数",align:"center"},{default:e(()=>[t("926")]),_:1}),l(a,{label:"零分率",align:"center"},{default:e(()=>[t("1%")]),_:1}),l(a,{label:"零分人数",align:"center"},{default:e(()=>[t("308")]),_:1})]),_:1}),B,l(i,{direction:"column",column:2,border:""},{default:e(()=>[l(a,{label:"验收作答个数",align:"center",width:"0px"},{default:e(()=>[t("200")]),_:1}),l(a,{label:"通过率",align:"center",width:"0px"},{default:e(()=>[t("98%")]),_:1})]),_:1}),N,D,F]),_:1})]),_:1},8,["modelValue"])}}}),q=V(U,[["__scopeId","data-v-4f4e9ea5"]]);export{q as default};
