const s=t=>/^[0-9]*$/.test(t),r=t=>/^[1-9][0-9]*$/.test(t),n=t=>/^([1-9][0-9]{0,1}|100)$/.test(t),o=t=>/^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d?(\.\d{1,2})?|100)$/.test(t),d=t=>/^(0\.(0[1-9]|[1-9]\d?)|[1-9]\d*(\.)?(\d)?(\d{1})?)$/.test(t),c=t=>/^[+]?((0(\.\d+)?)|([1-9]\d*(\.\d+)?))$/.test(t),g=t=>/^\d*(\.)?(\d)?(\d{1})?$/.test(t),u=t=>/^-?\d+(\.\d+)?$/.test(t),a=t=>/^-?\d+(\.)?(\d)?(\d{1})?$/.test(t),$=t=>/^\/.*$/.test(t),T=t=>/^[1][3456789]\d{9}$/.test(t),b=t=>/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t),m=t=>/^[\u4e00-\u9fa5]*$/.test(t);export{m as C,d as a,n as b,g as c,u as d,r as e,a as f,T as g,s as h,b as i,o as n,c as p,$ as s};
