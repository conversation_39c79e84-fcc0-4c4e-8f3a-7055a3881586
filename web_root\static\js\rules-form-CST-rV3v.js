import{g as n}from"./scoring-rules-BR2vQ7G3.js";import{l as b,P as e,T as f}from"./index-B63pSD2p.js";const m=b([{id:0,column:3,labelWidth:"80px",itemWidth:"186px",rules:{ques_type:[{required:!0,message:"请选择题型",trigger:["blur","change"]}],type:[{required:!0,message:"请选择评分规则",trigger:["change"]}],count:[{required:!0,message:"评分轮次不能为空",type:"number",trigger:["blur","change"]}]},fields:[{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",width:"485px",clearable:!0,disabled:!1,optionData:()=>[]}]}]),_=e({column:3,labelWidth:"80px",itemWidth:"186px",rules:{ques_type:[{required:!0,message:"请选择题型",trigger:["blur","change"]}],type:[{required:!0,message:"请选择评分规则",trigger:["change"]}],count:[{required:!0,message:"评分轮次不能为空",type:"number",trigger:["blur","change"]}]},fields:[{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,width:"485px",optionData:()=>[]}]});let l=e([]);const q=e([{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,width:"485px",optionData:()=>[]},{label:"是否可删",prop:"lock_state",type:"select",placeholder:"请选择是否可删",defaultValue:1,disabled:!0,isHidden:!0,optionData:()=>[{value:1,label:"是"},{value:2,label:"否"}]}]),V=e([{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,width:"485px",optionData:()=>[]},{label:"是否可删",prop:"lock_state",type:"select",placeholder:"请选择是否可删",defaultValue:1,disabled:!0,isHidden:!0,optionData:()=>[{value:1,label:"是"},{value:2,label:"否"}]}]),D=e([{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>[]},{label:"指定分",prop:"score",type:"input",clearable:!0,isHidden:!0,placeholder:"请输入指定分",defaultValue:""},{label:"是否可删",prop:"lock_state",type:"select",placeholder:"请选择是否可删",defaultValue:1,disabled:!0,isHidden:!0,optionData:()=>[{value:1,label:"是"},{value:2,label:"否"}]}]),v=e([{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>[]},{label:"评分轮次",prop:"count",type:"inputNumber",defaultValue:1,min:1,max:5,disabled:!1},{label:"是否可删",prop:"lock_state",type:"select",placeholder:"请选择是否可删",defaultValue:1,disabled:!0,isHidden:!0,optionData:()=>[{value:1,label:"是"},{value:2,label:"否"}]}]),x=e([{label:"题型",prop:"ques_type",type:"select",placeholder:"请选择题型",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>l},{label:"评分规则",prop:"type",type:"select",placeholder:"请选择评分规则",defaultValue:"",clearable:!0,disabled:!1,optionData:()=>[]},{label:"评分轮次",prop:"count",type:"inputNumber",defaultValue:1,min:1,max:5,disabled:!1},{label:"是否可删",prop:"lock_state",type:"select",placeholder:"请选择是否可删",defaultValue:1,disabled:!0,isHidden:!0,optionData:()=>[{value:1,label:"是"},{value:2,label:"否"}]}]);let y=e([]);const F=(i,c=[])=>{let u={page_size:-1};u.exclude_type_code_list=c,n(u).then(a=>{var p,s;if(a.code&&a.code===200){let d=[];(s=(p=a.data)==null?void 0:p.data)==null||s.forEach(t=>{var o;(!i||t.ques_type_code!=="F")&&(d.push({label:t.ques_type_name,value:t.ques_type_code}),(o=t.ques_mark_rule)==null||o.forEach(r=>{r.label=r.comment,r.value=r.type}))}),y=a.data.data,l=d}else f.warning(a.msg)})};export{y as a,V as b,v as c,x as d,_ as f,F as g,D as m,l as q,m as r,q as s};
