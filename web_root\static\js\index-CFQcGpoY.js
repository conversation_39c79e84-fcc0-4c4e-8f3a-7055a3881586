var T=(M,f,C)=>new Promise(($,h)=>{var I=p=>{try{l(C.next(p))}catch(e){h(e)}},F=p=>{try{l(C.throw(p))}catch(e){h(e)}},l=p=>p.done?$(p.value):Promise.resolve(p.value).then(I,F);l((C=C.apply(M,f)).next())});import Fe from"./task-progress-Dn93tYvE.js";import Me from"./test-question-CNvwcOEo.js";import Ce from"./historical-record-KlXyO7C0.js";import{M as Ie}from"./index-P2PNQqmI.js";import{V as De}from"./vue-drag-resize-CJIVu41Q.js";import{l as He,d as ze,b as Re,c as Pe,m as je}from"./manual-B17ad5iK.js";import{d as Ne,az as Qe,i as Oe,l as d,B as $e,n as Be,C as ve,T as b,r as me,o as k,c as w,e as r,q as Ae,b as y,u as R,g as Ee,h as x,t as U,y as P,f as j,U as Se,ac as Te,ad as Ge,_ as Le}from"./index-B63pSD2p.js";import"./question-info-CiM3dtIw.js";import"./handleImages-D-nd439N.js";import"./op-mark-step-DW83lcNi.js";import"./historical-ques-ov7VZ4qN.js";import"./validate-Dc6ka3px.js";const fe=M=>(Te("data-v-c60422f2"),M=M(),Ge(),M),Ve={class:"zf-first-box"},We={class:"zf-second-box"},Ue={class:"test-ques-card"},Xe={class:"mark-all-box"},Ye={class:"flex flex-column"},Je={class:"mr-[10px] mt-[10px]"},Ke={key:0,class:"secret-box"},Ze={key:1,class:"secret-box"},ea={class:"flex"},aa={class:"ques-detail-box"},sa=fe(()=>r("div",null,[r("span",{class:"history-text dark:bg-black eye-box"},"历史记录")],-1)),ta=[sa],la={key:0,class:"op-mark-show-box"},ua={class:"op-mark-content"},na={key:0},oa={key:1,class:"op-score"},ia=fe(()=>r("span",null,"评分总分：",-1)),ra={class:"op-score-value"},_a={key:0,class:"drag-resize-box"},da={class:"manual-quality-box dark:!bg-black"},ca={key:0},va={key:1},ma=Ne({name:"manual",__name:"index",setup(M){const f=Qe(),C=Oe(),$=d(null),h=d(null),I=d(null),F=d(null),l=d(null),p=["/manual-marking/my-marking-task/index","/manual-marking/my-arbitration-task/index","/manual-marking/my-quality-task/index"],e=d({}),D=d([]),B=d(!1),A=d(0),H=d(null),o=d({}),X=window.innerWidth,Y=window.innerHeight,J=d(X-446),K=d(Y-330),Z=d(!1),G=d(!1),L=d([]),ee=d(640);let ae=null;$e(()=>l.value,a=>{a&&a.focusInput()}),Be(()=>{Object.keys(f.query).length>0&&se(),ae=new ResizeObserver(a=>{a.forEach(s=>{F.value&&(ee.value=window.innerHeight-F.value.offsetHeight-200)})}),F.value&&ae.observe(F.value)});const pe=()=>{C.push(f.query.path)},se=()=>{const{m_read_task_id:a,group_id:s,paper_id:u,ques_code:_,project_id:m,subject_id:c,ques_type_code:v,ques_id:n}=f.query;He({m_read_task_id:a,group_id:s,paper_id:u,ques_code:_,project_id:m,subject_id:c,ques_type_code:v,ques_id:n}).then(t=>{var g,Q,O,ie,re,_e,de;if(t.code&&t.code===200){A.value=0,D.value=(g=t.data)==null?void 0:g.data;let ce=0;for(const q of D.value)q.ques_type_code==="G"&&(q.opMarkScore=null,L.value.push({ques_code:_,op_file:q.op_file,stu_secret_num:q.stu_secret_num,downloadIndex:ce}),ce++);e.value=(O=(Q=t.data)==null?void 0:Q.data[0])!=null?O:{},(ie=e.value)!=null&&ie.f_ques_desc&&e.value.f_ques_type_code==="F"?(o.value=(_e=(re=e.value)==null?void 0:re.children[0])!=null?_e:{},B.value=!0,ve(()=>{var q;(q=l==null?void 0:l.value)==null||q.openDialog(o.value)})):Object.keys(e.value).length>0&&(B.value=!0,ve(()=>{var q;(q=l==null?void 0:l.value)==null||q.openDialog(e.value)})),(((de=D.value)==null?void 0:de.length)===0||v==="G")&&(B.value=!1),v==="G"&&ye()}else b.warning(t.msg)})},ye=()=>T(this,null,function*(){G.value||(G.value=!0,yield te())}),te=()=>T(this,null,function*(){if(L.value.length===0){G.value=!1;return}const a=L.value.shift();yield he(a),yield te()}),he=a=>T(this,null,function*(){const s=a.ques_code,u=a.op_file,_=a.stu_secret_num;if(u){const m={ques_code:s,file_path:u,stu_secret_num:_};a.downloadIndex||(m.is_open_folder=!0),ze(m).then(c=>{c.code&&c.code===200||b.warning(c.msg)}).catch(c=>{b.warning("下载失败")})}}),le=()=>{var a;(a=l==null?void 0:l.value)==null||a.openDialog(o.value)},ue=a=>{var s,u,_,m,c,v,n;if(H.value=a,H.value||H.value===0){let i;if((s=e.value)!=null&&s.ques_score_list?i=(_=(u=e.value)==null?void 0:u.ques_score_list)==null?void 0:_.map(t=>parseFloat(t)).reduce((t,g)=>t+g,0):i=(c=(m=o.value)==null?void 0:m.ques_score_list)==null?void 0:c.map(t=>parseFloat(t)).reduce((t,g)=>t+g,0),Number(H.value)<=i){const{path:t}=f.query;t===p[0]?ke():t===p[1]?ge():p[2]}else b.warning(`得分不能超过题目总分（${i}分）！`),(v=l==null?void 0:l.value)==null||v.focusInput()}else b.warning("评分分数不能为空！"),(n=l==null?void 0:l.value)==null||n.focusInput()},ke=()=>{var n,i;const{group_id:a,m_read_task_id:s}=f.query;let u=e.value.ques_id?e.value.ques_id:o.value.ques_id,_=e.value.mark_state?e.value.mark_state:o.value.mark_state,m=e.value.distri_answer_id?e.value.distri_answer_id:o.value.distri_answer_id,c=e.value.stu_secret_num?e.value.stu_secret_num:o.value.stu_secret_num,v={m_read_task_id:s,group_id:a,distri_answer_id:m,stu_secret_num:c,ques_id:u,expert_mark_score:Number(H.value),mark_parse:h.value.getMarkParseFn(),step_score_list:e.value.step_score_list?e.value.step_score_list:null};_===6||_===7?(v.manual_mark_id=(n=e.value.manual_mark_id)!=null?n:o.value.manual_mark_id,v.manual_aq_id=(i=e.value.manual_aq_id)!=null?i:o.value.manual_aq_id,v.mark_type=3):v.mark_type=1,Re(v).then(t=>{t.code&&t.code===200?(e.value.f_ques_type_code&&e.value.f_ques_type_code==="F"?V():S(),e.value.step_score_list&&(e.value.step_score_list=e.value.step_score_list.map(g=>null))):b.warning(t.msg)})},ge=()=>{let a=e.value.expert_data?e.value.expert_data:o.value.expert_data;const s=a==null?void 0:a.map(i=>i.manual_mark_id),{group_id:u,m_read_task_id:_}=f.query;let m=e.value.ques_id?e.value.ques_id:o.value.ques_id,c=e.value.distri_answer_id?e.value.distri_answer_id:o.value.distri_answer_id,n={manual_aq_id:e.value.manual_aq_id?e.value.manual_aq_id:o.value.manual_aq_id,manual_mark_id_list:s,distri_answer_id:c,arbitrate_type:1,arbitrate_mark_score:Number(H.value),group_id:u,m_read_task_id:_,ques_id:m,step_score_list:e.value.step_score_list?e.value.step_score_list:null,arbitrator_parse:h.value.getMarkParseFn()};Pe(n).then(i=>{i.code&&i.code===200?e.value.f_ques_type_code&&e.value.f_ques_type_code==="F"?V():S():b.warning(i.msg)})},E=a=>{const{group_id:s,m_read_task_id:u}=f.query;let _=e.value.ques_id?e.value.ques_id:o.value.ques_id,m=e.value.manual_mark_id?e.value.manual_mark_id:o.value.manual_mark_id,c=e.value.distri_answer_id?e.value.distri_answer_id:o.value.distri_answer_id,v=e.value.manual_aq_id?e.value.manual_aq_id:o.value.manual_aq_id;const n=h.value.getMarkParseFn();!a&&!n?b.warning("质检意见不能为空！"):je({manual_aq_id:v,manual_mark_id:m,distri_answer_id:c,quality_result:a,quality_suggestion:n,group_id:s,m_read_task_id:u,ques_id:_,quality_type:1}).then(t=>{t.code&&t.code===200?e.value.f_ques_type_code&&e.value.f_ques_type_code==="F"?V():S():b.warning(t.msg)})},V=()=>{var a;h.value.unMarkQuestion.length>1?(I.value.queryHistoryFn(),h.value.autoChangeComIndex(),(a=l==null?void 0:l.value)==null||a.clearScoreFn(),h.value.clearMarkParseFn()):S()},S=()=>{var a;I.value.queryHistoryFn(),A.value++,e.value=D.value[A.value],A.value===D.value.length&&se(),e!=null&&e.value&&Object.keys(e==null?void 0:e.value).length>0&&e.value.f_ques_type_code&&e.value.f_ques_type_code==="F"&&le(),(a=l==null?void 0:l.value)==null||a.clearScoreFn(),$.value.updateTaskProcessFn(),h.value.clearMarkParseFn()},qe=a=>{Z.value=a,a?(z.value.style.width="calc(100% - 492px)",N.value.style.width="492px"):(z.value.style.width="100%",N.value.style.width="0px")},we=a=>{a.preventDefault(),a.stopPropagation(),document.addEventListener("mousemove",ne),document.addEventListener("mouseup",oe)},W=d(null),z=d(null),N=d(null),ne=a=>{var u;let s=a.clientX-((u=z.value)==null?void 0:u.getBoundingClientRect().left);s<240&&(s=240),z.value&&s>1e3&&(z.value.style.width=`${s}px`),N.value&&W.value.offsetWidth-s>420&&(N.value.style.width=W.value.offsetWidth-s+"px")},oe=()=>{document.removeEventListener("mousemove",ne),document.removeEventListener("mouseup",oe)},xe=(a,s)=>{const u=X-300,_=Y-200;J.value=Math.min(Math.max(0,a),u),K.value=Math.min(Math.max(0,s),_)},be=()=>{I.value.openHistoryFn()};return(a,s)=>{var m,c,v;const u=me("el-button"),_=me("el-card");return k(),w("div",Ve,[r("div",We,[r("div",Ue,[r("div",Xe,[r("div",{ref_key:"contentRef",ref:W,class:"manual-content-box"},[r("div",{ref_key:"asideRef",ref:z,class:"manual-left"},[r("div",{ref_key:"progressRef",ref:F,class:"progress-secret-box"},[r("div",{class:"marking-task-pro-box",style:Ae({width:e.value&&Object.keys(e.value).length>0?"calc(100% - 380px)":"100%"})},[y(Fe,{ref_key:"taskProgressRef",ref:$,taskInfo:R(f).query,quesDetail:e.value,pathArr:p},null,8,["taskInfo","quesDetail"])],4),e.value&&Object.keys(e.value).length>0?(k(),Ee(_,{key:0,class:"secret-box"},{default:x(()=>{var n,i,t,g,Q,O;return[r("div",Ye,[r("div",Je,[e.value&&Object.keys(e.value).length>0?(k(),w("div",Ke,[r("p",null," 考生密号："+U((n=e.value)!=null&&n.stu_secret_num?(i=e.value)==null?void 0:i.stu_secret_num:(t=o.value)==null?void 0:t.stu_secret_num),1)])):P("",!0),e.value&&Object.keys(e.value).length>0?(k(),w("div",Ze,[r("p",null," 试题编号："+U((g=e.value)!=null&&g.ques_code?(Q=e.value)==null?void 0:Q.ques_code:(O=o.value)==null?void 0:O.ques_code),1)])):P("",!0)]),r("div",{class:"flex justify-end cursor-pointer",onClick:pe},[y(u,{type:"primary",size:"small"},{default:x(()=>[j("返回")]),_:1})])])]}),_:1})):P("",!0)],512),y(_,{class:Se(R(f).query.ques_type_code!=="G"?"ques-show-box":"ques-show-box-op")},{default:x(()=>[r("div",ea,[r("div",aa,[y(Me,{ref_key:"testQuestionRef",ref:h,currentSmallQuestion:o.value,"onUpdate:currentSmallQuestion":s[0]||(s[0]=n=>o.value=n),quesDetail:e.value,maxHeight:ee.value,onChangeCurIndex:le},null,8,["currentSmallQuestion","quesDetail","maxHeight"])]),r("div",{class:"flex items-center ml-[5px]"},[r("div",{class:"history-box",onClick:be},ta)])])]),_:1},8,["class"]),R(f).query.ques_type_code==="G"&&D.value.length!==0?(k(),w("div",la,[r("div",ua,[((m=e.value)==null?void 0:m.manual_type)===3||((c=o.value)==null?void 0:c.manual_type)===3?(k(),w("div",na,[y(u,{type:"success",onClick:s[1]||(s[1]=n=>E(!0))},{default:x(()=>[j("通过")]),_:1}),y(u,{type:"warning",onClick:s[2]||(s[2]=n=>E(!1))},{default:x(()=>[j("不通过")]),_:1})])):(k(),w("div",oa,[ia,r("span",ra,U((v=e.value)==null?void 0:v.opMarkScore),1),y(u,{type:"primary",onClick:s[3]||(s[3]=n=>{var i;return ue((i=e.value)==null?void 0:i.opMarkScore)})},{default:x(()=>[j("确定")]),_:1})]))])])):P("",!0)],512),r("div",{ref_key:"hisAsideRef",ref:N,class:"history-right"},[Z.value?(k(),w("div",{key:0,class:"drag-his-box",onMousedown:we},null,32)):P("",!0),y(_,{class:"w-full h-full"},{default:x(()=>[y(Ce,{ref_key:"historicalRecordRef",ref:I,taskInfo:R(f).query,pathArr:p,onShowHistory:qe},null,8,["taskInfo"])]),_:1})],512)],512),B.value?(k(),w("div",_a,[y(R(De),{isActive:!0,isResizable:!1,z:100,parentLimitation:!0,x:J.value,y:K.value,onResizing:n=>!1,onDragging:n=>!1,onDraging:xe,dragCancel:".no-drag"},{default:x(()=>{var n,i;return[r("div",da,[((n=e.value)==null?void 0:n.manual_type)===3||((i=o.value)==null?void 0:i.manual_type)===3?(k(),w("div",ca,[y(u,{type:"success",onClick:s[4]||(s[4]=t=>E(!0))},{default:x(()=>[j("通过")]),_:1}),y(u,{type:"warning",onClick:s[5]||(s[5]=t=>E(!1))},{default:x(()=>[j("不通过")]),_:1})])):(k(),w("div",va,[y(Ie,{ref_key:"markingStepRef",ref:l,quesInfo:{},score_step:R(f).query.score_step,onMarkConfirmFn:ue},null,8,["score_step"])]))])]}),_:1},8,["x","y","dragCancel"])])):P("",!0)])])])])}}}),Ia=Le(ma,[["__scopeId","data-v-c60422f2"]]);export{Ia as default};
