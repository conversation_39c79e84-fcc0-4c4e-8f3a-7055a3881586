import{aC as a,aQ as t,aR as r}from"./index-B63pSD2p.js";a();const i=e=>Promise.allSettled([t.request("post",r("/v1/score_analysis/calculate_reviewed_count"),{data:e},{},!1),t.request("post",r("/v1/score_analysis/calculate_effective_review_count"),{data:e},{},!1),t.request("post",r("/v1/score_analysis/calculate_invalid_reviewed_count"),{data:e},{},!1),t.request("post",r("/v1/score_analysis/calculate_arbitration_count"),{data:e},{},!1)]).then(s=>s).catch(s=>s),l=(e,s=!0)=>t.request("post",r("/v1/paper_verification/verification_results"),{data:e},{},s);export{i as g,l as v};
