import{d as o,j as r,w as s,ai as a,P as n}from"./index-B63pSD2p.js";const d=o({name:"Motion",props:{delay:{type:Number,default:50}},render(){const{delay:t}=this,e=r("motion");return s(a("div",{},{default:()=>[this.$slots.default()]}),[[e,{initial:{opacity:0,y:100},enter:{opacity:1,y:0,transition:{delay:t}}}]])}}),p=n({password:[{validator:(t,e,i)=>{e===""?i(new Error("请输入密码")):i()},trigger:"blur"}]});export{d as M,p as l};
