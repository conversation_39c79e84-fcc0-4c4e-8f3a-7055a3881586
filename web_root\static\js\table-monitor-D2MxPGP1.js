var N=Object.defineProperty;var T=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var C=(s,a,i)=>a in s?N(s,a,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[a]=i,g=(s,a)=>{for(var i in a||(a={}))V.call(a,i)&&C(s,i,a[i]);if(T)for(var i of T(a))P.call(a,i)&&C(s,i,a[i]);return s};import{d as W,l as L,n as q,a2 as F,i as Z,r as u,o,g as h,h as f,e as t,c,F as A,p as H,q as z,t as d,f as n,y as b,b as k,a6 as w,C as U,T as $,ac as G,ad as J,_ as K}from"./index-B63pSD2p.js";const l=s=>(G("data-v-b90da79e"),s=s(),J(),s),O={key:0},Q=["onClick"],X={class:"li-item"},Y={class:"li-left"},tt={class:"li-l-top"},et={class:"li-l-bottom"},st={class:"bottom-item-wrap"},ot={class:"bottom-item"},it={style:{width:"85px"}},nt={key:1},at={class:"bottom-item"},ct={style:{width:"85px"}},lt={key:1},dt={class:"bottom-item"},_t={style:{width:"85px"}},rt={key:1},ut={class:"bottom-item"},ht={style:{width:"100px"}},vt={class:"bottom-item"},pt={style:{width:"55px"}},mt={class:"bottom-item"},yt={style:{width:"40px"}},gt={key:1},ft={key:0,class:"right-item"},bt={class:"num"},kt=l(()=>t("span",{class:"unit"},"%",-1)),wt=l(()=>t("div",{class:"title"},"及格率",-1)),xt={key:1,class:"right-item"},St={class:"num"},Tt=l(()=>t("div",{class:"title"},"平均分",-1)),Ct={key:2,class:"right-item"},Lt={class:"num"},zt=l(()=>t("div",{class:"title"},"问题卷",-1)),Bt={class:"right-item"},Dt={class:"num"},It=l(()=>t("span",{class:"unit"},"%",-1)),Rt=l(()=>t("div",{class:"title"},"满分率",-1)),jt={class:"right-item"},Et={class:"num"},Mt=l(()=>t("span",{class:"unit"},"%",-1)),Nt=l(()=>t("div",{class:"title"},"零分率",-1)),Vt={class:"li-detail"},Pt=l(()=>t("div",null,"详情",-1)),Wt=W({__name:"table-monitor",props:{tableListData:{type:Array,required:!0},isSubject:{type:Boolean,default:!1}},emits:["openDetailPage"],setup(s,{emit:a}){const i=L(1),x=L();q(()=>{m(),window.addEventListener("resize",m)}),F(()=>{window.removeEventListener("resize",m)});function m(){setTimeout(()=>{i.value=x.value.offsetWidth/1180},0)}const B=a;function D(_){B("openDetailPage",_,I)}const S=Z();function I(_){const v=S.getRoutes().find(r=>r.path===_.path);if(v){const p=w().multiTags.findIndex(y=>y.path===_.path);p!==-1&&w().handleTags("splice","",{startIndex:p,length:1}),w().handleTags("push",g({meta:v.meta,name:v.name},_)),U(()=>{S.push(g({},_))})}else $.warning("没有权限!")}return(_,v)=>{const r=u("el-statistic"),p=u("Right"),y=u("el-icon"),R=u("el-button"),j=u("el-empty"),E=u("el-scrollbar");return o(),h(E,{height:"100%",always:"","view-style":{height:"100%"}},{default:f(()=>[t("div",{class:"table-list-wrap",ref_key:"tableListWrapRef",ref:x},[s.tableListData.length!==0?(o(),c("ul",O,[(o(!0),c(A,null,H(s.tableListData,(e,M)=>(o(),c("li",{key:M,onClick:qt=>D(e),style:z({zoom:i.value})},[t("div",X,[t("div",Y,[t("div",tt,[t("div",null,d(e.title),1)]),t("div",et,[t("div",st,[t("div",ot,[n(" 阅卷总量："),t("div",it,[e.total_count!==void 0?(o(),h(r,{key:0,value:e.total_count},null,8,["value"])):(o(),c("span",nt,"-")),n("份 ")])]),t("div",at,[n(" 已阅量："),t("div",ct,[e.reviewed_count!==void 0?(o(),h(r,{key:0,value:e.reviewed_count},null,8,["value"])):(o(),c("span",lt,"-")),n("份 ")])]),t("div",dt,[n(" 未阅量："),t("div",_t,[e.unreviewed_count!==void 0?(o(),h(r,{key:0,value:e.unreviewed_count},null,8,["value"])):(o(),c("span",rt,"-")),n("份 ")])]),t("div",ut,[n(" 平均速度："),t("div",ht,d(e.average_speed!==void 0?e.average_speed:"-")+"份/时 ",1)]),t("div",vt,[n(" 预估剩余："),t("div",pt,d(e.remaining_time!==void 0?e.remaining_time:"-")+"h ",1)]),t("div",mt,[n(" 专家量："),t("div",yt,[e.expert_count!==void 0?(o(),h(r,{key:0,value:e.expert_count},null,8,["value"])):(o(),c("span",gt,"-")),n("人 ")])])])])]),t("div",{class:"li-right",style:z({width:s.isSubject?"24%":""})},[s.isSubject?b("",!0):(o(),c("div",ft,[t("div",bt,[n(d(e.pass_rate!==void 0?e.pass_rate:"-"),1),kt]),wt])),s.isSubject?(o(),c("div",xt,[t("div",St,d(e.average_score!==void 0?e.average_score:"-"),1),Tt])):b("",!0),s.isSubject?(o(),c("div",Ct,[t("div",Lt,d(e.exception_count!==void 0?e.exception_count:"-"),1),zt])):b("",!0),t("div",Bt,[t("div",Dt,[n(d(e.full_score_rate!==void 0?e.full_score_rate:"-"),1),It]),Rt]),t("div",jt,[t("div",Et,[n(d(e.zero_score_rate!==void 0?e.zero_score_rate:"-"),1),Mt]),Nt])],4),t("div",Vt,[k(R,{link:"",type:"primary"},{default:f(()=>[Pt,t("div",null,[k(y,null,{default:f(()=>[k(p)]),_:1})])]),_:1})])])],12,Q))),128))])):(o(),h(j,{key:1,description:"暂无数据"}))],512)]),_:1})}}}),At=K(Wt,[["__scopeId","data-v-b90da79e"]]);export{At as default};
