import{aQ as r,aR as a}from"./index-B63pSD2p.js";const s=e=>r.request("post",a("/v1/read_paper/get_read_paper"),{data:e},{},!0,10*60*1e3),_=e=>r.request("post",a("/v1/read_paper/get_ai_mark_data"),{data:e},{},!0,40*60*1e3,"获取数据中..."),p=e=>r.request("post",a("/v1/read_paper/init_ai_mark_state"),{data:e},{},!0,40*60*1e3,"数据初始化中..."),o=e=>r.request("post",a("/v1/read_paper/start_read_paper"),{data:e}),n=e=>r.request("post",a("/v1/read_paper/get_read_percentage"),{data:e},{},!1),u=e=>r.request("post",a("/v1/read_paper/profession_mark"),{data:e}),d=e=>r.request("post",a("/v1/read_paper/get_ques_mark_detail"),{data:e});export{n as a,s as b,_ as c,d as g,p as i,u as p,o as s};
