import t from"./index-BH2_6qpo.js";import{d as r,o,c as e,b as m}from"./index-B63pSD2p.js";import"./add-users-B_4OYwGe.js";import"./common-methods-BWkba4Bo.js";import"./test-paper-management-DjV_45YZ.js";import"./validate-Dc6ka3px.js";import"./user-management-B1vGxPiG.js";import"./index-CMAj5lxj.js";import"./add-expert-TgR65NrV.js";import"./permission-config-Bzm1MNg5.js";import"./roles-management-DlteFevS.js";import"./data-permission-B4Yb7yTr.js";import"./downloadRequest-CdE2PBjt.js";import"./uploadRequest-IEYs8WTn.js";import"./calculateTableHeight-BjE6OFD1.js";const C=r({name:"examiners",__name:"index",setup(i){return(p,a)=>(o(),e("div",null,[m(t,{"making-staff-flag":!0})]))}});export{C as default};
