var Q=Object.defineProperty;var q=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var B=(o,l,s)=>l in o?Q(o,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[l]=s,z=(o,l)=>{for(var s in l||(l={}))G.call(l,s)&&B(o,s,l[s]);if(q)for(var s of q(l))H.call(l,s)&&B(o,s,l[s]);return o};var R=(o,l,s)=>new Promise((h,_)=>{var v=c=>{try{i(s.next(c))}catch(b){_(b)}},T=c=>{try{i(s.throw(c))}catch(b){_(b)}},i=c=>c.done?h(c.value):Promise.resolve(c.value).then(v,T);i((s=s.apply(o,l)).next())});import{e as J,f as K,h as W,i as X}from"./paper-sample-DNcQEVKm.js";import{S as Y}from"./index-BCxZUZMh.js";import{d as Z}from"./downloadRequest-CdE2PBjt.js";import{d as ee,m as te,l as p,P as ae,n as se,T as u,B as A,r as S,j as ne,o as I,g as j,h as g,e as r,b as f,f as k,u as oe,c as le,F as re,w as ie,C as ue,ac as de,ad as ce,_ as pe}from"./index-B63pSD2p.js";const y=o=>(de("data-v-c61ab10e"),o=o(),ce(),o),_e={class:"content_root"},me={class:"left"},ge={class:"busin_table"},fe=y(()=>r("div",{class:"_title"},"抽取范围",-1)),he={class:"busTable_wrapper"},ve=y(()=>r("div",{class:"_title"},"抽取数量",-1)),be={class:"dump_wrapper"},we=y(()=>r("span",null,"抽卷",-1)),Ce=y(()=>r("span",null,"份",-1)),ye={class:"extract_div"},xe={class:"right"},De=y(()=>r("div",{class:"_title"},"抽取结果",-1)),Se={class:"table_wrapper"},Ie={style:{"margin-right":"20px"}},ke=ee({__name:"takeParam",props:{modelValue:{type:Boolean,default:!1},sample_id:{type:String,default:""},query_conditions:{type:String,default:""}},emits:[""],setup(o,{emit:l}){const s=l,h=o,_=te({get:()=>h.modelValue,set:e=>s("update:modelValue",e)}),v=p(null),T=p([]),i=ae({dumpNum:null,busTableColumns:[{prop:"project_name",label:"资格",width:150},{prop:"exam_session",label:"场次",width:60},{prop:"subject_name",label:"科目"}],busniessData:[]});se(()=>{c()});const c=()=>{i.busniessData=[],J().then(e=>{if(!e||e.code!=200)return u.error("后端接口异常");i.busniessData=e.data.extra_range_list||[]})},b=()=>{N(e=>{if(!e||e.code!=200||e.data.length==0)return u.error("后端接口异常,未返回导出ID");let t={sample_id:e.data[0]};Z("post","/v1/prepare/export_stu_answer_list_by_sample_id","考生作答信息",t,t,"zip").then(a=>{a&&a.data&&a.data.type=="application/json"&&u({message:"后端异常，原因:"+a.msg,type:"error"}),s("saveSuccess")})})},N=e=>{var n,d,w;if(x.value==1)return u.warning("请先抽取数据");let t=((w=(d=(n=m.value)==null?void 0:n.allResData)==null?void 0:d.data)==null?void 0:w.stu_ids)||[];if(t.length==0)return u.warning("没有数据可保存");W({query_conditions:"",stu_ids:t}).then(C=>{if(C.code!=200)return u.error(`接口异常，${C.msg}`);u.success("保存成功"),typeof e=="function"?e(C):s("saveSuccess")})},F=[{prop:"project_name",label:"资格",width:380,align:"center"},{prop:"subject_name",label:"科目",width:380,align:"center"},{prop:"session",label:"场次",width:80,align:"center"},{prop:"stu_id",label:"考生密号",align:"center"}];p(null);const E=()=>{var a;let e=[];return(a=v.value)==null||a.getSelectedDatas().forEach(n=>{e.push([n.project_id,n.subject_id,n.exam_session])}),{ids_list:e,number:i.dumpNum}},m=p(null),L=p({url:"/v1/prepare/get_stu_list",method:"POST",params:E,setMappingData:e=>{var t,a,n;return{data:(t=e==null?void 0:e.data)==null?void 0:t.stus_data.map((d,w)=>z({},d)),total:(n=(a=e==null?void 0:e.data)==null?void 0:a.stus_data)==null?void 0:n.length}}}),M=()=>{var t;if(!i.dumpNum)return u.warning("请输入抽取数量"),!1;var e=(t=v.value)==null?void 0:t.getSelectedDatas();return e.length==0?(u.warning("请选择资格科目场次"),!1):!0},$=()=>R(this,null,function*(){var e;M()&&(x.value=2,yield ue(),(e=m.value)==null||e.loadAllData())}),V=p([]),P=p([]),U=()=>{P.value=[];const e=O();if(e.length==0)return u.error("未获取到作答Id集合");X({answer_id_list:e}).then(a=>{var n;if(a.code!=200)return u.error(`接口异常，${a.msg}`);P.value=((n=a.data)==null?void 0:n.data)||[]})};function O(){var e=m.value.currentPage,t=m.value.pageSize;const a=(e-1)*t,n=a+t;return V.value.slice(a,n)||[]}const x=p(1);return p(null),A(()=>h.sample_id,e=>{e&&(x.value=2,K({sample_id:e}).then(t=>{var a,n;if(t.code!=200)return u.error(`接口异常，${t.msg}`);V.value=(((a=t.data)==null?void 0:a.data)||[]).map(d=>d.answer_id),m.value.total=(n=V.value.length)!=null?n:0,U()}))},{immediate:!0}),A(()=>h.query_conditions,e=>{},{immediate:!0,deep:!0}),(e,t)=>{const a=S("CommonTableComponent"),n=S("el-input-number"),d=S("el-button"),w=S("FullDialogComponent"),C=ne("debounceDirective");return I(),j(w,{modelValue:_.value,"onUpdate:modelValue":t[2]||(t[2]=D=>_.value=D),title:"试卷抽样"},{content:g(()=>[r("div",_e,[r("div",me,[r("div",ge,[fe,r("div",he,[f(a,{ref_key:"busTableRef",ref:v,data:i.busniessData,columns:i.busTableColumns,"show-pagination":!1,autoLoad:!1,showSelection:!0,selectedIds:T.value,"row-key":"session_id",show:"",border:"",stripe:"",height:"100%"},null,8,["data","columns","selectedIds"])])]),ve,r("div",be,[we,f(n,{modelValue:i.dumpNum,"onUpdate:modelValue":t[0]||(t[0]=D=>i.dumpNum=D),min:0,style:{width:"200px"}},null,8,["modelValue"]),Ce]),r("div",ye,[f(d,{type:"primary",onClick:$,style:{width:"70%"}},{default:g(()=>[k("开始抽取")]),_:1})])]),r("div",xe,[x.value==1?(I(),j(oe(Y),{key:0,title:"抽样的结果",stepList:["选择左侧抽样条件","点击【开始抽取】按钮，抽取数据","点击【导出】按钮，可导出样卷图片"]})):(I(),le(re,{key:1},[De,r("div",Se,[f(a,{ref_key:"extractTableRef",ref:m,onSizeChange:e.extractSizeChange,onCurrentPageChange:e.extractCurrentPageChange,columns:F,"show-pagination":!0,autoLoad:!1,data:P.value,showIndex:!0,"row-key":"stu_id",frontPagination:!0,request:L.value,border:"",stripe:"",height:"100%"},null,8,["onSizeChange","onCurrentPageChange","data","request"])])],64))])])]),footer:g(()=>[r("div",Ie,[f(d,{onClick:t[1]||(t[1]=D=>_.value=!1)},{default:g(()=>[k("取 消")]),_:1}),f(d,{onClick:b},{default:g(()=>[k("保存并导出")]),_:1}),ie((I(),j(d,{type:"primary"},{default:g(()=>[k("保 存")]),_:1})),[[C,()=>{N()},"200"]])])]),_:1},8,["modelValue"])}}}),qe=pe(ke,[["__scopeId","data-v-c61ab10e"]]);export{qe as default};
