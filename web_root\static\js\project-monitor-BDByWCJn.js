var M=Object.defineProperty,q=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var D=(n,a,t)=>a in n?M(n,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[a]=t,k=(n,a)=>{for(var t in a||(a={}))E.call(a,t)&&D(n,t,a[t]);if(S)for(var t of S(a))N.call(a,t)&&D(n,t,a[t]);return n},w=(n,a)=>q(n,B(a));var j=(n,a,t)=>new Promise((W,o)=>{var y=l=>{try{s(t.next(l))}catch(_){o(_)}},m=l=>{try{s(t.throw(l))}catch(_){o(_)}},s=l=>l.done?W(l.value):Promise.resolve(l.value).then(y,m);s((t=t.apply(n,a)).next())});import{g as A,d as U}from"./formal-monitor-BXl8VaF0.js";import{c as $,a as I}from"./calculateTableHeight-BjE6OFD1.js";import{p as Q,g as G,a as J}from"./common-methods-BWkba4Bo.js";import{d as K}from"./downloadRequest-CdE2PBjt.js";import{d as X,l as d,P as Y,n as Z,ao as ee,r as f,o as te,c as ae,b,h as g,ae as oe,e as C,f as le,aV as ne,T as V,C as re,_ as pe}from"./index-B63pSD2p.js";import"./test-paper-management-DjV_45YZ.js";const se={class:"zf-flex-end"},ie=X({__name:"project-monitor",setup(n){const a=[{value:1,label:"未发起"},{value:2,label:"进行中"},{value:3,label:"已完成"},{value:4,label:"已暂停"},{value:5,label:"已结束"}],t=d(null),W=d(null),o=d({}),y=Y({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>Q.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>m.value},{label:"任务",prop:"task_name",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择任务",optionData:()=>s.value},{label:"执行状态",prop:"round_state",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择执行状态",optionData:()=>a}]}),m=d([]),s=d([]),l=d({field:[{prop:"project_name",label:"所属资格",minWidth:"220px"},{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"exam_total",label:"参考人数",minWidth:"120px"},{prop:"expert_total",label:"参评人数",minWidth:"120px"},{prop:"total_count",label:"阅卷总量",minWidth:"120px"},{prop:"reviewed_count",label:"已阅量",minWidth:"120px"},{prop:"unreviewed_count",label:"未阅量",minWidth:"120px"},{prop:"rate",label:"阅卷进度",minWidth:"120px",formatter:e=>`${e.rate?e.rate:0}%`},{prop:"average_speed1",label:"平均速度（份/时）",minWidth:"100px"},{prop:"average_speed2",label:"平均评分时间（秒/份）",minWidth:"120px"},{prop:"max_speed",label:"最长评分时间（秒）",minWidth:"120px"},{prop:"min_speed",label:"最短评分时间（秒）",minWidth:"120px"},{prop:"time",label:"预估剩余时间（时）",minWidth:"120px"},{prop:"average_score",label:"平均分",minWidth:"120px"},{prop:"max_score",label:"最高分",minWidth:"120px"},{prop:"min_score",label:"最低分",minWidth:"120px"},{prop:"score_standard_deviation",label:"分数标准差",minWidth:"120px"},{prop:"pass_rate",label:"及格率",minWidth:"120px"},{prop:"pass_count",label:"及格人数",minWidth:"120px"},{prop:"full_score_rate",label:"满分率",minWidth:"120px"},{prop:"full_score_count",label:"满分人数",minWidth:"120px"},{prop:"zero_score_rate",label:"零分率",minWidth:"120px"},{prop:"zero_score_count",label:"零分人数",minWidth:"120px"},{prop:"remark_count",label:"复评次数",minWidth:"120px"}],styleOptions:{minHeight:"100px",isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),_=d([]);let O=null;Z(()=>{G(),v(),$(O,t.value,l.value,!0,58),h()}),ee(()=>{I(O)});const F=(e,r)=>j(this,null,function*(){if(e.prop==="project_id"&&(m.value=[],s.value=[],o.value.subject_id&&(o.value.subject_id=null),o.value.task_name&&(o.value.task_name=null),r)){const p=yield J(r);m.value=p||[],v()}e.prop==="subject_id"&&(s.value=[],o.value.task_name&&(o.value.task_name=null),r&&v())}),v=()=>j(this,null,function*(){var i,u;const r=(u=(i=(yield A({page_size:-1,project_id:o.value.project_id,subject_id:o.value.subject_id,task_type:1})).data)==null?void 0:i.data)!=null?u:[],p=new Map(r.map(c=>[c.task_name,c]));s.value=[...p.values()].map(c=>({label:c.task_name,value:c.task_name}))});function z(){let{currentPage:e,pageSize:r}=l.value.pageOptions;return k({current_page:e,page_size:r,task_type:1,round_state_list:o.value.round_state?[o.value.round_state]:[],round_count:"1"},o.value)}const h=()=>{U(z()).then(e=>{var p,i,u;const r=((p=e.data)==null?void 0:p.data)||[];_.value=r,l.value.pageOptions.total=(u=(i=e.data)==null?void 0:i.total)!=null?u:0})},P=()=>{ne.confirm("确定导出吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{K("post","/v1/survey_monitor/project_survey_monitor_export",void 0,{},w(k({},z()),{page_size:-1}),"xlsx").then(e=>{e&&e.data&&e.data.type=="application/json"&&V({message:"暂无导出信息！",type:"warning"})}).catch(()=>{V({type:"error",message:"导出失败"})})}).catch(()=>{})},R=e=>{l.value.pageOptions.pageSize=e,h()},T=e=>{l.value.pageOptions.currentPage=e,h()};function H(){m.value=[],s.value=[],re(()=>{v()})}return(e,r)=>{const p=f("form-component"),i=f("el-card"),u=f("el-button"),c=f("Auth"),L=f("table-component");return te(),ae("div",null,[b(i,null,{default:g(()=>[oe(e.$slots,"tabs",{},void 0,!0),C("div",{ref_key:"formDivRef",ref:t},[b(p,{ref_key:"formRef",ref:W,modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=x=>o.value=x),"form-options":y,"is-query-btn":!0,onOnchangeFn:F,onQueryDataFn:h,onResetFields:H},null,8,["modelValue","form-options"])],512)]),_:3}),b(i,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:g(()=>{var x;return[C("div",se,[b(c,{value:"formal-monitor/question-export"},{default:g(()=>[b(u,{type:"primary",onClick:P},{default:g(()=>[le("导出")]),_:1})]),_:1})]),b(L,{minHeight:(x=l.value.styleOptions)==null?void 0:x.minHeight,"table-options":l.value,"table-data":_.value,onOnHandleSizeChange:R,onOnHandleCurrentChange:T},null,8,["minHeight","table-options","table-data"])]}),_:1})])}}}),ve=pe(ie,[["__scopeId","data-v-fd66b3f6"]]);export{ve as default};
