var D=(a,y,s)=>new Promise((n,p)=>{var u=t=>{try{o(s.next(t))}catch(m){p(m)}},g=t=>{try{o(s.throw(t))}catch(m){p(m)}},o=t=>t.done?n(t.value):Promise.resolve(t.value).then(u,g);o((s=s.apply(a,y)).next())});import{a as P}from"./uploadRequest-IEYs8WTn.js";import{d as B,n as E,ao as I,P as N,B as U,l as h,r as _,o as b,g as R,h as r,e as d,b as c,u as V,ig as q,c as A,t as k,y as M,f as S,T as v,ac as z,ad as O,_ as T}from"./index-B63pSD2p.js";const Z=a=>(z("data-v-c6962b25"),a=a(),O(),a),j=Z(()=>d("div",{class:"el-upload__text"},"点击上传文件夹",-1)),G={key:0,class:"selected-folder"},H={class:"folder-info"},J=["title"],K={class:"file-count"},L=B({__name:"importExamineAnswerOld3",props:{isShowDialog:{type:Boolean,required:!0,default:!1},title:{type:String,default:""},ZIndex:{required:!1,type:Number,default:2001},getParams:{type:Function,default:()=>{}}},emits:["update:isShowDialog","closeDialog"],setup(a,{emit:y}){const s=a,n=y;E(()=>{}),I(()=>{});const p=N({visible:!1}),u=()=>{p.visible=!1,n("update:isShowDialog",!1),n("closeDialog")},g=()=>D(this,null,function*(){if(!o.value.files.length){v.warning("请先选择文件夹");return}let i="/v1/transfer/upload-folder";const l=new FormData;if(o.value.files.forEach((e,f)=>{l.append("files",e)}),typeof s.getParams=="function"){let e=s.getParams();for(let f in e)l.append(f,e[f])}n("uploadBegin"),P("post",i,l,!1).then(e=>{e&&e.code&&e.code===200?(v.success(e.msg),n("uploadSuccess"),u()):(v.warning(e.msg||"上传失败，后端返回null"),n("uploadError"))}).catch(e=>{v.error((e==null?void 0:e.msg)||"上传失败"),n("uploadError")})});U(()=>s.isShowDialog,i=>{p.visible=i},{deep:!0,immediate:!0});const o=h({name:"",files:[],fileCount:0});h(),h();const t=h(),m=()=>{t.value.click()},x=i=>{const l=Array.from(i.target.files);if(l.length>0){const C=l[0].webkitRelativePath.split("/")[0];o.value={name:C,files:l,fileCount:l.length}}};return(i,l)=>{const e=_("el-icon"),f=_("Folder"),C=_("CircleCheck"),w=_("el-button"),F=_("DialogComponent");return b(),R(F,{isShowDialog:p.visible,width:"500px",onCloseDialog:u,onSure:g,beforeClose:u,title:a.title,fullscreen:!1},{content:r(()=>[d("div",{class:"drop-zone",onClick:m},[c(e,{size:"50",class:"el-icon--upload"},{default:r(()=>[c(V(q))]),_:1}),j,d("input",{ref_key:"folderInputRef",ref:t,type:"file",webkitdirectory:"",directory:"",multiple:"",style:{display:"none"},onChange:x},null,544)]),o.value.name?(b(),A("div",G,[d("div",H,[c(e,null,{default:r(()=>[c(f)]),_:1}),d("span",{class:"folder-name",title:o.value.name},k(o.value.name),9,J),d("span",K,[d("span",null,"（"+k(o.value.fileCount)+" 个文件）",1),d("span",null,[c(e,{color:"var(--el-color-success)"},{default:r(()=>[c(C)]),_:1})])])])])):M("",!0)]),footer:r(()=>[c(w,{onClick:u,disabled:i.examineeUploading},{default:r(()=>[S("取消")]),_:1},8,["disabled"]),c(w,{type:"primary",onClick:g,loading:i.examineeUploading},{default:r(()=>[S("确认")]),_:1},8,["loading"])]),_:1},8,["isShowDialog","title"])}}}),Y=T(L,[["__scopeId","data-v-c6962b25"]]);export{Y as default};
