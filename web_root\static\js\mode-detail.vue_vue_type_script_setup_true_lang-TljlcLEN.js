import V from"./index-BAbjVBSZ.js";import{C as y}from"./close-line-DgsTxgUT.js";import{d as w,l as e,r as p,o as c,g as H,h as _,e as o,t as M,c as g,b as s,u as r,C as D}from"./index-B63pSD2p.js";const B={width:24,height:24,body:'<path fill="currentColor" d="M4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Zm1 2v14h14V5H5Z"/>'},Z=B,F={width:24,height:24,body:'<path fill="currentColor" d="M7 7V3a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-4v3.992C17 21.55 16.551 22 15.992 22H3.008A1.006 1.006 0 0 1 2 20.992l.003-12.985C2.003 7.451 2.452 7 3.01 7H7Zm2 0h6.993C16.549 7 17 7.449 17 8.007V15h3V4H9v3ZM4.003 9L4 20h11V9H4.003Z"/>'},L=F,N={class:"flex justify-between"},S={class:"flex items-center justify-end"},j={class:"zf-dialog-first-box"},z={class:"zf-dialog-second-box"},P=w({__name:"mode-detail",setup(R,{expose:C}){const u=e("阅卷模式详情"),t=e(!1),l=e(!1),n=e("56vh"),d=e({}),f=e(null),x=a=>{t.value=!0,d.value=a,D(()=>{f.value.getSingleProcessFn(a.process_id)})},h=()=>{t.value=!1},v=()=>{l.value?(l.value=!1,n.value="56vh"):(l.value=!0,n.value="80vh")};return C({openDialog:x}),(a,m)=>{const i=p("iconify-icon-offline"),b=p("el-dialog");return c(),H(b,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=k=>t.value=k),title:u.value,width:"80%","before-close":h,"close-on-click-modal":!1,"show-close":!1,fullscreen:l.value,"align-center":"",draggable:""},{header:_(()=>[o("div",N,[o("div",null,M(u.value),1),o("div",S,[l.value?(c(),g("i",{key:1,class:"cursor-pointer mr-[12px]",onClick:v},[s(i,{icon:r(L)},null,8,["icon"])])):(c(),g("i",{key:0,class:"cursor-pointer mr-[12px]",onClick:v},[s(i,{icon:r(Z)},null,8,["icon"])])),o("i",{class:"cursor-pointer text-[20px]",onClick:h},[s(i,{icon:r(y)},null,8,["icon"])])])])]),default:_(()=>[o("div",j,[o("div",z,[s(V,{ref_key:"newModeRef",ref:f,modeDetail:d.value,turboHeight:n.value},null,8,["modeDetail","turboHeight"])])])]),_:1},8,["modelValue","title","fullscreen"])}}});export{P as _};
