/* 工具栏 */
.kf-editor-toolbar {
    width: 100%;
    padding: 1px 10px;
    position: relative;
    top: 0;
    left: 0;
    z-index: 2;
    background-color: #f6f5ee;
    border-bottom: 1px solid #ccc;
    -moz-box-shadow:1px 1px 1px rgba( 0, 0, 0, 0.1 );
    -webkit-box-shadow:1px 1px 1px rgba( 0, 0, 0, 0.1 );
    box-shadow:1px 1px 1px rgba( 0, 0, 0, 0.1 );
    color: #000;
    font-family: Helvetica, Arial, "微软雅黑", "Microsoft YaHei", "宋体", sans-serif;
}

/* 按钮 */
.kf-editor-ui-button {
    padding: 8px 6px;
    height: 79px;
    font-size: 12px;
    display: inline-block;
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: default;
    position: relative;
    top: 0;
    left: 0;
    z-index: 3;
    vertical-align: top;
    opacity: 0.5;
    margin-right: 1px;
}

.kf-editor-toolbar .kf-editor-ui-overlap-button {
    width: 100%;
    height: 25px;
    background: #53b856;
    border-radius: 0;
}

.kf-editor-ui-button-icon {
    width: 32px;
    height: 32px;
    margin: 2px auto;
}

.kf-editor-ui-button-label {
    color: #666;
    text-align: center;
    display: block;
    font-size: 12px;
    line-height: 20px;
}

.kf-editor-ui-overlap-button .kf-editor-ui-button-label {
    padding: 3px 5px;
    text-align: left;
    color: white;
    font-size: 12px;
}

.kf-editor-ui-button-sign {
    border: 4px solid transparent;
    border-top-color: #2d2d2d;
    width: 0;
    height: 0;
    display: inline-block;
    margin: 8px auto;
    vertical-align: top;
}

.kf-editor-ui-button-mount-point {
    display: none;
    position: absolute;
    bottom: -2px;
    left: -1px;
}

.kf-editor-ui-overlap-button .kf-editor-ui-button-mount-point {
    width: 100%;
    height: 10000px;
}

.kf-editor-ui-wrap-group {
    overflow-x: hidden;
    overflow-y: auto;
}

.kf-editor-ui-overlap-button .kf-editor-ui-button-mount-point {
    top: 27px;
}

.kf-editor-toolbar .kf-editor-ui-button-in {
    border-color: #8fcc91!important;
    background: #e0f0dd!important;
}

.kf-editor-toolbar .kf-editor-ui-overlap-button {
    padding-top: 0;
    padding-bottom: 0;
    border-color: #61b864!important;
    background: #61b864!important;
}

/* 分割符 */
.kf-editor-ui-delimiter {
    width: 11px;
    height: 100%;
    display: none;
}

.kf-editor-ui-enabled.kf-editor-ui-button:HOVER {
    border: 1px solid #a9d9ab;
    background: #ebf7e6;
}

.kf-editor-ui-enabled.kf-editor-ui-overlap-button:HOVER {
    border: 1px solid #53b856;
    background: #53b856;
}

.kf-editor-ui-delimiter-line {
    width: 1px;
    height: 100%;
    margin: 0 auto;
    background: -webkit-linear-gradient(top, rgba(233, 233, 233, 0.11), rgba(92, 92, 92, 0.20) 60%, rgba(92, 92, 92, 0.41) 80%, rgba(123, 123, 123, 0.50));
}

/* box */
.kf-editor-ui-box {
    border: 1px solid #b3aead;
    border-radius: 3px;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.11);
    background: white;
    position: absolute;
    top: 0;
    left: -1px;
    overflow-x: hidden;
    overflow-y: auto;
}

.kf-editor-ui-area .kf-editor-ui-box {
    border-color: #61b864;
}

.kf-editor-ui-box-container {
    font-size: 12px;
}

.kf-editor-ui-box-group-title {
    background-color: #f7f6f0;
    height: 23px;
    line-height: 23px;
    font-size: 12px;
    border: 1px solid #ebeae4;
    border-width: 1px 0;
    padding-left: 12px;
}

.kf-editor-ui-box-group-item-container {
    padding: 7px 9px 17px 9px;
}

.kf-editor-ui-overlap-container {
    overflow: hidden;
}

.kf-editor-ui-area .kf-editor-ui-box {
    top: -1px;
}

.kf-editor-ui-overlap-container .kf-editor-ui-button-sign {
    border-top-color: white;
    border-width: 4px;
    margin-left: 10px;
    margin-top: 8px;
}

.kf-editor-ui-yushe-btn .kf-editor-ui-box-item {
    border: 1px solid transparent;
    padding: 5px;
}

.kf-editor-ui-box-item {
    display: inline-block;
    margin: 4px;
}

.kf-editor-ui-box-item-content:HOVER {
    border-color: #dff3df;
}

.kf-editor-ui-box-item-content:HOVER .kf-editor-ui-box-item-val {
    border-color: #6eb864;
}

.kf-editor-ui-area .kf-editor-ui-box-item {
    position: relative;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
    border: 0;
    margin: 3px;
    padding: 0;
    z-index: 1;
}

.kf-editor-ui-area .kf-editor-ui-box-item img {
    width: 32px;
    height: 32px;
}


.kf-editor-ui-box-item-label {
    margin-bottom: 5px;
}

.kf-editor-ui-box-item-content {
    background: white;
    border: 1px solid white;
}

.kf-editor-ui-area .kf-editor-ui-box-item-content {
    position: absolute;
    top: 0;
    left: 0;
}

.kf-editor-ui-area .kf-editor-ui-box-item-content:HOVER {
    border: 1px solid #dff3df;
}

.kf-editor-ui-box-item-val {
    padding: 5px;
    line-height: 0;
    border: 1px solid #808080;
}

.kf-editor-ui-area .kf-editor-ui-box-item-val {
    padding: 0;
    margin: 0;
}

/* area */
.kf-editor-ui-area {
    height: 79px;
    display: inline-block;
    cursor: default;
    position: relative;
    top: 0;
    left: 0;
    vertical-align: top;
    opacity: 0.5;
    z-index: 4;
    border: 1px solid #e0dfd5;
    border-radius: 4px;
    background: white;
    margin: 8px 10px;
}

.kf-editor-ui-area-container {
    width: 293px;
    height: 70px;
    /*margin: 7px 5px 5px 5px;*/
    margin: 5px;
    display: inline-block;
    border-right: 0;
    vertical-align: top;
    position: relative;
    top: 0;
    left: 0;
    overflow: hidden;
}

.kf-editor-ui-area-panel {
    position: absolute;
    top: 0;
    left: 0;
    line-height: 0;
    background: white;
    -webkit-transition: top 0.5s linear;
    -moz-transition: top 0.5s linear;
    transition: top 0.5s linear;
}

.kf-editor-ui-area-button-container {
    display: inline-block;
    width: 16px;
    height: 100%;
    overflow: hidden;
    text-align: center;
    border: 0 solid #D3D3D3;
    border-left-width: 1px;
    background-color: #f2f0e6;
}

.kf-editor-ui-moveup-button, .kf-editor-ui-movedown-button {
    line-height: 25px;
    height: 30px;
}

.kf-editor-ui-moveup-button {
    background: url("../images/toolbar/btn.png") -304px 9px no-repeat;
}

.kf-editor-ui-movedown-button {
    border: 1px solid #D3D3D3;
    border-width: 1px 0;
    background: url("../images/toolbar/btn.png") -325px 9px no-repeat;
}

.kf-editor-ui-area-button {
    width: 100%;
    height: 50px;
    line-height: 26px;
    background: url("../images/toolbar/btn.png") -346px 0 no-repeat;
}

.kf-editor-ui-enabled .kf-editor-ui-area-button:HOVER {
    background-color: #e5e4e1;
}

.kf-editor-ui-enabled .kf-editor-ui-moveup-button:HOVER {
    background-color: #e5e4e1;
}

.kf-editor-ui-enabled .kf-editor-ui-movedown-button:HOVER {
    background-color: #e5e4e1;
}

.kf-editor-ui-area-button-container .kf-editor-ui-disabled {
    opacity: 0.25;
    background-color: transparent!important;
}

.kf-editor-ui-area-mount {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
}

.kf-editor-ui-overlap-title {
    width: 100%;
    line-height: 1.5;
}

/* list */
.kf-editor-ui-list {
    background: #f9f8f5;
    border: 1px solid #b3aead;
    border-radius: 3px;
    position: fixed;
    top: 0;
    left: 0;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.11);
}

.kf-editor-ui-list-bg {
    display: none;
}

.kf-editor-ui-list-item-container {
    position: relative;
    top: 0;
    left: 0;
    z-index: 2;
}

.kf-editor-ui-list-item {
    line-height: 24px;
    padding: 2px 6px;
    border: 1px solid transparent;
    border-width: 1px 0;
}

.kf-editor-ui-list-item-select {
    color: #61b864;
    font-weight: bold;
}

.kf-editor-ui-list-item-select .kf-editor-ui-list-item-icon {
    visibility: visible;
    width: 16px;
    height: 16px;
    background: url(../images/toolbar/btn.png) no-repeat -367px 0;
}

.kf-editor-ui-list-item:HOVER {
    border-color: #beddbf;
    background-color: #ecf3e9;
}

.kf-editor-ui-list-item-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin-top: -2px;
    margin-right: 4px;
    visibility: hidden;
}

/* area 内容区 */
.kf-editor-ui-area-item {
    width: 26px;
    height: 26px;
    position: absolute;
    top: 0;
    left: 0;
}

.kf-editor-ui-area-item-inner {
    width: 34px;
    height: 34px;
    border: 1px solid white;
    position: absolute;
    top: -4px;
    left: -4px;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    transition: all 0.1s linear;

    -webkit-transform: scale( 0.76 );
    -moz-transform: scale( 0.76 );
    transform: scale( 0.76 );
}

.kf-editor-ui-enabled .kf-editor-ui-area-item-inner:HOVER {
    border-color: #dff3df;

    -webkit-transform: scale( 1 );
    -moz-transform: scale( 1 );
    transform: scale( 1 );
}

.kf-editor-ui-area-item-img {
    width: 32px;
    height: 32px;
    border: 1px solid #808080;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    transition: all 0.1s linear;
}

.kf-editor-ui-enabled .kf-editor-ui-area-item-inner:HOVER .kf-editor-ui-area-item-img {
    border-color: #6eb864;
}

.kf-editor-ui-enabled {
    opacity: 1;
}

.kf-editor-ui-overlap-container .kf-editor-ui-box-item {
    width: 34px;
    height: 34px;
    border: 1px solid white;
}

.kf-editor-ui-overlap-container .kf-editor-ui-box-item-content:HOVER {
}

.kf-editor-ui-overlap-container .kf-editor-ui-box-item-val {
    width: 32px;
    height: 32px;
}

/* scrollbar */
.kf-editor-ui-box::-webkit-scrollbar {
    width: 17px;
    background: url(../images/scrollbar/custom/bg.png) 0 0 repeat-y white;
}

.kf-editor-ui-box::-webkit-scrollbar-button:end:increment {
    height: 5px;
    background: url(../images/scrollbar/custom/down.png) 0 0 repeat-y white;
}
/*定义滚动条渐减按扭的样式 */
.kf-editor-ui-box::-webkit-scrollbar-button:start:decrement {
    height: 5px;
    background: url(../images/scrollbar/custom/up.png) 0 0 repeat-y white;
}

/* 垂直滚动条的第三层轨道的上段 */
.kf-editor-ui-box::-webkit-scrollbar-track-piece:vertical:start {
    background-image: url(../images/scrollbar/custom/top.png), url(../images/scrollbar/custom/bg.png);
    background-repeat: no-repeat, repeat-y;
}
/* 垂直滚动条的第三层轨道的下段 */
.kf-editor-ui-box::-webkit-scrollbar-track-piece:vertical:end {
    background-image: url(../images/scrollbar/custom/bottom.png), url(../images/scrollbar/custom/bg.png);
    background-repeat: no-repeat, repeat-y;
    background-position: 0 bottom, 0 0;
}
/* 垂直滚动条的滑动块 */
.kf-editor-ui-box::-webkit-scrollbar-thumb:vertical {
    /*background: url(./images2/bar.png) 6px 0 no-repeat;*/
    /*background-size: 6px;*/
    -webkit-border-image: url(../images/scrollbar/custom/bar.png) 8;
    border-width: 10px;
}

.kf-editor-ui-wrap-group::-webkit-scrollbar {
    width: 17px;
    background: url(../images/scrollbar/custom/bg.png) 0 0 repeat-y white;
}

.kf-editor-ui-wrap-group::-webkit-scrollbar-button:end:increment {
    height: 5px;
    background: url(../images/scrollbar/custom/down.png) 0 0 repeat-y white;
}
/*定义滚动条渐减按扭的样式 */
.kf-editor-ui-wrap-group::-webkit-scrollbar-button:start:decrement {
    height: 5px;
    background: url(../images/scrollbar/custom/up.png) 0 0 repeat-y white;
}

/* 垂直滚动条的第三层轨道的上段 */
.kf-editor-ui-wrap-group::-webkit-scrollbar-track-piece:vertical:start {
    background-image: url(../images/scrollbar/custom/top.png), url(../images/scrollbar/custom/bg.png);
    background-repeat: no-repeat, repeat-y;
}
/* 垂直滚动条的第三层轨道的下段 */
.kf-editor-ui-wrap-group::-webkit-scrollbar-track-piece:vertical:end {
    background-image: url(../images/scrollbar/custom/bottom.png), url(../images/scrollbar/custom/bg.png);
    background-repeat: no-repeat, repeat-y;
    background-position: 0 bottom, 0 0;
}
/* 垂直滚动条的滑动块 */
.kf-editor-ui-wrap-group::-webkit-scrollbar-thumb:vertical {
    -webkit-border-image: url(../images/scrollbar/custom/bar.png) 8;
    border-width: 10px;
}