var U=Object.defineProperty;var q=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var w=(n,a,t)=>a in n?U(n,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[a]=t,S=(n,a)=>{for(var t in a||(a={}))$.call(a,t)&&w(n,t,a[t]);if(q)for(var t of q(a))E.call(a,t)&&w(n,t,a[t]);return n};var k=(n,a,t)=>new Promise((m,b)=>{var _=s=>{try{i(t.next(s))}catch(u){b(u)}},f=s=>{try{i(t.throw(s))}catch(u){b(u)}},i=s=>s.done?m(s.value):Promise.resolve(s.value).then(_,f);i((t=t.apply(n,a)).next())});import{d as M,l as o,P as T,n as A,ao as J,aP as Q,T as G,r as y,o as x,c as V,e as D,b as v,h as j,u as z,aN as W,y as F,_ as K}from"./index-B63pSD2p.js";import{g as X,D as Y}from"./detail-Cmqa1ZEv.js";import{c as Z,a as ee}from"./calculateTableHeight-BjE6OFD1.js";import{p as te,g as ae,a as le}from"./common-methods-BWkba4Bo.js";import"./quesNum-CouueI57.js";import"./anticlockwise-2-line-Iit2_C-u.js";import"./handleImages-D-nd439N.js";import"./test-paper-management-DjV_45YZ.js";const re={class:"zf-first-box"},ne={class:"zf-second-box"},oe={class:"task-btn-box"},ie=["onClick"],se=["onClick"],ue=M({name:"plagiarzing-question-stem",__name:"index",setup(n){const a=o(null),t=o(null),m=o(!1),b=o("抄袭题干详情"),_=o(""),f=o({}),i=o({}),s=T({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>te.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>u.value},{label:"所属试卷",prop:"paper_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属试卷",optionData:()=>[]},{label:"题号",prop:"ques_number",type:"input",defaultValue:"",placeholder:"请输入题号",clearable:!0},{label:"试题编号",prop:"ques_code",type:"input",defaultValue:"",placeholder:"请输入试题编号",clearable:!0},{label:"考生密号",prop:"stu_secret_num",type:"input",defaultValue:"",placeholder:"请输入考生密号",clearable:!0},{label:"判定状态",prop:"judgement_status",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定状态",optionData:()=>[{label:"未判定",value:0},{label:"抄袭",value:1},{label:"未抄袭",value:2}]},{label:"相似度",prop:"similarity",type:"selectInput",defaultValue:[2,null],leftFilterable:!0,leftPlaceholder:"请选择",leftWidth:"76px",leftClearable:!1,leftOptionData:()=>[{label:"大于",value:2},{label:"等于",value:0},{label:"小于",value:3}],rightPlaceholder:"请输入",rightWidth:"74px",rightClearable:!0},{label:"判定结果",prop:"judgement_result",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择判定结果",optionData:()=>[{label:"未判定",value:0},{label:"抄袭",value:1},{label:"未抄袭",value:2}]}]}),u=o([]),d=o({field:[{prop:"subject_name",label:"所属科目",minWidth:"220px"},{prop:"paper_name",label:"所属试卷",minWidth:"120px",formatter:e=>{var l;return(l=e.paper_name)!=null?l:"—"}},{prop:"ques_order",label:"题号",minWidth:"90px",formatter:e=>{var l;return(l=e.ques_order)!=null?l:"—"}},{prop:"ques_code",label:"试题编号",minWidth:"200px"},{prop:"stu_secret_num",label:"考生密号（参考卷）",minWidth:"160px"},{prop:"similarity",label:"抄袭相似度",minWidth:"120px",formatter:e=>`${(e.similarity*100).toFixed(2)}%`},{prop:"judgement_result_text",label:"判定状态",minWidth:"140px"},{prop:"judgement_result_text",label:"人工判定结果",minWidth:"110px"},{prop:"operation",label:"操作",type:"slot",minWidth:"100px",fixed:"right"}],styleOptions:{isShowSort:!0,isShowSelection:!1},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),C=o([]);let O=null;A(()=>{ae(),Z(O,a.value,d.value,!1),h()}),J(()=>{ee(O)});const P=(e,l)=>{e.prop==="project_id"&&(u.value=[],i.value.subject_id&&(i.value.subject_id=null),l&&le(l).then(r=>{u.value=r||[]}))},h=()=>k(this,null,function*(){let{currentPage:e,pageSize:l}=d.value.pageOptions;const r=Q.cloneDeep(i.value);r.ques_type_score=r.similarity[0],r.similarity=r.similarity[1],delete r.similarity;let g=S({current_page:e,page_size:l},r);const c=yield X(g);c.code&&c.code===200?(C.value=c.data.items,d.value.pageOptions.total=c.data.total):G.error(c.msg)}),H=e=>{d.value.pageOptions.pageSize=e,h()},R=e=>{d.value.pageOptions.currentPage=e,h()},I=e=>{b.value="抄袭题干详情",f.value=e,_.value=e.answer_quest_similarity_id,m.value=!0},N=e=>{b.value="抄袭题干判定",f.value=e,_.value=e.answer_quest_similarity_id,m.value=!0};function B(){u.value=[]}return(e,l)=>{const r=y("form-component"),g=y("el-card"),c=y("table-component");return x(),V("div",re,[D("div",ne,[v(g,null,{default:j(()=>[D("div",{ref_key:"formDivRef",ref:a},[v(r,{ref_key:"formRef",ref:t,modelValue:i.value,"onUpdate:modelValue":l[0]||(l[0]=p=>i.value=p),"form-options":s,"is-query-btn":!0,onOnchangeFn:P,onQueryDataFn:h,onResetFields:B},null,8,["modelValue","form-options"])],512)]),_:1}),v(g,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:j(()=>[v(c,{minHeight:d.value.styleOptions.minHeight,"table-options":d.value,"table-data":C.value,onOnHandleSizeChange:H,onOnHandleCurrentChange:R},{operation:j(p=>[D("div",oe,[z(W)("plagiarzing-question-stem/detail")&&p.row.judgement_result!=0?(x(),V("span",{key:0,class:"task-btn",onClick:L=>I(p.row)},"详情",8,ie)):F("",!0),z(W)("plagiarzing-question-stem/judge")&&p.row.judgement_result==0?(x(),V("span",{key:1,class:"task-btn",onClick:L=>N(p.row)},"判定",8,se)):F("",!0)])]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),v(Y,{drawerVisible:m.value,"onUpdate:drawerVisible":l[1]||(l[1]=p=>m.value=p),title:b.value,currentSimilarityId:_.value,detailData:f.value},null,8,["drawerVisible","title","currentSimilarityId","detailData"])])}}}),ge=K(ue,[["__scopeId","data-v-43b3dfaa"]]);export{ge as default};
