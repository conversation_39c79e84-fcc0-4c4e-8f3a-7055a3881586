var F=(S,J,_)=>new Promise((b,w)=>{var A=q=>{try{m(_.next(q))}catch(v){w(v)}},k=q=>{try{m(_.throw(q))}catch(v){w(v)}},m=q=>q.done?b(q.value):Promise.resolve(q.value).then(A,k);m((_=_.apply(S,J)).next())});import{M as qe,D as se}from"./index-CaNJCtth.js";import{aQ as H,aR as W,d as Se,az as we,l as d,n as ye,B as be,r as z,o as I,c as V,b as M,h as N,e as x,f as B,u as ne,g as Z,F as Ae,p as Ee,U as Ie,q as Ne,y as j,t as G,T as D,C as Q,aV as ue,_ as Ce}from"./index-B63pSD2p.js";import{h as Me}from"./marking-Da5XG12E.js";import"./vue-drag-resize-CJIVu41Q.js";import"./index-BzG0ERft.js";import"./hooks-Cf_Naqnw.js";import"./problem-CRChWLev.js";import"./anticlockwise-2-line-Iit2_C-u.js";import"./reply-fill-DeI84Ty7.js";import"./base-DyvdloLK.js";import"./marking-paper-CSlVaOqn.js";import"./checkInfo-mJiqvlDC.js";import"./quesNum-CouueI57.js";const ie=S=>H.request("post",W("/v1/repeat_mark/get_repeat_task_stu"),{data:S}),xe=S=>H.request("post",W("/v1/ques_manage/get_ques_detail_list"),{data:S}),Te=S=>H.request("post",W("/v1/repeat_mark/repeat_stu_socre"),{data:S}),Oe=S=>H.request("post",W("/v1/repeat_mark/get_repeat_round_detail_list"),{data:S}),Ve="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAh1JREFUOE/NlDFIHEEUhv83uxcvWllYWCqeooiIiGUKeyOCgkWaQCxSBiEWIgQL8UCTw0CSQ4KKWohliGJsExElOiekCFYqisa0iwZu5oWZ5c7dW707JEUGtpk3+73/n39m6E97O+MfDvrfgV5RhW4qBWpuhpqagt7cLGdjjkJA0dUF1NTkf3T6+0GNjVALC+DTU3/++hp6exvwvEgDBr6EgEaR6Oy8WRiLAa5rIWA/O7668hVvbESABLwpbjmdhmhpQXZiAnp9vRzLT/JAqq8HNTUBQhS3bKqXl9A7O4UNjjXRozxQDAzA6evzLZpRWQmqrfUbeB747CwP4JMTZIeHQ0Bm/hDPZJ7fatmodZNJUF0dQARks1DLy1AzM3fZ1gw8jkv5OQqsqoI7PQ0Dxfm5hWopIdraoGZnoZaWomEwf3yQyTwzhRDQKhsdBSUSVhF1dPihTE5C9PRAtLZCLS5CpdNB6KEAnsak/BYCOoODEENDIMeBmp+3nxtImff24IyNwZxVvbsLNT4OvrgAAy/iUqZyHW5C6e6G6O2FXlmB3tqy9SAwd2yckRFbU2aPiV7HGhpe0uqqigBv2217c6qrwVJaNcHBwPsKrV/RwcGv4Py9XhurTKlkISwSSqmrwMAhgHcVicTboM37KNTEPEdEc7k072peyvIxM6+5RJ9cKddKOSi0bN6j3wz8FMAPBr5roq8P9/ePygHl1vwFDD3/cEEUkX8AAAAASUVORK5CYII=",Be={key:0,class:"ques-list dark:!bg-black eye-box"},Re={class:"ques-title"},Le={class:"ques-tab"},Ue=["onClick"],Fe=["src"],ze=Se({__name:"index",setup(S,{expose:J}){const _=we(),b=d(1),w=d(null),A=d("point"),k=d({}),m=d(0),q=d(!1),v=d([]),y=d({field:[{prop:"stu_secret_num",label:"考生密号",minWidth:"120px"},{prop:"official_score",label:_.query.type==1?"复评前分数":"得分",minWidth:"98px",formatter:a=>a.official_score?a.official_score:"-"},{prop:"stu_score",label:_.query.type==1?"复评分数":"验收分",minWidth:"88px"},{prop:"updated_time",label:_.query.type==1?"复评时间":"验收时间",minWidth:"120px",sortable:!0}],styleOptions:{isShowSort:!0},pageOptions:{isShowPage:!0,layout:"total, sizes, ->, prev, pager, next",currentPage:1,pageSize:10,total:0}}),X=d([]);d(!0);const p=d(""),r=d([]),ce=()=>{p.value.markValue=A.value},c=d(0),de=(a,e)=>{var o;p.value=a,c.value=e,R(),P(),(o=w.value)==null||o.getStepByQuesCode(p.value.quesInfo.ques_code)},T=d(!1);ye(()=>{te(),y.value.styleOptions.minHeight=window.innerHeight-208});const _e=a=>{y.value.styleOptions.minHeight=a},Y=a=>a.reduce((o,s)=>o.plus(new se(s)),new se(0)).toString().replace(/\.0+$/,"").replace(/(\.\d*?)0+$/,"$1"),$=(a,e)=>a>e?e:a;be(()=>p.value,a=>{K(p.value,p.value.markValue)},{deep:!0});const K=(a,e,o)=>{var s,t,u,l,n,i,h,C;if(a){if(a.oldTotalScore=(s=a==null?void 0:a.answer_data)==null?void 0:s.reduce((E,f)=>E+Number(f.official_score),0),(((t=_.query)==null?void 0:t.type)==4||((u=_.query)==null?void 0:u.type)==5)&&o){a.totalScore=a.oldTotalScore;return}if(e==="total")if((l=a.quesInfo)==null?void 0:l.children.find(f=>f.marking_score==null||f.marking_score===""))a.totalScore=null;else{const f=(i=(n=a.quesInfo)==null?void 0:n.children)==null?void 0:i.map(g=>Number(g.marking_score));if(f&&f.length>0){const g=Number(Y(f));a.totalScore=$(g,Number(a.quesInfo.ques_score))}}else if(e==="point"){let E=!1,f=[];if((C=(h=a.quesInfo)==null?void 0:h.children)==null||C.forEach(g=>{var U;if((U=g.ques_mark_point)!=null&&U.length)if(g.ques_mark_point.find(O=>O.marking_score==null||O.marking_score===""))E=!0;else{const O=g.ques_mark_point.reduce((ke,he)=>ke+Number(he.marking_score),0),re=Number(g.ques_score);f.push(O>re?re:O)}}),E)a.totalScore=null;else{if(f.length<=0){a.totalScore=null;return}const g=Number(Y(f));a.totalScore=$(g,Number(a.quesInfo.ques_score))}}}},ve=()=>{let a=r.value.reduce((t,u)=>u.oldTotalScore?t+u.oldTotalScore:t+0,0),e="",o="";if(r.value.filter(t=>t.totalScore===null||t.totalScore===""||t.totalScore===void 0).length==r.value.length)e="",o=Math.abs(a-e);else{for(let t of r.value)t.totalScore===null||t.totalScore===void 0||t.isSumit&&(e=Number(e)+t.totalScore);e==null||e===""?o="":o=Math.abs(a-e)}return{oldTotalScore:a,totalScore:e,diffScore:o}};d([]);const ee=()=>F(this,null,function*(){var o;let a=[];(o=r.value)==null||o.forEach(s=>{a.push(s.ques_code)});const e=yield xe({ques_code_list:a});if(e.code&&e.code===200){let s=e.data;s.forEach(t=>{t.ques_detail=Me(JSON.parse(JSON.stringify(t.ques_detail)))}),r.value&&r.value.length>0&&(r.value.forEach((t,u)=>{t.quesInfo=s[u].ques_detail}),P())}else D.error(e.msg)}),P=()=>{k.value=r.value[c.value].quesInfo,v.value.forEach(a=>{var e;(e=a.quesInfo)==null||e.children.forEach(o=>{o.ques_mark_point&&o.ques_mark_point.length>0&&o.ques_mark_point.forEach(s=>{s.isActive=!1})})}),b.value==1&&(r.value[c.value].totalScore===null||r.value[c.value].totalScore===void 0)&&(r.value[c.value].markValue=A.value),r.value[c.value].markValue=="point"?(A.value="point",Q(()=>{var a,e,o,s,t,u,l,n;(o=(e=(a=k.value)==null?void 0:a.children)==null?void 0:e[m.value])!=null&&o.ques_mark_point[0]&&((n=(l=(u=(t=(s=k.value)==null?void 0:s.children)==null?void 0:t[m.value])==null?void 0:u.ques_mark_point[0])==null?void 0:l.inputRef)==null||n.focus(),k.value.children[m.value].ques_mark_point[0].isActive=!0)})):(A.value="total",Q(()=>{k.value.children[m.value].inputRef.focus(),k.value.children[m.value].isActive=!0}))};J({getPaperTotal:()=>({oldTotalScore:v.reduce((a,e)=>a+e.oldTotalScore,0),totalScore:v.reduce((a,e)=>a+e.totalScore,0)})});const ae=d(!1),te=()=>{var e;b.value=1;let a={current_page:1,page_size:10,repeat_task_id:(e=_.query)==null?void 0:e.repeat_task_id};ie(a).then(o=>F(this,null,function*(){var s,t;if(o.code&&o.code==200)if(ae.value=!0,r.value=[],v.value=[],((s=o.data)==null?void 0:s.data.length)>0){o.data.data.forEach(u=>{u.answer_data.forEach(l=>{l.mark_point_score_list=l.official_stu_score_list})}),r.value=o.data.data,p.value=r.value[0],c.value=0,R(),yield ee(),oe();for(let u in r.value)K(r.value[u],r.value[u].markValue);A.value=p.value.markValue,(t=w.value)==null||t.getStepByQuesCode(p.value.quesInfo.ques_code)}else T.value=!1}))},R=()=>{var a,e;p.value&&(k.value=p.value.quesInfo,m.value=0,v.value=[p.value],v.value.forEach(o=>{o.answer_data.forEach(s=>{s.isActive=!1})}),v.value.length&&(e=(a=v.value[m.value])==null?void 0:a.answer_data)!=null&&e[0]&&(v.value[m.value].answer_data[0].isActive=!0))},oe=()=>{var a,e,o;for(let s in r.value){let t=r.value[s],u=t.answer_data[0].mark_point_score_list;u&&u.length>0?(t.markValue="point",(a=t.quesInfo.children)==null||a.forEach((l,n)=>{var i;(i=l.ques_mark_point)==null||i.forEach((h,C)=>{h.marking_score=t.answer_data[n].mark_point_score_list[C]})})):(t.markValue="total",b.value==3?(e=t.quesInfo.children)==null||e.forEach((l,n)=>{l.marking_score=t.answer_data[n].repeat_score}):(o=t.quesInfo.children)==null||o.forEach((l,n)=>{l.marking_score=t.answer_data[n].official_score}))}},pe=a=>{var o;T.value=!0,b.value=3;let e={current_page:1,page_size:10,repeat_task_id:(o=_.query)==null?void 0:o.repeat_task_id,stu_secret_num:a.stu_secret_num};ie(e).then(s=>F(this,null,function*(){if(s.code&&s.code==200){s.data.data.forEach(t=>{t.answer_data[0].mark_point_score_list&&t.answer_data[0].mark_point_score_list.length>0?t.markValue="point":t.markValue="total"}),r.value=s.data.data,p.value=r.value[0],c.value=0,R(),yield ee(),yield Q(),oe();for(let t in r.value)K(r.value[t],r.value[t].markValue)}else D.error(s.msg)}))},fe=()=>{var a;if(r.value[c.value].quesInfo=JSON.parse(JSON.stringify(k.value)),c.value==r.value.length-1){r.value[c.value].isSumit=!0;let e={repeat_round_detail_id:r.value[0].repeat_round_detail_id,big_ques_list:[]},o=[],s="";if(r.value.forEach(t=>{var l;o.push(t.totalScore);let u=t.answer_data.filter(n=>!n.is_do).length;if(!t.isSumit&&u!=t.answer_data.length&&(s+=((l=t.quesInfo)==null?void 0:l.knowledge_show)+"未确认；"),t.markValue==="point"){let n={repeat_score:"",mark_info:[]};t.answer_data.forEach((i,h)=>{let C=t.quesInfo.children[h].ques_mark_point.map(g=>g.marking_score);const E=Number(t.quesInfo.children[h].ques_score),f=C.reduce((g,U)=>g+U,0);n.mark_info.push({ques_code:t.ques_code,answer_id:i.answer_id,mark_point_score_list:C,mark_score:f>E?E:f,round_id:"",cost_time:0,mark_type:b.value})}),n.repeat_score=n.mark_info.reduce((i,h)=>i+h.mark_score,0),e.big_ques_list.push(n)}else{let n={repeat_score:t.quesInfo.children.reduce((i,h)=>i+Number(h.marking_score),0),mark_info:[]};t.answer_data.forEach((i,h)=>{n.mark_info.push({ques_code:t.ques_code,answer_id:i.answer_id,mark_score:Number(t.quesInfo.children[h].marking_score),round_id:"",cost_time:0,mark_type:b.value})}),e.big_ques_list.push(n)}}),e.is_repeat=b.value==3?1:0,o.includes(null)||o.includes(void 0)){D.warning("还有试题没有进行复评，请复评");return}if(s){ue.alert(s,"提示",{confirmButtonText:"确认"});return}Te(e).then(t=>{if(t.code&&t.code==200){q.value=!1,k.value=null,c.value=0,w.value.leftCollapsed||L(),le();const u=w.value.titleData.state;ue.alert(`已经返回${u}。`,"提示",{confirmButtonText:"知道了",callback:()=>{}})}else D.error(t.msg)})}else q.value=!1,r.value[c.value].isSumit=!0,c.value++,p.value=r.value[c.value],R(),P(),(a=w.value)==null||a.getStepByQuesCode(p.value.quesInfo.ques_code)},L=()=>{var a,e;Oe({current_page:y.value.pageOptions.currentPage,page_size:y.value.pageOptions.pageSize,repeat_task_id:(a=_.query)==null?void 0:a.repeat_task_id,subject_id:(e=_.query)==null?void 0:e.subject_id}).then(o=>{o.code&&o.code==200&&(X.value=o.data.round_details,y.value.pageOptions.total=o.data.total)})},me=a=>{y.value.pageOptions.currentPage=1,y.value.pageOptions.pageSize=a,L()},ge=a=>{y.value.pageOptions.currentPage=a,L()},le=()=>{T.value=!1,w.value.leftCollapsed=!0,te()};return(a,e)=>{const o=z("table-component"),s=z("el-text"),t=z("SuccessFilled"),u=z("el-icon");return I(),V("div",null,[M(qe,{ref_key:"markingPaperRef",ref:w,markValue:A.value,"onUpdate:markValue":e[0]||(e[0]=l=>A.value=l),quesInfo:k.value,"onUpdate:quesInfo":e[1]||(e[1]=l=>k.value=l),noMarkStuList:v.value,"onUpdate:noMarkStuList":e[2]||(e[2]=l=>v.value=l),curIndex:m.value,"onUpdate:curIndex":e[3]||(e[3]=l=>m.value=l),isFocusNext:q.value,"onUpdate:isFocusNext":e[4]||(e[4]=l=>q.value=l),reMarkingFlag:T.value,"onUpdate:reMarkingFlag":e[5]||(e[5]=l=>T.value=l),noMarkPaperList:r.value,"onUpdate:noMarkPaperList":e[6]||(e[6]=l=>r.value=l),currentQues:c.value,"onUpdate:currentQues":e[7]||(e[7]=l=>c.value=l),paperTotalScore:ve(),isLoadNoMark:ae.value,onCommitMark:fe,onLeftExpand:L,onChangeTableHeight:_e,onGoBackToTask:le,onChangeMarkValue:ce},{leftTable:N(()=>[M(o,{minHeight:y.value.styleOptions.minHeight,"table-options":y.value,"table-data":X.value,onOnHandleRowClick:pe,onOnHandleSizeChange:me,onOnHandleCurrentChange:ge},null,8,["minHeight","table-options","table-data"])]),centerContent:N(()=>[r.value.length>0?(I(),V("div",Be,[x("div",Re,[x("div",null,[M(s,null,{default:N(()=>[B("试题分")]),_:1})]),x("div",null,[ne(_).query.type==1?(I(),Z(s,{key:0},{default:N(()=>[B("复评前分数/复评分")]),_:1})):(I(),Z(s,{key:1},{default:N(()=>[B("得分/验收分")]),_:1}))])]),x("div",Le,[(I(!0),V(Ae,null,Ee(r.value,(l,n)=>(I(),V("div",{key:n,class:Ie([{active:c.value==n}]),style:Ne({width:100/r.value.length+"%",position:"relative"}),onClick:i=>de(l,n)},[l.answer_data.filter(i=>!i.is_do).length==l.answer_data.length?(I(),V("img",{key:0,class:"no-answer-img",src:ne(Ve)},null,8,Fe)):j("",!0),l.isSumit?(I(),Z(u,{key:1,class:"confirm-icon"},{default:N(()=>[M(t)]),_:1})):j("",!0),x("div",null,[M(s,null,{default:N(()=>{var i;return[B(G((i=l.quesInfo)==null?void 0:i.knowledge_show),1)]}),_:2},1024)]),x("div",null,[M(s,null,{default:N(()=>[B(G(l.oldTotalScore)+"/"+G(l.totalScore),1)]),_:2},1024)])],14,Ue))),128))])])):j("",!0)]),_:1},8,["markValue","quesInfo","noMarkStuList","curIndex","isFocusNext","reMarkingFlag","noMarkPaperList","currentQues","paperTotalScore","isLoadNoMark"])])}}}),oa=Ce(ze,[["__scopeId","data-v-bb29384e"]]);export{oa as default};
