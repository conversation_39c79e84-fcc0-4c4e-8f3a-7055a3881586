var U=Object.defineProperty;var F=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var R=(o,e,a)=>e in o?U(o,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):o[e]=a,D=(o,e)=>{for(var a in e||(e={}))w.call(e,a)&&R(o,a,e[a]);if(F)for(var a of F(e))I.call(e,a)&&R(o,a,e[a]);return o};import M from"./check-content-BEcAsvRo.js";import{aQ as Q,aR as G,d as $,l as i,P as J,aN as K,n as X,ao as Y,T as V,r as f,o as v,c as H,e as C,b as d,h as p,f as b,F as Z,g as k,y,_ as ee}from"./index-B63pSD2p.js";import{c as te,a as ae}from"./calculateTableHeight-BjE6OFD1.js";import{p as ne,g as oe,a as le}from"./common-methods-BWkba4Bo.js";import{d as se}from"./downloadRequest-CdE2PBjt.js";import"./index-BCxZUZMh.js";import"./checkInfo-mJiqvlDC.js";import"./paper-verrification-C986OvHz.js";import"./test-paper-management-DjV_45YZ.js";const re=o=>Q.request("post",G("/v1/paper_verification/verify_result_list"),{data:o}),ie={class:"zf-first-box"},ue={class:"zf-second-box"},pe={class:"zf-flex-end"},ce=$({name:"verify-management",__name:"index",setup(o){const e=i(null),a=i(null),O=i(null),m=i({}),P=J({labelWidth:"68px",itemWidth:"160px",rules:{},fields:[{label:"所属资格",prop:"project_id",type:"select",clearable:!0,defaultValue:"",filterable:!0,placeholder:"请选择所属资格",optionData:()=>ne.value},{label:"所属科目",prop:"subject_id",type:"select",clearable:!0,defaultValue:null,filterable:!0,placeholder:"请选择所属科目",optionData:()=>g.value},{label:"核验状态",prop:"verification_status",type:"select",defaultValue:"",placeholder:"请选择核验状态",clearable:!0,optionData:()=>[{value:0,label:"未核验"},{value:1,label:"核验通过"},{value:2,label:"核验未通过"}]}]}),g=i([]),u=i({field:[{prop:"project_name",label:"所属资格",minWidth:"90px"},{prop:"subject_name",label:"所属科目",minWidth:"140px"},{prop:"verification_status",label:"核验状态",minWidth:"80px",type:"slot"},{prop:"",label:"操作",type:"template",minWidth:"120px",fixed:"right",templateGroup:[{title:()=>{if(K("verify-management/check"))return"核验"},clickBtn(t){O.value.openDialog(t)}}]}],styleOptions:{isShowSort:!0,isShowSelection:!0},pageOptions:{isShowPage:!0,currentPage:1,pageSize:10,total:0}}),j=i([]),S=i([]),L=t=>{S.value=t},h=i(!1),W=(t,l)=>{let s={},n=S.value.map(r=>r.subject_id)||[];n.length>0&&(s.subject_ids=n),h.value=!0,se("post","/v1/export/export_summary","",{},s,"zip").then(r=>{h.value=!1}).catch(r=>{h.value=!1,V({type:"error",message:"后端接口异常,导出失败,原因:"+r.response.statusText})})};let z=null;X(()=>{oe(),te(z,e.value,u.value),_()}),Y(()=>{ae(z)});const _=()=>{let{currentPage:t,pageSize:l}=u.value.pageOptions,s=D({current_page:t,page_size:l},m.value);re(s).then(n=>{var r;n.code&&n.code===200?(j.value=((r=n.data)==null?void 0:r.data)||[],u.value.pageOptions.total=n.data.total):V.error(n.msg)})},B=t=>{u.value.pageOptions.currentPage=1,u.value.pageOptions.pageSize=t,_()},N=t=>{u.value.pageOptions.currentPage=t,_()};function q(){g.value=[]}const T=(t,l)=>{t.prop==="project_id"&&(g.value=[],m.value.subject_id&&(m.value.subject_id=null),l&&le(l).then(s=>{g.value=s||[]}))};return(t,l)=>{const s=f("form-component"),n=f("el-card"),r=f("el-button"),A=f("Auth"),x=f("el-tag"),E=f("table-component");return v(),H("div",ie,[C("div",ue,[d(n,null,{default:p(()=>[C("div",{ref_key:"formDivRef",ref:e},[d(s,{ref_key:"formRef",ref:a,modelValue:m.value,"onUpdate:modelValue":l[0]||(l[0]=c=>m.value=c),"form-options":P,"is-query-btn":!0,onOnchangeFn:T,onQueryDataFn:_,onResetFields:q},null,8,["modelValue","form-options"])],512)]),_:1}),d(n,{style:{"margin-top":"5px","padding-bottom":"20px"}},{default:p(()=>[C("div",pe,[d(A,{value:"verify-management/export"},{default:p(()=>[d(r,{type:"primary",onClick:l[1]||(l[1]=c=>W("1")),loading:h.value},{default:p(()=>[b("导出核验结果")]),_:1},8,["loading"])]),_:1})]),d(E,{minHeight:u.value.styleOptions.minHeight,"table-options":u.value,"table-data":j.value,onOnHandleSelectionChange:L,onOnHandleSizeChange:B,onOnHandleCurrentChange:N},{verification_status:p(({row:c})=>[c.verification_status?(v(),H(Z,{key:0},[c.verification_status==0?(v(),k(x,{key:0,type:"primary"},{default:p(()=>[b("未核验")]),_:1})):y("",!0),c.verification_status==1?(v(),k(x,{key:1,type:"success"},{default:p(()=>[b("核验通过")]),_:1})):y("",!0),c.verification_status==2?(v(),k(x,{key:2,type:"danger"},{default:p(()=>[b("核验未通过")]),_:1})):y("",!0)],64)):y("",!0)]),_:1},8,["minHeight","table-options","table-data"])]),_:1})]),d(M,{ref_key:"checkContentRef",ref:O,onCheckOver:_},null,512)])}}}),Ce=ee(ce,[["__scopeId","data-v-c280fe4c"]]);export{Ce as default};
